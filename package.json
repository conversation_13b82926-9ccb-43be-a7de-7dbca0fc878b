{"name": "bwms", "private": false, "version": "1.0.1", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build:test": "vue-tsc --noEmit && vite build --mode test", "build:prod": "vue-tsc --noEmit && vite build --mode prod", "preview": "vite preview", "farm:dev": "npx @farmfe/cli dev", "farm:build": "npx @farmfe/cli build", "farm:preview": "npx @farmfe/cli preview"}, "dependencies": {"@chatui/core": "^2.4.2", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/theme-one-dark": "^6.1.2", "@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/vue-fontawesome": "^3.0.8", "@heroicons/vue": "^2.1.1", "@iconify/vue": "^4.1.2", "@tinymce/tinymce-vue": "^5.1.1", "@tiptap/core": "^2.11.7", "@tiptap/extension-bubble-menu": "^2.11.7", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-floating-menu": "^2.11.7", "@tiptap/extension-font-family": "^2.11.7", "@tiptap/extension-highlight": "^2.11.7", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-strike": "^2.11.7", "@tiptap/extension-subscript": "^2.11.7", "@tiptap/extension-superscript": "^2.11.7", "@tiptap/extension-table": "^2.11.7", "@tiptap/extension-table-cell": "^2.11.7", "@tiptap/extension-table-header": "^2.11.7", "@tiptap/extension-table-row": "^2.11.7", "@tiptap/extension-task-item": "^2.11.7", "@tiptap/extension-task-list": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-typography": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/html": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@tiptap/vue-3": "^2.11.7", "@types/chart.js": "^2.9.41", "@vueuse/core": "^10.9.0", "@weiruo/tiptap-extension-indent": "^2.0.4-1", "amis": "6.12.0", "bootstrap": "^5.3.5", "chart.js": "^4.4.3", "chartjs-plugin-datalabels": "^2.2.0", "chatbot-bingo": "^0.1.1", "ckeditor4": "^4.24.0", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "element-plus": "^2.8.8", "file-saver": "^2.0.5", "filestudio-bingo": "^0.4.21", "jszip": "^3.10.1", "mitt": "3.0.1", "monaco-editor-webpack-plugin": "^7.1.0", "node-forge": "^1.3.1", "nprogress": "^0.2.0", "pinia": "^2.1.3", "quill": "^2.0.2", "react": "^18.3.1", "react-cropper": "^2.1.8", "react-dom": "^18.3.1", "recordrtc": "^5.6.2", "terser": "^5.28.1", "tinymce": "6.8.5", "vite-plugin-monaco-editor": "^1.1.0", "vue": "^3.4.34", "vue-clipboard3": "^2.0.0", "vue-codemirror": "^6.1.1", "vue-google-charts": "^1.1.0", "vue-i18n": "9", "vue-router": "4.3.0", "vue3-google-map": "^0.20.0", "vue3-grid-layout": "^1.0.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@iconify-json/logos": "^1.1.31", "@rollup/plugin-alias": "^5.0.0", "@stagewise/toolbar-vue": "^0.4.4", "@types/mockjs": "^1.0.7", "@types/node": "^20.12.8", "@types/node-forge": "^1.3.11", "@types/nprogress": "^0.2.0", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@vitejs/plugin-react": "^4.3.1", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.0.1", "autoprefixer": "^10.4.14", "axios": "^1.4.0", "cross-env": "^7.0.3", "esbuild": "^0.25.5", "eslint": "^8.57.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^16.0.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.14.0", "mockjs": "^1.1.0", "monaco-editor": "^0.48.0", "postcss": "^8.4.23", "prettier": "2.8.8", "sass": "^1.71.1", "tailwindcss": "^3.3.2", "typescript": "^5.4.5", "unplugin-auto-import": "^0.17.5", "unplugin-icons": "^0.18.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.1.4", "vite-plugin-html": "^3.2.0", "vite-plugin-mock": "^3.0.0", "vite-plugin-static-copy": "^1.0.4", "vue-tsc": "^2.0.26"}, "resolutions": {"package-manager-detector": "0.2.11", "@antfu/install-pkg": "0.1.1", "@iconify/utils": "2.1.7"}}