<template>
  <div class="data-making-container">
    <!-- 内容区域 -->
    <div class="content">
      <el-row :gutter="20" style="gap: 20px 0;">
        <!-- 数据卡片 -->
        <el-col 
          :xs="24" 
          :sm="24" 
          :md="12" 
          :lg="12" 
          :xl="12"
          v-for="(item, index) in dataItems" 
          :key="index"
          class="card-column"
        >
          <div class="data-card">
            <div class="card-header">
              <div class="country-badge">{{ item.countryCode }}</div>
              <div class="card-info">
                <h3 class="card-title">{{ item.title }}</h3>
                <div class="status-info">
                  <span class="status-badge" :class="item.status.type">
                    {{ item.status.text }}
                  </span>
                  <span class="update-time">{{ item.updateTime }}</span>
                </div>
              </div>
            </div>
            
            <div class="card-body">
              <div class="main-info">
                <div class="timestamp">{{ item.timestamp }}</div>
                <div class="description">{{ item.description }}</div>
              </div>
              <div class="tags">
                <span class="tag" :class="item.tag.type">{{ item.tag.text }}</span>
              </div>
            </div>
            
            <div class="card-footer">
              <div class="detail-btn">
                <span>查看詳細分析</span>
                <el-icon size="14" color="#37A0EA" ><ArrowRight /></el-icon>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 新增监控对象卡片 -->
        <el-col 
          :xs="24" 
          :sm="24" 
          :md="12" 
          :lg="12"
          :xl="12"
          class="card-column"
        >
          <div class="add-card" @click="handleAddMonitor">
            <div class="add-content">
              <div class="add-icon">
                <el-icon size="47" style="font-weight: bold;"><Plus /></el-icon>
              </div>
              <el-button type="primary" class="add-text">新增監控對象</el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ArrowRight, Plus, Right } from '@element-plus/icons-vue'

// 数据项
const dataItems = ref([
  {
    countryCode: 'SG',
    title: '新加坡人才網站',
    status: { text: '活躍中', type: 'active' },
    updateTime: '最新異常: 11/14 上午 09:23',
    timestamp: '11/14 上午 09:23',
    description: '新增「Tech.Pass 計劃」專題文章，詳細介紹科技人才申請流程',
    tag: { text: '內容更新', type: 'content' }
  },
  {
    countryCode: 'AU',
    title: '澳洲技術移民網站',
    status: { text: '監控中', type: 'monitoring' },
    updateTime: '最新異常: 11/10 下午 04:48',
    timestamp: '11/10 下午 04:48',
    description: '更新職業清單與申請時間表，新增多個技術職位類別',
    tag: { text: '結構調整', type: 'structure' }
  },
  {
    countryCode: 'CA',
    title: '加拿大人才計劃',
    status: { text: '今日有異動', type: 'updated' },
    updateTime: '最新異常: 11/14 上午 09:23',
    timestamp: '11/14 上午 09:23',
    description: '推出全新人才儲備庫功能和優化版申請表格',
    tag: { text: '功能增強', type: 'feature' }
  },
  {
    countryCode: 'GB',
    title: '英國全球人才簽證',
    status: { text: '活躍中', type: 'active' },
    updateTime: '最新異常: 11/5 下午 02:23',
    timestamp: '11/5 下午 02:23',
    description: '升級客服聊天機器人，新增多語言支援功能',
    tag: { text: '體驗優化', type: 'feature' }
  },
  {
    countryCode: 'VTC',
    title: '職業訓練網站',
    status: { text: '今日有異動', type: 'updated' },
    updateTime: '最新異常: 11/14 上午 09:23',
    timestamp: '11/14 上午 09:23',
    description: '發佈「數碼技能培訓」系列課程，重組技能資歷架構頁面',
    tag: { text: '課程更新', type: 'content' }
  }
])

// 处理新增监控对象
const handleAddMonitor = () => {
  console.log('新增監控對象')
}
</script>

<script lang="ts">
export default {
  name: 'DataMaking',
}
</script>

<style scoped lang="scss">
.data-making-container {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .title {
      font-size: 24px;
      font-weight: bold;
      line-height: 32px;
      color: #232323;
      margin: 0;
    }

    .header-actions {
      .view-more {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #18191A;
        font-size: 16px;
        cursor: pointer;
        transition: color 0.3s ease;
        background: #fff;
        width: 110px;
        height: 38px;
        justify-content: center;

        &:hover {
          color: #007EE5;
        }

        .el-icon {
          font-size: 14px;
          color: #171717;
        }
      }
    }
  }

  .content {
    .card-column {

    }

    .data-card {
      position: relative;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0px 3px 6px #00000029;
      padding: 25px 23px 20px 24px;
      height: 100%;
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0px 6px 12px #00000015;
      }

      .card-header {
        display: flex;
        gap: 21px;
        padding-bottom: 23px;
        margin-bottom: 14px;
        border-bottom: 1px solid #E3E3E3;

        .country-badge {
          width: 61px;
          height: 61px;
          border-radius: 5px;
          background-color: #EEEEEE;
          box-shadow: 0px 1px 1px #0000000D;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          font-weight: bold;
          color: #232323;
          flex-shrink: 0;
        }

        .card-info {
          flex: 1;

          .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #232323;
            margin: 0 0 8px 0;
          }

          .status-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;

            .status-badge {
                padding: 6px 12px;
                border-radius: 15px;
                font-size: 12px;
                max-width: 84px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                display: inline-block;

              &.active {
                background-color: #E3FEDB;
                color: #27A11E;
              }

              &.monitoring {
                background-color: #E2F2FF;
                color: #007EE5;
              }

              &.updated {
                background-color: #FDECEC;
                color: #F05657;
              }
            }

            .update-time {
              font-size: 14px;
              color: #9A9A9A;
            }
          }
        }
      }

      .card-body {
        .main-info {
          margin-bottom: 14px;

          .timestamp {
            font-size: 14px;
            color: #9A9A9A;
            margin-bottom: 13px;
          }

          .description {
            font-size: 17px;
            color: #464646;
            line-height: 1.3;
          }
        }

        .tags {
          .tag {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;

            &.content {
              background-color: #E8F5E8;
              color: #2D5A2D;
            }

            &.structure {
              background-color: #FFF3CD;
              color: #856404;
            }

            &.feature {
              background-color: #D1ECF1;
              color: #0C5460;
            }

            &.experience {
              background-color: #F3E5F5;
              color: #7B1FA2;
            }

            &.course {
              background-color: #E1F5FE;
              color: #0277BD;
            }
          }
        }
      }

      .card-footer {
        display: flex;
        justify-content: flex-end;
        .detail-btn {
          width: 100%;
          padding: 0 18px;
          height: 38px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border: 2px solid #86C0E9;
          border-radius: 5px;
          font-size: 14px;
          font-weight: bold;
          color: #37A0EA;
          margin-top: 17px;
          background: #F4F9FD;
        }
      }
    }

    .add-card {
      background-color: #E5F8FF;
      border: 1px dashed #35A0D8;
      border-radius: 10px;
      height: 100%;
      min-height: 280px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #007EE5;
        background-color: rgb(229, 248, 255, 0.05);
      }

      .add-content {
        text-align: center;

        .add-icon {
          margin-bottom: 19px;
          color: #007EE5;
          transition: color 0.3s ease;
          height: 47px;
        }

        .add-text {
          width: 131px;
        }
      }
    }
  }
}
</style>
