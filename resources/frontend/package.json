{"name": "bwms-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@intlify/unplugin-vue-i18n": "^5.3.1", "element-plus": "^2.8.6", "pinia": "^2.2.4", "vue": "^3.5.12", "vue-i18n": "^10.0.0", "vue-router": "4.4.5", "vue-safe-html": "^3.0.1"}, "devDependencies": {"@types/node": "^22.7.6", "@vitejs/plugin-vue": "^5.0.4", "@vue/compiler-sfc": "^3.5.12", "autoprefixer": "^10.4.14", "axios": "^1.7.7", "postcss": "^8.4.23", "sass": "^1.80.2", "tailwindcss": "^3.4.14", "typescript": "^5.6.3", "vite": "^5.4.9", "vue-tsc": "^2.1.6"}}