---
description: Best practices and guidelines for Laravel framework
globs: **/*.{php}
alwaysApply: true
---
您是 Laravel、PHP 开发技术的专家。

## 核心原则
- 使用准确的 PHP/Laravel 示例编写简洁、技术性的回复
- 优先考虑面向对象编程和干净架构的 SOLID 原则
- 遵循 PHP/Laravel  最佳实践，确保一致性和可读性
- 设计可扩展性和可维护性，确保系统可以轻松增长
- 优先使用迭代和模块化而不是重复，以促进代码重用
- 对变量、方法和类使用一致且描述性的名称，以提高可读性

### 后端依赖
- Composer 依赖管理
- PHP 8.2+
- Laravel 11.0+
- MySQL 8.0+
- Redis

### PHP 和 Laravel 标准
- 在适当的情况下利用 PHP 8.2+ 功能（例如，类型化属性、匹配表达式）
- 遵守 PSR-12 编码标准，以保持一致的代码风格
- 始终使用严格类型：declare(strict_types=1)
- 利用 Laravel 的内置功能和助手来最大限度地提高效率
- 遵循 Laravel 的目录结构和文件命名约定
- 实现强大的错误处理和日志记录：
  - 使用 Laravel 的异常处理和日志记录功能
  - 必要时创建自定义异常
  - 除了 controller层 之外，对预期的异常使用 try-catch 块
- 对表单和请求数据使用 Laravel 的验证功能
- 实现中间件以进行请求过滤和修改
- 利用 Laravel 的 Eloquent ORM 进行数据库交互
- 使用 Laravel 的查询生成器进行复杂的数据库操作
- 创建和维护适当的数据库迁移和种子

### Laravel 最佳实践
- 尽可能使用 Eloquent ORM 和查询生成器代替原始 SQL 查询
- 实现存储库和服务模式，以更好地组织代码并提高可重用性
- 利用 Laravel 的内置身份验证和授权功能（Sanctum、Policy）
- 利用 Laravel 的缓存机制（Redis、Memcached）提高性能
- 使用作业队列和 Laravel Horizo​​n 处理长时间运行的任务和后台处理
- 使用 PHPUnit 和 Laravel Dusk 进行全面测试，进行单元、功能和浏览器测试
- 使用 API 资源和版本控制构建强大且可维护的 API
- 使用 Laravel 的异常处理程序和日志外观实现适当的错误处理和日志记录
- 利用 Laravel 的验证功能（包括表单请求）确保数据完整性
- 实现数据库索引并使用 Laravel 的查询优化功能提高性能
- 在开发中使用 Laravel Telescope 进行调试和性能监控
- 利用 Laravel Nova 或 Filament 快速开发管理面板
- 实现适当的安全性措施，包括 CSRF 保护、XSS 预防和输入清理

### 后端架构
- 命名约定：
  - 对文件夹、类和文件使用一致的命名约定
  - 遵循 Laravel 的约定：模型使用单数，控制器使用复数（例如，User.php、UsersController.php）
  - 类名使用 PascalCase，方法名使用 camelCase，数据库列使用 snake_case
- 控制器设计：
  - 控制器应为 final 类，以防止继承
  - 使控制器为只读（即没有属性突变）
  - 避免将依赖项直接注入控制器。相反，使用方法注入或服务类
- 模型设计：
  - 模型应为 final 类，以确保数据完整性并防止继承导致意外行为
  - 模型层尽量少查询，可以抽象到Domain层的repositories
- 服务：
  - 在应用程序目录中创建一个 Services 文件夹
  - 将服务组织成特定于模型的服务和其他必需的服务
  - 服务类应为 final 和只读
  - 使用服务实现复杂的业务逻辑，保持控制器的精简
- 领域服务：
  - 在模块下创建一个 Domain 文件夹
  - 使用DDD 领域驱动的模式进行分层处理，里面可以建立Interface，repositories
  - 逻辑比较复杂，建立适合的设计模式进行代码逻辑分离
- 路由：
  - 路由分 Api，Admin，Web，OpenApi 的路由，分别有自己的route.php
  - 保持一致且有组织的路由
  - 为每个主要模型或功能区域创建单独的路由文件
  - 将相关路由组合在一个group（例如，Route::group(['prefix' => 'users'], function () {}); 中的所有用户相关路由）
- 类型声明：
  - 始终对方法和函数使用显式返回类型声明
  - 对方法参数使用适当的 PHP 类型提示
  - 必要时利用 PHP 8.2+ 功能，如联合类型和可空类型
- 数据类型一致性：
  - 在整个代码库中保持一致且明确的数据类型声明
  - 对属性、方法参数和返回类型使用类型提示
  - 利用 PHP 的严格类型尽早捕获与类型相关的错误
- 错误处理：
  - 使用 Laravel 的异常处理和日志记录功能来处理异常
  - 必要时创建自定义异常
  - 对预期异常使用 try-catch 块
  - 优雅地处理异常并返回适当的响应
  - 异常参考 BizException::throws(ChatErrorCode::INTENT_RECOGNITION_ERROR)，其中Chat 是模块名称ChatErrorCode 统一管理错误码。新增错误码是按照前面的类推自增错误码，使用 case ,而不是 const

  ### 后端开发场景

1. 路由开发 (*.route.php)
- 参考: @docs/backend/templates/routes/api-routes.md （API路由）
- 参考: @docs/backend/templates/routes/admin-routes.md （管理后台路由）
- 参考: @docs/backend/templates/routes/web-routes.md （Web路由）
- 参考: @docs/backend/templates/routes/openapi-routes.md （开放API路由）

2. 控制器开发 (*Controller.php)
- 参考: @docs/backend/templates/controllers/api-controller.md （API控制器）
- 参考: @docs/backend/templates/controllers/admin-controller.md （管理后台控制器）
- 参考: @docs/backend/templates/controllers/web-controller.md （Web控制器）
- 参考: @docs/backend/templates/controllers/openapi-controller.md （开放API控制器）

3. 模型开发 (Models/*.php)
- 参考: @docs/backend/templates/models/model-template.md （模型模板）
- 参考: @docs/backend/templates/models/relationships.md （关系模型）
- 参考: @docs/backend/templates/models/scopes.md （作用域模型）

4. 领域开发 (Domain/*)
- 参考: @docs/backend/templates/domain/entities.md （实体模型）
- 参考: @docs/backend/templates/domain/interfaces.md （接口模型）
- 参考: @docs/backend/templates/domain/repositories.md （仓储模型）
- 参考: @docs/backend/templates/domain/services.md （服务模型）

5. 服务开发 (Services/*.php)
- 参考: @docs/backend/templates/services/service-template.md （服务模板）
- 参考: @docs/backend/templates/services/best-practices.md （最佳实践）

6. 错误码开发 (Enums/*ErrorCode.php)
- 参考: @docs/backend/templates/error-codes/error-template.md （错误码模板）
- 参考: @docs/backend/templates/error-codes/naming-rules.md （命名规则）

  ## 通用要点
- 遵循 Laravel 的 MVC 架构，明确区分业务逻辑、数据和呈现
- 该项目是模块化开发，modules/*** 其中*** 代表模块名称
- 一个类的代码不大于500行，一个方法不大于50行。如果超出应该考虑拆分。
- 一个方法的代码逻辑不超过5个步骤，如果超过应该考虑拆分。
- 测试用例不要清空数据库RefreshDatabase。

### 控制器返回值规范
- 所有控制器方法应统一返回数组，不直接返回 response()->json() 或 Response 对象，便于统一响应处理和前后端解耦。
