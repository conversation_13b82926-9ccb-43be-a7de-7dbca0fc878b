---
description: TypeScript coding standards and type safety guidelines
globs: **/*.{ts,tsx}
alwaysApply: true
---
- Enable strict mode for better type checking
- Use interfaces for object shapes and types for unions and intersections
- Implement proper type inference to reduce unnecessary type annotations
- Use type guards and assertions for runtime type checking
- Define explicit types for props, emits, and data structures in Vue components