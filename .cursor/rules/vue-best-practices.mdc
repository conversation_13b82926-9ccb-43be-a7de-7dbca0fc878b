---
description: Best practices and guidelines for Vue.js framework
globs: **/*.{vue,js,ts}
alwaysApply: true
---
- Design components with single responsibility and keep them pure
- Use Pinia for global state management and organize stores modularly
- Implement modular routing configuration and use route guards for access control
- Use Form components to encapsulate common form logic and validation
- Encapsulate API calls in a unified service layer using Axios
- Optimize performance with keep-alive, computed properties, and virtual lists
- Implement lazy loading for images and route components
- Use TypeScript for type safety, especially in props, emits, and data structures