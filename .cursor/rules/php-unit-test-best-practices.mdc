---
description: 
globs: 
alwaysApply: true
---
# PHP单元测试通用规则

**核心原则：简洁优先，可靠性至上**

## 组织与命名
- 测试类命名: `{Feature}Test`；测试方法命名: `test_{功能}_{场景}`  
- 辅助方法动词开头：`assert_{条件}`、`create_{对象}`、`setup_{环境}`
- 严格遵循AAA模式: `Arrange-Act-Assert`（准备-执行-断言）
- 每个测试方法专注于一个功能点，避免测试多个功能

## 编码约定
- 为测试类和方法添加清晰的PHPDoc注释，描述测试场景和目的
- Laravel项目使用PHPUnit框架，可结合Pest语法提高可读性
- 使用原生断言 `$this->assertTrue()`或Pest语法 `expect()`，提供清晰的失败消息
- 使用数据提供器 `@dataProvider` 或Pest中的 `it->with()` 进行参数化测试
- 使用 `setUp()` 方法或Pest中的 `beforeEach()` 共享测试前置条件
- 异常测试使用 `$this->expectException()` 或 `expect()->toThrow()`
- Mock外部依赖使用 `Mockery` 或 `$this->mock()`，避免模拟业务逻辑
- 当方法超过30行或断言超过5条时，考虑拆分或提取辅助方法

## 数据处理
- 使用工厂 `Factory` 和假数据 `Faker` 创建测试数据
- 使用 `DatabaseTransactions` 而非 `RefreshDatabase`，提高测试效率
- 为复杂测试数据创建专用的测试固件 (fixtures)
- 单元测试应尽量避免数据库操作，专注于业务逻辑测试

## 覆盖率目标
- 核心业务逻辑行覆盖率 ≥ 90%  
- 服务/领域层覆盖率 ≥ 80%
- 工具/辅助模块 ≥ 70%
- 控制器层通过Feature测试覆盖

## 测试类型划分
- **单元测试**: 测试独立类和方法，使用Mock隔离外部依赖
- **集成测试**: 测试多个组件协同工作，但仍可控制某些边界
- **功能测试**: 测试完整的功能流程，模拟HTTP请求和响应

## PHPUnit示例代码

```php
<?php

namespace Tests\Unit\Modules\User\Domain;

use PHPUnit\Framework\TestCase;
use Modules\User\Domain\Entities\User;
use Modules\User\Domain\ValueObjects\Username;
use Modules\User\Domain\ValueObjects\Password;
use Modules\User\Domain\Enums\UserStatus;
use Carbon\Carbon;
use Mockery;

/**
 * User实体测试类
 * 
 * 测试User实体的行为和方法，确保符合业务需求
 */
class UserTest extends TestCase
{
    /**
     * 创建用户名值对象
     */
    protected function createUsername(): Username
    {
        return new Username('testuser');
    }
    
    /**
     * 创建密码值对象
     */
    protected function createPassword(): Password
    {
        return new Password('Password123');
    }
    
    /**
     * 提供基本测试用户
     */
    protected function createTestUser(): User
    {
        return new User(
            1,
            $this->createUsername(),
            $this->createPassword()
        );
    }
    
    /**
     * 自定义时间数据
     */
    protected function getCustomTimes(): array
    {
        return [
            'created_at' => Carbon::now()->subDays(5),
            'last_login_at' => Carbon::now()->subDays(2)
        ];
    }
    
    /**
     * 提供带有自定义属性的用户
     */
    protected function createCustomUser(): User
    {
        $times = $this->getCustomTimes();
        
        return (new User(
            100,
            $this->createUsername(),
            $this->createPassword()
        ))->setCreatedAt($times['created_at']);
    }
    
    /**
     * 测试使用密码对象创建用户
     */
    public function test_user_creation_with_password_object(): void
    {
        // 准备
        $username = $this->createUsername();
        $password = $this->createPassword();
        
        // 执行
        $user = new User(
            1,
            $username,
            $password
        );
        
        // 断言
        $this->assertEquals(1, $user->getId(), '用户ID应正确设置');
        $this->assertSame($username, $user->getUsername(), '用户名应正确设置');
        $this->assertEquals($password->getHash(), $user->getPasswordHash(), '密码哈希应从密码对象获取');
        $this->assertEquals($password->getSalt(), $user->getPasswordSalt(), '密码盐值应从密码对象获取');
        $this->assertEquals(UserStatus::ACTIVE, $user->getStatus(), '默认状态应为活跃');
        $this->assertNotNull($user->getCreatedAt(), '创建时间应自动设置');
        $this->assertNull($user->getLastLoginAt(), '最后登录时间应初始化为NULL');
    }
    
    /**
     * 密码验证测试数据提供者
     */
    public function passwordVerificationProvider(): array
    {
        return [
            'correct_password' => ['Password123', true],
            'wrong_password' => ['WrongPassword', false],
            'empty_password' => ['', false],
        ];
    }
    
    /**
     * 测试密码验证功能
     * 
     * @dataProvider passwordVerificationProvider
     */
    public function test_verify_password(string $inputPassword, bool $expectedResult): void
    {
        // 准备
        $user = $this->createTestUser();
        
        // 执行
        $result = $user->verifyPassword($inputPassword);
        
        // 断言
        $this->assertEquals($expectedResult, $result, 
            sprintf('密码验证应返回%s', $expectedResult ? 'true' : 'false'));
    }
    
    /**
     * 测试修改账号状态
     */
    public function test_change_status(): void
    {
        // 准备
        $user = $this->createTestUser();
        
        // 前置断言
        $this->assertEquals(UserStatus::ACTIVE, $user->getStatus(), '初始状态应为活跃');
        
        // 执行
        $user->changeStatus(UserStatus::LOCKED);
        
        // 断言
        $this->assertEquals(UserStatus::LOCKED, $user->getStatus(), '状态应变更为锁定');
    }
    
    /**
     * 测试更新最后登录时间
     */
    public function test_update_last_login(): void
    {
        // 准备
        $user = $this->createTestUser();
        $mockDate = Carbon::create(2023, 1, 1, 12, 0, 0);
        Carbon::setTestNow($mockDate);
        
        // 前置断言
        $this->assertNull($user->getLastLoginAt(), '初始最后登录时间应为NULL');
        
        // 执行
        $user->updateLastLogin();
        
        // 断言
        $this->assertEquals($mockDate, $user->getLastLoginAt(), '最后登录时间应更新为当前时间');
        
        // 重置模拟时间
        Carbon::setTestNow();
    }
}
```

## Pest框架示例代码

```php
<?php

use Modules\User\Domain\Entities\User;
use Modules\User\Domain\ValueObjects\Username;
use Modules\User\Domain\ValueObjects\Password;
use Modules\User\Domain\Enums\UserStatus;
use Carbon\Carbon;

/**
 * User实体测试
 * 
 * 测试User实体的行为和方法，确保符合业务需求
 */
describe('User Entity', function () {
    
    beforeEach(function () {
        $this->username = new Username('testuser');
        $this->password = new Password('Password123');
        $this->user = new User(
            1,
            $this->username,
            $this->password
        );
    });
    
    it('correctly creates with password object', function () {
        // 断言
        expect($this->user->getId())->toBe(1)
            ->and($this->user->getUsername())->toBe($this->username)
            ->and($this->user->getPasswordHash())->toBe($this->password->getHash())
            ->and($this->user->getPasswordSalt())->toBe($this->password->getSalt())
            ->and($this->user->getStatus())->toBe(UserStatus::ACTIVE)
            ->and($this->user->getCreatedAt())->not->toBeNull()
            ->and($this->user->getLastLoginAt())->toBeNull();
    });
    
    it('verifies password correctly', function (string $inputPassword, bool $expectedResult) {
        $result = $this->user->verifyPassword($inputPassword);
        expect($result)->toBe($expectedResult);
    })->with([
        ['Password123', true],
        ['WrongPassword', false],
        ['', false],
    ]);
    
    it('changes status correctly', function () {
        // 前置断言
        expect($this->user->getStatus())->toBe(UserStatus::ACTIVE);
        
        // 执行
        $this->user->changeStatus(UserStatus::LOCKED);
        
        // 断言
        expect($this->user->getStatus())->toBe(UserStatus::LOCKED);
    });
    
    it('updates last login time', function () {
        // 准备
        $mockDate = Carbon::create(2023, 1, 1, 12, 0, 0);
        Carbon::setTestNow($mockDate);
        
        // 前置断言
        expect($this->user->getLastLoginAt())->toBeNull();
        
        // 执行
        $this->user->updateLastLogin();
        
        // 断言
        expect($this->user->getLastLoginAt())->toBe($mockDate);
        
        // 重置模拟时间
        Carbon::setTestNow();
    });
});