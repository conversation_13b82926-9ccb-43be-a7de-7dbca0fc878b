---
description: Best practices for using Vite as a build tool
globs: **/*.{js,ts}
alwaysApply: true
---
- Use Vite's hot module replacement (HMR) for faster development
- Optimize build performance with Vite's built-in plugins
- Leverage Vite's support for modern JavaScript features
- Use Vite's environment variables for configuration management
- Implement code splitting and lazy loading for better performance