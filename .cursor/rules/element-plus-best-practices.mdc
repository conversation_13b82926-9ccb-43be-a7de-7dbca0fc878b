---
description: Best practices for using Element Plus UI components
globs: **/*.{vue,js,ts}
alwaysApply: true
---
您是 Web 开发技术的专家。

## 核心原则
- 使用准确的 Vue/TypeScript 示例编写简洁、技术性的回复
- 优先考虑面向对象编程和干净架构的 SOLID 原则
- 遵循Vue/TypeScript 最佳实践，确保一致性和可读性
- 设计可扩展性和可维护性，确保系统可以轻松增长
- 优先使用迭代和模块化而不是重复，以促进代码重用
- 对变量、方法和类使用一致且描述性的名称，以提高可读性

### 前端依赖
- Node.js 18+
- Vue 3.3+
- TypeScript 5.0+
- Vite 4.0+
- Element Plus 2.3+
- Pinia 2.0+
- Vue Router 4.0+

### Vue 和 TypeScript 标准
- 使用 Vue 3 组合式 API 和 `<script setup>` 语法
- 启用 TypeScript 严格模式 (strict: true)
- 使用 ESLint 和 Prettier 进行代码格式化
- 组件命名采用 PascalCase
- Props 定义必须包含类型和默认值：
```typescript
const props = defineProps<{
  title: string
  count?: number
}>()
```
- Emits 必须显式声明：
```typescript
const emit = defineEmits<{
  (e: 'update', value: string): void
  (e: 'delete', id: number): void
}>()
```
- 使用 TypeScript 类型和接口定义数据结构：
```typescript
interface User {
  id: number
  name: string
  email: string
  roles: Role[]
}
```
- 组件状态管理使用 Pinia：
```typescript
export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const isLoggedIn = computed(() => !!user.value)
  
  async function login(credentials: Credentials) {
    // 实现登录逻辑
  }
  
  return { user, isLoggedIn, login }
})
```

### Vue 最佳实践
- 组件设计：
  - 组件应该是单一职责的
  - 保持组件的纯函数特性
  - 避免副作用，将副作用移至 actions 或 services
  - 组件大小不超过 300 行，超出时考虑拆分

- 状态管理：
  - 使用 Pinia 进行全局状态管理
  - 按模块拆分 store
  - 使用组合式 store 模式
  - 异步操作放在 actions 中处理

- 路由管理：
  - 使用模块化的路由配置
  - 实现路由守卫进行权限控制
  - 使用路由元信息声明页面属性
  - 懒加载路由组件

- 表单处理：
  - 使用 Form 组件封装通用逻辑
  - 实现统一的表单验证
  - 使用 TypeScript 类型确保表单数据类型安全
  - 处理表单提交的错误状态

- API 调用：
  - 使用 Axios 进行 HTTP 请求
  - 封装统一的 API 调用层
  - 实现请求和响应拦截器
  - 统一处理错误响应

- 性能优化：
  - 使用 keep-alive 缓存组件
  - 合理使用计算属性和监听器
  - 实现虚拟列表处理大数据
  - 图片懒加载

### 前端架构
- 目录结构：
```
src/
├── assets/          # 静态资源
├── components/      # 通用组件
├── composables/     # 组合式函数
├── layouts/         # 布局组件
├── pages/          # 页面组件
├── router/         # 路由配置
├── stores/         # Pinia stores
├── services/       # API 服务
├── types/          # TypeScript 类型
└── utils/          # 工具函数
```

- 组件命名规范：
  - 基础组件使用 Base 前缀
  - 单例组件使用 The 前缀
  - 紧密耦合的组件使用父组件名作为前缀
  - 组件名使用多个单词

- 类型定义：
  - 接口名使用 I 前缀
  - 类型名使用 T 前缀
  - 枚举名使用 E 前缀
  - 常量使用 UPPER_SNAKE_CASE

- API 服务：
  - 按模块组织 API 服务
  - 使用 TypeScript 类型注解
  - 实现统一的错误处理
  - 支持请求取消

### 前端开发场景
1. 组件开发
- 参考: @docs/frontend/templates/components/basic-components.md （基础组件）
- 参考: @docs/frontend/templates/components/form-components.md （表单组件）
- 参考: @docs/frontend/templates/components/table-components.md （表格组件）

2. 布局开发
- 参考: @docs/frontend/templates/layouts/admin-layout.md （管理后台布局）
- 参考: @docs/frontend/templates/layouts/web-layout.md （Web端布局）

3. 页面开发
- 参考: @docs/frontend/templates/pages/list-page.md （列表页面）
- 参考: @docs/frontend/templates/pages/form-page.md （表单页面）
- 参考: @docs/frontend/templates/pages/detail-page.md （详情页面）

4. 状态管理
- 参考: @docs/frontend/templates/stores/store-template.md （Store模板）
- 参考: @docs/frontend/templates/stores/best-practices.md （最佳实践）

5. API服务
- 参考: @docs/frontend/templates/services/api-service.md （API服务）
- 参考: @docs/frontend/templates/services/request-template.md （请求模板）

6. 工具函数
- 参考: @docs/frontend/templates/utils/common-utils.md （通用工具）
- 参考: @docs/frontend/templates/utils/validators.md （验证器）


