---
description: 
globs: 
alwaysApply: true
---

- 数据库操作尽量使用model进行操作
- model目录：Modules\模块名称\Models
- request目录：
    1.<PERSON><PERSON><PERSON>\模块名称\Admin\Requests
    2.<PERSON><PERSON><PERSON>\模块名称\Api\Requests
- request命名规则：动词开头，如：StoreMemberRequest,ListMemberRequest,UpdateMemberRequest
- request示例：

```php
<?php

namespace Modules\Member\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreMemberRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            //
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            //
        ];
    }
}
```

- T函数命名示例：
    1.T('Members::validation.store_member.account.required')
    2.T('Members::validation.store_member.account.string')
    3.T('Members::validation.store_member.account.max')

- T函数对呀的国家化文件：
    1.<PERSON><PERSON><PERSON>\模块名称\Lang\en\validation.php
    2.<PERSON><PERSON>les\模块名称\Lang\zh_CN\validation.php
    3.Modules\模块名称\Lang\zh_HK\validation.php

- validation.php格式：
```php
<?php

return [
    'store_member' => [
        'account' => [
            'required' => '账号必填',
            'string' => '账号必须为字符串',
            'max' => '账号最大长度为10',
        ],
        'password' => [
            'required' => '密码必填',
            'string' => '密码必须为字符串',
            'max' => '密码最大长度为10',
        ],
    ],
];
```

- controller会调用service -> service会调用business -> business会调用repository -> repository会调用model
- business路径：
    1.Modules\模块名称\Domain\Business\BusinessName
    2.Modules\模块名称\Business\BusinessName\BusinessName
    3.Modules\模块名称\Business\BusinessName\BusinessName\BusinessName
    4.Modules\模块名称\Business\BusinessName\BusinessName\BusinessName\BusinessName
    5.Modules\模块名称\Business\BusinessName\BusinessName\BusinessName\BusinessName\BusinessName
    6.Modules\模块名称\Business\BusinessName\Bus

- repository路径：
    1.Modules\模块名称\Domain\Repository\RepositoryName

- 路由文件地址：
    1.Modules\模块名称\Admin\route.php
    1.Modules\模块名称\Api\route.php




