---
description: 
globs: 
alwaysApply: true
---
---
description: 错误码规则
globs: 
alwaysApply: false
---
# 错误码模板

## 概述

错误码是系统中用于标识不同错误类型的唯一标识符。本文档提供了错误码的标准模板和最佳实践，包括多语言支持。

## 基本结构（推荐使用EnumEnhance Trait）

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Enums;

use Bingo\Enums\Traits\EnumEnhance;

/**
 * 错误码枚举
 */
enum YourErrorCode: int
{
    use EnumEnhance;

    /**
     * 通用错误码 (10000-10099)
     */
    case UNKNOWN_ERROR = 10000;
    case INVALID_PARAMS = 10001;
    case OPERATION_FAILED = 10002;

    /**
     * 业务错误码 (11000-11999)
     */
    case DATA_NOT_FOUND = 11000;
    case DATA_ALREADY_EXISTS = 11001;
    case DATA_VALIDATION_FAILED = 11002;

    /**
     * 用户相关错误码 (12000-12999)
     */
    case USER_NOT_FOUND = 12000;
    case USER_ALREADY_EXISTS = 12001;
    case USER_PASSWORD_INVALID = 12002;

    /**
     * 获取 HTTP 状态码
     *
     * @return int
     */
    public function httpCode(): int
    {
        return match ($this) {
            self::INVALID_PARAMS, self::DATA_VALIDATION_FAILED => 400,
            self::DATA_NOT_FOUND, self::USER_NOT_FOUND => 404,
            default => 400,
        };
    }
}
```

## 多语言支持

### 1. 语言文件结构

创建多语言文件：`Modules/YourModule/Lang/{locale}/enums.php`

```php
<?php
// Modules/YourModule/Lang/zh_CN/enums.php

use Modules\YourModule\Enums\YourErrorCode;

return [
    YourErrorCode::class => [
        // 通用错误码
        YourErrorCode::UNKNOWN_ERROR->name => '未知错误',
        YourErrorCode::INVALID_PARAMS->name => '无效的参数',
        YourErrorCode::OPERATION_FAILED->name => '操作失败',

        // 业务错误码
        YourErrorCode::DATA_NOT_FOUND->name => '数据不存在',
        YourErrorCode::DATA_ALREADY_EXISTS->name => '数据已存在',
        YourErrorCode::DATA_VALIDATION_FAILED->name => '数据验证失败',

        // 用户相关错误码
        YourErrorCode::USER_NOT_FOUND->name => '用户不存在',
        YourErrorCode::USER_ALREADY_EXISTS->name => '用户已存在',
        YourErrorCode::USER_PASSWORD_INVALID->name => '密码无效',
    ],
];
```

```php
<?php
// Modules/YourModule/Lang/en/enums.php

use Modules\YourModule\Enums\YourErrorCode;

return [
    YourErrorCode::class => [
        // General error codes
        YourErrorCode::UNKNOWN_ERROR->name => 'Unknown error',
        YourErrorCode::INVALID_PARAMS->name => 'Invalid parameters',
        YourErrorCode::OPERATION_FAILED->name => 'Operation failed',

        // Business error codes
        YourErrorCode::DATA_NOT_FOUND->name => 'Data not found',
        YourErrorCode::DATA_ALREADY_EXISTS->name => 'Data already exists',
        YourErrorCode::DATA_VALIDATION_FAILED->name => 'Data validation failed',

        // User related error codes
        YourErrorCode::USER_NOT_FOUND->name => 'User not found',
        YourErrorCode::USER_ALREADY_EXISTS->name => 'User already exists',
        YourErrorCode::USER_PASSWORD_INVALID->name => 'Invalid password',
    ],
];
```

### 2. 获取多语言错误信息

```php
// 使用 EnumEnhance trait 的 description() 方法自动获取多语言错误信息
$errorMessage = YourErrorCode::USER_NOT_FOUND->description();
```

## 传统结构（不推荐，仅用于特殊情况）

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Enums;

/**
 * 错误码枚举（传统方式）
 */
enum YourErrorCode: int
{
    /**
     * 通用错误码
     */
    case UNKNOWN_ERROR = 10000;
    case INVALID_PARAMS = 10001;
    case UNAUTHORIZED = 10002;
    case FORBIDDEN = 10003;
    case NOT_FOUND = 10004;
    case METHOD_NOT_ALLOWED = 10005;
    case VALIDATION_ERROR = 10006;
    case SERVICE_UNAVAILABLE = 10007;

    /**
     * 获取错误信息（不推荐，建议使用多语言）
     *
     * @return string
     */
    public function message(): string
    {
        return match ($this) {
            self::UNKNOWN_ERROR => '未知错误',
            self::INVALID_PARAMS => '无效的参数',
            self::UNAUTHORIZED => '未授权',
            self::FORBIDDEN => '禁止访问',
            self::NOT_FOUND => '资源不存在',
            self::METHOD_NOT_ALLOWED => '方法不允许',
            self::VALIDATION_ERROR => '验证错误',
            self::SERVICE_UNAVAILABLE => '服务不可用',
        };
    }

    /**
     * 获取 HTTP 状态码
     *
     * @return int
     */
    public function httpCode(): int
    {
        return match ($this) {
            self::INVALID_PARAMS, self::VALIDATION_ERROR => 400,
            self::UNAUTHORIZED => 401,
            self::FORBIDDEN => 403,
            self::NOT_FOUND => 404,
            self::METHOD_NOT_ALLOWED => 405,
            self::SERVICE_UNAVAILABLE => 503,
            default => 400,
        };
    }
}
```

## 使用示例

### 1. 抛出异常

```php
use Modules\YourModule\Enums\YourErrorCode;
use Bingo\Exceptions\BizException;

class YourService
{
    public function process(): void
    {
        if (!$this->validate()) {
            BizException::throws(YourErrorCode::INVALID_PARAMS);
        }

        if (!$this->checkPermission()) {
            BizException::throws(YourErrorCode::FORBIDDEN);
        }

        try {
            // 业务逻辑
        } catch (\Exception $e) {
            BizException::throws(YourErrorCode::OPERATION_FAILED);
        }
    }
}
```

### 2. 多语言错误信息获取

```php
use Modules\YourModule\Enums\YourErrorCode;

class YourService
{
    public function getErrorMessage(): string
    {
        // 使用 EnumEnhance trait 的 description() 方法
        return YourErrorCode::USER_NOT_FOUND->description();
        
        // 传统方式（不推荐）
        return YourErrorCode::USER_NOT_FOUND->message();
    }
}
```

### 3. 异常处理

```php
use Modules\YourModule\Enums\YourErrorCode;
use Bingo\Exceptions\BizException;

class ExceptionHandler extends Handler
{
    public function render($request, Throwable $e)
    {
        if ($e instanceof BizException) {
            return response()->json([
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], $e->getHttpCode());
        }

        return parent::render($request, $e);
    }
}
```

### 4. 错误响应

```php
use Modules\YourModule\Enums\YourErrorCode;

class YourController
{
    public function show($id)
    {
        $data = $this->service->find($id);
        
        if (!$data) {
            return response()->json([
                'code' => YourErrorCode::DATA_NOT_FOUND->value,
                'message' => YourErrorCode::DATA_NOT_FOUND->description(), // 多语言支持
            ], YourErrorCode::DATA_NOT_FOUND->httpCode());
        }

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $data
        ]);
    }
}
```

## 规范要求

1. **错误码定义**
   - 使用枚举类型
   - 错误码范围划分（按模块分配5位数字）
   - 错误信息明确
   - HTTP 状态码映射
   - 推荐使用 `EnumEnhance` trait

2. **错误码分类**
   - 通用错误码（模块号000-099）
   - 业务错误码（模块号100-999）
   - 模块间错误码要避免冲突

3. **错误码格式**
   - 5位数字
   - 模块前缀（前2位）
   - 错误类型（第3位）
   - 具体错误（后2位）

4. **多语言支持**
   - 使用 `enums.php` 文件存储错误信息
   - 支持至少中文简体、英文、中文繁体
   - 使用枚举类名作为键
   - 使用 `->name` 属性作为错误码标识

## 最佳实践

### 1. 错误码定义

```php
// 好的实践 - 使用 EnumEnhance trait
enum OrderErrorCode: int
{
    use EnumEnhance;
    
    case ORDER_NOT_FOUND = 13000;
    case ORDER_STATUS_INVALID = 13001;
}

// 不好的实践 - 硬编码错误信息
enum OrderErrorCode: int
{
    case ORDER_NOT_FOUND = 13000;
    case ORDER_STATUS_INVALID = 13001;
    
    public function message(): string
    {
        return match ($this) {
            self::ORDER_NOT_FOUND => '订单不存在',
            self::ORDER_STATUS_INVALID => '订单状态无效',
        };
    }
}
```

### 2. 多语言错误信息

```php
// 好的实践 - 详细的错误信息
return [
    OrderErrorCode::class => [
        OrderErrorCode::ORDER_NOT_FOUND->name => '订单不存在，请检查订单号',
        OrderErrorCode::ORDER_STATUS_INVALID->name => '订单状态无效，只能是待支付、已支付状态',
    ],
];

// 不好的实践 - 模糊的错误信息
return [
    OrderErrorCode::class => [
        OrderErrorCode::ORDER_NOT_FOUND->name => '订单错误',
        OrderErrorCode::ORDER_STATUS_INVALID->name => '状态错误',
    ],
];
```

### 3. 错误码使用

```php
// 好的实践 - 统一使用 BizException::throws()
if (!$order) {
    BizException::throws(OrderErrorCode::ORDER_NOT_FOUND);
}

// 不好的实践 - 直接抛出异常
if (!$order) {
    throw new Exception('订单不存在');
}
```

## 注意事项

1. **错误码要唯一** - 避免模块间错误码冲突
2. **错误信息要明确** - 提供有用的错误描述
3. **合理使用 HTTP 状态码** - 映射到正确的HTTP状态
4. **避免暴露敏感信息** - 不要在错误信息中包含敏感数据
5. **保持向后兼容** - 谨慎修改已存在的错误码
6. **完善错误文档** - 维护错误码文档
7. **统一异常处理** - 使用 `BizException::throws()`
8. **多语言支持** - 使用 `EnumEnhance` trait 和语言文件
9. **记录错误日志** - 重要错误要记录日志
10. **定期维护更新** - 定期审查和更新错误码
