---
description: 
globs: 
alwaysApply: true
---
# 前端测试通用规则

**核心原则：简洁有效，覆盖全面**

## 组织与命名
- 测试文件命名: `{Component}.spec.ts` 或 `{功能}.test.ts`
- 测试套件命名: `describe('组件/功能名称', ...)`
- 测试用例命名: `it('应该实现什么功能', ...)`、`test('当什么条件时应该怎样', ...)`
- 保持每个测试用例的独立性，不依赖其他测试用例的执行结果
- 遵循 AAA 模式：Arrange(准备)、Act(执行)、Assert(断言)jiany

## 编码约定
- 为测试套件和用例添加清晰的描述，表明测试目的
- 使用 Vitest/Jest 作为测试框架，对UI组件使用 Vue Test Utils
- 使用 `expect()` 断言，提供明确的失败消息
- 使用 `beforeEach` 设置共享的测试前置条件
- 使用 `vi.mock()` 模拟外部依赖，如API服务
- 异常测试使用 `expect().toThrow()` 捕获预期错误
- 使用 `.each()` 进行参数化测试，提高测试效率
- 当测试用例过于复杂时，拆分为多个小的测试用例

## 组件测试策略
- 单元测试侧重于组件的逻辑函数和计算属性
- 组件测试关注组件的渲染结果和交互行为
- 集成测试验证多个组件的协同工作
- 组件交互测试使用 `wrapper.trigger()` 模拟用户行为
- 异步测试使用 `async/await` 或 `flush-promises`
- 路由和状态管理相关测试使用适当的测试夹具(fixtures)

## 数据处理
- 使用工厂函数创建测试数据，避免测试间数据干扰
- 使用 `vi.mock()` 模拟Axios或Fetch请求
- 针对不同场景创建专门的测试固件(fixtures)
- 避免在测试中使用真实的API调用，总是使用模拟数据

## 覆盖率目标
- 核心组件和工具函数覆盖率 ≥ 90%
- 业务逻辑和状态管理覆盖率 ≥ 80%
- UI组件覆盖率 ≥ 70%
- 路由和页面组件通过集成测试覆盖

## 测试类型划分
- **单元测试**: 测试独立函数和方法，如工具函数、计算属性
- **组件测试**: 测试单个组件的渲染和交互
- **集成测试**: 测试多个组件协同工作
- **E2E测试**: 使用Cypress等工具测试完整用户流程

## 组件单元测试示例代码

```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import UserProfile from '@/components/UserProfile.vue'
import { useUserStore } from '@/stores/user'
import type { User } from '@/types'

describe('UserProfile组件', () => {
  // 测试数据
  const mockUser: User = {
    id: 1,
    name: '测试用户',
    email: '<EMAIL>',
    role: 'user',
    avatar: '/images/avatar.jpg'
  }
  
  // 预设store
  beforeEach(() => {
    // 创建新的pinia实例，避免测试间状态污染
    setActivePinia(createPinia())
    
    // 预设store状态
    const userStore = useUserStore()
    userStore.user = mockUser
    userStore.isLoggedIn = true
  })
  
  it('正确渲染用户资料', () => {
    // 准备
    const wrapper = mount(UserProfile)
    
    // 断言
    expect(wrapper.find('.user-name').text()).toBe('测试用户')
    expect(wrapper.find('.user-email').text()).toBe('<EMAIL>')
    expect(wrapper.find('img.avatar').attributes('src')).toBe('/images/avatar.jpg')
  })
  
  it('根据用户角色显示管理员标签', async () => {
    // 准备
    const userStore = useUserStore()
    userStore.user = { ...mockUser, role: 'admin' }
    const wrapper = mount(UserProfile)
    
    // 断言
    expect(wrapper.find('.admin-badge').exists()).toBe(true)
    
    // 修改角色
    userStore.user = { ...mockUser, role: 'user' }
    await wrapper.vm.$nextTick()
    
    // 断言标签不存在
    expect(wrapper.find('.admin-badge').exists()).toBe(false)
  })
  
  it('点击编辑按钮显示编辑表单', async () => {
    // 准备
    const wrapper = mount(UserProfile)
    
    // 前置断言
    expect(wrapper.find('.edit-form').exists()).toBe(false)
    
    // 执行点击
    await wrapper.find('.edit-button').trigger('click')
    
    // 断言
    expect(wrapper.find('.edit-form').exists()).toBe(true)
  })
  
  it('提交表单时调用更新方法', async () => {
    // 准备
    const userStore = useUserStore()
    const updateUserSpy = vi.spyOn(userStore, 'updateUser').mockResolvedValue(undefined)
    const wrapper = mount(UserProfile)
    
    // 显示表单
    await wrapper.find('.edit-button').trigger('click')
    
    // 修改输入值
    await wrapper.find('input[name="name"]').setValue('新用户名')
    await wrapper.find('input[name="email"]').setValue('<EMAIL>')
    
    // 提交表单
    await wrapper.find('form').trigger('submit.prevent')
    
    // 断言
    expect(updateUserSpy).toHaveBeenCalledTimes(1)
    expect(updateUserSpy).toHaveBeenCalledWith({
      id: 1,
      name: '新用户名',
      email: '<EMAIL>',
      role: 'user',
      avatar: '/images/avatar.jpg'
    })
  })
  
  it('处理表单验证错误', async () => {
    // 准备
    const wrapper = mount(UserProfile)
    
    // 显示表单
    await wrapper.find('.edit-button').trigger('click')
    
    // 设置无效的电子邮件
    await wrapper.find('input[name="email"]').setValue('invalid-email')
    
    // 提交表单
    await wrapper.find('form').trigger('submit.prevent')
    
    // 断言错误消息显示
    expect(wrapper.find('.error-message').exists()).toBe(true)
    expect(wrapper.find('.error-message').text()).toContain('有效的电子邮件')
  })
})
```

## Pinia 状态管理测试示例

```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useUserStore } from '@/stores/user'
import { userApi } from '@/services/api'

// 模拟API服务
vi.mock('@/services/api', () => ({
  userApi: {
    login: vi.fn(),
    logout: vi.fn(),
    fetchUser: vi.fn(),
    updateUser: vi.fn()
  }
}))

describe('User Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.resetAllMocks()
  })
  
  describe('actions', () => {
    it('login成功后应设置用户信息和登录状态', async () => {
      // 准备
      const mockUser = { id: 1, name: '测试用户', role: 'user' }
      const mockCredentials = { email: '<EMAIL>', password: 'password' }
      userApi.login.mockResolvedValue({ data: { user: mockUser, token: 'mock-token' } })
      
      // 执行
      const store = useUserStore()
      await store.login(mockCredentials)
      
      // 断言
      expect(userApi.login).toHaveBeenCalledWith(mockCredentials)
      expect(store.user).toEqual(mockUser)
      expect(store.token).toBe('mock-token')
      expect(store.isLoggedIn).toBe(true)
      expect(localStorage.getItem('auth_token')).toBe('mock-token')
    })
    
    it('login失败时应抛出错误', async () => {
      // 准备
      const error = new Error('认证失败')
      userApi.login.mockRejectedValue(error)
      
      // 执行与断言
      const store = useUserStore()
      await expect(store.login({ email: '<EMAIL>', password: 'wrong' }))
        .rejects.toThrow('认证失败')
      
      // 验证状态未改变
      expect(store.isLoggedIn).toBe(false)
      expect(store.user).toBeNull()
    })
    
    it('logout应清除用户状态', async () => {
      // 准备
      userApi.logout.mockResolvedValue({})
      const store = useUserStore()
      store.token = 'mock-token'
      store.user = { id: 1, name: '测试用户' }
      store.isLoggedIn = true
      localStorage.setItem('auth_token', 'mock-token')
      
      // 执行
      await store.logout()
      
      // 断言
      expect(userApi.logout).toHaveBeenCalled()
      expect(store.token).toBeNull()
      expect(store.user).toBeNull()
      expect(store.isLoggedIn).toBe(false)
      expect(localStorage.getItem('auth_token')).toBeNull()
    })
    
    it('updateUser应更新用户信息', async () => {
      // 准备
      const currentUser = { id: 1, name: '旧名字', email: '<EMAIL>' }
      const updatedUser = { id: 1, name: '新名字', email: '<EMAIL>' }
      userApi.updateUser.mockResolvedValue({ data: updatedUser })
      
      // 设置初始状态
      const store = useUserStore()
      store.user = currentUser
      
      // 执行
      await store.updateUser({ name: '新名字', email: '<EMAIL>' })
      
      // 断言
      expect(userApi.updateUser).toHaveBeenCalledWith(1, { name: '新名字', email: '<EMAIL>' })
      expect(store.user).toEqual(updatedUser)
    })
  })
  
  describe('getters', () => {
    it('userFullName应返回正确的全名', () => {
      // 准备
      const store = useUserStore()
      store.user = { id: 1, firstName: '张', lastName: '三', email: '<EMAIL>' }
      
      // 断言
      expect(store.userFullName).toBe('张三')
    })
    
    it('isAdmin应正确识别管理员角色', () => {
      // 准备
      const store = useUserStore()
      
      // 用户角色
      store.user = { id: 1, name: '普通用户', role: 'user' }
      expect(store.isAdmin).toBe(false)
      
      // 管理员角色
      store.user = { id: 2, name: '管理员', role: 'admin' }
      expect(store.isAdmin).toBe(true)
    })
  })
})
```

## Vue 组合式API 测试示例

```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useCounter } from '@/composables/useCounter'
import { mount } from '@vue/test-utils'

// 测试具体组合式函数
describe('useCounter组合式函数', () => {
  it('应提供初始计数和操作方法', () => {
    // 执行
    const { count, increment, decrement, reset } = useCounter(10)
    
    // 断言
    expect(count.value).toBe(10)
    expect(typeof increment).toBe('function')
    expect(typeof decrement).toBe('function')
    expect(typeof reset).toBe('function')
  })
  
  it('increment应增加计数', () => {
    // 准备
    const { count, increment } = useCounter(5)
    
    // 执行
    increment()
    
    // 断言
    expect(count.value).toBe(6)
    
    // 再次执行
    increment(3)
    expect(count.value).toBe(9)
  })
  
  it('decrement应减少计数', () => {
    // 准备
    const { count, decrement } = useCounter(5)
    
    // 执行
    decrement()
    
    // 断言
    expect(count.value).toBe(4)
    
    // 再次执行
    decrement(2)
    expect(count.value).toBe(2)
  })
  
  it('reset应重置计数', () => {
    // 准备
    const { count, increment, reset } = useCounter(5)
    
    // 修改值
    increment(10)
    expect(count.value).toBe(15)
    
    // 执行
    reset()
    
    // 断言
    expect(count.value).toBe(5)
    
    // 指定重置值
    reset(100)
    expect(count.value).toBe(100)
  })
})

// 在组件中测试组合式函数
describe('在组件中使用useCounter', () => {
  // 创建测试组件
  const TestComponent = {
    template: `
      <div>
        <span class="count">{{ count }}</span>
        <button class="increment" @click="increment">增加</button>
        <button class="decrement" @click="decrement">减少</button>
        <button class="reset" @click="reset">重置</button>
      </div>
    `,
    setup() {
      const { count, increment, decrement, reset } = useCounter(0)
      return { count, increment, decrement, reset }
    }
  }
  
  it('应正确渲染和响应用户操作', async () => {
    // 准备
    const wrapper = mount(TestComponent)
    
    // 初始状态
    expect(wrapper.find('.count').text()).toBe('0')
    
    // 执行增加操作
    await wrapper.find('.increment').trigger('click')
    expect(wrapper.find('.count').text()).toBe('1')
    
    // 执行减少操作
    await wrapper.find('.decrement').trigger('click')
    expect(wrapper.find('.count').text()).toBe('0')
    
    // 先增加几次
    await wrapper.find('.increment').trigger('click')
    await wrapper.find('.increment').trigger('click')
    expect(wrapper.find('.count').text()).toBe('2')
    
    // 执行重置操作
    await wrapper.find('.reset').trigger('click')
    expect(wrapper.find('.count').text()).toBe('0')
  })
})