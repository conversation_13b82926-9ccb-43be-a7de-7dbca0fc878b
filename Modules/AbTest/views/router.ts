import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
    {
        path: '/abtest',
        component: () => import('/admin/layout/index.vue'),
        meta: { title: 'AuditLog.router.auditLogManagement', icon: 'Monitor' },
        children: [
            {
                path: 'list',
                name: 'abTestList',
                meta: { title: '系統管理面板', icon: 'DataBoard' },
                component: () => import('./ui/list.vue'),
            },
            {
                path: 'settings',
                name: 'abTestSettings',
                meta: { title: '系統記錄', icon: 'Document' },
                component: () => import('./ui/setting.vue'),
            },
        ],
    },
]

export default router
