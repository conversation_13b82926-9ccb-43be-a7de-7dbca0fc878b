<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top">
          <!-- Logo设置 -->
          <el-form-item label="Logo">
            <el-switch
              v-model="showLogo"
              @change="handleLogoVisibilityChange"
              class="mb-2"
            />
            
            <template v-if="showLogo">
              <div class="image-preview-container">
                <div v-if="logoUrl" class="image-preview">
                  <img :src="logoUrl" class="preview-img" />
                </div>
                <div v-else class="image-placeholder">
                  <el-icon class="placeholder-icon"><Picture /></el-icon>
                  <div class="placeholder-text">暂无Logo</div>
                </div>
              </div>

              <div class="button-group">
                <el-button type="primary" class="button-no-border" @click="openLogoFileManager">
                  <el-icon class="icon"><Upload /></el-icon>
                  <span>选择Logo</span>
                </el-button>
                <el-button v-if="logoUrl" class="delete-btn" @click="confirmDeleteLogo">
                  <el-icon class="icon"><Delete /></el-icon>
                  <span>删除Logo</span>
                </el-button>
              </div>

              <el-form-item label="Logo高度">
                <el-input-number 
                  v-model="logoHeight" 
                  :min="20" 
                  :max="100" 
                  @change="markAsChanged"
                  size="default"
                  controls-position="right"
                />
                <span class="unit-text">px</span>
              </el-form-item>
            </template>
          </el-form-item>

          <!-- 导航链接设置 -->
          <el-form-item label="导航链接">
            <div v-for="(item, index) in navItems" :key="index" class="nav-item-container">
              <div class="nav-item-header">
                <span>导航项 {{ index + 1 }}</span>
                <el-button 
                  type="danger" 
                  size="small" 
                  circle
                  @click="removeNavItem(index)"
                  v-if="navItems.length > 1"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
              
              <el-input
                v-model="item.text"
                placeholder="链接文本"
                class="mb-2"
                @input="markAsChanged"
              />
              <el-input
                v-model="item.href"
                placeholder="链接地址"
                class="mb-2"
                @input="markAsChanged"
              />
            </div>
            
            <el-button 
              type="primary" 
              plain 
              class="add-nav-btn" 
              @click="addNavItem"
            >
              <el-icon><Plus /></el-icon>
              添加导航项
            </el-button>
          </el-form-item>

          <!-- 登录按钮设置 -->
          <el-form-item label="登录按钮">
            <el-switch
              v-model="showLoginButton"
              @change="markAsChanged"
              class="mb-2"
            />
            
            <template v-if="showLoginButton">
              <el-input
                v-model="loginButton.text"
                placeholder="按钮文本"
                class="mb-2"
                @input="markAsChanged"
              />
              <el-input
                v-model="loginButton.href"
                placeholder="按钮链接"
                class="mb-2"
                @input="markAsChanged"
              />
            </template>
          </el-form-item>

          <!-- 操作按钮设置 -->
          <el-form-item label="主操作按钮">
            <el-switch
              v-model="showActionButton"
              @change="markAsChanged"
              class="mb-2"
            />
            
            <template v-if="showActionButton">
              <el-input
                v-model="actionButton.text"
                placeholder="按钮文本"
                class="mb-2"
                @input="markAsChanged"
              />
              <el-input
                v-model="actionButton.href"
                placeholder="按钮链接"
                class="mb-2"
                @input="markAsChanged"
              />
            </template>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="样式" name="style">
        <el-form label-position="top">
          <!-- 导航栏背景色 -->
          <el-form-item label="背景颜色">
            <el-select 
              v-model="navbarBg" 
              @change="markAsChanged"
              class="w-100"
            >
              <el-option label="透明白色" value="bg-transparent" />
              <el-option label="白色" value="bg-white" />
              <el-option label="浅色" value="bg-light" />
              <el-option label="深色" value="bg-dark navbar-dark" />
              <el-option label="主题色" value="bg-primary navbar-dark" />
              <el-option label="自定义" value="bg-custom" />
            </el-select>

            <!-- 自定义背景色 -->
            <template v-if="navbarBg === 'bg-custom'">
              <div class="mt-2 color-input-row">
                <span class="color-hash">#</span>
                <el-input
                  v-model="customBgColor"
                  placeholder="输入颜色代码"
                  class="color-input"
                  @input="markAsChanged"
                />
                <div 
                  class="color-preview" 
                  :style="{ backgroundColor: customBgColor }"
                ></div>
                <el-color-picker
                  v-model="customBgColor"
                  @change="markAsChanged"
                  show-alpha
                />
              </div>
            </template>
          </el-form-item>

          <!-- 导航栏高度 -->
          <el-form-item label="导航栏高度">
            <el-input-number 
              v-model="navbarHeight" 
              :min="40" 
              :max="120" 
              @change="markAsChanged"
              size="default"
              controls-position="right"
            />
            <span class="unit-text">px</span>
          </el-form-item>

          <!-- 导航栏定位 -->
          <el-form-item label="导航栏定位">
            <el-select 
              v-model="navbarPosition" 
              @change="markAsChanged"
              class="w-100"
            >
              <el-option label="默认" value="" />
              <el-option label="固定顶部" value="fixed-top" />
              <el-option label="粘性顶部" value="sticky-top" />
            </el-select>
          </el-form-item>

          <!-- 导航栏阴影 -->
          <el-form-item label="导航栏阴影">
            <el-switch
              v-model="navbarShadow"
              @change="markAsChanged"
              class="mb-2"
            />
          </el-form-item>

          <!-- 导航栏内边距 -->
          <el-form-item label="内边距">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="水平内边距">
                  <el-input-number 
                    v-model="navbarPaddingX" 
                    :min="0" 
                    :max="50" 
                    @change="markAsChanged"
                    size="default"
                    controls-position="right"
                  />
                  <span class="unit-text">px</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="垂直内边距">
                  <el-input-number 
                    v-model="navbarPaddingY" 
                    :min="0" 
                    :max="50" 
                    @change="markAsChanged"
                    size="default"
                    controls-position="right"
                  />
                  <span class="unit-text">px</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form-item>

          <!-- 导航项样式 -->
          <el-form-item label="导航项样式">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="字体大小">
                  <el-input-number 
                    v-model="navItemFontSize" 
                    :min="12" 
                    :max="24" 
                    @change="markAsChanged"
                    size="default"
                    controls-position="right"
                  />
                  <span class="unit-text">px</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字体粗细">
                  <el-select 
                    v-model="navItemFontWeight" 
                    @change="markAsChanged"
                    class="w-100"
                  >
                    <el-option label="常规" value="normal" />
                    <el-option label="中等" value="500" />
                    <el-option label="加粗" value="bold" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form-item>

          <!-- 登录按钮样式 -->
          <el-form-item label="登录按钮样式" v-if="showLoginButton">
            <el-select 
              v-model="loginButton.style" 
              @change="markAsChanged"
              class="w-100"
            >
              <el-option label="主要轮廓" value="btn-outline-primary" />
              <el-option label="次要轮廓" value="btn-outline-secondary" />
              <el-option label="主要按钮" value="btn-primary" />
              <el-option label="次要按钮" value="btn-secondary" />
              <el-option label="链接按钮" value="btn-link" />
            </el-select>
          </el-form-item>

          <!-- 主操作按钮样式 -->
          <el-form-item label="主操作按钮样式" v-if="showActionButton">
            <el-select 
              v-model="actionButton.style" 
              @change="markAsChanged"
              class="w-100"
            >
              <el-option label="主要按钮" value="btn-primary" />
              <el-option label="次要按钮" value="btn-secondary" />
              <el-option label="主要轮廓" value="btn-outline-primary" />
              <el-option label="次要轮廓" value="btn-outline-secondary" />
              <el-option label="链接按钮" value="btn-link" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">应用更改</el-button>
    </div>

    <!-- 文件管理器弹窗 -->
    <DocumentsManager 
      :BaseUrl="baseUrl" 
      :token="token" 
      :isMultiSelect="false"
      :locale="localeLang"
      @confirmSelection="confirmLogoSelection" 
      ref="documentsManagerRef" 
      v-model="visibleDialog"
      :showUploadButton="false" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, computed, watch } from 'vue'
import { Picture, Upload, Delete, Plus } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { DocumentsManager } from 'filestudio-bingo'
import { env, getAuthToken } from '/admin/support/helper'

// 定义组件名称
defineOptions({
  name: 'NavEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 原始HTML
const originalHtml = ref('')

// Logo相关
const logoUrl = ref('')
const logoHeight = ref(30)

// 导航项
const navItems = ref([
  { text: 'Home', href: '#' },
  { text: 'Features', href: '#' },
  { text: 'Pricing', href: '#' }
])

// 操作按钮
const showActionButton = ref(true)
const actionButton = ref({
  text: 'Get started',
  href: '#',
  style: 'btn-primary'
})

// 登录按钮
const showLoginButton = ref(true)
const loginButton = ref({
  text: '登录',
  href: '#',
  style: 'btn-outline-primary'
})

// 样式设置
const navbarBg = ref('bg-transparent')
const navbarPaddingX = ref(15)
const navbarPaddingY = ref(8)
const navbarPosition = ref('')
const navbarShadow = ref(false)
const navbarHeight = ref(70)

// Logo显示控制
const showLogo = ref(true)

// 新增样式控制
const customBgColor = ref('')
const navItemFontSize = ref(16)
const navItemFontWeight = ref('normal')

// 文件管理器相关
const visibleDialog = ref(false)
const documentsManagerRef = ref(null)
let tokens: string | null = getAuthToken()
const token = ref<string>(tokens ?? '')
const baseUrl = ref<string>(env('VITE_BASE_URL').replace('/admin/', '/'))
const localeLang = computed(() => localStorage.getItem('bwms_language') || 'zh_CN')

/**
 * 设置默认值
 */
const setDefaultValues = () => {
  logoUrl.value = 'https://7528302.fs1.hubspotusercontent-na1.net/hub/7528302/hubfs/theme_hubspot/elevate/images/hexagontalxio-dark.png'
  logoHeight.value = 30
  
  navItems.value = [
    { text: '首页', href: '#' },
    { text: '功能', href: '#' },
    { text: '价格', href: '#' },
    { text: '关于', href: '#' }
  ]
  
  showLoginButton.value = true
  loginButton.value = {
    text: '登录',
    href: '#',
    style: 'btn-outline-primary'
  }
  
  showActionButton.value = true
  actionButton.value = {
    text: '立即开始',
    href: '#',
    style: 'btn-primary'
  }
  
  navbarBg.value = 'bg-transparent'
  navbarPaddingX.value = 15
  navbarPaddingY.value = 8
  navbarPosition.value = 'fixed-top'
  navbarShadow.value = false
  navbarHeight.value = 70
  showLogo.value = true
}

/**
 * 提取导航栏数据
 */
const extractNavbarData = (): boolean => {
  if (!props.blockElement) return false
  
  try {
    // 保存原始HTML
    originalHtml.value = props.blockElement.outerHTML
    
    // 提取Logo相关
    const logoImg = props.blockElement.querySelector('.navbar-brand img')
    if (logoImg) {
      showLogo.value = true
      logoUrl.value = logoImg.getAttribute('src') || ''
      logoHeight.value = parseInt(logoImg.getAttribute('height') || '30')
    } else {
      showLogo.value = false
      logoUrl.value = ''
    }
    
    // 提取导航栏样式
    const navbarWrapper = props.blockElement.querySelector('.navbar-wrapper')
    
    if (navbarWrapper) {
      const styles = window.getComputedStyle(navbarWrapper)
      
      // 提取高度
      navbarHeight.value = parseInt(styles.height) || 70
      
      // 提取内边距
      navbarPaddingX.value = parseInt(styles.paddingLeft) || 15
      navbarPaddingY.value = parseInt(styles.paddingTop) || 8
      
      // 提取阴影
      navbarShadow.value = styles.boxShadow !== 'none'
    }
    
    // 提取导航项样式
    const navLink = props.blockElement.querySelector('.nav-link')
    if (navLink) {
      const styles = window.getComputedStyle(navLink)
      navItemFontSize.value = parseInt(styles.fontSize) || 16
      navItemFontWeight.value = styles.fontWeight
    }
    
    // 提取导航项 - 适配新的平铺式导航
    const navLinks = props.blockElement.querySelectorAll('.nav-link')
    if (navLinks.length > 0) {
      // 过滤掉按钮内的链接，只保留导航链接
      const navLinksArray = Array.from(navLinks).filter(link => 
        !link.parentElement?.classList.contains('btn')
      )
      
      navItems.value = navLinksArray.map(link => ({
        text: link.textContent || '',
        href: link.getAttribute('href') || '#'
      }))
    }
    
    // 提取按钮
    const buttons = props.blockElement.querySelectorAll('.btn')
    
    // 重置按钮状态
    showLoginButton.value = false
    showActionButton.value = false
    
    if (buttons.length > 0) {
      // 如果有多个按钮，第一个通常是登录按钮
      if (buttons.length >= 2) {
        const loginBtn = buttons[0]
        showLoginButton.value = true
        loginButton.value = {
          text: loginBtn.textContent?.trim() || '登录',
          href: loginBtn.getAttribute('href') || '#',
          style: Array.from(loginBtn.classList)
            .find(cls => cls.startsWith('btn-')) || 'btn-outline-primary'
        }
        
        // 第二个是主操作按钮
        const actionBtn = buttons[1]
        showActionButton.value = true
        actionButton.value = {
          text: actionBtn.textContent?.trim() || '立即开始',
          href: actionBtn.getAttribute('href') || '#',
          style: Array.from(actionBtn.classList)
            .find(cls => cls.startsWith('btn-')) || 'btn-primary'
        }
      } else {
        // 只有一个按钮时，视为主操作按钮
        const actionBtn = buttons[0]
        showActionButton.value = true
        actionButton.value = {
          text: actionBtn.textContent?.trim() || '立即开始',
          href: actionBtn.getAttribute('href') || '#',
          style: Array.from(actionBtn.classList)
            .find(cls => cls.startsWith('btn-')) || 'btn-primary'
        }
      }
    }
    
    // 提取样式 - 适配新结构
    if (props.blockElement.style.backgroundColor) {
      if (props.blockElement.style.backgroundColor.includes('rgba(255, 255, 255')) {
        navbarBg.value = 'bg-white'
      } else if (props.blockElement.style.backgroundColor.includes('rgba(0, 0, 0')) {
        navbarBg.value = 'bg-dark navbar-dark'
      } else if (props.blockElement.style.backgroundColor.includes('rgb(37, 99, 235)')) {
        navbarBg.value = 'bg-primary navbar-dark'
      } else if (props.blockElement.style.backgroundColor.includes('transparent')) {
        navbarBg.value = 'bg-transparent'
      } else {
        navbarBg.value = 'bg-white'
      }
    }
    
    // 定位检测
    const position = window.getComputedStyle(props.blockElement).position
    if (position === 'fixed') navbarPosition.value = 'fixed-top'
    else if (position === 'sticky') navbarPosition.value = 'sticky-top'
    else navbarPosition.value = ''
    
    return true
  } catch (error) {
    console.error('提取导航栏数据时出错:', error)
    return false
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置更改状态
    isChanged.value = false
    
    // 重新提取数据
    const extracted = extractNavbarData()
    
    // 如果提取失败，使用默认值
    if (!extracted) {
      setDefaultValues()
    }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  // 移除原有的初始化逻辑，因为已经由 watch 处理
  isChanged.value = false
})

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 打开文件管理器
const openLogoFileManager = () => {
  visibleDialog.value = true
}

// 确认删除Logo
const confirmDeleteLogo = () => {
  ElMessageBox.confirm(
    '确定要删除当前Logo吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    logoUrl.value = ''
    markAsChanged()
    ElMessage.success('Logo已删除')
  }).catch(() => {
    // 取消删除，不做任何操作
  })
}

// Logo选择确认
const confirmLogoSelection = (selectedFiles: any[]) => {
  if (!selectedFiles || selectedFiles.length === 0) return
  
  const file = selectedFiles[0]
  logoUrl.value = file.path || file.url
  markAsChanged()
}

// 添加导航项
const addNavItem = () => {
  navItems.value.push({
    text: '新导航项',
    href: '#'
  })
  markAsChanged()
}

// 删除导航项
const removeNavItem = (index: number) => {
  navItems.value.splice(index, 1)
  markAsChanged()
}

// Logo显示切换处理
const handleLogoVisibilityChange = () => {
  if (!showLogo.value) {
    logoUrl.value = ''
  }
  markAsChanged()
}

// 生成导航栏HTML
const generateNavbarHtml = (): string => {
  try {
    // 检查是否有原始HTML可以作为基础
    if (!originalHtml.value || !props.blockElement) {
      return generateDefaultNavbarHtml()
    }

    // 创建一个临时元素来操作DOM
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = originalHtml.value

    // 获取导航栏元素
    const navElement = tempDiv.querySelector('nav')
    if (!navElement) {
      return generateDefaultNavbarHtml()
    }

    // 更新Logo
    const logoImg = navElement.querySelector('.navbar-brand img')
    if (logoImg) {
      logoImg.setAttribute('src', logoUrl.value)
      logoImg.setAttribute('height', logoHeight.value.toString())
    }

    // 更新导航链接
    const navLinksContainer = navElement.querySelector('.navbar-links')
    if (navLinksContainer) {
      // 移除旧的导航链接和按钮
      const oldLinks = Array.from(navLinksContainer.querySelectorAll('.nav-link:not(.btn .nav-link)'))
      const oldButtons = Array.from(navLinksContainer.querySelectorAll('.btn'))
      
      // 移除旧的导航链接
      oldLinks.forEach(link => {
        if (!link.closest('.btn')) { // 确保不是按钮内的链接
          link.remove()
        }
      })
      
      // 移除旧的按钮
      oldButtons.forEach(button => button.remove())
      
      // 添加新的导航链接
      navItems.value.forEach(item => {
        const link = document.createElement('a')
        link.className = 'nav-link'
        link.href = item.href
        link.textContent = item.text
        navLinksContainer.appendChild(link)
      })
      
      // 添加按钮
      if (showLoginButton.value) {
        const loginBtn = document.createElement('button')
        loginBtn.className = `btn ${loginButton.value.style} me-2`
        loginBtn.textContent = loginButton.value.text
        navLinksContainer.appendChild(loginBtn)
      }
      
      if (showActionButton.value) {
        const actionBtn = document.createElement('button')
        actionBtn.className = `btn ${actionButton.value.style}`
        actionBtn.textContent = actionButton.value.text
        navLinksContainer.appendChild(actionBtn)
      }
    }

    // 更新导航栏高度
    const navbarWrapper = navElement.querySelector('.navbar-wrapper')
    if (navbarWrapper && navbarWrapper instanceof HTMLElement) {
      let heightValue = '70px'
      if (navbarPaddingX.value === 15) heightValue = '60px'
      else if (navbarPaddingX.value === 20) heightValue = '80px'
      
      // 设置高度样式
      navbarWrapper.style.height = heightValue
    }

    // 更新定位类
    navElement.className = navElement.className
      .replace(/\bfixed-top\b|\bfixed-bottom\b|\bsticky-top\b/g, '')
      .trim()
    
    if (navbarPosition.value) {
      navElement.classList.add(navbarPosition.value)
    }

    // 更新导航栏样式
    if (navbarBg.value === 'bg-custom') {
      navElement.style.backgroundColor = customBgColor.value
      navElement.className = navElement.className.replace(/bg-\w+/g, '').trim()
    } else {
      navElement.style.backgroundColor = ''
      navElement.className = `${navElement.className.replace(/bg-\w+/g, '').trim()} ${navbarBg.value}`
    }
    
    // 设置阴影
    if (navbarShadow.value) {
      navElement.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)'
    } else {
      navElement.style.boxShadow = 'none'
    }

    // 更新导航栏包装器样式
    if (navbarWrapper && navbarWrapper instanceof HTMLElement) {
      navbarWrapper.style.height = `${navbarHeight.value}px`
      navbarWrapper.style.padding = `${navbarPaddingY.value}px ${navbarPaddingX.value}px`
    }

    // 更新导航项样式
    const navLinks = tempDiv.querySelectorAll('.nav-link')
    navLinks.forEach(link => {
      if (link instanceof HTMLElement) {
        link.style.fontSize = `${navItemFontSize.value}px`
        link.style.fontWeight = navItemFontWeight.value
      }
    })

    // 返回更新后的HTML
    return tempDiv.innerHTML
  } catch (error) {
    console.error('生成导航栏HTML时出错:', error)
    return generateDefaultNavbarHtml()
  }
}

// 生成默认导航栏HTML（当无法基于原始HTML修改时）
const generateDefaultNavbarHtml = (): string => {
  // 生成导航项HTML
  const navItemsHtml = navItems.value.map(item => `
    <a class="nav-link" href="${item.href}">${item.text}</a>
  `).join('\n')
  
  // 生成按钮HTML
  let buttonsHtml = ''
  
  if (showLoginButton.value) {
    buttonsHtml += `<button class="btn ${loginButton.value.style} me-2">${loginButton.value.text}</button>\n`
  }
  
  if (showActionButton.value) {
    buttonsHtml += `<button class="btn ${actionButton.value.style}">${actionButton.value.text}</button>\n`
  }
  
  // 设置高度类
  let heightClass = ''
  if (navbarPaddingX.value === 15) heightClass = 'height: 60px;'
  else if (navbarPaddingX.value === 20) heightClass = 'height: 80px;'
  else heightClass = 'height: 70px;'
  
  // 设置导航栏样式
  const navbarStyles = [
    `height: ${navbarHeight.value}px`,
    `padding: ${navbarPaddingY.value}px ${navbarPaddingX.value}px`
  ]
  
  if (navbarShadow.value) {
    navbarStyles.push('box-shadow: 0 2px 4px rgba(0,0,0,0.1)')
  }
  
  // 设置背景色
  let bgClass = navbarBg.value
  let customBgStyle = ''
  if (navbarBg.value === 'bg-custom') {
    bgClass = ''
    customBgStyle = `background-color: ${customBgColor.value};`
  }
  
  // 生成完整的导航栏HTML
  return `
<nav class="navbar-section responsive-block ${navbarPosition.value} ${bgClass}" data-bs-component="navbar" style="${customBgStyle}">
  <div class="container">
    <div class="navbar-wrapper" style="${navbarStyles.join('; ')}">
      ${showLogo.value ? `
        <a class="navbar-brand" href="#">
          <img src="${logoUrl.value}" height="${logoHeight.value}" alt="Logo">
        </a>
      ` : ''}
      <div class="navbar-links">
        ${navItemsHtml}
        ${buttonsHtml}
      </div>
    </div>
  </div>
</nav>
  `.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    const html = generateNavbarHtml()
    emit('update-block', { html })
    isChanged.value = false
  } catch (error) {
    console.error('应用导航栏更改时出错:', error)
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.image-preview-container {
  width: 100%;
  height: 100px;
  background-color: #F6F6F6;
  margin-bottom: 18px;
  overflow: hidden;
  border-radius: 4px;
}

.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .preview-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  
  .placeholder-icon {
    font-size: 40px;
    color: #9E9E9E;
    margin-bottom: 10px;
  }
  
  .placeholder-text {
    color: #9E9E9E;
    font-size: 16px;
  }
}

.button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  
  .delete-btn {
    color: #707070;
    background: #FFFFFF;
    border-radius: 5px;
  }
}

.nav-item-container {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  
  .nav-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    span {
      font-weight: 500;
    }
  }
}

.add-nav-btn {
  width: 100%;
  margin-top: 10px;
}

.unit-text {
  margin-left: 8px;
  color: #909399;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}

.mb-2 {
  margin-bottom: 8px;
}

.w-100 {
  width: 100%;
}

:deep(.el-input-number) {
  width: 120px;
}

.color-input-row {
  display: flex;
  align-items: center;
  margin-top: 8px;
  
  .color-hash {
    padding: 0 8px;
    background-color: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #606266;
    height: 32px;
    display: flex;
    align-items: center;
  }
  
  .color-input {
    flex-grow: 1;
    
    :deep(.el-input__inner) {
      border-radius: 0;
    }
  }
  
  .color-preview {
    width: 32px;
    height: 32px;
    border: 1px solid #dcdfe6;
    border-left: none;
    border-right: none;
    flex-shrink: 0;
  }
  
  :deep(.el-color-picker) {
    height: 32px;
    margin-left: -1px;
    
    .el-color-picker__trigger {
      border-radius: 0 4px 4px 0;
      border: 1px solid #dcdfe6;
      height: 32px;
      padding: 3px;
    }
  }
}

.mt-2 {
  margin-top: 8px;
}
</style> 