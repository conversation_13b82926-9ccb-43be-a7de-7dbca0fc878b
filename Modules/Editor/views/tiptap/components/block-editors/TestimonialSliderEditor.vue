<template>
  <!-- 确保Font Awesome图标库加载 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top" size="small">
          <div v-for="(slide, index) in testimonialSlides" :key="index" class="testimonial-slide-item">
            <div class="slide-header">
              <h4 class="slide-title">推荐 #{{ index + 1 }}</h4>
              <el-button
                v-if="testimonialSlides.length > 1"
                type="danger"
                size="small"
                circle
                icon="Delete"
                @click="removeSlide(index)">
              </el-button>
            </div>

            <el-form-item label="推荐语">
              <el-input
                v-model="slide.quoteText"
                type="textarea"
                :rows="3"
                placeholder="添加推荐语..."
                @change="markAsChanged"
              />
            </el-form-item>

            <el-form-item label="客户名称">
              <el-input
                v-model="slide.authorName"
                placeholder="客户名称"
                @change="markAsChanged"
              />
            </el-form-item>

            <el-form-item label="客户角色/职位">
              <el-input
                v-model="slide.authorRole"
                placeholder="客户角色/职位"
                @change="markAsChanged"
              />
            </el-form-item>

            <el-form-item label="客户头像">
              <div class="image-preview-container">
                <div class="author-image-preview-wrapper">
                  <img :src="slide.authorImage" class="author-image-preview" :alt="slide.authorName">
                </div>
                <div class="image-actions">
                  <el-button size="small" type="primary" @click="openImageSelector(index)">
                    <el-icon class="icon"><Upload /></el-icon>
                    <span>选择图片</span>
                  </el-button>
                  <el-button size="small" type="danger" @click="removeImage(index)" v-if="slide.authorImage !== 'https://via.placeholder.com/60x60'">
                    <el-icon class="icon"><Delete /></el-icon>
                    <span>删除图片</span>
                  </el-button>
                </div>
              </div>
            </el-form-item>

            <el-form-item label="CTA链接">
              <el-input v-model="slide.ctaUrl" placeholder="https://..." @change="markAsChanged">
                <template #prepend>URL</template>
              </el-input>
            </el-form-item>

            <el-form-item label="CTA文本">
              <el-input v-model="slide.ctaText" placeholder="阅读案例研究" @change="markAsChanged" />
            </el-form-item>

            <el-divider />
          </div>

          <div class="add-slide-wrapper">
            <el-button type="primary" @click="addSlide" size="small">添加新推荐</el-button>
          </div>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="样式" name="style">
        <el-form label-position="top" size="small">
          <el-form-item label="背景颜色">
            <el-color-picker v-model="backgroundColor" @change="markAsChanged" />
          </el-form-item>

          <el-form-item label="文字颜色">
            <el-color-picker v-model="textColor" @change="markAsChanged" />
          </el-form-item>

          <el-form-item label="引号颜色">
            <el-color-picker v-model="quoteIconColor" @change="markAsChanged" />
          </el-form-item>

          <el-form-item label="自动播放">
            <el-switch v-model="autoplay" @change="markAsChanged" />
          </el-form-item>

          <el-form-item label="自动播放间隔 (毫秒)" v-if="autoplay">
            <el-input-number v-model="autoplayInterval" :min="1000" :max="10000" :step="500" @change="markAsChanged" />
          </el-form-item>

          <el-form-item label="显示导航按钮">
            <el-switch v-model="showNavButtons" @change="markAsChanged" />
          </el-form-item>

          <el-form-item label="显示导航点">
            <el-switch v-model="showDots" @change="markAsChanged" />
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges" size="small">应用更改</el-button>
    </div>

    <!-- 文件管理器弹窗 -->
    <DocumentsManager 
      :BaseUrl="baseUrl" 
      :token="token" 
      :isMultiSelect="false" 
      :locale="localeLang"
      @confirmSelection="confirmSelection" 
      ref="documentsManagerRef" 
      v-model="visibleDialog"
      :showUploadButton="false" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Upload, Picture } from '@element-plus/icons-vue'
import { DocumentsManager } from 'filestudio-bingo'
import { env, getAuthToken } from '/admin/support/helper'

// 定义组件名称
defineOptions({
  name: 'TestimonialSliderEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  }
})

const emit = defineEmits(['update-block'])

const activeTab = ref('content')
const isChanged = ref(false)

// 文件管理器相关
const visibleDialog = ref(false)
const documentsManagerRef = ref(null)
let tokens: string | null = getAuthToken()
const token = ref<string>(tokens ?? '')
const baseUrl = ref<string>(env('VITE_BASE_URL').replace('/admin/', '/'))
const localeLang = computed(() => localStorage.getItem('bwms_language') || 'zh_CN')
const currentEditingIndex = ref(-1)

// 推荐轮播数据
interface TestimonialSlide {
  quoteText: string
  authorName: string
  authorRole: string
  authorImage: string
  ctaUrl: string
  ctaText: string
}

// 默认推荐轮播配置
const defaultTestimonialSlides: TestimonialSlide[] = [
  {
    quoteText: '添加推荐语 #1。保持简洁有力，以提高您业务的可信度。',
    authorName: '客户名称一',
    authorRole: '客户角色一',
    authorImage: 'https://via.placeholder.com/60x60',
    ctaUrl: '#',
    ctaText: '阅读案例研究'
  },
  {
    quoteText: '添加推荐语 #2。保持简洁有力，以提高您业务的可信度。',
    authorName: '客户名称二',
    authorRole: '客户角色二',
    authorImage: 'https://via.placeholder.com/60x60',
    ctaUrl: '#',
    ctaText: '阅读案例研究'
  }
]

// 样式设置
const backgroundColor = ref('#f8f9fa')
const textColor = ref('#333333')
const quoteIconColor = ref('#6c5ce7')
const autoplay = ref(true)
const autoplayInterval = ref(5000)
const showNavButtons = ref(true)
const showDots = ref(true)

const testimonialSlides = ref<TestimonialSlide[]>([...defaultTestimonialSlides])

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 添加新推荐
const addSlide = () => {
  const newIndex = testimonialSlides.value.length + 1
  testimonialSlides.value.push({
    quoteText: `添加推荐语 #${newIndex}。保持简洁有力，以提高您业务的可信度。`,
    authorName: `客户名称${newIndex}`,
    authorRole: `客户角色${newIndex}`,
    authorImage: 'https://via.placeholder.com/60x60',
    ctaUrl: '#',
    ctaText: '阅读案例研究'
  })
  markAsChanged()
}

// 移除推荐
const removeSlide = (index: number) => {
  testimonialSlides.value.splice(index, 1)
  markAsChanged()
}

// 获取token
const getToken = () => {
  // 从localStorage或cookie获取token
  const authToken = localStorage.getItem('auth_token') || ''
  token.value = authToken
}

// 打开图片选择器
const openImageSelector = (index: number) => {
  currentEditingIndex.value = index
  visibleDialog.value = true
}

// 文件选择确认
const confirmSelection = (selectedFiles: any[]) => {
  if (!selectedFiles || selectedFiles.length === 0 || currentEditingIndex.value < 0) return
  
  const file = selectedFiles[0]
  testimonialSlides.value[currentEditingIndex.value].authorImage = file.path || file.url
  markAsChanged()
}

// 删除图片
const removeImage = (index: number) => {
  ElMessageBox.confirm(
    '确定要删除当前图片吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    testimonialSlides.value[index].authorImage = 'https://via.placeholder.com/60x60'
    markAsChanged()
    ElMessage.success('图片已删除')
  }).catch(() => {
    // 取消删除，不做任何操作
  })
}

// 重置为默认配置
const resetToDefault = () => {
  testimonialSlides.value = [...defaultTestimonialSlides]
  backgroundColor.value = '#f8f9fa'
  textColor.value = '#333333'
  quoteIconColor.value = '#6c5ce7'
  autoplay.value = true
  autoplayInterval.value = 5000
  showNavButtons.value = true
  showDots.value = true
}

// 提取现有配置的函数
const extractExistingConfig = () => {
  if (!props.blockElement) return false

  try {
    // 提取推荐轮播信息
    const slides = props.blockElement.querySelectorAll('.testimonial-slide')

    if (slides.length > 0) {
      testimonialSlides.value = []

      slides.forEach((slide) => {
        const quoteTextElement = slide.querySelector('.quote-text')
        const authorNameElement = slide.querySelector('.author-name')
        const authorRoleElement = slide.querySelector('.author-role')
        const authorImageElement = slide.querySelector('.author-image img')
        const ctaLinkElement = slide.querySelector('.read-more-link')

        const quoteText = quoteTextElement ? quoteTextElement.textContent || '' : ''
        const authorName = authorNameElement ? authorNameElement.textContent || '' : ''
        const authorRole = authorRoleElement ? authorRoleElement.textContent || '' : ''
        const authorImage = authorImageElement && authorImageElement instanceof HTMLImageElement
          ? authorImageElement.src
          : 'https://via.placeholder.com/60x60'
        const ctaUrl = ctaLinkElement && ctaLinkElement instanceof HTMLAnchorElement
          ? ctaLinkElement.href
          : '#'
        const ctaText = ctaLinkElement ? ctaLinkElement.textContent?.replace(/\s*<i.*<\/i>\s*$/, '').trim() || '阅读案例研究' : '阅读案例研究'

        testimonialSlides.value.push({
          quoteText,
          authorName,
          authorRole,
          authorImage,
          ctaUrl,
          ctaText
        })
      })
    }

    // 提取样式信息
    const style = props.blockElement.querySelector('style')
    if (style && style.textContent) {
      const styleContent = style.textContent

      // 提取背景颜色
      const bgColorMatch = styleContent.match(/\.testimonial-slide\s*{[^}]*background-color:\s*([^;]+);/)
      if (bgColorMatch && bgColorMatch[1]) {
        backgroundColor.value = bgColorMatch[1].trim()
      }

      // 提取文字颜色
      const textColorMatch = styleContent.match(/\.quote-text\s*{[^}]*color:\s*([^;]+);/)
      if (textColorMatch && textColorMatch[1]) {
        textColor.value = textColorMatch[1].trim()
      }

      // 提取引号颜色
      const quoteColorMatch = styleContent.match(/\.quote-icon\s*{[^}]*color:\s*([^;]+);/)
      if (quoteColorMatch && quoteColorMatch[1]) {
        quoteIconColor.value = quoteColorMatch[1].trim()
      }
    }

    // 提取自动播放设置
    const script = props.blockElement.querySelector('script')
    if (script && script.textContent) {
      const scriptContent = script.textContent

      // 检查是否有自动播放
      const hasAutoplay = scriptContent.includes('setInterval(nextSlide')
      autoplay.value = hasAutoplay

      // 提取自动播放间隔
      const intervalMatch = scriptContent.match(/setInterval\(nextSlide,\s*(\d+)\)/)
      if (intervalMatch && intervalMatch[1]) {
        autoplayInterval.value = parseInt(intervalMatch[1])
      }

      // 检查是否显示导航按钮
      const hasNavButtons = props.blockElement.querySelectorAll('.nav-button').length > 0
      showNavButtons.value = hasNavButtons

      // 检查是否显示导航点
      const hasDots = props.blockElement.querySelector('.slider-dots') !== null
      showDots.value = hasDots
    }

    return true
  } catch (error) {
    console.error('提取推荐轮播配置时出错:', error)
    return false
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue) => {
  if (newValue) {
    const extracted = extractExistingConfig()
    if (!extracted) {
      // 如果无法提取配置，使用默认值
      resetToDefault()
    }
  } else {
    // 如果 blockElement 为空，重置为默认值
    resetToDefault()
  }
  // 重置更改状态
  isChanged.value = false
}, { immediate: true })

// 修改 onMounted 钩子
onMounted(() => {
  getToken()
  // 初始化时标记为未更改
  isChanged.value = false
})

// 应用更改
const applyChanges = () => {
  // 生成更新后的HTML
  let slidesHtml = ''
  testimonialSlides.value.forEach((slide, index) => {
    slidesHtml += `
      <div class="testimonial-slide${index === 0 ? ' active' : ''}">
        <div class="testimonial-content">
          <div class="testimonial-quote">
            <i class="fas fa-quote-left quote-icon"></i>
            <p class="quote-text">${slide.quoteText}</p>
          </div>
          <div class="testimonial-author">
            <div class="author-image">
              <img src="${slide.authorImage}" alt="${slide.authorName}">
            </div>
            <div class="author-info">
              <h4 class="author-name">${slide.authorName}</h4>
              <p class="author-role">${slide.authorRole}</p>
            </div>
          </div>
          <div class="testimonial-cta">
            <a href="${slide.ctaUrl}" class="read-more-link">${slide.ctaText} <i class="fas fa-arrow-right"></i></a>
          </div>
        </div>
      </div>
    `
  })

  let dotsHtml = ''
  if (showDots.value) {
    dotsHtml = `
      <div class="slider-dots">
        ${testimonialSlides.value.map((_, i) =>
          `<span class="dot${i === 0 ? ' active' : ''}" data-slide="${i}"></span>`
        ).join('')}
      </div>
    `
  }

  const navButtonsHtml = showNavButtons.value ? `
    <button class="nav-button prev-button"><i class="fas fa-chevron-left"></i></button>
    <button class="nav-button next-button"><i class="fas fa-chevron-right"></i></button>
  ` : ''

  const autoplayScript = autoplay.value ? `
    // 设置自动轮播
    const autoSlide = setInterval(nextSlide, ${autoplayInterval.value});

    // 当用户交互时暂停自动轮播
    [prevButton, nextButton, ...dots].forEach(el => {
      el.addEventListener('click', () => {
        clearInterval(autoSlide);
      });
    });
  ` : ''

  const updatedHtml = `
    <div data-bs-component="testimonial-slider" class="testimonial-slider-block">
      <div class="testimonial-slider-container">
        <div class="testimonial-slider">
          ${navButtonsHtml}
          ${slidesHtml}
        </div>
        ${dotsHtml}
      </div>

      <style>
      .testimonial-slider-block {
        position: relative;
        padding: 40px 0;
      }

      .testimonial-slider-container {
        max-width: 100%;
        margin: 0 auto;
        padding: 20px;
        overflow: hidden;
      }

      .testimonial-slider {
        position: relative;
        width: 100%;
        min-height: 400px;
        display: flex;
        align-items: center;
      }

      .testimonial-slide {
        display: none;
        width: 100%;
        padding: 30px;
        border-radius: 8px;
        background-color: ${backgroundColor.value};
        box-shadow: 0 2px 15px rgba(0,0,0,0.08);
      }

      .testimonial-slide.active {
        display: block;
      }

      .testimonial-content {
        display: flex;
        flex-direction: row;
        gap: 40px;
        align-items: flex-start;
        min-height: 280px;
      }

      .testimonial-quote {
        flex: 3;
        padding: 20px 30px;
        position: relative;
      }

      .quote-icon {
        color: ${quoteIconColor.value};
        font-size: 30px;
        margin-bottom: 20px;
        opacity: 0.5;
      }

      .quote-text {
        font-size: 22px;
        line-height: 1.5;
        color: ${textColor.value};
        font-style: italic;
        margin-bottom: 20px;
      }

      .testimonial-author {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
      }

      .author-image {
        width: 80px;
        height: 80px;
        margin-bottom: 15px;
        border-radius: 50%;
        overflow: hidden;
        border: 2px solid #fff;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      }

      .author-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .author-info {
        width: 100%;
      }

      .author-name {
        margin: 0 0 5px 0;
        font-size: 18px;
        font-weight: 600;
        color: ${textColor.value};
      }

      .author-role {
        margin: 0;
        font-size: 14px;
        color: ${textColor.value === '#333' ? '#666' : textColor.value};
      }

      .testimonial-cta {
        margin-top: 20px;
        text-align: center;
      }

      .read-more-link {
        display: inline-flex;
        align-items: center;
        color: ${quoteIconColor.value};
        text-decoration: none;
        font-weight: 500;
        font-size: 14px;
        transition: color 0.3s;
      }

      .read-more-link:hover {
        color: ${quoteIconColor.value === '#6c5ce7' ? '#5649c0' : quoteIconColor.value};
      }

      .read-more-link i {
        margin-left: 5px;
        font-size: 12px;
      }

      .nav-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: #fff;
        border: none;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        cursor: pointer;
        z-index: 10;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .prev-button {
        left: -20px;
      }

      .next-button {
        right: -20px;
      }

      .nav-button:hover {
        background-color: ${quoteIconColor.value};
        color: white;
        box-shadow: 0 4px 12px rgba(108,92,231,0.3);
      }

      .slider-dots {
        display: flex;
        justify-content: center;
        gap: 8px;
        margin-top: 20px;
      }

      .dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #d1d1d1;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .dot.active {
        background-color: ${quoteIconColor.value};
        transform: scale(1.2);
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .testimonial-content {
          flex-direction: column;
          gap: 20px;
        }

        .testimonial-quote {
          padding: 15px;
        }

        .quote-text {
          font-size: 18px;
        }

        .nav-button {
          width: 36px;
          height: 36px;
        }

        .prev-button {
          left: 5px;
        }

        .next-button {
          right: 5px;
        }
      }
      </style>

      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

      <script>
      document.addEventListener("DOMContentLoaded", function() {
        const slider = document.querySelector('.testimonial-slider');
        const slides = document.querySelectorAll('.testimonial-slide');
        const dots = document.querySelectorAll('.dot');
        const prevButton = document.querySelector('.prev-button');
        const nextButton = document.querySelector('.next-button');
        let currentSlide = 0;

        function showSlide(index) {
          slides.forEach(slide => slide.classList.remove('active'));
          dots.forEach(dot => dot.classList.remove('active'));

          slides[index].classList.add('active');
          dots[index].classList.add('active');
          currentSlide = index;
        }

        function nextSlide() {
          currentSlide = (currentSlide + 1) % slides.length;
          showSlide(currentSlide);
        }

        function prevSlide() {
          currentSlide = (currentSlide - 1 + slides.length) % slides.length;
          showSlide(currentSlide);
        }

        // 点击导航点切换幻灯片
        dots.forEach((dot, index) => {
          dot.addEventListener('click', () => showSlide(index));
        });

        // 上一张/下一张按钮
        prevButton.addEventListener('click', prevSlide);
        nextButton.addEventListener('click', nextSlide);

        ${autoplayScript}
      });
      `
    
  // 触发更新事件
  emit('update-block', { html: updatedHtml })

  // 重置更改状态
  isChanged.value = false

  ElMessage.success('推荐轮播模块已更新')
}
</script>

<style lang="scss" scoped>
.edit-section {
  padding: 15px 0;
}

.testimonial-slide-item {
  margin-bottom: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.slide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.slide-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.image-preview-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.author-image-preview-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.author-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.add-slide-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
}
</style>
