<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="內容" name="content">
        <div class="editor-section">
          <div class="form-item">
            <label>標題</label>
            <el-input 
              v-model="title" 
              placeholder="請輸入標題"
              @change="markAsChanged"
            />
          </div>

          <div class="form-item">
            <label>內容 (可選)</label>
            <el-input 
              v-model="content" 
              type="textarea" 
              :rows="3"
              placeholder="請輸入內容描述"
              @change="markAsChanged"
            />
          </div>

          <div class="form-item">
            <label>按鈕文本</label>
            <el-input 
              v-model="buttonLabel" 
              placeholder="請輸入按鈕文本"
              @change="markAsChanged"
            />
          </div>

          <div class="form-item">
            <label>按鈕連結</label>
            <el-input 
              v-model="buttonUrl" 
              placeholder="請輸入按鈕連結"
              @change="markAsChanged"
            />
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="樣式" name="style">
        <div class="editor-section">
          <div class="form-item">
            <label>背景顏色</label>
            <el-select 
              v-model="bgColor" 
              placeholder="請選擇背景顏色"
              @change="markAsChanged"
              style="width: 100%"
            >
              <el-option label="主要 (藍色)" value="primary" />
              <el-option label="次要 (灰色)" value="secondary" />
              <el-option label="成功 (綠色)" value="success" />
              <el-option label="危險 (紅色)" value="danger" />
              <el-option label="警告 (黃色)" value="warning" />
              <el-option label="資訊 (青色)" value="info" />
              <el-option label="亮色 (白色)" value="light" />
              <el-option label="暗色 (深灰)" value="dark" />
            </el-select>
          </div>

          <div class="form-item">
            <label>按鈕樣式</label>
            <el-select 
              v-model="buttonType" 
              placeholder="請選擇按鈕樣式"
              @change="markAsChanged"
              style="width: 100%"
            >
              <el-option label="主要 (藍色)" value="primary" />
              <el-option label="次要 (灰色)" value="secondary" />
              <el-option label="成功 (綠色)" value="success" />
              <el-option label="危險 (紅色)" value="danger" />
              <el-option label="警告 (黃色)" value="warning" />
              <el-option label="資訊 (青色)" value="info" />
              <el-option label="亮色 (白色)" value="light" />
              <el-option label="暗色 (深灰)" value="dark" />
              <el-option label="連結 (無背景)" value="link" />
            </el-select>
          </div>

          <div class="form-item">
            <label>對齊方式</label>
            <el-radio-group v-model="textAlign" @change="markAsChanged">
              <el-radio-button label="left">
                左對齊
              </el-radio-button>
              <el-radio-button label="center">
                居中對齊
              </el-radio-button>
              <el-radio-button label="right">
                右對齊
              </el-radio-button>
            </el-radio-group>
          </div>

          <div class="form-item">
            <label>按鈕形狀</label>
            <div class="button-shape-options">
              <el-radio-group v-model="buttonShape" @change="markAsChanged">
                <el-radio label="default">方形</el-radio>
                <el-radio label="rounded">圓角</el-radio>
                <el-radio label="pill">膠囊</el-radio>
              </el-radio-group>
            </div>
          </div>
          
          <div class="form-item">
            <label>上邊距</label>
            <el-input-number 
              v-model="paddingTop" 
              :min="0" 
              :max="10"
              @change="markAsChanged"
            />
          </div>
          <div class="form-item">
            <label>下邊距</label>
            <el-input-number 
              v-model="paddingBottom" 
              :min="0" 
              :max="10"
              @change="markAsChanged"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 應用按鈕，只在有更改時顯示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges" size="small">應用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 定義組件名稱
defineOptions({
  name: 'CTAEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  }
})

const emit = defineEmits(['update-block'])

// 狀態
const activeTab = ref('content')
const title = ref('立即行動，抓住機會！')
const content = ref('')
const buttonLabel = ref('立即聯繫')
const buttonUrl = ref('#')
const buttonType = ref('light')
const bgColor = ref('primary')
const textAlign = ref('center')
const buttonShape = ref('pill')
const paddingTop = ref(5)
const paddingBottom = ref(5)
const isChanged = ref(false)

// 原始結構信息
const originalStructure = ref({
  containerClasses: [] as string[],
  rowClasses: [] as string[],
  colClasses: [] as string[],
  customAttributes: {} as Record<string, string>
})

// 原始HTML
const originalHtml = ref('')

// 枚舉CTA類型
enum CTAType {
  // 標準CTA - 帶有容器的完整結構
  STANDARD = 'standard',
  // 簡單CTA - 只有div+標題+按鈕的簡化結構
  SIMPLE = 'simple',
  // 未知類型
  UNKNOWN = 'unknown'
}

// CTA結構類型
const ctaStructure = ref({
  type: CTAType.UNKNOWN,
})

/**
 * 從元素中提取所有屬性
 */
const extractAttributes = (element: Element): Record<string, string> => {
  const attributes: Record<string, string> = {}
  
  Array.from(element.attributes).forEach(attr => {
    if (attr.name !== 'class' && attr.name !== 'style' && !attr.name.startsWith('data-v-')) {
      attributes[attr.name] = attr.value
    }
  })
  
  return attributes
}

// 標記為已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 設置文本對齊並標記為已更改
const setTextAlign = (align: string) => {
  if (textAlign.value === align) return
  textAlign.value = align
  markAsChanged()
}

/**
 * 設置默認值
 */
const setDefaultValues = () => {
  title.value = '立即行動，抓住機會！'
  content.value = ''
  buttonLabel.value = '立即聯繫'
  buttonUrl.value = '#'
  buttonType.value = 'light'
  bgColor.value = 'primary'
  textAlign.value = 'center'
  buttonShape.value = 'pill'
  paddingTop.value = 5
  paddingBottom.value = 5
}

/**
 * 從DOM元素加載數據
 */
const loadDataFromElement = (element: HTMLElement): boolean => {
  try {
    if (!element) {
      console.warn('未提供有效的塊元素，使用默認值')
      return false
    }
    
    // 保存原始HTML以備參考
    originalHtml.value = element.outerHTML
    
    // 判斷CTA類型
    // 1. 檢查是否是標準CTA組件
    const isCTAComponent = element.getAttribute('data-bs-component') === 'bootstrap-cta'
    const containerQuery = element.querySelector('.container, .container-fluid')
    
    if (isCTAComponent && containerQuery) {
      // 標準CTA結構
      ctaStructure.value.type = CTAType.STANDARD
      
      // 提取容器結構
      const container = containerQuery
      originalStructure.value.containerClasses = Array.from(container.classList)
      
      // 提取行和列結構
      const row = container.querySelector('.row')
      if (row) {
        originalStructure.value.rowClasses = Array.from(row.classList)
        
        const col = row.querySelector('[class*="col-"]')
        if (col) {
          originalStructure.value.colClasses = Array.from(col.classList)
        }
      }
      
      // 提取自定義屬性
      originalStructure.value.customAttributes = extractAttributes(element)
    } else {
      // 簡單CTA結構
      ctaStructure.value.type = CTAType.SIMPLE
      originalStructure.value.customAttributes = extractAttributes(element)
    }
    
    // 獲取標題
    const titleElement = element.querySelector('h2')
    if (titleElement) {
      title.value = titleElement.textContent?.trim() || '立即行動，抓住機會！'
    }

    // 獲取內容
    const contentElement = element.querySelector('.container > p')
    if (contentElement) {
      content.value = contentElement.textContent?.trim() || ''
    }

    // 獲取按鈕文本和屬性
    const buttonElement = element.querySelector('button')
    if (buttonElement) {
      buttonLabel.value = buttonElement.textContent?.trim() || '立即聯繫'
      
      // 獲取按鈕類型
      const btnClasses = Array.from(buttonElement.classList)
      for (const btnClass of btnClasses) {
        if (btnClass.startsWith('btn-')) {
          buttonType.value = btnClass.replace('btn-', '')
          break
        }
      }
      
      // 獲取按鈕形狀
      if (buttonElement.classList.contains('rounded-pill')) {
        buttonShape.value = 'pill'
      } else if (buttonElement.classList.contains('rounded')) {
        buttonShape.value = 'rounded'
      } else {
        buttonShape.value = 'default'
      }
      
      // 獲取按鈕連結
      const btnUrl = buttonElement.getAttribute('data-button-url')
      if (btnUrl) {
        buttonUrl.value = btnUrl
      }
    }

    // 獲取背景顏色
    const ctaElement = element.matches('[data-bs-component="bootstrap-cta"]') ? element : element.querySelector('[data-bs-component="bootstrap-cta"]')
    if (ctaElement) {
      const elementClasses = Array.from(ctaElement.classList)
      for (const cls of elementClasses) {
        if (cls.startsWith('bg-')) {
          bgColor.value = cls.replace('bg-', '')
          break
        }
      }
      
      // 獲取邊距
      const pyTopClass = elementClasses.find(cls => cls.startsWith('pt-') || cls.startsWith('py-'))
      if (pyTopClass) {
        const value = parseInt(pyTopClass.replace(/p[ty]-/, ''))
        if (!isNaN(value)) {
          paddingTop.value = value
        }
      }
      
      const pyBottomClass = elementClasses.find(cls => cls.startsWith('pb-') || cls.startsWith('py-'))
      if (pyBottomClass) {
        const value = parseInt(pyBottomClass.replace(/p[by]-/, ''))
        if (!isNaN(value)) {
          paddingBottom.value = value
        }
      }
    }

    // 獲取對齊方式
    const containerElement = element.querySelector('.container')
    if (containerElement) {
      if (containerElement.classList.contains('text-left')) {
        textAlign.value = 'left'
      } else if (containerElement.classList.contains('text-right')) {
        textAlign.value = 'right'
      } else {
        // 默認為居中
        textAlign.value = 'center'
      }
    }
    
    console.log('CTA元素數據加載成功', {
      title: title.value,
      content: content.value,
      buttonLabel: buttonLabel.value,
      buttonUrl: buttonUrl.value,
      buttonType: buttonType.value,
      bgColor: bgColor.value,
      textAlign: textAlign.value,
      structure: ctaStructure.value.type
    })

    return true
  } catch (error) {
    console.error('加載CTA元素數據時出錯:', error)
    return false
  }
}

// 監聽 blockElement 的變化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置更改狀態
    isChanged.value = false
    
    // 重新加載數據
    const loaded = loadDataFromElement(newValue)
    
    // 如果加載失敗，使用默認值
    if (!loaded) {
      setDefaultValues()
    }
  }
}, { immediate: true, deep: true })

// 組件掛載時初始化
onMounted(() => {
  // 移除原有的初始化邏輯，因為已經由 watch 處理
  isChanged.value = false
})

/**
 * 準備最終CTA HTML
 */
const prepareCTAHTML = (): string => {
  try {
    // 如果沒有原始元素或原始HTML，直接生成新的HTML
    if (!props.blockElement || !originalHtml.value) {
      // 根據按鈕形狀設置類名
      const buttonShapeClass = buttonShape.value === 'pill' 
        ? 'rounded-pill' 
        : buttonShape.value === 'rounded' 
          ? 'rounded' 
          : ''

      // 設置文本對齊類名（center是默認值，不需要額外類名）
      const textAlignClass = textAlign.value !== 'center' ? `text-${textAlign.value}` : ''
      
      // 創建標準CTA結構
      return `
<div data-bs-component="bootstrap-cta" class="py-${paddingTop.value} py-${paddingBottom.value} text-white bg-${bgColor.value}">
  <div class="container ${textAlignClass}">
    <h2 class="mb-4 display-6 fw-bold">${title.value}</h2>
    ${content.value ? `<p class="mb-4">${content.value}</p>` : ''}
    <button type="button" class="px-4 py-2 bootstrap-button btn btn-${buttonType.value} ${buttonShapeClass}" data-button-url="${buttonUrl.value}">${buttonLabel.value}</button>
  </div>
</div>
      `.trim()
    }
    
    // 保留原始結構但更新內容
    // 創建一個臨時的DOM元素來解析原始HTML
    const tempElement = document.createElement('div')
    tempElement.innerHTML = originalHtml.value
    const ctaElement = tempElement.firstElementChild
    
    if (!ctaElement) {
      return generateDefaultCTA()
    }
    
    // 確保有data-bs-component="cta"屬性
    ctaElement.setAttribute('data-bs-component', 'bootstrap-cta')
    
    // 更新按鈕形狀
    const buttonShapeClass = buttonShape.value === 'pill' 
      ? 'rounded-pill' 
      : buttonShape.value === 'rounded' 
        ? 'rounded' 
        : ''
    
    // 更新背景色類
    // 移除舊的背景色類
    const bgClasses = Array.from(ctaElement.classList).filter(cls => cls.startsWith('bg-'))
    bgClasses.forEach(cls => ctaElement.classList.remove(cls))
    
    // 添加新的背景色類
    ctaElement.classList.add(`bg-${bgColor.value}`)
    
    // 更新padding類
    // 移除舊的padding類
    const paddingClasses = Array.from(ctaElement.classList).filter(cls => 
      cls.startsWith('py-') || cls.startsWith('pt-') || cls.startsWith('pb-'))
    paddingClasses.forEach(cls => ctaElement.classList.remove(cls))
    
    // 添加新的padding類
    ctaElement.classList.add(`py-${paddingTop.value}`, `py-${paddingBottom.value}`)
    
    // 查找標題元素並更新
    const titleElement = ctaElement.querySelector('h1, h2, h3, h4, h5, h6')
    if (titleElement) {
      titleElement.textContent = title.value
    }
    
    // 查找內容元素並更新
    const contentElement = ctaElement.querySelector('p')
    if (contentElement) {
      if (content.value) {
        contentElement.textContent = content.value
      } else {
        // 如果沒有內容，嘗試移除段落
        contentElement.parentNode?.removeChild(contentElement)
      }
    } else if (content.value) {
      // 如果有內容但沒有段落元素，嘗試添加一個
      const container = ctaElement.querySelector('.container')
      if (container) {
        const newContentElement = document.createElement('p')
        newContentElement.className = 'mb-4'
        newContentElement.textContent = content.value
        
        // 找到合適的位置插入內容
        const h2 = container.querySelector('h2')
        if (h2 && h2.nextSibling) {
          container.insertBefore(newContentElement, h2.nextSibling)
        } else if (h2) {
          h2.insertAdjacentElement('afterend', newContentElement)
        } else {
          container.appendChild(newContentElement)
        }
      }
    }
    
    // 查找按鈕元素並更新
    const buttonElement = ctaElement.querySelector('button, a.btn') as HTMLElement | null
    if (buttonElement) {
      // 更新按鈕文本
      buttonElement.textContent = buttonLabel.value
      
      // 更新按鈕URL屬性
      buttonElement.setAttribute('data-button-url', buttonUrl.value)
      
      // 如果是a標籤，還要更新href
      if (buttonElement.tagName.toLowerCase() === 'a') {
        buttonElement.setAttribute('href', buttonUrl.value)
      }
      
      // 更新按鈕樣式類
      // 移除舊的樣式類
      const btnClasses = Array.from(buttonElement.classList).filter(cls => cls.startsWith('btn-'))
      btnClasses.forEach(cls => buttonElement.classList.remove(cls))
      
      // 移除舊的形狀類
      buttonElement.classList.remove('rounded', 'rounded-pill')
      
      // 添加新的樣式類
      buttonElement.classList.add(`btn-${buttonType.value}`)
      
      // 添加新的形狀類
      if (buttonShapeClass) {
        buttonElement.classList.add(buttonShapeClass)
      }
    }
    
    // 查找並更新文本對齊方式
    const container = ctaElement.querySelector('.container')
    if (container) {
      // 移除舊的文本對齊類
      container.classList.remove('text-left', 'text-center', 'text-right')
      
      // 添加新的文本對齊類
      if (textAlign.value !== 'center') {
        container.classList.add(`text-${textAlign.value}`)
      }
    }
    
    // 返回更新後的HTML
    return tempElement.innerHTML
  } catch (error) {
    console.error('準備CTA HTML時出錯:', error)
    // 出錯時返回基本HTML結構
    return generateDefaultCTA()
  }
}

/**
 * 生成默認的CTA HTML
 */
const generateDefaultCTA = (): string => {
  // 根據按鈕形狀設置類名
  const buttonShapeClass = buttonShape.value === 'pill' 
    ? 'rounded-pill' 
    : buttonShape.value === 'rounded' 
      ? 'rounded' 
      : ''

  // 設置文本對齊類名（center是默認值，不需要額外類名）
  const textAlignClass = textAlign.value !== 'center' ? `text-${textAlign.value}` : ''
  
  return `
<div data-bs-component="bootstrap-cta" class="py-${paddingTop.value} py-${paddingBottom.value} text-white bg-${bgColor.value} responsive-block">
  <div class="container ${textAlignClass}">
    <h2 class="mb-4 display-6 fw-bold">${title.value || '立即行動，抓住機會！'}</h2>
    ${content.value ? `<p class="mb-4">${content.value}</p>` : ''}
    <button type="button" class="px-4 py-2 bootstrap-button btn btn-${buttonType.value} ${buttonShapeClass}" data-button-url="${buttonUrl.value || '#'}">${buttonLabel.value || '立即聯繫'}</button>
  </div>
</div>
  `.trim()
}

// 應用更改
const applyChanges = () => {
  try {
    const html = prepareCTAHTML()
    
    // 發出更新事件
    emit('update-block', { 
      html,
    })
    
    // 重置更改狀態
    isChanged.value = false
    
    ElMessage.success('CTA內容已更新')
  } catch (error) {
    console.error('應用CTA更改時出錯:', error)
    ElMessage.error('更新CTA失敗')
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 15px;
  position: relative;
  max-width: 100%;
  overflow-x: hidden;
}

.editor-section {
  margin-bottom: 10px;
}

.form-item {
  margin-bottom: 15px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #606266;
  }
}

.align-options {
  display: flex;
  gap: 5px;

  .align-option {
    flex: 1;
    padding: 8px 0;
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #f5f7fa;
    }

    &.active {
      background-color: #ecf5ff;
      color: #409eff;
      border-color: #b3d8ff;
    }
  }
}

.button-shape-options {
  margin-top: 8px;
}

:deep(.el-tabs__header) {
  margin-bottom: 15px;
}

:deep(.el-tabs__item) {
  font-size: 14px;
  height: 40px;
  line-height: 40px;
}

.apply-button-container {
  margin-top: 15px;
  text-align: center;
  padding: 8px 0;
  border-top: 1px dashed #e4e7ed;
}
</style> 