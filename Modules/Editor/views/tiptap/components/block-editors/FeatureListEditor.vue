<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top">
          <el-form-item label="Number of items per row *">
            <el-select 
              v-model="itemsPerRow" 
              placeholder="选择每行显示数量" 
              @change="markAsChanged"
              style="width: 100%"
            >
              <el-option :label="1" :value="1" />
              <el-option :label="2" :value="2" />
              <el-option :label="3" :value="3" />
              <el-option :label="4" :value="4" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="Features">
            <div v-for="(feature, index) in features" :key="feature.id" class="feature-item-edit">
              <div class="feature-item-header">
                <span>{{ feature.title || '特性 #' + (index + 1) }}</span>
                <div class="feature-item-actions">
                  <el-button 
                    type="text" 
                    :icon="Edit" 
                    @click="editItem(index)"
                    title="编辑"
                  />
                  <el-button 
                    type="text" 
                    :icon="Delete" 
                    @click="removeFeature(index)" 
                    v-if="features.length > 1"
                    title="删除"
                  />
                </div>
              </div>
              <div v-if="editingIndex === index" class="feature-item-content">
                <el-form-item label="特性标题">
                  <el-input v-model="features[index].title" @input="markAsChanged" />
                </el-form-item>
                <el-form-item label="特性描述">
                  <el-input 
                    v-model="features[index].description" 
                    type="textarea" 
                    :rows="2"
                    @input="markAsChanged" 
                    placeholder="描述这个特性的详细信息" 
                  />
                </el-form-item>
                <el-form-item label="图标">
                  <el-select
                    v-model="features[index].icon"
                    placeholder="选择图标"
                    @change="markAsChanged"
                    style="width: 100%"
                  >
                    <el-option label="文件" value="fa-file-alt" />
                    <el-option label="图表" value="fa-chart-line" />
                    <el-option label="消息" value="fa-comments" />
                    <el-option label="集成" value="fa-puzzle-piece" />
                    <el-option label="自动化" value="fa-magic" />
                    <el-option label="日历" value="fa-calendar" />
                    <el-option label="机器人" value="fa-robot" />
                    <el-option label="分享" value="fa-share-alt" />
                    <el-option label="搜索" value="fa-search" />
                    <el-option label="用户" value="fa-user" />
                    <el-option label="设置" value="fa-cog" />
                    <el-option label="数据库" value="fa-database" />
                  </el-select>
                </el-form-item>
              </div>
            </div>
            <div class="add-item-button">
              <el-button type="primary" @click="addFeature" icon="Plus">添加特性</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="样式" name="style">
        <el-form label-position="top">
          <el-form-item label="Text color">
            <el-select 
              v-model="textColor" 
              @change="markAsChanged"
              style="width: 100%"
            >
              <el-option label="Light section 1 text color" value="default" />
              <el-option label="Dark section 1 text color" value="dark" />
              <el-option label="Light section 2 text color" value="light" />
              <el-option label="Custom color" value="custom" />
            </el-select>
            
            <div v-if="textColor === 'custom'" class="color-picker-container">
              <div class="color-input-row">
                <span class="color-hash">#</span>
                <el-input
                  v-model="customTextColor"
                  placeholder="000000"
                  @input="markAsChanged"
                  class="color-input"
                />
                <div class="color-preview" :style="{ backgroundColor: '#' + customTextColor }"></div>
                <el-color-picker 
                  v-model="textColorPicker" 
                  show-alpha 
                  @change="updateTextColor" 
                />
              </div>
            </div>
          </el-form-item>
          
          <el-form-item label="Heading style">
            <el-select 
              v-model="headingStyle" 
              @change="markAsChanged"
              style="width: 100%"
            >
              <el-option label="Heading 1" value="h1" />
              <el-option label="Heading 2" value="h2" />
              <el-option label="Heading 3" value="h3" />
              <el-option label="Heading 4" value="h4" />
              <el-option label="Heading 5" value="h5" />
              <el-option label="Heading 6" value="h6" />
            </el-select>
            
            <div class="heading-options">
              <div class="option-group">
                <label>字体大小</label>
                <el-input-number
                  v-model="headingFontSize"
                  :min="0.5"
                  :max="3"
                  :step="0.1"
                  @change="markAsChanged"
                  controls-position="right"
                  size="small"
                ></el-input-number>
              </div>
              
              <div class="option-group">
                <label>字体粗细</label>
                <el-select 
                  v-model="headingFontWeight" 
                  @change="markAsChanged"
                  size="small"
                >
                  <el-option label="Normal" value="normal" />
                  <el-option label="Bold" value="bold" />
                  <el-option label="Light" value="light" />
                </el-select>
              </div>
            </div>
          </el-form-item>
          
          <el-divider content-position="left">卡片样式</el-divider>
          
          <el-form-item label="背景颜色">
            <div class="color-input-row">
              <span class="color-hash">#</span>
              <el-input
                v-model="cardBackgroundColor"
                placeholder="F8F9FA"
                @input="markAsChanged"
                class="color-input"
              />
              <div class="color-preview" :style="{ backgroundColor: '#' + cardBackgroundColor }"></div>
              <el-color-picker 
                v-model="cardBgColorPicker" 
                show-alpha 
                @change="updateCardBgColor" 
              />
            </div>
          </el-form-item>
          
          <el-form-item label="图标颜色">
            <div class="color-input-row">
              <span class="color-hash">#</span>
              <el-input
                v-model="iconColor"
                placeholder="4E73DF"
                @input="markAsChanged"
                class="color-input"
              />
              <div class="color-preview" :style="{ backgroundColor: '#' + iconColor }"></div>
              <el-color-picker 
                v-model="iconColorPicker" 
                show-alpha 
                @change="updateIconColor" 
              />
            </div>
          </el-form-item>
          
          <el-form-item label="圆角">
            <el-slider
              v-model="borderRadius"
              :min="0"
              :max="20"
              @change="markAsChanged"
              :format-tooltip="value => `${value}px`"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineProps, defineEmits, defineOptions } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Delete, Plus } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'FeatureListEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 编辑中的特性索引
const editingIndex = ref(0)

// 特性列表
interface Feature {
  id: number
  title: string
  description: string
  icon: string
}

const features = ref<Feature[]>([])
const itemsPerRow = ref(2)

// 样式相关
const textColor = ref('default')
const customTextColor = ref('333333')
const textColorPicker = ref('#333333')

const headingStyle = ref('h5')
const headingFontSize = ref(1.1)
const headingFontWeight = ref('bold')

const cardBackgroundColor = ref('F8F9FA')
const cardBgColorPicker = ref('#F8F9FA')
const iconColor = ref('4E73DF')
const iconColorPicker = ref('#4E73DF')
const borderRadius = ref(8)

// 自动生成ID
let nextId = 1

/**
 * 设置默认值
 */
const setDefaultValues = () => {
  features.value = [
    {
      id: nextId++,
      title: '内容创作',
      description: '多样化的内容编辑工具，支持富文本、图片和视频等多种媒体格式。',
      icon: 'fa-file-alt'
    },
    {
      id: nextId++,
      title: '数据分析',
      description: '强大的数据分析和报告功能，帮助您了解用户行为和内容效果。',
      icon: 'fa-chart-line'
    },
    {
      id: nextId++,
      title: '消息互动',
      description: '实时消息通知和用户互动功能，提高用户参与度和留存率。',
      icon: 'fa-comments'
    },
    {
      id: nextId++,
      title: '系统集成',
      description: '无缝集成各类第三方服务和API，扩展系统功能和应用场景。',
      icon: 'fa-puzzle-piece'
    }
  ]
  itemsPerRow.value = 2
  textColor.value = 'default'
  customTextColor.value = '333333'
  textColorPicker.value = '#333333'
  headingStyle.value = 'h5'
  headingFontSize.value = 1.1
  headingFontWeight.value = 'bold'
  cardBackgroundColor.value = 'F8F9FA'
  cardBgColorPicker.value = '#F8F9FA'
  iconColor.value = '4E73DF'
  iconColorPicker.value = '#4E73DF'
  borderRadius.value = 8
}

/**
 * 提取特性列表数据
 */
const extractFeatureListData = (): boolean => {
  if (!props.blockElement) return false
  
  try {
  // 查找所有特性卡片
  const featureCards = props.blockElement.querySelectorAll('.feature-list-card')
  
  if (featureCards.length > 0) {
    // 清空现有特性
    features.value = []
    
    // 提取每行特性数量
    const firstRow = props.blockElement.querySelector('.feature-list-container > .row')
    if (firstRow) {
      const colClass = Array.from(firstRow.classList).find(cls => cls.startsWith('col-md-'))
      if (colClass) {
        const colNumber = parseInt(colClass.replace('col-md-', ''))
        itemsPerRow.value = 12 / colNumber || 2
      }
    }
    
    // 遍历每个特性卡片
    featureCards.forEach((card, index) => {
      const titleEl = card.querySelector('.feature-title')
      const descEl = card.querySelector('.feature-description')
      const iconEl = card.querySelector('.feature-icon i')
      
      const title = titleEl ? titleEl.textContent || '' : ''
      const description = descEl ? descEl.textContent || '' : ''
      let icon = 'fa-file-alt'
      
      // 提取图标类名
      if (iconEl) {
        const iconClass = Array.from(iconEl.classList)
          .find(cls => cls.startsWith('fa-'))
        
        if (iconClass) {
          icon = iconClass
        }
      }
      
      // 添加到特性列表
      features.value.push({
        id: nextId++,
        title,
        description,
        icon
      })
    })

// 提取样式
  // 提取标题样式
  const titleEl = props.blockElement.querySelector('.feature-title')
  if (titleEl) {
    // 检查是哪种标题元素
    const tagName = titleEl.tagName.toLowerCase()
    headingStyle.value = tagName
    
    // 提取字体大小
    const fontSize = window.getComputedStyle(titleEl).fontSize
    if (fontSize) {
      const sizeValue = parseFloat(fontSize)
      headingFontSize.value = sizeValue / 16 // 转换为 rem
    }
    
    // 提取字体粗细
    const fontWeight = window.getComputedStyle(titleEl).fontWeight
    if (fontWeight) {
      if (parseInt(fontWeight) >= 700) {
        headingFontWeight.value = 'bold'
      } else if (parseInt(fontWeight) <= 300) {
        headingFontWeight.value = 'light'
      } else {
        headingFontWeight.value = 'normal'
      }
    }
  }
  
  // 提取卡片背景色
  const cardEl = props.blockElement.querySelector('.feature-list-card')
  if (cardEl) {
    const bgColor = window.getComputedStyle(cardEl).backgroundColor
    if (bgColor && bgColor !== 'transparent') {
      const rgbMatch = bgColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
      if (rgbMatch) {
        const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0')
        const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0')
        const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0')
        cardBackgroundColor.value = `${r}${g}${b}`.toUpperCase()
        cardBgColorPicker.value = `#${cardBackgroundColor.value}`
      }
    }
    
    // 提取圆角
    const borderRadiusValue = window.getComputedStyle(cardEl).borderRadius
    if (borderRadiusValue) {
      const radiusValue = parseInt(borderRadiusValue)
      if (!isNaN(radiusValue)) {
        borderRadius.value = radiusValue
      }
    }
  }
  
  // 提取图标颜色
  const iconEl = props.blockElement.querySelector('.feature-icon')
  if (iconEl) {
    const iconColorValue = window.getComputedStyle(iconEl).color
    if (iconColorValue) {
      const rgbMatch = iconColorValue.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
      if (rgbMatch) {
        const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0')
        const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0')
        const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0')
        iconColor.value = `${r}${g}${b}`.toUpperCase()
        iconColorPicker.value = `#${iconColor.value}`
      }
    }
  }
  
  // 提取文本颜色
  const descEl = props.blockElement.querySelector('.feature-description')
  if (descEl) {
    const textColorValue = window.getComputedStyle(descEl).color
    if (textColorValue) {
      const rgbMatch = textColorValue.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
      if (rgbMatch) {
        const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0')
        const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0')
        const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0')
        customTextColor.value = `${r}${g}${b}`.toUpperCase()
        textColorPicker.value = `#${customTextColor.value}`
        
        // 如果是自定义颜色，设置为custom
        textColor.value = 'custom'
      }
    }
  }
      
      return true
    }
    
    return false
  } catch (error) {
    console.error('提取特性列表数据时出错:', error)
    return false
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置更改状态
    isChanged.value = false
    editingIndex.value = -1
    
    // 重新提取数据
    const extracted = extractFeatureListData()
    
    // 如果提取失败，使用默认值
    if (!extracted) {
      setDefaultValues()
    }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  // 移除原有的初始化逻辑，因为已经由 watch 处理
  isChanged.value = false
})

// 编辑特性
const editItem = (index: number) => {
  editingIndex.value = editingIndex.value === index ? -1 : index
}

// 添加新特性
const addFeature = () => {
  features.value.push({
    id: nextId++,
    title: '新特性',
    description: '描述这个特性的详细信息，突出其主要优势和用途。',
    icon: 'fa-cog'
  })
  editingIndex.value = features.value.length - 1
  markAsChanged()
}

// 移除特性
const removeFeature = (index: number) => {
  if (features.value.length > 1) {
    features.value.splice(index, 1)
    if (editingIndex.value === index) {
      editingIndex.value = -1
    } else if (editingIndex.value > index) {
      editingIndex.value--
    }
    markAsChanged()
  }
}

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 更新文本颜色
const updateTextColor = (value: string) => {
  if (value) {
    // 从 '#RRGGBB' 或 '#RRGGBBAA' 格式转换为 'RRGGBB'
    customTextColor.value = value.substring(1).replace(/[^0-9A-F]/gi, '').slice(0, 6)
    markAsChanged()
  }
}

// 更新卡片背景颜色
const updateCardBgColor = (value: string) => {
  if (value) {
    cardBackgroundColor.value = value.substring(1).replace(/[^0-9A-F]/gi, '').slice(0, 6)
    markAsChanged()
  }
}

// 更新图标颜色
const updateIconColor = (value: string) => {
  if (value) {
    iconColor.value = value.substring(1).replace(/[^0-9A-F]/gi, '').slice(0, 6)
    markAsChanged()
  }
}

// 监听颜色输入，同步更新颜色选择器
watch(customTextColor, (newValue) => {
  textColorPicker.value = '#' + newValue
})

watch(cardBackgroundColor, (newValue) => {
  cardBgColorPicker.value = '#' + newValue
})

watch(iconColor, (newValue) => {
  iconColorPicker.value = '#' + newValue
})

// 生成HTML
const generateFeatureListHTML = () => {
  // 根据每行特性数量计算列宽
  const colWidth = 12 / itemsPerRow.value
  
  // 生成特性HTML
  const featuresHTML = features.value.map(feature => {
    return `
      <div class="mb-4 col-md-${colWidth}">
        <div class="feature-list-card" style="background: #${cardBackgroundColor.value}; border-radius: ${borderRadius.value}px;">
          <div class="feature-icon" style="color: #${iconColor.value};">
            <i class="fas ${feature.icon} text-primary"></i>
          </div>
          <div class="feature-content">
            <${headingStyle.value} class="feature-title" style="font-size: ${headingFontSize.value}rem; font-weight: ${headingFontWeight.value === 'bold' ? '600' : headingFontWeight.value === 'light' ? '300' : 'normal'};">${feature.title}</${headingStyle.value}>
            <p class="feature-description" style="${textColor.value === 'custom' ? `color: #${customTextColor.value};` : ''}">${feature.description}</p>
          </div>
        </div>
      </div>
    `
  }).join('\n')
  
  // 生成完整的特性列表HTML
  return `
<div data-bs-component="feature-list" class="py-5 bootstrap-feature-list">
  <div class="p-0 container-fluid">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-8">
        <div class="row feature-list-container">
          ${featuresHTML}
        </div>
      </div>
    </div>
  </div>
  <style>
    .bootstrap-feature-list {
      background-color: #fff;
      padding: 30px 0;
    }
    .feature-list-container {
      margin-top: 20px;
    }
    .feature-list-card {
      display: flex;
      background: #${cardBackgroundColor.value};
      border-radius: ${borderRadius.value}px;
      padding: 20px;
      height: 100%;
      transition: all 0.3s ease;
    }
    .feature-list-card:hover {
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      transform: translateY(-5px);
    }
    .feature-icon {
      flex: 0 0 40px;
      margin-right: 15px;
      font-size: 24px;
      color: #${iconColor.value};
    }
    .feature-content {
      flex: 1;
    }
    .feature-title {
      font-size: ${headingFontSize.value}rem;
      font-weight: ${headingFontWeight.value === 'bold' ? '600' : headingFontWeight.value === 'light' ? '300' : 'normal'};
      margin-bottom: 8px;
      color: ${textColor.value === 'custom' ? `#${customTextColor.value}` : '#333'};
    }
    .feature-description {
      font-size: 0.9rem;
      color: ${textColor.value === 'custom' ? `#${customTextColor.value}` : '#666'};
      margin-bottom: 0;
    }
  </style>
</div>
  `.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    const html = generateFeatureListHTML()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
    
    ElMessage.success('特性列表已更新')
  } catch (error) {
    console.error('应用特性列表更改时出错:', error)
    ElMessage.error('更新特性列表失败，请检查输入')
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.feature-item-edit {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.feature-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  
  span {
    font-weight: bold;
  }
}

.feature-item-actions {
  display: flex;
  gap: 5px;
}

.feature-item-content {
  padding: 15px;
}

.add-item-button {
  margin-top: 15px;
  text-align: center;
}

// 颜色选择器样式
.color-input-row {
  display: flex;
  align-items: center;
  margin-top: 8px;
  
  .color-hash {
    padding: 0 8px;
    background-color: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #606266;
    height: 32px;
    display: flex;
    align-items: center;
  }
  
  .color-input {
    flex-grow: 1;
    
    :deep(.el-input__inner) {
      border-radius: 0;
    }
  }
  
  .color-preview {
    width: 32px;
    height: 32px;
    border: 1px solid #dcdfe6;
    border-left: none;
    border-right: none;
    flex-shrink: 0;
  }
  
  :deep(.el-color-picker) {
    height: 32px;
    margin-left: -1px;
    
    .el-color-picker__trigger {
      border-radius: 0 4px 4px 0;
      border: 1px solid #dcdfe6;
      height: 32px;
      padding: 3px;
    }
  }
}

.heading-options {
  display: flex;
  gap: 20px;
  margin-top: 12px;
  
  .option-group {
    flex: 1;
    
    label {
      display: block;
      font-size: 14px;
      color: #606266;
      margin-bottom: 5px;
    }
    
    :deep(.el-input-number) {
      width: 100%;
    }
    
    :deep(.el-select) {
      width: 100%;
    }
  }
}

.color-picker-container {
  margin-top: 10px;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}

:deep(.el-divider__text) {
  font-size: 14px;
  color: #606266;
  background-color: #fff;
}
</style> 