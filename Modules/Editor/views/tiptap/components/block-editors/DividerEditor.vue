<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="样式" name="style">
        <el-form label-position="top">
          <el-form-item label="分隔线样式">
            <el-select v-model="dividerStyle" @change="markAsChanged" style="width: 100%">
              <el-option label="实线" value="solid" />
              <el-option label="虚线" value="dashed" />
              <el-option label="点线" value="dotted" />
              <el-option label="双线" value="double" />
            </el-select>
          </el-form-item>

          <el-form-item label="分隔线颜色">
            <el-select v-model="dividerColor" @change="markAsChanged" style="width: 100%">
              <el-option label="默认" value="default" />
              <el-option label="主题色" value="primary" />
              <el-option label="成功色" value="success" />
              <el-option label="信息色" value="info" />
              <el-option label="警告色" value="warning" />
              <el-option label="危险色" value="danger" />
            </el-select>
          </el-form-item>

          <el-form-item label="分隔线宽度">
            <el-input-number 
              v-model="dividerWidth" 
              :min="1" 
              :max="10" 
              @change="markAsChanged"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="上下间距">
            <el-select v-model="dividerSpacing" @change="markAsChanged" style="width: 100%">
              <el-option label="无间距" value="none" />
              <el-option label="小间距" value="small" />
              <el-option label="中等间距" value="medium" />
              <el-option label="大间距" value="large" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions } from 'vue'

// 定义组件名称
defineOptions({
  name: 'DividerEditor'
})

const props = defineProps({
  blockElement: {
    type: Object,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('style')

// 是否有未保存的更改
const isChanged = ref(false)

// 分隔线样式选项
const dividerStyle = ref('solid')
const dividerColor = ref('default')
const dividerWidth = ref(1)
const dividerSpacing = ref('medium')

// 组件挂载时，从现有块元素中提取分隔线数据
onMounted(() => {
  if (props.blockElement) {
    const dividerEl = props.blockElement.querySelector('.divider') as HTMLElement
    
    if (dividerEl) {
      // 提取样式
      const computedStyle = window.getComputedStyle(dividerEl)
      
      // 提取边框样式
      const borderStyle = computedStyle.borderStyle
      if (borderStyle) {
        dividerStyle.value = borderStyle
      }
      
      // 提取颜色
      if (dividerEl.classList.contains('border-primary')) {
        dividerColor.value = 'primary'
      } else if (dividerEl.classList.contains('border-success')) {
        dividerColor.value = 'success'
      } else if (dividerEl.classList.contains('border-info')) {
        dividerColor.value = 'info'
      } else if (dividerEl.classList.contains('border-warning')) {
        dividerColor.value = 'warning'
      } else if (dividerEl.classList.contains('border-danger')) {
        dividerColor.value = 'danger'
      }
      
      // 提取宽度
      const borderWidth = parseInt(computedStyle.borderWidth)
      if (!isNaN(borderWidth)) {
        dividerWidth.value = borderWidth
      }
      
      // 提取间距
      if (dividerEl.classList.contains('my-1')) {
        dividerSpacing.value = 'small'
      } else if (dividerEl.classList.contains('my-3')) {
        dividerSpacing.value = 'medium'
      } else if (dividerEl.classList.contains('my-5')) {
        dividerSpacing.value = 'large'
      }
    }
  }
  
  // 初始化时标记为未更改
  isChanged.value = false
})

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 准备分隔线HTML
const prepareDividerHTML = (): string => {
  // 基础类
  let dividerClasses = ['divider']
  
  // 添加颜色类
  if (dividerColor.value !== 'default') {
    dividerClasses.push(`border-${dividerColor.value}`)
  }
  
  // 添加间距类
  switch (dividerSpacing.value) {
    case 'small':
      dividerClasses.push('my-1')
      break
    case 'medium':
      dividerClasses.push('my-3')
      break
    case 'large':
      dividerClasses.push('my-5')
      break
  }
  
  // 构建样式
  const styles = [
    `border-style: ${dividerStyle.value}`,
    `border-width: ${dividerWidth.value}px`
  ]
  
  return `
    <div class="divider-block responsive-block" data-bs-component="divider">
      <div class="p-0 container-fluid">
        <div class="row justify-content-center">
          <div class="col-12 col-md-10 col-lg-8">
            <hr class="${dividerClasses.join(' ')}" style="${styles.join('; ')}" />
          </div>
        </div>
      </div>
    </div>
  `.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    const html = prepareDividerHTML()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
  } catch (error) {
    console.error('应用分隔线更改时出错:', error)
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}
</style> 