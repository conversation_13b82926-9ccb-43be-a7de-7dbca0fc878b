<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top" size="small">
          <!-- 价格卡片列表 -->
          <div v-for="(card, index) in cards" :key="index" class="mb-4 pricing-card-editor">
            <div class="mb-2 card-header d-flex align-items-center justify-content-between">
              <h4 class="m-0">价格卡片 {{ index + 1 }}</h4>
              <div class="card-actions">
                <el-button 
                  type="danger" 
                  size="small" 
                  icon="Delete"
                  @click="removeCard(index)"
                  :disabled="cards.length <= 1"
                >删除</el-button>
              </div>
            </div>

            <el-form-item label="标题">
              <el-input v-model="card.title" @input="markAsChanged" />
            </el-form-item>

            <el-form-item label="价格">
              <div class="price-input-group">
                <el-input-number 
                  v-model="card.price" 
                  :min="0" 
                  :precision="0"
                  @change="markAsChanged"
                />
                <el-input 
                  v-model="card.period" 
                  placeholder="计费周期，如: /month"
                  @input="markAsChanged"
                />
              </div>
            </el-form-item>

            <el-form-item label="描述">
              <el-input
                v-model="card.description"
                type="textarea"
                :rows="2"
                @input="markAsChanged"
              />
            </el-form-item>

            <el-form-item label="功能列表">
              <div v-for="(feature, featureIndex) in card.features" :key="featureIndex" class="feature-item">
                <div class="gap-2 mb-2 d-flex">
                  <el-input 
                    v-model="card.features[featureIndex]" 
                    @input="markAsChanged"
                  >
                    <template #append>
                      <el-button 
                        type="danger" 
                        icon="Delete"
                        @click="removeFeature(index, featureIndex)"
                      />
                    </template>
                  </el-input>
                </div>
              </div>
              <el-button 
                type="primary" 
                plain 
                icon="Plus" 
                @click="addFeature(index)"
              >添加功能</el-button>
            </el-form-item>

            <el-form-item label="按钮">
              <el-input v-model="card.button.text" placeholder="按钮文字" @input="markAsChanged" />
              <div class="mt-2">
                <div class="sub-field-label">跳转链接</div>
                <el-input 
                  v-model="card.button.link" 
                  placeholder="输入完整的URL地址，例如: /pricing/starter" 
                  @input="markAsChanged"
                >
                  <template #prepend>URL</template>
                </el-input>
              </div>
            </el-form-item>
          </div>

          <el-button 
            type="primary" 
            plain 
            icon="Plus" 
            @click="addCard"
            class="mt-3"
          >添加价格卡片</el-button>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="样式" name="style">
        <el-form label-position="top" size="small">
          <el-form-item label="卡片样式">
            <div class="sub-field-group">
              <div class="sub-field-label">阴影效果</div>
              <el-radio-group v-model="cardStyle.shadow" @change="markAsChanged" size="small">
                <el-radio :label="true">启用</el-radio>
                <el-radio :label="false">禁用</el-radio>
              </el-radio-group>
            </div>

            <div class="sub-field-group">
              <div class="sub-field-label">圆角大小</div>
              <el-slider
                v-model="cardStyle.borderRadius"
                :min="0"
                :max="20"
                @change="markAsChanged"
              />
            </div>

            <div class="sub-field-group">
              <div class="sub-field-label">边框</div>
              <el-radio-group v-model="cardStyle.border" @change="markAsChanged" size="small">
                <el-radio label="none">无边框</el-radio>
                <el-radio label="light">浅色边框</el-radio>
                <el-radio label="dark">深色边框</el-radio>
              </el-radio-group>
            </div>
          </el-form-item>

          <el-form-item label="按钮样式">
            <div class="sub-field-group">
              <div class="sub-field-label">样式类型</div>
              <el-radio-group v-model="buttonStyle.variant" @change="markAsChanged" size="small">
                <el-radio label="primary">主要按钮</el-radio>
                <el-radio label="outline-primary">线框按钮</el-radio>
              </el-radio-group>
            </div>

            <div class="sub-field-group">
              <div class="sub-field-label">按钮大小</div>
              <el-radio-group v-model="buttonStyle.size" @change="markAsChanged" size="small">
                <el-radio label="sm">小</el-radio>
                <el-radio label="md">中</el-radio>
                <el-radio label="lg">大</el-radio>
              </el-radio-group>
            </div>
          </el-form-item>

          <el-form-item label="布局">
            <div class="sub-field-group">
              <div class="sub-field-label">对齐方式</div>
              <el-radio-group v-model="layout.alignment" @change="markAsChanged" size="small">
                <el-radio-button label="left">
                  左对齐
                </el-radio-button>
                <el-radio-button label="center">
                  居中对齐
                </el-radio-button>
                <el-radio-button label="right">
                  右对齐
                </el-radio-button>
              </el-radio-group>
            </div>

            <div class="sub-field-group">
              <div class="sub-field-label">列布局</div>
              <el-radio-group v-model="layout.columns" @change="markAsChanged" size="small">
                <el-radio label="1">单列</el-radio>
                <el-radio label="2">双列</el-radio>
                <el-radio label="3">三列</el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges" size="small">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 定义组件名称
defineOptions({
  name: 'PricingEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 价格卡片数据结构
interface PricingCard {
  title: string
  price: number
  period: string
  description: string
  features: string[]
  button: {
    text: string
    link: string
  }
}

// 价格卡片列表
const cards = ref<PricingCard[]>([
  {
    title: 'Starter',
    price: 79,
    period: '/month',
    description: 'Perfect for small businesses or startups looking to kickstart their digital marketing efforts',
    features: [
      'Social media management',
      'Email marketing campaigns',
      'Basic SEO optimization',
      'Monthly performance reports'
    ],
    button: {
      text: 'Get started',
      link: '/pricing/starter'
    }
  }
])

// 样式配置
const cardStyle = ref({
  shadow: true,
  borderRadius: 8,
  border: 'light'
})

const buttonStyle = ref({
  variant: 'primary',
  size: 'lg'
})

const layout = ref({
  alignment: 'center',
  columns: '3'
})

// 添加新卡片
const addCard = () => {
  cards.value.push({
    title: `Plan ${cards.value.length + 1}`,
    price: 99,
    period: '/month',
    description: 'Add your plan description here',
    features: ['Feature 1', 'Feature 2', 'Feature 3'],
    button: {
      text: 'Get started',
      link: '/pricing/plan'
    }
  })
  markAsChanged()
}

// 删除卡片
const removeCard = (index: number) => {
  if (cards.value.length > 1) {
    cards.value.splice(index, 1)
    markAsChanged()
  }
}

// 添加功能项
const addFeature = (cardIndex: number) => {
  cards.value[cardIndex].features.push('New feature')
  markAsChanged()
}

// 删除功能项
const removeFeature = (cardIndex: number, featureIndex: number) => {
  cards.value[cardIndex].features.splice(featureIndex, 1)
  markAsChanged()
}

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 提取数据的函数
const extractBlockData = () => {
  if (props.blockElement) {
    try {
      // 提取价格卡片数据
      const cardElements = props.blockElement.querySelectorAll('.card')
      const extractedCards: PricingCard[] = []

      cardElements.forEach(cardEl => {
        const title = cardEl.querySelector('.fw-normal')?.textContent || ''
        const priceText = cardEl.querySelector('.pricing-card-title')?.textContent || ''
        const price = parseInt(priceText.replace(/[^\d]/g, '')) || 0
        const period = priceText.match(/\/\w+/)?.[0] || '/month'
        const description = cardEl.querySelector('p')?.textContent || ''
        
        const features: string[] = []
        cardEl.querySelectorAll('ul li').forEach(li => {
          if (li.textContent && !li.classList.contains('opacity-0')) {
            features.push(li.textContent)
          }
        })

        const button = cardEl.querySelector('button')
        const buttonText = button?.textContent || 'Get started'
        const buttonLink = button?.getAttribute('data-pricing-plan') || '#'
        
        extractedCards.push({
          title,
          price,
          period,
          description,
          features,
          button: {
            text: buttonText,
            link: buttonLink
          }
        })
      })

      if (extractedCards.length > 0) {
        cards.value = extractedCards
      }

      // 提取样式
      const container = props.blockElement
      
      // 提取卡片样式
      const card = container.querySelector('.card')
      if (card) {
        cardStyle.value.shadow = card.classList.contains('shadow-sm')
        cardStyle.value.borderRadius = parseInt(window.getComputedStyle(card).borderRadius) || 8
        cardStyle.value.border = card.classList.contains('border') ? 'light' : 'none'
      }

      // 提取按钮样式
      const button = container.querySelector('button')
      if (button) {
        buttonStyle.value.variant = button.classList.contains('btn-outline-primary') ? 'outline-primary' : 'primary'
        buttonStyle.value.size = button.classList.contains('btn-lg') ? 'lg' : 'md'
      }

      // 提取布局
      const row = container.querySelector('.row')
      if (row) {
        layout.value.alignment = row.classList.contains('text-center') ? 'center' : 'left'
        layout.value.columns = row.classList.contains('row-cols-md-3') ? '3' : '1'
      }
    } catch (error) {
      console.error('提取价格卡片数据时出错:', error)
    }
  }

  // 重置更改状态
  isChanged.value = false
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue) => {
  if (newValue) {
    extractBlockData()
  }
}, { immediate: true })

// 修改 onMounted 钩子
onMounted(() => {
  // 初始化时标记为未更改
  isChanged.value = false
})

// 生成价格卡片HTML
const generatePricingHTML = (): string => {
  const columnClass = `row-cols-1 row-cols-md-${layout.value.columns}`
  const alignmentClass = layout.value.alignment === 'center' ? 'text-center' : ''
  
  const cardClasses = [
    'card',
    'rounded-3',
    'd-flex',
    'flex-column',
    'h-100',
    cardStyle.value.shadow ? 'shadow-sm' : '',
    cardStyle.value.border !== 'none' ? 'border' : ''
  ].filter(Boolean).join(' ')

  const buttonClasses = [
    'btn',
    `btn-${buttonStyle.value.variant}`,
    `btn-${buttonStyle.value.size}`,
    'w-100',
    'mt-auto'
  ].join(' ')

  const cardsHTML = cards.value.map(card => `
    <div class="col">
      <div class="${cardClasses}">
        <div class="py-3 card-header">
          <h4 class="my-0 fw-normal">${card.title}</h4>
        </div>
        <div class="card-body d-flex flex-column">
          <h1 class="card-title pricing-card-title">$${card.price}<small class="text-body-secondary fw-light">${card.period}</small></h1>
          <p class="mt-3 mb-4">${card.description}</p>
          <ul class="mt-3 mb-4 list-unstyled flex-grow-1">
            ${card.features.map(feature => `<li>${feature}</li>`).join('\n')}
            ${card.features.length < 5 ? '<li class="opacity-0">Hidden item for height</li>' : ''}
          </ul>
          <button type="button" class="${buttonClasses}" data-pricing-plan="${card.button.link}">${card.button.text}</button>
        </div>
      </div>
    </div>
  `).join('\n')

  // 添加点击处理脚本
  const script = `
    (function() {
      const pricingComponents = document.querySelectorAll('[data-bs-component="pricing"]');
      
      pricingComponents.forEach(component => {
        const pricingButtons = component.querySelectorAll('[data-pricing-plan]');
        
        pricingButtons.forEach(button => {
          button.addEventListener('click', function() {
            const planType = this.getAttribute('data-pricing-plan');
            
            switch(planType) {
              case 'starter':
                window.location.href = '/pricing/starter';
                break;
              case 'pro':
                window.location.href = '/pricing/pro';
                break;
              case 'enterprise':
                window.location.href = '/contact/sales';
                break;
              default:
                window.location.href = '/pricing';
            }
            
            console.log('用户点击了' + planType + '价格计划');
            
            const clickEvent = new CustomEvent('pricing-plan-selected', { 
              detail: { plan: planType } 
            });
            document.dispatchEvent(clickEvent);
          });
        });
      });
    })();
  `

  return `
<div class="container py-5" data-bs-component="pricing">
  <div class="mb-3 ${alignmentClass} row ${columnClass} g-3">
    ${cardsHTML}
  </div>
  <script>${script}<\/script>
</div>`.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    const html = generatePricingHTML()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
    
    ElMessage.success('价格卡片已更新')
  } catch (error) {
    console.error('应用价格卡片更改时出错:', error)
    ElMessage.error('更新价格卡片失败，请检查输入')
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 15px;
  position: relative;
  max-width: 100%;
  overflow-x: hidden;
}

.pricing-card-editor {
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.card-header {
  h4 {
    font-size: 16px;
    color: #303133;
  }
}

.price-input-group {
  display: flex;
  gap: 10px;
  align-items: center;

  .el-input-number {
    width: 120px;
  }

  .el-input {
    flex: 1;
  }
}

.feature-item {
  margin-bottom: 8px;
}

.apply-button-container {
  margin-top: 15px;
  text-align: center;
  padding: 8px 0;
  border-top: 1px dashed #e4e7ed;
}

.sub-field-group {
  margin-bottom: 12px;
  
  .sub-field-label {
    font-size: 13px;
    color: #606266;
    margin-bottom: 6px;
  }
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

:deep(.el-tabs__nav) {
  padding: 0 5px;
}

:deep(.el-radio-button__inner) {
  padding: 6px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-form-item__label) {
  padding-bottom: 4px;
  line-height: 1.4;
  font-size: 13px;
}

:deep(.el-slider) {
  margin-top: 6px;
  margin-bottom: 6px;
}
</style> 