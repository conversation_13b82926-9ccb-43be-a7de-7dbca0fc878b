<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top">
          <el-form-item label="标题文本">
            <el-input
              v-model="headingText"
              @input="markAsChanged"
              placeholder="输入标题文本"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </el-form-item>
          
          <el-form-item label="标题级别">
            <el-select v-model="headingLevel" @change="markAsChanged" style="width: 100%">
              <el-option label="标题 1 (最大)" value="1" />
              <el-option label="标题 2" value="2" />
              <el-option label="标题 3" value="3" />
              <el-option label="标题 4" value="4" />
              <el-option label="标题 5" value="5" />
              <el-option label="标题 6 (最小)" value="6" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="样式" name="style">
        <el-form label-position="top">
          <el-form-item label="文本对齐">
            <el-radio-group v-model="textAlign" @change="markAsChanged">
              <el-radio-button label="left">
                左对齐
              </el-radio-button>
              <el-radio-button label="center">
                居中对齐
              </el-radio-button>
              <el-radio-button label="right">
               右对齐
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="字体颜色">
            <div class="color-input-row">
              <span class="color-hash">#</span>
              <el-input
                v-model="textColor"
                placeholder="000000"
                @input="markAsChanged"
                class="color-input"
              />
              <div class="color-preview" :style="{ backgroundColor: '#' + textColor }"></div>
              <el-color-picker 
                v-model="textColorPicker" 
                show-alpha 
                @change="updateTextColor" 
              />
            </div>
          </el-form-item>
          
          <el-form-item label="字体大小">
            <el-slider
              v-model="fontSize"
              :min="0.8"
              :max="3"
              :step="0.1"
              @change="markAsChanged"
              :format-tooltip="value => `${value}rem`"
            />
          </el-form-item>
          
          <el-form-item label="字体粗细">
            <el-select v-model="fontWeight" @change="markAsChanged" style="width: 100%">
              <el-option label="常规" value="normal" />
              <el-option label="中等" value="medium" />
              <el-option label="粗体" value="bold" />
              <el-option label="特粗" value="bolder" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineProps, defineEmits, defineOptions } from 'vue'
import { ElMessage } from 'element-plus'
import { Minus } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'HeadingEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 标题属性
const headingText = ref('清晰醒目的标题')
const headingLevel = ref('1')
const textAlign = ref('left')
const textColor = ref('062953')
const textColorPicker = ref('#062953')
const fontSize = ref(2.5)
const fontWeight = ref('bold')

// 原始HTML
const originalHtml = ref('')

// 标题结构信息
const headingStructure = ref({
  containerClasses: [] as string[],
  rowClasses: [] as string[],
  colClasses: [] as string[],
  customAttributes: {} as Record<string, string>,
  headingAttributes: {} as Record<string, string>
})

/**
 * 从元素中提取所有属性
 */
const extractAttributes = (element: Element): Record<string, string> => {
  const attributes: Record<string, string> = {}
  
  Array.from(element.attributes).forEach(attr => {
    if (attr.name !== 'class' && attr.name !== 'style' && !attr.name.startsWith('data-v-')) {
      attributes[attr.name] = attr.value
    }
  })
  
  return attributes
}

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 更新文本颜色
const updateTextColor = (value: string) => {
  if (value) {
    // 从 '#RRGGBB' 或 '#RRGGBBAA' 格式转换为 'RRGGBB'
    textColor.value = value.substring(1).replace(/[^0-9A-F]/gi, '').slice(0, 6)
    markAsChanged()
  }
}

// 监听颜色输入，同步更新颜色选择器
watch(textColor, (newValue) => {
  textColorPicker.value = '#' + newValue
})

/**
 * 设置默认值
 */
const setDefaultValues = () => {
  headingText.value = '清晰醒目的标题'
  headingLevel.value = '1'
  textAlign.value = 'left'
  textColor.value = '062953'
  textColorPicker.value = '#062953'
  fontSize.value = 2.5
  fontWeight.value = 'bold'
  
  // 重置结构信息
  headingStructure.value = {
    containerClasses: [],
    rowClasses: [],
    colClasses: [],
    customAttributes: {},
    headingAttributes: {}
  }
}

/**
 * 提取标题内容和样式
 */
const extractHeadingContent = (): boolean => {
  if (!props.blockElement) return false

  // 保存原始HTML
  originalHtml.value = props.blockElement.outerHTML

  try {
    // 提取自定义属性
    headingStructure.value.customAttributes = extractAttributes(props.blockElement)

    // 找到容器元素
    const container = props.blockElement.querySelector('.container, .container-fluid')
    if (container) {
      headingStructure.value.containerClasses = Array.from(container.classList)
      
      // 提取行元素
      const row = container.querySelector('.row')
      if (row) {
        headingStructure.value.rowClasses = Array.from(row.classList)
        
        // 提取列元素
        const col = row.querySelector('[class*="col-"]')
        if (col) {
          headingStructure.value.colClasses = Array.from(col.classList)
          
          // 提取文本对齐方式
          if (col.classList.contains('text-center')) {
            textAlign.value = 'center'
          } else if (col.classList.contains('text-right')) {
            textAlign.value = 'right'
          } else {
            textAlign.value = 'left'
          }
        }
      }
    }

    // 找到标题元素
    const headingEl = props.blockElement.querySelector('.heading-title, h1, h2, h3, h4, h5, h6')
    if (headingEl) {
      // 提取标题属性
      headingStructure.value.headingAttributes = extractAttributes(headingEl)
      
      // 提取文本
      headingText.value = headingEl.textContent || '清晰醒目的标题'
      
      // 提取标题级别
      const tagName = headingEl.tagName.toLowerCase()
      if (tagName.startsWith('h') && tagName.length === 2) {
        headingLevel.value = tagName.substring(1)
      } else if (headingEl.hasAttribute('data-heading-level')) {
        headingLevel.value = headingEl.getAttribute('data-heading-level') || '1'
      }
      
      // 提取样式
      const computedStyle = window.getComputedStyle(headingEl)
      
      // 提取字体颜色
      const color = computedStyle.color
      if (color) {
        const rgbMatch = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
        if (rgbMatch) {
          const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0')
          const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0')
          const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0')
          textColor.value = `${r}${g}${b}`.toUpperCase()
          textColorPicker.value = `#${textColor.value}`
        }
      }
      
      // 提取字体大小
      const fontSizeValue = computedStyle.fontSize
      if (fontSizeValue) {
        const sizeValue = parseFloat(fontSizeValue)
        fontSize.value = sizeValue / 16 // 转换为 rem
      }
      
      // 提取字体粗细
      const fontWeightValue = computedStyle.fontWeight
      if (fontWeightValue) {
        if (fontWeightValue === '700' || fontWeightValue === 'bold') {
          fontWeight.value = 'bold'
        } else if (fontWeightValue === '800' || fontWeightValue === 'bolder') {
          fontWeight.value = 'bolder'
        } else if (fontWeightValue === '500' || fontWeightValue === 'medium') {
          fontWeight.value = 'medium'
        } else {
          fontWeight.value = 'normal'
        }
      }
    }
    
    return true
  } catch (error) {
    console.error('提取标题内容时出错:', error)
    return false
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置更改状态
    isChanged.value = false
    
    // 重新提取数据
    const extracted = extractHeadingContent()
    
    // 如果提取失败，使用默认值
    if (!extracted) {
      setDefaultValues()
    }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  // 移除原有的初始化逻辑，因为已经由 watch 处理
  isChanged.value = false
})

/**
 * 准备标题HTML，保留原始结构
 */
const prepareHeadingHTML = (): string => {
  try {
    // 获取字体粗细值
    let weightValue = '400'
    if (fontWeight.value === 'bold') {
      weightValue = '700'
    } else if (fontWeight.value === 'bolder') {
      weightValue = '800'
    } else if (fontWeight.value === 'medium') {
      weightValue = '500'
    }

    // 如果传入的是简单标题元素
    if (props.blockElement && ['H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(props.blockElement.tagName)) {
      // 这种情况需要创建一个完整的包装结构，不能只返回标题元素
      const tagName = `h${headingLevel.value}`
      
      // 为包装元素创建data-bs-component属性
      let wrapperAttributesStr = ' data-bs-component="bootstrap-heading"'
      
      // 创建完整的标题组件结构，而不仅是标题元素
      return `
<div class="bootstrap-heading"${wrapperAttributesStr}>
  <div class="p-0 container-fluid">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-8 ${textAlign.value !== 'left' ? `text-${textAlign.value}` : ''}">
        <${tagName} class="heading-title" data-heading-level="${headingLevel.value}" data-align="${textAlign.value}" style="color: #${textColor.value}; font-size: ${fontSize.value}rem; font-weight: ${weightValue};">${headingText.value}</${tagName}>
      </div>
    </div>
  </div>
  <style>
    .bootstrap-heading {
      margin: 20px 0;
    }
    .heading-title {
      margin-bottom: 1rem;
      color: #${textColor.value};
      font-weight: ${weightValue};
      font-size: ${fontSize.value}rem;
    }
  </style>
</div>`.trim()
    }
    
    // 保留原始结构但更新内容
    // 复制原始的元素并更新内容
    if (!props.blockElement) {
      return generateHeadingHTML()
    }
    
    // 创建一个临时的DOM元素来解析原始HTML
    const tempElement = document.createElement('div')
    tempElement.innerHTML = originalHtml.value
    const headingElement = tempElement.firstElementChild
    
    if (!headingElement) {
      return generateHeadingHTML()
    }
    
    // 确保有data-bs-component="heading"属性
    headingElement.setAttribute('data-bs-component', 'bootstrap-heading')
    
    // 查找标题元素
    const headingTag = headingElement.querySelector('h1, h2, h3, h4, h5, h6, .heading-title')
    if (headingTag) {
      // 更新标题文本
      headingTag.textContent = headingText.value
      
      // 更新标题级别
      if (headingTag.tagName.toLowerCase() !== `h${headingLevel.value}`) {
        // 创建新标题元素
        const newHeading = document.createElement(`h${headingLevel.value}`)
        
        // 复制原始属性
        Array.from(headingTag.attributes).forEach(attr => {
          newHeading.setAttribute(attr.name, attr.value)
        })
        
        // 复制内容
        newHeading.textContent = headingText.value
        
        // 更新样式
        newHeading.style.color = `#${textColor.value}`
        newHeading.style.fontSize = `${fontSize.value}rem`
        newHeading.style.fontWeight = weightValue
        
        // 替换原始标题
        headingTag.parentNode?.replaceChild(newHeading, headingTag)
      } else {
        // 直接更新标题样式
        const headingElement = headingTag as HTMLElement
        headingElement.style.color = `#${textColor.value}`
        headingElement.style.fontSize = `${fontSize.value}rem`
        headingElement.style.fontWeight = weightValue
      }
    }
    
    // 查找并更新文本对齐方式
    const container = headingElement.querySelector('.container, .container-fluid')
    if (container) {
      const col = container.querySelector('[class*="col-"]')
      if (col) {
        // 移除旧的文本对齐类
        col.classList.remove('text-left', 'text-center', 'text-right')
        
        // 添加新的文本对齐类
        if (textAlign.value !== 'left') {
          col.classList.add(`text-${textAlign.value}`)
        }
      }
    }
    
    // 返回更新后的HTML
    return tempElement.innerHTML
  } catch (error) {
    console.error('准备标题HTML时出错:', error)
    // 如果出错，返回原始HTML
    return originalHtml.value || generateHeadingHTML()
  }
}

// 生成HTML
const generateHeadingHTML = () => {
  // 获取字体粗细值
  let weightValue = '400'
  if (fontWeight.value === 'bold') {
    weightValue = '700'
  } else if (fontWeight.value === 'bolder') {
    weightValue = '800'
  } else if (fontWeight.value === 'medium') {
    weightValue = '500'
  }

  // 生成标题HTML，确保包含data-bs-component属性在最外层div上
  return `
<div data-bs-component="bootstrap-heading" class="bootstrap-heading">
  <div class="p-0 container-fluid">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-8 ${textAlign.value !== 'left' ? `text-${textAlign.value}` : ''}">
        <h${headingLevel.value} class="heading-title" data-heading-level="${headingLevel.value}" data-align="${textAlign.value}" style="color: #${textColor.value}; font-size: ${fontSize.value}rem; font-weight: ${weightValue};">${headingText.value}</h${headingLevel.value}>
      </div>
    </div>
  </div>
  <style>
    .bootstrap-heading {
      margin: 20px 0;
    }
    .heading-title {
      margin-bottom: 1rem;
      color: #${textColor.value};
      font-weight: ${weightValue};
      font-size: ${fontSize.value}rem;
    }
  </style>
</div>
  `.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    // 使用新的方法准备HTML，优先使用保留原始结构的方法
    const html = prepareHeadingHTML()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
    
    ElMessage.success('标题已更新')
  } catch (error) {
    console.error('应用标题更改时出错:', error)
    ElMessage.error('更新标题失败，请检查输入')
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

// 颜色选择器样式
.color-input-row {
  display: flex;
  align-items: center;
  margin-top: 8px;
  
  .color-hash {
    padding: 0 8px;
    background-color: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #606266;
    height: 32px;
    display: flex;
    align-items: center;
  }
  
  .color-input {
    flex-grow: 1;
    
    :deep(.el-input__inner) {
      border-radius: 0;
    }
  }
  
  .color-preview {
    width: 32px;
    height: 32px;
    border: 1px solid #dcdfe6;
    border-left: none;
    border-right: none;
    flex-shrink: 0;
  }
  
  :deep(.el-color-picker) {
    height: 32px;
    margin-left: -1px;
    
    .el-color-picker__trigger {
      border-radius: 0 4px 4px 0;
      border: 1px solid #dcdfe6;
      height: 32px;
      padding: 3px;
    }
  }
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}

:deep(.el-radio-button__inner) {
  padding: 8px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 