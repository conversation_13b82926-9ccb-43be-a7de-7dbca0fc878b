<template>
  <div class="edit-section">
    <el-collapse v-model="activeCollapse" class="compact-collapse">
      <!-- 可见性设置 -->
      <el-collapse-item title="可见性" name="visibility">
        <el-form label-position="top" size="small" class="compact-form">
          <el-form-item label="在此断点上隐藏">
            <el-radio-group v-model="visibility" @change="markAsChanged" size="small">
              <el-radio-button label="show">显示</el-radio-button>
              <el-radio-button label="hide">隐藏</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 对齐方式和间距 -->
      <el-collapse-item title="对齐方式和间距" name="alignment">
        <el-form label-position="top" size="small" class="compact-form">
          <!-- 垂直对齐 -->
          <el-form-item label="垂直对齐">
            <div class="alignment-buttons">
              <el-button
                size="small"
                :class="{ active: verticalAlign === 'top' }"
                @click="setVerticalAlign('top')"
                title="顶部"
              >
                <el-icon><Top /></el-icon>
              </el-button>
              <el-button
                size="small"
                :class="{ active: verticalAlign === 'middle' }"
                @click="setVerticalAlign('middle')"
                title="中间"
              >
                <el-icon><Top /></el-icon>
              </el-button>
              <el-button
                size="small"
                :class="{ active: verticalAlign === 'bottom' }"
                @click="setVerticalAlign('bottom')"
                title="底部"
              >
                <el-icon><Bottom /></el-icon>
              </el-button>
            </div>
          </el-form-item>

          <!-- 内容对齐方式 -->
          <el-form-item label="内容对齐方式">
            <div class="content-layout-options">
              <div
                class="layout-option"
                :class="{ active: contentLayout === 'center' }"
                @click="setContentLayout('center')"
              >
                <div class="layout-preview center-preview"></div>
                <div class="layout-label">内容居中</div>
              </div>
              <div
                class="layout-option"
                :class="{ active: contentLayout === 'full' }"
                @click="setContentLayout('full')"
              >
                <div class="layout-preview full-preview"></div>
                <div class="layout-label">完整宽度</div>
              </div>
            </div>
          </el-form-item>

          <!-- 填充和边距 -->
          <el-form-item label="填充和边距">
            <div class="padding-margin-section">
              <div class="tabs">
                <div
                  class="tab"
                  :class="{ active: spacingTab === 'padding' }"
                  @click="spacingTab = 'padding'"
                >
                  填充
                </div>
                <div
                  class="tab"
                  :class="{ active: spacingTab === 'margin' }"
                  @click="spacingTab = 'margin'"
                >
                  边距
                </div>
              </div>

              <div class="spacing-controls">
                <el-checkbox v-model="applyToAllSides" @change="markAsChanged" size="small">应用到所有边</el-checkbox>

                <div class="spacing-inputs">
                  <!-- 上边距/填充 -->
                  <div class="spacing-input">
                    <label>上</label>
                    <div class="input-with-controls">
                      <el-input-number
                        v-if="spacingTab === 'padding'"
                        v-model="paddingTop"
                        :min="0"
                        :max="200"
                        @change="handleSpacingChange"
                        size="small"
                        controls-position="right"
                      />
                      <el-input-number
                        v-else
                        v-model="marginTop"
                        :min="0"
                        :max="200"
                        @change="handleSpacingChange"
                        size="small"
                        controls-position="right"
                      />
                      <div class="unit">px</div>
                    </div>
                  </div>

                  <!-- 左边距/填充 -->
                  <div class="spacing-input">
                    <label>左</label>
                    <div class="input-with-controls">
                      <el-input-number
                        v-if="spacingTab === 'padding'"
                        v-model="paddingLeft"
                        :min="0"
                        :max="200"
                        @change="handleSpacingChange"
                        size="small"
                        controls-position="right"
                      />
                      <el-input-number
                        v-else
                        v-model="marginLeft"
                        :min="0"
                        :max="200"
                        @change="handleSpacingChange"
                        size="small"
                        controls-position="right"
                      />
                      <div class="unit">px</div>
                    </div>
                  </div>

                  <!-- 中间图标 -->
                  <div class="spacing-icon">
                    <el-icon><Document /></el-icon>
                  </div>

                  <!-- 右边距/填充 -->
                  <div class="spacing-input">
                    <label>右</label>
                    <div class="input-with-controls">
                      <el-input-number
                        v-if="spacingTab === 'padding'"
                        v-model="paddingRight"
                        :min="0"
                        :max="200"
                        @change="handleSpacingChange"
                        size="small"
                        controls-position="right"
                      />
                      <el-input-number
                        v-else
                        v-model="marginRight"
                        :min="0"
                        :max="200"
                        @change="handleSpacingChange"
                        size="small"
                        controls-position="right"
                      />
                      <div class="unit">px</div>
                    </div>
                  </div>

                  <!-- 下边距/填充 -->
                  <div class="spacing-input">
                    <label>下</label>
                    <div class="input-with-controls">
                      <el-input-number
                        v-if="spacingTab === 'padding'"
                        v-model="paddingBottom"
                        :min="0"
                        :max="200"
                        @change="handleSpacingChange"
                        size="small"
                        controls-position="right"
                      />
                      <el-input-number
                        v-else
                        v-model="marginBottom"
                        :min="0"
                        :max="200"
                        @change="handleSpacingChange"
                        size="small"
                        controls-position="right"
                      />
                      <div class="unit">px</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 背景设置 -->
      <el-collapse-item title="背景" name="background">
        <el-form label-position="top" size="small" class="compact-form">
          <el-form-item label="背景类型">
            <el-radio-group v-model="backgroundType" @change="markAsChanged" size="small">
              <el-radio label="none">无</el-radio>
              <el-radio label="color">颜色</el-radio>
              <el-radio label="image">图像</el-radio>
              <el-radio label="gradient">渐变</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 颜色背景 -->
          <el-form-item v-if="backgroundType === 'color'" label="背景颜色">
            <el-color-picker v-model="backgroundColor" @change="markAsChanged" show-alpha size="small" />
          </el-form-item>

          <!-- 图像背景 -->
          <template v-if="backgroundType === 'image'">
            <el-form-item label="背景图片">
              <el-input v-model="backgroundImage" placeholder="输入图片URL" @input="markAsChanged" size="small" />
              <div class="image-preview-container" v-if="backgroundImage">
                <img :src="backgroundImage" class="image-preview" alt="背景图片预览" />
              </div>
            </el-form-item>
            <el-form-item label="背景尺寸">
              <el-select v-model="backgroundSize" @change="markAsChanged" style="width: 100%" size="small">
                <el-option label="覆盖 (cover)" value="cover" />
                <el-option label="包含 (contain)" value="contain" />
                <el-option label="自动 (auto)" value="auto" />
              </el-select>
            </el-form-item>
            <el-form-item label="背景重复">
              <el-select v-model="backgroundRepeat" @change="markAsChanged" style="width: 100%" size="small">
                <el-option label="不重复" value="no-repeat" />
                <el-option label="重复" value="repeat" />
                <el-option label="水平重复" value="repeat-x" />
                <el-option label="垂直重复" value="repeat-y" />
              </el-select>
            </el-form-item>
          </template>

          <!-- 渐变背景 -->
          <template v-if="backgroundType === 'gradient'">
            <el-form-item label="渐变类型">
              <el-select v-model="gradientType" @change="markAsChanged" style="width: 100%" size="small">
                <el-option label="线性渐变" value="linear" />
                <el-option label="径向渐变" value="radial" />
              </el-select>
            </el-form-item>
            <el-form-item label="渐变方向" v-if="gradientType === 'linear'">
              <el-select v-model="gradientDirection" @change="markAsChanged" style="width: 100%" size="small">
                <el-option label="从上到下" value="to bottom" />
                <el-option label="从左到右" value="to right" />
                <el-option label="从左上到右下" value="to bottom right" />
                <el-option label="从左下到右上" value="to top right" />
              </el-select>
            </el-form-item>
            <el-form-item label="渐变颜色">
              <div class="gradient-colors">
                <div class="gradient-color">
                  <span>起始颜色</span>
                  <el-color-picker v-model="gradientStartColor" @change="markAsChanged" show-alpha size="small" />
                </div>
                <div class="gradient-color">
                  <span>结束颜色</span>
                  <el-color-picker v-model="gradientEndColor" @change="markAsChanged" show-alpha size="small" />
                </div>
              </div>
            </el-form-item>
          </template>
        </el-form>
      </el-collapse-item>
    </el-collapse>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" size="small" @click="applyChanges">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions } from 'vue'
import { Document, Top, Bottom } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'LayoutEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  },
  styles: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update-block', 'update-styles'])

// 当前激活的折叠面板
const activeCollapse = ref(['visibility', 'alignment', 'background'])

// 是否有未保存的更改
const isChanged = ref(false)

// 可见性设置
const visibility = ref('show')

// 对齐方式设置
const verticalAlign = ref('top')
const contentLayout = ref('center')

// 间距设置
const spacingTab = ref('padding')
const applyToAllSides = ref(false)
const paddingTop = ref(0)
const paddingRight = ref(0)
const paddingBottom = ref(0)
const paddingLeft = ref(0)
const marginTop = ref(0)
const marginRight = ref(0)
const marginBottom = ref(0)
const marginLeft = ref(0)

// 背景设置
const backgroundType = ref('none')
const backgroundColor = ref('')
const backgroundImage = ref('')
const backgroundSize = ref('cover')
const backgroundRepeat = ref('no-repeat')
const gradientType = ref('linear')
const gradientDirection = ref('to bottom')
const gradientStartColor = ref('rgba(0, 123, 255, 1)')
const gradientEndColor = ref('rgba(0, 86, 179, 1)')

// 组件挂载时，从现有块元素中提取数据
onMounted(() => {
  if (props.blockElement) {
    // 提取布局元素
    const layoutElement = props.blockElement

    // 提取可见性
    if (layoutElement.style.display === 'none') {
      visibility.value = 'hide'
    } else {
      visibility.value = 'show'
    }

    // 提取垂直对齐方式
    const alignItems = layoutElement.style.alignItems
    if (alignItems === 'center') {
      verticalAlign.value = 'middle'
    } else if (alignItems === 'flex-end') {
      verticalAlign.value = 'bottom'
    } else {
      verticalAlign.value = 'top'
    }

    // 提取内容布局
    const containerDiv = layoutElement.querySelector('.container')
    if (containerDiv && containerDiv.classList.contains('container-fluid')) {
      contentLayout.value = 'full'
    } else {
      contentLayout.value = 'center'
    }

    // 提取填充和边距
    if (props.styles) {
      paddingTop.value = props.styles.paddingTop || 0
      paddingRight.value = props.styles.paddingRight || 0
      paddingBottom.value = props.styles.paddingBottom || 0
      paddingLeft.value = props.styles.paddingLeft || 0
      marginTop.value = props.styles.marginTop || 0
      marginRight.value = props.styles.marginRight || 0
      marginBottom.value = props.styles.marginBottom || 0
      marginLeft.value = props.styles.marginLeft || 0
    }

    // 提取背景设置
    const style = layoutElement.style
    if (style.backgroundImage) {
      if (style.backgroundImage.includes('gradient')) {
        backgroundType.value = 'gradient'
        // 解析渐变
        if (style.backgroundImage.includes('linear-gradient')) {
          gradientType.value = 'linear'
          // 尝试提取方向和颜色
          const match = style.backgroundImage.match(/linear-gradient\(([^,]+),\s*([^,]+),\s*([^)]+)\)/)
          if (match) {
            gradientDirection.value = match[1]
            gradientStartColor.value = match[2]
            gradientEndColor.value = match[3]
          }
        } else if (style.backgroundImage.includes('radial-gradient')) {
          gradientType.value = 'radial'
          // 尝试提取颜色
          const match = style.backgroundImage.match(/radial-gradient\([^,]+,\s*([^,]+),\s*([^)]+)\)/)
          if (match) {
            gradientStartColor.value = match[1]
            gradientEndColor.value = match[2]
          }
        }
      } else if (style.backgroundImage !== 'none') {
        backgroundType.value = 'image'
        backgroundImage.value = style.backgroundImage.replace(/url\(['"]?([^'"]+)['"]?\)/, '$1')
        backgroundSize.value = style.backgroundSize || 'cover'
        backgroundRepeat.value = style.backgroundRepeat || 'no-repeat'
      }
    } else if (style.backgroundColor && style.backgroundColor !== 'transparent') {
      backgroundType.value = 'color'
      backgroundColor.value = style.backgroundColor
    } else {
      backgroundType.value = 'none'
    }
  }

  // 初始化时标记为未更改
  isChanged.value = false
})

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 设置垂直对齐
const setVerticalAlign = (align: string) => {
  verticalAlign.value = align
  markAsChanged()
}

// 设置内容布局
const setContentLayout = (layout: string) => {
  contentLayout.value = layout
  markAsChanged()
}

// 处理间距变化
const handleSpacingChange = () => {
  markAsChanged()

  if (applyToAllSides.value) {
    if (spacingTab.value === 'padding') {
      const value = paddingTop.value
      paddingRight.value = value
      paddingBottom.value = value
      paddingLeft.value = value
    } else {
      const value = marginTop.value
      marginRight.value = value
      marginBottom.value = value
      marginLeft.value = value
    }
  }
}

// 准备布局HTML
const prepareLayoutHTML = (): string => {
  // 获取原始布局类型
  const layoutType = props.blockElement?.getAttribute('data-bs-layout') || 'single-column'

  // 获取原始HTML结构
  let originalHtml = props.blockElement?.innerHTML || ''

  // 创建新的布局HTML
  let containerClass = contentLayout.value === 'full' ? 'container-fluid' : 'container'

  // 构建样式字符串
  let styleString = ''

  // 添加可见性
  if (visibility.value === 'hide') {
    styleString += 'display: none;'
  }

  // 添加垂直对齐
  if (verticalAlign.value === 'middle') {
    styleString += 'align-items: center;'
  } else if (verticalAlign.value === 'bottom') {
    styleString += 'align-items: flex-end;'
  }

  // 添加背景
  if (backgroundType.value === 'color') {
    styleString += `background-color: ${backgroundColor.value};`
  } else if (backgroundType.value === 'image') {
    styleString += `background-image: url('${backgroundImage.value}');`
    styleString += `background-size: ${backgroundSize.value};`
    styleString += `background-repeat: ${backgroundRepeat.value};`
  } else if (backgroundType.value === 'gradient') {
    if (gradientType.value === 'linear') {
      styleString += `background-image: linear-gradient(${gradientDirection.value}, ${gradientStartColor.value}, ${gradientEndColor.value});`
    } else {
      styleString += `background-image: radial-gradient(circle, ${gradientStartColor.value}, ${gradientEndColor.value});`
    }
  }

  // 构建新的HTML
  const html = `
    <div data-bs-component="layout" data-bs-layout="${layoutType}" class="my-4 bootstrap-layout" style="${styleString}">
      <div class="p-0 ${containerClass}">
        ${originalHtml}
      </div>
    </div>
  `.trim()

  return html
}

// 应用更改
const applyChanges = () => {
  try {
    // 准备HTML
    const html = prepareLayoutHTML()

    // 准备样式
    const styles = {
      paddingTop: paddingTop.value,
      paddingRight: paddingRight.value,
      paddingBottom: paddingBottom.value,
      paddingLeft: paddingLeft.value,
      marginTop: marginTop.value,
      marginRight: marginRight.value,
      marginBottom: marginBottom.value,
      marginLeft: marginLeft.value
    }

    // 发出更新事件
    emit('update-block', { html })
    emit('update-styles', styles)

    // 重置更改状态
    isChanged.value = false
  } catch (error) {
    console.error('应用布局更改时出错:', error)
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 10px;
  position: relative;
}

.alignment-buttons {
  display: flex;
  gap: 5px;

  .el-button {
    flex: 1;
    padding: 6px 10px;
    height: auto;

    &.active {
      background-color: #409eff;
      color: white;
    }
  }
}

.content-layout-options {
  display: flex;
  gap: 8px;
  margin-top: 5px;

  .layout-option {
    flex: 1;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 6px;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s;

    &:hover {
      border-color: #c0c4cc;
    }

    &.active {
      border-color: #409eff;
      background-color: #ecf5ff;
    }

    .layout-preview {
      height: 40px;
      background-color: #f5f7fa;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      justify-content: center;

      &::before {
        content: '';
        display: block;
      }

      &.center-preview::before {
        width: 60%;
        height: 15px;
        background-color: #409eff;
      }

      &.full-preview::before {
        width: 90%;
        height: 15px;
        background-color: #409eff;
      }
    }

    .layout-label {
      font-size: 12px;
      color: #606266;
    }
  }
}

.padding-margin-section {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;

  .tabs {
    display: flex;
    border-bottom: 1px solid #dcdfe6;

    .tab {
      flex: 1;
      text-align: center;
      padding: 5px 0;
      cursor: pointer;
      font-size: 13px;
      color: #606266;

      &.active {
        background-color: #f5f7fa;
        color: #409eff;
        font-weight: 500;
      }
    }
  }

  .spacing-controls {
    padding: 10px;

    .spacing-inputs {
      display: grid;
      grid-template-columns: 1fr auto 1fr;
      grid-template-rows: auto auto;
      gap: 8px;
      margin-top: 10px;
      align-items: center;

      .spacing-input {
        display: flex;
        flex-direction: column;
        gap: 3px;

        label {
          font-size: 12px;
          color: #606266;
        }

        .input-with-controls {
          display: flex;
          align-items: center;

          .unit {
            margin-left: 3px;
            color: #909399;
            font-size: 12px;
          }
        }
      }

      .spacing-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #909399;
        font-size: 20px;
      }
    }
  }
}

.image-preview-container {
  margin-top: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  height: 80px;

  .image-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.gradient-colors {
  display: flex;
  gap: 10px;

  .gradient-color {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 3px;

    span {
      font-size: 12px;
      color: #606266;
    }
  }
}

.apply-button-container {
  margin-top: 10px;
  text-align: center;
  padding: 8px 0;
  border-top: 1px dashed #e4e7ed;
}

:deep(.el-collapse-item__header) {
  font-weight: 500;
  font-size: 14px;
  padding: 8px 0;
  height: auto;
}

:deep(.el-collapse-item__content) {
  padding-bottom: 10px;
}

:deep(.el-form-item) {
  margin-bottom: 12px;
}

:deep(.el-form-item__label) {
  padding-bottom: 4px;
  line-height: 1.2;
  font-size: 13px;
}

:deep(.el-input-number) {
  width: 100%;

  .el-input-number__decrease,
  .el-input-number__increase {
    height: 100%;
    width: 20px;
  }

  .el-input__inner {
    padding-left: 5px;
    padding-right: 5px;
    height: 28px;
    line-height: 28px;
  }
}

:deep(.el-radio) {
  margin-right: 10px;
  line-height: 28px;
  height: 28px;
}

:deep(.el-checkbox) {
  height: 28px;
}

/* 新增的紧凑样式 */
.compact-collapse {
  :deep(.el-collapse-item__arrow) {
    font-size: 12px;
  }
}

.compact-form {
  :deep(.el-form-item__content) {
    line-height: 1.2;
  }

  :deep(.el-radio-button) {
    --el-radio-button-checked-text-color: #fff;
    --el-radio-button-checked-bg-color: #409eff;
    --el-radio-button-checked-border-color: #409eff;

    .el-radio-button__inner {
      padding: 5px 12px;
      height: 28px;
      line-height: 1;
    }
  }

  :deep(.el-select) {
    width: 100%;
  }
}
</style>
