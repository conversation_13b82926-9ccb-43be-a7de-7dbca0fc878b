<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top">
          <!-- 标题部分 -->
          <el-form-item label="标题">
            <el-input v-model="title" @input="markAsChanged" />
          </el-form-item>
          <el-form-item label="副标题">
            <el-input v-model="subtitle" @input="markAsChanged" type="textarea" :rows="2" />
          </el-form-item>

          <!-- 团队成员列表 -->
          <el-form-item label="团队成员">
            <div v-for="(member, index) in teamMembers" :key="index" class="member-item-edit">
              <div class="member-item-header">
                <span>成员 #{{ index + 1 }}</span>
                <div class="member-item-actions">
                  <el-button 
                    type="text" 
                    :icon="Edit" 
                    @click="editItem(index)"
                    title="编辑"
                  />
                  <el-button 
                    type="text" 
                    :icon="Delete" 
                    @click="removeItem(index)" 
                    title="删除"
                  />
                </div>
              </div>
              <div v-if="editingIndex === index" class="member-item-content">
                <el-form-item label="头像">
                  <el-input v-model="member.image" @input="markAsChanged" placeholder="输入图片URL" />
                </el-form-item>
                <el-form-item label="姓名">
                  <el-input v-model="member.name" @input="markAsChanged" />
                </el-form-item>
                <el-form-item label="职位">
                  <el-input v-model="member.role" @input="markAsChanged" />
                </el-form-item>
              </div>
            </div>
            <div class="add-item-button">
              <el-button type="primary" @click="addItem" icon="Plus">添加成员</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="样式" name="style">
        <el-form label-position="top">
          <!-- 布局设置 -->
          <el-form-item label="每行显示数量">
            <el-select v-model="columnsPerRow" @change="markAsChanged" style="width: 100%">
              <el-option label="2列" value="2" />
              <el-option label="3列" value="3" />
              <el-option label="4列" value="4" />
            </el-select>
          </el-form-item>

          <!-- 对齐方式 -->
          <el-form-item label="对齐方式">
            <el-radio-group v-model="alignment" @change="markAsChanged">
              <el-radio-button label="left">
                左对齐
              </el-radio-button>
              <el-radio-button label="center">
                居中对齐
              </el-radio-button>
              <el-radio-button label="right">
                右对齐
              </el-radio-button>
            </el-radio-group>
          </el-form-item>

          <!-- 间距设置 -->
          <el-form-item label="成员卡片间距">
            <el-select v-model="gapSize" @change="markAsChanged" style="width: 100%">
              <el-option label="小" value="small" />
              <el-option label="中等" value="medium" />
              <el-option label="大" value="large" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, watch } from 'vue'
import { Edit, Delete, Plus } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'TeamEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 编辑中的成员索引
const editingIndex = ref(-1)

// 团队成员数据结构
interface TeamMember {
  image: string
  name: string
  role: string
}

// 标题和副标题
const title = ref('Our team')
const subtitle = ref('Meet the awesome folks who make all of this possible day to day')

// 团队成员列表
const teamMembers = ref<TeamMember[]>([])

// 样式属性
const columnsPerRow = ref('4')
const alignment = ref('center')
const gapSize = ref('medium')

// 从DOM中提取数据
const extractTeamData = () => {
  if (!props.blockElement) return false

  try {
    // 提取标题和副标题
    const titleEl = props.blockElement.querySelector('h2')
    const subtitleEl = props.blockElement.querySelector('p.text-muted')

    if (titleEl) {
      title.value = titleEl.textContent?.trim() || 'Our team'
    }
    if (subtitleEl) {
      subtitle.value = subtitleEl.textContent?.trim() || 'Meet the awesome folks who make all of this possible day to day'
    }

    // 提取团队成员数据
    const memberCards = props.blockElement.querySelectorAll('.team-card')
    const members: TeamMember[] = []

    memberCards.forEach(card => {
      const imageEl = card.querySelector('img')
      const nameEl = card.querySelector('h5')
      const roleEl = card.querySelector('p.text-muted')

      members.push({
        image: imageEl?.getAttribute('src') || '',
        name: nameEl?.textContent?.trim() || 'Team member',
        role: roleEl?.textContent?.trim() || 'Role here'
      })
    })

    if (members.length > 0) {
      teamMembers.value = members
    } else {
      // 如果没有找到成员，添加默认成员
      teamMembers.value = [
        {
          image: 'https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-1.webp',
          name: 'Team member',
          role: 'Role here'
        }
      ]
    }

    // 提取布局信息
    const row = props.blockElement.querySelector('.row')
    if (row) {
      const firstCol = row.querySelector('[class*="col-"]')
      if (firstCol) {
        // 根据列类确定每行显示数量
        if (firstCol.classList.contains('col-md-6')) {
          columnsPerRow.value = '2'
        } else if (firstCol.classList.contains('col-md-4')) {
          columnsPerRow.value = '3'
        } else if (firstCol.classList.contains('col-md-3')) {
          columnsPerRow.value = '4'
        }
      }

      // 提取对齐方式
      if (row.classList.contains('text-center')) {
        alignment.value = 'center'
      } else if (row.classList.contains('text-right')) {
        alignment.value = 'right'
      } else {
        alignment.value = 'left'
      }

      // 提取间距
      if (row.classList.contains('g-2')) {
        gapSize.value = 'small'
      } else if (row.classList.contains('g-4')) {
        gapSize.value = 'large'
      } else {
        gapSize.value = 'medium'
      }
    }

    return true
  } catch (error) {
    console.error('提取团队数据时出错:', error)
    return false
  }
}

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 添加成员
const addItem = () => {
  teamMembers.value.push({
    image: 'https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-1.webp',
    name: 'Team member',
    role: 'Role here'
  })
  editingIndex.value = teamMembers.value.length - 1
  markAsChanged()
}

// 编辑成员
const editItem = (index: number) => {
  editingIndex.value = editingIndex.value === index ? -1 : index
}

// 删除成员
const removeItem = (index: number) => {
  teamMembers.value.splice(index, 1)
  if (editingIndex.value === index) {
    editingIndex.value = -1
  } else if (editingIndex.value > index) {
    editingIndex.value--
  }
  markAsChanged()
}

// 生成团队HTML
const generateTeamHTML = (): string => {
  // 确定列宽类
  const colClass = columnsPerRow.value === '2' ? 'col-md-6' :
                  columnsPerRow.value === '3' ? 'col-md-4' :
                  'col-md-3'

  // 确定间距类
  const gapClass = gapSize.value === 'small' ? 'g-2' :
                  gapSize.value === 'large' ? 'g-4' :
                  'g-3'

  // 生成成员卡片HTML
  const membersHTML = teamMembers.value.map(member => `
    <div class="mb-4 ${colClass} col-sm-6">
      <div class="text-center team-card">
        <div class="mb-3">
          <img data-bs-component="bootstrap-image" src="${member.image}" class="img-fluid rounded-4" alt="${member.name}">
        </div>
        <div data-bs-component="richTextBlock">
          <h5 class="mb-1">${member.name}</h5>
          <p class="text-muted small">${member.role}</p>
        </div>
      </div>
    </div>
  `).join('\n')

  return `
<div data-bs-component="team-block" class="py-5 team-section">
  <div class="container">
    <div class="mb-4 text-${alignment.value}">
      <div data-bs-component="richTextBlock">
        <h2 class="display-6 fw-bold">${title.value}</h2>
        <p class="text-muted">${subtitle.value}</p>
      </div>
    </div>
    <div class="row ${gapClass}">
      ${membersHTML}
    </div>
  </div>
</div>

<style>
.team-section {
  padding: 80px 0;
}

.team-card {
  transition: transform 0.3s ease;
}

.team-card img {
  transition: transform 0.5s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.team-card:hover {
  transform: translateY(-5px);
}

.team-card:hover img {
  transform: scale(1.03);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

@media (max-width: 767.98px) {
  .team-card {
    margin-bottom: 1.5rem;
  }
}
</style>
  `.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    const html = generateTeamHTML()
    emit('update-block', { html })
    isChanged.value = false
  } catch (error) {
    console.error('应用团队更改时出错:', error)
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue) => {
  if (newValue) {
    // 重置编辑状态
    editingIndex.value = -1
    isChanged.value = false
    
    // 提取团队数据
    extractTeamData()
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.member-item-edit {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.member-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  
  span {
    font-weight: bold;
  }
}

.member-item-actions {
  display: flex;
  gap: 5px;
}

.member-item-content {
  padding: 15px;
}

.add-item-button {
  margin-top: 15px;
  text-align: center;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}

:deep(.el-radio-button__inner) {
  padding: 8px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>