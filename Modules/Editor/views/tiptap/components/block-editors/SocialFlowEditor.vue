<template>
  <!-- 确保Font Awesome图标库加载 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="社交媒体" name="content">
        <el-form label-position="top" size="small">
          <div v-for="(link, index) in socialLinks" :key="index" class="social-link-item">
            <div class="social-link-header">
              <div class="social-link-title-wrapper">
                <span class="social-icon-preview" :class="link.class">
                  <i :class="link.icon"></i>
                </span>
                <div class="social-link-title">
              <span class="social-icon-preview" :class="[link.class, iconStyle]">
                <i :class="link.icon"></i>
              </span>
              <span>{{ link.platform }}</span>
            </div>
              </div>
              <el-button v-if="socialLinks.length > 1" type="danger" size="small" circle icon="Delete" @click="removeLink(index)"></el-button>
            </div>
            
            <el-form-item label="平台名称">
              <el-select 
                :model-value="getPlatformValue(link)"
                placeholder="选择社交平台" 
                style="width: 100%" 
                @update:model-value="(val) => handlePlatformChange(val, link)">
                <el-option
                  v-for="platform in availablePlatforms"
                  :key="platform.value"
                  :label="platform.label"
                  :value="platform.value">
                  <div style="display: flex; align-items: center;">
                    <span 
                      class="platform-icon-preview social-icon" 
                      :class="platform.class" 
                      style="margin-right: 8px; width: 24px; height: 24px; font-size: 12px;">
                      <i :class="platform.icon"></i>
                    </span>
                    <span>{{ platform.label }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="URL">
              <el-input v-model="link.url" placeholder="https://..." @change="markAsChanged" />
            </el-form-item>
            
            <el-form-item label="是否启用">
              <el-switch v-model="link.enabled" @change="markAsChanged" />
            </el-form-item>
            
            <el-form-item label="图标颜色">
              <div class="color-options">
                <span 
                  v-for="color in ['facebook-icon', 'twitter-icon', 'instagram-icon', 'linkedin-icon', 'youtube-icon', 'custom-icon']" 
                  :key="color"
                  class="color-option social-icon"
                  :class="[color, { active: link.class === color }]"
                  @click="link.class = color; markAsChanged()"
                >
                  <i :class="link.icon"></i>
                </span>
              </div>
            </el-form-item>
            
            <el-divider />
          </div>
          
          <div class="add-link-wrapper">
            <el-button type="primary" @click="addLink" size="small">添加新链接</el-button>
          </div>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="样式" name="style">
        <el-form label-position="top" size="small">
          <el-form-item label="图标大小">
            <el-select v-model="iconSize" @change="markAsChanged">
              <el-option label="小" value="sm" />
              <el-option label="中" value="md" />
              <el-option label="大" value="lg" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="图标样式">
            <el-select v-model="iconStyle" @change="markAsChanged">
              <el-option label="圆形" value="circle" />
              <el-option label="方形" value="square" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="图标排列">
            <el-select v-model="iconArrangement" @change="markAsChanged">
              <el-option label="水平" value="horizontal" />
              <el-option label="垂直" value="vertical" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges" size="small">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 定义组件名称
defineOptions({
  name: 'SocialFlowEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  }
})

const emit = defineEmits(['update-block'])

const activeTab = ref('content')
const isChanged = ref(false)

// 社交链接数据
interface SocialLink {
  platform: string
  url: string
  enabled: boolean
  icon: string
  class: string
}

// 可用的社交平台列表
interface PlatformOption {
  label: string
  value: string
  icon: string
  class: string
  defaultUrl: string
}

const availablePlatforms: PlatformOption[] = [
  { label: 'Facebook', value: 'facebook', icon: 'fab fa-facebook-f', class: 'facebook-icon', defaultUrl: 'https://facebook.com/' },
  { label: 'Twitter/X', value: 'twitter', icon: 'fab fa-twitter', class: 'twitter-icon', defaultUrl: 'https://twitter.com/' },
  { label: 'Instagram', value: 'instagram', icon: 'fab fa-instagram', class: 'instagram-icon', defaultUrl: 'https://instagram.com/' },
  { label: 'LinkedIn', value: 'linkedin', icon: 'fab fa-linkedin-in', class: 'linkedin-icon', defaultUrl: 'https://linkedin.com/' },
  { label: 'YouTube', value: 'youtube', icon: 'fab fa-youtube', class: 'youtube-icon', defaultUrl: 'https://youtube.com/' },
  { label: 'Pinterest', value: 'pinterest', icon: 'fab fa-pinterest-p', class: 'pinterest-icon', defaultUrl: 'https://pinterest.com/' },
  { label: 'TikTok', value: 'tiktok', icon: 'fab fa-tiktok', class: 'tiktok-icon', defaultUrl: 'https://tiktok.com/' },
  { label: 'WeChat', value: 'wechat', icon: 'fab fa-weixin', class: 'wechat-icon', defaultUrl: '#' },
  { label: 'Weibo', value: 'weibo', icon: 'fab fa-weibo', class: 'weibo-icon', defaultUrl: '#' },
  { label: 'GitHub', value: 'github', icon: 'fab fa-github', class: 'github-icon', defaultUrl: 'https://github.com/' },
  { label: 'QQ', value: 'qq', icon: 'fab fa-qq', class: 'qq-icon', defaultUrl: '#' },
  { label: 'Telegram', value: 'telegram', icon: 'fab fa-telegram-plane', class: 'telegram-icon', defaultUrl: 'https://t.me/' },
  { label: 'WhatsApp', value: 'whatsapp', icon: 'fab fa-whatsapp', class: 'whatsapp-icon', defaultUrl: 'https://wa.me/' },
  { label: '自定义', value: 'custom', icon: 'fab fa-globe', class: 'custom-icon', defaultUrl: 'https://' }
]

// 默认社交平台配置
const defaultSocialLinks: SocialLink[] = [
  { platform: 'Facebook', url: 'https://facebook.com/', enabled: true, icon: 'fab fa-facebook-f', class: 'facebook-icon' },
  { platform: 'Twitter/X', url: 'https://twitter.com/', enabled: true, icon: 'fab fa-twitter', class: 'twitter-icon' },
  { platform: 'Instagram', url: 'https://instagram.com/', enabled: true, icon: 'fab fa-instagram', class: 'instagram-icon' },
  { platform: 'LinkedIn', url: 'https://linkedin.com/', enabled: true, icon: 'fab fa-linkedin-in', class: 'linkedin-icon' },
  { platform: 'YouTube', url: 'https://youtube.com/', enabled: true, icon: 'fab fa-youtube', class: 'youtube-icon' },
]

const socialLinks = ref<SocialLink[]>([...defaultSocialLinks])
const iconSize = ref('md')
const iconStyle = ref('circle')
const iconArrangement = ref('horizontal')

// 根据平台名称查找平台信息
const findPlatformByName = (name: string): PlatformOption | undefined => {
  return availablePlatforms.find(p => 
    p.label.toLowerCase() === name.toLowerCase() || 
    p.value.toLowerCase() === name.toLowerCase()
  )
}

// 获取平台值
const getPlatformValue = (link: SocialLink): string => {
  const platform = availablePlatforms.find(p => 
    p.label === link.platform || 
    p.icon === link.icon ||
    p.class === link.class
  )
  return platform ? platform.value : 'custom'
}

// 平台选择改变时的处理
const handlePlatformChange = (selectedValue: string, link: SocialLink) => {
  const platform = availablePlatforms.find(p => p.value === selectedValue)
  if (platform) {
    link.platform = platform.label
    link.icon = platform.icon
    link.class = platform.class
    
    // 如果URL是默认URL或为空，则更新为新平台的默认URL
    if (!link.url || link.url === 'https://' || 
        availablePlatforms.some(p => p.defaultUrl === link.url && p.value !== selectedValue)) {
      link.url = platform.defaultUrl
    }
    
    markAsChanged()
  }
}

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 添加新链接
const addLink = () => {
  // 查找一个尚未添加的平台
  const existingPlatforms = socialLinks.value.map(link => getPlatformValue(link))
  const newPlatform = availablePlatforms.find(p => !existingPlatforms.includes(p.value))
  
  if (newPlatform) {
    // 如果有未添加的平台，使用它
    socialLinks.value.push({
      platform: newPlatform.label,
      url: newPlatform.defaultUrl,
      enabled: true,
      icon: newPlatform.icon,
      class: newPlatform.class
    })
  } else {
    // 否则使用自定义平台
    socialLinks.value.push({
      platform: '自定义',
      url: 'https://',
      enabled: true,
      icon: 'fab fa-globe',
      class: 'custom-icon'
    })
  }
  markAsChanged()
}

// 移除链接
const removeLink = (index: number) => {
  socialLinks.value.splice(index, 1)
  markAsChanged()
}

// 提取数据的函数
const extractBlockData = () => {
  if (props.blockElement) {
    // 提取链接信息
    const links = props.blockElement.querySelectorAll('.social-icon')
    
    if (links.length > 0) {
      socialLinks.value = []
      
      links.forEach((link, index) => {
        if (link instanceof HTMLAnchorElement) {
          const title = link.title || `Platform ${index + 1}`
          const url = link.href || 'https://'
          const enabled = !link.classList.contains('disabled')
          
          // 提取链接样式类（比如facebook-icon等）
          let cssClass = 'custom-icon'
          link.classList.forEach(cls => {
            if (cls.includes('-icon') && cls !== 'social-icon') {
              cssClass = cls
            }
          })
          
          // 根据样式类推断平台
          let icon = 'fab fa-globe'
          
          // 根据样式类或标题找到对应的平台
          const matchedPlatform = availablePlatforms.find(p => 
            p.class === cssClass || 
            p.label === title
          )
          
          // 如果找到匹配的平台，使用平台定义的图标
          if (matchedPlatform) {
            icon = matchedPlatform.icon
          } else {
            // 尝试从iconElement中提取
            const iconElement = link.querySelector('i')
            if (iconElement) {
              const classes = Array.from(iconElement.classList)
              const iconClass = classes.filter(cls => cls.startsWith('fa-')).join(' ')
              if (iconClass) {
                icon = (iconClass.includes('fab ') || iconClass.includes('fas ')) 
                  ? iconClass 
                  : 'fab ' + iconClass
              }
            }
          }
          
          if (matchedPlatform) {
            // 如果找到匹配的平台，使用平台信息
            socialLinks.value.push({
              platform: matchedPlatform.label,
              url,
              enabled,
              icon: matchedPlatform.icon,
              class: matchedPlatform.class
            })
          } else {
            // 否则使用提取的信息
            socialLinks.value.push({
              platform: title,
              url,
              enabled,
              icon: icon || 'fab fa-globe',
              class: cssClass || 'custom-icon'
            })
          }
        }
      })
    }
    
    // 提取样式信息
    const iconSizeClass = props.blockElement.querySelector('.social-icons-row')?.classList
    if (iconSizeClass && iconSizeClass.contains('icon-sm')) {
      iconSize.value = 'sm'
    } else if (iconSizeClass && iconSizeClass.contains('icon-lg')) {
      iconSize.value = 'lg'
    } else {
      iconSize.value = 'md'
    }
    
    // 检查样式
    const firstIcon = props.blockElement.querySelector('.social-icon')
    if (firstIcon && firstIcon.classList.contains('square')) {
      iconStyle.value = 'square'
    } else {
      iconStyle.value = 'circle'
    }
    
    // 检查排列
    if (props.blockElement.querySelector('.social-icons-column')) {
      iconArrangement.value = 'vertical'
    } else {
      iconArrangement.value = 'horizontal'
    }
  } else {
    // 如果没有现有元素，使用默认配置
    socialLinks.value = [...defaultSocialLinks]
    iconSize.value = 'md'
    iconStyle.value = 'circle'
    iconArrangement.value = 'horizontal'
  }

  // 重置更改状态
  isChanged.value = false
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue) => {
  extractBlockData()
}, { immediate: true })

// 修改 onMounted 钩子
onMounted(() => {
  // 初始化时标记为未更改
  isChanged.value = false
})

// 应用更改
const applyChanges = () => {
  // 生成更新后的HTML
  const sizeClass = iconSize.value === 'sm' ? 'icon-sm' : (iconSize.value === 'lg' ? 'icon-lg' : '')
  const arrangeClass = iconArrangement.value === 'vertical' ? 'social-icons-column' : 'social-icons-row'
  
  let linksHtml = ''
  socialLinks.value.forEach(link => {
    if (link.enabled) {
      linksHtml += `
        <a href="${link.url}" class="social-icon ${link.class} ${iconStyle.value}" title="${link.platform}" target="_blank">
          <i class="${link.icon}"></i>
        </a>
      `
    }
  })
  
  const updatedHtml = `
    <div class="social-flow-container">
      <div class="social-icons-wrapper">
        <div class="${arrangeClass} ${sizeClass}">
          ${linksHtml}
        </div>
      </div>
    </div>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
    .social-flow-container {
      display: flex;
      justify-content: center;
      padding: 15px 0;
    }
    .social-icons-wrapper {
      display: inline-flex;
      align-items: center;
    }
    .social-icons-row {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      justify-content: center;
    }
    .social-icons-column {
      display: flex;
      flex-direction: column;
      gap: 15px;
      align-items: center;
    }
    .social-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      color: white;
      transition: all 0.3s ease;
    }
    .icon-sm .social-icon {
      width: 32px;
      height: 32px;
    }
    .icon-lg .social-icon {
      width: 48px;
      height: 48px;
    }
    .social-icon.square {
      border-radius: 4px;
    }
    .facebook-icon {
      background-color: #1877F2;
    }
    .twitter-icon, .twitterx-icon {
      background-color: #1DA1F2;
    }
    .instagram-icon {
      background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D);
    }
    .linkedin-icon {
      background-color: #0A66C2;
    }
    .youtube-icon {
      background-color: #FF0000;
    }
    .pinterest-icon {
      background-color: #E60023;
    }
    .tiktok-icon {
      background-color: #000000;
    }
    .wechat-icon {
      background-color: #07C160;
    }
    .weibo-icon {
      background-color: #DF2029;
    }
    .github-icon {
      background-color: #171515;
    }
    .qq-icon {
      background-color: #12B7F5;
    }
    .telegram-icon {
      background-color: #0088CC;
    }
    .whatsapp-icon {
      background-color: #25D366;
    }
    .custom-icon {
      background-color: #6c757d;
    }
    .social-icon:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 10px rgba(0,0,0,0.2);
    }
    </style>
  `
  
  // 触发更新事件
  emit('update-block', { html: updatedHtml })
  
  // 重置更改状态
  isChanged.value = false
  
  ElMessage.success('社交媒体模块已更新')
}
</script>

<style lang="scss" scoped>
.edit-section {
  padding: 15px 0;
}

.social-link-item {
  margin-bottom: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.social-link-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.social-link-title-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.social-link-title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.social-icon-preview {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  margin-right: 10px;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #333;
  
  &.circle {
    border-radius: 50%;
  }
  
  &.square {
    border-radius: 4px;
  }
  
  &.rounded {
    border-radius: 8px;
  }
  
  &.facebook-icon {
    background-color: #1877F2;
    color: #fff;
  }
  
  &.twitter-icon {
    background-color: #1DA1F2;
    color: #fff;
  }
  
  &.instagram-icon {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    color: #fff;
  }
  
  &.linkedin-icon {
    background-color: #0A66C2;
    color: #fff;
  }
  
  &.youtube-icon {
    background-color: #FF0000;
    color: #fff;
  }
  
  &.pinterest-icon {
    background-color: #E60023;
    color: #fff;
  }
  
  &.tiktok-icon {
    background-color: #000000;
    color: #fff;
  }
  
  &.wechat-icon {
    background-color: #07C160;
    color: #fff;
  }
  
  &.weibo-icon {
    background-color: #DF2029;
    color: #fff;
  }
  
  &.github-icon {
    background-color: #171515;
    color: #fff;
  }
  
  &.qq-icon {
    background-color: #12B7F5;
    color: #fff;
  }
  
  &.telegram-icon {
    background-color: #0088CC;
    color: #fff;
  }
  
  &.whatsapp-icon {
    background-color: #25D366;
    color: #fff;
  }
}

.platform-icon-preview {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  
  &.facebook-icon {
    background-color: #1877F2;
    color: #fff;
  }
  
  &.twitter-icon {
    background-color: #1DA1F2;
    color: #fff;
  }
  
  &.instagram-icon {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    color: #fff;
  }
  
  &.linkedin-icon {
    background-color: #0A66C2;
    color: #fff;
  }
  
  &.youtube-icon {
    background-color: #FF0000;
    color: #fff;
  }
  
  &.pinterest-icon {
    background-color: #E60023;
    color: #fff;
  }
  
  &.tiktok-icon {
    background-color: #000000;
    color: #fff;
  }
  
  &.wechat-icon {
    background-color: #07C160;
    color: #fff;
  }
  
  &.weibo-icon {
    background-color: #DF2029;
    color: #fff;
  }
  
  &.github-icon {
    background-color: #171515;
    color: #fff;
  }
  
  &.qq-icon {
    background-color: #12B7F5;
    color: #fff;
  }
  
  &.telegram-icon {
    background-color: #0088CC;
    color: #fff;
  }
  
  &.whatsapp-icon {
    background-color: #25D366;
    color: #fff;
  }
}

.social-icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: white;
  font-size: 14px;
}

.add-link-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
}

.color-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: 5px;
}

.color-option {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 50%;
  color: white;
  
  &.active {
    border-color: #409EFF;
    transform: scale(1.1);
  }
}

.facebook-icon {
  background-color: #1877F2;
}

.twitter-icon {
  background-color: #1DA1F2;
}

.instagram-icon {
  background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D);
}

.linkedin-icon {
  background-color: #0A66C2;
}

.youtube-icon {
  background-color: #FF0000;
}

.custom-icon {
  background-color: #6c757d;
}

.pinterest-icon {
  background-color: #E60023;
}

.tiktok-icon {
  background-color: #000000;
}

.wechat-icon {
  background-color: #07C160;
}

.weibo-icon {
  background-color: #DF2029;
}
</style> 