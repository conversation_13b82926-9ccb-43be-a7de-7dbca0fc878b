<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top">
          <!-- 合作伙伴列表 -->
          <el-form-item label="合作伙伴">
            <div v-for="(partner, index) in partners" :key="index" class="partner-item">
              <div class="partner-item-header">
                <span>合作伙伴 #{{ index + 1 }}</span>
                <el-button type="danger" link @click="removePartner(index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
              <el-form-item label="Logo URL">
                <el-input v-model="partner.logo" @input="updateContent" />
              </el-form-item>
              <el-form-item label="替代文本">
                <el-input v-model="partner.alt" @input="updateContent" />
              </el-form-item>
            </div>
            <el-button type="primary" @click="addPartner">添加合作伙伴</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="样式" name="style">
        <el-form label-position="top">
          <!-- Logo 尺寸 -->
          <el-form-item label="Logo 尺寸">
            <el-input-number v-model="logoHeight" :min="20" :max="100" @change="updateContent" />
            <span class="unit">px</span>
          </el-form-item>

          <!-- 间距设置 -->
          <el-form-item label="Logo 间距">
            <el-input-number v-model="logoGap" :min="0" :max="50" @change="updateContent" />
            <span class="unit">px</span>
          </el-form-item>

          <!-- 透明度设置 -->
          <el-form-item label="默认透明度">
            <el-slider v-model="opacity" :min="0" :max="100" :step="1" @change="updateContent" />
          </el-form-item>

          <!-- 响应式布局设置 -->
          <el-form-item label="移动端 Logo 尺寸">
            <el-input-number v-model="mobileLogoHeight" :min="15" :max="80" @change="updateContent" />
            <span class="unit">px</span>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Delete } from '@element-plus/icons-vue'

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    required: true
  }
})

const emit = defineEmits(['update-block'])

const activeTab = ref('content')
const partners = ref<Array<{ logo: string; alt: string }>>([])
const logoHeight = ref(30)
const logoGap = ref(16)
const opacity = ref(70)
const mobileLogoHeight = ref(25)

// 初始化数据
onMounted(() => {
  if (props.blockElement) {
    // 获取所有合作伙伴 logo
    const logos = props.blockElement.querySelectorAll('.partner-logo')
    partners.value = Array.from(logos).map((logo: Element) => ({
      logo: (logo as HTMLImageElement).src,
      alt: (logo as HTMLImageElement).alt || 'Partner'
    }))

    // 获取样式设置
    const style = window.getComputedStyle(logos[0])
    logoHeight.value = parseInt(style.height) || 30
    opacity.value = parseFloat(style.opacity) * 100 || 70

    // 获取间距
    const container = props.blockElement.querySelector('.d-flex')
    if (container) {
      const gap = window.getComputedStyle(container).gap
      logoGap.value = parseInt(gap) || 16
    }
  }
})

// 添加合作伙伴
const addPartner = () => {
  partners.value.push({
    logo: 'https://new-bwms.bingo-test.com/tiptap/lyraxionics-light.webp',
    alt: 'Partner'
  })
  updateContent()
}

// 移除合作伙伴
const removePartner = (index: number) => {
  partners.value.splice(index, 1)
  updateContent()
}

// 更新内容
const updateContent = () => {
  const html = `
    <div data-bs-component="partners" class="py-4">
      <div class="container">
        <div class="flex-wrap d-flex justify-content-center align-items-center" style="gap: ${logoGap.value}px">
          ${partners.value.map(partner => `
            <img src="${partner.logo}" alt="${partner.alt}" class="partner-logo">
          `).join('')}
        </div>
      </div>
    </div>

    <style>
    .partner-logo {
      height: ${logoHeight.value}px;
      opacity: ${opacity.value / 100};
      transition: opacity 0.3s ease;
    }

    .partner-logo:hover {
      opacity: 1;
    }

    @media (max-width: 767.98px) {
      .partner-logo {
        height: ${mobileLogoHeight.value}px;
      }
    }
    </style>
  `

  emit('update-block', { html })
}
</script>

<style scoped>
.edit-section {
  padding: 16px;
}

.partner-item {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.partner-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.unit {
  margin-left: 8px;
  color: #909399;
}
</style> 