<template>
  <div class="edit-section">
    <el-form label-position="top">
      <div class="form-fields">
        <div v-for="(field, index) in formFields" :key="index" class="form-field-item">
          <div class="item-header">
            <span class="item-title">{{ getFormFieldTypeName(field.type) }} {{ index + 1 }}</span>
            <el-button class="remove-button" @click="removeFormField(index)" v-if="formFields.length > 1" type="danger" size="small" :icon="Delete">
            </el-button>
          </div>
          
          <el-form-item label="字段类型">
            <el-select v-model="field.type" @change="updateFormContent" style="width: 100%">
              <el-option label="文本框" value="text" />
              <el-option label="多行文本" value="textarea" />
              <el-option label="电子邮件" value="email" />
              <el-option label="密码" value="password" />
              <el-option label="下拉菜单" value="select" />
              <el-option label="复选框" value="checkbox" />
              <el-option label="单选按钮" value="radio" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="标签文本">
            <el-input v-model="field.label" @input="updateFormContent" />
          </el-form-item>
          
          <el-form-item label="占位文本">
            <el-input v-model="field.placeholder" @input="updateFormContent" />
          </el-form-item>
          
          <el-form-item v-if="['select', 'checkbox', 'radio'].includes(field.type)" label="选项 (用逗号分隔)">
            <el-input v-model="field.options" @input="updateFormContent" placeholder="选项1,选项2,选项3" />
          </el-form-item>
          
          <el-form-item label="是否必填">
            <el-switch v-model="field.required" @change="updateFormContent" />
          </el-form-item>
        </div>
        
        <el-button class="add-button" @click="addFormField" type="primary" plain>添加表单字段</el-button>
      </div>
      
      <el-form-item label="提交按钮文本">
        <el-input v-model="formSubmitText" @input="updateFormContent" />
      </el-form-item>
      
      <el-form-item label="提交按钮样式">
        <el-select v-model="formSubmitStyle" @change="updateFormContent" style="width: 100%">
          <el-option label="主要" value="primary" />
          <el-option label="次要" value="secondary" />
          <el-option label="成功" value="success" />
          <el-option label="危险" value="danger" />
          <el-option label="警告" value="warning" />
          <el-option label="信息" value="info" />
          <el-option label="浅色" value="light" />
          <el-option label="深色" value="dark" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions } from 'vue'
import { Delete } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'FormEditor'
})

interface FormField {
  type: string
  label: string
  placeholder: string
  required: boolean
  options: string
}

const props = defineProps({
  blockElement: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update-block'])

// 表单数据
const formFields = ref<FormField[]>([
  {
    type: 'text',
    label: '姓名',
    placeholder: '请输入您的姓名',
    required: true,
    options: ''
  },
  {
    type: 'email',
    label: '邮箱',
    placeholder: '请输入您的邮箱',
    required: true,
    options: ''
  },
  {
    type: 'textarea',
    label: '留言',
    placeholder: '请输入您的留言',
    required: false,
    options: ''
  }
])

const formSubmitText = ref('提交')
const formSubmitStyle = ref('primary')

// 组件挂载时从现有块元素中提取表单数据
onMounted(() => {
  if (props.blockElement) {
    const formEl = props.blockElement as HTMLElement
    
    // 获取所有表单字段
    const formGroups = formEl.querySelectorAll('.mb-3, .form-group')
    if (formGroups.length) {
      formFields.value = []
      
      formGroups.forEach((group: Element) => {
        if (group.querySelector('button[type="submit"]')) {
          // 这是提交按钮，获取文本和样式
          const submitBtn = group.querySelector('button[type="submit"]')
          if (submitBtn) {
            formSubmitText.value = submitBtn.textContent || '提交'
            
            // 获取按钮样式
            const btnStyleClass = Array.from(submitBtn.classList)
              .find((cls: string) => cls.startsWith('btn-'))
            if (btnStyleClass) {
              formSubmitStyle.value = btnStyleClass.replace('btn-', '')
            }
          }
          return
        }
        
        // 普通表单字段
        const label = group.querySelector('label')
        const input = group.querySelector('input, textarea, select')
        
        if (label && input) {
          const field: FormField = {
            type: 'text',
            label: label.textContent || '',
            placeholder: '',
            required: false,
            options: ''
          }
          
          // 获取字段类型
          if (input.tagName === 'TEXTAREA') {
            field.type = 'textarea'
          } else if (input.tagName === 'SELECT') {
            field.type = 'select'
            
            // 获取选项
            const options = Array.from(input.querySelectorAll('option'))
              .map(opt => opt.textContent || '')
              .filter(text => text)
            
            field.options = options.join(',')
          } else if (input instanceof HTMLInputElement) {
            field.type = input.type || 'text'
            field.placeholder = input.placeholder || ''
            field.required = input.required
            
            // 对于 checkbox 和 radio，获取选项
            if (input.type === 'checkbox' || input.type === 'radio') {
              const allInputs = Array.from(group.querySelectorAll('input'))
              const labels = Array.from(group.querySelectorAll('.form-check-label'))
                .map(lbl => lbl.textContent || '')
                .filter(text => text)
              
              field.options = labels.join(',')
            }
          }
          
          formFields.value.push(field)
        }
      })
    }
  }
})

// 表单相关方法
const getFormFieldTypeName = (type: string): string => {
  switch (type) {
    case 'text': return '文本框'
    case 'textarea': return '多行文本'
    case 'email': return '电子邮件'
    case 'password': return '密码'
    case 'select': return '下拉菜单'
    case 'checkbox': return '复选框'
    case 'radio': return '单选按钮'
    default: return '表单字段'
  }
}

const addFormField = () => {
  formFields.value.push({
    type: 'text',
    label: '新字段',
    placeholder: '请输入',
    required: false,
    options: ''
  })
  updateFormContent()
}

const removeFormField = (index: number) => {
  formFields.value.splice(index, 1)
  updateFormContent()
}

const updateFormContent = () => {
  let fieldsHtml = ''
  
  // 构建表单字段HTML
  formFields.value.forEach(field => {
    let fieldHtml = ''
    
    if (field.type === 'textarea') {
      fieldHtml = `
        <div class="mb-3">
          <label for="${field.label.toLowerCase().replace(/\s+/g, '-')}" class="form-label">${field.label}</label>
          <textarea class="form-control" id="${field.label.toLowerCase().replace(/\s+/g, '-')}" rows="3" placeholder="${field.placeholder}" ${field.required ? 'required' : ''}></textarea>
        </div>
      `
    } else if (field.type === 'select') {
      const options = field.options.split(',').map(opt => 
        `<option>${opt.trim()}</option>`
      ).join('')
      
      fieldHtml = `
        <div class="mb-3">
          <label for="${field.label.toLowerCase().replace(/\s+/g, '-')}" class="form-label">${field.label}</label>
          <select class="form-select" id="${field.label.toLowerCase().replace(/\s+/g, '-')}" ${field.required ? 'required' : ''}>
            ${options}
          </select>
        </div>
      `
    } else if (field.type === 'checkbox' || field.type === 'radio') {
      const options = field.options.split(',')
      let optionsHtml = ''
      
      options.forEach((opt, i) => {
        optionsHtml += `
          <div class="form-check">
            <input class="form-check-input" type="${field.type}" name="${field.label.toLowerCase().replace(/\s+/g, '-')}" id="${field.label.toLowerCase().replace(/\s+/g, '-')}-${i}" ${i === 0 && field.required ? 'required' : ''}>
            <label class="form-check-label" for="${field.label.toLowerCase().replace(/\s+/g, '-')}-${i}">
              ${opt.trim()}
            </label>
          </div>
        `
      })
      
      fieldHtml = `
        <div class="mb-3">
          <label class="form-label">${field.label}</label>
          ${optionsHtml}
        </div>
      `
    } else {
      fieldHtml = `
        <div class="mb-3">
          <label for="${field.label.toLowerCase().replace(/\s+/g, '-')}" class="form-label">${field.label}</label>
          <input type="${field.type}" class="form-control" id="${field.label.toLowerCase().replace(/\s+/g, '-')}" placeholder="${field.placeholder}" ${field.required ? 'required' : ''}>
        </div>
      `
    }
    
    fieldsHtml += fieldHtml
  })
  
  // 添加提交按钮
  fieldsHtml += `
    <div class="mb-3">
      <button type="submit" class="btn btn-${formSubmitStyle.value}">${formSubmitText.value}</button>
    </div>
  `
  
  const html = `
    <div data-bs-component="form" class="form-container">
      <form>
        ${fieldsHtml}
      </form>
    </div>
  `
  
  emit('update-block', { html })
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
}

.form-field-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #f8f9fa;
  margin-bottom: 15px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.item-title {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
}

.add-button {
  margin-top: 10px;
  width: 100%;
}
</style> 