<template>
  <div class="tiptap-editor-container">
    <div class="editor-wrapper" :class="{ 'fullscreen-container': isFullscreen }">
      <div class="toolbar-container" v-if="!isFullscreen">
        <div class="button-group">
          <button @click="handleAiFormat" class="ai-button" title="AI 排版助手">AI 排版</button>
          <button @click="editor?.chain().focus().toggleBold().run()" :disabled="!editor?.can().chain().focus().toggleBold().run()" :class="{ 'is-active': editor?.isActive('bold') }">粗体</button>
          <button @click="editor?.chain().focus().toggleItalic().run()" :disabled="!editor?.can().chain().focus().toggleItalic().run()" :class="{ 'is-active': editor?.isActive('italic') }">
            斜体
          </button>
          <button @click="editor?.chain().focus().toggleStrike().run()" :disabled="!editor?.can().chain().focus().toggleStrike().run()" :class="{ 'is-active': editor?.isActive('strike') }">
            删除线
          </button>
          <button @click="editor?.chain().focus().toggleCode().run()" :disabled="!editor?.can().chain().focus().toggleCode().run()" :class="{ 'is-active': editor?.isActive('code') }">代码</button>
          <button @click="editor?.chain().focus().unsetAllMarks().run()">清除标记</button>
          <button @click="editor?.chain().focus().clearNodes().run()">清除节点</button>
          <button @click="editor?.chain().focus().setParagraph().run()" :class="{ 'is-active': editor?.isActive('paragraph') }">段落</button>
          <button @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 1 }) }">H1</button>
          <button @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 2 }) }">H2</button>
          <button @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 3 }) }">H3</button>
          <button @click="editor?.chain().focus().toggleHeading({ level: 4 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 4 }) }">H4</button>
          <button @click="editor?.chain().focus().toggleHeading({ level: 5 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 5 }) }">H5</button>
          <button @click="editor?.chain().focus().toggleHeading({ level: 6 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 6 }) }">H6</button>
          <button @click="editor?.chain().focus().toggleBulletList().run()" :class="{ 'is-active': editor?.isActive('bulletList') }">无序列表</button>
          <button @click="editor?.chain().focus().toggleOrderedList().run()" :class="{ 'is-active': editor?.isActive('orderedList') }">有序列表</button>
          <button @click="editor?.chain().focus().toggleCodeBlock().run()" :class="{ 'is-active': editor?.isActive('codeBlock') }">代码块</button>
          <button @click="editor?.chain().focus().toggleBlockquote().run()" :class="{ 'is-active': editor?.isActive('blockquote') }">引用块</button>
          <button @click="editor?.chain().focus().setHorizontalRule().run()">水平分割线</button>
          <button @click="editor?.chain().focus().setHardBreak().run()">硬换行</button>
          <button @click="editor?.chain().focus().undo().run()" :disabled="!editor?.can().chain().focus().undo().run()">撤销</button>
          <button @click="editor?.chain().focus().redo().run()" :disabled="!editor?.can().chain().focus().redo().run()">重做</button>
          <button @click="editor?.chain().focus().setColor('#958DF1').run()" :class="{ 'is-active': editor?.isActive('textStyle', { color: '#958DF1' }) }">紫色</button>
          <button @click="toggleFullscreen" class="fullscreen-button">
            {{ isFullscreen ? '退出全屏' : '全屏编辑' }}
          </button>
          <button @click="insertTable">插入表格</button>
          <button @click="addColumnBefore" :disabled="!editor?.can().addColumnBefore()">前插入列</button>
          <button @click="addColumnAfter" :disabled="!editor?.can().addColumnAfter()">后插入列</button>
          <button @click="deleteColumn" :disabled="!editor?.can().deleteColumn()">删除列</button>
          <button @click="addRowBefore" :disabled="!editor?.can().addRowBefore()">上插入行</button>
          <button @click="addRowAfter" :disabled="!editor?.can().addRowAfter()">下插入行</button>
          <button @click="deleteRow" :disabled="!editor?.can().deleteRow()">删除行</button>
          <button @click="deleteTable" :disabled="!editor?.can().deleteTable()">删除表格</button>
          <button @click="mergeCells" :disabled="!editor?.can().mergeCells()">合并单元格</button>
          <button @click="splitCell" :disabled="!editor?.can().splitCell()">拆分单元格</button>
          <button @click="toggleHeaderCell" :disabled="!editor?.can().toggleHeaderCell()">切换表头</button>
          <button @click="showBlockSelector" title="插入区块" class="block-button">插入区块</button>
          <button @click="blockSelectorVisible = true" title="块添加" class="block-button">块添加</button>
        </div>
      </div>

      <div class="editor-content-wrapper">
        <div class="editor-content-isolated">
          <div v-if="editor && !isFullscreen">
            <editor-content :editor="editor" class="editor-content editor-content-area bootstrap-enabled" />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 使用 Teleport 将全屏编辑器直接挂载到 body -->
  <teleport to="body" v-if="isFullscreen">
    <div class="editor-fullscreen-wrapper">
      <div class="toolbar-container">
        <div class="button-group">
          <button @click="handleAiFormat" class="ai-button" title="AI 排版助手">AI 排版</button>
          <button @click="editor?.chain().focus().toggleBold().run()" :disabled="!editor?.can().chain().focus().toggleBold().run()" :class="{ 'is-active': editor?.isActive('bold') }">粗体</button>
          <button @click="editor?.chain().focus().toggleItalic().run()" :disabled="!editor?.can().chain().focus().toggleItalic().run()" :class="{ 'is-active': editor?.isActive('italic') }">
            斜体
          </button>
          <button @click="editor?.chain().focus().toggleStrike().run()" :disabled="!editor?.can().chain().focus().toggleStrike().run()" :class="{ 'is-active': editor?.isActive('strike') }">
            删除线
          </button>
          <button @click="editor?.chain().focus().toggleCode().run()" :disabled="!editor?.can().chain().focus().toggleCode().run()" :class="{ 'is-active': editor?.isActive('code') }">代码</button>
          <button @click="editor?.chain().focus().unsetAllMarks().run()">清除标记</button>
          <button @click="editor?.chain().focus().clearNodes().run()">清除节点</button>
          <button @click="editor?.chain().focus().setParagraph().run()" :class="{ 'is-active': editor?.isActive('paragraph') }">段落</button>
          <button @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 1 }) }">H1</button>
          <button @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 2 }) }">H2</button>
          <button @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 3 }) }">H3</button>
          <button @click="editor?.chain().focus().toggleHeading({ level: 4 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 4 }) }">H4</button>
          <button @click="editor?.chain().focus().toggleHeading({ level: 5 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 5 }) }">H5</button>
          <button @click="editor?.chain().focus().toggleHeading({ level: 6 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 6 }) }">H6</button>
          <button @click="editor?.chain().focus().toggleBulletList().run()" :class="{ 'is-active': editor?.isActive('bulletList') }">无序列表</button>
          <button @click="editor?.chain().focus().toggleOrderedList().run()" :class="{ 'is-active': editor?.isActive('orderedList') }">有序列表</button>
          <button @click="editor?.chain().focus().toggleCodeBlock().run()" :class="{ 'is-active': editor?.isActive('codeBlock') }">代码块</button>
          <button @click="editor?.chain().focus().toggleBlockquote().run()" :class="{ 'is-active': editor?.isActive('blockquote') }">引用块</button>
          <button @click="editor?.chain().focus().setHorizontalRule().run()">水平分割线</button>
          <button @click="editor?.chain().focus().setHardBreak().run()">硬换行</button>
          <button @click="editor?.chain().focus().undo().run()" :disabled="!editor?.can().chain().focus().undo().run()">撤销</button>
          <button @click="editor?.chain().focus().redo().run()" :disabled="!editor?.can().chain().focus().redo().run()">重做</button>
          <button @click="editor?.chain().focus().setColor('#958DF1').run()" :class="{ 'is-active': editor?.isActive('textStyle', { color: '#958DF1' }) }">紫色</button>
          <button @click="toggleFullscreen" class="fullscreen-button">
            {{ isFullscreen ? '退出全屏' : '全屏编辑' }}
          </button>
          <button @click="insertTable">插入表格</button>
          <button @click="addColumnBefore" :disabled="!editor?.can().addColumnBefore()">前插入列</button>
          <button @click="addColumnAfter" :disabled="!editor?.can().addColumnAfter()">后插入列</button>
          <button @click="deleteColumn" :disabled="!editor?.can().deleteColumn()">删除列</button>
          <button @click="addRowBefore" :disabled="!editor?.can().addRowBefore()">上插入行</button>
          <button @click="addRowAfter" :disabled="!editor?.can().addRowAfter()">下插入行</button>
          <button @click="deleteRow" :disabled="!editor?.can().deleteRow()">删除行</button>
          <button @click="deleteTable" :disabled="!editor?.can().deleteTable()">删除表格</button>
          <button @click="mergeCells" :disabled="!editor?.can().mergeCells()">合并单元格</button>
          <button @click="splitCell" :disabled="!editor?.can().splitCell()">拆分单元格</button>
          <button @click="toggleHeaderCell" :disabled="!editor?.can().toggleHeaderCell()">切换表头</button>
          <button @click="showBlockSelector" title="插入区块" class="block-button">页面编辑</button>
          <!-- <button @click="blockSelectorVisible = true" title="块添加" class="block-button">插入块</button> -->

        </div>
      </div>
      <div class="editor-fullscreen-content">
        <editor-content :editor="editor" class="editor-content editor-content-area bootstrap-enabled" />
      </div>
    </div>
  </teleport>

  <div v-if="aiMenuVisible" class="ai-dropdown-menu">
    <div class="ai-menu-header">AI 排版助手</div>
    <div class="ai-menu-item" @click="processAIRequest('summarize')">智能摘要</div>
    <div class="ai-menu-item" @click="processAIRequest('improve')">优化内容</div>
    <div class="ai-menu-item" @click="processAIRequest('rewrite')">重写内容</div>
    <div class="ai-menu-item" @click="processAIRequest('correct')">语法校正</div>
    <div class="ai-menu-item" @click="showCustomPromptDialog">自定义提示词...</div>
  </div>

  <div v-if="customPromptDialogVisible" class="custom-prompt-dialog">
    <div class="custom-prompt-header">
      <h3>自定义 AI 提示词</h3>
      <button class="close-button" @click="customPromptDialogVisible = false">×</button>
    </div>
    <div class="custom-prompt-body">
      <textarea v-model="customPrompt" placeholder="请输入自定义提示词..." class="custom-prompt-textarea"></textarea>
    </div>
    <div class="custom-prompt-footer">
      <button @click="customPromptDialogVisible = false" class="cancel-button">取消</button>
      <button @click="processCustomAIPrompt" class="submit-button">提交</button>
    </div>
  </div>

  <div v-if="isAIProcessing" class="ai-processing-overlay" :class="{ fullscreen: isFullscreen }">
    <div class="ai-processing-content">
      <div class="ai-loader"></div>
      <div class="ai-processing-text">AI 正在处理中...</div>
    </div>
  </div>

  <BlockSelector :editor="editor" :visible="blockSelectorVisible" @block-selected="handleBlockSelected" @close="blockSelectorVisible = false" />
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick, inject, defineProps, defineEmits, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Color } from '@tiptap/extension-color'
import ListItem from '@tiptap/extension-list-item'
import TextStyle from '@tiptap/extension-text-style'
import StarterKit from '@tiptap/starter-kit'

import BubbleMenu from '@tiptap/extension-bubble-menu'
import FloatingMenu from '@tiptap/extension-floating-menu'
import Underline from '@tiptap/extension-underline'
import Strike from '@tiptap/extension-strike'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import TextAlign from '@tiptap/extension-text-align'
import Placeholder from '@tiptap/extension-placeholder'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import TaskList from '@tiptap/extension-task-list'
import TaskItem from '@tiptap/extension-task-item'
import Highlight from '@tiptap/extension-highlight'
import FontFamily from '@tiptap/extension-font-family'
import Subscript from '@tiptap/extension-subscript'
import Superscript from '@tiptap/extension-superscript'
import Typography from '@tiptap/extension-typography'
import Indent from '@weiruo/tiptap-extension-indent'
import { Editor, EditorContent, getMarkRange } from '@tiptap/vue-3'
import { ElMessage, ElLoading } from 'element-plus'
import BlockSelector from './components/BlockSelector.vue'

import axios from 'axios'
import http from '/admin/support/http'
import { NodeSelection } from 'prosemirror-state'



// 引入自定义区块扩展
import { GridBlock, CtaBlock, ImageTextBlock, HeadlineBlock, BootstrapComponent, RichTextNode, AllowAttributes } from './extensions'

// 在<script>部分顶部添加bootstrap类型声明
declare global {
  interface Window {
    bootstrap: any;
  }
}

// 传入的内容和内容类型
const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  contentType: {
    type: String,
    default: 'html', // 'html', 'markdown', 'text'
  },
  placeholder: {
    type: String,
    default: '请输入内容...',
  },
  modelValue: {
    type: String,
    default: '',
  },
})

const editor = ref<Editor | null>(null)

// 设置 HTML 内容
function setHtmlContent(html: string) {
  if (!editor.value) return

  // 直接设置HTML内容，保留所有样式属性
  editor.value.commands.setContent(html, false)

  // 初始化Bootstrap组件
  nextTick(() => {
    initBootstrapComponents()
  })
}

// 设置纯文本内容
function setTextContent(text: string) {
  if (editor.value) {
    editor.value.commands.setContent(`<p>${text}</p>`)
  }
}

// 监听内容变化，根据内容类型设置编辑器内容
watch(
  [() => props.content, () => props.contentType],
  ([newContent, newContentType]) => {
    if (!editor.value || !newContent) return

    if (newContentType === 'html') {
      // 直接设置 HTML 内容
      setHtmlContent(newContent)
    } else if (newContentType === 'markdown') {
      // 设置为纯文本
      setTextContent(newContent)
    } else {
      // 处理纯文本
      setTextContent(newContent)
    }
  },
  { immediate: true },
)

// 监听modelValue变化
watch(
  () => props.modelValue,
  newValue => {
    if (!editor.value || !newValue || editor.value.getHTML() === newValue) return

    // 设置编辑器内容
    setHtmlContent(newValue)
  },
)

const isFullscreen = ref(false)
const blockSelectorVisible = ref(false)
const aiMenuVisible = ref(false)
const customPromptDialogVisible = ref(false)
const customPrompt = ref('')
const isAIProcessing = ref(false)
const aiMenuPosition = ref({ top: '0px', left: '0px' })
const aiButtonRef = ref<HTMLElement | null>(null)


// 添加 emit 定义
const emit = defineEmits(['update:modelValue', 'change'])

// 监听内容变化并触发更新事件
const updateContent = () => {
  if (editor.value) {
    try {
    const content = editor.value.getHTML()
    emit('update:modelValue', content)
    emit('change', content)
    } catch (error) {
      console.error('获取HTML内容时出错:', error)
    }
  }
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value

  if (isFullscreen.value) {
    // 添加全屏时的背景遮罩
    const backdrop = document.createElement('div')
    backdrop.id = 'editor-fullscreen-backdrop'
    document.body.appendChild(backdrop)

    // 禁止body滚动
    document.body.style.overflow = 'hidden'
  } else {
    // 移除遮罩
    const backdrop = document.getElementById('editor-fullscreen-backdrop')
    if (backdrop) {
      document.body.removeChild(backdrop)
    }

    // 恢复body滚动
    document.body.style.overflow = ''
  }

  nextTick(() => {
    editor.value?.commands.focus()
  })
}

const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && isFullscreen.value) {
    isFullscreen.value = false
  }
}

const insertTable = () => {
  editor.value
    ?.chain()
    .focus()
    .insertTable({
      rows: 3,
      cols: 3,
      withHeaderRow: true,
    })
    .run()
}

const addColumnBefore = () => {
  editor.value?.chain().focus().addColumnBefore().run()
}

const addColumnAfter = () => {
  editor.value?.chain().focus().addColumnAfter().run()
}

const deleteColumn = () => {
  editor.value?.chain().focus().deleteColumn().run()
}

const addRowBefore = () => {
  editor.value?.chain().focus().addRowBefore().run()
}

const addRowAfter = () => {
  editor.value?.chain().focus().addRowAfter().run()
}

const deleteRow = () => {
  editor.value?.chain().focus().deleteRow().run()
}

const deleteTable = () => {
  editor.value?.chain().focus().deleteTable().run()
}

const mergeCells = () => {
  editor.value?.chain().focus().mergeCells().run()
}

const splitCell = () => {
  editor.value?.chain().focus().splitCell().run()
}

const toggleHeaderCell = () => {
  editor.value?.chain().focus().toggleHeaderCell().run()
}

const handleDrop = (event: Event) => {
  const dragEvent = event as DragEvent

  if (!editor.value || !dragEvent.dataTransfer) return

  const files = Array.from(dragEvent.dataTransfer.files)

  files.forEach((file: File) => {
    if (file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = e => {
        if (typeof e.target?.result === 'string') {
          editor.value
            ?.chain()
            .focus()
            .setImage({
              src: e.target.result,
              alt: file.name,
            })
            .run()
        }
      }
      reader.readAsDataURL(file)
    }
  })
}

const handlePaste = (pasteEvent: any) => {
  const event = pasteEvent.event as ClipboardEvent

  if (!editor.value || !event.clipboardData) return

  // 处理粘贴的文件（如截图）
  const files = Array.from(event.clipboardData.files)

  if (files.length > 0) {
    // 如果有文件，优先处理文件
    files.forEach((file: File) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader()
        reader.onload = e => {
          if (typeof e.target?.result === 'string') {
            // 简单插入图片，不添加额外样式
            editor.value
              ?.chain()
              .focus()
              .setImage({ src: e.target.result })
              .run()
          }
        }
        reader.readAsDataURL(file)
      }
    })
    event.preventDefault()
  } else {
    // 检查剪贴板中是否有HTML内容包含图片
    const html = event.clipboardData.getData('text/html')
    if (html && html.includes('<img')) {
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = html

      const images = tempDiv.querySelectorAll('img')
      if (images.length > 0) {
        images.forEach(img => {
          const src = img.getAttribute('src')
          if (src) {
            // 简单插入图片，不添加额外样式
            editor.value
              ?.chain()
              .focus()
              .setImage({ src: src })
              .run()
          }
        })
        event.preventDefault()
      }
    }
  }
}

const showBlockSelector = () => {
  // 修改为路由跳转而不是显示弹窗
  router.push('/editor/page-list')
}

// 更新handleBlockSelected函数，确保内容更新后立即重新初始化Bootstrap组件
const handleBlockSelected = (blockType?: string) => {
  blockSelectorVisible.value = false

  // 如果没有编辑器，不做任何处理
  if (!editor.value) {
    ElMessage.warning('编辑器未初始化');
    return;
  }



  // 延迟处理以确保编辑器内容已更新
  setTimeout(() => {
    editor.value?.commands.focus()

    // 不同类型的块执行不同的处理逻辑
    if (blockType && blockType.startsWith('bootstrap-')) {
      // 对于Bootstrap组件，需要特殊处理，但不重置编辑器
      handleBootstrapContent(blockType);
    } else {
      // 对于非Bootstrap组件，只需要正常更新即可
      nextTick(() => {
        // 手动触发编辑器内容更新
        updateContent();

        // 成功提示
        ElMessage.success('区块已添加到编辑器');
      });
    }
  }, 50);
}

// 处理Bootstrap内容的函数
const handleBootstrapContent = (blockType: string) => {
  // 确保编辑器存在
  if (!editor.value) return;

  // 不再保存和重设整个HTML内容，只初始化Bootstrap组件
  nextTick(() => {
    // 初始化Bootstrap组件
    initBootstrapComponents();

    // 手动触发编辑器内容更新，但不重置内容
    updateContent();

    // 成功提示，显示不同的消息
    switch (blockType) {
      case 'bootstrap-alert':
        ElMessage.success('Bootstrap提示框已添加');
        break;
      case 'bootstrap-card':
        ElMessage.success('Bootstrap卡片已添加');
        break;
      case 'bootstrap-button-group':
        ElMessage.success('Bootstrap按钮组已添加');
        break;
      case 'bootstrap-form':
        ElMessage.success('Bootstrap表单已添加');
        break;
      case 'bootstrap-nav':
        ElMessage.success('Bootstrap导航已添加');
        break;
      default:
        ElMessage.success('Bootstrap组件已添加');
    }
  });
}

// 创建和更新Bootstrap容器的函数
const createOrUpdateBootstrapContainer = (container: Element, forceCreate = false) => {


  // 获取所有Bootstrap组件
  const bootstrapComponents = container.querySelectorAll('[data-bs-component]');

  // 如果没有Bootstrap组件，不需要特殊处理
  if (bootstrapComponents.length === 0) {

    return null;
  }



  // 确保编辑器容器有bootstrap类，这样可以确保Bootstrap样式正确应用
  container.classList.add('bootstrap');

  // 直接处理原始组件，不再创建容器和克隆
  bootstrapComponents.forEach(component => {
    // 获取组件类型
    const componentType = component.getAttribute('data-bs-component');


    // 确保组件有正确的属性
    component.setAttribute('data-bs-processed', 'true');

    // 不添加任何内联样式，让Bootstrap CSS完全控制样式
    // 只添加点击事件监听器
    component.addEventListener('click', (e) => {
      // 阻止事件冒泡，避免触发编辑器的选择
      e.stopPropagation();
    });
  });

  // 手动初始化Bootstrap组件
  if (typeof window !== 'undefined' && window.bootstrap) {
    setTimeout(() => {
      try {
        // 初始化所有tooltips
        const tooltipTriggerList = container.querySelectorAll('[data-bs-toggle="tooltip"]');
        if (tooltipTriggerList.length > 0) {
          Array.from(tooltipTriggerList).forEach(tooltipTriggerEl => {
            new window.bootstrap.Tooltip(tooltipTriggerEl);
          });
        }

        // 初始化所有popovers
        const popoverTriggerList = container.querySelectorAll('[data-bs-toggle="popover"]');
        if (popoverTriggerList.length > 0) {
          Array.from(popoverTriggerList).forEach(popoverTriggerEl => {
            new window.bootstrap.Popover(popoverTriggerEl);
          });
        }

        // 初始化所有可关闭的提示框
        const alertElements = container.querySelectorAll('.alert.alert-dismissible');
        alertElements.forEach(alert => {
          const closeBtn = alert.querySelector('.btn-close');
          if (closeBtn) {
            closeBtn.addEventListener('click', (e) => {
              e.preventDefault();
              e.stopPropagation();
              alert.classList.remove('show');
              setTimeout(() => {
                if (alert.parentNode) {
                  alert.parentNode.removeChild(alert);
                }
              }, 150);
            });
          }
        });
      } catch (error) {
        console.error('初始化Bootstrap组件时出错:', error);
      }
    }, 50);
  }

  return null; // 不再返回容器，因为我们直接处理原始组件
};

// 监听编辑器内容变化，重新初始化Bootstrap组件
const handleEditorContentChange = () => {
  // 延迟执行，确保DOM已更新
  setTimeout(() => {
    initBootstrapComponents();
  }, 100);
};

// 修改initBootstrapComponents函数中的bootstrap相关代码
const initBootstrapComponents = () => {
  nextTick(() => {
    try {
      // 获取所有编辑器容器
      const editorContainers = document.querySelectorAll('.editor-content.bootstrap-enabled');
      if (editorContainers.length === 0) {
        return;
      }
      // 确保Bootstrap CSS已加载
      ensureBootstrapCssLoaded();
      // 为每个编辑器容器处理Bootstrap组件
      editorContainers.forEach(container => {
        createOrUpdateBootstrapContainer(container);
      });

    } catch (error) {
      console.error('初始化Bootstrap组件时出错:', error);
    }
  });
};

// 确保Bootstrap CSS已加载
const ensureBootstrapCssLoaded = () => {
  // 检查是否已加载Bootstrap CSS
  const bootstrapCssExists = document.querySelector('link[href*="bootstrap"]');
  if (!bootstrapCssExists) {
    console.log('加载Bootstrap CSS');
    const cssLink = document.createElement('link');
    cssLink.rel = 'stylesheet';
    cssLink.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css';
    cssLink.integrity = 'sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3';
    cssLink.crossOrigin = 'anonymous';
    document.head.appendChild(cssLink);
  }

  // 检查是否已加载Bootstrap JS
  if (typeof window.bootstrap === 'undefined') {
    console.log('加载Bootstrap JS');
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
    script.integrity = 'sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p';
    script.crossOrigin = 'anonymous';
    document.head.appendChild(script);
  }
};

// 注入控制AIAside显示的方法
const toggleAiLayout = inject('toggleAiLayout', () => {})

// AI格式化处理函数
const handleAiFormat = () => {
  // 直接调用父组件提供的方法显示AIAside
  toggleAiLayout()
}

const closeAIMenu = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  const isAIButton = target.closest('.ai-button')
  const isAIMenu = target.closest('.ai-dropdown-menu')

  if (!isAIButton && !isAIMenu) {
    aiMenuVisible.value = false
    document.removeEventListener('click', closeAIMenu)
  }
}

const processAIRequest = async (action: string, prompt?: string) => {
  aiMenuVisible.value = false
  customPromptDialogVisible.value = false

  if (!editor.value) {
    ElMessage.warning('编辑器未初始化')
    return
  }

  // 获取处理内容
  let content = ''
  let contentType = 'text'

  const selection = editor.value.state.selection

  if (!selection.empty) {
    // 文本选择
    const { from, to } = selection
    content = editor.value.state.doc.textBetween(from, to, ' ')
    contentType = 'text'
  } else {
    ElMessage.warning('请先选择需要处理的文本内容')
    return
  }

  if (!content || content.trim() === '') {
    ElMessage.warning('所选内容为空，请重新选择')
    return
  }

  try {
    // 构建请求参数
    const requestData = {
      content: content,
      contentType: contentType, // 新增，标识内容类型
      style: {
        action: action,
        // 只有当action是自定义(custom)时才设置prompt
        ...(action === 'custom' && prompt ? { prompt } : {}),
      },
      lang: localStorage.getItem('activeName') || 'zh_CN',
    }

    console.log('发送的请求数据:', requestData) // 调试用

    isAIProcessing.value = true

    // 调用接口 - 可能需要根据内容类型使用不同的端点
    const endpoint = '/seo/format/block'

    const response = await http.post(endpoint, requestData)
    console.log('AI 处理结果:', response.data)

    if (response.data) {
      // 处理文本响应 - 替换选中的内容
      const { from, to } = selection
      editor.value.chain().focus().deleteRange({ from, to }).run()
      editor.value.chain().focus().insertContent(response.data.data.content).run()

      ElMessage.success('AI 处理完成')
    } else {
      throw new Error(response.data?.message || '处理失败')
    }
  } catch (error) {
    console.error('AI 处理错误:', error)
    ElMessage.error(`AI 处理失败: ${error instanceof Error ? error.message : '未知错误'}`)
  } finally {
    isAIProcessing.value = false
  }
}

const showCustomPromptDialog = () => {
  aiMenuVisible.value = false
  customPromptDialogVisible.value = true
  customPrompt.value = ''

  nextTick(() => {
    const textarea = document.querySelector('.custom-prompt-textarea') as HTMLTextAreaElement
    if (textarea) {
      textarea.focus()
    }
  })
}

const processCustomAIPrompt = () => {
  if (!customPrompt.value.trim()) {
    ElMessage.warning('请输入自定义提示词')
    return
  }

  processAIRequest('custom', customPrompt.value)
}

// 菜单配置数据结构
const menuConfig = {
  改写: {
    isSubmenu: true,
    items: {
      轻松明快: 'light',
      严肃正规: 'serious',
      添加修辞: 'embellish',
      简洁化: 'simplify',
      精确优化: 'optimize',
    },
  },
  扩写: 'expand',
  续写: 'continue',
  智能排版: {
    isSubmenu: true,
    items: {
      自动分段: 'auto_paragraph',
      提取要点: 'extract_points',
      添加小标题: 'add_headings',
      格式化列表: 'format_list',
    },
  },
  图文混排: {
    isSubmenu: true,
    items: {
      建议图片位置: 'suggest_image_position',
      生成图片描述: 'generate_image_caption',
      左图右文: 'left_image_right_text',
      右图左文: 'right_image_left_text',
      上图下文: 'top_image_bottom_text',
      下图上文: 'bottom_image_top_text',
      图片背景覆盖: 'image_background_overlay',
    },
  },
  自定义格式: 'custom',
}

// 获取router实例
const router = useRouter()

onMounted(() => {
  // 添加Bootstrap CSS和JS的CDN引用
  const loadBootstrapResources = () => {
    // 检查Bootstrap是否已加载
    if (typeof window.bootstrap !== 'undefined') {
      console.log('Bootstrap已经加载');
      return Promise.resolve();
    }

    return new Promise<void>((resolve, reject) => {
      // 移除可能存在的旧版本
      const oldCss = document.getElementById('bootstrap-css');
      if (oldCss) {
        oldCss.remove();
      }

      const oldJs = document.getElementById('bootstrap-js');
      if (oldJs) {
        oldJs.remove();
      }

      const oldStyle = document.getElementById('bootstrap-editor-fixes');
      if (oldStyle) {
        oldStyle.remove();
      }

      // 加载Bootstrap CSS
      const cssLink = document.createElement('link');
      cssLink.rel = 'stylesheet';
      cssLink.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css';
      cssLink.id = 'bootstrap-css';
      cssLink.integrity = 'sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3';
      cssLink.crossOrigin = 'anonymous';
      document.head.appendChild(cssLink);

      // 加载Bootstrap JS
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
      script.id = 'bootstrap-js';
      script.integrity = 'sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p';
      script.crossOrigin = 'anonymous';

      script.onload = () => {
        console.log('Bootstrap资源加载完成');

        // 添加自定义样式，确保Bootstrap样式在编辑器内部正确应用
        const customStyle = document.createElement('style');
        customStyle.id = 'bootstrap-editor-fixes';
        // customStyle.textContent = `
        //   /* 确保编辑器内的Bootstrap组件正确显示 */
        //   .tiptap-editor [data-bs-component],
        //   .editor-content [data-bs-component] {
        //     display: block !important;
        //     position: relative !important;
        //     margin: 1rem 0 !important;
        //     width: 100% !important;
        //   }

        //   /* 确保编辑器内的Bootstrap样式不受编辑器样式影响 */
        //   .tiptap-editor.bootstrap .btn,
        //   .editor-content.bootstrap .btn {
        //     all: revert;
        //     display: inline-block !important;
        //     font-weight: 400 !important;
        //     line-height: 1.5 !important;
        //     text-align: center !important;
        //     text-decoration: none !important;
        //     vertical-align: middle !important;
        //     cursor: pointer !important;
        //     user-select: none !important;
        //     border: 1px solid transparent !important;
        //     padding: 0.375rem 0.75rem !important;
        //     font-size: 1rem !important;
        //     border-radius: 0.25rem !important;
        //     transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out !important;
        //   }

        //   /* 确保编辑器内的Bootstrap导航样式正确 */
        //   .tiptap-editor.bootstrap .nav,
        //   .editor-content.bootstrap .nav {
        //     all: revert;
        //     display: flex !important;
        //     flex-wrap: wrap !important;
        //     padding-left: 0 !important;
        //     margin-bottom: 0 !important;
        //     list-style: none !important;
        //   }

        //   /* 确保编辑器内的Bootstrap列表样式正确 */
        //   .tiptap-editor.bootstrap ul.list-group,
        //   .editor-content.bootstrap ul.list-group {
        //     all: revert;
        //     display: flex !important;
        //     flex-direction: column !important;
        //     padding-left: 0 !important;
        //     margin-bottom: 0 !important;
        //     border-radius: 0.25rem !important;
        //   }

        //   .tiptap-editor.bootstrap .list-group-item,
        //   .editor-content.bootstrap .list-group-item {
        //     all: revert;
        //     position: relative !important;
        //     display: block !important;
        //     padding: 0.5rem 1rem !important;
        //     color: #212529 !important;
        //     text-decoration: none !important;
        //     background-color: #fff !important;
        //     border: 1px solid rgba(0,0,0,.125) !important;
        //   }
        // `;
        document.head.appendChild(customStyle);

        resolve();
      };

      script.onerror = (error) => {
        console.error('Bootstrap资源加载失败:', error);
        reject(error);
      };

      document.head.appendChild(script);
    });
  };

  // 加载Bootstrap资源
  loadBootstrapResources()
    .then(() => {
      console.log('Bootstrap资源加载成功');

      // 确保编辑器容器有bootstrap类
      const editorContainers = document.querySelectorAll('.editor-content');
      editorContainers.forEach(container => {
        container.classList.add('bootstrap', 'bootstrap-enabled');
      });

      // 初始化Bootstrap组件
      setTimeout(() => {
        initBootstrapComponents();
      }, 200);
    })
    .catch(error => {
      console.error('加载Bootstrap资源时出错:', error);
    });

  // 添加编辑器内容变化的监听
  if (editor.value) {
    editor.value.on('update', handleEditorContentChange);
  }

  window.addEventListener('keydown', handleKeyDown)

  // 创建菜单元素
  const bubbleMenuElement = document.createElement('div')
  bubbleMenuElement.className = 'bubble-menu'

  // 构建菜单HTML
  let bubbleMenuHTML = `<div class="bubble-menu-container">
    <button class="bubble-button" data-action="bold" title="粗体">B</button>
    <button class="bubble-button" data-action="italic" title="斜体">I</button>
    <button class="bubble-button" data-action="strike" title="删除线">S</button>
    <button class="bubble-button" data-action="code" title="代码">\`</button>
    <button class="bubble-button" data-action="color" title="紫色">紫</button>
    <div class="bubble-dropdown">
      <button class="bubble-button bubble-ai-button" data-action="ai" title="AI 处理">AI</button>
      <div class="bubble-dropdown-content bubble-ai-dropdown-content">`

  // 构建AI菜单项
  for (const [menuTitle, menuValue] of Object.entries(menuConfig)) {
    if (typeof menuValue === 'string') {
      // 简单菜单项
      bubbleMenuHTML += `<div class="bubble-dropdown-item" data-ai-action="${menuValue}">${menuTitle}</div>`
    } else {
      // 子菜单
      bubbleMenuHTML += `
        <div class="bubble-dropdown-item bubble-dropdown-submenu">
          <span>${menuTitle}</span>
          <div class="bubble-dropdown-submenu-content">`

      for (const [subTitle, subValue] of Object.entries(menuValue.items)) {
        bubbleMenuHTML += `<div class="bubble-dropdown-item" data-ai-action="${subValue}">${subTitle}</div>`
      }

      bubbleMenuHTML += `</div></div>`
    }
  }

  bubbleMenuHTML += `</div></div></div>`
  bubbleMenuElement.innerHTML = bubbleMenuHTML
  document.body.appendChild(bubbleMenuElement)

  // 基本按钮事件处理
  bubbleMenuElement.querySelectorAll('.bubble-button:not(.bubble-ai-button)').forEach(button => {
    button.addEventListener('click', () => {
      if (!editor.value) return

      const action = (button as HTMLElement).dataset.action
      if (!action) return

      switch (action) {
        case 'bold':
          editor.value.chain().focus().toggleBold().run()
          break
        case 'italic':
          editor.value.chain().focus().toggleItalic().run()
          break
        case 'strike':
          editor.value.chain().focus().toggleStrike().run()
          break
        case 'code':
          editor.value.chain().focus().toggleCode().run()
          break
        case 'color':
          editor.value.chain().focus().setColor('#958DF1').run()
          break
      }
    })
  })

  // AI按钮点击事件
  const aiButton = bubbleMenuElement.querySelector('.bubble-ai-button')
  if (aiButton) {
    aiButton.addEventListener('click', e => {
      e.stopPropagation()
      const dropdown = bubbleMenuElement.querySelector('.bubble-ai-dropdown-content')
      if (dropdown) {
        if ((dropdown as HTMLElement).style.display === 'block') {
          ;(dropdown as HTMLElement).style.display = 'none'
        } else {
          ;(dropdown as HTMLElement).style.display = 'block'
        }
      }
    })
  }

  // 子菜单项的悬停事件
  bubbleMenuElement.querySelectorAll('.bubble-dropdown-submenu').forEach(submenu => {
    submenu.addEventListener('mouseenter', () => {
      const submenuContent = submenu.querySelector('.bubble-dropdown-submenu-content')
      if (submenuContent) {
        ;(submenuContent as HTMLElement).style.display = 'block'
      }
    })

    submenu.addEventListener('mouseleave', () => {
      const submenuContent = submenu.querySelector('.bubble-dropdown-submenu-content')
      if (submenuContent) {
        ;(submenuContent as HTMLElement).style.display = 'none'
      }
    })
  })

  // 对接菜单项的点击事件
  bubbleMenuElement.querySelectorAll('.bubble-dropdown-item[data-ai-action]').forEach(item => {
    item.addEventListener('click', e => {
      e.stopPropagation()

      const aiAction = (item as HTMLElement).dataset.aiAction
      if (!aiAction) return

      // 处理特殊情况
      if (aiAction === 'custom') {
        showCustomPromptDialog()
      } else {
        processAIRequest(aiAction)
      }

      // 隐藏所有下拉菜单
      const dropdownContents = document.querySelectorAll('.bubble-dropdown-content, .bubble-dropdown-submenu-content')
      dropdownContents.forEach(content => {
        ;(content as HTMLElement).style.display = 'none'
      })
    })
  })

  // 点击文档其他地方时隐藏所有下拉菜单
  document.addEventListener('click', () => {
    const dropdownContents = document.querySelectorAll('.bubble-dropdown-content, .bubble-dropdown-submenu-content')
    dropdownContents.forEach(content => {
      ;(content as HTMLElement).style.display = 'none'
    })
  })

  // 创建并配置 FloatingMenu
  const floatingMenuElement = document.createElement('div')
  floatingMenuElement.className = 'floating-menu'
  floatingMenuElement.innerHTML = `
    <div class="floating-menu-container">
      <button class="floating-button" data-action="heading1" title="标题1">H1</button>
      <button class="floating-button" data-action="heading2" title="标题2">H2</button>
      <button class="floating-button" data-action="heading3" title="标题3">H3</button>
      <button class="floating-button" data-action="bulletList" title="无序列表">•</button>
      <button class="floating-button" data-action="orderedList" title="有序列表">1.</button>
      <button class="floating-button" data-action="blockquote" title="引用块">❞</button>
      <button class="floating-button" data-action="codeBlock" title="代码块"><></button>
    </div>
  `
  document.body.appendChild(floatingMenuElement)

  // 为 FloatingMenu 添加事件处理
  floatingMenuElement.querySelectorAll('.floating-button').forEach(button => {
    button.addEventListener('click', () => {
      if (!editor.value) return

      const action = (button as HTMLElement).dataset.action
      if (!action) return

      switch (action) {
        case 'heading1':
          editor.value.chain().focus().toggleHeading({ level: 1 }).run()
          break
        case 'heading2':
          editor.value.chain().focus().toggleHeading({ level: 2 }).run()
          break
        case 'heading3':
          editor.value.chain().focus().toggleHeading({ level: 3 }).run()
          break
        case 'bulletList':
          editor.value.chain().focus().toggleBulletList().run()
          break
        case 'orderedList':
          editor.value.chain().focus().toggleOrderedList().run()
          break
        case 'blockquote':
          editor.value.chain().focus().toggleBlockquote().run()
          break
        case 'codeBlock':
          editor.value.chain().focus().toggleCodeBlock().run()
          break
      }
    })
  })

  editor.value = new Editor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          HTMLAttributes: {
            class: 'tiptap-bullet-list',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'tiptap-ordered-list',
          },
        },
        // 启用HTML解析，允许原始HTML
        codeBlock: {
          HTMLAttributes: {
            class: 'tiptap-code-block',
          },
        },
      }),

      Color,
      TextStyle,
      Underline,
      Strike,
      Highlight.configure({
        multicolor: true,
      }),
      FontFamily,
      Subscript,
      Superscript,
      Typography,

      Indent,

      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'tiptap-link',
        },
      }),

      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right', 'justify'],
      }),

      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'tiptap-table',
        },
      }),
      TableRow.configure({
        HTMLAttributes: {
          class: 'tiptap-table-row',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'tiptap-table-cell',
        },
      }),
      TableHeader.configure({
        HTMLAttributes: {
          class: 'tiptap-table-header',
        },
      }),

      TaskList.configure({
        HTMLAttributes: {
          class: 'tiptap-task-list',
        },
      }),
      TaskItem.configure({
        nested: true,
      }),

      BubbleMenu.configure({
        element: bubbleMenuElement,
        shouldShow: ({ state }) => {
          return !state.selection.empty
        },
        tippyOptions: {
          placement: 'top',
          interactive: true,
          hideOnClick: false,
        },
      }),
      Image.configure({
        inline: false,
        allowBase64: true,
        HTMLAttributes: {
          class: 'tiptap-image',
        },
      }),
      FloatingMenu.configure({
        element: floatingMenuElement,
        shouldShow: ({ state }) => {
          const { $from, empty } = state.selection
          const isAtStart = $from.parent.type.name === 'paragraph' && $from.parent.textContent === '' && $from.pos === 1
          return isAtStart || (empty && $from.parent.textContent === '')
        },
        tippyOptions: {
          placement: 'left',
          interactive: true,
        },
      }),

      Placeholder.configure({
        placeholder: props.placeholder,
      }),

      // 自定义区块扩展
      GridBlock,
      CtaBlock,
      ImageTextBlock,
      HeadlineBlock,
      BootstrapComponent,
      // 新增富文本节点支持
      RichTextNode,
      // 允许保留所有内联样式
      AllowAttributes,
    ],
    content: props.content || props.modelValue || '', // 使用props中的content或modelValue
    editorProps: {
      attributes: {
        class: 'tiptap-editor',
        spellcheck: 'false',
      },
    },
    onUpdate: () => {
      // 先更新内容
      updateContent() // 内容更新时触发事件

      // 内容更新时初始化Bootstrap组件，但使用延迟确保DOM已完全更新
      setTimeout(() => {
        initBootstrapComponents()
      }, 100)
    },
  })

  const editorElement = document.querySelector('.editor-content')
  if (editorElement) {
    editorElement.addEventListener('dragover', (e: Event) => {
      e.preventDefault()
      editorElement.classList.add('dragover')
    })

    editorElement.addEventListener('dragleave', () => {
      editorElement.classList.remove('dragover')
    })

    editorElement.addEventListener('drop', (e: Event) => {
      e.preventDefault()
      handleDrop(e)
    })
  }

  if (editor.value) {
    editor.value.on('paste', e => {
      handlePaste(e)
    })
  }

  // 确保编辑器完全初始化后再初始化Bootstrap组件
  nextTick(() => {
    aiButtonRef.value = document.querySelector('.ai-button')

    // 使用延迟确保DOM已完全渲染
    setTimeout(() => {
      console.log('初始化编辑器后的Bootstrap组件')
      initBootstrapComponents()
    }, 200)
  })

  // 处理 AI 菜单项点击
  const aiMenuItems = document.querySelectorAll('.ai-menu-item')
  aiMenuItems.forEach(item => {
    item.addEventListener('click', () => {
      const action = (item as HTMLElement).dataset.action
      if (action) {
        processAIRequest(action)
      }
    })
  })

  // 处理自定义提示词对话框提交
  const submitCustomPromptButton = document.querySelector('.submit-button')
  if (submitCustomPromptButton) {
    submitCustomPromptButton.addEventListener('click', () => {
      processCustomAIPrompt()
    })
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeyDown)

  const bubbleMenuElement = document.querySelector('.bubble-menu')
  const floatingMenuElement = document.querySelector('.floating-menu')

  if (bubbleMenuElement) {
    bubbleMenuElement.remove()
  }

  if (floatingMenuElement) {
    floatingMenuElement.remove()
  }

  if (editor.value) {
    editor.value.destroy()
  }

  const editorElement = document.querySelector('.editor-content')
  if (editorElement) {
    editorElement.removeEventListener('dragover', (e: Event) => {
      e.preventDefault()
    })
    editorElement.removeEventListener('dragleave', () => {})
    editorElement.removeEventListener('drop', (e: Event) => {
      e.preventDefault()
    })
  }

  if (editor.value) {
    editor.value.off('paste')
  }

  document.removeEventListener('click', closeAIMenu)
})
</script>

<style lang="scss" scoped>
/* 使用深度选择器来确保编辑器内部样式正确应用 */
.tiptap-editor-container {
  max-width: 1200px;
  margin: 20px auto;
  max-width: 1000px;
  position: relative;
  z-index: 100;

  :deep(*) {
    box-sizing: border-box;
  }
}

.editor-wrapper {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-wrapper.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999999 !important; /* 使用!important确保优先级 */
  border-radius: 0;
  margin: 0;
}

.toolbar-container {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  z-index: 100000;
}

button {
  padding: 5px 10px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #f5f7fa;
}

button.is-active {
  background-color: #ecf5ff;
  color: #409eff;
  border-color: #b3d8ff;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.block-button {
  background-color: #409eff !important;
  color: white !important;
  border-color: #409eff !important;
}

.fullscreen-button {
  margin-left: auto;
  background-color: #409eff !important;
  color: white !important;
  border-color: #409eff !important;
}

.fullscreen-button:hover {
  background-color: #66b1ff !important;
}
.ai-button {
  background-color: #409eff !important;
  color: white !important;
  border-color: #409eff !important;
}
.editor-content-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #fff;
}

.editor-content-isolated {
  width: 100%;
  height: 100%;
  position: relative;
  isolation: isolate;
}

.editor-content {
  padding: 16px;
  min-height: 300px;
}

/* 使用深度选择器确保编辑器内部样式能够正确应用 */
:deep(.tiptap-editor) {
  outline: none;
  min-height: 100%;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 16px;
  line-height: 1.5;

  /* 确保Bootstrap组件在编辑器中正确显示 */
  [data-bs-component] {
    display: block;
    position: relative;
    margin: 1rem 0;
    width: 100%;
  }

  &:focus {
    outline: none;
  }

  &.dragover {
    background-color: rgba(64, 158, 255, 0.1);
    border: 2px dashed #409eff;
  }
}

/* 使用更具体的选择器，确保样式只应用于编辑器内容区域 */
:deep(.editor-content-area .tiptap-editor) {
  /* 编辑器内容区域的基本样式 */
  p {
    margin: 1em 0;
  }

  /* 列表样式 - 使用更具体的选择器确保只应用于编辑器内容 */
  // & > ul,
  // & > ol,
  // & div > ul,
  // & div > ol,
  // & p + ul,
  // & p + ol {
  //   display: block ;
  //   padding-left: 2em ;
  //   margin: 1em 0 ;
  //   list-style-position: outside ;
  // }

  // & > ul,
  // & div > ul,
  // & p + ul {
  //   list-style-type: disc ;
  // }

  // & > ol,
  // & div > ol,
  // & p + ol {
  //   list-style-type: decimal ;
  // }

  // & > ul > li,
  // & > ol > li,
  // & div > ul > li,
  // & div > ol > li {
  //   display: list-item ;
  //   padding-left: 0 ;
  //   margin: 0.5em 0 ;
  // }

  .tiptap-bullet-list {
    list-style-type: disc !important;
  }

  .tiptap-ordered-list {
    list-style-type: decimal ;
  }

  // & > ul > li > ul,
  // & div > ul > li > ul {
  //   list-style-type: circle ;
  // }

  & > ul > li > ul > li > ul,
  & div > ul > li > ul > li > ul {
    list-style-type: square ;
  }

  /* 标题样式 - 使用更具体的选择器确保只应用于编辑器内容 */
  & > h1,
  & > h2,
  & > h3,
  & > h4,
  & > h5,
  & > h6,
  & div > h1,
  & div > h2,
  & div > h3,
  & div > h4,
  & div > h5,
  & div > h6 {
    line-height: 1.1;
    margin-top: 2.5rem;
    margin-bottom: 1rem;
  }

  & > h1,
  & div > h1 {
    font-size: 1.4rem;
  }

  & > h2,
  & div > h2 {
    font-size: 1.2rem;
  }

  & > h3,
  & div > h3 {
    font-size: 1.1rem;
  }

  & > h4,
  & > h5,
  & > h6,
  & div > h4,
  & div > h5,
  & div > h6 {
    font-size: 1rem;
  }

  /* 代码样式 */
  code {
    background-color: #f0ecfe;
    border-radius: 0.4rem;
    color: #333;
    font-size: 0.85rem;
    padding: 0.25em 0.3em;
  }

  pre {
    background: #333;
    border-radius: 0.5rem;
    color: #fff;
    font-family: 'JetBrainsMono', monospace;
    margin: 1.5rem 0;
    padding: 0.75rem 1rem;

    code {
      background: none;
      color: inherit;
      font-size: 0.8rem;
      padding: 0;
    }
  }

  /* 引用和分隔线样式 */
  blockquote {
    border-left: 3px solid #c0c4cc;
    margin: 1.5rem 0;
    padding-left: 1rem;
  }

  hr {
    border: none;
    border-top: 1px solid #e4e7ed;
    margin: 2rem 0;
  }

  /* 任务列表样式 */
  .tiptap-task-list {
    list-style-type: none !important;
    padding-left: 0.5em !important;

    li {
      display: flex !important;
      align-items: flex-start !important;
      margin-bottom: 0.5em !important;

      > label {
        margin-right: 0.5em !important;
        user-select: none !important;
      }
    }
  }

  /* 表格样式 */
  .tiptap-table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
    margin: 1rem 0;
    border: 2px solid #dcdfe6;

    td,
    th {
      border: 2px solid #dcdfe6;
      padding: 8px;
      position: relative;
      min-width: 100px;
      height: 40px;
      background-color: #fff;
      vertical-align: top;

      > * {
        margin: 0;
      }
    }

    th {
      background-color: #f5f7fa;
      font-weight: bold;
      text-align: left;
    }

    .column-resize-handle {
      background-color: #409eff;
      height: 100%;
      width: 4px;
      position: absolute;
      right: -2px;
      top: 0;
      cursor: col-resize;
      opacity: 0;
      transition: opacity 0.3s;
      z-index: 20;
    }

    .selectedCell {
      background-color: rgba(64, 158, 255, 0.1);

      &::after {
        background: rgba(64, 158, 255, 0.1);
        content: '';
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        pointer-events: none;
        position: absolute;
        z-index: 2;
      }
    }

    &.resize-cursor {
      cursor: col-resize;
    }

    &:hover .column-resize-handle {
      opacity: 1;
    }
  }

  .tiptap-table-cell,
  .tiptap-table-header {
    p {
      margin: 0;
    }
  }

  .tableFloatingMenu {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 5px;
    display: flex;
    gap: 5px;
    position: absolute;
    z-index: 10;
  }
}

/* ProseMirror 特定样式也需要使用深度选择器，并限制在编辑器内容区域 */
:deep(.editor-content-area .ProseMirror) {
  /* 使用更具体的选择器确保只应用于编辑器内容的表格 */
  & > table,
  & div > table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
    overflow: hidden;
  }

  & > table td,
  & > table th,
  & div > table td,
  & div > table th {
    position: relative;
    min-width: 1em;
    border: 2px solid #dcdfe6;
    padding: 8px;
    vertical-align: top;
  }

  & > table th,
  & div > table th {
    font-weight: bold;
    background-color: #f5f7fa;
  }

  & > table .selectedCell:after,
  & div > table .selectedCell:after {
    z-index: 2;
    position: absolute;
    content: '';
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(64, 158, 255, 0.1);
    pointer-events: none;
  }

  & > table p,
  & div > table p {
    margin: 0;
  }
}

:deep(.editor-content-area .tiptap-link) {
  color: #409eff;
  text-decoration: underline;
  cursor: pointer;
}

/* 其他非编辑器内部的样式 */
.typewriter-quick-bar {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 5px;
  z-index: 10000;
  display: flex;
  gap: 5px;
}

.typewriter-quick-bar-button {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background-color: #f5f7fa;
  }

  &.is-active {
    background-color: #ecf5ff;
    color: #409eff;
    border-color: #b3d8ff;
  }
}

.ai-icon {
  color: white;
  background-color: #3370ff;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

.ai-dropdown-trigger {
  margin-left: 5px;
  position: relative;
}

.ai-dropdown-menu {
  position: absolute;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 10000002 !important; /* 使用!important确保优先级 */
  min-width: 160px;
  padding: 5px 0;
  top: v-bind('aiMenuPosition.top');
  left: v-bind('aiMenuPosition.left');
}

.ai-menu-header {
  display: none;
}

.ai-menu-item {
  padding: 8px 15px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.ai-menu-item:hover {
  background-color: #f5f7fa;
}

.custom-prompt-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  z-index: 10000005 !important; /* 使用!important确保优先级 */
  width: 500px;
  max-width: 90vw;
}

.custom-prompt-header {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.custom-prompt-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  color: #909399;
  cursor: pointer;
}

.custom-prompt-body {
  padding: 15px;
}

.custom-prompt-textarea {
  width: 100%;
  min-height: 150px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
}

.custom-prompt-footer {
  padding: 15px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancel-button {
  padding: 8px 15px;
  background-color: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
}

.submit-button {
  padding: 8px 15px;
  background-color: #3370ff;
  color: white;
  border: 1px solid #3370ff;
  border-radius: 4px;
  cursor: pointer;
}

.submit-button:hover {
  background-color: #66b1ff;
}

.ai-processing-overlay {
  position: absolute;
  top: 50px; /* 工具栏高度 */
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0 0 4px 4px;
}

.ai-processing-overlay.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000000 !important; /* 使用!important确保优先级 */
}

.ai-processing-content {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.ai-loader {
  border: 2px solid #f3f3f3;
  border-radius: 50%;
  border-top: 2px solid #3370ff;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.ai-processing-text {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.bubble-dropdown {
  position: relative;
  display: inline-block;
}

.bubble-dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  min-width: 160px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  z-index: 10000003;
  margin-top: 5px;
  padding: 5px 0;
}

.bubble-dropdown-item {
  padding: 8px 15px;
  cursor: pointer;
  font-size: 14px;
  position: relative;
  white-space: nowrap;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bubble-dropdown-item:hover {
  background-color: #f5f7fa;
}

// 子菜单样式
.bubble-dropdown-submenu {
  position: relative;
}

.bubble-dropdown-submenu > span::after {
  content: '▶';
  font-size: 10px;
  margin-left: 5px;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
}

.bubble-dropdown-submenu-content {
  display: none;
  position: absolute;
  top: 0;
  left: 100%;
  background-color: white;
  min-width: 160px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  z-index: 10000004;
  padding: 5px 0;
}

// AI菜单项
.bubble-ai-dropdown-content {
  /* 确保AI下拉菜单与按钮对齐 */
  left: auto;
  right: 0;
}

// 高亮选中项
.bubble-dropdown-item.active {
  background-color: #ecf5ff;
  color: #409eff;
}

/* 修复 quickbar 样式 */
:deep(.bubble-menu) {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 5px;
  z-index: 10000001 !important; /* 使用!important确保优先级 */
}

:deep(.bubble-menu-container) {
  display: flex;
  gap: 5px;
}

:deep(.bubble-button) {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
}

:deep(.bubble-button:hover) {
  background-color: #f5f7fa;
}

:deep(.bubble-ai-button) {
  background-color: #3370ff !important;
  color: white !important;
  border-color: #3370ff !important;
}

:deep(.floating-menu) {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 5px;
  z-index: 10000001 !important; /* 使用!important确保优先级 */
}

:deep(.floating-menu-container) {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.floating-button) {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
}

:deep(.floating-button:hover) {
  background-color: #f5f7fa;
}

:deep(.bubble-dropdown) {
  position: relative;
  display: inline-block;
}

:deep(.bubble-dropdown-content) {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  min-width: 160px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  z-index: 10000003;
  margin-top: 5px;
  padding: 5px 0;
}

:deep(.bubble-dropdown-item) {
  padding: 8px 15px;
  cursor: pointer;
  font-size: 14px;
  position: relative;
  white-space: nowrap;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.bubble-dropdown-item:hover) {
  background-color: #f5f7fa;
}

:deep(.bubble-dropdown-submenu) {
  position: relative;
}

:deep(.bubble-dropdown-submenu > span::after) {
  content: '▶';
  font-size: 10px;
  margin-left: 5px;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
}

:deep(.bubble-dropdown-submenu-content) {
  display: none;
  position: absolute;
  top: 0;
  left: 100%;
  background-color: white;
  min-width: 160px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  z-index: 10000004;
  padding: 5px 0;
}

:deep(.bubble-ai-dropdown-content) {
  /* 确保AI下拉菜单与按钮对齐 */
  left: auto;
  right: 0;
}

:deep(.bubble-dropdown-item.active) {
  background-color: #ecf5ff;
  color: #409eff;
}

/* 新增图片工具栏样式 */
.image-quickbar {
  position: absolute;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 5px;
  z-index: 10005;
  display: flex;
  gap: 5px;

  button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  .image-menu-separator {
    width: 1px;
    background-color: #dcdfe6;
    margin: 0 2px;
  }
}



/* 图片属性对话框样式 */
.image-properties-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  z-index: 10006;
  width: 500px;
  max-width: 90vw;
}

.dialog-header {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.dialog-body {
  padding: 15px;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #606266;
}

.form-control {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
}

select.form-control {
  height: 38px;
}

.dialog-footer {
  padding: 15px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancel-button {
  padding: 8px 15px;
  background-color: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  cursor: pointer;
  min-width: auto;
}

.cancel-button:hover {
  background-color: #f5f7fa;
}

.submit-button {
  padding: 8px 15px;
  background-color: #409eff;
  color: white;
  border: 1px solid #409eff;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  min-width: auto;
}

.submit-button:hover {
  background-color: #66b1ff;
}

/* 添加全屏模式相关样式 */
.editor-fullscreen-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: white;
  z-index: 999;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 确保全屏模式下的工具栏样式与非全屏一致 */
.editor-fullscreen-wrapper .toolbar-container {
  padding: 8px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.editor-fullscreen-wrapper .button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  align-items: center;
}

/* 确保全屏模式下的按钮样式与非全屏一致 */
.editor-fullscreen-wrapper button {
  padding: 5px 10px;
  border: 1px solid #dcdfe6;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.editor-fullscreen-wrapper button:hover {
  background-color: #f5f7fa;
}

.editor-fullscreen-wrapper button.is-active {
  background-color: #ecf5ff;
  color: #409eff;
  border-color: #b3d8ff;
}

.editor-fullscreen-wrapper .fullscreen-button {
  margin-left: auto;
  background-color: #409eff !important;
  color: white !important;
  border-color: #409eff !important;
}

.editor-fullscreen-wrapper .fullscreen-button:hover {
  background-color: #66b1ff !important;
}

.editor-fullscreen-wrapper .ai-button {
  background-color: #409eff !important;
  color: white !important;
  border-color: #409eff !important;
}

.editor-fullscreen-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

#editor-fullscreen-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999998;
}

/* 确保全屏模式下的编辑器内容样式正确 */
:deep(.editor-fullscreen-content .tiptap-editor) {
  min-height: calc(100vh - 120px);
  outline: none;
}
</style>

