{"placeholder": "Enter content here", "list": {"create_blank_template": "Create Blank Template", "select_template": "Select Template", "all": "All", "no_templates": "No Templates", "edit": "Edit", "delete": "Delete", "preview": "Preview", "confirm_delete": "Confirm Delete", "confirm_delete_message": "Are you sure you want to delete template", "delete_warning": "This operation cannot be undone!", "cancel": "Cancel", "confirm_delete_button": "Confirm Delete", "get_categories_failed": "Failed to get category data", "load_categories_failed": "Failed to load categories, please try again later", "get_category_templates_failed": "Failed to get category template data", "load_category_templates_failed": "Failed to load category templates, please try again later", "get_templates_failed": "Failed to get template data", "load_templates_failed": "Failed to load templates, please try again later", "template_deleted_success": "Template deleted successfully", "delete_template_failed": "Failed to delete template", "delete_template_failed_retry": "Failed to delete template, please try again later", "no_template_data": "No template data", "back": "Back"}, "preview": {"back": "Back", "mobile": "Mobile", "tablet": "Tablet", "desktop": "Desktop", "template_id_empty": "Template ID cannot be empty", "loading_template": "Loading template data...", "template_load_success": "Template data loaded successfully", "template_format_warning": "Content format may have issues, but loading has been attempted", "get_template_failed": "Failed to get template data", "unknown_error": "Unknown error"}, "ai": {"formatting": "AI Formatting", "formatting_in_progress": "AI formatting in progress...", "format_success": "AI formatting completed!", "format_error": "AI formatting failed, please try again later", "content_empty": "Please fill in the content before formatting", "no_text_selected": "Please select text to process", "process_error": "AI processing error, please try again later", "writing": "Writing", "writing_assistant": "AI Writing Assistant", "rewrite": "Rewrite", "light_style": "Light & Casual", "serious_style": "Serious & Formal", "embellish": "Add Rhetoric", "simplify": "Simplify", "optimize": "Optimize", "expand": "Expand", "continue": "Continue Writing", "smart_formatting": "Smart Formatting", "auto_paragraph": "Auto Paragraph", "extract_points": "Extract Points", "add_headings": "Add Headings", "format_list": "Format List", "image_text_layout": "Image-Text Layout", "suggest_image_position": "Suggest Image Position", "generate_image_caption": "Generate Image Caption", "left_image_right_text": "Left Image Right Text", "right_image_left_text": "Right Image Left Text", "top_image_bottom_text": "Top Image Bottom Text", "bottom_image_top_text": "Bottom Image Top Text", "image_background_overlay": "Image Background Overlay", "custom_format": "Custom Format", "custom_prompt": "Enter custom formatting instructions"}, "upload": {"size_error": "Upload failed, please keep image size within 10M", "server_error": "Server Error!"}, "chart": {"load_error": "Failed to load chart data", "data_error": "Error parsing chart data", "title": "Chart"}, "image": {"options": "Image Options", "layout_options": "Image Layout Options", "text_wrap": "Text Wrap", "none": "None", "left_wrap": "Left Wrap", "right_wrap": "Right Wrap", "margin": "<PERSON><PERSON> (px)", "size": "Size", "original_size": "Original Size", "small": "Small", "medium": "Medium", "large": "Large", "custom": "Custom", "cancel": "Cancel", "confirm": "Confirm", "rotate_left": "Rotate Left", "rotate_right": "Rotate Right", "position": "Position", "position_settings": "Image Position Settings", "center": "Center", "block": "Block", "inline": "Inline", "reset_position": "Reset Position", "align": "Align", "align_settings": "Image Alignment Settings", "top_align": "Top Align", "middle_align": "Middle Align", "bottom_align": "Bottom Align", "baseline_align": "<PERSON><PERSON>", "reset_align": "<PERSON><PERSON>", "size_settings": "Image Size Settings", "preset_size": "Preset Size", "small_size": "Small Size (25%)", "medium_size": "Medium Size (50%)", "large_size": "Large Size (75%)", "full_width": "Full Width (100%)", "reset_size": "Original Size", "custom_width": "Custom Width (px or %)", "custom_height": "Custom Height (px)"}, "button": {"h1": "H1", "h1_tooltip": "Heading 1", "h2": "H2", "h2_tooltip": "Heading 2", "h3": "H3", "h3_tooltip": "Heading 3"}, "fullscreenEditor": {"unknown_module": "Unknown <PERSON><PERSON>le", "layout_module_not_allowed": "Cannot place layout module in another layout module", "add_success": "Added {module} to layout", "image_upload_failed": "Image upload failed", "exit": "Exit", "select_template_type": "Select Template Type", "no_title": "Untitled", "editing_start_time": "Editing start time", "editing": "Editing", "preview": "Preview", "update_template": "Update Template", "save_as_template": "Save as Template", "publish": "Publish", "update": "Update", "desktop_preview": "Desktop Preview", "tablet_preview": "Tablet Preview", "mobile_preview": "Mobile Preview", "editor_settings": "Editor Settings", "refresh_content": "Refresh Editor Content", "undo": "Undo", "redo": "Redo", "add_to_page": "Add to Page", "search": "Search...", "modules": "<PERSON><PERSON><PERSON>", "sections": "Templates", "layouts": "Layouts", "current_selected_module": "Current selected module", "ai_optimize_module": "AI Optimize Module", "ai_formatting_assistant": "AI Formatting Assistant", "ai_summarize": "Smart Summary", "ai_improve": "Improve Content", "ai_rewrite": "Rewrite Content", "ai_correct": "Grammar Correction", "ai_custom_prompt": "Custom Prompt...", "custom_ai_prompt": "Custom AI Prompt", "enter_custom_prompt": "Enter custom prompt...", "cancel": "Cancel", "submit": "Submit", "ai_processing": "AI processing...", "content_preview": "Content Preview", "edit_title": "Edit Title", "page_title": "Page Title", "enter_title": "Please enter title", "title_length_error": "Title length should be between 1 and 50 characters", "enter_page_title": "Please enter page title", "title_tip": "The title will be displayed at the top of the page, keep it concise", "confirm": "Confirm", "status_editing": "Editing", "status_draft": "Draft", "refresh_confirm": "Are you sure you want to refresh the editor content? This will reset all unsaved changes.", "refresh_confirm_title": "Confirm Refresh", "refresh_success": "Editor content reloaded", "undo_success": "Previous operation undone", "redo_success": "Operation redone", "editor_not_initialized": "Editor not initialized", "selected_content_empty": "Selected content is empty, please select again", "ai_processing_complete": "AI processing complete", "processing_failed": "Processing failed", "ai_processing_failed": "AI processing failed", "please_enter_custom_prompt": "Please enter custom prompt", "single_column_layout": "Single Column Layout", "two_column_layout": "Two Column Layout", "three_column_layout": "Three Column Layout", "left_1_3_right_2_3": "Left 1/3 Right 2/3", "left_2_3_right_1_3": "Left 2/3 Right 1/3", "four_column_layout": "Four Column Layout", "theme": "Theme", "draft": "Draft", "alert": "<PERSON><PERSON>", "card": "Card", "button_group": "Button Group", "text": "Text", "commerce": "Commerce", "design": "Design", "functionality": "Functionality", "forms_and_buttons": "Forms and Buttons", "body_content": "Body Content", "media": "Media", "blog": "Blog", "social": "Social", "all": "All", "undo_info": "Previous operation undone", "redo_info": "Operation redone", "refresh_canceled": "Refresh operation canceled", "loading_template_data": "Loading template data...", "switch_to_desktop": "Switched to desktop preview mode", "switch_to_tablet": "Switched to tablet preview mode", "switch_to_mobile": "Switched to mobile preview mode", "loading_page_detail": "Loading page details...", "processing_request": "Processing request...", "continue_editing": "Continue editing current page", "saving_template": "Saving template...", "updating_template": "Updating template...", "template_not_found": "Temp<PERSON> not found", "template_not_found_with_type": "Template not found: {type}", "message": {"editor_not_initialized": "Editor not initialized", "content_empty": "Editor content cannot be empty, please add content before {action}", "title_empty": "Title cannot be empty", "template_type_empty": "Please select a template type first", "template_save_success": "Template saved successfully", "template_update_success": "Template updated successfully", "template_save_failed": "Failed to save template", "template_update_failed": "Failed to update template", "template_load_success": "Template content loaded successfully", "template_load_failed": "Failed to load template content", "template_parse_failed": "Failed to parse template content", "template_confirm_update": "Are you sure you want to update the template '{name}'? This will overwrite the original content.", "template_confirm_update_title": "Update Confirmation", "template_confirm_update_confirm": "Confirm Update", "template_confirm_update_cancel": "Cancel", "publish_success": "Published successfully", "update_success": "Updated successfully", "publish_failed": "Publish failed", "update_failed": "Update failed", "processing": "Processing {action} request...", "continue_editing": "Continue editing current page", "return_list": "Return to list", "add_success": "Added {module}", "insert_success": "Inserted {module}", "block_update_success": "Block updated successfully", "block_update_failed": "Block update failed", "block_copy_success": "Block copied", "block_delete_success": "Block deleted", "block_delete_forced": "Block forcibly deleted", "block_delete_failed": "Failed to delete block: {msg}", "block_delete_unable": "Unable to delete block, please refresh the page and try again", "placeholder_delete_success": "Placeholder deleted", "style_update_success": "Style updated successfully", "style_update_failed": "Failed to update style", "ai_apply_success": "AI optimized content applied successfully", "ai_apply_failed": "Failed to apply AI optimized content, invalid HTML", "select_block_first": "Please select a block to optimize first", "image_upload_failed": "Image upload failed", "unsupported_file_type": "Unsupported file type: {type}", "fetch_template_type_failed": "Failed to fetch template types", "fetch_page_detail_success": "Page details loaded successfully", "fetch_page_detail_failed": "Failed to fetch page details: {msg}", "fetch_template_success": "Template data loaded successfully", "fetch_template_failed": "Failed to fetch template data: {msg}", "parse_template_failed": "Failed to parse template content, possibly incompatible format", "block_forced_delete_failed": "Forced delete also failed: {msg}", "block_placeholder_add": "Drag content here or click to add", "block_placeholder_menu_header": "Add Content", "block_placeholder_menu_more": "More...", "left": "Left", "center": "Center", "right": "Right", "alert_content": "This is an alert box", "card_title": "Card Title", "card_content": "This is the card content. You can add any text description here.", "card_button_text": "Learn More"}, "navigation": "Navigation", "form": "Form", "carousel": "Carousel", "table": "Table", "accordion": "Accordion", "accordion_description": "Add a collapsible content panel group", "a_class_module": "A-Class Promotion Module", "rich_text": "Rich Text", "heading": "Heading", "divider": "Divider", "button": "<PERSON><PERSON>", "countdown": "Countdown", "feature_list": "Feature List", "metrics": "Metrics", "pricing": "Pricing", "cta": "Call to Action", "social_media": "Social Media", "customer_testimonial": "Customer Testimonial", "timeline": "Timeline", "stats_card": "Stats Card", "navbar": "Navigation Bar", "hero_section": "Hero Section", "feature_cards": "Feature Cards", "info_section": "Info Section", "footer": "Footer", "image": "Image", "team_showcase": "Team Showcase", "partners": "Partners"}, "Editor": {"pageTemplate": {"marketingLandingPageName": "Marketing Landing Page", "marketingLandingPageDescription": "A complete landing page for product promotion, including pricing table, data display, FAQ, and more.", "productsPageName": "Product Marketing Page", "productsPageDescription": "A modern product marketing page showcasing product features, data statistics, and customer testimonials.", "demoLandingPageName": "Demo Booking Page", "demoLandingPageDescription": "A demo booking page for SaaS products, including feature display, customer testimonials, FAQ, and more.", "downloadResourcePageName": "Resource Download Page", "downloadResourcePageDescription": "A page for collecting user information and providing digital resource downloads, including forms, user reviews, and FAQs.", "shiliPageName": "Marketing Example Page", "shiliPageDescription": "A complete marketing website with brand display, multimedia content, customer testimonials, and social media links.", "aboutPageName": "About/Team Page", "aboutPageDescription": "A team introduction page showing team members, company philosophy, core values, and services.", "thanksPageName": "Thank You Page", "thanksPageDescription": "A page showing thank you information, download button, and next steps after resource download.", "chickthroughPageName": "Modern Marketing Page", "chickthroughPageDescription": "A modern marketing page with dashboard display, automated workflow, data statistics, and customer recommendations."}}, "router": {"editorManagement": "Editor Management", "tinyMceEditor": "TinyMCE Editor", "tiptapEditor": "Tiptap Editor", "pageList": "Page List", "pagePreview": "Page Preview", "fullscreenEditor": "Fullscreen Editor", "moduleEditor": "Module Editor", "tiptapPreview": "Tiptap Preview"}, "aiOptimizeSidebar": {"title": "AI Optimization Module", "loading": "AI is generating optimized content, please wait...", "presetSection": "Preset Optimization Schemes", "inputSection": "Optimization Instruction", "inputSectionSelected": "(Preset selected)", "tip": "Tip:", "tipHowToWrite": "How to write effective instructions?", "quickFill": "Quick Fill:", "generate": "Generate Optimized Content", "reset": "Reset", "cancel": "Cancel", "result": "Generated Result", "apply": "Apply Content", "regenerate": "Regenerate", "preview": "Preview", "code": "Code", "inputPlaceholder": "Describe how you want to optimize this module, e.g. improve copy, adjust style, or add more details", "inputError": "Please enter optimization instruction", "success": "Optimized content generated successfully", "fail": "Failed to generate content", "error": "Error occurred while generating content, please try again", "noContent": "No content to apply", "applied": "Optimized content applied", "blockType": {"richTextBlock": "Rich Text", "bootstrap-button": "<PERSON><PERSON>", "bootstrap-card": "Card", "heroBlock": "Hero Section", "navbarBlock": "<PERSON><PERSON><PERSON>", "bootstrap-metrics": "Metrics", "bootstrap-pricing": "Pricing Card", "featureCardsBlock": "Feature Cards", "statsCardBlock": "Stats Card", "socialFlowBlock": "Social Media", "testimonialSliderBlock": "Customer Testimonial", "timelineBlock": "Timeline", "infoSectionBlock": "Info Section", "footerBlock": "Footer", "layout-single": "Single Column Layout", "layout-two-column": "Two Column Layout", "layout-three-column": "Three Column Layout", "layout-four-column": "Four Column Layout", "layout-1-3-2-3": "Left 1/3 Right 2/3 Layout", "layout-2-3-1-3": "Left 2/3 Right 1/3 Layout", "bootstrap-heading": "Heading", "bootstrap-alert": "<PERSON><PERSON>", "bootstrap-accordion": "Accordion", "bootstrap-divider": "Divider", "bootstrap-countdown": "Countdown", "bootstrap-feature-list": "Feature List", "bootstrap-cta": "Call to Action", "bootstrap-image": "Image", "bootstrap-carousel": "Carousel", "bootstrap-nav": "Navigation Menu", "bootstrap-form": "Form", "bootstrap-table": "Table", "bootstrap-layout": "Layout", "team-block": "Team Showcase", "partners": "Partners", "unknown": "Unknown <PERSON><PERSON>le"}, "placeholder": {"bootstrap-button": "e.g. Optimize button copy, use more attractive wording, highlight call to action", "bootstrap-card": "e.g. Adjust card content, add more details, use more professional language", "heroBlock": "e.g. Redesign hero section for more impact, modify title and subtitle", "navbarBlock": "e.g. Optimize navigation structure, improve menu naming, enhance brand display", "richTextBlock": "e.g. Optimize text content, improve grammar and expression for professionalism", "bootstrap-metrics": "e.g. Highlight key metrics, add comparison, increase persuasiveness", "bootstrap-pricing": "e.g. Optimize pricing strategy display, enhance value perception", "featureCardsBlock": "e.g. Highlight core features, add application scenarios", "default": "Describe how you want to optimize this module, e.g. improve copy, adjust style, or add more details"}, "quickActions": {"optimizeCopywriting": "Optimize Copywriting", "addDetails": "Add Details", "improveDesign": "Improve Design", "enhanceInteraction": "Enhance Interaction"}, "tab": {"preview": "Preview", "code": "Code"}}, "blockControls": {"copy": "Copy", "delete": "Delete", "up": "Move Up", "down": "Move Down", "aiOptimize": "AI Optimize"}}