<?php

declare(strict_types=1);

namespace Modules\Approval\Enums;

use Bingo\Enums\Traits\EnumEnhance;
use Bingo\Enums\Traits\EnumTranslation;
/**
 * 审批模块错误码
 */
enum ApprovalErrorCode: int
{
    use EnumEnhance;

    // 通用错误码 (210000-210099)
    case NO_RELATED_TRANSLATION = 210001; // 无相关翻译
    case USER_NOT_FOUND = 210002; // 用户未找到

    // 审批流程相关错误码 (210100-210199)
    case FLOW_NOT_FOUND = 210100; // 流程未找到
    case FLOW_ALREADY_EXISTS = 210101; // 流程已存在
    case FLOW_CANNOT_BE_DELETED = 210102; // 流程不能删除
    case FLOW_CANNOT_BE_UPDATED = 210103; // 流程不能更新
    case FLOW_STEP_NOT_FOUND = 210104; // 流程步骤未找到
    case FLOW_STEP_DATA_ERROR = 210105; // 流程步骤数据错误
    case FLOW_HAS_UNFINISHED_RECORDS = 210106; // 流程存在未完成的审批记录
    case FLOW_IS_NOT_ENABLED = 210107; // 流程未打开

    // 审批步骤相关错误码 (210200-210299)
    case STEP_NOT_FOUND = 210200; // 步骤未找到
    case STEP_ALREADY_EXISTS = 210201; // 步骤已存在
    case STEP_CANNOT_BE_DELETED = 210202; // 步骤不能删除
    case STEP_CANNOT_BE_UPDATED = 210203; // 步骤不能更新

    // 审批角色相关错误码 (210300-210399)
    case ROLE_NOT_FOUND = 210300; // 角色未找到
    case ROLE_ALREADY_EXISTS = 210301; // 角色已存在
    case ROLE_CANNOT_BE_DELETED = 210302; // 角色不能删除
    case ROLE_CANNOT_BE_UPDATED = 210303; // 角色不能更新
    case ROLE_IN_USE = 210304; // 角色正在使用中
    case ROLE_CODE_EXISTS = 210305; // 角色代码已存在

    // 审批成员相关错误码 (210400-210499)
    case MEMBER_NOT_FOUND = 210400; // 成员未找到
    case MEMBER_ALREADY_EXISTS = 210401; // 成员已存在
    case MEMBER_CANNOT_BE_DELETED = 210402; // 成员不能删除
    case MEMBER_CANNOT_BE_UPDATED = 210403; // 成员不能更新

    // 审批组相关错误码 (210500-210599)
    case GROUP_NOT_FOUND = 210500; // 审批组未找到
    case GROUP_CREATE_FAILED = 210501; // 审批组创建失败
    case GROUP_UPDATE_FAILED = 210502; // 审批组更新失败
    case GROUP_DELETE_FAILED = 210503; // 审批组删除失败
    case GROUP_BATCH_DELETE_FAILED = 210504; // 审批组批量删除失败
    case GROUP_STATUS_UPDATE_FAILED = 210505; // 审批组状态更新失败
    case USER_NOT_IN_APPROVAL_GROUP = 210506; // 用户不在审批组中
    case GROUP_IN_USE = 210507; // 审批组正在使用中

    // 产品审批相关错误码 (210600-210699)
    case PRODUCT_FLOW_NOT_FOUND = 210600; // 产品流程未找到
    case PRODUCT_FLOW_ALREADY_EXISTS = 210601; // 产品流程已存在
    case PRODUCT_FLOW_CANNOT_DELETE = 210602; // 产品流程不能删除
    case PRODUCT_FLOW_HAS_RECORDS = 210603; // 产品流程存在审批记录
    case PRODUCT_FLOW_CREATE_FAILED = 210604; // 产品流程创建失败

    // 审批记录相关错误码 (210700-210799)
    case RECORD_NOT_FOUND = 210700; // 审批记录未找到
    case RECORD_CREATE_FAILED = 210701; // 审批记录创建失败
    case RECORD_ALREADY_APPROVED = 210702; // 审批记录已审批
    case LOG_NOT_FOUND = 210703; // 审批日志未找到
    case RECORD_CANNOT_BE_DELETED = 210704; // 审批记录不能删除
    case RECORD_DELETE_FAILED = 210705; // 审批记录删除失败
    case DUPLICATE_EMAIL = 210706; // 重复的邮箱
    case USER_NAME_MISMATCH = 210707; // 用户名称不匹配

     /**
     * 获取错误码对应的消息
     */
    public function message(): string
    {
        return match ($this) {

            // 通用错误码
            self::NO_RELATED_TRANSLATION => T('Approval::validation.error.noRelatedTranslation'),
            self::USER_NOT_FOUND => T('Approval::validation.error.userNotFound'),

            // 审批流程相关错误码
            self::FLOW_NOT_FOUND => T('Approval::validation.error.flowNotFound'),
            self::FLOW_ALREADY_EXISTS => T('Approval::validation.error.flowAlreadyExists'),
            self::FLOW_CANNOT_BE_DELETED => T('Approval::validation.error.flowCannotBeDeleted'),
            self::FLOW_CANNOT_BE_UPDATED => T('Approval::validation.error.flowCannotBeUpdated'),
            self::FLOW_STEP_NOT_FOUND => T('Approval::validation.error.flowStepNotFound'),
            self::FLOW_STEP_DATA_ERROR => T('Approval::validation.error.flowStepDataError'),
            self::FLOW_HAS_UNFINISHED_RECORDS => T('Approval::validation.error.flowHasUnfinishedRecords'),
            self::FLOW_IS_NOT_ENABLED => T('Approval::validation.error.flowIsNotEnabled'),

            // 审批步骤相关错误码
            self::STEP_NOT_FOUND => T('Approval::validation.error.stepNotFound'),
            self::STEP_ALREADY_EXISTS => T('Approval::validation.error.stepAlreadyExists'),
            self::STEP_CANNOT_BE_DELETED => T('Approval::validation.error.stepCannotBeDeleted'),
            self::STEP_CANNOT_BE_UPDATED => T('Approval::validation.error.stepCannotBeUpdated'),

            // 审批角色相关错误码
            self::ROLE_NOT_FOUND => T('Approval::validation.error.roleNotFound'),
            self::ROLE_ALREADY_EXISTS => T('Approval::validation.error.roleAlreadyExists'),
            self::ROLE_CANNOT_BE_DELETED => T('Approval::validation.error.roleCannotBeDeleted'),
            self::ROLE_CANNOT_BE_UPDATED => T('Approval::validation.error.roleCannotBeUpdated'),
            self::ROLE_IN_USE => T('Approval::validation.error.roleInUse'),
            self::ROLE_CODE_EXISTS => T('Approval::validation.error.roleCodeExists'),

            // 审批成员相关错误码
            self::MEMBER_NOT_FOUND => T('Approval::validation.error.memberNotFound'),
            self::MEMBER_ALREADY_EXISTS => T('Approval::validation.error.memberAlreadyExists'),
            self::MEMBER_CANNOT_BE_DELETED => T('Approval::validation.error.memberCannotBeDeleted'),
            self::MEMBER_CANNOT_BE_UPDATED => T('Approval::validation.error.memberCannotBeUpdated'),

            // 审批组相关错误码
            self::GROUP_NOT_FOUND => T('Approval::validation.error.groupNotFound'),
            self::GROUP_CREATE_FAILED => T('Approval::validation.error.groupCreateFailed'),
            self::GROUP_UPDATE_FAILED => T('Approval::validation.error.groupUpdateFailed'),
            self::GROUP_DELETE_FAILED => T('Approval::validation.error.groupDeleteFailed'),
            self::GROUP_BATCH_DELETE_FAILED => T('Approval::validation.error.groupBatchDeleteFailed'),
            self::GROUP_STATUS_UPDATE_FAILED => T('Approval::validation.error.groupStatusUpdateFailed'),
            self::USER_NOT_IN_APPROVAL_GROUP => T('Approval::validation.error.userNotInApprovalGroup'),
            self::GROUP_IN_USE => T('Approval::validation.error.groupInUse'),

            // 产品审批相关错误码
            self::PRODUCT_FLOW_NOT_FOUND => T('Approval::validation.error.productFlowNotFound'),
            self::PRODUCT_FLOW_ALREADY_EXISTS => T('Approval::validation.error.productFlowAlreadyExists'),
            self::PRODUCT_FLOW_CANNOT_DELETE => T('Approval::validation.error.productFlowCannotDelete'),
            self::PRODUCT_FLOW_HAS_RECORDS => T('Approval::validation.error.productFlowHasRecords'),
            self::PRODUCT_FLOW_CREATE_FAILED => T('Approval::validation.error.productFlowCreateFailed'),

            // 审批记录相关错误码
            self::RECORD_NOT_FOUND => T('Approval::validation.error.recordNotFound'),
            self::RECORD_CREATE_FAILED => T('Approval::validation.error.recordCreateFailed'),
            self::RECORD_ALREADY_APPROVED => T('Approval::validation.error.recordAlreadyApproved'),
            self::LOG_NOT_FOUND => T('Approval::validation.error.logNotFound'),
            self::RECORD_CANNOT_BE_DELETED => T('Approval::validation.error.recordCannotBeDeleted'),
            self::RECORD_DELETE_FAILED => T('Approval::validation.error.recordDeleteFailed'),
            self::DUPLICATE_EMAIL => T('Approval::validation.error.duplicateEmail'),
            self::USER_NAME_MISMATCH => T('Approval::validation.error.userNameMismatch'),
        };
    }
}

   

