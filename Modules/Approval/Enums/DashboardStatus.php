<?php

declare(strict_types=1);

namespace Modules\Approval\Enums;

/**
 * 审批仪表盘状态枚举
 */
enum DashboardStatus: string
{
    /**
     * 待处理
     */
    case PENDING = 'pending';
    
    /**
     * 已发布
     */
    case PUBLISHED = 'published';
    
    /**
     * 已完成
     */
    case COMPLETED = 'completed';
    
    /**
     * 获取所有状态值
     *
     * @return array<string>
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
    
    /**
     * 获取状态对应的显示名称
     *
     * @return string
     */
    public function label(): string
    {
        return match($this) {
            self::PENDING => '待处理',
            self::PUBLISHED => '已发布',
            self::COMPLETED => '已完成',
        };
    }
    
    /**
     * 检查给定的值是否为有效的状态
     *
     * @param string $value
     * @return bool
     */
    public static function isValid(string $value): bool
    {
        return in_array($value, self::values(), true);
    }

}
