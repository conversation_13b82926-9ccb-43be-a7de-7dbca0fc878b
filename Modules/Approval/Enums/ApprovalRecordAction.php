<?php

declare(strict_types=1);

namespace Modules\Approval\Enums;

enum ApprovalRecordAction: int
{
    case APPROVE = 1; // 通过
    case REJECT = 2;  // 拒绝

    /**
     * 获取动作对应的步骤状态
     */
    public static function getStepStatusFromAction(?int $action): string
    {
        return match($action) {
            self::APPROVE->value => 'completed',
            self::REJECT->value => 'rejected',
            default => 'pending'
        };
    }
}