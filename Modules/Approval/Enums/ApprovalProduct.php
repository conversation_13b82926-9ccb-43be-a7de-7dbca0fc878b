<?php

declare(strict_types=1);

namespace Modules\Approval\Enums;

/**
 * 审批产品枚举类
 */
enum ApprovalProduct: string
{
    /**
     * 会员审批
     */
    case MEMBER = 'member';
    /**
     * 网页审批
     */
    case PAGE = 'page';

    /**
     * 新闻审批
     */
    case NEWS = 'news';


    /**
     * 获取产品名称
     * @return string
     */
    public function getName(): string
    {
        return match($this) {
            self::MEMBER => '会员审批',
            self::PAGE => '网页审批',
            self::NEWS => '新闻审批',
        };
    }

    /**
     * 根据语言获取产品名称
     * @param string $locale 语言代码
     * @return string
     */
    public function getNameByLocale(string $locale = 'zh_CN'): string
    {
        return match($this) {
            self::MEMBER => match($locale) {
                'zh_HK' => '會員審批',
                'en' => 'Member Approval',
                default => '会员审批',
            },
            self::PAGE => match($locale) {
                'zh_HK' => '網頁審批',
                'en' => 'Page Approval',
                default => '网页审批',
            },
            self::NEWS => match($locale) {
                'zh_HK' => '新聞審批',
                'en' => 'News Approval',
                default => '新闻审批',
            },
        };
    }

    /**
     * 获取所有产品列表
     * @param string $locale 语言代码，默认为简体中文
     * @return array
     */
    public static function getList(string $locale = 'zh_CN'): array
    {
        return array_map(function($case) use ($locale) {
            return [
                'key' => $case->value,
                'name' => $case->getNameByLocale($locale)
            ];
        }, self::cases());
    }

    /**
     * 获取产品类别
     * @return string
     */
    public function getCategory(): string
    {
        switch ($this) {
            case self::MEMBER:
                return '会员';
            case self::PAGE:
            case self::NEWS:
                return 'CMS';
            default:
                return '其他';
        }

    }
}
