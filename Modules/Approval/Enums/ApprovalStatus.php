<?php

declare(strict_types=1);

namespace Modules\Approval\Enums;

enum ApprovalStatus: int
{
    case PENDING = 0;    // 待审批
    case PROCESSING = 1; // 审批中
    case APPROVED = 2;   // 已通过
    case REJECTED = 3;   // 已拒绝

    /**
     * 获取状态文本
     */
    public function getText(): string
    {
        return match($this) {
            self::PENDING => '待审批',
            self::PROCESSING => '审批中',
            self::APPROVED => '已通过',
            self::REJECTED => '已拒绝'
        };
    }

    /**
     * 从整数值获取状态文本
     */
    public static function getTextFromValue(?int $value): string
    {
        return self::tryFrom($value)?->getText() ?? '未知状态';
    }
}