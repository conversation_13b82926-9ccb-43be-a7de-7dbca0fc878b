<?php

declare(strict_types=1);

namespace Modules\Approval\Domain\Repositories;

use Modules\Approval\Models\ApprovalReviewMembers;
use Modules\Approval\Enums\ApprovalErrorCode;
use Bingo\Exceptions\BizException;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

/**
 * 审批角色成员仓储
 */
final class ApprovalMemberRepository
{
    /**
     * 创建成员
     *
     * @param array $data
     * @return ApprovalReviewMembers
     */
    public function create(array $data): ApprovalReviewMembers
    {
        return ApprovalReviewMembers::create($data);
    }

    /**
     * 更新成员
     *
     * @param ApprovalReviewMembers $member
     * @param array $data
     * @return bool
     */
    public function update(ApprovalReviewMembers $member, array $data): bool
    {
        return $member->update($data);
    }

    /**
     * 根据ID查找成员
     *
     * @param int $id
     * @return ApprovalReviewMembers|null
     */
    public function find(int $id): ?ApprovalReviewMembers
    {
        return ApprovalReviewMembers::find($id);
    }

    /**
     * 根据ID查找成员，如果不存在则抛出异常
     *
     * @param int $id
     * @return ApprovalReviewMembers
     * @throws BizException
     */
    public function findOrFail(int $id): ApprovalReviewMembers
    {
        $member = $this->find($id);
        
        if (!$member) {
            BizException::throws(ApprovalErrorCode::MEMBER_NOT_FOUND,ApprovalErrorCode::MEMBER_NOT_FOUND->message());
        }
        
        return $member;
    }

    /**
     * 删除成员
     *
     * @param ApprovalReviewMembers $member
     * @return bool
     */
    public function delete(ApprovalReviewMembers $member): bool
    {
        return $member->delete();
    }

    /**
     * 根据审核组ID分页获取成员列表
     */
    public function paginateByGroupId(int $groupId, int $page, int $limit, ?string $keyword = null): LengthAwarePaginator
    {
        $query = ApprovalReviewMembers::query()
            ->select(['id', 'group_id', 'name', 'position', 'email', 'is_enabled', 'created_at']) // 只查询需要的字段
            ->where('group_id', $groupId)
            ->where('deleted_at', 0);

        if ($keyword) {
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('email', 'like', "%{$keyword}%")
                  ->orWhere('position', 'like', "%{$keyword}%");
            });
        }

        // 添加索引字段排序
        $query->orderBy('id', 'desc');

        return $query->paginate($limit, ['*'], 'page', $page);
    }

    /**
     * 根据ID分页获取成员列表
     */
    public function paginate(int $page, int $limit, ?string $keyword = null): LengthAwarePaginator
    {
        $query = ApprovalReviewMembers::query()
            ->select(['id', 'group_id', 'name', 'position', 'email', 'is_enabled', 'created_at']) // 只查询需要的字段
            ->where('deleted_at', 0);

        if ($keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                    ->orWhere('email', 'like', "%{$keyword}%")
                    ->orWhere('position', 'like', "%{$keyword}%");
            });
        }

        // 添加索引字段排序
        $query->orderBy('id', 'desc');

        return $query->paginate($limit, ['*'], 'page', $page);
    }

    /**
     * 批量创建成员
     *
     * @param array $members
     * @return bool
     */
    public function createMany(array $members): bool
    {
        return ApprovalReviewMembers::insert($members);
    }

    /**
     * 获取已存在的邮箱
     */
    public function getExistingEmails(int $groupId, array $emails): array
    {
        return ApprovalReviewMembers::where('group_id', $groupId)
            ->whereIn('email', $emails)
            ->where('deleted_at', 0)
            ->pluck('email')
            ->toArray();
    }
    /**
     * 检查用户是否在指定的审批组中
     */
    public function isUserInAnyGroup(array $groupIds, int $userId): bool
    {
        if (empty($groupIds)) {
            return false;
        }

        return ApprovalReviewMembers::query()
            ->whereIn('group_id', array_unique($groupIds))
            ->where('user_id', $userId)
            ->where('is_enabled', 1)
            ->exists();
    }
    /**
     * 获取用户在指定审批组中的成员信息
     */
    public function findUserInGroups(array $groupIds, int $userId)
    {
        return ApprovalReviewMembers::query()
            ->whereIn('group_id', array_unique($groupIds))
            ->where('user_id', $userId)
            ->where('is_enabled', 1)
            ->first();
    }

    /**
     * 获取指定审批组中所有成员
     * @param int $groupId
     */
    public function getMemberByGroupIds(array $groupIds) 
    {
        return ApprovalReviewMembers::query()
            ->whereIn('group_id', $groupIds)
            ->where('is_enabled', 1)
            ->get();
    }
}