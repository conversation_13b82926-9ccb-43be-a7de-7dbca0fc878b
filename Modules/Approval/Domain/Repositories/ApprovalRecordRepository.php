<?php

declare(strict_types=1);

namespace Modules\Approval\Domain\Repositories;

use Modules\Approval\Models\ApprovalRecord;
use Illuminate\Support\Facades\Auth;
use Modules\Approval\Models\ApprovalReviewMembers;
use Modules\Approval\Models\ApprovalSteps;
use Modules\Approval\Enums\ApprovalProduct;

/**
 * 审批记录仓储类
 */
final class ApprovalRecordRepository
{
    /**
     * 创建审批记录
     * @param array $data
     * @return ApprovalRecord
     */
    public function create(array $data): ApprovalRecord
    {
        return ApprovalRecord::create($data);
    }

    /**
     * 根据步骤和记录ID查找审批记录
     * @param int $stepId
     * @param int $recordId
     * @return ApprovalRecord|null
     */
    public function findByStepAndRecord(int $stepId, int $recordId): ?ApprovalRecord
    {
        return ApprovalRecord::where('step_id', $stepId)
            ->where('record_id', $recordId)
            ->where('deleted_at', 0)
            ->first();
    }

    /**
     * 根据ID查找审批记录
     * @param int $id
     * @return ApprovalRecord|null
     */
    public function find(int $id): ?ApprovalRecord
    {
        return ApprovalRecord::find($id);
    }

     /**
     * 根据ID查找审批记录
     * @param int $id
     * @return ApprovalRecord|null
     */
    public function findNotRejected(int $id): ?ApprovalRecord
    {
        return ApprovalRecord::where('id', $id)
            ->where('rejected_at', 0)
            ->first();
    }

    /**
     * 根据ID查找审批记录,不存在则抛出异常
     * @param int $id
     * @return ApprovalRecord
     */
    public function findOrFail(int $id): ApprovalRecord
    {
        return ApprovalRecord::findOrFail($id);
    }

    /**
     * 更新审批记录
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool
    {
        return ApprovalRecord::where('id', $id)->update($data);
    }

    /**
     * 删除审批记录
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        return ApprovalRecord::where('id', $id)->delete();
    }

    /**
     * 根据产品表名和流程ID查询审核记录
     * @param string $productTable 产品表名
     * @param int $flowId 流程ID
     * @return ApprovalRecord|null
     */
    public function findByProductAndFlowNotRejected(string $productKey, int $flowId): ?ApprovalRecord
    {
        return ApprovalRecord::query()
            ->where('product_key', $productKey)
            ->where('flow_id', $flowId)
            ->where('rejected_at', 0)
            ->first();
    }

    /**
     * 获取审批记录列表
     * @param array $params 查询参数
     * @return array
     */
    public function getApprovalRecordList(array $params): array
    {
        $query = ApprovalRecord::query();

        // 构建查询条件
        if (!empty($params['keyword'])) {
            $query->where(function ($q) use ($params) {
                $q->where('title', 'like', "%{$params['keyword']}%")
                  ->orWhere('description', 'like', "%{$params['keyword']}%");
            });
        }

        if (isset($params['is_enabled'])) {
            $query->where('is_enabled', $params['is_enabled']);
        }

        if (!empty($params['start_date'])) {
            $query->where('created_at', '>=', $params['start_date']);
        }

        if (!empty($params['end_date'])) {
            $query->where('created_at', '<=', $params['end_date']);
        }

        // 排序
        $sortField = $params['sort_field'] ?? 'id';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->orderBy($sortField, $sortOrder);

        // 分页
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 15;
        $items = $query->paginate($limit, ['*'], 'page', $page);

        return [
            'items' => $items->items(),
            'total' => $items->total(),
            'page' => $items->currentPage(),
            'limit' => $items->perPage()
        ];
    }


    /**
     * 获取审批记录及其关联的流程设置和步骤信息
     * 
     * @param int $id 审批记录ID
     * @return ApprovalRecord|null 返回审批记录对象,未找到记录时返回null
     */
    public function getRecordWithFlowAndSettings(int $id): ?ApprovalRecord
    {
        // 获取审批记录
        $record = ApprovalRecord::with(['settings', 'step'])
            ->find($id);

        if (!$record) {
            return null;
        }

        return $record;
    }

    /**
     *  获取审批进行中的数量
     * 
     * @return int 审批进行中的数量
     */
    public function countWithProcessing(): int
    {
        return ApprovalRecord::query()
            ->selectRaw('COUNT(id) as count')
            ->whereIn('status', [ApprovalRecord::STATUS_PENDING, ApprovalRecord::STATUS_PROCESSING])
            ->first()
            ->count();
    }

    /**
     * 统计超时的审批记录数量
     * 
     * @return int 超时记录数量
     */
    public function countTimeout(): int
    {
        return ApprovalRecord::query()
            ->whereIn('status', [ApprovalRecord::STATUS_PENDING, ApprovalRecord::STATUS_PROCESSING])
            ->leftJoin('approval_flow_settings', 'approval_records.flow_id', '=', 'approval_flow_settings.flow_id')
            ->where('approval_flow_settings.allow_planned_date', 1)
            ->whereNotNull('approval_flow_settings.planned_date')
            ->where('approval_flow_settings.planned_date', '<', date('Y-m-d H:i:s'))
            ->count('approval_records.id');
    }

    /**
     * 统计待发布的审批记录数量
     * 
     * @return int 待发布记录数量
     */
    public function countPendingPublish(): int
    {
       return 0;
    }
   

    /**
     * 获取需要用户审批的记录
     * 
     * @param int $page 页码
     * @param int $limit 每页记录数
     * @param string $keyword 关键词
     * @param array $status 状态
     * @return array 用户需审批的记录
     */
    public function getRecordsByUserApproval(int $page, int $limit,string $keyword = '',$status = []): array
    {
        $userId =  Auth::guard()->id() ?? 0;
        if(empty($userId)){
            return [];
        }

        // 获取用户所在的审批组
        // 查询用户所在的审批组成员记录
        $approvalGroupIds = ApprovalReviewMembers::query()
            ->select('group_id')
            ->where('user_id', $userId)
            ->get()
            ->pluck('group_id')
            ->unique()
            ->toArray();
           
        // 如果用户不在任何审批组中，返回空数组
        if (empty($approvalGroupIds)) {
            return [];
        }

        // 查询包含这些审批组的步骤
        $flowIds = ApprovalSteps::query()
            ->where(function($query) use ($approvalGroupIds) {
                foreach ($approvalGroupIds as $groupId) {
                    $query->orWhereJsonContains('group_ids', (string)$groupId)
                          ->orWhereJsonContains('group_ids', (int)$groupId);
                }
            })
            ->select('id', 'flow_id')
            ->get()->pluck('flow_id')->unique()->toArray();
    
        // 如果没有找到相关流程，返回空数组
        if (empty($flowIds)) {
            return [];
        }

        $statusList = [ApprovalRecord::STATUS_PENDING, ApprovalRecord::STATUS_PROCESSING];
        if(!empty($status)){
            $statusList = $status;
        }

        $offset = ($page - 1) * $limit; 
        $query = ApprovalRecord::query()->with('settings')
            ->whereIn('status', $statusList)
            ->whereIn('flow_id', $flowIds)
            ->limit($limit)
            ->offset($offset);

        if(!empty($keyword)){
            $query->where('product_name', 'like', "%{$keyword}%");
        }

        $records = $query->get()
            ->map(function ($record) {
                return [
                    'id' => $record->id,
                    'product_key' => $record->product_key,
                    'product_name' => ApprovalProduct::from($record->product_key)->getName(),
                    'product_category' => ApprovalProduct::from($record->product_key)->getCategory(),
                    'product_id' => $record->product_id,
                    'status' => $record->status,
                    'status_text' => ApprovalRecord::getStatusText($record->status),
                    'created_at' => date('Y-m-d H:i:s', $record->created_at),
                    'updated_at' => date('Y-m-d H:i:s', $record->updated_at),
                    'expire_time' => !empty($record->settings->planned_date) ? date('Y-m-d H:i:s', strtotime($record->settings->planned_date)) : '',
                ];
            })
            ->toArray();
        
        return $records;
    }

    /**
     * 获取用户已发布的记录
     * 
     * @param int $page 页码
     * @param int $limit 每页记录数
     * @param string $keyword 关键词
     * @param array $status 状态
     * @return array 用户已发布的记录
     */
    public function getRecordsByUserPublished(int $page, int $limit,string $keyword = '',$status = []): array
    {
        $userId =  Auth::guard()->id() ?? 0;
        if(empty($userId)){
            return [];
        }

        // 构建查询条件
        $offset = ($page - 1) * $limit;
        $query = ApprovalRecord::query()
            ->where('creator_id', $userId);

        if(!empty($status)){    
            $query->whereIn('status', $status);
        }

        if(!empty($keyword)){
            $query->where('product_name', 'like', "%{$keyword}%");
        }

        $records = $query->with('settings')
        ->limit($limit)
        ->offset($offset)
        ->get()
        ->map(function ($record) {
            return [
                'id' => $record->id,
                'product_key' => $record->product_key,
                'product_name' => ApprovalProduct::from($record->product_key)->getName(),
                'product_category' => ApprovalProduct::from($record->product_key)->getCategory(),
                'product_id' => $record->product_id,
                'status' => $record->status,
                'status_text' => ApprovalRecord::getStatusText($record->status),
                'created_at' => date('Y-m-d H:i:s', $record->created_at),
                'updated_at' => date('Y-m-d H:i:s', $record->updated_at),
                'expire_time' => !empty($record->settings->planned_date) ? date('Y-m-d H:i:s', strtotime($record->settings->planned_date)) : '',
            ];
        })->toArray();

        return $records;
    }

    /**
     * 获取用户已完成的记录
     * 
     * @param int $page 页码
     * @param int $limit 每页记录数
     * @param string $keyword 关键词
     * @return array 用户已完成的记录
     */
    public function getCompletedRecordsWithUser(int $page, int $limit,string $keyword = ''): array
    {
        $records = $this->getRecordsByUserApproval($page, $limit, $keyword, [ApprovalRecord::STATUS_APPROVED, ApprovalRecord::STATUS_REJECTED,ApprovalRecord::STATUS_TIMEOUT]);
        
        $publishedRecords = $this->getRecordsByUserPublished($page, $limit, $keyword, [ApprovalRecord::STATUS_APPROVED, ApprovalRecord::STATUS_REJECTED,ApprovalRecord::STATUS_TIMEOUT]);
        $records = array_merge($records, $publishedRecords);

        return $records;
    }
}
