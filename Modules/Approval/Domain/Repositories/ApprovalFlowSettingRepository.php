<?php

declare(strict_types=1);

namespace Modules\Approval\Domain\Repositories;

use Modules\Approval\Models\ApprovalSettings;
use Modules\Approval\Enums\ApprovalErrorCode;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\DB;

/**
 * 审批流程设置仓储
 */
final class ApprovalFlowSettingRepository
{
    public function __construct(
        private readonly ApprovalSettings $model
    ) {
    }
    /**
     * 获取流程设置
     *
     * @param int $flowId
     * @return ApprovalFlowSettings|null
     */
    public function getSettings(int $flowId): ?ApprovalSettings
    {
        return $this->model->query()
            ->where('flow_id', $flowId)
            ->first();
    }

    /**
     * 获取默认设置
     *
     * @return array
     */
    public function getDefaultSettings(): array
    {
        return [
            'lock_published_content' => false,
            'allow_attachments' => true,
            'allow_planned_date' => true,
            'enable_time_limit' => false,
            'allow_cancel_pending' => true,
            'allow_permission_extend' => false,
            'notification_rules' => [],
            'planned_date' => null
        ];
    }

    /**
     * 查询审批流的设置
     *
     * @param int $flowId
     * @return array|null
     */
    public function findByFlowId(int $flowId): ?array
    {
        $settings = $this->model->query()
            ->where('flow_id', $flowId)
            ->first();

        if (!$settings) {
            return null;
        }

        return [
            'lock_published_content' => $settings->lock_published_content,
            'allow_attachments' => $settings->allow_attachments,
            'allow_planned_date' => $settings->allow_planned_date,
            'enable_time_limit' => $settings->enable_time_limit,
            'allow_cancel_pending' => $settings->allow_cancel_pending,
            'allow_permission_extend' => $settings->allow_permission_extend,
            'notification_rules' => $settings->notification_rules,
            'planned_date' => $settings->planned_date
        ];
    }


    /**
     * 创建审批流程设置
     *
     * @param array $data
     * @return bool
     */
    public function create(array $data): array
    {
        $setting = $this->model->create([
            'flow_id' => $data['flow_id'],
            'lock_published_content' => $data['settings']['lock_published_content'] ?? false,
            'allow_attachments' => $data['settings']['allow_attachments'] ?? true,
            'allow_planned_date' => $data['settings']['allow_planned_date'] ?? true,
            'enable_time_limit' => $data['settings']['enable_time_limit'] ?? false,
            'allow_cancel_pending' => $data['settings']['allow_cancel_pending'] ?? true,
            'allow_permission_extend' => $data['settings']['allow_permission_extend'] ?? false,
            'notification_rules' => $data['settings']['notification_rules'] ?? [],
            'planned_date' => $data['settings']['planned_date'] ?? null,
            'created_at' => time(),
            'updated_at' => time()
        ]);

        return [
            'lock_published_content' => $setting->lock_published_content,
            'allow_attachments' => $setting->allow_attachments,
            'allow_planned_date' => $setting->allow_planned_date,
            'enable_time_limit' => $setting->enable_time_limit,
            'allow_cancel_pending' => $setting->allow_cancel_pending,
            'allow_permission_extend' => $setting->allow_permission_extend,
            'notification_rules' => $setting->notification_rules,
            'planned_date' => $setting->planned_date
        ];
    }

    /**
     * 更新审批流程设置
     *
     * @param int $flowId
     * @param array $settings
     * @return bool
     */
    public function update(int $flowId, array $settings): array
    {
        $this->model->newQuery()
            ->where('flow_id', $flowId)
            ->update([
                'lock_published_content' => $settings['lock_published_content'] ?? false,
                'allow_attachments' => $settings['allow_attachments'] ?? true,
                'allow_planned_date' => $settings['allow_planned_date'] ?? true,
                'enable_time_limit' => $settings['enable_time_limit'] ?? false,
                'allow_cancel_pending' => $settings['allow_cancel_pending'] ?? true,
                'allow_permission_extend' => $settings['allow_permission_extend'] ?? false,
                'notification_rules' => $settings['notification_rules'] ?? [],
                'planned_date' => $settings['planned_date'] ?? null,
                'updated_at' => time()
            ]);

        $setting = $this->model->newQuery()
            ->where('flow_id', $flowId)
            ->first();

        return [
            'lock_published_content' => $setting->lock_published_content,
            'allow_attachments' => $setting->allow_attachments,
            'allow_planned_date' => $setting->allow_planned_date,
            'enable_time_limit' => $setting->enable_time_limit,
            'allow_cancel_pending' => $setting->allow_cancel_pending,
            'allow_permission_extend' => $setting->allow_permission_extend,
            'notification_rules' => $setting->notification_rules,
            'planned_date' => $setting->planned_date
        ];
    }
}
