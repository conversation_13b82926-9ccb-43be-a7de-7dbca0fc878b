<?php

declare(strict_types=1);

namespace Modules\Approval\Domain\Repositories;

use Modules\Approval\Models\ApprovalProductFlowsRecords;
use Modules\Approval\Domain\Repositories\ApprovalMemberRepository;

class ApprovalProductRecordRepository
{
    /**
     * 获取步骤的审批记录
     */
    public function findByStepAndProduct(int $stepId, string $productTable, int $productId): ?ApprovalProductFlowsRecords
    {
        return ApprovalProductFlowsRecords::query()
            ->where('step_id', $stepId)
            ->where('product_table', $productTable)
            ->where('product_id', $productId)
            ->orderBy('id', 'desc')
            ->first();
    }
    /**
     * 创建审批记录
     */
    public function create(array $data): ApprovalProductFlowsRecords
    {
        return ApprovalProductFlowsRecords::create([
            'product_table' => $data['product_table'],
            'product_id' => $data['product_id'],
            'flow_id' => $data['flow_id'],
            'step_id' => $data['step_id'],
            'group_id' => $data['group_id'],
            'user_id' => $data['user_id'],
            'action' => $data['action'],
            'remark' => $data['remark'],
            'creator_id' => $data['creator_id'],
            'created_at' => $data['created_at'],
            'updated_at' => $data['updated_at']
        ]);
    }
}