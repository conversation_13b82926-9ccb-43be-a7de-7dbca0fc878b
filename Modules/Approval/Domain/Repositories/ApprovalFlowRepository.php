<?php

declare(strict_types=1);

namespace Modules\Approval\Domain\Repositories;

use Modules\Approval\Models\ApprovalFlows;
use Modules\Approval\Models\ApprovalSteps;
use Modules\Approval\Models\ApprovalProductFlows;
use Modules\Approval\Models\ApprovalRecord;
use Modules\Approval\Enums\ApprovalErrorCode;
use Bingo\Exceptions\BizException;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

/**
 * 审批流程仓储
 */
final class ApprovalFlowRepository
{
    /**
     * 创建流程
     *
     * @param array $data
     * @return ApprovalFlows
     */
    public function create(array $data): ApprovalFlows
    {
        return ApprovalFlows::create($data);
    }

    /**
     * 更新流程
     *
     * @param ApprovalFlows $flow
     * @param array $data
     * @return bool
     */
    public function update(ApprovalFlows $flow, array $data): bool
    {
        return $flow->update($data);
    }

    /**
     * 根据ID查找流程
     *
     * @param int $id
     * @return ApprovalFlows|null
     */
    public function find(int $id): ?ApprovalFlows
    {
        return ApprovalFlows::find($id);
    }

    /**
     * 根据ID查找流程，如果不存在则抛出异常
     *
     * @param int $id
     * @return ApprovalFlows
     * @throws BizException
     */
    public function findOrFail(int $id): ApprovalFlows
    {
        $flow = $this->find($id);
        
        if (!$flow) {
            BizException::throws(ApprovalErrorCode::FLOW_NOT_FOUND,ApprovalErrorCode::FLOW_NOT_FOUND->message());
        }
        
        return $flow;
    }

    /**
     * 删除流程
     *
     * @param ApprovalFlows $flow
     * @return bool
     */
    public function delete(ApprovalFlows $flow): bool
    {
        return $flow->delete();
    }

    /**
     * 分页获取流程列表
     *
     * @param array $conditions 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return LengthAwarePaginator
     */
    public function paginateFlows(array $conditions, int $page, int $limit): LengthAwarePaginator
    {
        $query = ApprovalFlows::query();

        // 关键词搜索
        if (!empty($conditions['keyword'])) {
            $query->where('name', 'like', '%' . $conditions['keyword'] . '%');
        }

        // 状态筛选
        if (isset($conditions['status'])) {
            $query->where('status', $conditions['status']);
        }

        // 排序
        $sortField = $conditions['sort_field'] ?? 'id';
        $sortOrder = $conditions['sort_order'] ?? 'desc';
        $query->orderBy($sortField, $sortOrder);

        // 关联查询步骤数据
        $query->with(['steps' => function($query) {
            $query->orderBy('sort', 'asc');
        }]);

        //关联审批流程settings
        $query->with(['settings']);

        
        return $query->paginate($limit, ['*'], 'page', $page);
    }

    /**
     * 更新流程和步骤
     *
     * @param ApprovalFlows $flow 流程对象
     * @param array $flowData 流程数据
     * @param array $steps 步骤数据
     * @return array
     */
    public function updateFlowAndSteps(ApprovalFlows $flow, array $flowData, array $steps): array
    {
        try {
            DB::beginTransaction();

            // 更新流程基本信息
            $flow->fill($flowData);
            $flow->save();

            $formattedSteps = [];

            // 如果有步骤数据则更新步骤
            if (!empty($steps)) {
                // 获取当前流程所有步骤
                $existingSteps = $flow->steps()->get();
                $existingStepCodes = $existingSteps->pluck('step_code')->toArray();
                
                // 记录传入的step codes用于后续删除不在传入列表中的步骤
                $newStepCodes = array_column($steps, 'stepCode');

                foreach ($steps as $step) {
                    // 根据step_code查找已存在的步骤
                    $flowStep = $flow->steps()->where('step_code', $step['stepCode'])->first();

                    if ($flowStep) {
                        // 更新已有步骤,不更新stepcode
                        $flowStep->update([
                            'name' => $step['name'],
                            'group_ids' => $step['groupIds'],
                            'sort' => $step['sort'],
                            'reject_to_draft' => $step['rejectToDraft'] ?? 0
                        ]);
                        $formattedSteps[] = $flowStep->toArray();
                    } else {
                        // 检查step_code是否已存在
                        $duplicateCheck = ApprovalSteps::where('step_code', $step['stepCode'])->exists();

                        if ($duplicateCheck) {
                            throw new \Exception("Step code {$step['stepCode']} already exists");
                        }

                        // 新增步骤
                        $newStep = $flow->steps()->create([
                            'name' => $step['name'],
                            'step_code' => $step['stepCode'],
                            'group_ids' => $step['groupIds'],
                            'sort' => $step['sort'],
                            'reject_to_draft' => $step['rejectToDraft'] ?? 0
                        ]);
                        $formattedSteps[] = $newStep->toArray();
                    }
                }

                // 删除不在传入步骤列表中的已有步骤
                foreach ($existingSteps as $existingStep) {
                    if (!in_array($existingStep->step_code, $newStepCodes)) {
                        $existingStep->forceDelete();
                    }
                }
            } else {
                // 如果没有传入步骤数据，删除所有已有步骤
                $flow->steps()->forceDelete();
            }

            DB::commit();

            return [
                'id' => $flow->id,
                'name' => $flow->name,
                'description' => $flow->description,
                'scope' => $flow->scope,
                'settings' => $flow->settings,
                'is_enabled' => $flow->is_enabled,
                'steps' => $formattedSteps
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 检查流程是否有产品关联
     *
     * @param int $flowId
     * @return bool
     */
    public function hasProductAssociation(int $flowId): bool
    {
        return ApprovalProductFlows::query()
            ->where('flow_id', $flowId)
            ->exists();
    }

    /**
     * 检查流程是否有审批记录
     *
     * @param int $flowId
     * @return bool
     */
    public function hasApprovalRecords(int $flowId): bool
    {
        return ApprovalRecord::query()
            ->where('flow_id', $flowId)
            ->exists();
    }

    /**
     * 检查流程是否有未完成的审批记录
     *
     * @param int $flowId
     * @return bool
     */
    public function hasUnfinishedApprovalRecords(int $flowId): bool
    {
        return ApprovalRecord::query()
            ->where('flow_id', $flowId)
            ->whereIn('status', [ApprovalRecord::STATUS_PENDING, ApprovalRecord::STATUS_PROCESSING]) // 0-待审批，1-审批中
            ->exists();
    }

    /**
     * 检查流程是否有未完成的审批记录
     *
     * @param int $flowId
     * @return bool
     */
    public function hasUnfinishedApprovalsExceptFlow(int $flowId,string $scope): bool
    {
        return ApprovalRecord::query()
            ->where('flow_id', '!=', $flowId)
            ->whereHas('flow', function($query) use ($scope) {
                $query->where('scope', $scope)
                ->where('is_enabled', true);
            })
            ->whereIn('status', [ApprovalRecord::STATUS_PENDING, ApprovalRecord::STATUS_PROCESSING]) // 0-待审批，1-审批中
            ->exists();
    }

    /**
     * 删除流程及其关联数据
     *
     * @param ApprovalFlows $flow
     * @return bool
     */
    public function deleteWithRelations(ApprovalFlows $flow): bool
    {
        try {
            DB::beginTransaction();
            
            // 删除流程相关的步骤
            $flow->steps()->delete();
            
            // 删除流程相关的设置
            $flow->settings()->delete();
            
            // 删除流程本身
            $result = $flow->delete();
            
            DB::commit();
            return $result;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 关闭相同scope的其他流程
     *
     * @param int $currentFlowId 当前流程ID
     * @param string $scope 流程scope
     * @return void
     */
    public function closeOtherFlows(int $currentFlowId, string $scope): void
    {
        ApprovalFlows::where('scope', $scope)
            ->where('id', '!=', $currentFlowId)
            ->where('is_enabled', true)
            ->update(['is_enabled' => false]);
    }

    /**
     * 复制流程及其关联数据
     *
     * @param ApprovalFlows $sourceFlow 源流程
     * @return ApprovalFlows 新流程
     * @throws \Exception
     */
    public function copyFlowWithRelations(ApprovalFlows $sourceFlow): ApprovalFlows
    {
        try {
            DB::beginTransaction();
            
            // 复制流程基本信息
            $newFlow = $sourceFlow->replicate();
            $newFlow->name = $sourceFlow->name . ' - 副本';
            $newFlow->creator_id = Auth::guard()->user()->id ?? 0;
            $newFlow->created_at = time();
            $newFlow->updated_at = time();
            $newFlow->save();
            
            // 复制流程步骤
            $sourceSteps = $sourceFlow->steps()->orderBy('sort')->get();
            foreach ($sourceSteps as $step) {
                $newStep = $step->replicate();
                $newStep->flow_id = $newFlow->id;
                $newStep->created_at = time();
                $newStep->updated_at = time();
                $newStep->save();
            }
            
            // 复制流程设置
            $sourceSettings = $sourceFlow->settings;
            if ($sourceSettings) {
                $newSettings = $sourceSettings->replicate();
                $newSettings->flow_id = $newFlow->id;
                $newSettings->created_at = time();
                $newSettings->updated_at = time();
                $newSettings->save();
            }
            
            DB::commit();
            return $newFlow;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 切换流程启用状态
     *
     * @param ApprovalFlows $flow
     * @return bool
     */
    public function switchStatus(ApprovalFlows $flow): bool
    {
        $flow->is_enabled = !$flow->is_enabled;
        return $flow->save();
    }
}