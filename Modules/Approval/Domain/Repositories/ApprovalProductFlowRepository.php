<?php

declare(strict_types=1);

namespace Modules\Approval\Domain\Repositories;

use Modules\Approval\Models\ApprovalProductFlows;
use Modules\Approval\Enums\ApprovalErrorCode;
use Bingo\Exceptions\BizException;

/**
 * 产品审批流程仓储
 */
final class ApprovalProductFlowRepository
{
    /**
     * 创建产品审批关联
     */
    public function create(array $data): ApprovalProductFlows
    {
        return ApprovalProductFlows::create($data);
    }

    /**
     * 根据ID查找产品审批关联
     */
    public function findOrFail(int $id): ApprovalProductFlows
    {
        $productFlow = ApprovalProductFlows::find($id);
        
        if (!$productFlow) {
            throw BizException::throws(ApprovalErrorCode::PRODUCT_FLOW_NOT_FOUND,ApprovalErrorCode::PRODUCT_FLOW_NOT_FOUND->message());
        }
        
        return $productFlow;
    }

    /**
     * 删除产品审批关联
     */
    public function delete(ApprovalProductFlows $productFlow): bool
    {
        return $productFlow->delete();
    }

   
    /**
     * 根据产品和流程查找审批关联
     */
    public function findByProductAndFlow(string $productKey, int $flowId): ?ApprovalProductFlows
    {
        return ApprovalProductFlows::query()
            ->where('product_key', $productKey)
            ->where('flow_id', $flowId)
            ->where('deleted_at', 0)
            ->first();
    }
    
    /**
     * 根据产品信息查找审批关联
     * @param string $productTable 产品表名
     * @param int $productId 产品ID
     * @return ApprovalProductFlows|null
     */
    public function findByProduct(string $productKey): ?ApprovalProductFlows
    {
        return ApprovalProductFlows::query()
            ->where('product_key', $productKey)
            ->where('deleted_at', 0)
            ->first();
    }
}