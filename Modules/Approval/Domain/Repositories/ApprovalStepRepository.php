<?php

declare(strict_types=1);

namespace Modules\Approval\Domain\Repositories;

use Modules\Approval\Models\ApprovalSteps;
use Modules\Approval\Enums\ApprovalErrorCode;
use Bingo\Exceptions\BizException;

/**
 * 审批流程步骤仓储
 */
final class ApprovalStepRepository
{
    /**
     * 创建步骤
     *
     * @param array $data
     * @return ApprovalSteps
     */
    public function create(array $data): ApprovalSteps
    {
        return ApprovalSteps::create($data);
    }

    /**
     * 更新步骤
     *
     * @param ApprovalSteps $step
     * @param array $data
     * @return bool
     */
    public function update(ApprovalSteps $step, array $data): bool
    {
        return $step->update($data);
    }

    /**
     * 根据ID查找步骤
     *
     * @param int $id
     * @return ApprovalSteps|null
     */
    public function find(int $id): ?ApprovalSteps
    {
        return ApprovalSteps::find($id);
    }

    /**
     * 根据ID查找步骤，如果不存在则抛出异常
     *
     * @param int $id
     * @return ApprovalSteps
     * @throws BizException
     */
    public function findOrFail(int $id): ApprovalSteps
    {
        $step = $this->find($id);
        
        if (!$step) {
            BizException::throws(ApprovalErrorCode::STEP_NOT_FOUND,ApprovalErrorCode::STEP_NOT_FOUND->message());
        }
        
        return $step;
    }

    /**
     * 删除步骤
     *
     * @param ApprovalSteps $step
     * @return bool
     */
    public function delete(ApprovalSteps $step): bool
    {
        return $step->delete();
    }

    /**
     * 根据流程ID获取步骤列表
     *
     * @param int $flowId
     * @return array
     */
    public function getByFlowId(int $flowId): array
    {
        return ApprovalSteps::where('flow_id', $flowId)
            ->orderBy('sort', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * 根据流程ID查找步骤
     *
     * @param int $flowId
     * @return Collection
     */
    public function findByFlowId(int $flowId): array
    {
        return ApprovalSteps::where('flow_id', $flowId)
            ->orderBy('sort', 'asc')
            ->get()
            ->toArray();
    }
} 