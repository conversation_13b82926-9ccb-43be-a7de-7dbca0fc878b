<?php

declare(strict_types=1);

namespace Modules\Approval\Domain\Repositories;

use Modules\Approval\Models\ApprovalReviewGroups;
use Modules\Approval\Models\ApprovalSteps;
use Modules\Approval\Models\ApprovalReviewMembers;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Pagination\LengthAwarePaginator;
use Bingo\Exceptions\BizException;
use Modules\Approval\Enums\ApprovalErrorCode;
use Illuminate\Support\Collection;

final class ApprovalGroupRepository
{
    public function __construct(
        private readonly ApprovalReviewGroups $model,
    ) {
    }

    public function list(array $params): LengthAwarePaginator
    {
        $query = $this->model->newQuery()
            ->select([
                'id',
                'name',
                'code',
                'level',
                'is_inherit',
                'permissions',
                'description',
                'status',
                'created_at',
                'updated_at'
            ])
            ->withCount('members'); // 使用关联计数代替子查询

        // 添加搜索条件,使用索引
        if (!empty($params['keyword'])) {
            $query->where('name', 'like', $params['keyword'] . '%');
        }

        // 排序
        $sortField = $params['sort_field'] ?? 'created_at';
        $sortOrder = $params['sort_order'] ?? 'desc';

        $query->orderBy($sortField, $sortOrder);

        // 使用缓存优化分页
        $limit = $params['limit'] ?? 15;
        $page = $params['page'] ?? 1;

        // 返回 LengthAwarePaginator
        $paginator = $query->paginate($limit, ['*'], 'page', $page);
        
        $paginator->through(function ($item) {
            $item->permissions = is_string($item->permissions) 
                ? json_decode($item->permissions, true) 
                : $item->permissions;
            return $item;
        });

        return $paginator;
    }

    public function find(int $id): ?array
    {
        $group = $this->model->newQuery()
            ->select([
                'id',
                'name',
                'code',
                'description',
                'level',
                'is_inherit',
                'permissions',
                'status',
                'created_at',
                'updated_at'
            ])
            ->with(['members' => function ($query) {
                $query->select([
                    'id',
                    'group_id',
                    'name',
                    'position',
                    'email',
                    'is_enabled',
                    'user_id'
                ]);
            }])
            ->find($id);

        if (!$group) {
            return null;
        }

        $permissions = is_string($group->permissions)
            ? json_decode($group->permissions, true)
            : $group->permissions;

        return [
            'id' => $group->id,
            'name' => $group->name,
            'code' => $group->code,
            'description' => $group->description,
            'level' => $group->level,
            'is_inherit' => $group->is_inherit,
            'permissions' => $permissions ?? [],
            'status' => $group->status,
            'members' => $group->members->map(function ($member) {
                return [
                    'id' => $member->id,
                    'name' => $member->name,
                    'position' => $member->position,
                    'email' => $member->email,
                    'is_enabled' => $member->is_enabled,
                    'user_id' => $member->user_id
                ];
            })->toArray(),
            'created_at' => $group->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $group->updated_at?->format('Y-m-d H:i:s'),
        ];
    }

    public function create(array $data): array
    {
        DB::beginTransaction();
        try {
            $group = $this->model->create([
                'name' => $data['name'],
                'code' => $data['code'] ?? null,
                'description' => $data['description'] ?? null,
                'level' => $data['role_level'] ?? 0,
                'is_inherit' => $data['is_inherit'] ?? false,
                'permissions' => isset($data['permissions']) ? (is_array($data['permissions']) ? json_encode($data['permissions']) : $data['permissions']) : null,
                'status' => $data['status'] ?? true,
                'creator_id' => $data['creator_id'] ?? Auth::guard()->user()->id ?? 0,
            ]);

            if(empty($group)){
                BizException::throws(ApprovalErrorCode::GROUP_CREATE_FAILED, ApprovalErrorCode::GROUP_CREATE_FAILED->message());
            }

            if (!empty($data['members'])) {
                $currentTimestamp = time();
                $members = collect($data['members'])->map(function ($member) use ($group, $currentTimestamp) {
                    return [
                        'group_id' => $group->id,
                        'name' => $member['name'],
                        'position' => $member['position'] ?? null,
                        'email' => $member['email'],
                        'is_enabled' => $member['is_enabled'] ?? true,
                        'user_id' => $member['user_id'],
                        'created_at' => $currentTimestamp,
                        'updated_at' => $currentTimestamp,
                    ];
                })->toArray();

                DB::table('approval_review_members')->insert($members);
            }

            DB::commit();
            return $this->find($group->id) ?? [];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(int $id, array $data): array
    {
        DB::beginTransaction();
        try {
            $group = $this->model->findOrFail($id);

            // 更新审批组基本信息
            $fields = [
                'name',
                ['code', 'code'],
                ['description', 'description'],
                ['role_level', 'level'], 
                ['is_inherit', 'is_inherit'],
                ['status', 'status'],
                ['permissions', 'permissions', function($value) {
                    return is_array($value) ? json_encode($value) : $value;
                }]
            ];

            $updateData = collect($fields)->reduce(function ($acc, $field) use ($data) {
                if (is_array($field)) {
                    $sourceKey = $field[0];
                    $targetKey = $field[1];
                    if (isset($data[$sourceKey])) {
                        $value = isset($field[2]) ? $field[2]($data[$sourceKey]) : $data[$sourceKey];
                        $acc[$targetKey] = $value;
                    }
                } else {
                    if (isset($data[$field])) {
                        $acc[$field] = $data[$field];
                    }
                }
                return $acc;
            }, []);

            $group->update($updateData);

            if (!empty($data['members'])) {
                $currentTimestamp = time();

                // 获取现有成员
                $existingMembers = DB::table('approval_review_members')
                    ->where('group_id', $id)
                    ->get()
                    ->keyBy('email')
                    ->toArray();

                // 处理新增和更新的成员
                $memberOperations = collect($data['members'])->map(function ($member) use ($group, $currentTimestamp, $existingMembers) {
                    $memberData = [
                        'group_id' => $group->id,
                        'name' => $member['name'],
                        'position' => $member['position'] ?? null,
                        'email' => $member['email'],
                        'is_enabled' => $member['is_enabled'] ?? true,
                        'updated_at' => $currentTimestamp,
                    ];

                    if (isset($existingMembers[$member['email']])) {
                        // 更新已存在的成员
                        return [
                            'type' => 'update',
                            'email' => $member['email'],
                            'data' => $memberData
                        ];
                    } else {
                        // 新增成员
                        $memberData['created_at'] = $currentTimestamp;
                        $memberData['user_id'] = $member['user_id']; // 只在新增时设置user_id
                        return [
                            'type' => 'insert',
                            'data' => $memberData
                        ];
                    }
                });

                // 执行更新操作
                foreach ($memberOperations as $operation) {
                    if ($operation['type'] === 'update') {
                        DB::table('approval_review_members')
                            ->where('group_id', $id)
                            ->where('email', $operation['email'])
                            ->update($operation['data']);
                    } else {
                        DB::table('approval_review_members')->insert($operation['data']);
                    }
                }

                // 删除不在传入参数中的现有成员
                $newMemberUserIds = collect($data['members'])->pluck('user_id')->toArray();
                DB::table('approval_review_members')
                    ->where('group_id', $id)
                    ->whereNotIn('user_id', $newMemberUserIds)
                    ->delete();
            }

            DB::commit();
            return $this->find($group->id) ?? [];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function delete(int $id): void
    {
        $this->model->findOrFail($id)->delete();
    }

    public function batchDelete(array $ids): void
    {
        $this->model->whereIn('id', $ids)->delete();
    }

    /**
     * 判断审批组是否存在
     *
     * @param int $id
     * @return bool
     */
    public function exists(int $id): bool
    {
        return $this->model->where('id', $id)->exists();
    }

    /**
     * 检查审批组是否在使用中
     * @param int $id 审批组ID
     * @return bool
     */
    public function isGroupInUse(int $id): bool
    {
        // 检查审批步骤中是否使用了该审批组
        $isUsedInStep = ApprovalSteps::query()
            ->whereJsonContains('group_ids', $id)
            ->exists();

        return $isUsedInStep;
    }

    /**
     * 删除审批组成员
     * @param int $id 审批组ID
     * @return void
     */
    public function deleteGroupMembers(int $id): void
    {
        ApprovalReviewMembers::query()
            ->where('group_id', $id)
            ->delete();
    }

    /**
     * 根据审批组ID查询审批组
     * @param array $groupIds 审批组ID数组
     * @return Collection
     */
    public function findByGroupIds(array $groupIds): Collection
    {
        return $this->model->whereIn('id', $groupIds)->get();
    }
}
