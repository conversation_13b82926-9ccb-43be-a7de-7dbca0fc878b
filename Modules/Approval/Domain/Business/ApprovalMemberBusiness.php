<?php

declare(strict_types=1);

namespace Modules\Approval\Domain\Business;

use Bingo\Exceptions\BizException;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use Modules\Approval\Domain\Repositories\ApprovalGroupRepository;
use Modules\Approval\Domain\Repositories\ApprovalMemberRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\UploadedFile;
use Modules\Approval\Enums\ApprovalErrorCode;
use PhpOffice\PhpSpreadsheet\IOFactory;
use RuntimeException;


final readonly class ApprovalMemberBusiness
{
   /**
     * @param ApprovalMemberRepository $memberRepository
     * @param ApprovalGroupRepository $groupRepository
     */
    public function __construct(
        private ApprovalMemberRepository $memberRepository,
        private ApprovalGroupRepository  $groupRepository,
    ) {
    }

    /**
     * 批量添加成员
     *
     * @param int $groupId
     * @param array $members
     * @return bool
     * @throws BizException
     */
    public function addMembers(int $groupId, array $members): bool
    {
        // 检查审批组是否存在
        if (!$this->groupRepository->exists($groupId)) {
            BizException::throws(ApprovalErrorCode::GROUP_NOT_FOUND,ApprovalErrorCode::GROUP_NOT_FOUND->message());
        }

        // 过滤出已存在的邮箱
        $existingEmails = $this->memberRepository->getExistingEmails($groupId, array_column($members, 'email'));

        // 过滤掉已存在邮箱的成员
        $members = array_filter($members, function ($member) use ($existingEmails) {
            return empty($member['email']) || !in_array($member['email'], $existingEmails);
        });

        // 如果过滤后没有需要添加的成员,直接返回true
        if (empty($members)) {
            return true;
        }

        // 检查邮箱对应的用户是否存在
        foreach ($members as &$member) {
            if (!empty($member['email'])) {
                $user = DB::table('iam_users')
                    ->where('email', $member['email'])
                    ->where('deleted_at', 0)
                    ->first(['id']);

                if (!$user) {
                    BizException::throws(ApprovalErrorCode::USER_NOT_FOUND,ApprovalErrorCode::USER_NOT_FOUND->message());
                }

                $member['user_id'] = $user->id;
            }
        }
        unset($member);

        $creatorId = Auth::guard()->user()->id ?? 0;
        $data = array_map(function ($member) use ($groupId, $creatorId) {
            return [
                'group_id' => $groupId,
                'name' => $member['name'],
                'position' => $member['position'] ?? '',
                'email' => $member['email'] ?? '',
                'user_id' => $member['user_id'] ?? 0,
                'is_enabled' => $member['is_enabled'],
                'creator_id' => $creatorId,
                'created_at' => time(),
                'updated_at' => time(),
            ];
        }, $members);

        return $this->memberRepository->createMany($data);
    }

    /**
     * 批量导入角色成员
     *
     * @param int $groupId
     * @param UploadedFile $file
     * @return array
     */
    public function importMembers(int $groupId, UploadedFile $file): array
    {
        try {
            DB::beginTransaction();

           // 检查审批组是否存在
            if (!$this->groupRepository->exists($groupId)) {
                BizException::throws(ApprovalErrorCode::GROUP_NOT_FOUND,ApprovalErrorCode::GROUP_NOT_FOUND->message());
            }


            // 解析Excel文件
            // 注意：这里需要根据实际情况实现Excel解析逻辑
            // 假设解析后的数据格式为：[['userId' => 1, 'name' => '张三', 'position' => '经理', 'email' => '<EMAIL>'], ...]
            $excelData = $this->parseExcelFile($file);

            // 导入成员
            $total = count($excelData);

            // 获取所有邮箱
            $emails = array_column($excelData, 'email');

            // 过滤掉空邮箱
            $emails = array_filter($emails);

            // 获取已存在的邮箱
            $existingEmails = $this->memberRepository->getExistingEmails($groupId, $emails);

            // 过滤掉已存在的邮箱数据
            $excelData = array_filter($excelData, function($row) use ($existingEmails) {
                return !empty($row['email']) && !in_array($row['email'], $existingEmails);
            });

            // 如果过滤后没有数据,直接返回
            if (empty($excelData)) {
                return [
                    'success' => false,
                    'total' => $total,
                    'succeeded' => 0,
                    'failed' => $total,
                    'failedRows' => [
                        [
                            'row' => 0,
                            'reason' => '所有邮箱都已存在'
                        ]
                    ]
                ];
            }

            // 获取所有有效的邮箱
            $validEmails = array_filter(array_column($excelData, 'email'));

            // 批量查询用户ID
            $userIds = DB::table('iam_users')
                ->whereIn('email', $validEmails)
                ->where('application_id', 2)
                ->where('deleted_at', 0)
                ->pluck('id', 'email')
                ->toArray();

            // 准备批量插入的数据
            $membersToCreate = [];
            $failedRows = [];
            $succeeded = 0;
            $failed = 0;
            $creatorId = Auth::guard()->user()->id ?? 0;

            foreach ($excelData as $index => $rowData) {
                try {
                    // 验证数据
                    if (empty($rowData['email']) || empty($rowData['name'])) {
                        throw new InvalidArgumentException('邮箱和姓名不能为空');
                    }

                    // 从预先查询的结果中获取用户ID
                    $userId = $userIds[$rowData['email']] ?? 0;
                    if ($userId === 0) {
                        throw new InvalidArgumentException('用户不存在');
                    }

                    $membersToCreate[] = [
                        'group_id' => $groupId,
                        'user_id' => $userId,
                        'name' => $rowData['name'],
                        'position' => $rowData['position'] ?? null,
                        'email' => $rowData['email'],
                        'is_enabled' => true,
                        'creator_id' => $creatorId,
                        'created_at' => time(),
                        'updated_at' => time(),
                    ];
                    $succeeded++;

                } catch (Exception $e) {
                    $failed++;
                    $failedRows[] = [
                        'row' => $index + 2,
                        'reason' => $e->getMessage(),
                    ];
                }
            }

            // 批量创建成员
            if (!empty($membersToCreate)) {
                $result = $this->memberRepository->createMany($membersToCreate);
                if (!$result) {
                    throw new RuntimeException('批量创建成员失败');
                }
            }

            DB::commit();

            return [
                'success' => $succeeded > 0,
                'total' => $total,
                'succeeded' => $succeeded,
                'failed' => $failed,
                'failedRows' => !empty($failedRows) ? $failedRows : null,
            ];
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 更新成员状态
     *
     * @param int $id
     * @param bool $isEnabled
     * @return bool
     * @throws BizException
     */
    public function updateMemberStatus(int $id, bool $isEnabled): bool
    {
        // 获取成员
        $member = $this->memberRepository->findOrFail($id);

        // 更新状态
        return $this->memberRepository->update($member, [
            'is_enabled' => $isEnabled,
        ]);
    }

    /**
     * 删除角色成员
     *
     * @param int $id
     * @return bool
     * @throws BizException
     */
    public function deleteMember(int $id): bool
    {
        // 获取成员
        $member = $this->memberRepository->findOrFail($id);

        // 删除成员
        return $this->memberRepository->delete($member);
    }

    /**
     * 解析Excel文件
     *
     * @param UploadedFile $file
     * @return array
     */
    private function parseExcelFile(UploadedFile $file): array
    {
        // 使用PhpSpreadsheet读取Excel文件
        $spreadsheet = IOFactory::load($file->getPathname());
        $worksheet = $spreadsheet->getActiveSheet();

        // 获取所有数据行
        $rows = $worksheet->toArray();

        // 移除第一行表头
        array_shift($rows);

        // 格式化数据
        $data = [];
        foreach ($rows as $row) {
            if (!empty($row[0])) { // 确保行不为空
                $data[] = [
                    'name' => $row[0], // 姓名
                    'email' => $row[1], // 邮箱
                    'position' => $row[2], // 职位
                ];
            }
        }

        return $data;
    }

    /**
     * 获取成员列表
     *
     * @param int $groupId
     * @param array $params
     * @return LengthAwarePaginator
     */
    public function getMemberListByGroup(int $groupId, array $params = []): LengthAwarePaginator
    {
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 15;
        $keyword = $params['keyword'] ?? null;

        return $this->memberRepository->paginateByGroupId(
            $groupId,
            $page,
            $limit,
            $keyword
        );
    }

    /**
     * 获取成员列表
     *
     * @param array $params
     * @return LengthAwarePaginator
     */
    public function getMemberList(array $params = []): LengthAwarePaginator
    {
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 15;
        $keyword = $params['keyword'] ?? null;

        return $this->memberRepository->paginate(
            $page,
            $limit,
            $keyword
        );
    }
}
