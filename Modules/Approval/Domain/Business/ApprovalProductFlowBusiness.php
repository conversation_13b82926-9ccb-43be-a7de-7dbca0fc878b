<?php

declare(strict_types=1);

namespace Modules\Approval\Domain\Business;


use Bingo\Exceptions\BizException;
use Exception;
use Illuminate\Support\Facades\DB;
use Modules\Approval\Domain\Repositories\ApprovalProductFlowRepository;
use Modules\Approval\Domain\Repositories\ApprovalFlowRepository;
use Modules\Approval\Domain\Repositories\ApprovalMemberRepository;
use Modules\Approval\Enums\ApprovalErrorCode;
use Modules\Approval\Enums\ApprovalStatus;
use Modules\Approval\Enums\ApprovalRecordAction;
use Illuminate\Support\Facades\Auth;
use Modules\Approval\Domain\Repositories\ApprovalRecordRepository;
use Modules\Approval\Models\ApprovalRecord;
use Modules\Approval\Domain\Repositories\ApprovalLogRepository;
use Modules\Approval\Events\ApprovalPassed;
use Modules\Approval\Events\ApprovalRejected;
/**
 * 产品审批流程业务
 */
final readonly class ApprovalProductFlowBusiness
{
    public function __construct(
        private ApprovalProductFlowRepository   $repository,
        private ApprovalFlowRepository          $flowRepository,
        private ApprovalMemberRepository        $memberRepository,
        private ApprovalRecordRepository $recordRepository,
        private ApprovalLogRepository $logRepository
    ) {
    }

    /**
     * 创建产品审批关联
     * @param array $data
     * @return array
     * @throws BizException
     */
    public function createProductFlow(array $data): array
    {
        try {
            DB::beginTransaction();

            // 检查流程是否存在
            $flow = $this->flowRepository->findOrFail($data['flow_id']);

            // 获取流程的第一个步骤
            $firstStep = $flow->steps()->orderBy('sort')->first();
            if (!$firstStep) {
                throw BizException::throws(ApprovalErrorCode::FLOW_STEP_NOT_FOUND,ApprovalErrorCode::FLOW_STEP_NOT_FOUND->message());
            }
            // 检查产品是否已关联该流程
            $existingFlow = $this->repository->findByProductAndFlow(
                $data['product_table'],
                $data['flow_id']
            );
            if ($existingFlow) {
                throw BizException::throws(ApprovalErrorCode::PRODUCT_FLOW_ALREADY_EXISTS,ApprovalErrorCode::PRODUCT_FLOW_ALREADY_EXISTS->message());
            }

            // 创建产品审批关联
            $productFlow = $this->repository->create([
                'product_table' => $data['product_table'], // 例如：'products'
                'flow_id' => $data['flow_id'],
                'creator_id' => Auth::guard()->user()->id ?? 0,
                'created_at' => time(),
                'updated_at' => time()
            ]);

            DB::commit();

            return [
                'id' => $productFlow->id,
                'product_table' => $productFlow->product_table,
                'flow_id' => $productFlow->flow_id,
                'creator_id' => $productFlow->creator_id,
                'created_at' => $productFlow->created_at,
                'updated_at' => $productFlow->updated_at
            ];
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 删除产品审批关联
     * @param int $id
     * @throws BizException
     */
    public function deleteProductFlow(int $id): void
    {
        try {
            DB::beginTransaction();

            $productFlow = $this->repository->findOrFail($id);

            // 检查是否存在审核记录
            $hasApprovalRecords = $this->recordRepository->findByProductAndFlow(
                $productFlow->product_table,
                $productFlow->flow_id
            );

            if ($hasApprovalRecords) {
                BizException::throws(ApprovalErrorCode::PRODUCT_FLOW_HAS_RECORDS,ApprovalErrorCode::PRODUCT_FLOW_HAS_RECORDS->message());
            }

            $this->repository->delete($productFlow);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    

 
}

