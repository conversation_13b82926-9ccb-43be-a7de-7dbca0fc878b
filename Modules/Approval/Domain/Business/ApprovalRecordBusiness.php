<?php

declare(strict_types=1);

namespace Modules\Approval\Domain\Business;

use Modules\Approval\Domain\Repositories\ApprovalRecordRepository;
use Modules\Approval\Domain\Repositories\ApprovalLogRepository;
use Modules\Approval\Domain\Repositories\ApprovalFlowRepository;
use Modules\Approval\Enums\ApprovalRecordAction;
use Modules\Approval\Enums\ApprovalStatus;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Modules\Approval\Enums\ApprovalErrorCode;
use Modules\Approval\Domain\Repositories\ApprovalProductFlowRepository;
use Exception;
use Modules\Approval\Domain\Repositories\ApprovalMemberRepository;
use Modules\Approval\Events\ApprovalPassed;
use Modules\Approval\Events\ApprovalRejected;
use Modules\Approval\Models\ApprovalRecord;
use Modules\Approval\Models\ApprovalLog;
use Modules\Approval\Domain\Repositories\ApprovalFlowSettingRepository;
use Modules\Approval\Models\ApprovalSettings;
use Modules\Approval\Enums\DashboardStatus;
use Illuminate\Support\Facades\Log;
use Modules\Approval\Models\ApprovalReviewGroups;
use Modules\Approval\Models\ApprovalReviewMembers;
use Modules\Approval\Services\EmailService;
use Modules\Approval\Enums\ApprovalProduct;

/**
 * 审批记录业务类
 */
final class ApprovalRecordBusiness
{
    public function __construct(
        private readonly ApprovalRecordRepository $recordRepository,
        private readonly ApprovalLogRepository $logRepository,
        private readonly ApprovalFlowRepository $flowRepository,
        private readonly ApprovalProductFlowRepository $flowRelationRepository,
        private readonly ApprovalMemberRepository $memberRepository,
        private readonly ApprovalFlowSettingRepository $settingsRepository,
        private readonly EmailService $emailService
    ) {}

    /**
     * 获取审批记录列表
     * @param array $params 查询参数
     * @return array
     */
    public function getApprovalRecordList(array $params): array
    {
        return $this->recordRepository->getApprovalRecordList($params);
    }

    /**
     * 获取产品审批进度
     * @param int $id
     * @return array
     * @throws BizException
     */
    public function getApprovalProgress(int $id): array
    {
        $record = $this->recordRepository->findNotRejected($id);
        if (empty($record)) {
            BizException::throws(ApprovalErrorCode::RECORD_NOT_FOUND, ApprovalErrorCode::RECORD_NOT_FOUND->message());
        }

        // 获取流程信息
        $flow = $this->flowRepository->find($record->flow_id);
        if (empty($flow)) {
            BizException::throws(ApprovalErrorCode::FLOW_NOT_FOUND, ApprovalErrorCode::FLOW_NOT_FOUND->message());
        }

        // 获取所有步骤
        // 获取流程步骤并按排序升序排列
        $steps = $flow->steps()
            ->orderBy('sort', 'asc')
            ->get()
            ->map(function ($step) use ($record) {
                if (!$step || !isset($step->id) || !isset($step->name) || !isset($step->sort)) {
                    BizException::throws(ApprovalErrorCode::FLOW_STEP_DATA_ERROR, ApprovalErrorCode::FLOW_STEP_DATA_ERROR->message());
                }

                $status = $this->getStepStatus($step, $record);
                return [
                    'id' => (int)$step->id,
                    'name' => (string)$step->name,
                    'sort' => (int)$step->sort,
                    'status' => $status['status'],
                    'approver_name' => $status['approver_name'],
                    'group_name' => $status['group_name'],
                    'created_at' => $step->created_at->format('Y-m-d H:i:s'),
                ];
            })
            ->values()
            ->toArray();

        if (empty($steps)) {
            return [];
        }

        return [
            'id' => $record->id,
            'product_id' => $record->product_id,
            'product_key' => $record->product_key,
            'flow_id' => $record->flow_id,
            'current_step' => $record->step_id,
            'status' => $record->status,
            'status_text' => ApprovalStatus::getTextFromValue($record->status),
            'steps' => $steps
        ];
    }

    /**
     * 获取步骤状态
     * @param object $step
     * @param object $record
     * @return array
     */
    private function getStepStatus(object $step, object $record): array
    {
        //如果当前审批记录状态超时，那么直接返回超时
        if ($record->status === ApprovalRecord::STATUS_TIMEOUT) {
            return ['status' => 'timeout', 'approver_name' => '', 'group_name' => ''];
        }

        // 查询步骤对应的审批日志
        $log = ApprovalLog::where('record_id', $record->id)
            ->where('step_id', $step->id)
            ->where('rejected_at', 0)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($log) {
            // 获取审批人名称
            $approverName = '';
            if ($log->user_id) {
                $approver = ApprovalReviewMembers::where('user_id', $log->user_id)->first();
                $approverName = $approver ? $approver->name : '';
            }

            // 获取审批组名称
            $groupName = '';
            if ($log->group_id) {
                $group = ApprovalReviewGroups::find($log->group_id);
                $groupName = $group ? $group->name : '';
            }

            // 如果有日志记录，根据日志的action返回状态
            return ['status' => match ($log->action) {
                ApprovalLog::ACTION_APPROVE => 'completed', // 通过
                ApprovalLog::ACTION_REJECT => 'rejected',   // 拒绝
                default => 'pending' // 待审批
            }, 'approver_name' => $approverName, 'group_name' => $groupName];
        }

        // 如果不存在审批记录，返回待审批状态
        return ['status' => 'pending', 'approver_name' => '', 'group_name' => ''];
    }

    /**
     * 根据产品信息创建审批记录
     * @param string $product_key 产品key
     * @param int $product_id 产品id
     * @return array 审批记录详情
     * @throws BizException
     */
    public function createRecord($product_key, $product_id): array
    {
        // 参数校验
        if (empty($product_key) || empty($product_id)) {
            BizException::throws(ApprovalErrorCode::RECORD_CREATE_FAILED, ApprovalErrorCode::RECORD_CREATE_FAILED->message());
        }
        //需要创建审批流程才能触发事件
        // 查询审批流程关联
        $productFlowRelation = $this->flowRelationRepository->findByProduct($product_key);
        if (!$productFlowRelation) {
            BizException::throws(ApprovalErrorCode::PRODUCT_FLOW_NOT_FOUND, ApprovalErrorCode::PRODUCT_FLOW_NOT_FOUND->message());
        }

        // 获取审批流程
        $flow = $this->flowRepository->findOrFail($productFlowRelation->flow_id);
        if (!$flow) {
            BizException::throws(ApprovalErrorCode::FLOW_NOT_FOUND, ApprovalErrorCode::FLOW_NOT_FOUND->message());
        }

        // 检查流程是否打开
        if (!$flow->is_enabled) {
            BizException::throws(ApprovalErrorCode::FLOW_IS_NOT_ENABLED, ApprovalErrorCode::FLOW_IS_NOT_ENABLED->message());
        }

        // 获取第一个步骤
        $firstStep = $flow->steps()->orderBy('sort')->first();
        if (!$firstStep) {
            BizException::throws(ApprovalErrorCode::FLOW_STEP_NOT_FOUND, ApprovalErrorCode::FLOW_STEP_NOT_FOUND->message());
        }

        $data = [
            'product_key' => $product_key,
            'product_id' => $product_id,
            'flow_id' => $flow->id,
            'step_id' => $firstStep->id
        ];

        $record = $this->recordRepository->create([
            'product_key' => $data['product_key'],
            'product_name' => ApprovalProduct::from($data['product_key'])->getName(),
            'product_id' => $data['product_id'],
            'flow_id' => $data['flow_id'],
            'step_id' => $data['step_id'],
            'status' => ApprovalStatus::PENDING->value,
            'remark' => $data['remark'] ?? '',
            'creator_id' => Auth::guard()->user()->id ?? 0,
            'created_at' => time(),
            'updated_at' => time()
        ]);

        return [
            'id' => $record->id,
            'product_key' => $record->product_key,
            'product_id' => $record->product_id,
            'flow_id' => $record->flow_id,
            'step_id' => $record->step_id,
            'status' => $record->status,
            'status_text' => match ($record->status) {
                ApprovalStatus::PENDING->value => '待审批',
                ApprovalStatus::PROCESSING->value => '审批中',
                ApprovalStatus::APPROVED->value => '已通过',
                ApprovalStatus::REJECTED->value => '已拒绝',
                default => '未知状态'
            },
            'remark' => $record->remark,
            'creator_id' => $record->creator_id,
            'created_at' => $record->created_at,
            'updated_at' => $record->updated_at
        ];
    }

    /**
     * 审批流程通知
     * @param int $id 审批记录ID
     * @return void
     * @throws BizException
     */
    public function handleNotification(int $recordId, array $notificationData): void
    {
        try {
            $record = $this->recordRepository->getRecordWithFlowAndSettings($recordId);
            // 获取流程设置
            $flowSettings = $record->settings;
            if (!empty($flowSettings)) {
                // 获取审批组成员的邮箱
                $memberEmails = [];
                if (!empty($record->step->group_ids)) {
                    // 获取审批组成员
                    $members = $this->memberRepository->getMemberByGroupIds($record->step->group_ids);
                    $memberEmails = collect($members)
                        ->filter(fn($member) => !empty($member->email))
                        ->pluck('email')
                        ->toArray();
                }
                if (empty($memberEmails)) {
                    return;
                }

                foreach ($flowSettings->notification_rules as $rule) {
                    if ($rule['eventType'] === ApprovalSettings::EVENT_TYPE_PROCESS_CHANGE) {
                        // 根据通知渠道发送通知
                        foreach ($rule['channels'] as $channel) {
                            switch ($channel) {
                                case ApprovalSettings::CHANNEL_EMAIL:
                                    // 发送邮件通知
                                    foreach ($memberEmails as $email) {
                                        try {
                                            $this->emailService->sendEmail(
                                                $email,
                                                '审批流程通知',
                                                "您有一个审批流程需要关注。\n审批步骤：{$record->step->name}\n审批操作：{$notificationData['action']}\n审批意见：{$notificationData['comment']}\n审批人：{$notificationData['approval_user']}\n审批人邮箱：{$notificationData['approval_user_email']}\n审批时间：" . date('Y-m-d H:i:s')
                                            );
                                        } catch (\Exception $e) {
                                            //记录日志
                                            Log::error('审批流程邮件发送失败', [
                                                'email' => $email,
                                                'error' => $e->getMessage()
                                            ]);
                                        }
                                    }
                                    break;
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            //记录日志
            Log::error('审批流程通知', [
                'record_id' => $recordId,
                'notification_data' => $notificationData,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 创建审批日志
     * @param int $id 审批记录ID
     * @param array $data 审批数据
     * @return array
     * @throws BizException
     */
    public function createApprovalLog(int $id, array $data): array
    {
        try {
            DB::beginTransaction();

            // 获取产品审批流程
            $approvalRecord = $this->recordRepository->findOrFail($id);

            //查看是否已经审批结束了
            if ($approvalRecord->status === ApprovalStatus::APPROVED->value || $approvalRecord->status === ApprovalStatus::REJECTED->value) {
                throw BizException::throws(ApprovalErrorCode::RECORD_ALREADY_APPROVED, ApprovalErrorCode::RECORD_ALREADY_APPROVED->message());
            }

            // 获取当前步骤信息
            $flow = $this->flowRepository->findOrFail($approvalRecord->flow_id);
            $currentStep = $flow->steps()->where('id', $approvalRecord->step_id)->first();
            if (!$currentStep) {
                throw BizException::throws(ApprovalErrorCode::FLOW_STEP_NOT_FOUND, ApprovalErrorCode::FLOW_STEP_NOT_FOUND->message());
            }
            $creatorId = Auth::guard()->user()->id ?? 0;

            // 检查当前用户是否在审批组成员中并获取成员信息
            $member = $this->checkUserInApprovalGroup($currentStep->group_ids, $creatorId);

            //检查审批日志是否存在存在且没有审批旧更新
            $log = $this->logRepository->findByRecordIdAndStepIdNotRejected($approvalRecord->id, $currentStep->id);
            if ($log) {
                $this->logRepository->update($log->id, [
                    'updated_at' => time(),
                    'action' => $data['action'],
                    'remark' => $data['comment'] ?? '',
                    'rejected_at' => $data['action'] === ApprovalLog::ACTION_REJECT ? time() : 0
                ]);
            } else {
                // 创建审批记录
                $log = $this->logRepository->create([
                    'record_id' => $approvalRecord->id, // 审批记录ID
                    'product_key' => $approvalRecord->product_key, // 产品key
                    'product_name' => ApprovalProduct::from($approvalRecord->product_key)->getName(), // 产品名称
                    'product_id' => $approvalRecord->product_id, // 产品ID
                    'flow_id' => $approvalRecord->flow_id, // 审批流程ID
                    'step_id' => $approvalRecord->step_id, // 当前审批步骤ID
                    'group_id' => $member['group_id'], // 审核组ID
                    'user_id' => $member['user_id'], // 审批人ID
                    'action' => $data['action'], // 审批动作
                    'remark' => $data['comment'] ?? '', // 审批意见
                    'attachments' => $data['attachments'] ?? '', // 附件
                    'creator_id' => $creatorId, // 创建人ID
                    'created_at' => time(), // 创建时间
                    'updated_at' => time(), // 更新时间
                    'rejected_at' => $data['action'] === ApprovalLog::ACTION_REJECT ? time() : 0
                ]);
            }

            // 更新产品审批流程状态
            if ($data['action'] === ApprovalLog::ACTION_APPROVE) {
                // 如果是通过，获取下一个步骤
                $nextStep = $flow->steps()
                    ->where('sort', '>', $currentStep->sort)
                    ->orderBy('sort')
                    ->first();

                if ($nextStep) {
                    // 还有下一步，更新为审批中状态和下一步骤
                    $approvalRecord->update([
                        'step_id' => $nextStep->id,
                        'status' => ApprovalStatus::PROCESSING->value,
                        'updated_at' => time()
                    ]);
                } else {
                    // 没有下一步，更新为已通过状态
                    $approvalRecord->update([
                        'status' => ApprovalStatus::APPROVED->value,
                        'updated_at' => time()
                    ]);
                    // 触发审批通过事件
                    event(new ApprovalPassed($approvalRecord->product_key, $approvalRecord->product_id));
                }
            } else {
                // 如果是拒绝，直接更新为已拒绝状态
                $approvalRecord->update([
                    'status' => ApprovalStatus::REJECTED->value,
                    'updated_at' => time(),
                    'rejected_at' => time()
                ]);

                // 更新审批记录的拒绝时间
                $approvalRecord->update([
                    'updated_at' => time(),
                    'rejected_at' => time()
                ]);

                // 触发审批拒绝事件
                event(new ApprovalRejected($approvalRecord->product_key, $approvalRecord->product_id, $data['comment']));
            }

            DB::commit();

            $this->handleNotification($approvalRecord->id, [
                'action' => $data['action'],
                'comment' => $data['comment'],
                'approval_user' => $member['name'],
                'approval_user_email' => $member['email']
            ]);

            return [
                'id' => $log->id,
                'product_id' => $log->product_id,
                'flow_id' => $log->flow_id,
                'step_id' => $log->step_id,
                'action' => $log->action,
                'remark' => $log->remark,
                'created_at' => $log->created_at
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 检查当前用户是否在审批组成员中并返回成员信息
     * @param array $groupIds
     * @param int $userId
     * @return array
     * @throws BizException
     */
    private function checkUserInApprovalGroup(array $groupIds, int $userId): array
    {
        // 获取用户在审批组中的成员信息
        $member = $this->memberRepository->findUserInGroups($groupIds, $userId);

        if (!$member) {
            throw BizException::throws(ApprovalErrorCode::USER_NOT_IN_APPROVAL_GROUP, ApprovalErrorCode::USER_NOT_IN_APPROVAL_GROUP->message());
        }

        return [
            'group_id' => $member->group_id,
            'user_id' => $member->user_id,
            'name' => $member->name,
            'position' => $member->position,
            'email' => $member->email
        ];
    }

    /**
     * 获取审批仪表盘数据
     *
     * @param array $params 查询参数
     * @return array 仪表盘数据统计
     */
    public function dashboard(array $params): array
    {
        // 统一对分页参数进行int类型转换
        $params['page'] = (int)($params['page'] ?? 1);
        $params['limit'] = (int)($params['limit'] ?? 10);

        // 获取当前审批记录状态统计
        $processingCount = $this->recordRepository->countWithProcessing();

        // 获取超时的审批数量
        $timeoutCount = $this->recordRepository->countTimeout();

        // 获取待发布的审批数量（已通过但尚未发布的）
        $pendingPublishCount = $this->recordRepository->countPendingPublish();

        // 分页获取审批记录
        switch ($params['status']) {
            case DashboardStatus::PENDING->value:
                $items = $this->recordRepository->getRecordsByUserApproval($params['page'], $params['limit'], $params['keyword']);
                break;
            case DashboardStatus::PUBLISHED->value:
                $items = $this->recordRepository->getRecordsByUserPublished($params['page'], $params['limit'], $params['keyword']);
                break;
            case DashboardStatus::COMPLETED->value:
                $items = $this->recordRepository->getCompletedRecordsWithUser($params['page'], $params['limit'], $params['keyword']);
                break;
        }

        return [
            'processing_count' => $processingCount,
            'timeout_count' => $timeoutCount,
            'pending_publish_count' => $pendingPublishCount,
            'items' => $items,
        ];
    }

    /**
     * 删除审批记录
     * @param int $id 审批记录ID
     * @return void
     * @throws BizException 当记录不存在、不是待审批状态或存在审批日志时抛出异常
     */
    public function deleteApprovalRecord(int $id): void
    {
        // 查找审批记录
        $record = $this->recordRepository->findOrFail($id);

        // 检查审批记录状态是否为待审批
        if ($record->status !== ApprovalRecord::STATUS_PENDING) {
            BizException::throws(
                ApprovalErrorCode::RECORD_CANNOT_BE_DELETED,
                ApprovalErrorCode::RECORD_CANNOT_BE_DELETED->message()
            );
        }

        // 检查是否存在审批日志
        $hasLogs = $this->logRepository->hasLogsByRecordId($id);
        if ($hasLogs) {
            BizException::throws(
                ApprovalErrorCode::RECORD_CANNOT_BE_DELETED,
                ApprovalErrorCode::RECORD_CANNOT_BE_DELETED->message()
            );
        }

        // 执行删除操作
        $record->delete();
    }

    /**
     * 根据productKey和productId获取审批记录
     * @param string $productKey 产品key
     * @param int $productId 产品id
     * @return array 审批记录
     */
    public function getApprovalRecordByProduct(string $productKey, int $productId): array
    {
        // 根据productKey和productId获取参评审批关系
        $productFlowRelation = $this->flowRelationRepository->findByProduct($productKey);
        if (empty($productFlowRelation)) {
            BizException::throws(ApprovalErrorCode::PRODUCT_FLOW_NOT_FOUND, ApprovalErrorCode::PRODUCT_FLOW_NOT_FOUND->message());
        }



        // 获取审批记录
        $record = $this->recordRepository->findByProductAndFlowNotRejected($productKey, $productFlowRelation->flow_id);
        if (empty($record)) {
            BizException::throws(ApprovalErrorCode::RECORD_NOT_FOUND, ApprovalErrorCode::RECORD_NOT_FOUND->message());
        }

        $res = $this->getApprovalProgress($record->id);
        return $res;
    }
}
