<?php

namespace Modules\Approval\Domain\Business;

use Log;
use Exception;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Modules\Appointment\Models\IamUser;
use Modules\Approval\Enums\ApprovalErrorCode;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Approval\Domain\Repositories\ApprovalGroupRepository;

final readonly class ApprovalGroupBusiness
{
    public function __construct(
        private ApprovalGroupRepository $repository,
    ) {}

    /**
     * 获取审批组列表
     * @param array $params
     * @return LengthAwarePaginator
     */
    public function getGroupList(array $params): LengthAwarePaginator
    {
        return $this->repository->list($params);
    }

    /**
     * 创建审批组
     * @param array $data
     * @return array
     * @throws BizException
     */
    public function createGroup(array $data): array
    {
        // 检查成员邮箱是否存在于IAM用户表中并获取user_id
        if (isset($data['members']) && is_array($data['members'])) {
            $data['members'] = $this->validateAndEnrichMembersWithUserId($data['members']);
        }

        $data['creator_id'] = Auth::guard()->user()->id ?? 0;

        return $this->repository->create($data);
    }

    /**
     * 更新审批组
     * @param int $id
     * @param array $data
     * @return array
     * @throws BizException
     */
    public function updateGroup(int $id, array $data): array
    {
        $group = $this->repository->find($id);
        if (!$group) {
            BizException::throws(ApprovalErrorCode::GROUP_NOT_FOUND, ApprovalErrorCode::GROUP_NOT_FOUND->message());
        }

        // 检查成员邮箱是否存在于IAM用户表中并获取user_id
        if (isset($data['members']) && is_array($data['members'])) {
            $data['members'] = $this->validateAndEnrichMembersWithUserId($data['members']);
        }

        return $this->repository->update($id, $data);
    }

    /**
     * 删除审批组
     * @param int $id 审批组ID
     * @throws BizException 当删除失败时抛出异常
     */
    public function deleteGroup(int $id): void
    {
        // 检查审批组是否存在
        $group = $this->repository->find($id);
        if (!$group) {
            BizException::throws(ApprovalErrorCode::GROUP_NOT_FOUND, ApprovalErrorCode::GROUP_NOT_FOUND->message());
        }

        // 检查审批组是否在使用中
        $isInUse = $this->repository->isGroupInUse($id);
        if ($isInUse) {
            BizException::throws(ApprovalErrorCode::GROUP_IN_USE, ApprovalErrorCode::GROUP_IN_USE->message());
        }

        // 使用事务确保数据一致性
        DB::beginTransaction();
        try {
            // 删除审批组成员
            $this->repository->deleteGroupMembers($id);

            // 删除审批组
            $this->repository->delete($id);

            DB::commit();
        } catch (BizException $e) {
            DB::rollBack();
            Log::error('Failed to delete approval group', [
                'group_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取所有可用的权限列表
     * @return array
     */
    public function getGroupPermissions(): array
    {
        return [
            // 审批流程权限
            [
                'id' => 'approval.flows.view',
                'name' => '查看审批流程',
                'description' => '允许查看审批流程列表和详情',
                'actions' => ['index', 'show']
            ],
            [
                'id' => 'approval.flows.create',
                'name' => '创建审批流程',
                'description' => '允许创建新的审批流程',
                'actions' => ['store']
            ],
            [
                'id' => 'approval.flows.edit',
                'name' => '编辑审批流程',
                'description' => '允许编辑现有审批流程',
                'actions' => ['update']
            ],
            [
                'id' => 'approval.flows.delete',
                'name' => '删除审批流程',
                'description' => '允许删除审批流程',
                'actions' => ['destroy']
            ]
        ];
    }

    /**
     * 获取审批组详情
     * @param int $id
     * @return ?array
     * @throws BizException
     */
    public function getGroupDetail(int $id): ?array
    {
        $group = $this->repository->find($id);

        if (!$group) {
            BizException::throws(ApprovalErrorCode::GROUP_NOT_FOUND, ApprovalErrorCode::GROUP_NOT_FOUND->message());
        }

        return $group;
    }

    /**
     * 根据邮箱列表获取并验证用户信息
     * @param array $members 包含email字段的成员数组
     * @return array 添加了user_id的成员数组
     * @throws BizException 当有用户未找到时抛出异常
     */
    private function validateAndEnrichMembersWithUserId(array $members): array
    {
        if (empty($members)) {
            return $members;
        }

        $emailCounts = array_count_values(array_column($members, 'email'));
        $duplicateEmails = array_filter($emailCounts, function($count) {
            return $count > 1;
        });

        if (!empty($duplicateEmails)) {
            BizException::throws(ApprovalErrorCode::DUPLICATE_EMAIL, ApprovalErrorCode::DUPLICATE_EMAIL->message());
        }

        $memberEmails = array_column($members, 'email');

        $tempUsers = IamUser::query()
            ->whereIn('email', $memberEmails)
            ->select('id','email','name')
            ->get()
            ->toArray();


        $userMap = [];
        foreach ($tempUsers as $user) {
            $userMap[$user['email']] = $user;
        }

        $emailList =  array_column($tempUsers, 'email');

        if (count($memberEmails) != count($emailList)) {
            BizException::throws(ApprovalErrorCode::USER_NOT_FOUND, ApprovalErrorCode::USER_NOT_FOUND->message());
        }


        //验证用户的名称
        foreach ($members as $member) {
            if ($userMap[$member['email']]['name'] !== $member['name']) {
                BizException::throws(ApprovalErrorCode::USER_NAME_MISMATCH, ApprovalErrorCode::USER_NAME_MISMATCH->message());
            }
        }

        // 添加user_id到成员数据中
        foreach ($members as &$member) {
            $member['user_id'] = $userMap[$member['email']]['id'];
        }

        return $members;
    }

    /**
     * 复制审批组
     * @param int $id 审批组ID
     * @return array
     * @throws BizException
     */
    public function copyGroup(int $id): array
    {
        // 获取原审批组信息
        $group = $this->repository->find($id);
        if (!$group) {
            BizException::throws(ApprovalErrorCode::GROUP_NOT_FOUND, ApprovalErrorCode::GROUP_NOT_FOUND->message());
        }

        // 复制组信息并修改名称
        $newGroup = $group;
        $newGroup['name'] = $group['name'] . ' -copy';
        // 如果code不为null,需要生成新的code,因为code是唯一索引
        if (!empty($newGroup['code'])) {
            $newGroup['code'] = $group['code'] . '-copy';
        } else {
            $newGroup['code'] = null;
        }
        unset($newGroup['id'], $newGroup['created_at'], $newGroup['updated_at']);

        // 创建新组
        return $this->repository->create($newGroup);
    }
}
