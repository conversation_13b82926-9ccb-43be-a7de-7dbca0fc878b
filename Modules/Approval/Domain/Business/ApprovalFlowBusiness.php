<?php

namespace Modules\Approval\Domain\Business;

use Bingo\Exceptions\BizException;
use Exception;
use Modules\Approval\Domain\Repositories\ApprovalFlowRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Approval\Domain\Repositories\ApprovalStepRepository;
use Modules\Approval\Domain\Repositories\ApprovalFlowSettingRepository;
use Modules\Approval\Enums\ApprovalErrorCode;
use Modules\Approval\Domain\Repositories\ApprovalGroupRepository;
use Modules\Approval\Domain\Repositories\ApprovalProductFlowRepository;

final readonly class ApprovalFlowBusiness
{
    public function __construct(
        private ApprovalFlowRepository        $flowRepository,
        private ApprovalStepRepository        $stepRepository,
        private ApprovalFlowSettingRepository $settingRepository,
        private ApprovalGroupRepository       $groupRepository,
        private ApprovalProductFlowRepository $productFlowRepository
    ) {
    }

    /**
     * 获取流程列表
     *
     * @param array $params 查询参数
     * @return LengthAwarePaginator
     */
    public function getFlowList(array $params): LengthAwarePaginator
    {
        // 提取并验证分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $limit = min(100, max(1, (int)($params['limit'] ?? 15)));

        // 提取查询条件
        $conditions = [
            'keyword' => $params['keyword'] ?? null,
            'sort_field' => in_array($params['sort_field'] ?? '', ['id', 'created_at', 'updated_at']) ? $params['sort_field'] : 'id',
            'sort_order' => in_array($params['sort_order'] ?? '', ['asc', 'desc']) ? $params['sort_order'] : 'desc'
        ];

        // 调用仓储层方法进行分页查询
        return $this->flowRepository->paginateFlows($conditions, $page, $limit);
    }

    /**
     * 获取流程详情
     *
     * @param int $id
     * @return array|null
     */
    public function getFlowDetail(int $id): ?array
    {
        $flow = $this->flowRepository->find($id);

        if (!$flow) {
            return null;
        }

        // 获取流程步骤
        $steps = $this->stepRepository->findByFlowId($id);
        $formattedSteps = [];

        // 获取流程设置
        $settings = $this->settingRepository->findByFlowId($id);

        foreach ($steps as $step) {
            $stepObj = (object)$step;

            // 查询审批组
            $groupCollection = $this->groupRepository->findByGroupIds($stepObj->group_ids);
            // 将审批组对象集合添加到步骤对象中
            $groups = [];
            
            foreach ($groupCollection as $group) {
                $groups[] = [
                    'id' => $group->id,
                    'name' => $group->name
                ];
            }
            
            // 将审批组信息添加到步骤对象中
            $stepObj->groups = $groups;

            $formattedSteps[] = $this->formatStep($stepObj);
        }

        return [
            ...$this->formatFlow($flow),
            'steps' => $formattedSteps,
            'settings' => $settings
        ];
    }

    /**
     * 创建流程（包含步骤）
     *
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function createFlow(array $data): array
    {
        try {
            DB::beginTransaction();

            $creatorId = Auth::guard()->user()->id ?? 0;

            // 创建流程
            $flowData = [
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'scope' => $data['scope'],
                'creator_id' => $creatorId,
                'is_enabled' => false // 默认关闭
            ];

            $flow = $this->flowRepository->create($flowData);


            // 创建产品审批关联
            $productFlow = $this->productFlowRepository->create([
                'flow_id' => $flow->id,
                'product_key' => $flow->scope,
                'creator_id' => $creatorId,
            ]);
            if(!$productFlow){
                BizException::throws(ApprovalErrorCode::PRODUCT_FLOW_CREATE_FAILED, ApprovalErrorCode::PRODUCT_FLOW_CREATE_FAILED->message());
            }

            // 处理 settings
            if (!empty($data['settings'])) {
                // 检查是否存在设置，存在则更新，不存在则创建
                $existingSettings = $this->settingRepository->findByFlowId($flow->id);
                if ($existingSettings) {
                    $settings = $this->settingRepository->update($flow->id, $data['settings']);
                } else {
                    $settings = $this->settingRepository->create([
                        'flow_id' => $flow->id,
                        'settings' => $data['settings']
                    ]);
                }
            } else {
                // 如果没有传入settings,使用默认设置
                $settings = $this->settingRepository->create([
                    'flow_id' => $flow->id,
                    'settings' => $this->settingRepository->getDefaultSettings()
                ]);
            }

            // 创建步骤
            $steps = [];
            foreach ($data['steps'] as $stepData) {
                $stepEntity = [
                    'flow_id' => $flow->id,
                    'name' => $stepData['name'],
                    'step_code' => $stepData['stepCode'],
                    'group_ids' => $stepData['groupIds'],
                    'sort' => $stepData['sort'],
                    'reject_to_draft' => $stepData['rejectToDraft'] ?? 1,
                    'lang' => $stepData['lang'] ?? null,
                    'creator_id' => $creatorId,
                    'created_at' => time(),
                    'updated_at' => time()
                ];

                $step = $this->stepRepository->create($stepEntity);
                $steps[] = $this->formatStep($step);
            }

            DB::commit();

            return [
                ...$this->formatFlow($flow),
                'settings' => $settings,
                'steps' => $steps
            ];

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 更新流程（包含步骤）
     *
     * @param int $id
     * @param array $data
     * @return array
     * @throws BizException
     */
    public function updateFlow(int $id, array $data): array
    {
        try {
            DB::beginTransaction();

            $data['creator_id'] = Auth::guard()->user()->id ?? 0;

            // 获取流程
            $flow = $this->flowRepository->findOrFail($id);

            // 更新流程
            $updateData = [];
            if (isset($data['name'])) {
                $updateData['name'] = $data['name'];
            }
            if (isset($data['description'])) {
                $updateData['description'] = $data['description'];
            }
            if (isset($data['scope'])) {
                $updateData['scope'] = $data['scope'];
            }
            if (isset($data['is_enabled'])) {
                $updateData['is_enabled'] = $data['is_enabled'];
            }

            // 更新流程和步骤
            $result = $this->flowRepository->updateFlowAndSteps($flow, $updateData, $data['steps'] ?? []);

            // 更新或创建设置
            if (isset($data['settings'])) {
                $settings = $this->settingRepository->findByFlowId($id);
                if ($settings) {
                    $settings = $this->settingRepository->update($id, $data['settings']);
                } else {
                    $settings = $this->settingRepository->create([
                        'flow_id' => $id,
                        'settings' => $data['settings']
                    ]);
                }
                $result['settings'] = $settings;
            }

            // 關閉相同scope的其他流程
            $this->flowRepository->closeOtherFlows($flow->id, $updateData['scope']);

            DB::commit();
            return $result;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 格式化流程数据
     *
     * @param object $flow
     * @return array
     */
    private function formatFlow(object $flow): array
    {
        return [
            'id' => $flow->id,
            'name' => $flow->name,
            'description' => $flow->description,
            'scope' => $flow->scope,
            'settings' => $flow->settings,
            'isEnabled' => $flow->is_enabled,
            'status' => $flow->status,
            'createdAt' => $flow->created_at->timestamp,
            'updatedAt' => $flow->updated_at->timestamp,
        ];
    }

    /**
     * 格式化步骤数据
     *
     * @param object $step
     * @return array
     */
    private function formatStep(object $step): array
    {
        return [
            'id' => $step->id,
            'flowId' => $step->flow_id,
            'name' => $step->name,
            'stepCode' => $step->step_code,
            'groupIds' => $step->group_ids,
            'groups' => !empty($step->groups) ? $step->groups : null,
            'sort' => $step->sort,
            'rejectToDraft' => $step->reject_to_draft,
            'lang' => $step->lang,
            'creatorId' => $step->creator_id,
            'createdAt' => $step->created_at,
            'updatedAt' => $step->updated_at
        ];
    }

    /**
     * 获取审批流程设置
     *
     * @param int $flowId
     * @return array
     */
    public function getSettings(int $flowId): array
    {
        return [
            'settings' => $this->settingRepository->findByFlowId($flowId)
        ];
    }

    /**
     * 获取审批流程设置结构
     *
     * @return array
     */
    public function getSettingsStructure(): array
    {
        return [
            'settings' => [
                'lock_published_content' => [
                    'type' => 'boolean',
                    'default' => false,
                    'label' => '锁定已发布内容',
                    'comment' => '锁定已发布内容：1-是，0-否'
                ],
                'allow_attachments' => [
                    'type' => 'boolean',
                    'default' => false,
                    'label' => '允许添加附件',
                    'comment' => '允许添加附件：1-是，0-否'
                ],
                'allow_planned_date' => [
                    'type' => 'boolean',
                    'default' => false,
                    'label' => '允许计划日期',
                    'comment' => '允许计划日期：1-是，0-否'
                ],
                'planned_date' => [
                    'type' => 'date',
                    'default' => null,
                    'label' => '计划日期',
                    'comment' => '计划日期'
                ],
                'enable_time_limit' => [
                    'type' => 'boolean',
                    'default' => false,
                    'label' => '启用流程时效限制',
                    'comment' => '启用流程时效限制：1-是，0-否'
                ],
                'allow_cancel_pending' => [
                    'type' => 'boolean',
                    'default' => false,
                    'label' => '允许取消待审批流程',
                    'comment' => '允许取消待审批流程：1-是，0-否'
                ],
                'allow_permission_extend' => [
                    'type' => 'boolean',
                    'default' => false,
                    'label' => '允许权限扩展',
                    'comment' => '允许权限扩展：1-是，0-否'
                ],
                'notification_rules' => [
                    'type' => 'text',
                    'default' => null,
                    'label' => '通知设置',
                    'comment' => '通知设置'
                ]
            ]
        ];
    }

    /**
     * 复制审批流程
     *
     * @param int $id 源流程ID
     * @return array 复制后的流程信息
     * @throws BizException 当流程不存在时抛出异常
     */
    public function copyFlow(int $id): array
    {
        try {
            // 获取源流程信息
            $sourceFlow = $this->flowRepository->findOrFail($id);
            
            // 复制流程及其关联数据
            $newFlow = $this->flowRepository->copyFlowWithRelations($sourceFlow);
            
            // 获取完整的流程信息
            $steps = $this->stepRepository->findByFlowId($newFlow->id);
            $formattedSteps = [];

            foreach ($steps as $step) {
                $stepObj = (object)$step;
                $formattedSteps[] = $this->formatStep($stepObj);
            }
            
            // 获取流程设置
            $settings = $this->settingRepository->findByFlowId($newFlow->id);
            
            // 返回格式化的结果
            return [
                ...$this->formatFlow($newFlow),
                'steps' => $formattedSteps,
                'settings' => $settings
            ];
            
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 删除审批流程
     * 
     * @param int $id 流程ID
     * @return void
     * @throws BizException 当流程不存在或删除失败时抛出异常
     */
    public function deleteFlow(int $id): void
    {
        try {
            // 获取流程信息，如果不存在会抛出异常
            $flow = $this->flowRepository->findOrFail($id);
            
            // 检查是否存在产品关联
            if ($this->flowRepository->hasProductAssociation($id)) {
                BizException::throws(ApprovalErrorCode::FLOW_CANNOT_BE_DELETED, ApprovalErrorCode::FLOW_CANNOT_BE_DELETED->message());
            }
            
            // 检查是否存在审批记录
            if ($this->flowRepository->hasApprovalRecords($id)) {
                BizException::throws(ApprovalErrorCode::PRODUCT_FLOW_HAS_RECORDS, ApprovalErrorCode::PRODUCT_FLOW_HAS_RECORDS->message());
            }
            
            // 删除流程及其关联数据
            $this->flowRepository->deleteWithRelations($flow);
            
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 开关审批流程
     * 
     * @param int $id 流程ID
     * @return void
     * @throws BizException 当流程不存在或存在未完成的审批记录时抛出异常
     */
    public function switchFlow(int $id): void
    {
        try {
            DB::beginTransaction();
            
            // 获取流程信息
            $flow = $this->flowRepository->findOrFail($id);

            // 如果流程已打开，则直接返回
            if($flow->is_enabled){
                return;
            }
            
             // 检查是否存在未完成的审批记录
             if ($this->flowRepository->hasUnfinishedApprovalsExceptFlow($flow->id, $flow->scope)) {
                 BizException::throws(ApprovalErrorCode::PRODUCT_FLOW_HAS_RECORDS, ApprovalErrorCode::PRODUCT_FLOW_HAS_RECORDS->message());
             }

             //打开流程
             $flow->is_enabled = true;
             $flow->save();

             // 关闭相同scope的其他流程
             $this->flowRepository->closeOtherFlows($flow->id, $flow->scope);
            
            
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
