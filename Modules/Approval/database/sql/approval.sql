/* 审批流程定义表 */
DROP TABLE IF EXISTS `bwms`.`bingo_approval_flows`;
CREATE TABLE `bwms`.`bingo_approval_flows`
(
    `id`          bigint UNSIGNED NOT NULL AUTO_INCREMENT,
    `name`        varchar(100) NOT NULL DEFAULT '' COMMENT '流程名称',
    `scope`       varchar(50)  NOT NULL DEFAULT '' COMMENT '流程应用内容',
    `description` text COMMENT '描述',
    `is_enabled`  tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否启用：1-启用，0-禁用',
    `lang`        varchar(50)  NULL DEFAULT NULL COMMENT '语言标识',
    `creator_id`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `created_at`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
    `updated_at`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
    `deleted_at`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         `idx_scope`(`scope`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '审批流程定义表';

/* 审批流程步骤表 */
DROP TABLE IF EXISTS `bwms`.`bingo_approval_steps`;
CREATE TABLE `bwms`.`bingo_approval_steps`
(
    `id`              bigint UNSIGNED NOT NULL AUTO_INCREMENT,
    `flow_id`         bigint UNSIGNED NOT NULL COMMENT '流程ID',
    `name`            varchar(100) NOT NULL DEFAULT '' COMMENT '步骤名称',
    `step_code`       varchar(50)  NOT NULL DEFAULT '' COMMENT '步骤唯一标识代码',
    `sort`            int          NOT NULL DEFAULT 0 COMMENT '步骤顺序',
    `group_ids`       varchar(255) NOT NULL DEFAULT '' COMMENT '参与审核组ID列表',
    `reject_to_draft` tinyint(1)   NOT NULL DEFAULT 1 COMMENT '拒绝时返回第一步：1-是，0-否',
    `lang`            varchar(50)  NULL DEFAULT NULL COMMENT '语言标识',
    `creator_id`      int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `created_at`      int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
    `updated_at`      int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
    `deleted_at`      int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX             `idx_flow`(`flow_id`) USING BTREE,
    UNIQUE KEY `uk_flow_step_code`(`flow_id`, `step_code`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '审批流程步骤表';

/* 审批流程设置表 */
DROP TABLE IF EXISTS `bwms`.`bingo_approval_flow_settings`;
CREATE TABLE `bwms`.`bingo_approval_flow_settings`
(
    `id`                      bigint UNSIGNED NOT NULL AUTO_INCREMENT,
    `flow_id`                 bigint UNSIGNED NOT NULL COMMENT '流程ID',
    `lock_published_content`  tinyint(1) NOT NULL DEFAULT 0 COMMENT '锁定已发布内容：1-是，0-否',
    `allow_attachments`       tinyint(1) NOT NULL DEFAULT 0 COMMENT '允许添加附件：1-是，0-否',
    `allow_planned_date`      tinyint(1) NOT NULL DEFAULT 0 COMMENT '允许计划日期：1-是，0-否',
    `planned_date`            date NULL COMMENT '计划日期',
    `enable_time_limit`       tinyint(1) NOT NULL DEFAULT 0 COMMENT '启用流程时效限制：1-是，0-否',
    `allow_cancel_pending`    tinyint(1) NOT NULL DEFAULT 0 COMMENT '允许取消待审批流程：1-是，0-否',
    `allow_permission_extend` tinyint(1) NOT NULL DEFAULT 0 COMMENT '允许权限扩展：1-是，0-否',
    `notification_rules`      text COMMENT '通知设置',
    `creator_id`              int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `created_at`              int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
    `updated_at`              int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
    `deleted_at`      int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_flow_deleted`(`flow_id`, `deleted_at`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '审批流程设置表';

/* 审核组表 */
DROP TABLE IF EXISTS `bwms`.`bingo_approval_review_groups`;
CREATE TABLE `bwms`.`bingo_approval_review_groups`
(
    `id`          bigint UNSIGNED NOT NULL AUTO_INCREMENT,
    `name`        varchar(100) NOT NULL DEFAULT '' COMMENT '角色名称',
    `code`        varchar(50)  DEFAULT NULL COMMENT '角色代码',
    `level`       int          NOT NULL DEFAULT 1 COMMENT '角色层级',
    `is_inherit`  tinyint(1)   NOT NULL DEFAULT 1 COMMENT '权限继承：1-是，0-否',
    `permissions` text COMMENT '权限设置',
    `description` text COMMENT '描述',
    `status`      tinyint(1)   NOT NULL DEFAULT 1 COMMENT '状态: 1-启用, 0-禁用',
    `creator_id`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `created_at`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
    `updated_at`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
    `deleted_at`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_code_deleted`(`code`, `deleted_at`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '审核组表';

/* 审核组成员表 */
DROP TABLE IF EXISTS `bwms`.`bingo_approval_review_members`;
CREATE TABLE `bwms`.`bingo_approval_review_members`
(
    `id`         bigint UNSIGNED NOT NULL AUTO_INCREMENT,
    `group_id`   bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核组ID',
    `user_id`    bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
    `name`       varchar(100) NOT NULL DEFAULT '' COMMENT '姓名',
    `position`   varchar(100) NOT NULL DEFAULT '' COMMENT '职位',
    `email`      varchar(100) NOT NULL DEFAULT '' COMMENT '邮箱',
    `is_enabled` tinyint(1)   NOT NULL DEFAULT 1 COMMENT '启用状态: 1-启用, 0-禁用',
    `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
    `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
    `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX        `idx_group`(`group_id`) USING BTREE,
    INDEX        `idx_user`(`user_id`) USING BTREE,
    INDEX        `idx_email`(`email`) USING BTREE,
    UNIQUE KEY `uk_group_user`(`group_id`, `user_id`, `deleted_at`) USING BTREE,
    UNIQUE KEY `uk_group_email`(`group_id`, `email`, `deleted_at`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '审核组成员表';

/* 产品审批关联表 */
DROP TABLE IF EXISTS `bwms`.`bingo_approval_product_flows`;
CREATE TABLE `bwms`.`bingo_approval_product_flows`
(
    `id`          bigint UNSIGNED NOT NULL AUTO_INCREMENT,
    `product_key` varchar(255) NOT NULL DEFAULT '' COMMENT '产品表名',
    `flow_id`     bigint UNSIGNED NOT NULL COMMENT '审批流程ID',
    `creator_id`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `created_at`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
    `updated_at`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
    `deleted_at`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         `idx_flow`(`flow_id`) USING BTREE,
    UNIQUE KEY    `uk_product_flow`(`product_key`,`flow_id`,  `deleted_at`) USING BTREE

) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '产品审批关联表';

/* 审批记录表 */
DROP TABLE IF EXISTS `bwms`.`bingo_approval_records`;
CREATE TABLE `bwms`.`bingo_approval_records` (
    `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
    `product_key` varchar(50) NOT NULL DEFAULT '' COMMENT '产品表名',
    `product_id` bigint UNSIGNED NOT NULL COMMENT '产品ID',
    `flow_id` bigint UNSIGNED NOT NULL COMMENT '审批流程ID',
    `step_id` bigint UNSIGNED NOT NULL COMMENT '当前审批步骤ID',
    `status` tinyint NOT NULL DEFAULT 0 COMMENT '审批状态：0-待审批，1-审批中，2-已通过，3-已拒绝',
    `remark` text COMMENT '审批备注',
    `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
    `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
    `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
    `rejected_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '拒绝时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_product`(`product_id`) USING BTREE,
    INDEX `idx_flow`(`flow_id`) USING BTREE,
    INDEX `idx_step`(`step_id`) USING BTREE,
    INDEX `idx_status`(`status`) USING BTREE,
    UNIQUE KEY `uk_product_flow_status` (`product_key`, `product_id`, `flow_id`, `rejected_at`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '审批记录表';


/* 审核日志记录表 */
DROP TABLE IF EXISTS `bwms`.`bingo_approval_logs`;
CREATE TABLE `bwms`.`bingo_approval_logs`
(
    `id`          bigint UNSIGNED NOT NULL AUTO_INCREMENT,
    `record_id`   bigint UNSIGNED NOT NULL COMMENT '审批记录ID',
    `product_key` varchar(50) NOT NULL DEFAULT '' COMMENT '产品表名',
    `product_name` varchar(100) NOT NULL DEFAULT '' COMMENT '产品名称',
    `product_id`  bigint UNSIGNED NOT NULL COMMENT '产品ID',
    `flow_id`     bigint UNSIGNED NOT NULL COMMENT '审批流程ID',
    `step_id`     bigint UNSIGNED NOT NULL COMMENT '审批步骤ID',
    `group_id`    bigint UNSIGNED NOT NULL COMMENT '审核组ID',
    `user_id`     bigint UNSIGNED NOT NULL COMMENT '审批人ID',
    `action`      tinyint NOT NULL COMMENT '审批动作：1-通过，2-拒绝',
    `remark`      text COMMENT '审批意见',
    `attachments` text COMMENT '附件',
    `creator_id`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `created_at`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
    `updated_at`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
    `deleted_at`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
    `rejected_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '拒绝时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         `idx_product`(`product_id`) USING BTREE,
    INDEX         `idx_flow`(`flow_id`) USING BTREE,
    INDEX         `idx_step`(`step_id`) USING BTREE,
    INDEX         `idx_user`(`user_id`) USING BTREE,
    INDEX         `idx_action`(`action`) USING BTREE,
    INDEX         `idx_record_id`(`record_id`) USING BTREE,
    UNIQUE KEY `uk_product_flow_status` (`record_id`,`step_id`,`rejected_at`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '审核日志记录表';
