<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {

        Schema::create('approval_review_groups', function (Blueprint $table) {
            $table->comment('审核组表');
            $table->bigIncrements('id');
            $table->string('name', 100)->default('')->comment('角色名称');
            $table->string('code', 50)->nullable()->comment('角色代码'); 
            $table->integer('level')->default(1)->comment('角色层级');
            $table->boolean('is_inherit')->default(true)->comment('权限继承：1-是，0-否');
            $table->text('permissions')->nullable()->comment('权限设置');
            $table->text('description')->nullable()->comment('描述');
            $table->boolean('status')->default(true)->comment('状态: 1-启用, 0-禁用');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            $table->unique(['code', 'deleted_at'], 'uk_code_deleted');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_review_groups_table');
    }
};
