<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 创建审批日志表
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('approval_logs', function (Blueprint $table) {
            // 主键
            $table->comment('审核日志记录表');
            $table->bigIncrements('id')->comment('主键ID');
            
            // 审批记录相关字段
            $table->unsignedBigInteger('record_id')->comment('审批记录ID');
            
            // 产品相关字段
            $table->string('product_key', 50)->default('')->comment('产品表名');
            $table->string('product_name', 100)->default('')->comment('产品名称');
            $table->unsignedBigInteger('product_id')->comment('产品ID');
            
            // 审批流程相关字段
            $table->unsignedBigInteger('flow_id')->comment('审批流程ID');
            $table->unsignedBigInteger('step_id')->comment('审批步骤ID');
            $table->unsignedBigInteger('group_id')->comment('审核组ID');
            $table->unsignedBigInteger('user_id')->comment('审批人ID');
            $table->tinyInteger('action')->comment('审批动作：1-通过，2-拒绝');
            
            // 备注字段
            $table->text('remark')->nullable()->comment('审批意见');
            $table->text('attachments')->nullable()->comment('附件');
            
            // 创建和更新字段
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            $table->unsignedInteger('rejected_at')->default(0)->comment('拒绝时间');
            
            // 索引
            $table->index('product_id', 'idx_product');
            $table->index('flow_id', 'idx_flow');
            $table->index('step_id', 'idx_step');
            $table->index('user_id', 'idx_user');
            $table->index('action', 'idx_action');
            $table->index('record_id', 'idx_record_id');
            $table->unique(['record_id', 'step_id', 'rejected_at'], 'uk_product_flow_status');
        });
    }

    /**
     * Reverse the migrations.
     * 回滚迁移
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_logs');
    }
}; 