<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('approval_product_flows', function (Blueprint $table) {
            $table->comment('产品审批关联表');
            $table->bigIncrements('id');
            $table->string('product_key', 255)->default('')->comment('产品key');
            $table->unsignedBigInteger('flow_id')->comment('审批流程ID');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
            $table->index('product_key', 'idx_product_key');
            $table->index('flow_id', 'idx_flow');
            $table->unique(['product_key', 'flow_id', 'deleted_at'], 'uk_product_flow');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('approval_product_flows');
    }
};
