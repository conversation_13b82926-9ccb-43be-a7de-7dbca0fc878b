<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_review_members', function (Blueprint $table) {
            $table->comment('审核组成员表');
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('group_id')->default(0)->comment('审核组ID');
            $table->unsignedBigInteger('user_id')->default(0)->comment('用户ID');
            $table->string('name', 100)->default('')->comment('姓名');
            $table->string('position', 100)->default('')->comment('职位');
            $table->string('email', 100)->default('')->comment('邮箱');
            $table->tinyInteger('is_enabled')->default(1)->comment('启用状态: 1-启用, 0-禁用');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
            $table->index('group_id', 'idx_group');
            $table->index('user_id', 'idx_user');
            $table->index('email', 'idx_email');
            $table->unique(['group_id', 'user_id', 'deleted_at'], 'uk_group_user');
            $table->unique(['group_id', 'email', 'deleted_at'], 'uk_group_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_review_members');
    }
};
