<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('approval_records', function (Blueprint $table) {
            // 主键
            $table->comment('审核日志记录表');
            $table->bigIncrements('id')->comment('主键ID');
            
            // 基本信息字段
            $table->string('product_key', 50)->default('')->comment('产品表名');
            $table->string('product_name', 100)->default('')->comment('产品名称');
            $table->unsignedBigInteger('product_id')->comment('产品ID');
            $table->unsignedBigInteger('flow_id')->comment('审批流程ID');
            $table->unsignedBigInteger('step_id')->comment('审批步骤ID');
            $table->tinyInteger('status')->comment('审批状态：0-待审批，1-审批中，2-已通过，3-已拒绝');
            $table->text('remark')->nullable()->comment('审批意见');
            
            // 审计字段
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            $table->unsignedInteger('rejected_at')->default(0)->comment('拒绝时间');
            
            // 索引
            $table->index('product_id', 'idx_product');
            $table->index('flow_id', 'idx_flow');
            $table->index('step_id', 'idx_step');
            $table->index('status', 'idx_status');
            
            // 唯一索引
            $table->unique(['product_key', 'product_id', 'flow_id', 'rejected_at'], 'uk_product_flow_status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_records');
    }
};
