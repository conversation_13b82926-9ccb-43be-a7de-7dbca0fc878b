<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_flow_settings', function (Blueprint $table) {
            $table->comment('审批流程设置表');
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('flow_id')->comment('流程ID');
            $table->tinyInteger('lock_published_content')->default(0)->comment('锁定已发布内容：1-是，0-否');
            $table->tinyInteger('allow_attachments')->default(0)->comment('允许添加附件：1-是，0-否');
            $table->tinyInteger('allow_planned_date')->default(0)->comment('允许计划日期：1-是，0-否');
            $table->date('planned_date')->nullable()->comment('计划日期');
            $table->tinyInteger('enable_time_limit')->default(0)->comment('启用流程时效限制：1-是，0-否');
            $table->tinyInteger('allow_cancel_pending')->default(0)->comment('允许取消待审批流程：1-是，0-否');
            $table->tinyInteger('allow_permission_extend')->default(0)->comment('允许权限扩展：1-是，0-否');
            $table->text('notification_rules')->nullable()->comment('通知设置');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
            $table->unique(['flow_id', 'deleted_at'], 'uk_flow_deleted');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_flow_settings');
    }
};
