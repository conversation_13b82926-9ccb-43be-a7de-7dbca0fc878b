<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_flows', function (Blueprint $table) {
            $table->comment('审批流程定义表');
            $table->bigIncrements('id')->comment('主键ID');
            $table->string('name', 100)->default('')->comment('流程名称');
            $table->string('scope', 50)->default('')->index('idx_scope')->comment('流程应用内容');
            $table->text('description')->nullable()->comment('描述');
            $table->boolean('is_enabled')->default(false)->comment('是否启用：1-启用，0-禁用');
            $table->string('lang', 50)->nullable()->comment('语言标识');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_flows');
    }
};
