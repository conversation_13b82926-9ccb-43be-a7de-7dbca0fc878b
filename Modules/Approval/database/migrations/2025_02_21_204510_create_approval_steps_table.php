<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_steps', function (Blueprint $table) {
            $table->comment('审批流程步骤表');
            $table->bigIncrements('id');
            $table->unsignedBigInteger('flow_id')->comment('流程ID');
            $table->string('name', 100)->default('')->comment('步骤名称');
            $table->string('step_code', 50)->default('')->comment('步骤唯一标识代码');
            $table->integer('sort')->default(0)->comment('步骤顺序');
            $table->string('group_ids', 255)->default('')->comment('参与审核组ID列表');
            $table->tinyInteger('reject_to_draft')->default(1)->comment('拒绝时返回第一步：1-是，0-否');
            $table->string('lang', 50)->nullable()->comment('语言标识');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            $table->primary('id');
            $table->index('flow_id', 'idx_flow');
            $table->unique(['flow_id', 'step_code'], 'uk_flow_step_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_steps');
    }
};
