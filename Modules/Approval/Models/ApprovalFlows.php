<?php

namespace Modules\Approval\Models;

use Bingo\Base\BingoModel as Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property mixed $id
 */
class ApprovalFlows extends Model
{
    protected $table = 'approval_flows';

    protected $fillable = [
        'id', 'name', 'scope', 'description', 'settings', 'is_enabled', 'lang', 'creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

    /**
     * Get the steps for the approval flow.
     */
    public function steps(): HasMany
    {
        return $this->hasMany(ApprovalSteps::class, 'flow_id');
    }

    /**
     * Get the settings for the approval flow.
     */
    public function settings(): HasOne
    {
        return $this->hasOne(ApprovalSettings::class, 'flow_id');
    }
    
}
