<?php

namespace Modules\Approval\Models;

use Bingo\Base\BingoModel as Model;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ApprovalReviewGroups extends Model
{
    protected $table = 'approval_review_groups';

    protected $fillable = [
        'id', 'name', 'code', 'level', 'is_inherit', 'permissions', 'description', 'status', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

    /**
     * Get the members for the review group.
     */
    public function members(): HasMany
    {
        return $this->hasMany(ApprovalReviewMembers::class, 'group_id');
    }
}
