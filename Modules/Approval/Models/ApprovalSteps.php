<?php

namespace Modules\Approval\Models;

use Bingo\Base\BingoModel as Model;


class ApprovalSteps extends Model
{
    protected $table = 'approval_steps';

    protected $fillable = [
        'id', 'flow_id', 'name', 'step_code', 'sort', 'group_ids', 'reject_to_draft', 'lang', 'creator_id', 'created_at', 'updated_at', 'deleted_at'
    ];

    /**
     * group_ids字段的访问器和修改器
     */

    /**
     * 将数据库中的group_ids字符串转换为数组
     * 
     * @param string|null $value
     * @return array
     */
    public function getGroupIdsAttribute(?string $value): array
    {
        if (empty($value)) {
            return [];
        }
        return json_decode($value, true);
    }

    /**
     * 将group_ids数组转换为JSON字符串存储到数据库
     * 
     * @param array|null $value
     * @return void
     */
    public function setGroupIdsAttribute(?array $value): void
    {
        $this->attributes['group_ids'] = empty($value) ? [] : json_encode($value);
    }

}
