<?php

namespace Modules\Approval\Models;

use Bingo\Base\BingoModel as Model;


class ApprovalSettings extends Model
{
    protected $table = 'approval_flow_settings';

    protected $fillable = [
        'id', 'flow_id', 'lock_published_content', 'allow_attachments', 'allow_planned_date', 'planned_date', 'enable_time_limit', 'allow_cancel_pending', 'allow_permission_extend', 'notification_rules', 'creator_id', 'created_at', 'updated_at', 
    ];

    /**
     * 将notification_rules数组转换为JSON字符串存储
     *
     * @param array $value
     * @return void
     */
    public function setNotificationRulesAttribute($value): void
    {
        $this->attributes['notification_rules'] = json_encode($value);
    }

    /**
     * 将notification_rules JSON字符串转换为数组
     * 
     * @param string|null $value
     * @return array
     */
    public function getNotificationRulesAttribute($value): array
    {
        return $value ? json_decode($value, true) : [];
    }


    /**
     * 审批流程事件类型枚举
     */
    public const EVENT_TYPE_PROCESS_CHANGE = 'ProcessChange'; // 流程变更
    public const EVENT_TYPE_PROCESS_TIMEOUT = 'ProcessTimeout'; // 流程超时
    public const EVENT_TYPE_EMERGENCY_PROCESS = 'EmergencyProcessTrigger'; // 紧急流程触发

    /**
     * 通知渠道枚举
     */
    public const CHANNEL_EMAIL = 'Email'; // 邮件通知

    /**
     * 获取所有支持的事件类型
     *
     * @return array
     */
    public static function getSupportedEventTypes(): array
    {
        return [
            self::EVENT_TYPE_PROCESS_CHANGE => '流程变更',
            self::EVENT_TYPE_PROCESS_TIMEOUT => '流程超时',
            self::EVENT_TYPE_EMERGENCY_PROCESS => '紧急流程触发'
        ];
    }

    /**
     * 获取所有支持的通知渠道
     *
     * @return array
     */
    public static function getSupportedChannels(): array
    {
        return [
            self::CHANNEL_EMAIL => '邮件通知'
        ];
    }

    /**
     * 验证事件类型是否有效
     *
     * @param string $eventType
     * @return bool
     */
    public static function isValidEventType(string $eventType): bool
    {
        return in_array($eventType, array_keys(self::getSupportedEventTypes()));
    }

    /**
     * 验证通知渠道是否有效
     *
     * @param string $channel
     * @return bool
     */
    public static function isValidChannel(string $channel): bool
    {
        return in_array($channel, array_keys(self::getSupportedChannels()));
    }

}
