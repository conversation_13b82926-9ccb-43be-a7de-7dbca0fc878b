<?php

declare(strict_types=1);

namespace Modules\Approval\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 审批记录模型
 * 
 * @property int $id 主键ID
 * @property string $product_key 产品key
 * @property string $product_name 产品名称
 * @property int $product_id 产品ID
 * @property int $flow_id 审批流程ID
 * @property int $step_id 当前审批步骤ID
 * @property int $status 审批状态：0-待审批，1-审批中，2-已通过，3-已拒绝
 * @property string|null $remark 审批备注
 * @property int $creator_id 创建人ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间
 * @property int $rejected_at 拒绝时间
 */
class ApprovalRecord extends Model
{
    /**
     * @var string 数据表名
     */
    protected $table = 'approval_records';

    /**
     * @var bool 是否使用时间戳
     */
    public $timestamps = true;

    /**
     * @var string 时间戳格式
     */
    protected $dateFormat = 'U';

    /**
     * @var array 可批量赋值的属性
     */
    protected $fillable = [
        'product_key',
        'product_name',
        'product_id',
        'flow_id',
        'step_id',
        'status',
        'remark',
        'creator_id',
        'deleted_at',
        'updated_at',
        'created_at',
        'rejected_at'
    ];

    /**
     * @var array 类型转换
     */
    protected $casts = [
        'created_at' => 'integer',
        'updated_at' => 'integer',
        'deleted_at' => 'integer',
        'rejected_at' => 'integer',
    ];

    /**
     * 审批状态：待审批
     */
    public const STATUS_PENDING = 0;

    /**
     * 审批状态：审批中
     */
    public const STATUS_PROCESSING = 1;

    /**
     * 审批状态：已通过
     */
    public const STATUS_APPROVED = 2;

    /**
     * 审批状态：已拒绝
     */
    public const STATUS_REJECTED = 3;

    /**
     * 审批状态：超时
     */
    public const STATUS_TIMEOUT = 4;

    /**
     * 获取审批状态文本
     *
     * @param int $status 审批状态
     * @return string 审批状态文本
     */
    public static function getStatusText(int $status): string
    {
        return match($status) {
            self::STATUS_PENDING => '待审批',
            self::STATUS_PROCESSING => '审批中',
            self::STATUS_APPROVED => '已通过',
            self::STATUS_REJECTED => '已拒绝',
            self::STATUS_TIMEOUT => '超时',
        };
    }

    /**
     * 获取审批流程
     *
     * @return BelongsTo
     */
    public function flow(): BelongsTo
    {
        return $this->belongsTo(ApprovalFlows::class, 'flow_id');
    }

    /** 
     * 获取审批流程设置
     *
     * @return BelongsTo
     */
    public function settings(): BelongsTo
    {
        return $this->belongsTo(ApprovalSettings::class, 'flow_id','flow_id');
    }

    /**
     * 获取当前审批步骤
     *
     * @return BelongsTo
     */
    public function step(): BelongsTo
    {
        return $this->belongsTo(ApprovalSteps::class, 'step_id');
    }

    /**
     * 获取审批日志记录
     *
     * @return HasMany
     */
    public function logs(): HasMany
    {
        return $this->hasMany(ApprovalLog::class, 'product_id')->where('product_key', $this->product_key);
    }
}