<?php

declare(strict_types=1);

namespace Modules\Approval\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 审批日志模型
 * 
 * @property int $id 主键ID
 * @property int $record_id 审批记录ID
 * @property string $product_key 产品key
 * @property string $product_name 产品名称
 * @property int $product_id 产品ID
 * @property int $flow_id 审批流程ID
 * @property int $step_id 审批步骤ID
 * @property int $group_id 审核组ID
 * @property int $user_id 审批人ID
 * @property int $action 审批动作：1-通过，2-拒绝
 * @property string|null $remark 审批意见
 * @property string|null $attachments 附件
 * @property int $creator_id 创建人ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间
 * @property int $rejected_at 拒绝时间
 */
class ApprovalLog extends Model
{
    /**
     * @var string 数据表名
     */
    protected $table = 'approval_logs';

    /**
     * @var bool 是否使用时间戳
     */
    public $timestamps = true;

    /**
     * @var string 时间戳格式
     */
    protected $dateFormat = 'U';

    /**
     * @var array 可批量赋值的属性
     */
    protected $fillable = [
        'record_id',
        'product_key',
        'product_name',
        'product_id',
        'flow_id',
        'step_id',
        'group_id',
        'user_id',
        'action',
        'remark',
        'attachments',
        'creator_id',
        'deleted_at',
        'updated_at',
        'created_at',
        'rejected_at'
    ];

    /**
     * @var array 类型转换
     */
    protected $casts = [
        'record_id' => 'integer',
        'product_id' => 'integer',
        'flow_id' => 'integer',
        'step_id' => 'integer',
        'group_id' => 'integer',
        'user_id' => 'integer',
        'action' => 'integer',
        'creator_id' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
        'deleted_at' => 'integer',
        'rejected_at' => 'integer',
        'attachments' => 'array',
    ];

    /**
     * 审批动作：通过
     */
    public const ACTION_APPROVE = 1;

    /**
     * 审批动作：拒绝
     */
    public const ACTION_REJECT = 2;

    /**
     * 获取审批流程
     *
     * @return BelongsTo
     */
    public function flow(): BelongsTo
    {
        return $this->belongsTo(ApprovalFlows::class, 'flow_id');
    }

    /**
     * 获取审批步骤
     *
     * @return BelongsTo
     */
    public function step(): BelongsTo
    {
        return $this->belongsTo(ApprovalSteps::class, 'step_id');
    }

    /**
     * 获取审核组
     *
     * @return BelongsTo
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(ApprovalReviewGroups::class, 'group_id');
    }

    /**
     * 获取审批记录
     *
     * @return BelongsTo
     */
    public function record(): BelongsTo
    {
        return $this->belongsTo(ApprovalRecord::class, 'record_id');
    }
} 