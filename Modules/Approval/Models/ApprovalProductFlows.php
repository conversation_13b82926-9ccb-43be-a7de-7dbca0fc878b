<?php

namespace Modules\Approval\Models;

use Bingo\Base\BingoModel as Model;


/**
 * @property mixed $id
 * @property mixed $product_id
 * @property mixed $flow_id
 * @property mixed $step_id
 * @property mixed $status
 * @property mixed $remark
 * @property mixed $creator_id
 * @property mixed $created_at
 * @property mixed $updated_at
 * @property mixed $product_table
 */
class ApprovalProductFlows extends Model
{
    protected $table = 'approval_product_flows';

    protected $fillable = [
        'id',
        'product_key',
        'product_id',
        'flow_id',
        'step_id',
        'status',
        'remark',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];
}
