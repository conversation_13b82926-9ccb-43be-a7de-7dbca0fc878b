<?php

namespace Modules\Approval\Models;

use Bingo\Base\BingoModel as Model;


/**
 * @property mixed $action
 * @property mixed $product_id
 * @property mixed $flow_id
 * @property mixed $step_id
 * @property mixed $group_id
 * @property mixed $user_id
 * @property mixed $remark
 * @property mixed $creator_id
 * @property mixed $created_at
 * @property mixed $updated_at
 * @property mixed $deleted_at
 * @property mixed $id
 */
class ApprovalProductFlowsRecords extends Model
{
    protected $table = 'approval_product_records';

    protected $fillable = [
        'id', 'product_table', 'product_id', 'flow_id', 'step_id', 'group_id', 'user_id', 'action', 'remark', 'creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

}
