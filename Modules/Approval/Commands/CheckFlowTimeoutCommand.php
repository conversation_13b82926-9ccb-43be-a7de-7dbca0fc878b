<?php

namespace Modules\Approval\Commands;

use Modules\Approval\Models\ApprovalRecord;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Modules\Approval\Models\ApprovalSettings;
use Modules\Approval\Models\ApprovalSteps;
use Modules\Approval\Models\ApprovalReviewMembers;
use Modules\Iam\Commands\AbstractEmailCommand;
use Modules\Approval\Events\ApprovalRejected;
use Modules\Iam\Services\EmailService;
use Modules\Iam\Services\MessageService;
use Modules\Iam\Services\PasswordService;
use Modules\Iam\Services\UserService;
use Modules\Approval\Services\EmailService as ApprovalEmailService;

class CheckFlowTimeoutCommand extends AbstractEmailCommand
{
    protected $signature = 'approval:check-flow-timeout {--force}';
    protected $description = '检查审批流程是否超时';
    private const LOCK_KEY = 'approval:check-flow-timeout:lock';
    private readonly ApprovalEmailService $approvalEmailService;

    public function __construct(
        EmailService $emailService,
        MessageService $messageService,
        PasswordService $passwordService,
        UserService $userService,
        ApprovalEmailService $approvalEmailService
    ) {
        parent::__construct($emailService, $messageService, $passwordService, $userService);
        $this->approvalEmailService = $approvalEmailService;
    }

    public function handle(): int
    {
        if (! $this->acquireLock(self::LOCK_KEY)) {
            $this->error('Another instance is already running');
            return self::FAILURE;
        }
        try {
            $count = 0;
            $startTime = microtime(true);
            // //检查审批流程超时
            $count += $this->checkFlowTimeout();
            $executionTime = round(microtime(true) - $startTime, 2);
            $this->info("任务完成! 共检查 {$count} 个审批流程, 耗时 {$executionTime} 秒");
            return self::SUCCESS;
        } catch (\Exception $e) {
            $this->logError('检查审批流程超时失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return self::FAILURE;
        } finally {
            $this->releaseLock(self::LOCK_KEY);
        }
    }

    private function checkFlowTimeout(): int
    {
        // 获取待审批和处理中的审批记录及其设置
        $flows = \DB::table('approval_records as ar')
            ->select([
                'ar.id',
                'ar.flow_id',
                'ar.step_id',
                'ar.status',
                'ar.created_at',
                'afs.id as setting_id',
                'afs.allow_planned_date',
                'afs.planned_date',
                'afs.allow_cancel_pending',
                'afs.notification_rules'
            ])
            ->leftJoin('approval_flow_settings as afs', 'ar.flow_id', '=', 'afs.flow_id')
            ->whereIn('ar.status', [ApprovalRecord::STATUS_PENDING, ApprovalRecord::STATUS_PROCESSING])
            ->where('afs.allow_planned_date', 1)
            ->whereNotNull('afs.planned_date')
            ->limit(100)
            ->get();
        if ($flows->isEmpty()) {
            return 0;
        }
        $count = 0;
        foreach ($flows as $flow) {
            try {
                // 判断是否超过计划时间
                $plannedDate = Carbon::createFromTimestamp(strtotime($flow->planned_date));
                if (Carbon::now()->gt($plannedDate)) {

                    //使用业务层处理超时
                    if($flow->allow_cancel_pending){ //如果允许取消待审批
                        $this->handleTimeout($flow->id);
                    }

                    // 检查并发送通知
                    $this->sendNotifications($flow);

                    $count++;
                }
            } catch (\Exception $e) {
                $this->logError('更新审批流程状态失败', [
                    'flow_id' => $flow->id,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        return $count;
    }

    /**
     * 处理审批流程的通知发送
     * @param object $flow 审批流程对象
     * @return void
     */
    private function sendNotifications(object $flow): void
    {
        // 检查是否需要发送通知
        $notificationRules = $flow->notification_rules;
        if (empty($notificationRules)) {
            return;
        }

        // 获取当前审批流程步骤中的审批组
        $approvalStep = ApprovalSteps::find($flow->step_id);
        if(empty($approvalStep) || empty($approvalStep->group_ids)){
            return;
        }

        //查询审批组成员邮箱
        $emails = ApprovalReviewMembers::whereIn('group_id', $approvalStep->group_ids)
        ->pluck('email')->toArray();
        if(empty($emails)){
            return;
        }
        // 解码通知规则JSON
        $rules = json_decode($notificationRules, true);
        // 遍历通知规则
        foreach ($rules as $rule) {
            // 验证事件类型和通知渠道的有效性
            if (
                $rule['eventType'] === ApprovalSettings::EVENT_TYPE_PROCESS_TIMEOUT &&
                is_array($rule['channels']) &&
                in_array(ApprovalSettings::CHANNEL_EMAIL, $rule['channels'], true)
            ) {

                foreach($emails as $email){
                    try {
                        $this->approvalEmailService->sendEmail(
                            $email,
                            '审批流程超时',
                            "您有一个审批流程已超时，请尽快处理。\n审批流程ID：{$flow->id}\n审批步骤：{$approvalStep->name}\n创建时间：".date('Y-m-d H:i:s',$flow->created_at)."\n超时时间：".date('Y-m-d H:i:s',strtotime($flow->planned_date))."\n请登录系统及时处理该审批流程，避免影响业务进展。"
                        );
                    } catch (\Exception $e) {
                        $this->logError('发送邮件通知失败', [
                            'flow_id' => $flow->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
                break;
            }
        }
    }

    /**
     * 处理审批超时
     *
     * @param int $id 审批记录ID
     * @return void
     * @throws \Exception 异常
     */
    public function handleTimeout(int $id): void
    {
        try {
            DB::beginTransaction();

            // 获取并检查审批记录
            $record = ApprovalRecord::findOrFail($id);;

            // 检查当前状态是否允许设置超时
            // 如果审批记录已经是已通过、已拒绝或已超时状态，则直接返回空数组
            // 避免重复处理已完成的审批记录
            if ($record->status === ApprovalRecord::STATUS_APPROVED ||
                $record->status === ApprovalRecord::STATUS_REJECTED ||
                $record->status === ApprovalRecord::STATUS_TIMEOUT) {
                return;
            }

            // 更新审批记录状态为超时
            $record->status = ApprovalRecord::STATUS_TIMEOUT;
            $record->updated_at = time();
            $record->save();

            // 触发审批超时事件
            event(new ApprovalRejected($record->product_key, $record->product_id, '审批流程已超时'));

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
