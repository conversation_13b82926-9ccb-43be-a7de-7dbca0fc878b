<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 创建产品审批关联请求验证
 */
final class StoreApprovalProductFlowRequest extends FormRequest
{
    /**
     * 确定用户是否有权提出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'product_table' => 'required|string',
            'product_id' => 'required|integer|exists:'.$this->product_table.',id',
            'flow_id' => 'required|integer|exists:approval_flows,id',
            'remark' => 'nullable|string|max:500',
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'product_table.required' => T('Approval::validation.store_approval_product_flow.product_table.required'),
            'product_table.string' => T('Approval::validation.store_approval_product_flow.product_table.string'),
            'product_id.required' => T('Approval::validation.store_approval_product_flow.product_id.required'),
            'product_id.integer' => T('Approval::validation.store_approval_product_flow.product_id.integer'),
            'product_id.exists' => T('Approval::validation.store_approval_product_flow.product_id.exists'),
            'flow_id.required' => T('Approval::validation.store_approval_product_flow.flow_id.required'),
            'flow_id.integer' => T('Approval::validation.store_approval_product_flow.flow_id.integer'),
            'flow_id.exists' => T('Approval::validation.store_approval_product_flow.flow_id.exists'),
            'remark.string' => T('Approval::validation.store_approval_product_flow.remark.string'),
            'remark.max' => T('Approval::validation.store_approval_product_flow.remark.max'),
        ];
    }
}