<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ListApprovalMemberRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
            'keyword' => 'nullable|string|max:50',
        ];
    }

    public function messages(): array
    {
        return [
            'page.integer' => T('Approval::validation.list_approval_member.page.integer'),
            'page.min' => T('Approval::validation.list_approval_member.page.min'),
            'limit.integer' => T('Approval::validation.list_approval_member.limit.integer'), 
            'limit.min' => T('Approval::validation.list_approval_member.limit.min'),
            'limit.max' => T('Approval::validation.list_approval_member.limit.max'),
            'keyword.string' => T('Approval::validation.list_approval_member.keyword.string'),
            'keyword.max' => T('Approval::validation.list_approval_member.keyword.max'),
        ];
    }
}