<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class UpdateApprovalGroupRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:50',
            'members' => 'required|array',
            'members.*.name' => 'required|string|max:50',
            'members.*.position' => 'required|string|max:50',
            'members.*.email' => 'required|email|max:100',
            'members.*.is_enabled' => 'required|boolean'
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => T('Approval::validation.update_approval_group.name.required'),
            'name.max' => T('Approval::validation.update_approval_group.name.max'),
            'code.required' => T('Approval::validation.update_approval_group.code.required'),
            'code.max' => T('Approval::validation.update_approval_group.code.max'),
            'code.unique' => T('Approval::validation.update_approval_group.code.unique'),
            'is_inherit.required' => T('Approval::validation.update_approval_group.is_inherit.required'),
            'is_inherit.boolean' => T('Approval::validation.update_approval_group.is_inherit.boolean'),
            'description.max' => T('Approval::validation.update_approval_group.description.max'),
            'status.required' => T('Approval::validation.update_approval_group.status.required'),
            'status.boolean' => T('Approval::validation.update_approval_group.status.boolean'),
            'permissions.array' => T('Approval::validation.update_approval_group.permissions.array'),
            'permissions.*.id.required' => T('Approval::validation.update_approval_group.permissions.*.id.required'),
            'permissions.*.id.string' => T('Approval::validation.update_approval_group.permissions.*.id.string'),
            'permissions.*.name.required' => T('Approval::validation.update_approval_group.permissions.*.name.required'),
            'permissions.*.name.string' => T('Approval::validation.update_approval_group.permissions.*.name.string'),
            'permissions.*.description.string' => T('Approval::validation.update_approval_group.permissions.*.description.string'),
            'permissions.*.description.max' => T('Approval::validation.update_approval_group.permissions.*.description.max'),
            'permissions.*.actions.required' => T('Approval::validation.update_approval_group.permissions.*.actions.required'),
            'permissions.*.actions.array' => T('Approval::validation.update_approval_group.permissions.*.actions.array'),
            'permissions.*.actions.*.required' => T('Approval::validation.update_approval_group.permissions.*.actions.*.required'),
            'permissions.*.actions.*.string' => T('Approval::validation.update_approval_group.permissions.*.actions.*.string'),
            'permissions.*.actions.*.in' => T('Approval::validation.update_approval_group.permissions.*.actions.*.in'),
            'members.required' => T('Approval::validation.update_approval_group.members.required'),
            'members.array' => T('Approval::validation.update_approval_group.members.array'),
            'members.*.name.required' => T('Approval::validation.update_approval_group.members.*.name.required'),
            'members.*.name.max' => T('Approval::validation.update_approval_group.members.*.name.max'),
            'members.*.position.required' => T('Approval::validation.update_approval_group.members.*.position.required'),
            'members.*.position.max' => T('Approval::validation.update_approval_group.members.*.position.max'),
            'members.*.email.required' => T('Approval::validation.update_approval_group.members.*.email.required'),
            'members.*.email.email' => T('Approval::validation.update_approval_group.members.*.email.email'),
            'members.*.email.max' => T('Approval::validation.update_approval_group.members.*.email.max'),
            'members.*.is_enabled.required' => T('Approval::validation.update_approval_group.members.*.is_enabled.required'),
            'members.*.is_enabled.boolean' => T('Approval::validation.update_approval_group.members.*.is_enabled.boolean')
        ];
    }
}