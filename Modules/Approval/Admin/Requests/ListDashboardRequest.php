<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Approval\Enums\DashboardStatus;
/**
 * 审批仪表盘列表请求验证
 */
final class ListDashboardRequest extends FormRequest
{
    /**
     * 确定用户是否有权提出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'page' => ['sometimes', 'nullable', 'integer', 'min:1'],
            'limit' => ['sometimes', 'nullable', 'integer', 'min:1', 'max:100'],
            'status' => ['sometimes', 'nullable', 'string', 'in:'.implode(',', DashboardStatus::values())],
            'keyword' => ['sometimes', 'nullable', 'string'],
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'page.integer' => T('Approval::validation.list_dashboard.page.integer'),
            'page.min' => T('Approval::validation.list_dashboard.page.min'),
            'limit.integer' => T('Approval::validation.list_dashboard.limit.integer'),
            'limit.min' => T('Approval::validation.list_dashboard.limit.min'),
            'limit.max' => T('Approval::validation.list_dashboard.limit.max'),
            'status.string' => T('Approval::validation.list_dashboard.status.string'),
            'status.in' => T('Approval::validation.list_dashboard.status.in'),
            'keyword.string' => T('Approval::validation.list_dashboard.keyword.string'),
        ];
    }
} 