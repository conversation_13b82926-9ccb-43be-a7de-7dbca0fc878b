<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * 更新审批流程请求验证
 */
final class UpdateApprovalFlowRequest extends FormRequest
{
    /**
     * 确定用户是否有权提出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:50',
            'description' => 'nullable|string|max:200',
            'scope' => 'required|string|max:50',
            'is_enabled' => 'required|boolean',
            'settings' => 'nullable|sometimes|array',
            'settings.lock_published_content' => 'nullable|boolean',
            'settings.allow_attachments' => 'nullable|boolean',
            'settings.allow_planned_date' => 'nullable|boolean',
            'settings.planned_date' => 'nullable|date',
            'settings.enable_time_limit' => 'nullable|boolean',
            'settings.allow_cancel_pending' => 'nullable|boolean',
            'settings.allow_permission_extend' => 'nullable|boolean',
            'settings.notification_rules' => 'nullable|array',
            'steps' => 'required|array|min:1',
            'steps.*.name' => 'required|string|max:50',
            'steps.*.stepCode' => 'required|string|max:50',
            'steps.*.sort' => 'required|integer|min:1',
            'steps.*.groupIds' => 'required|array',
            'steps.*.groupIds.*' => 'required|integer|exists:approval_review_groups,id',
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => T('Approval::validation.update_approval_flow.name.required'),
            'name.string' => T('Approval::validation.update_approval_flow.name.string'),
            'name.max' => T('Approval::validation.update_approval_flow.name.max'),
            'description.string' => T('Approval::validation.update_approval_flow.description.string'),
            'description.max' => T('Approval::validation.update_approval_flow.description.max'),
            'scope.required' => T('Approval::validation.update_approval_flow.scope.required'),
            'scope.string' => T('Approval::validation.update_approval_flow.scope.string'),
            'scope.max' => T('Approval::validation.update_approval_flow.scope.max'),
            'is_enabled.required' => T('Approval::validation.update_approval_flow.is_enabled.required'),
            'is_enabled.boolean' => T('Approval::validation.update_approval_flow.is_enabled.boolean'),
            'settings.lock_published_content.required' => T('Approval::validation.update_approval_flow.settings.lock_published_content.required'),
            'settings.lock_published_content.boolean' => T('Approval::validation.update_approval_flow.settings.lock_published_content.boolean'),
            'settings.allow_attachments.required' => T('Approval::validation.update_approval_flow.settings.allow_attachments.required'),
            'settings.allow_attachments.boolean' => T('Approval::validation.update_approval_flow.settings.allow_attachments.boolean'),
            'settings.allow_planned_date.required' => T('Approval::validation.update_approval_flow.settings.settings.allow_planned_date.required'),
            'settings.allow_planned_date.boolean' => T('Approval::validation.update_approval_flow.settings.settings.allow_planned_date.boolean'),
            'settings.planned_date.date' => T('Approval::validation.update_approval_flow.settings.settings.planned_date.date'),
            'settings.enable_time_limit.required' => T('Approval::validation.update_approval_flow.settings.settings.enable_time_limit.required'),
            'settings.enable_time_limit.boolean' => T('Approval::validation.update_approval_flow.settings.settings.enable_time_limit.boolean'),
            'settings.allow_cancel_pending.required' => T('Approval::validation.update_approval_flow.settings.settings.allow_cancel_pending.required'),
            'settings.allow_cancel_pending.boolean' => T('Approval::validation.update_approval_flow.settings.settings.allow_cancel_pending.boolean'),
            'settings.allow_permission_extend.required' => T('Approval::validation.update_approval_flow.settings.settings.allow_permission_extend.required'),
            'settings.allow_permission_extend.boolean' => T('Approval::validation.update_approval_flow.settings.settings.allow_permission_extend.boolean'),
            'settings.notification_rules.present' => T('Approval::validation.update_approval_flow.settings.settings.notification_rules.present'),
            'settings.notification_rules.array' => T('Approval::validation.update_approval_flow.settings.settings.notification_rules.array'),
            'steps.required' => T('Approval::validation.update_approval_flow.steps.required'),
            'steps.array' => T('Approval::validation.update_approval_flow.steps.array'),
            'steps.min' => T('Approval::validation.update_approval_flow.steps.min'),
            'steps.*.name.required' => T('Approval::validation.update_approval_flow.steps.*.name.required'),
            'steps.*.name.string' => T('Approval::validation.update_approval_flow.steps.*.name.string'),
            'steps.*.name.max' => T('Approval::validation.update_approval_flow.steps.*.name.max'),
            'steps.*.stepCode.required' => T('Approval::validation.update_approval_flow.steps.*.stepCode.required'),
            'steps.*.stepCode.string' => T('Approval::validation.update_approval_flow.steps.*.stepCode.string'),
            'steps.*.stepCode.max' => T('Approval::validation.update_approval_flow.steps.*.stepCode.max'),
            'steps.*.sort.required' => T('Approval::validation.update_approval_flow.steps.*.sort.required'),
            'steps.*.sort.integer' => T('Approval::validation.update_approval_flow.steps.*.sort.integer'),
            'steps.*.sort.min' => T('Approval::validation.update_approval_flow.steps.*.sort.min'),
            'steps.*.groupIds.required' => T('Approval::validation.update_approval_flow.steps.*.groupIds.required'),
            'steps.*.groupIds.array' => T('Approval::validation.update_approval_flow.steps.*.groupIds.array'),
            'steps.*.groupIds.*.required' => T('Approval::validation.update_approval_flow.steps.*.groupIds.*.required'),
            'steps.*.groupIds.*.integer' => T('Approval::validation.update_approval_flow.steps.*.groupIds.*.integer'),
            'steps.*.groupIds.*.exists' => T('Approval::validation.update_approval_flow.steps.*.groupIds.*.exists'),
        ];
    }
}
