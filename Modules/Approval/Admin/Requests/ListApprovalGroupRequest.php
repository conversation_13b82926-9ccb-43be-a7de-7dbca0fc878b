<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class ListApprovalGroupRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'keyword' => 'nullable|string|max:50',
            'sort_field' => 'nullable|string',
            'sort_order' => 'nullable|string|in:asc,desc',
            'page' => ['sometimes', 'integer', 'min:1'],
            'limit' => ['sometimes', 'integer', 'min:1', 'max:100'],
        ];
    }

    public function messages(): array
    {
        return [
            'keyword.string' => T('Approval::validation.list_approval_group.keyword.string'),
            'keyword.max' => T('Approval::validation.list_approval_group.keyword.max'),
            'sort_field.string' => T('Approval::validation.list_approval_group.sort_field.string'), 
            'sort_order.string' => T('Approval::validation.list_approval_group.sort_order.string'),
            'sort_order.in' => T('Approval::validation.list_approval_group.sort_order.in'),
            'page.integer' => T('Approval::validation.list_approval_group.page.integer'),
            'page.min' => T('Approval::validation.list_approval_group.page.min'),
            'limit.integer' => T('Approval::validation.list_approval_group.limit.integer'),
            'limit.min' => T('Approval::validation.list_approval_group.limit.min'),
            'limit.max' => T('Approval::validation.list_approval_group.limit.max'),
        ];
    }
}