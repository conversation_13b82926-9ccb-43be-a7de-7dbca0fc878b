<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class StoreApprovalGroupRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:50',
            'members' => 'required|array',
            'members.*.name' => 'required|string|max:50',
            'members.*.position' => 'required|string|max:50',
            'members.*.email' => 'required|email|max:100',
            'members.*.is_enabled' => 'required|boolean'
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => T('Approval::validation.store_approval_group.name.required'),
            'name.max' => T('Approval::validation.store_approval_group.name.max'),
            'members.required' => T('Approval::validation.store_approval_group.members.required'),
            'members.array' => T('Approval::validation.store_approval_group.members.array'),
            'members.*.name.required' => T('Approval::validation.store_approval_group.members.*.name.required'),
            'members.*.name.max' => T('Approval::validation.store_approval_group.members.*.name.max'),
            'members.*.position.required' => T('Approval::validation.store_approval_group.members.*.position.required'),
            'members.*.position.max' => T('Approval::validation.store_approval_group.members.*.position.max'),
            'members.*.email.required' => T('Approval::validation.store_approval_group.members.*.email.required'),
            'members.*.email.email' => T('Approval::validation.store_approval_group.members.*.email.email'),
            'members.*.email.max' => T('Approval::validation.store_approval_group.members.*.email.max'),
            'members.*.is_enabled.required' => T('Approval::validation.store_approval_group.members.*.is_enabled.required'),
            'members.*.is_enabled.boolean' => T('Approval::validation.store_approval_group.members.*.is_enabled.boolean')
        ];
    }
}