<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class ListApprovalFlowRequest extends FormRequest
{
    /**
     * 确定用户是否有权发出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'page' => ['sometimes', 'integer', 'min:1'],
            'limit' => ['sometimes', 'integer', 'min:1', 'max:100'],
            'keyword' => ['sometimes', 'string', 'max:100'],
            'is_enabled' => ['sometimes', 'boolean'],
            'start_date' => ['sometimes', 'date'],
            'end_date' => ['sometimes', 'date', 'after_or_equal:start_date'],
            'sort_field' => ['sometimes', 'string', Rule::in(['id', 'created_at', 'updated_at'])],
            'sort_order' => ['sometimes', 'string', Rule::in(['asc', 'desc'])],
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     */
    public function messages(): array
    {
        return [
            'page.integer' => T('Approval::validation.list_approval_flow.page.integer'),
            'page.min' => T('Approval::validation.list_approval_flow.page.min'),
            'limit.integer' => T('Approval::validation.list_approval_flow.limit.integer'), 
            'limit.min' => T('Approval::validation.list_approval_flow.limit.min'),
            'limit.max' => T('Approval::validation.list_approval_flow.limit.max'),
            'keyword.string' => T('Approval::validation.list_approval_flow.keyword.string'),
            'keyword.max' => T('Approval::validation.list_approval_flow.keyword.max'),
            'is_enabled.boolean' => T('Approval::validation.list_approval_flow.is_enabled.boolean'),
            'start_date.date' => T('Approval::validation.list_approval_flow.start_date.date'),
            'end_date.date' => T('Approval::validation.list_approval_flow.end_date.date'),
            'end_date.after_or_equal' => T('Approval::validation.list_approval_flow.end_date.after_or_equal'),
            'sort_field.string' => T('Approval::validation.list_approval_flow.sort_field.string'),
            'sort_field.in' => T('Approval::validation.list_approval_flow.sort_field.in'),
            'sort_order.string' => T('Approval::validation.list_approval_flow.sort_order.string'),
            'sort_order.in' => T('Approval::validation.list_approval_flow.sort_order.in'),
        ];
    }
} 