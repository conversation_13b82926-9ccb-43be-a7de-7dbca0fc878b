<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreApprovalLogRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'action' => ['required', 'integer', 'in:1,2'],
            'comment' => ['nullable', 'string', 'max:1000'],
            'attachments' => ['nullable', 'array'],
            'attachments.*' => ['nullable', 'string'],
        ];
    }


    public function messages(): array
    {
        return [
            'action.required' => T('Approval::validation.store_approval_product_record.action.required'),
            'action.integer' => T('Approval::validation.store_approval_product_record.action.integer'),
            'action.in' => T('Approval::validation.store_approval_product_record.action.in'),
            'comment.string' => T('Approval::validation.store_approval_product_record.comment.string'),
            'comment.max' => T('Approval::validation.store_approval_product_record.comment.max'),
            'attachments.array' => T('Approval::validation.store_approval_product_record.attachments.array'),
            'attachments.*.string' => T('Approval::validation.store_approval_product_record.attachments._string'),
        ];
    }
}
