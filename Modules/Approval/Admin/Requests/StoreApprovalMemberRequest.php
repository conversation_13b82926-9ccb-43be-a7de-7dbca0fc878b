<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * 添加审批角色成员请求验证
 */
final class StoreApprovalMemberRequest extends FormRequest
{
    /**
     * 确定用户是否有权提出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'members' => 'required|array|min:1',
            'members.*.name' => 'required|string|max:50',
            'members.*.position' => 'nullable|string|max:50',
            'members.*.email' => 'nullable|email|max:100',
            'members.*.is_enabled' => 'required|boolean',
            'groupId' => 'required|integer|min:1'
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'members.required' => T('Approval::validation.store_approval_member.members.required'),
            'members.array' => T('Approval::validation.store_approval_member.members.array'),
            'members.min' => T('Approval::validation.store_approval_member.members.min'),
            'members.*.name.required' => T('Approval::validation.store_approval_member.members.*.name.required'),
            'members.*.name.string' => T('Approval::validation.store_approval_member.members.*.name.string'),
            'members.*.name.max' => T('Approval::validation.store_approval_member.members.*.name.max'),
            'members.*.position.string' => T('Approval::validation.store_approval_member.members.*.position.string'),
            'members.*.position.max' => T('Approval::validation.store_approval_member.members.*.position.max'),
            'members.*.email.email' => T('Approval::validation.store_approval_member.members.*.email.email'),
            'members.*.email.max' => T('Approval::validation.store_approval_member.members.*.email.max'),
            'members.*.is_enabled.required' => T('Approval::validation.store_approval_member.members.*.is_enabled.required'),
            'members.*.is_enabled.boolean' => T('Approval::validation.store_approval_member.members.*.is_enabled.boolean'),
            'groupId.required' => T('Approval::validation.store_approval_member.groupId.required'),
            'groupId.integer' => T('Approval::validation.store_approval_member.groupId.integer'),
            'groupId.min' => T('Approval::validation.store_approval_member.groupId.min'),
        ];
    }
}