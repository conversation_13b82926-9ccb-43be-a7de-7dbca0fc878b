<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * 更新审批角色成员状态请求验证
 */
final class UpdateApprovalMemberStatusRequest extends FormRequest
{
    /**
     * 确定用户是否有权提出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'isEnabled' => 'required|boolean',
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'isEnabled.required' => T('Approval::validation.update_approval_member_status.isEnabled.required'),
            'isEnabled.boolean' => T('Approval::validation.update_approval_member_status.isEnabled.boolean'),
        ];
    }
} 