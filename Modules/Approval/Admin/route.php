<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Modules\Approval\Admin\Controllers\ApprovalFlowController;
use Modules\Approval\Admin\Controllers\ApprovalGroupController;
use Modules\Approval\Admin\Controllers\ApprovalMemberController;
use Modules\Approval\Admin\Controllers\ApprovalSettingController;
use Modules\Approval\Admin\Controllers\ApprovalProductFlowController;
use Modules\Approval\Admin\Controllers\ApprovalRecordController;
use Modules\Approval\Admin\Controllers\ApprovalDashboardController;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| 模块的管理后台路由定义
|
*/

// 原有路由保留
Route::prefix('approval')->group(function () {
    // 审批面板

    Route::post('/dashboard', [ApprovalDashboardController::class, 'index'])->name('admin.approval.dashboard');

    // 审批组相关接口
    Route::prefix('groups')->group(function () {
        // 获取审批组列表
        Route::get('/', [ApprovalGroupController::class, 'index'])->name('admin.approval.groups.index');

        // 获取审批组详情
        Route::get('/{id}', [ApprovalGroupController::class, 'show'])->name('admin.approval.groups.show');

        // 创建审批组
        Route::post('/', [ApprovalGroupController::class, 'store'])->name('admin.approval.groups.store');

        // 更新审批组
        Route::put('/{id}', [ApprovalGroupController::class, 'update'])->name('admin.approval.groups.update');

        // 删除审批组
        Route::delete('/{id}', [ApprovalGroupController::class, 'destroy'])->name('admin.approval.groups.destroy');

        //复制审批组
        Route::post('/{id}/copy', [ApprovalGroupController::class, 'copy'])->name('admin.approval.groups.copy');

        // 测试
        Route::get('/1/test', [ApprovalGroupController::class, 'test'])->name('admin.approval.groups.test');
    });

    // 权限列表相关接口
    Route::prefix('permissions')->group(function () {
        // 获取权限列表
        Route::get('/', [ApprovalGroupController::class, 'permissions'])->name('admin.approval.permissions.index');
    });

    // 审批组成员相关接口
    Route::prefix('groups/{groupId}/members')->group(function () {
        // 获取审批组成员列表
        Route::get('/', [ApprovalMemberController::class, 'indexByGroup'])->name('admin.approval.groups.members.index');

        // 添加审批组成员
        Route::post('/', [ApprovalMemberController::class, 'store'])->name('admin.approval.groups.members.store');

        // 批量导入审批组成员
        Route::post('/import', [ApprovalMemberController::class, 'import'])->name('admin.approval.groups.members.import');
    });

    // 审批组成员操作接口
    Route::prefix('/members')->group(function () {
        // 更新审批组成员状态
        Route::put('/{id}/status', [ApprovalMemberController::class, 'updateStatus'])->name('admin.approval.members.update-status');

        // 删除审批组成员
        Route::delete('/{id}', [ApprovalMemberController::class, 'destroy'])->name('admin.approval.members.destroy');

         // 获取审批组成员列表
         Route::get('/', [ApprovalMemberController::class, 'index'])->name('admin.approval.members.index');
    });

    // 审批流程相关接口
    Route::prefix('flows')->group(function () {
        // 获取流程列表
        Route::get('/', [ApprovalFlowController::class, 'index'])->name('admin.approval.flows.index');

        // 获取流程详情
        Route::get('/{id}', [ApprovalFlowController::class, 'show'])->name('admin.approval.flows.show');

        // 创建流程
        Route::post('/', [ApprovalFlowController::class, 'store'])->name('admin.approval.flows.store');

        // 更新流程
        Route::put('/{id}', [ApprovalFlowController::class, 'update'])->name('admin.approval.flows.update');

        // 删除流程
        Route::delete('/{id}', [ApprovalFlowController::class, 'destroy'])->name('admin.approval.flows.destroy');

        // 获取审批流程设置
        Route::get('/{id}/setting', [ApprovalSettingController::class, 'index'])->name('admin.approval.settings.index');

        // 获取审批流程设置结构
        Route::get('/setting/structure', [ApprovalSettingController::class, 'structure'])->name('admin.approval.settings.structure');

        // 复制审批流程
        Route::post('/{id}/copy', [ApprovalFlowController::class, 'copy'])->name('admin.approval.flows.copy');

        // 开关审批流程
        Route::post('/{id}/switch', [ApprovalFlowController::class, 'switch'])->name('admin.approval.flows.switch');
    });

    //创建产品审批关联
    Route::prefix('products')->group(function () {
        // 创建产品审批关联
        Route::post('/', [ApprovalProductFlowController::class,'storeProductFlow'])->name('admin.approval.products.store');

        // 删除产品审批关联
        Route::delete('/{id}', [ApprovalProductFlowController::class, 'destroyProductFlow'])->name('admin.approval.products.destroy');

        // 获取产品审批关联列表
        Route::get('/', [ApprovalProductFlowController::class, 'index'])->name('admin.approval.products.index');
    });

    // 审批记录相关接口
    Route::prefix('records')->group(function () {
        // 获取审批记录列表
        Route::get('/', [ApprovalRecordController::class, 'index'])->name('admin.approval.records.index');

        // 根据productKey和productId获取审批进度
        Route::get('/product/{productKey}/{productId}', [ApprovalRecordController::class, 'getApprovalRecordByProduct'])->name('admin.approval.records.getApprovalRecordByProduct');

        // 获取审批进度
        Route::get('/{id}/progress', [ApprovalRecordController::class, 'progress'])->name('admin.approval.records.progress');

        // 添加审批进度
        Route::post('/{id}/progress', [ApprovalRecordController::class, 'addProgress'])->name('admin.approval.records.addProgress');

        // 删除审批记录
        Route::delete('/{id}', [ApprovalRecordController::class, 'destroy'])->name('admin.approval.records.destroy');
    });

});

// 审批流程相关接口
// Route::prefix('admin/approval')->group(function () {
//     // 流程相关接口
//     Route::prefix('flows')->group(function () {
//         // 创建流程（包含步骤）
//         Route::post('/', [ApprovalFlowController::class, 'store'])->name('admin.approval.flows.store');

//         // 更新流程（包含步骤）
//         Route::put('/{id}', [ApprovalFlowController::class, 'update'])->name('admin.approval.flows.update');
//     });

//     // 角色成员相关接口
//     Route::prefix('roles')->group(function () {
//         // 获取角色成员列表
//         Route::get('/{roleId}/members', [ApprovalMemberController::class, 'index'])->name('admin.approval.roles.members.index');

//         // 添加角色成员
//         Route::post('/{roleId}/members', [ApprovalMemberController::class, 'store'])->name('admin.approval.roles.members.store');

//         // 批量导入角色成员
//         Route::post('/{roleId}/members/import', [ApprovalMemberController::class, 'import'])->name('admin.approval.roles.members.import');
//     });

//     // 成员相关接口
//     Route::prefix('members')->group(function () {
//         // 更新成员状态
//         Route::put('/{id}/status', [ApprovalMemberController::class, 'updateStatus'])->name('admin.approval.members.update-status');

//         // 删除角色成员
//         Route::delete('/{id}', [ApprovalMemberController::class, 'destroy'])->name('admin.approval.members.destroy');
//     });


// });
