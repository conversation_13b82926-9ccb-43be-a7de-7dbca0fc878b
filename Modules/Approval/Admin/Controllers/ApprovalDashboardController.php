<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Controllers;

use App\Http\Controllers\Controller;
use Modules\Approval\Admin\Requests\ListDashboardRequest;
use Modules\Approval\Services\ApprovalRecordService;
use Modules\Approval\Enums\DashboardStatus;

/**
 * 审批面板控制器
 */
class ApprovalDashboardController extends Controller
{

    public function __construct(
        private ApprovalRecordService $service
    ) {
    }
    
    /**
     * 获取审批面板数据
     * 
     * @return array
     */
    public function index(ListDashboardRequest $request): array
    {
        $params = $request->validated();
        return $this->service->dashboard(
           [
            'page' => $params['page'] ?? 1,
            'limit' => $params['limit'] ?? 10,
            'status' => $params['status'] ?? DashboardStatus::PENDING->value,
            'keyword' => $params['keyword'] ?? ''
           ]
        );
    }
} 