<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Controllers;

use App\Http\Controllers\Controller;
use Bingo\Exceptions\BizException;
use Modules\Approval\Admin\Requests\ListApprovalFlowRequest;
use Modules\Approval\Admin\Requests\StoreApprovalFlowRequest;
use Modules\Approval\Admin\Requests\UpdateApprovalFlowRequest;
use Modules\Approval\Enums\ApprovalErrorCode;
use Modules\Approval\Services\ApprovalFlowService;

/**
 * 审批流程控制器
 */
final class ApprovalFlowController extends Controller
{
    /**
     * 构造函数
     */
    public function __construct(
        private readonly ApprovalFlowService $service
    ) {
    }

    /**
     * 获取审批流程列表
     */
    public function index(ListApprovalFlowRequest $request): array
    {
        $result = $this->service->getFlowList($request->validated());

        return [
            'items' => $result->items(),
            'total' => $result->total()
        ];
    }

    /**
     * 获取审批流程详情
     */
    public function show(int $id): array
    {
        $item = $this->service->getFlowDetail($id);
        if (!$item) {
            throw BizException::throws(ApprovalErrorCode::FLOW_NOT_FOUND);
        }

        return [
            'item' => $item
        ];
    }

    /**
     * 创建审批流程（包含步骤）
     */
    public function store(StoreApprovalFlowRequest $request): array
    {
        $data = $request->validated();
        $result = $this->service->createFlow($data);

        return [
            'item' => $result
        ];
    }

    /**
     * 更新审批流程（包含步骤）
     */
    public function update(int $id, UpdateApprovalFlowRequest $request): array
    {
        $data = $request->validated();
        $result = $this->service->updateFlow($id, $data);

        return [
            'item' => $result
        ];
    }

    /**
     * 删除审批流程
     * 
     * @param int $id
     * @return array
     */
    public function destroy(int $id): array
    {
        $this->service->deleteFlow($id);

        return [];
    }

    /**
     * 复制审批流程
     * 
     * @param int $id
     * @return array
     */
    public function copy(int $id): array
    {
        $result = $this->service->copyFlow($id);

        return [
            'item' => $result   
        ];
    }

    /**
     * 开关审批流程
     * 
     * @param int $id
     * @return array
     */
    public function switch(int $id): array
    {
        $this->service->switchFlow($id);

        return [];
    }
}
