<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Controllers;

use App\Http\Controllers\Controller;
use Modules\Approval\Enums\ApprovalProduct;
use Modules\Approval\Services\ApprovalProductFlowService;
use Modules\Approval\Admin\Requests\StoreApprovalProductFlowRequest;
use Illuminate\Http\Request;

/**
 * 产品审批流程控制器
 */
final class ApprovalProductFlowController extends Controller
{
    /**
     * 构造函数
     */
    public function __construct(
        private readonly ApprovalProductFlowService $service
    ) {
    }

    /**
     * 创建产品审批关联
     */
    public function storeProductFlow(StoreApprovalProductFlowRequest $request): array
    {
        $data = $request->validated();
        $result = $this->service->createProductFlow($data);

        return [
            'item' => $result
        ];
    }

    /**
     * 删除产品审批关联
     */
    public function destroyProductFlow(int $id): array
    {
        $this->service->deleteProductFlow($id);

        return [
            'message' => '删除成功'
        ];
    }

    /**
     * 获取产品审批关联列表
     */
    public function index(Request $request): array
    {
        $locale = $request->get('locale', 'zh_CN');
        $result = ApprovalProduct::getList($locale);

        return $result;
    }
}