<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Controllers;

use App\Http\Controllers\Controller;
use Modules\Approval\Admin\Requests\ListApprovalRecordRequest;
use Modules\Approval\Services\ApprovalRecordService;
use Modules\Approval\Admin\Requests\StoreApprovalLogRequest;

/**
 * 审批记录控制器
 */
class ApprovalRecordController extends Controller
{
    public function __construct(
        private readonly ApprovalRecordService $service
    ) {
    }

    /**
     * 获取审批记录列表
     */
    public function index(ListApprovalRecordRequest $request)
    {
        $data = $request->validated();
        $result = $this->service->getApprovalRecordList($data);
        return [
            'items' => $result['items'],
            'total' => $result['total'],
            'page' => $result['page'],
            'limit' => $result['limit']
        ];
    }

    /**
     * 获取审批进度
     */
    public function progress(int $id)
    {
        $result = $this->service->getProductFlowProgress($id);
        return $result;
    }

    /**
     * 添加审批进度
     */
    public function addProgress(int $id, StoreApprovalLogRequest $request)
    {
        $result = $this->service->addProgress($id, $request->validated());
        return $result;
    }

    /**
     * 删除审批记录
     */
    public function destroy(int $id)
    {
        $this->service->deleteApprovalRecord($id);
    }

    /**
     * 根据productKey和productId获取审批记录
     * @param string $productKey 产品key
     * @param int $productId 产品id
     * @return array 审批记录
     */
    public function getApprovalRecordByProduct(string $productKey, int $productId)
    {
        $result = $this->service->getApprovalRecordByProduct($productKey, $productId);
        return $result;
    }
} 