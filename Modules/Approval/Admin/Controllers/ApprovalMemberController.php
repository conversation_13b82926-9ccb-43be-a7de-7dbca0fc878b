<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Approval\Admin\Requests\StoreApprovalMemberRequest;
use Modules\Approval\Admin\Requests\UpdateApprovalMemberStatusRequest;
use Modules\Approval\Services\ApprovalMemberService;
use Modules\Approval\Admin\Requests\ListApprovalMemberRequest;

/**
 * 审批角色成员控制器
 */
final class ApprovalMemberController extends Controller
{
    /**
     * 构造函数
     */
    public function __construct(
        private readonly ApprovalMemberService $service
    ) {
    }

    /**
     * 获取审批组成员列表
     */
    public function indexByGroup(int $groupId, ListApprovalMemberRequest $request): array
    {
        $params = $request->validated();
        $result = $this->service->getMemberListByGroup($groupId, $params);

        return [
            'items' => $result->items(),
            'total' => $result->total(),
            'page' => $result->currentPage(),
            'pageSize' => $result->perPage(),
            'hasMore' => $result->hasMorePages()
        ];
    }

     /**
     * 获取审批组成员列表
     */
    public function index(ListApprovalMemberRequest $request): array
    {
        $params = $request->validated();
        $result = $this->service->getMemberList($params);

        return [
            'items' => $result->items(),
            'total' => $result->total(),
            'page' => $result->currentPage(),
            'pageSize' => $result->perPage(),
            'hasMore' => $result->hasMorePages()
        ];
    }

    /**
     * 添加审批组成员
     */
    public function store(int $groupId, StoreApprovalMemberRequest $request): array
    {
        $data = $request->validated();
        $result = $this->service->addMembers($groupId, $data['members']);

        return [
            'success' => $result,
        ];
    }

    /**
     * 批量导入角色成员
     */
    public function import(int $groupId, Request $request): array
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls|max:10240',
        ]);

        $file = $request->file('file');
        $result = $this->service->importMembers($groupId, $file);

        return $result;
    }

    /**
     * 更新成员状态
     */
    public function updateStatus(int $id, UpdateApprovalMemberStatusRequest $request): array
    {
        $data = $request->validated();
        $result = $this->service->updateMemberStatus($id, $data['isEnabled']);

        return [
            'success' => $result,
        ];
    }

    /**
     * 删除角色成员
     */
    public function destroy(int $id): array
    {
        $result = $this->service->deleteMember($id);

        return [
            'success' => $result,
        ];
    }
} 