<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Controllers;

use App\Http\Controllers\Controller;
use Bingo\Exceptions\BizException;
use Modules\Approval\Admin\Requests\ListApprovalGroupRequest;
use Modules\Approval\Admin\Requests\StoreApprovalGroupRequest;
use Modules\Approval\Admin\Requests\UpdateApprovalGroupRequest;
use Modules\Approval\Enums\ApprovalErrorCode;
use Modules\Approval\Services\ApprovalGroupService;
use Modules\Approval\Domain\Business\ApprovalRecordBusiness;
use Modules\Approval\Services\EmailService;

/**
 * 审批组控制器
 */
final class ApprovalGroupController extends Controller
{
    /**
     * 构造函数
     */
    public function __construct(
        private readonly ApprovalGroupService $service,
        private readonly ApprovalRecordBusiness $approvalRecordBusiness,
        private readonly EmailService $emailService
    ) {
    }

    /**
     * 获取审批组列表
     * @param ListApprovalGroupRequest $request
     * @return array
     */
    public function index(ListApprovalGroupRequest $request): array
    {
        $result = $this->service->getGroupList($request->validated());

        return [
            'items' => $result->items(),
            'total' => $result->total(),
            'page' => $result->currentPage(),
            'pageSize' => $result->perPage(),
            'hasMore' => $result->hasMorePages()
        ];
    }

    /**
     * 获取审批组详情
     */
    public function show(int $id): array
    {
        $item = $this->service->getGroupDetail($id);
        if (!$item) {
            throw BizException::throws(ApprovalErrorCode::GROUP_NOT_FOUND);
        }

        return ['item' => $item];
    }

    /**
     * 创建审批组
     */
    public function store(StoreApprovalGroupRequest $request): array
    {
         $data = $request->validated();

        $result = $this->service->createGroup($data);

        return ['item' => $result];
    }

    /**
     * 更新审批组
     */
    public function update(int $id, UpdateApprovalGroupRequest $request): array
    {
        $data = $request->validated();
        $result = $this->service->updateGroup($id, $data);

        return ['item' => $result];
    }

    /**
     * 删除审批组
     */
    public function destroy(int $id): array
    {
        $this->service->deleteGroup($id);

        return [];
    }

   
    public function permissions(): array
    {
        $result = $this->service->getGroupPermissions();

        return ['item' => $result];
    }

    /**
     * 复制审批组
     * @param int $id
     * @return array
     */
    public function copy(int $id): array
    {
        $result = $this->service->copyGroup($id);

        return ['item' => $result];
    }

    /**
     * 测试发送邮件功能
     * @return void
     */
    public function test(): void
    {
        
    }
}
