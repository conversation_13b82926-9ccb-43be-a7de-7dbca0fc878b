<?php

declare(strict_types=1);

namespace Modules\Approval\Admin\Controllers;

use App\Http\Controllers\Controller;
use Modules\Approval\Services\ApprovalFlowService;

/**
 * 审批流程设置控制器
 */
final class ApprovalSettingController extends Controller
{
    private ApprovalFlowService $service;

    public function __construct(ApprovalFlowService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取审批流程设置结构
     */
    public function structure(): array
    {
        return [
            'structure' => $this->service->getSettingsStructure()
        ];
    }
}
