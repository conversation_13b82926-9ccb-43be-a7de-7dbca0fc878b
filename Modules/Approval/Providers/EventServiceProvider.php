<?php

// app/Providers/EventServiceProvider.php

namespace Modules\Approval\Providers;


use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Approval\Events\ApprovalCreated;
use Modules\Approval\Listeners\HandleApprovalCreated;
use Modules\Approval\Events\ApprovalPassed;
use Modules\Approval\Listeners\HandleApprovalPassed;
use Modules\Approval\Events\ApprovalRejected;
use Modules\Approval\Listeners\HandleApprovalRejected;


class EventServiceProvider extends ServiceProvider
{
   /**
     * @var array 事件到监听器的映射
     */
    protected $listen = [
        ApprovalCreated::class => [
            HandleApprovalCreated::class, // 审批创建事件
        ],
        ApprovalPassed::class => [
            HandleApprovalPassed::class, // 审批通过事件
        ],

        ApprovalRejected::class => [
            HandleApprovalRejected::class, // 审批拒绝事件
        ],
    ];



    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot(): void
    {
        parent::boot();
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
