<?php

namespace Modules\Approval\Providers;

use Bingo\Providers\BingoModuleServiceProvider;
use Modules\Members\Enums\MenuType;
use Modules\Edm\Enums\MessageType;
use Modules\Approval\Commands\CheckFlowTimeoutCommand;

class ApprovalServiceProvider extends BingoModuleServiceProvider
{
    public function boot(): void
    {
        $path = dirname(__DIR__, 2).DIRECTORY_SEPARATOR.'Approval'.DIRECTORY_SEPARATOR.'Lang';
        $this->loadTranslationsFrom($path, 'Approval');
        $this->registerNavigation();
        $this->registerModulePermissions();
        $this->registerModuleSchedule();
        $this->registerConsole();

    }

    /**
     * route path
     *
     * @return string
     */
    public function moduleName(): string
    {
        return 'Approval';
    }

    protected function navigation(): array
    {
        return [
            [
                "key" => "approval",
                "parent" => "application",
                "nav_name" => T("Approval::nav.admin_setting.approval"),
                "path" => "/approval",
                "icon" => "Nav/Asset/menu_icon/approval.png",
                "order" => 6,
                "children" => [
                    // [
                    //     "key" => "groups",
                    //     "parent" => "approval",
                    //     "nav_name" => T("Approval::nav.admin_setting.groups"),
                    //     "path" => "/approval/groups",
                    //     "icon" => "Grid",
                    //     "order" => 1,
                    // ],
                    [
                        "key" => "flows",
                        "parent" => "approval",
                        "nav_name" => T("Approval::nav.admin_setting.flows"),
                        "path" => "/approval/flows",
                        "icon" => "Connection",
                        "order" => 2,
                    ],
                    // [
                    //     "key" => "records",
                    //     "parent" => "approval",
                    //     "nav_name" => T("Approval::nav.admin_setting.records"),
                    //     "path" => "/approval/records",
                    //     "icon" => "Document",
                    //     "order" => 3,
                    // ],
                ]
            ]
        ];
    }

    /**
     * 注册配置
     * @return array
     */
    public function registerSettings(): array
    {
        return [
        ];
    }

    /**
     * 注册权限
     * 
     * @return array
     */
    public function registerPermissions(): array
    {
        $admin = [
            // 审批管理顶级权限
            [
                'permission_name' => T("Approval::permission.admin_setting.title"),
                'route' => '/approval',
                'parent_id' => '0',
                'permission_mark' => 'approval',
                'component' => '/admin/layout/index.vue',
                'type' => MenuType::Top->value(),
                'children' => [
                    // 审批组权限
                    [
                        'permission_name' => T("Approval::permission.admin_setting.groups"),
                        'route' => '/approval/groups',
                        'parent_id' => 'approval',
                        'permission_mark' => 'approval_groups',
                        'component' => '/admin/layout/index.vue',
                        'type' => MenuType::Top->value(),
                        'children' => [
                            [
                                'permission_name' => T("Approval::permission.admin_setting.groups"),
                                'route' => '/approval/groups',
                                'parent_id' => 'approval_groups',
                                'permission_mark' => 'approval_groups_list',
                                'component' => '/admin/views/approval/groups/index.vue',
                                'type' => MenuType::Menu->value(),
                                'actions' => [
                                    [
                                        'permission_name' => T("Approval::List"),
                                        'route' => '/approval/groups',
                                        'permission_mark' => 'approval_groups@index',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_groups_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Detail"),
                                        'route' => '/approval/groups/{id}',
                                        'permission_mark' => 'approval_groups@show',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_groups_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Create"),
                                        'route' => '/approval/groups',
                                        'permission_mark' => 'approval_groups@store',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_groups_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Update"),
                                        'route' => '/approval/groups/{id}',
                                        'permission_mark' => 'approval_groups@update',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_groups_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Delete"),
                                        'route' => '/approval/groups/{id}',
                                        'permission_mark' => 'approval_groups@destroy',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_groups_list'
                                    ],
                                ]
                            ]
                        ]
                    ],
                    // 审批成员权限
                    [
                        'permission_name' => T("Approval::permission.admin_setting.members"),
                        'route' => '/approval/members',
                        'parent_id' => 'approval',
                        'permission_mark' => 'approval_members',
                        'component' => '/admin/layout/index.vue',
                        'type' => MenuType::Top->value(),
                        'children' => [
                            [
                                'permission_name' => T("Approval::permission.admin_setting.members"),
                                'route' => '/approval/members',
                                'parent_id' => 'approval_members',
                                'permission_mark' => 'approval_members_list',
                                'component' => '/admin/views/approval/members/index.vue',
                                'type' => MenuType::Menu->value(),
                                'actions' => [
                                    [
                                        'permission_name' => T("Approval::List"),
                                        'route' => '/approval/members',
                                        'permission_mark' => 'approval_members@index',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_members_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Permissions"),
                                        'route' => '/approval/permissions',
                                        'permission_mark' => 'approval_members@permissions',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_members_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Members List"),
                                        'route' => '/approval/groups/{groupId}/members',
                                        'permission_mark' => 'approval_members@members_index',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_members_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Add Member"),
                                        'route' => '/approval/groups/{groupId}/members',
                                        'permission_mark' => 'approval_members@members_store',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_members_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Import Members"),
                                        'route' => '/approval/groups/{groupId}/members/import',
                                        'permission_mark' => 'approval_members@members_import',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_members_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Update Status"),
                                        'route' => '/approval/members/{id}/status',
                                        'permission_mark' => 'approval_members@members_update_status',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_members_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Delete Member"),
                                        'route' => '/approval/members/{id}',
                                        'permission_mark' => 'approval_members@members_destroy',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_members_list'
                                    ],
                                ]
                            ]
                        ]
                    ],
                    // 审批流程权限
                    [
                        'permission_name' => T("Approval::permission.admin_setting.flows"),
                        'route' => '/approval/flows',
                        'parent_id' => 'approval',
                        'permission_mark' => 'approval_flows',
                        'component' => '/admin/layout/index.vue',
                        'type' => MenuType::Top->value(),
                        'children' => [
                            [
                                'permission_name' => T("Approval::permission.admin_setting.flows"),
                                'route' => '/approval/flows',
                                'parent_id' => 'approval_flows',
                                'permission_mark' => 'approval_flows_list',
                                'component' => '/admin/views/approval/flows/index.vue',
                                'type' => MenuType::Menu->value(),
                                'actions' => [
                                    [
                                        'permission_name' => T("Approval::List"),
                                        'route' => '/approval/flows',
                                        'permission_mark' => 'approval_flows@index',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_flows_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Detail"),
                                        'route' => '/approval/flows/{id}',
                                        'permission_mark' => 'approval_flows@show',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_flows_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Create"),
                                        'route' => '/approval/flows',
                                        'permission_mark' => 'approval_flows@store',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_flows_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Update"),
                                        'route' => '/approval/flows/{id}',
                                        'permission_mark' => 'approval_flows@update',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_flows_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Delete"),
                                        'route' => '/approval/flows/{id}',
                                        'permission_mark' => 'approval_flows@destroy',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_flows_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Setting Index"),
                                        'route' => '/approval/flows/{id}/setting',
                                        'permission_mark' => 'approval_flows@setting_index',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_flows_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Setting Structure"),
                                        'route' => '/approval/flows/setting/structure',
                                        'permission_mark' => 'approval_flows@setting_structure',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_flows_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Product Store"),
                                        'route' => '/approval/products',
                                        'permission_mark' => 'approval_flows@product_store',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_flows_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Product Delete"),
                                        'route' => '/approval/products/{id}',
                                        'permission_mark' => 'approval_flows@product_destroy',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_flows_list'
                                    ]
                                ]
                            ]
                        ]
                    ],
                    // 审批记录权限
                    [
                        'permission_name' => T("Approval::permission.admin_setting.records"),
                        'route' => '/approval/records',
                        'parent_id' => 'approval',
                        'permission_mark' => 'approval_records',
                        'component' => '/admin/layout/index.vue',
                        'type' => MenuType::Top->value(),
                        'children' => [
                            [
                                'permission_name' => T("Approval::permission.admin_setting.records"),
                                'route' => '/approval/records',
                                'parent_id' => 'approval_records',
                                'permission_mark' => 'approval_records_list',
                                'component' => '/admin/views/approval/records/index.vue',
                                'type' => MenuType::Menu->value(),
                                'actions' => [
                                    [
                                        'permission_name' => T("Approval::List"),
                                        'route' => '/approval/records',
                                        'permission_mark' => 'approval_records@index',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_records_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Progress"),
                                        'route' => '/approval/records/{id}/progress',
                                        'permission_mark' => 'approval_records@progress',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_records_list'
                                    ],
                                    [
                                        'permission_name' => T("Approval::Add_Progress"),
                                        'route' => '/approval/records/{id}/progress',
                                        'permission_mark' => 'approval_records@add_progress',
                                        'component' => '',
                                        'type' => MenuType::Action->value(),
                                        'parent_id' => 'approval_records_list'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        
        $frontend = [];
        
        return [
            'admin' => $admin,
            'frontend' => $frontend
        ];
    }

    /**
     * 注册计划任务
     *
     * @return array<string, array>
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function registerSchedule(): array
    {
        return [
            // 检查审批流程是否超时
            'approval.check_flow_timeout' => [
                'name' => T('Approval::schedule.check_flow_timeout'),
                'command' => 'approval:check-flow-timeout',
                'spec' => '0 11 * * *', // 每天早上11点执行
                'description' => T('Approval::schedule.check_flow_timeout_desc'),
                'type' => 'artisan',
                'status' => 'waiting',
                'platform' => PHP_OS_FAMILY === 'Windows' ? 'windows' : 'linux',
                'creator_id' => 1,
                'lang' => config('app.locale', 'zh_CN')
            ],
        ];
    }

    public function registerMailTemplates(): array
    {
        return [
            // 审批流程超时通知类模板
            'approval.flow_timeout' => [
                'name' => T('Approval::template.notification.flow_timeout'),
                'type' => MessageType::NOTIFICATION->value,
                'view' => 'Approval::emails.flow_timeout',
                'subject' => T('Approval::template.flow_timeout_subject'),
                'description' => T('Approval::template.flow_timeout_description'),
                'content' => $this->getTemplateContent('emails.flow_timeout'),
            ],
        ];
    }

    /**
     * 注册控制台命令
     * @return void
     */
    protected function registerConsole(): void
    {
        // 主邮件发送命令
        $this->registerConsoleCommand('approval:check-flow-timeout', CheckFlowTimeoutCommand::class);
    }
}
