<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | SMTP 配置
    |--------------------------------------------------------------------------
    |
    | 这里定义了系统使用的SMTP配置，可以配置多个SMTP服务
    |
    */

    'services' => [
        // Brevo SMTP 服务配置
        'brevo' => [
            'host' => env('APPROVAL_BREVO_SMTP_HOST', 'smtp-relay.brevo.com'),
            'port' => env('APPROVAL_BREVO_SMTP_PORT', '587'),
            'username' => env('APPROVAL_BREVO_SMTP_USER', '<EMAIL>'),
            'password' => env('APPROVAL_BREVO_SMTP_PASSWORD', 'xsmtpsib-84e9db45a4e9f81bdbadf15e03c3b961108a2b15526cc40f538901f73229d5f9-yZSMUAC0N6EDFk1r'),
            'encryption' => env('APPROVAL_BREVO_SMTP_ENCRYPTION', 'tls'),
            'from_name' => env('APPROVAL_BREVO_SMTP_FROM_NAME', 'Hong Kong Bingo Web Design Company'),
            'from_address' => env('APPROVAL_BREVO_SMTP_FROM', '<EMAIL>')
        ],
    ],
    
    // 默认使用的SMTP服务
    'default' => env('APPROVAL_DEFAULT_SMTP_SERVICE', 'brevo'),
]; 