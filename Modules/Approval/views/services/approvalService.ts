import http from '/admin/support/http'
import type { 
  IApiResponse, 
  IPaginationResponse,
  IApprovalFlow,
  IApprovalRole,
  IApprovalReviewGroup,
  IHistoryRecord,
  IPageParams,
  ISearchForm
} from '../types'

// API 基础路径
const BASE_URL = '/api/approval'

// 流程管理相关接口
export const approvalService = {
  // 获取流程列表
  getList: (params: ISearchForm) => {
    return http.get<IApiResponse>('/approval/flows', params)
  },

  // 获取流程详情
  getDetail: (id: number) => {
    return http.get<IApiResponse<IApprovalFlow>>(`/approval/flows/${id}`)
  },

  // 创建流程
  create: (data: Omit<IApprovalFlow, 'id' | 'createdAt' | 'updatedAt'>) => {
    return http.post<IApiResponse<IApprovalFlow>>(`/approval/flows`, data)
  },

  // 更新流程
  update: (id: number, data: Partial<IApprovalFlow>) => {
    return http.put<IApiResponse<IApprovalFlow>>(`/approval/flows/${id}`, data)
  },

  // 删除流程
  delete: (id: number) => {
    return http.delete<IApiResponse>(`/approval/flows/${id}`)
  },

  // 切换流程状态
  toggleStatus: (id: number, enabled: boolean) => {
    return http.patch<IApiResponse>(`${BASE_URL}/flows/${id}/status`, { enabled })
  },

  // 复制流程
  duplicate: (id: number) => {
    return http.post<IApiResponse>(`/approval/flows/${id}/copy`)
  },

  // 导出流程
  export: (id: number) => {
    return http.get(`${BASE_URL}/flows/${id}/export`, { 
      responseType: 'blob'
    })
  },

  // 获取审批历史
  getHistory: (flowId: number, params: any) => {
    return http.get<IApiResponse<IPaginationResponse<IHistoryRecord>>>(
      `${BASE_URL}/flows/${flowId}/history`,
      { params }
    )
  },

  // 添加 switchStatus 方法
  switchStatus: (id: number) => {
    return http.post<IApiResponse>(`/approval/flows/${id}/switch`)
  },
  // 审批记录列表
  getRecordsList: (params: ISearchForm) => {
    return http.post<IApiResponse>('/approval/dashboard', params)
  },

  // 获取流程进度
  getProgress: (id: number) => {
    return http.get<IApiResponse>(`/approval/records/${id}/progress`)
  },

  // 审批通过
  approve: (data: { id: number, action: number, comment: string, attachments?: string[] }) => {
    return http.post(`/approval/records/${data.id}/progress`, {
      action: data.action,
      comment: data.comment,
      attachments: data.attachments || []
    })
  },
  // 删除审批记录
  deleteRecord: (id: number) => {
    return http.delete<IApiResponse>(`/approval/records/${id}`)
  }
}

// 角色组管理相关接口
export const roleService = {
  // 获取角色组列表
  getList: (params: any) => {
    return http.get<IApiResponse<IPaginationResponse<IApprovalRole>>>(
      `/approval/groups/1/members`,
      { params }
    )
  },

  // 获取角色组详情
  getDetail: (id: number) => {
    return http.get<IApiResponse<IApprovalRole>>(`${BASE_URL}/roles/${id}`)
  },

  // 创建角色组
  create: (data: Omit<IApprovalRole, 'id'>) => {
    return http.post<IApiResponse<IApprovalRole>>(`/approval/groups/1/members`, data)
  },

  // 更新角色组
  update: (id: number, data: Partial<IApprovalRole>) => {
    return http.put<IApiResponse<IApprovalRole>>(`/approval/members/13/status/${id}`, data)
  },

  // 删除角色组
  delete: (id: number) => {
    return http.delete<IApiResponse>(`/approval/members/${id}`)
  }
}

// 审核组管理相关接口
export const reviewGroupService = {
  // 获取审核组列表
  getList: (params: IPageParams) => {
    return http.get<IApiResponse>(
      `/approval/groups`, params
    )
  },

  // 获取审核组详情
  getDetail: (id: number) => {
    return http.get<IApiResponse<IApprovalReviewGroup>>(`/approval/groups/${id}`)
  },

  // 创建审核组
  create: (data: Omit<IApprovalReviewGroup, 'id'>) => {
    return http.post<IApiResponse<IApprovalReviewGroup>>(`/approval/groups`, data)
  },

  // 更新审核组
  update: (id: number, data: Partial<IApprovalReviewGroup>) => {
    return http.put<IApiResponse<IApprovalReviewGroup>>(`/approval/groups/${id}`, data)
  },

  // 删除审核组
  delete: (id: number) => {
    return http.delete<IApiResponse>(`/approval/groups/${id}`)
  },

  // 添加成员
  addMember: (groupId: number, data: Omit<IApprovalReviewMember, 'id' | 'groupId'>) => {
    return http.post<IApiResponse>(`${BASE_URL}/review-groups/${groupId}/members`, data)
  },

  // 移除成员
  removeMember: (groupId: number, memberId: number) => {
    return http.delete<IApiResponse>(`${BASE_URL}/review-groups/${groupId}/members/${memberId}`)
  },

  // 更新成员状态
  updateMemberStatus: (groupId: number, memberId: number, isEnabled: boolean) => {
    return http.patch<IApiResponse>(
      `/approval/members/${memberId}/status`,
      { isEnabled }
    )
  },

  // 复制审核组
  copy: (id: number) => {
    return http.post<IApiResponse>(`/approval/groups/${id}/copy`)
  },
} 