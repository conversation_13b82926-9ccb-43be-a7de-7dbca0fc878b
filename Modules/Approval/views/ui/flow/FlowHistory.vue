<template>
  <div class="bwms-module">
 
    <div class="module-header">
    </div>
    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-form :inline="true" :model="searchForm">
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" placeholder="请选择" clearable>
                <el-option label="已通过" value="approved" />
                <el-option label="已拒绝" value="rejected" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 历史记录表格 -->
        <el-table 
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column prop="step" label="审批步骤" min-width="120" />
          <el-table-column prop="approver" label="审批人" min-width="120" />
          <el-table-column prop="status" label="审批结果" min-width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="审批意见" min-width="200" show-overflow-tooltip />
          <el-table-column prop="time" label="审批时间" min-width="160" />
          <el-table-column label="附件" min-width="120">
            <template #default="{ row }">
              <template v-if="row.attachments?.length">
                <el-link 
                  v-for="file in row.attachments"
                  :key="file.id"
                  type="primary"
                  :href="file.url"
                  target="_blank"
                  class="attachment-link"
                >
                  {{ file.name }}
                </el-link>
              </template>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { IHistoryRecord, ISearchForm } from '../../types/index'
import { approvalService } from '../../services/approvalService'

const route = useRoute()
const loading = ref(false)

// 搜索表单
const searchForm = reactive<ISearchForm>({
  dateRange: undefined,
  status: undefined
})

// 表格数据
const tableData = ref<IHistoryRecord[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    approved: 'success',
    rejected: 'danger',
    cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    approved: '已通过',
    rejected: '已拒绝',
    cancelled: '已取消'
  }
  return textMap[status] || status
}

// 查询
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.dateRange = undefined
  searchForm.status = undefined
  handleSearch()
}

// 改变每页条数
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchData()
}

// 改变页码
const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      startDate: searchForm.dateRange?.[0]?.toISOString(),
      endDate: searchForm.dateRange?.[1]?.toISOString(),
      status: searchForm.status
    }
    
    const { data } = await approvalService.getHistory(Number(route.params.id), params)
    tableData.value = data.list
    pagination.total = data.total
  } catch (error) {
    ElMessage.error('获取历史记录失败')
  } finally {
    loading.value = false
  }
}

// 初始化
fetchData()
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;
      overflow: scroll;
      .search-area {
        margin-bottom: 20px;
      }

      .pagination {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
      }

      .attachment-link {
        display: block;
        margin: 4px 0;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style> 