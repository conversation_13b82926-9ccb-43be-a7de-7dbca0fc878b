<template>
  <div class="table-page bwms-module">
    <!-- 头部区域 - 添加标题显示 -->
    <div class="module-header">
      <div class="content-title">
        {{ contentTitle }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="module-con scroll-bar-custom-transparent">
      <el-row :gutter="20">
        <!-- 左侧内容区域 -->
        <el-col :span="17">
          <div class="approval-content-box">
            <!-- 流程操作区域 -->
            <div class="flow-operation">
              <div class="operation-header">
                <div class="title">{{ $t('Approval.FlowApproval.flowOperation') }}</div>
              </div>
              <div class="operation-content">
                <el-form ref="formRef" :model="formData" label-position="top">
                  <!-- 快速留言选择 -->
                  <el-form-item :label="$t('Approval.FlowApproval.quickMessage')">
                    <el-select v-model="formData.quickMessage" :placeholder="$t('Approval.FlowApproval.quickMessage')"
                      @change="handleQuickMessageChange">
                      <el-option :label="$t('Approval.FlowApproval.quickMessageOptions.pass')" value="pass" />
                      <el-option :label="$t('Approval.FlowApproval.quickMessageOptions.incomplete')"
                        value="incomplete" />
                      <el-option :label="$t('Approval.FlowApproval.quickMessageOptions.reject')" value="reject" />
                    </el-select>
                  </el-form-item>

                  <!-- 审批意见 -->
                  <el-form-item :label="$t('Approval.FlowApproval.approvalOpinion')">
                    <el-input v-model="formData.comment" type="textarea" :rows="4"
                      :placeholder="$t('Approval.FlowApproval.approvalOpinionPlaceholder')" />
                  </el-form-item>

                  <!-- 添加附件 -->
                  <el-form-item :label="$t('Approval.FlowApproval.attachment')">
                    <div class="attachments-container">
                      <el-upload class="file-uploader" action="#" :show-file-list="true" :auto-upload="false"
                        :on-change="handleFileChange" :before-upload="beforeFileUpload" :on-remove="handleFileRemove"
                        accept=".xlsx,.xls,image/*" multiple :file-list="fileList">
                        <el-button type="primary">
                          <el-icon>
                            <Upload />
                          </el-icon>
                          <span>{{ $t('Approval.FlowApproval.addAttachment') }}</span>
                        </el-button>
                        <template #tip>
                          <div class="el-upload__tip">
                            {{ $t('Approval.FlowApproval.attachmentTip') }}
                          </div>
                        </template>
                      </el-upload>
                    </div>
                  </el-form-item>
                </el-form>

              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-center">
            <el-button style="min-width: 99px;" @click="handleCancel">{{ $t('Approval.FlowApproval.cancel')
              }}</el-button>
            <el-button type="danger" style="min-width: 99px; background-color: #FF6D6D; color: #fff;"
              @click="handleReject">{{
                $t('Approval.FlowApproval.reject') }}</el-button>
            <el-button type="primary" style="min-width: 99px; background-color: #007EE5; color: #fff;"
              @click="handleApprove">{{
                $t('Approval.FlowApproval.approve') }}</el-button>
          </div>
        </el-col>

        <!-- 右侧流程状态 -->
        <el-col :span="7">
          <div class="approval-content-box">
            <div class="flow-status">
              <div class="status-header">
                <div class="title">{{ $t('Approval.FlowApproval.flowStatus') }}</div>
              </div>
              <div class="status-content" v-loading="loading">
                <el-timeline v-if="flowSteps.length > 0">

                  <el-timeline-item v-for="step in flowSteps" :key="step.id" :type="getStepType(step.status)"
                    :color="getStepColor(step.status)" :size="isCurrStep(step.id) ? 'large' : 'normal'">
                    <div class="step-info">
                      <div class="step-header">
                        <span class="step-name">{{ step.name }}</span>
                        <el-tag :type="getStatusTagType(step.status)" size="small">
                          {{ getStatusText(step.status) }}
                        </el-tag>
                      </div>
                      <div class="step-user" v-if="step.approver_name">
                        <span>{{ step.approver_name }}</span>
                        <span class="department">({{ step.group_name }})</span>
                      </div>
                      <div class="step-time" v-if="step.created_at">
                        {{ step.created_at }}
                      </div>
                      <div class="step-comment" v-if="step.name">
                        {{ step.name }}
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
                <el-empty v-else :description="$t('Cms.list.no_data')" image-size="100px" />
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { approvalService } from '../../services/approvalService'
import { useI18n } from 'vue-i18n'
import http from '/admin/support/http'

// 定义流程数据接口
interface FlowStep {
  id: number
  name: string
  sort: number
  status: string
  approver_name?: string
  group_name?: string
  created_at?: string
}

interface FlowData {
  id: number
  product_id: number
  product_key: string
  flow_id: number
  current_step: number
  status: number
  status_text: string
  steps: FlowStep[]
}

// 定义附件类型接口
interface Attachment {
  name: string
  url?: string
  path?: string
}

// 定义上传文件类型
interface UploadFile {
  name: string
  url?: string
  path?: string
}

// 定义审批参数接口
interface ApprovalParams {
  id: number
  action: number
  comment: string
  attachments?: string[]
}

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()
const loading = ref(false)
const { t } = useI18n()

// 添加附件相关的响应式数据
const fileList = ref<UploadFile[]>([])

// 表单数据
const formData = reactive({
  quickMessage: '',
  comment: '',
  attachments: [] as string[]
})

// 流程数据
const flowData = ref<FlowData | null>(null)
const flowSteps = ref<FlowStep[]>([])

// 标题数据
const contentTitle = ref('')

// 判断来源是否为CMS列表
const isFromCmsList = ref(false)

// Mock数据
const mockFlowSteps: FlowStep[] = [
  {
    id: 1,
    name: '內容審核',
    sort: 1,
    status: 'completed',
    approver_name: 'Eric Wong',
    group_name: '內容編輯組',
    created_at: '2023-06-15 14:30:25'
  },
  {
    id: 2,
    name: '部門經理審核',
    sort: 2,
    status: 'processing',
    approver_name: 'Thomas Chan',
    group_name: '內容管理組',
    created_at: '2023-06-16 09:15:32'
  },
  {
    id: 3,
    name: '發佈審核',
    sort: 3,
    status: 'pending',
    approver_name: '',
    group_name: '發佈管理組'
  }
]

const mockFlowData: FlowData = {
  id: 1,
  product_id: 123,
  product_key: 'CMS_CONTENT',
  flow_id: 1,
  current_step: 2,
  status: 1,
  status_text: '審批中',
  steps: mockFlowSteps
}

// 处理快速留言选择变化
const handleQuickMessageChange = (value: string) => {
  if (value) {
    // 根据选择的快速留言设置审批意见内容
    switch (value) {
      case 'pass':
        formData.comment = t('Approval.FlowApproval.quickMessageOptions.pass')
        break
      case 'incomplete':
        formData.comment = t('Approval.FlowApproval.quickMessageOptions.incomplete')
        break
      case 'reject':
        formData.comment = t('Approval.FlowApproval.quickMessageOptions.reject')
        break
      default:
        formData.comment = value
    }
  }
}

// 获取流程进度
const fetchProgress = async () => {
  // 如果是从CMS列表跳转过来，直接使用mock数据
  if (isFromCmsList.value) {
    flowData.value = mockFlowData
    flowSteps.value = mockFlowSteps
    loading.value = false
    return
  }
  
  // 原有逻辑不变
  loading.value = true
  try {
    const recordId = Number(route.params.id)
    const { data } = await approvalService.getProgress(recordId)

    if (data.data) {
      flowData.value = data.data
      flowSteps.value = data.data.steps || []
    }
  } catch (error) {
    ElMessage.error(t('Approval.FlowApproval.getProgressFailed'))
  } finally {
    loading.value = false
  }
}

// 判断是否当前步骤
const isCurrStep = (stepId: number) => {
  return flowData.value?.current_step === stepId
}

// 获取步骤类型
const getStepType = (status: string) => {
  const typeMap: Record<string, string> = {
    'completed': 'success',
    'pending': 'primary',
    'rejected': 'danger',
    'processing': 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取步骤颜色
const getStepColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'completed': '#67C23A',
    'pending': '#409EFF',
    'rejected': '#F56C6C',
    'processing': '#E6A23C'
  }
  return colorMap[status] || '#909399'
}

// 获取步骤大小
const getStepSize = (status: string) => {
  return status === 'processing' ? 'large' : 'normal'
}

// 获取状态标签类型
const getStatusTagType = (status: string | number) => {
  if (typeof status === 'string') {
    const typeMap: Record<string, string> = {
      'completed': 'success',
      'pending': '',
      'rejected': 'danger',
      'processing': 'warning'
    }
    return typeMap[status] || 'info'
  } else {
    const numTypeMap: Record<number, string> = {
      0: 'info',    // 草稿
      1: 'warning', // 审批中
      2: 'success', // 已发布
      3: 'danger',  // 已拒绝
      4: 'info'     // 已完成
    }
    return numTypeMap[status] || 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  return t(`Approval.FlowApproval.status.${status}`)
}

// 处理文件上传前的验证
const beforeFileUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.ms-excel' ||
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  const isImage = file.type.startsWith('image/')
  const isValidType = isExcel || isImage
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error(t('Approval.FlowApproval.attachmentTypeError'))
    return false
  }
  if (!isLt10M) {
    ElMessage.error(t('Approval.FlowApproval.attachmentSizeError'))
    return false
  }
  return true
}

// 处理文件变化
const handleFileChange = async (uploadFile: any) => {
  if (!beforeFileUpload(uploadFile.raw)) {
    return
  }

  const file = uploadFile.raw
  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('dir', 'approval/attachments')
    formData.append('mode', 'OVERWRITE')

    const { data } = await http.post('/media/upload', formData)
    if (data.code === 200 && data.data.file) {
      const newFile: UploadFile = {
        name: uploadFile.name,
        path: data.data.file.url
      }
      fileList.value = [...fileList.value, newFile]
      updateFormAttachments()
      ElMessage.success(t('Approval.FlowApproval.uploadSuccess'))
    } else {
      ElMessage.error(t('Approval.FlowApproval.uploadFailed'))
    }
  } catch (error) {
    ElMessage.error(t('Approval.FlowApproval.uploadFailed'))
  }
}

// 处理文件移除
const handleFileRemove = (uploadFile: UploadFile) => {
  fileList.value = fileList.value.filter(file => file.name !== uploadFile.name)
  updateFormAttachments()
}

// 更新表单附件列表
const updateFormAttachments = () => {
  formData.attachments = fileList.value.map(file => file.path || '')
    .filter(path => path) // 过滤掉空路径
}

// 修改审批方法
const handleApprove = async () => {
  if (!formData.comment) {
    ElMessage.warning(t('Approval.FlowApproval.pleaseEnterOpinion'))
    return
  }

  try {
    const recordId = route.params.id
    const params: ApprovalParams = {
      id: Array.isArray(recordId) ? parseInt(recordId[0]) : parseInt(recordId),
      action: 1,
      comment: formData.comment,
      attachments: formData.attachments
    }
    await approvalService.approve(params)
    ElMessage.success(t('Approval.FlowApproval.approveSuccess'))
    router.back()
  } catch (error) {
  }
}

// 修改拒绝方法
const handleReject = () => {
  if (!formData.comment) {
    ElMessage.warning(t('Approval.FlowApproval.pleaseEnterOpinion'))
    return
  }
  ElMessageBox.confirm(
    t('Approval.FlowApproval.confirmRejectContent'),
    t('Approval.FlowApproval.confirmReject'),
    {
      confirmButtonText: t('Approval.FlowList.confirm'),
      cancelButtonText: t('Approval.FlowApproval.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    try {
      const recordId = route.params.id
      const params: ApprovalParams = {
        id: Array.isArray(recordId) ? parseInt(recordId[0]) : parseInt(recordId),
        action: 2,
        comment: formData.comment,
        attachments: formData.attachments
      }
      await approvalService.approve(params)
      ElMessage.success(t('Approval.FlowApproval.rejectSuccess'))
      router.back()
    } catch (error) {
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 取消
const handleCancel = () => {
  router.back()
}

// 生命周期钩子
onMounted(() => {
  // 从localStorage中获取内容标题
  const title = localStorage.getItem('approval_content_title')
  if (title) {
    contentTitle.value = title
    // 设置来源标志，表明是从CMS列表跳转过来
    isFromCmsList.value = true
  }

  // 获取流程进度
  fetchProgress()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  height: 100%;
  display: flex;
  flex-direction: column;

  .module-header {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: start;
    height: 60px;
    padding: 0 20px;
    margin-top: 50px;

    .content-title {
      font: normal normal normal 30px/40px Microsoft JhengHei;
      letter-spacing: 0px;
      color: #202020;
      opacity: 1;
    }
  }

  .module-con {
    flex: 1;
    height: calc(100vh - 120px);
    overflow: hidden;

    .el-row {
      height: 100%;

      .el-col {
        height: 100%;
      }
    }

    .approval-content-box {
      padding: 20px;
      background: #fff;
      border-radius: 10px;
      margin-bottom: 20px;


      .flow-operation,
      .flow-status {

        .operation-header,
        .status-header {
          margin-bottom: 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .title {
            font-size: 16px;
            font-weight: 500;
            color: #000;
          }
        }
      }

      .flow-operation {
        .operation-content {
          .operation-buttons {
            margin-top: 24px;
            display: flex;
            justify-content: center;
            gap: 24px;
          }
        }
      }

      .flow-status {
        height: 100%;

        .status-header {
          margin-bottom: 16px;
        }

        .status-content {
          height: calc(100% - 56px);
          overflow-y: auto;
          padding-right: 10px;

          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-thumb {
            background-color: #E4E7ED;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-track {
            background-color: #F5F7FA;
          }

          .el-timeline {
            padding: 4px 0 20px;
          }

          :deep(.el-timeline-item__node--normal),
          :deep(.el-timeline-item__node--large) {
            left: 0;
          }

          .step-info {
            .step-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              .step-name {
                font-size: 14px;
                color: #303030;
              }
            }

            .step-user {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;
              color: #18191A;
              font-size: 16px;
              font-weight: 500;
            }

            .step-time {
              font-size: 14px;
              color: #9A9A9A;
              margin-bottom: 8px;
            }

            .step-comment {
              padding: 8px 12px;
              background: #F3F3F3;
              border-radius: 5px;
              color: #18191A;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .attachments-container {
    .file-uploader {
      :deep(.el-upload-list) {
        margin-top: 10px;
      }

      :deep(.el-upload-list__item) {
        transition: all 0.3s;

        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }

  .el-upload__tip {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
  }
}

:deep(.el-timeline-item__node) {
  &.is-success {
    background-color: #67C23A;
  }

  &.is-warning {
    background-color: #E6A23C;
  }

  &.is-danger {
    background-color: #F56C6C;
  }
}
</style>