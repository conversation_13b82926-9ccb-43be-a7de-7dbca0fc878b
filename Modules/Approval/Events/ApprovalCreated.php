<?php

declare(strict_types=1);

namespace Modules\Approval\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 会员审批创建事件
 */
class ApprovalCreated
{
    use Dispatchable, SerializesModels;



    /**
     * 创建一个新的事件实例
     *
     * @param string $productTable 产品表名
     * @param int $productId 产品ID
     */
    public function __construct(public string $productKey,public int $productId)
    {
    }
}