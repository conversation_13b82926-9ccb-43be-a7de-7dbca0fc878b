<?php

declare(strict_types=1);

namespace Modules\Approval\Services;

use Bingo\Exceptions\BizException;
use Modules\Approval\Domain\Business\ApprovalGroupBusiness;
use Illuminate\Pagination\LengthAwarePaginator;

final readonly class ApprovalGroupService
{
    public function __construct(
        private ApprovalGroupBusiness $business,
    ) {}

    /**
     * 获取审批组列表
     * @param array $params
     * @return LengthAwarePaginator
     */
    public function getGroupList(array $params): LengthAwarePaginator
    {
        return $this->business->getGroupList($params);
    }

    /**
     * 获取审批组详情
     * @param int $id
     * @return ?array
     * @throws BizException
     */
    public function getGroupDetail(int $id): ?array
    {
        return $this->business->getGroupDetail($id);
    }

    /**
     * 创建审批组
     * @param array $data
     * @return array
     * @throws BizException
     */
    public function createGroup(array $data): array
    {
        return $this->business->createGroup($data);
    }

    /**
     * 更新审批组
     * @param int $id
     * @param array $data
     * @return array
     * @throws BizException
     */
    public function updateGroup(int $id, array $data): array
    {
        return $this->business->updateGroup($id, $data);
    }

    /**
     * 删除审批组
     * @param int $id
     * @throws BizException
     */
    public function deleteGroup(int $id): void
    {
        $this->business->deleteGroup($id);
    }

    /**
     * 获取所有可用的权限列表
     * @return array
     */
    public function getGroupPermissions(): array
    {
        return $this->business->getGroupPermissions();
    }

    /**
     * 复制审批组
     * @param int $id 审批组ID
     * @return array
     * @throws BizException
     */
    public function copyGroup(int $id): array
    {
        return $this->business->copyGroup($id);
    }
}
