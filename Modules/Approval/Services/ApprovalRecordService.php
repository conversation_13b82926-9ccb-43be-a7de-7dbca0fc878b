<?php

declare(strict_types=1);

namespace Modules\Approval\Services;

use Modules\Approval\Domain\Business\ApprovalRecordBusiness;
use Modules\Approval\Domain\Business\ApprovalProductFlowBusiness;

/**
 * 审批记录服务类
 */
final class ApprovalRecordService
{
    /**
     * 构造函数
     */
    public function __construct(
        private readonly ApprovalRecordBusiness $business,
    ) {
    }

    /**
     * 获取审批记录列表
     * @param array $params 查询参数
     * @return array 审批记录列表数据
     */
    public function getApprovalRecordList(array $params): array
    {
        return $this->business->getApprovalRecordList($params);
    }

    /**
     * 获取审批进度
     * @param int $id 审批记录ID
     * @return array
     */
    public function getProductFlowProgress(int $id): array
    {
        return $this->business->getApprovalProgress($id);
    }

    /**
     * 添加审批进度
     */
    public function addProgress(int $id, array $data): array    
    {
        return $this->business->createApprovalLog($id, $data);
    }

    /**
     * 获取审批仪表盘数据
     * 
     * @param array $params 查询参数
     * @return array 仪表盘数据
     */
    public function dashboard(array $params): array
    {
        return $this->business->dashboard($params);
    }

    /**
     * 删除审批记录
     */
    public function deleteApprovalRecord(int $id)
    {
        return $this->business->deleteApprovalRecord($id);
    }

    /**
     * 根据productKey和productId获取审批记录
     * @param string $productKey 产品key
     * @param int $productId 产品id
     * @return array 审批记录
     */
    public function getApprovalRecordByProduct(string $productKey, int $productId): array
    {
        return $this->business->getApprovalRecordByProduct($productKey, $productId);
    }
}
