<?php

declare(strict_types=1);

namespace Modules\Approval\Services;

use Bingo\Exceptions\BizException;
use Modules\Approval\Domain\Business\ApprovalProductFlowBusiness;

/**
 * 产品审批流程服务
 */
final readonly class ApprovalProductFlowService
{
    public function __construct(
       private ApprovalProductFlowBusiness $business
    ) {
    }

    /**
     * 创建产品审批关联
     * @param array $data
     * @return array
     * @throws BizException
     */
    public function createProductFlow(array $data): array
    {
       return $this->business->createProductFlow($data);
    }

    /**
     * 删除产品审批关联
     * @param int $id
     * @throws BizException
     */
    public function deleteProductFlow(int $id): void
    {
       $this->business->deleteProductFlow($id);
    }

}
