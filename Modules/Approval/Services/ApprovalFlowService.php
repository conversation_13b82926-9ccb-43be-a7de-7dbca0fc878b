<?php

declare(strict_types=1);

namespace Modules\Approval\Services;

use Bingo\Exceptions\BizException;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Modules\Approval\Domain\Business\ApprovalFlowBusiness;

/**
 * 审批流程服务
 */
final readonly class ApprovalFlowService
{

    public function __construct(
        private ApprovalFlowBusiness $business,
    ) {
    }

    /**
     * 获取流程列表
     *
     * @param array $params 查询参数
     * @return LengthAwarePaginator
     */
    public function getFlowList(array $params): LengthAwarePaginator
    {
        return $this->business->getFlowList($params);
    }

    /**
     * 获取流程详情
     *
     * @param int $id
     * @return array|null
     */
    public function getFlowDetail(int $id): ?array
    {
        return $this->business->getFlowDetail($id);
    }

    /**
     * 创建流程（包含步骤）
     *
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function createFlow(array $data): array
    {
        return $this->business->createFlow($data);
    }

    /**
     * 更新流程（包含步骤）
     *
     * @param int $id
     * @param array $data
     * @return array
     * @throws BizException
     */
    public function updateFlow(int $id, array $data): array
    {
        return $this->business->updateFlow($id, $data);
    }

    /**
     * 获取审批流程设置
     *
     * @param int $flowId
     * @return array
     */
    public function getSettings(int $flowId): array
    {
        return $this->business->getSettings($flowId);
    }

    /**
     * 获取审批流程设置结构
     *
     * @return array
     */
    public function getSettingsStructure(): array
    {
        return $this->business->getSettingsStructure();
    }

    /**
     * 复制审批流程
     *
     * @param int $id
     * @return array
     */
    public function copyFlow(int $id): array
    {
        return $this->business->copyFlow($id);
    }

    /**
     * 删除审批流程
     *
     * @param int $id
     * @return void
     */
    public function deleteFlow(int $id): void
    {
        $this->business->deleteFlow($id);
    }

    /**
     * 开关审批流程
     *
     * @param int $id
     * @return void
     */
    public function switchFlow(int $id): void
    {
        $this->business->switchFlow($id);
    }
}
