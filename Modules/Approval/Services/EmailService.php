<?php

declare(strict_types=1);

namespace Modules\Approval\Services;

use Illuminate\Support\Facades\Log;
use Modules\Edm\Domain\SMTP\SMTPMail;

/**
 * 审批模块邮件服务
 */
final class EmailService
{
    /**
     * 构造函数
     */
    public function __construct(
        private readonly SMTPMail $smtpMail
    ) {
    }
    
    /**
     * 发送邮件
     * 
     * @param string $to 收件人邮箱
     * @param string $subject 邮件主题
     * @param string $content 邮件内容
     * @return bool 发送结果
     */
    public function sendEmail(string $to, string $subject, string $content): bool
    {
        try {
            // 初始化依赖
            $smtpConfig = new \Modules\Edm\Domain\SMTP\SMTPConfig();
            $mailLogService = app(\Modules\Edm\Domain\Email\Logs::class);
            $mailsManager = app(\Bingo\Core\Mail\MailsManager::class);
            
            // 创建SMTPMail实例
            $smtpMail = new \Modules\Edm\Domain\SMTP\SMTPMail(
                $smtpConfig,
                $mailLogService, 
                $mailsManager
            );
            
            // 获取SMTP邮件配置
            $customConfig = [
                'smtpHost' => config('mail.smtp.services.brevo.host'),  // SMTP主机
                'smtpPort' => config('mail.smtp.services.brevo.port'),  // SMTP端口
                'smtpUser' => config('mail.smtp.services.brevo.username'), // 用户名
                'smtpPassword' => config('mail.smtp.services.brevo.password'), // 密码
                'smtpEncryption' => config('mail.smtp.services.brevo.encryption'), // 加密方式
                'smtpFromName' => config('mail.smtp.services.brevo.from_name'), // 发件人名称
                'smtpFrom' => config('mail.smtp.services.brevo.from_address')   // 发件人邮箱
            ];
            
            // 保存SMTP配置到数据库
            $smtpConfig->Save(['brevoSMTP' => json_encode($customConfig)], $smtpConfig->lang);
            
            // 使用自定义服务发送邮件
            return $smtpMail->send($to, $subject, $content, 'brevoSMTP');
        } catch (\Exception $e) {
            \Log::error('发送邮件失败', [
                'to' => $to,
                'subject' => $subject,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
} 