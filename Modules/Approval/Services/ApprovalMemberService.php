<?php

declare(strict_types=1);

namespace Modules\Approval\Services;

use Bingo\Exceptions\BizException;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Approval\Domain\Business\ApprovalMemberBusiness;


/**
 * 审批角色成员服务
 */
final readonly class ApprovalMemberService
{

    public function __construct(
        private ApprovalMemberBusiness $business,
    ) {
    }

    /**
     * 批量添加成员
     *
     * @param int $groupId
     * @param array $members
     * @return bool
     * @throws BizException
     */
    public function addMembers(int $groupId, array $members): bool
    {
        return $this->business->addMembers($groupId, $members);
    }

    /**
     * 批量导入角色成员
     *
     * @param int $groupId
     * @param UploadedFile $file
     * @return array
     * @throws Exception
     */
    public function importMembers(int $groupId, UploadedFile $file): array
    {
        return $this->business->importMembers($groupId, $file);
    }

    /**
     * 更新成员状态
     *
     * @param int $id
     * @param bool $isEnabled
     * @return bool
     * @throws BizException
     */
    public function updateMemberStatus(int $id, bool $isEnabled): bool
    {
        return $this->business->updateMemberStatus($id, $isEnabled);
    }

    /**
     * 删除角色成员
     *
     * @param int $id
     * @return bool
     * @throws BizException
     */
    public function deleteMember(int $id): bool
    {
        return $this->business->deleteMember($id);
    }

    /**
     * 获取成员列表
     *
     * @param int $groupId
     * @param array $params
     * @return LengthAwarePaginator
     */
    public function getMemberListByGroup(int $groupId, array $params = []): LengthAwarePaginator
    {
        return $this->business->getMemberListByGroup($groupId, $params);
    }

    /**
     * 获取成员列表
     *
     * @param array $params
     * @return LengthAwarePaginator
     */
    public function getMemberList(array $params = []): LengthAwarePaginator
    {
        return $this->business->getMemberList($params);
    }
}
