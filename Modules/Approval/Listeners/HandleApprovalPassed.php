<?php

declare(strict_types=1);

namespace Modules\Approval\Listeners;

use Modules\Approval\Events\ApprovalPassed;
use Modules\Members\Domain\Business\MemberBusiness;
use Modules\Members\Models\MembersRegisterLogs;
use Modules\Approval\Enums\ApprovalProduct;
use Modules\Cms\Services\CmsContentService;

class HandleApprovalPassed
{


    public function __construct(
        private MemberBusiness $memberBusiness
    )
    {

    }

    public function handle(ApprovalPassed $event): void
    {
        switch ($event->productKey) {
            case ApprovalProduct::MEMBER->value: //会员审核
                $member = MembersRegisterLogs::find($event->productId);
                $this->memberBusiness->approvalMember($member);
                break;
            case ApprovalProduct::PAGE->value: //网页审核
            case ApprovalProduct::NEWS->value: //新闻审核
                CmsContentService::updateStatusById($event->productId, 1);
                break;
        }
    }
}
