<?php

declare(strict_types=1);

namespace Modules\Approval\Listeners;

use Modules\Approval\Events\ApprovalRejected;
use Modules\Members\Domain\Business\MemberBusiness;
use Modules\Members\Models\MembersRegisterLogs;
use Modules\Approval\Enums\ApprovalProduct;
use Modules\Cms\Services\CmsContentService;

class HandleApprovalRejected
{

    public function __construct(
        private MemberBusiness $memberBusiness
    ) {}

    public function handle(ApprovalRejected $event): void
    {
        switch ($event->productKey) {
            case ApprovalProduct::MEMBER->value: //会员审核
                $member = MembersRegisterLogs::find($event->productId);
                $this->memberBusiness->rejectMember($member, $event->reason);
                break;
            case ApprovalProduct::PAGE->value: //网页审核
            case ApprovalProduct::NEWS->value: //新闻审核
                CmsContentService::updateStatusById($event->productId, 0);
                break;
        }
    }
}
