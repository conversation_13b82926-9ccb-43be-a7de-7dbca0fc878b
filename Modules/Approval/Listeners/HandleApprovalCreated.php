<?php

declare(strict_types=1);

namespace Modules\Approval\Listeners;

use Modules\Approval\Events\ApprovalCreated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Modules\Approval\Domain\Business\ApprovalRecordBusiness;
use Modules\Approval\Services\ApprovalRecordService;


/**
 * 处理会员审批创建事件的监听器
 */
class HandleApprovalCreated implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 创建事件监听器
     */
    public function __construct(
        private ApprovalRecordBusiness $approvalRecordBusiness
    )
    {

    }

    /**
     * 处理会员审批创建事件
     *
     * @param ApprovalCreated $event 审批创建事件
     * @return void
     */
    public function handle(ApprovalCreated $event): void
    {
        // 获取会员注册记录
        $productKey = $event->productKey;
        $productId = $event->productId;
        // 创建审批记录
        $this->approvalRecordBusiness->createRecord($productKey,$productId);
    }
}
