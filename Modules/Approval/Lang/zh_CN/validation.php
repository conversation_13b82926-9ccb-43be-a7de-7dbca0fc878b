<?php

return [
    'list_approval_group' => [
        'keyword' => [
            'string' => '关键词必须是字符串',
            'max' => '关键词不能超过50个字符'
        ],
        'sort_field' => [
            'string' => '排序字段必须是字符串'
        ],
        'sort_order' => [
            'string' => '排序方向必须是字符串',
            'in' => '排序方向必须是 asc 或 desc'
        ],
        'page' => [
            'integer' => '页码必须是整数',
            'min' => '页码必须大于等于1'
        ],
        'limit' => [
            'integer' => '每页数量必须是整数',
            'min' => '每页数量必须大于等于1',
            'max' => '每页数量不能超过100'
        ]
    ],
    'store_approval_group' => [
        'name' => [
            'required' => '名称不能为空',
            'max' => '名称不能超过50个字符'
        ],
        'code' => [
            'required' => '编码不能为空',
            'max' => '编码不能超过50个字符',
            'unique' => '编码已存在'
        ],
        'is_inherit' => [
            'required' => '继承标识不能为空',
            'boolean' => '继承标识必须是布尔值'
        ],
        'description' => [
            'max' => '描述不能超过200个字符'
        ],
        'status' => [
            'required' => '状态不能为空',
            'boolean' => '状态必须是布尔值'
        ],
        'permissions' => [
            'present' => '权限字段不能为空',
            'array' => '权限必须是数组格式'
        ],
        'permissions.*.id' => [
            'required' => '权限ID不能为空',
            'string' => '权限ID必须是字符串'
        ],
        'permissions.*.name' => [
            'required' => '权限名称不能为空',
            'string' => '权限名称必须是字符串'
        ],
        'permissions.*.description' => [
            'string' => '权限描述必须是字符串',
            'max' => '权限描述不能超过:max个字符'
        ],
        'permissions.*.actions' => [
            'required' => '权限操作不能为空',
            'array' => '权限操作必须是数组格式'
        ],
        'permissions.*.actions.*' => [
            'required' => '操作不能为空',
            'string' => '操作必须是字符串',
            'in' => '操作值无效'
        ],
        'members' => [
            'required' => '成员不能为空',
            'array' => '成员必须是数组'
        ],
        'members.*' => [
            'name' => [
                'required' => '成员名称不能为空',
                'max' => '成员名称不能超过50个字符'
            ],
            'position' => [
                'required' => '职位不能为空',
                'max' => '职位不能超过50个字符'
            ],
            'email' => [
                'required' => '邮箱不能为空',
                'email' => '邮箱格式不正确',
                'max' => '邮箱不能超过100个字符'
            ],
            'is_enabled' => [
                'required' => '启用状态不能为空',
                'boolean' => '启用状态必须是布尔值'
            ]
        ]
    ],
    'update_approval_group' => [
        'name' => [
            'required' => '名称不能为空',
            'max' => '名称不能超过50个字符'
        ],
        'code' => [
            'required' => '编码不能为空',
            'max' => '编码不能超过50个字符',
            'unique' => '编码已存在'
        ],
        'is_inherit' => [
            'required' => '继承标识不能为空',
            'boolean' => '继承标识必须是布尔值'
        ],
        'description' => [
            'max' => '描述不能超过200个字符'
        ],
        'status' => [
            'required' => '状态不能为空',
            'boolean' => '状态必须是布尔值'
        ],
        'permissions' => [
            'array' => '权限必须是数组'
        ],
        'permissions.*' => [
            'id' => [
                'required' => '权限ID不能为空',
                'string' => '权限ID必须是字符串'
            ],
            'name' => [
                'required' => '权限名称不能为空', 
                'string' => '权限名称必须是字符串'
            ],
            'description' => [
                'string' => '权限描述必须是字符串',
                'max' => '权限描述不能超过200个字符'
            ],
            'actions' => [
                'required' => '权限操作不能为空',
                'array' => '权限操作必须是数组'
            ],
            'actions.*' => [
                'required' => '操作不能为空',
                'string' => '操作必须是字符串',
                'in' => '操作值无效'
            ]
        ],
        'members' => [
            'required' => '成员不能为空',
            'array' => '成员必须是数组'
        ],
        'members.*' => [
            'name' => [
                'required' => '成员名称不能为空',
                'max' => '成员名称不能超过50个字符'
            ],
            'position' => [
                'required' => '职位不能为空',
                'max' => '职位不能超过50个字符'
            ],
            'email' => [
                'required' => '邮箱不能为空',
                'email' => '邮箱格式不正确',
                'max' => '邮箱不能超过100个字符'
            ],
            'is_enabled' => [
                'required' => '启用状态不能为空',
                'boolean' => '启用状态必须是布尔值'
            ]
        ]
    ],
    'batch_approval_group' => [
        'ids' => 'ID列表',
        'id' => 'ID',
        'ids.required' => 'ID列表不能为空',
        'ids.array' => 'ID列表必须是数组',
        'ids.*.required' => '数组中的每个ID都是必需的',
        'ids.*.exists' => '一个或多个所选ID无效'
    ],
    'store_approval_role' => [
        'name' => [
            'required' => '请输入角色名称',
            'string' => '角色名称必须是字符串',
            'max' => '角色名称不能超过50个字符',
        ],
        'code' => [
            'required' => '请输入角色编码',
            'string' => '角色编码必须是字符串',
            'max' => '角色编码不能超过50个字符',
            'unique' => '该角色编码已被使用',
        ],
        'level' => [
            'required' => '请输入角色级别',
            'integer' => '角色级别必须是整数',
            'min' => '角色级别不能小于1',
        ],
        'is_inherit' => [
            'required' => '请选择是否继承',
            'boolean' => '继承标识必须是布尔值'
        ],
        'permissions' => [
            'required' => '权限不能为空',
            'array' => '权限必须是数组'
        ],
        'description' => [
            'required' => '请输入描述',
            'string' => '描述必须是字符串',
            'max' => '描述不能超过255个字符',
        ],
        'status' => [
            'required' => '请选择状态',
            'boolean' => '状态必须是布尔值'
        ]
    ],
    'batch_approval_role' => [
        'ids' => [
            'required' => '请选择要操作的角色',
            'array' => '角色ID列表格式错误',
        ],
        'ids.*' => [
            'required' => '角色ID不能为空',
            'integer' => '角色ID必须是整数',
            'min' => '角色ID必须大于0',
        ],
    ],
    'list_approval_role' => [
        'keyword' => [
            'string' => '关键词必须是字符串',
            'max' => '关键词不能超过50个字符'
        ],
        'status' => [
            'boolean' => '状态必须是布尔值'
        ],
        'sort_field' => [
            'string' => '排序字段必须是字符串',
            'in' => '排序字段无效'
        ],
        'sort_order' => [
            'string' => '排序方向必须是字符串',
            'in' => '排序方向必须是 asc 或 desc'
        ],
        'per_page' => [
            'integer' => '每页数量必须是整数',
            'min' => '每页数量不能小于1',
            'max' => '每页数量不能大于100'
        ]
    ],
    'update_approval_role' => [
        'name' => [
            'required' => '请输入角色名称',
            'string' => '角色名称必须是字符串',
            'max' => '角色名称不能超过50个字符',
        ],
        'code' => [
            'required' => '请输入角色编码',
            'string' => '角色编码必须是字符串',
            'max' => '角色编码不能超过50个字符',
            'unique' => '角色编码已存在',
        ],
        'level' => [
            'integer' => '角色级别必须是整数',
            'min' => '角色级别不能小于1',
        ],
        'is_inherit' => [
            'boolean' => '继承标识必须是布尔值'
        ],
        'permissions' => [
            'array' => '权限必须是数组'
        ],
        'description' => [
            'string' => '描述必须是字符串',
            'max' => '描述不能超过255个字符',
        ],
        'status' => [
            'boolean' => '状态必须是布尔值'
        ]
    ],
    'list_approval_member' => [
        'page' => [
            'integer' => '页码必须是整数',
            'min' => '页码必须大于等于1'
        ],
        'limit' => [
            'integer' => '每页数量必须是整数',
            'min' => '每页数量必须大于等于1',
            'max' => '每页数量不能超过100'
        ],
        'keyword' => [
            'string' => '关键词必须是字符串',
            'max' => '关键词不能超过50个字符'
        ]
    ],
    'store_approval_member' => [
        'members' => [
            'required' => '成员列表不能为空',
            'array' => '成员列表必须是数组',
            'min' => '至少需要添加一个成员'
        ],
        'members.*.name' => [
            'required' => '用户名称不能为空',
            'string' => '用户名称必须是字符串',
            'max' => '用户名称不能超过50个字符'
        ],
        'members.*.user_id' => [
            'required' => '用户ID不能为空',
            'integer' => '用户ID必须是整数',
            'min' => '用户ID必须大于0',
            'unique' => '该用户已存在于当前审核组'
        ],
        'members.*.position' => [
            'string' => '职位必须是字符串',
            'max' => '职位不能超过50个字符'
        ],
        'members.*.email' => [
            'email' => '邮箱格式不正确',
            'max' => '邮箱不能超过100个字符'
        ],
        'members.*.is_enabled' => [
            'required' => '启用状态不能为空',
            'boolean' => '启用状态必须是布尔值'
        ],
        'groupId' => [
            'required' => '分组ID不能为空',
            'integer' => '分组ID必须是整数',
            'min' => '分组ID必须大于0'
        ]
    ],
    'batch_approval_flow' => [
        'ids' => [
            'required' => '请选择要操作的审批流程',
            'array' => '审批流程ID必须是数组',
            'min' => '至少选择一个审批流程',
            'max' => '最多可选择100个审批流程',
        ],
        'ids.*' => [
            'integer' => '审批流程ID必须是整数',
            'exists' => '选择的审批流程不存在',
        ],
        'action' => [
            'string' => '操作类型必须是字符串',
            'in' => '操作类型无效',
        ],
    ],
    'list_approval_flow' => [
        'page' => [
            'integer' => '页码必须是整数',
            'min' => '页码必须大于等于1',
        ],
        'limit' => [
            'integer' => '每页数量必须是整数',
            'min' => '每页数量必须大于等于1',
            'max' => '每页数量不能超过100',
        ],
        'keyword' => [
            'string' => '关键词必须是字符串',
            'max' => '关键词长度不能超过100',
        ],
        'is_enabled' => [
            'boolean' => '启用状态必须是布尔值',
        ],
        'start_date' => [
            'date' => '开始日期格式无效',
        ],
        'end_date' => [
            'date' => '结束日期格式无效',
            'after_or_equal' => '结束日期必须大于或等于开始日期',
        ],
        'sort_field' => [
            'string' => '排序字段必须是字符串',
            'in' => '排序字段无效',
        ],
        'sort_order' => [
            'string' => '排序方向必须是字符串',
            'in' => '排序方向必须是 asc 或 desc',
        ],
    ],
    'store_approval_flow' => [
        'name' => [
            'required' => '审批流程名称不能为空',
            'string' => '审批流程名称必须是字符串',
            'max' => '审批流程名称不能超过50个字符',
        ],
        'description' => [
            'nullable' => '审批流程描述可以为空',
            'string' => '审批流程描述必须是字符串',
            'max' => '审批流程描述不能超过200个字符',
        ],
        'scope' => [
            'required' => '流程适用范围不能为空',
            'string' => '流程适用范围必须是字符串',
            'max' => '流程适用范围不能超过50个字符',
        ],
        'is_enabled' => [
            'required' => '流程启用状态不能为空',
            'boolean' => '流程启用状态必须是布尔值',
        ],
        'settings' => [
            'required' => '流程设置不能为空',
            'array' => '流程设置必须是数组',
        ],
        'settings.lock_published_content' => [
            'required' => '锁定已发布内容设置不能为空',
            'boolean' => '锁定已发布内容设置必须是布尔值',
        ],
        'settings.allow_attachments' => [
            'required' => '允许附件设置不能为空',
            'boolean' => '允许附件设置必须是布尔值',
        ],
        'settings.allow_planned_date' => [
            'required' => '允许计划日期设置不能为空',
            'boolean' => '允许计划日期设置必须是布尔值',
        ],
        'settings.planned_date' => [
            'date' => '计划日期必须是有效的日期格式',
            'required_if' => '启用计划日期时，计划日期不能为空',
        ],
        'settings.enable_time_limit' => [
            'required' => '启用时间限制设置不能为空',
            'boolean' => '启用时间限制设置必须是布尔值',
        ],
        'settings.allow_cancel_pending' => [
            'required' => '允许取消待处理设置不能为空',
            'boolean' => '允许取消待处理设置必须是布尔值',
        ],
        'settings.allow_permission_extend' => [
            'required' => '允许权限扩展设置不能为空',
            'boolean' => '允许权限扩展设置必须是布尔值',
        ],
        'settings.notification_rules' => [
            'present' => '通知规则必须存在',
            'array' => '通知规则必须是数组',
        ],
        'steps' => [
            'required' => '流程步骤不能为空',
            'min' => '至少需要一个步骤',
        ],
        'steps.name' => [
            'required' => '步骤名称不能为空',
            'string' => '步骤名称必须是字符串',
            'max' => '步骤名称不能超过50个字符',
        ],
        'steps.stepCode' => [
            'required' => '步骤编码不能为空',
            'string' => '步骤编码必须是字符串',
            'max' => '步骤编码不能超过50个字符',
        ],
        'steps.groupIds' => [
            'required' => '步骤审核组不能为空',
            'array' => '步骤审核组必须是数组',
        ],
        'steps.groupIds' => [
            'required' => '步骤审核组ID不能为空',
            'integer' => '步骤审核组ID必须是整数',
            'exists' => '步骤审核组ID不存在',
        ],
        'steps.sort' => [
            'required' => '步骤排序不能为空',
            'integer' => '步骤排序必须是整数',
            'min' => '步骤排序必须大于0',
        ],
        'steps.rejectToDraft' => [
            'required' => '拒绝时返回草稿设置不能为空',
            'integer' => '拒绝时返回草稿设置必须是整数',
            'in' => '拒绝时返回草稿设置值无效',
        ],
    ],
    'update_approval_flow' => [
        'name' => [
            'required' => '审批流程名称不能为空',
            'string' => '审批流程名称必须是字符串',
            'max' => '审批流程名称不能超过50个字符',
        ],
        'description' => [
            'string' => '审批流程描述必须是字符串',
            'max' => '审批流程描述不能超过200个字符',
        ],
        'scope' => [
            'required' => '流程适用范围不能为空',
            'string' => '流程适用范围必须是字符串',
            'max' => '流程适用范围不能超过50个字符',
        ],
        'is_enabled' => [
            'required' => '流程启用状态不能为空',
            'boolean' => '流程启用状态必须是布尔值',
        ],
        'settings.settings.lock_published_content' => [
            'required' => '锁定已发布内容设置不能为空',
            'boolean' => '锁定已发布内容设置必须是布尔值',
        ],
        'settings.settings.allow_attachments' => [
            'required' => '允许附件设置不能为空',
            'boolean' => '允许附件设置必须是布尔值',
        ],
        'settings.settings.allow_planned_date' => [
            'required' => '允许计划日期设置不能为空',
            'boolean' => '允许计划日期设置必须是布尔值',
        ],
        'settings.settings.planned_date' => [
            'date' => '计划日期必须是有效的日期',
        ],
        'settings.settings.enable_time_limit' => [
            'required' => '启用时间限制设置不能为空',
            'boolean' => '启用时间限制设置必须是布尔值',
        ],
        'settings.settings.allow_cancel_pending' => [
            'required' => '允许取消待处理设置不能为空',
            'boolean' => '允许取消待处理设置必须是布尔值',
        ],
        'settings.settings.allow_permission_extend' => [
            'required' => '允许权限扩展设置不能为空',
            'boolean' => '允许权限扩展设置必须是布尔值',
        ],
        'settings.settings.notification_rules' => [
            'present' => '通知规则必须存在',
            'array' => '通知规则必须是数组',
        ],
        'steps' => [
            'required' => '流程步骤不能为空',
            'array' => '流程步骤必须是数组',
            'min' => '至少需要一个步骤',
        ],
        'steps.*.name' => [
            'required' => '步骤名称不能为空',
            'string' => '步骤名称必须是字符串',
            'max' => '步骤名称不能超过50个字符',
        ],
        'steps.*.stepCode' => [
            'required' => '步骤编码不能为空',
            'string' => '步骤编码必须是字符串',
            'max' => '步骤编码不能超过50个字符',
        ],
        'steps.*.sort' => [
            'required' => '步骤排序不能为空',
            'integer' => '步骤排序必须是整数',
            'min' => '步骤排序必须大于0',
        ],
        'steps.*.groupIds' => [
            'required' => '步骤审核组不能为空',
            'array' => '步骤审核组必须是数组',
        ],
        'steps_groupIds' => [
            'required' => '步骤审核组ID不能为空',
            'integer' => '步骤审核组ID必须是整数',
            'exists' => '步骤审核组ID不存在',
        ],
    ],
    'store_approval_product_flow' => [
        'product_table' => [
            'required' => '产品表名不能为空',
            'string' => '产品表名必须为字符串',
        ],
        'product_id' => [
            'required' => '产品ID不能为空',
            'integer' => '产品ID必须为整数',
            'exists' => '产品不存在',
        ],
        'flow_id' => [
            'required' => '审批流程ID不能为空',
            'integer' => '审批流程ID必须为整数',
            'exists' => '审批流程不存在',
        ],
        'remark' => [
            'string' => '备注必须为字符串',
            'max' => '备注最大长度为500个字符',
        ],
    ],
    'store_approval_product_record' => [
        'action' => [
            'required' => '请选择审批动作',
            'integer' => '审批动作格式不正确',
            'in' => '无效的审批动作',
        ],
        'comment' => [
            'string' => '审批意见必须是字符串',
            'max' => '审批意见不能超过1000个字符',
        ],
        'attachments' => [
            'array' => '附件必须是数组',
            '_string' => '每个附件必须是字符串',
        ],
    ],
    'error' => [
        // 通用错误码
        'noRelatedTranslation' => '无相关翻译',
        'userNotFound' => '用户未找到',
        'duplicateEmail' => '邮箱已存在',
        'userNameMismatch' => '用户名称不匹配',

        // 审批流程相关错误码
        'flowNotFound' => '流程未找到',
        'flowAlreadyExists' => '流程已存在',
        'flowCannotBeDeleted' => '流程不能删除',
        'flowCannotBeUpdated' => '流程不能更新',
        'flowStepNotFound' => '流程步骤未找到',
        'flowStepDataError' => '流程步骤数据错误',
        'flowIsNotEnabled' => '流程未打开',

        // 审批步骤相关错误码
        'stepNotFound' => '步骤未找到',
        'stepAlreadyExists' => '步骤已存在',
        'stepCannotBeDeleted' => '步骤不能删除',
        'stepCannotBeUpdated' => '步骤不能更新',

        // 审批角色相关错误码
        'roleNotFound' => '角色未找到',
        'roleAlreadyExists' => '角色已存在',
        'roleCannotBeDeleted' => '角色不能删除',
        'roleCannotBeUpdated' => '角色不能更新',
        'roleInUse' => '角色正在使用中',
        'roleCodeExists' => '角色代码已存在',

        // 审批成员相关错误码
        'memberNotFound' => '成员未找到',
        'memberAlreadyExists' => '成员已存在',
        'memberCannotBeDeleted' => '成员不能删除',
        'memberCannotBeUpdated' => '成员不能更新',

        // 审批组相关错误码
        'groupNotFound' => '审批组未找到',
        'groupCreateFailed' => '审批组创建失败',
        'groupUpdateFailed' => '审批组更新失败',
        'groupDeleteFailed' => '审批组删除失败',
        'groupBatchDeleteFailed' => '审批组批量删除失败',
        'groupStatusUpdateFailed' => '审批组状态更新失败',
        'userNotInApprovalGroup' => '用户不在审批组中',
        'groupInUse' => '审批组正在使用中',

        // 产品审批相关错误码
        'productFlowNotFound' => '产品流程未找到',
        'productFlowAlreadyExists' => '产品流程已存在',
        'productFlowCannotDelete' => '产品流程不能删除',
        'productFlowHasRecords' => '产品流程存在审批记录',
        'productFlowCreateFailed' => '产品流程创建失败',

        // 审批记录相关错误码
        'recordNotFound' => '审批记录未找到',
        'recordCreateFailed' => '审批记录创建失败',
        'recordAlreadyApproved' => '审批记录已审批',
        'logNotFound' => '审批日志未找到',

        // 审批流程相关错误码
        'flowHasUnfinishedRecords' => '该审批流程存在未完成的审批记录，无法关闭',
        'flowCannotBeSwitched' => '该审批流程无法切换状态',
        'recordCannotBeDeleted' => '审批记录不能删除',
        'recordDeleteFailed' => '审批记录删除失败',
    ],
    'list_dashboard' => [
        'page' => [
            'integer' => '页码必须是整数',
            'min' => '页码必须大于等于1'
        ],
        'limit' => [
            'integer' => '每页数量必须是整数',
            'min' => '每页数量必须大于等于1',
            'max' => '每页数量不能超过100'
        ],
        'status' => [
            'string' => '状态必须是字符串',
            'in' => '状态值无效'
        ],
        'keyword' => [
            'string' => '关键词必须是字符串',
        ],
    ],
];
