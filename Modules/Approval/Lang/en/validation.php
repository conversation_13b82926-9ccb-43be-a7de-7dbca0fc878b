<?php

return [
    'batch_approval_flow' => [
        'ids' => [
            'required' => 'Please select approval flows to operate',
            'array' => 'Approval flow IDs must be an array',
            'min' => 'Please select at least one approval flow',
            'max' => 'You can select up to 100 approval flows',
        ],
        'ids.*' => [
            'integer' => 'Approval flow ID must be an integer',
            'exists' => 'Selected approval flow does not exist',
        ],
        'action' => [
            'string' => 'Action type must be a string',
            'in' => 'Invalid action type',
        ],
    ],
    'list_approval_flow' => [
        'page' => [
            'integer' => 'Page number must be an integer',
            'min' => 'Page number must be at least 1',
        ],
        'limit' => [
            'integer' => 'Items per page must be an integer',
            'min' => 'Items per page must be at least 1',
            'max' => 'Items per page cannot exceed 100',
        ],
        'keyword' => [
            'string' => 'Keyword must be a string',
            'max' => 'Keyword cannot exceed 100 characters',
        ],
        'is_enabled' => [
            'boolean' => 'Enabled status must be a boolean',
        ],
        'start_date' => [
            'date' => 'Invalid start date format',
        ],
        'end_date' => [
            'date' => 'Invalid end date format',
            'after_or_equal' => 'End date must be greater than or equal to start date',
        ],
        'sort_field' => [
            'string' => 'Sort field must be a string',
            'in' => 'Invalid sort field',
        ],
        'sort_order' => [
            'string' => 'Sort order must be a string',
            'in' => 'Sort order must be either asc or desc',
        ],
    ],
    'store_approval_flow' => [
        'name' => [
            'required' => 'Approval flow name is required',
            'string' => 'Approval flow name must be a string',
            'max' => 'Approval flow name cannot exceed 50 characters',
        ],
        'description' => [
            'nullable' => 'Approval flow description can be empty',
            'string' => 'Approval flow description must be a string',
            'max' => 'Approval flow description cannot exceed 200 characters',
        ],
        'scope' => [
            'required' => 'Flow scope is required',
            'string' => 'Flow scope must be a string',
            'max' => 'Flow scope cannot exceed 50 characters',
        ],
        'is_enabled' => [
            'required' => 'Flow enabled status is required',
            'boolean' => 'Flow enabled status must be a boolean',
        ],
        'settings' => [
            'required' => 'Flow settings are required',
            'array' => 'Flow settings must be an array',
        ],
        'settings.lock_published_content' => [
            'required' => 'Lock published content setting is required',
            'boolean' => 'Lock published content setting must be a boolean',
        ],
        'settings.allow_attachments' => [
            'required' => 'Allow attachments setting is required',
            'boolean' => 'Allow attachments setting must be a boolean',
        ],
        'settings.allow_planned_date' => [
            'required' => 'Allow planned date setting is required',
            'boolean' => 'Allow planned date setting must be a boolean',
        ],
        'settings.planned_date' => [
            'date' => 'Planned date must be a valid date',
            'required_if' => 'Planned date is required when allow planned date is enabled',
        ],
        'settings.enable_time_limit' => [
            'required' => 'Enable time limit setting is required',
            'boolean' => 'Enable time limit setting must be a boolean',
        ],
        'settings.allow_cancel_pending' => [
            'required' => 'Allow cancel pending setting is required',
            'boolean' => 'Allow cancel pending setting must be a boolean',
        ],
        'settings.allow_permission_extend' => [
            'required' => 'Allow permission extend setting is required',
            'boolean' => 'Allow permission extend setting must be a boolean',
        ],
        'settings.notification_rules' => [
            'present' => 'Notification rules must be present',
            'array' => 'Notification rules must be an array',
        ],
        'steps' => [
            'required' => 'Flow steps are required',
            'min' => 'At least one step is required',
        ],
        'steps.name' => [
            'required' => 'Step name is required',
            'string' => 'Step name must be a string',
            'max' => 'Step name cannot exceed 50 characters',
        ],
        'steps.stepCode' => [
            'required' => 'Step code is required',
            'string' => 'Step code must be a string',
            'max' => 'Step code cannot exceed 50 characters',
        ],
        'steps.groupIds' => [
            'required' => 'Step groups are required',
            'array' => 'Step groups must be an array',
        ],
        'steps.groupIds' => [
            'required' => 'Step group ID is required',
            'integer' => 'Step group ID must be an integer',
            'exists' => 'Step group does not exist',
        ],
        'steps.sort' => [
            'required' => 'Step sort order is required',
            'integer' => 'Step sort order must be an integer',
            'min' => 'Step sort order must be greater than 0',
        ],
        'steps.rejectToDraft' => [
            'required' => 'Reject to draft setting is required',
            'integer' => 'Reject to draft setting must be an integer',
            'in' => 'Invalid reject to draft setting',
        ],
    ],
    'update_approval_flow' => [
        'name' => [
            'required' => 'Approval flow name is required',
            'string' => 'Approval flow name must be a string',
            'max' => 'Approval flow name cannot exceed 50 characters',
        ],
        'description' => [
            'string' => 'Approval flow description must be a string',
            'max' => 'Approval flow description cannot exceed 200 characters',
        ],
        'scope' => [
            'required' => 'Flow scope is required',
            'string' => 'Flow scope must be a string',
            'max' => 'Flow scope cannot exceed 50 characters',
        ],
        'is_enabled' => [
            'required' => 'Flow enabled status is required',
            'boolean' => 'Flow enabled status must be a boolean',
        ],
        'settings.settings.lock_published_content' => [
            'required' => 'Lock published content setting is required',
            'boolean' => 'Lock published content setting must be a boolean',
        ],
        'settings.settings.allow_attachments' => [
            'required' => 'Allow attachments setting is required',
            'boolean' => 'Allow attachments setting must be a boolean',
        ],
        'settings.settings.allow_planned_date' => [
            'required' => 'Allow planned date setting is required',
            'boolean' => 'Allow planned date setting must be a boolean',
        ],
        'settings.settings.planned_date' => [
            'date' => 'Planned date must be a valid date',
        ],
        'settings.settings.enable_time_limit' => [
            'required' => 'Enable time limit setting is required',
            'boolean' => 'Enable time limit setting must be a boolean',
        ],
        'settings.settings.allow_cancel_pending' => [
            'required' => 'Allow cancel pending setting is required',
            'boolean' => 'Allow cancel pending setting must be a boolean',
        ],
        'settings.settings.allow_permission_extend' => [
            'required' => 'Allow permission extend setting is required',
            'boolean' => 'Allow permission extend setting must be a boolean',
        ],
        'settings.settings.notification_rules' => [
            'present' => 'Notification rules must be present',
            'array' => 'Notification rules must be an array',
        ],
        'steps' => [
            'required' => 'Flow steps are required',
            'array' => 'Flow steps must be an array',
            'min' => 'At least one step is required',
        ],
        'steps.*.name' => [
            'required' => 'Step name is required',
            'string' => 'Step name must be a string',
            'max' => 'Step name cannot exceed 50 characters',
        ],
        'steps.*.stepCode' => [
            'required' => 'Step code is required',
            'string' => 'Step code must be a string',
            'max' => 'Step code cannot exceed 50 characters',
        ],
        'steps.*.sort' => [
            'required' => 'Step sort order is required',
            'integer' => 'Step sort order must be an integer',
            'min' => 'Step sort order must be greater than 0',
        ],
        'steps.*.groupIds' => [
            'required' => 'Step groups are required',
            'array' => 'Step groups must be an array',
        ],
        'steps.*.groupIds.*' => [
            'required' => 'Step group ID is required',
            'integer' => 'Step group ID must be an integer',
            'exists' => 'Step group does not exist',
        ],
    ],
    'list_approval_member' => [
        'page' => [
            'integer' => 'Page number must be an integer',
            'min' => 'Page number must be at least 1'
        ],
        'limit' => [
            'integer' => 'Items per page must be an integer',
            'min' => 'Items per page must be at least 1',
            'max' => 'Items per page cannot exceed 100'
        ],
        'keyword' => [
            'string' => 'Keyword must be a string',
            'max' => 'Keyword cannot exceed 50 characters'
        ]
    ],
    'store_approval_member' => [
        'members' => [
            'required' => 'Member list is required',
            'array' => 'Member list must be an array',
            'min' => 'At least one member is required'
        ],
        'members.*.name' => [
            'required' => 'User name is required',
            'string' => 'User name must be a string',
            'max' => 'User name cannot exceed 50 characters'
        ],
        'members.*.user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer',
            'min' => 'User ID must be greater than 0',
            'unique' => 'This user already exists in the current review group'
        ],
        'members.*.position' => [
            'string' => 'Position must be a string',
            'max' => 'Position cannot exceed 50 characters'
        ],
        'members.*.email' => [
            'email' => 'Invalid email format',
            'max' => 'Email cannot exceed 100 characters'
        ],
        'members.*.is_enabled' => [
            'required' => 'Member status is required',
            'boolean' => 'Member status must be a boolean'
        ],
        'groupId' => [
            'required' => 'Group ID is required',
            'integer' => 'Group ID must be an integer',
            'min' => 'Group ID must be greater than 0'
        ]
    ],
    'update_approval_member_status' => [
        'status' => [
            'required' => 'Status is required',
            'string' => 'Status must be a string',
            'in' => 'Invalid status value',
        ],
        'isEnabled' => [
            'required' => 'Status is required',
            'boolean' => 'Invalid status value',
        ],
    ],
    'list_approval_group' => [
        'keyword' => [
            'string' => 'Keyword must be a string',
            'max' => 'Keyword cannot exceed 50 characters'
        ],
        'sort_field' => [
            'string' => 'Sort field must be a string'
        ],
        'sort_order' => [
            'string' => 'Sort order must be a string',
            'in' => 'Sort order must be either asc or desc'
        ],
        'page' => [
            'integer' => 'Page number must be an integer',
            'min' => 'Page number must be at least 1'
        ],
        'limit' => [
            'integer' => 'Items per page must be an integer',
            'min' => 'Items per page must be at least 1',
            'max' => 'Items per page cannot exceed 100'
        ]
    ],
    'store_approval_group' => [
        'name' => [
            'required' => 'Name is required',
            'string' => 'Name must be a string',
            'max' => 'Name cannot exceed 50 characters'
        ],
        'code' => [
            'required' => 'Code is required',
            'string' => 'Code must be a string', 
            'max' => 'Code cannot exceed 50 characters',
            'unique' => 'Code has already been taken'
        ],
        'is_inherit' => [
            'required' => 'Inherit status is required',
            'boolean' => 'Inherit status must be a boolean'
        ],
        'description' => [
            'max' => 'Description cannot exceed 200 characters'
        ],
        'status' => [
            'required' => 'Status is required',
            'boolean' => 'Status must be a boolean'
        ],
        'permissions' => [
            'present' => 'Permissions field is required',
            'array' => 'Permissions must be an array'
        ],
        'permissions.*' => [
            'id' => [
                'required' => 'Permission ID is required',
                'string' => 'Permission ID must be a string'
            ],
            'name' => [
                'required' => 'Permission name is required',
                'string' => 'Permission name must be a string'
            ],
            'description' => [
                'string' => 'Permission description must be a string',
                'max' => 'Permission description cannot exceed 200 characters'
            ],
            'actions' => [
                'required' => 'Permission actions are required',
                'array' => 'Permission actions must be an array'
            ],
            'actions.*' => [
                'required' => 'Action is required',
                'string' => 'Action must be a string',
                'in' => 'Invalid action value'
            ]
        ],
        'members' => [
            'required' => 'Members are required',
            'array' => 'Members must be an array'
        ],
        'members.*' => [
            'name' => [
                'required' => 'Member name is required',
                'string' => 'Member name must be a string',
                'max' => 'Member name cannot exceed 50 characters'
            ],
            'position' => [
                'required' => 'Position is required', 
                'string' => 'Position must be a string',
                'max' => 'Position cannot exceed 50 characters'
            ],
            'email' => [
                'required' => 'Email is required',
                'email' => 'Invalid email format',
                'max' => 'Email cannot exceed 100 characters'
            ],
            'is_enabled' => [
                'required' => 'Member status is required',
                'boolean' => 'Member status must be a boolean'
            ]
        ]
    ],
    'update_approval_group' => [
        'name' => [
            'required' => 'Name is required',
            'max' => 'Name cannot exceed 50 characters'
        ],
        'code' => [
            'required' => 'Code is required',
            'max' => 'Code cannot exceed 50 characters',
            'unique' => 'Code has already been taken'
        ],
        'is_inherit' => [
            'required' => 'Inherit status is required', 
            'boolean' => 'Inherit status must be a boolean'
        ],
        'description' => [
            'max' => 'Description cannot exceed 200 characters'
        ],
        'status' => [
            'required' => 'Status is required',
            'boolean' => 'Status must be a boolean'
        ],
        'permissions' => [
            'array' => 'Permissions must be an array'
        ],
        'permissions.*' => [
            'id' => [
                'required' => 'Permission ID is required',
                'string' => 'Permission ID must be a string'
            ],
            'name' => [
                'required' => 'Permission name is required', 
                'string' => 'Permission name must be a string'
            ],
            'description' => [
                'string' => 'Permission description must be a string',
                'max' => 'Permission description cannot exceed 200 characters'
            ],
            'actions' => [
                'required' => 'Permission actions are required',
                'array' => 'Permission actions must be an array'
            ],
            'actions.*' => [
                'required' => 'Each action is required',
                'string' => 'Each action must be a string',
                'in' => 'Invalid action value'
            ]
        ],
        'members' => [
            'required' => 'Members are required',
            'array' => 'Members must be an array'
        ],
        'members.*' => [
            'name' => [
                'required' => 'Member name is required',
                'max' => 'Member name cannot exceed 50 characters'
            ],
            'position' => [
                'required' => 'Position is required',
                'max' => 'Position cannot exceed 50 characters'
            ],
            'email' => [
                'required' => 'Email is required',
                'email' => 'Invalid email format',
                'max' => 'Email cannot exceed 100 characters'
            ],
            'is_enabled' => [
                'required' => 'Member status is required',
                'boolean' => 'Member status must be a boolean'
            ]
        ]
    ],
    'batch_approval_group' => [
        'ids' => 'IDs',
        'id' => 'ID',
        'ids.required' => 'The IDs field is required.',
        'ids.array' => 'The IDs must be an array.',
        'ids.*.required' => 'Each ID in the array is required.',
        'ids.*.exists' => 'One or more selected IDs are invalid.'
    ],
    'store_approval_role' => [
        'name' => [
            'required' => 'Role name is required',
            'string' => 'Role name must be a string',
            'max' => 'Role name cannot exceed 50 characters',
        ],
        'code' => [
            'required' => 'Role code is required',
            'string' => 'Role code must be a string',
            'max' => 'Role code cannot exceed 50 characters',
            'unique' => 'Role code already exists',
        ],
        'level' => [
            'required' => 'Role level is required',
            'integer' => 'Role level must be an integer',
            'min' => 'Role level must be at least 1',
        ],
        'is_inherit' => [
            'required' => 'Please select whether to inherit',
            'boolean' => 'Inherit flag must be a boolean'
        ],
        'permissions' => [
            'required' => 'Permissions are required',
            'array' => 'Permissions must be an array'
        ],
        'description' => [
            'required' => 'Description is required',
            'string' => 'Description must be a string',
            'max' => 'Description cannot exceed 255 characters',
        ],
        'status' => [
            'required' => 'Status is required',
            'boolean' => 'Status must be a boolean'
        ]
    ],
    'batch_approval_role' => [
        'ids' => [
            'required' => 'Please select roles to operate',
            'array' => 'Role IDs must be an array',
        ],
        'ids.*' => [
            'required' => 'Role ID cannot be empty',
            'integer' => 'Role ID must be an integer',
            'min' => 'Role ID must be greater than 0',
        ],
    ],
    'list_approval_role' => [
        'keyword' => [
            'string' => 'The keyword must be a string',
            'max' => 'The keyword must not exceed 50 characters'
        ],
        'status' => [
            'boolean' => 'The status must be a boolean'
        ],
        'sort_field' => [
            'string' => 'The sort field must be a string',
            'in' => 'Invalid sort field'
        ],
        'sort_order' => [
            'string' => 'The sort order must be a string',
            'in' => 'The sort order must be either asc or desc'
        ],
        'per_page' => [
            'integer' => 'The per page value must be an integer',
            'min' => 'The per page value must be at least 1',
            'max' => 'The per page value must not exceed 100'
        ]
    ],
    'update_approval_role' => [
        'name' => [
            'required' => 'Role name is required',
            'string' => 'Role name must be a string',
            'max' => 'Role name cannot exceed 50 characters',
        ],
        'code' => [
            'required' => 'Role code is required',
            'string' => 'Role code must be a string',
            'max' => 'Role code cannot exceed 50 characters',
            'unique' => 'Role code already exists',
        ],
        'level' => [
            'integer' => 'Role level must be an integer',
            'min' => 'Role level must be greater than 0',
        ],
        'is_inherit' => [
            'boolean' => 'Inherit flag must be a boolean'
        ],
        'permissions' => [
            'array' => 'Permissions must be an array'
        ],
        'description' => [
            'string' => 'Description must be a string',
            'max' => 'Description cannot exceed 255 characters',
        ],
        'status' => [
            'boolean' => 'Status must be a boolean'
        ]
    ],
    'store_approval_product_flow' => [
        'product_table' => [
            'required' => 'Product table name is required',
            'string' => 'Product table name must be a string',
        ],
        'product_id' => [
            'required' => 'Product ID is required',
            'integer' => 'Product ID must be an integer',
            'exists' => 'Product does not exist',
        ],
        'flow_id' => [
            'required' => 'Approval flow ID is required',
            'integer' => 'Approval flow ID must be an integer',
            'exists' => 'Approval flow does not exist',
        ],
        'remark' => [
            'string' => 'Remark must be a string',
            'max' => 'Remark cannot exceed 500 characters',
        ],
    ],
    'store_approval_product_record' => [
        'action' => [
            'required' => 'Please select an approval action',
            'integer' => 'Invalid approval action format',
            'in' => 'Invalid approval action',
        ],
        'comment' => [
            'string' => 'Comment must be a string',
            'max' => 'Comment cannot exceed 1000 characters',
        ],
        'attachments' => [
            'array' => 'Attachments must be an array',
            '_string' => 'Each attachment must be a string',
        ],
    ],
    'error' => [
        // 通用错误码
        'noRelatedTranslation' => 'No related translation',
        'userNotFound' => 'User not found',
        'duplicateEmail' => 'Email already exists',
        'userNameMismatch' => 'User name mismatch',

        // 审批流程相关错误码
        'flowNotFound' => 'Approval flow not found',
        'flowAlreadyExists' => 'Approval flow already exists',
        'flowCannotBeDeleted' => 'Approval flow cannot be deleted',
        'flowCannotBeUpdated' => 'Approval flow cannot be updated',
        'flowStepNotFound' => 'Flow step not found',
        'flowStepDataError' => 'Flow step data error',
        'flowIsNotEnabled' => 'Approval flow is not enabled',

        // 审批步骤相关错误码
        'stepNotFound' => 'Step not found',
        'stepAlreadyExists' => 'Step already exists',
        'stepCannotBeDeleted' => 'Step cannot be deleted',
        'stepCannotBeUpdated' => 'Step cannot be updated',

        // 审批角色相关错误码
        'roleNotFound' => 'Role not found',
        'roleAlreadyExists' => 'Role already exists',
        'roleCannotBeDeleted' => 'Role cannot be deleted',
        'roleCannotBeUpdated' => 'Role cannot be updated',
        'roleInUse' => 'Role is in use',
        'roleCodeExists' => 'Role code already exists',

        // 审批成员相关错误码
        'memberNotFound' => 'Member not found',
        'memberAlreadyExists' => 'Member already exists',
        'memberCannotBeDeleted' => 'Member cannot be deleted',
        'memberCannotBeUpdated' => 'Member cannot be updated',

        // 审批组相关错误码
        'groupNotFound' => 'Approval group not found',
        'groupCreateFailed' => 'Failed to create approval group',
        'groupUpdateFailed' => 'Failed to update approval group',
        'groupDeleteFailed' => 'Failed to delete approval group',
        'groupBatchDeleteFailed' => 'Failed to batch delete approval groups',
        'groupStatusUpdateFailed' => 'Failed to update approval group status',
        'userNotInApprovalGroup' => 'User is not in the approval group',
        'groupInUse' => 'Approval group is in use',

        // 产品审批相关错误码
        'productFlowNotFound' => 'Product flow not found',
        'productFlowAlreadyExists' => 'Product flow already exists',
        'productFlowCannotDelete' => 'Product flow cannot be deleted',
        'productFlowHasRecords' => 'Product flow has approval records',
        'productFlowCreateFailed' => 'Product flow creation failed',

        // 审批记录相关错误码
        'recordNotFound' => 'Approval record not found',
        'recordCreateFailed' => 'Failed to create approval record',
        'recordAlreadyApproved' => 'Record has already been approved',
        'logNotFound' => 'Approval log not found',

        // 审批流程相关错误码
        'flowHasUnfinishedRecords' => 'The approval flow has unfinished approval records and cannot be closed',
        'flowCannotBeSwitched' => 'The approval flow cannot be switched',
        'recordCannotBeDeleted' => 'The approval record cannot be deleted',
        'recordDeleteFailed' => 'Failed to delete approval record',
    ],
    'list_dashboard' => [
        'page' => [
            'integer' => 'Page must be an integer',
            'min' => 'Page must be at least 1',
        ],
        'limit' => [
            'integer' => 'Limit must be an integer',
            'min' => 'Limit must be at least 1',
            'max' => 'Limit must not exceed 100',
        ],
        'status' => [
            'string' => 'Status must be a string',
            'in' => 'Invalid status value',
        ],
        'keyword' => [
            'string' => 'Keyword must be a string',
        ],
    ],
];
