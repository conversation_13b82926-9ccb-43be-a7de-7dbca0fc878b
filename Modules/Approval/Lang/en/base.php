<?php

return [
    "nav" => [
        "admin_setting" => [
            "approval" => "Approval Management",
            "groups" => "Approval Groups",
            "flows" => "Approval Flows",
            "records" => "Approval Records",
            "members" => "Approval Members"
        ]
    ],
    'permission' => [
        "admin_setting" => [
            "groups" => "Approval Groups",
            "flows" => "Approval Flows",
            "records" => "Approval Records",
            "members" => "Approval Members",
            "title" => "Approval Management"
        ]
    ],
    'schedule' => [
        'check_flow_timeout' => 'Check Flow Timeout',
        'check_flow_timeout_description' => 'Check if any approval flows have timed out and update their status accordingly.'
    ],
    'template' => [
        'notification' => [
            'flow_timeout' => 'Flow Timeout Notification',
        ],
        'flow_timeout_subject' => 'Flow Timeout Notification',
        'flow_timeout_description' => 'The approval flow has timed out. Please check the status of the flow.'
    ],
    "Progress" => "Progress",
    "Add_Progress" => "Add Progress",
    "List" => "List",
    "Detail" => "Detail",
    "Create" => "Create",
    "Update" => "Update",
    "Delete" => "Delete",
    "Permissions" => "Permissions",
    "Members List" => "Members List",
    "Add Member" => "Add Member",
    "Import Members" => "Import Members",
    "Update Status" => "Update Status",
    "Delete Member" => "Delete Member",
    "Setting Index" => "Setting Index",
    "Setting Structure" => "Setting Structure",
    "Product Store" => "Product Store",
    "Product Delete" => "Product Delete",  
];
