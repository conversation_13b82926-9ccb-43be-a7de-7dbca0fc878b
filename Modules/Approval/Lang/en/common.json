{"FlowList": {"create": "Create Flow", "keyword": "Keyword", "namePlaceholder": "Flow Name", "search": "Query", "reset": "Reset", "name": "Flow Name", "scope": "<PERSON><PERSON>", "createdAt": "Created Date", "updatedAt": "Modified Date", "status": "Status", "operations": "Operations", "view": "View", "copy": "Copy", "edit": "Edit", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this flow? This action cannot be undone.", "tip": "Tip", "confirm": "Confirm", "cancel": "Cancel", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "deleteCancel": "Delete cancelled", "enabled": "Enabled", "disabled": "Disabled", "statusUpdateFailed": "Status update failed", "getListFailed": "Failed to get flow list", "copySuccess": "Copy successful", "copyFailed": "Co<PERSON> failed", "scopeOptions": {"product": "Product Category Management", "whitepaper": "Whitepaper Management", "news": "News List"}}, "FlowCreate": {"basicInfo": "Basic Information", "name": "Flow Name", "namePlaceholder": "Please enter flow name", "scope": "<PERSON><PERSON>", "scopePlaceholder": "Please enter scope", "description": "Description", "descriptionPlaceholder": "Please enter description", "steps": "Step Configuration", "addStep": "Add Step", "step": "Step {num}", "stepName": "Step Name", "stepNamePlaceholder": "Please enter step name", "stepCode": "Step Code", "stepCodePlaceholder": "Please enter step code", "approvalRoles": "Approval Roles", "selectRoles": "Please select approval roles", "settings": {"title": "Flow Settings", "lockContent": "Lock Published Content", "lockContentDesc": "Control edit access permissions for content nodes in workflow", "allowAttachments": "Allow Attachments", "allowAttachmentsDesc": "Allow adding attachments when starting workflow", "allowPlannedDate": "Enable Planned Date", "allowPlannedDateDesc": "Allow setting planned date when starting workflow", "selectDate": "Please select date", "enableTimeLimit": "Enable Time Limit", "enableTimeLimitDesc": "Allow auto-promotion of task priority in urgent flows", "allowCancelPending": "Allow <PERSON><PERSON> Pending Approval", "allowCancelPendingDesc": "Allow setting planned date when starting workflow", "allowPermissionExtend": "Allow Permission Extension (Default is Permission Replacement)", "allowPermissionExtendDesc": "Should approval flow extend or replace user's save and publish permissions?"}, "notification": {"title": "Notification Settings", "send": "Send", "when": "When", "selectChannels": "Please select notification methods", "selectEvent": "Please select trigger event", "addRule": "Add Notification Rule", "channels": {"email": "Email Notification", "sms": "SMS Notification", "system": "System Notification"}, "events": {"processStart": "Process Start", "processTimeout": "Process Timeout", "emergencyTrigger": "Emergency Process Trigger"}}, "cancel": "Cancel", "confirm": "Confirm", "rules": {"nameRequired": "Please enter flow name", "nameLength": "Length should be between 2 and 50 characters", "scopeRequired": "Please enter scope", "scopeLength": "Cannot exceed 100 characters", "descriptionLength": "Cannot exceed 200 characters", "stepNameRequired": "Please enter step name", "stepCodeRequired": "Please enter step code", "groupRequired": "Please select approval roles"}, "getGroupsFailed": "Failed to get approval groups", "maxNotificationRules": "Maximum 3 notification rules allowed", "createSuccess": "Created successfully", "createFailed": "Creation failed"}, "FlowDetail": {"edit": "Edit Flow", "basicInfo": "Basic Information", "name": "Flow Name", "scope": "<PERSON><PERSON>", "description": "Description", "createdAt": "Created Time", "updatedAt": "Modified Time", "stepInfo": "Step Information", "stepCode": "Step Code", "approvalRoles": "Approval Roles", "yes": "Yes", "no": "No", "settings": {"title": "Flow Settings", "lockContent": "Lock Published Content", "allowAttachments": "Allow Attachments", "allowPlannedDate": "Allow Planned Date", "plannedDate": "Planned Date", "enableTimeLimit": "Enable Time Limit", "allowCancelPending": "Allow <PERSON><PERSON> A<PERSON>al", "allowPermissionExtend": "Allow Permission Extension"}, "notification": {"title": "Notification Rules", "empty": "No notification rules", "events": {"processStart": "Process Start", "processTimeout": "Process Timeout", "emergencyTrigger": "Emergency Alert"}, "channels": {"email": "Email Notification", "sms": "SMS Notification", "system": "System Notification"}}, "getDetailFailed": "Failed to get flow details"}, "FlowEdit": {"basicInfo": "Basic Information", "name": "Flow Name", "namePlaceholder": "Please enter flow name", "scope": "<PERSON><PERSON>", "scopePlaceholder": "Please enter scope", "description": "Description", "descriptionPlaceholder": "Please enter description", "steps": "Step Configuration", "addStep": "Add Step", "step": "Step {num}", "stepName": "Step Name", "stepNamePlaceholder": "Please enter step name", "stepCode": "Step Code", "stepCodePlaceholder": "Please enter step code", "approvalRoles": "Approval Roles", "selectRoles": "Please select approval roles", "settings": {"title": "Flow Settings", "lockContent": "Lock Published Content", "lockContentDesc": "Control edit access permissions for content nodes in workflow", "allowAttachments": "Allow Attachments", "allowAttachmentsDesc": "Allow adding attachments when starting workflow", "allowPlannedDate": "Enable Planned Date", "allowPlannedDateDesc": "Allow setting planned date when starting workflow", "selectDate": "Please select date", "enableTimeLimit": "Enable Time Limit", "enableTimeLimitDesc": "Allow auto-promotion of task priority in urgent flows", "allowCancelPending": "Allow <PERSON><PERSON> Pending Approval", "allowCancelPendingDesc": "Allow setting planned date when starting workflow", "allowPermissionExtend": "Allow Permission Extension (Default is Permission Replacement)", "allowPermissionExtendDesc": "Should approval flow extend or replace user's save and publish permissions?"}, "notification": {"title": "Notification Settings", "send": "Send", "when": "When", "selectChannels": "Please select notification methods", "selectEvent": "Please select trigger event", "addRule": "Add Notification Rule", "channels": {"email": "Email Notification", "sms": "SMS Notification", "system": "System Notification"}, "events": {"processStart": "Process Start", "processTimeout": "Process Timeout", "emergencyTrigger": "Emergency Process Trigger"}}, "cancel": "Cancel", "save": "Save", "rules": {"nameRequired": "Please enter flow name", "nameLength": "Length should be between 2 and 50 characters", "scopeRequired": "Please enter scope", "scopeLength": "Cannot exceed 100 characters", "descriptionLength": "Cannot exceed 200 characters", "stepNameRequired": "Please enter step name", "stepCodeRequired": "Please enter step code", "groupRequired": "Please select approval roles"}, "getGroupsFailed": "Failed to get approval groups", "maxNotificationRules": "Maximum 3 notification rules allowed", "getDetailFailed": "Failed to get flow details", "updateSuccess": "Save successful", "updateFailed": "Save failed"}, "ReviewGroup": {"create": "Create Review Group", "keyword": "Keyword", "namePlaceholder": "Review Group Name", "search": "Search", "reset": "Reset", "name": "Review Group Name", "memberCount": "Member Count", "operations": "Operations", "edit": "Edit", "copy": "Copy", "delete": "Delete", "confirmTitle": "Confirm Delete", "confirmDelete": "Are you sure you want to delete this review group? This action cannot be undone.", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "copySuccess": "Copy successful", "copyFailed": "Co<PERSON> failed", "getListFailed": "Failed to get review group list", "searchPlaceholder": "Search review groups", "viewDetail": "View Detail", "editGroup": "Edit Review Group", "noData": "No data available", "totalItems": "Total {total} items", "pageSize": "Items per page", "selectAction": "Select Action", "selectActionTip": "Please select the action you want to perform", "deleteTitle": "Delete Review Group", "memberList": "Member List", "memberName": "Member Name", "memberNamePlaceholder": "Member Name", "position": "Position", "positionPlaceholder": "Position", "email": "Email", "emailPlaceholder": "Email", "status": "Status", "enabled": "Enabled", "disabled": "Disabled", "addMember": "Add Member", "addMemberBtn": "Add Member", "manualAdd": "Manual Add Member", "batchImport": "Batch Import", "selectFile": "Select File", "previewData": "Preview Data", "import": "Import", "add": "Add", "update": "Update", "editMember": "Edit Member", "emailExists": "Email already exists", "updateMemberSuccess": "Member updated successfully", "addMemberSuccess": "Member added successfully", "fileParseSuccess": "File parsed successfully, {count} records found", "fileParseError": "File parsing failed", "noNewMembers": "No new members to import", "batchImportSuccess": "Batch import successful, {count} members imported", "updateSuccess": "Save successful", "updateFailed": "Save failed", "createSuccess": "Create successful", "createFailed": "Create failed", "getDetailFailed": "Failed to get review group details", "rules": {"nameRequired": "Please enter review group name", "nameLength": "Length should be between 2 and 50 characters", "membersRequired": "Please add at least one member", "memberNameRequired": "Please enter member name", "positionRequired": "Please enter position", "emailRequired": "Please enter email", "emailInvalid": "Please enter a valid email address"}}, "FlowDashboard": {"flowStatus": "Flow Status", "processing": "Processing", "timeout": "Timeout", "pendingPublish": "Pending Publish", "pendingApproval": "Pending Approval", "publishedApproval": "Published Approval", "completedApproval": "Completed Approval", "keyword": "Please enter keyword", "filter": "Filter", "projectName": "Project Name", "contentType": "Content Type", "deadline": "Deadline", "lastModified": "Last Modified", "operations": "Operations", "view": "View", "delete": "Delete", "noDeadline": "No Deadline", "getDataFailed": "Failed to get data", "deleteConfirmTitle": "Confirm Delete", "deleteConfirmContent": "Are you sure you want to delete this item?", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed"}, "FlowApproval": {"flowOperation": "Flow Operation", "quickMessage": "Quick Message", "quickMessageOptions": {"pass": "Approved, please proceed with the next steps", "incomplete": "Data incomplete, please provide necessary information", "reject": "Sorry, your application has been rejected"}, "approvalOpinion": "Approval Opinion", "approvalOpinionPlaceholder": "Please enter approval opinion", "approve": "Approve", "reject": "Reject", "cancel": "Cancel", "flowStatus": "Flow Status", "status": {"completed": "Completed", "pending": "Pending", "rejected": "Rejected", "processing": "Processing"}, "department": "Department", "confirmReject": "Confirm Reject", "confirmRejectContent": "Are you sure you want to reject this approval?", "pleaseEnterOpinion": "Please enter approval opinion", "approveSuccess": "Approval successful", "approveFailed": "Approval failed", "rejectSuccess": "Rejected successfully", "rejectFailed": "Operation failed", "getProgressFailed": "Failed to get flow progress", "attachment": "Attachments", "addAttachment": "Add Attachment", "attachmentTip": "Support images and Excel files, max size 10MB per file", "attachmentTypeError": "Only images and Excel files are allowed", "attachmentSizeError": "File size cannot exceed 10MB", "uploadFailed": "Upload failed", "uploadSuccess": "Upload successful"}, "router": {"management": "Management", "recordList": "Approval Records", "recordApproval": "Approval Processing", "flowList": "Flow List", "flowCreate": "View Flow", "flowDetail": "Flow Details", "flowEdit": "Edit Flow", "flowHistory": "Flow History", "roleList": "Role List", "roleCreate": "Create Role", "roleEdit": "Edit Role", "groupList": "Review Group List", "groupCreate": "Create Review Group", "groupEdit": "Edit Review Group"}}