<?php

return [
    'batch_approval_flow' => [
        'ids' => [
            'required' => '請選擇要操作的審批流程',
            'array' => '審批流程ID必須是數組',
            'min' => '至少選擇一個審批流程',
            'max' => '最多可選擇100個審批流程',
        ],
        'ids.*' => [
            'integer' => '審批流程ID必須是整數',
            'exists' => '選擇的審批流程不存在',
        ],
        'action' => [
            'string' => '操作類型必須是字符串',
            'in' => '操作類型無效',
        ],
    ],
    'list_approval_flow' => [
        'page' => [
            'integer' => '頁碼必須是整數',
            'min' => '頁碼必須大於等於1',
        ],
        'limit' => [
            'integer' => '每頁數量必須是整數',
            'min' => '每頁數量必須大於等於1',
            'max' => '每頁數量不能超過100',
        ],
        'keyword' => [
            'string' => '關鍵詞必須是字符串',
            'max' => '關鍵詞長度不能超過100',
        ],
        'status' => [
            'string' => '狀態必須是字符串',
            'in' => '狀態值無效',
        ],
        'start_date' => [
            'date' => '開始日期格式無效',
        ],
        'end_date' => [
            'date' => '結束日期格式無效',
            'after_or_equal' => '結束日期必須大於或等於開始日期',
        ],
        'sort_field' => [
            'string' => '排序字段必須是字符串',
            'in' => '排序字段無效',
        ],
        'sort_order' => [
            'string' => '排序方向必須是字符串',
            'in' => '排序方向必須是 asc 或 desc',
        ],
    ],
    'store_approval_flow' => [
        'name' => [
            'required' => '審批流程名稱不能為空',
            'string' => '審批流程名稱必須是字符串',
            'max' => '審批流程名稱不能超過50個字符',
        ],
        'description' => [
            'nullable' => '審批流程描述可以為空',
            'string' => '審批流程描述必須是字符串',
            'max' => '審批流程描述不能超過200個字符',
        ],
        'scope' => [
            'required' => '流程適用範圍不能為空',
            'string' => '流程適用範圍必須是字符串',
            'max' => '流程適用範圍不能超過50個字符',
        ],
        'is_enabled' => [
            'required' => '流程啟用狀態不能為空',
            'boolean' => '流程啟用狀態必須是布爾值',
        ],
        'settings' => [
            'required' => '流程設置不能為空',
            'array' => '流程設置必須是數組',
        ],
        'settings.lock_published_content' => [
            'required' => '鎖定已發布內容設置不能為空',
            'boolean' => '鎖定已發布內容設置必須是布爾值',
        ],
        'settings.allow_attachments' => [
            'required' => '允許附件設置不能為空',
            'boolean' => '允許附件設置必須是布爾值',
        ],
        'settings.allow_planned_date' => [
            'required' => '允許計劃日期設置不能為空',
            'boolean' => '允許計劃日期設置必須是布爾值',
        ],
        'settings.planned_date' => [
            'date' => '計劃日期必須是有效的日期格式',
            'required_if' => '啟用計劃日期時，計劃日期不能為空',
        ],
        'settings.enable_time_limit' => [
            'required' => '啟用時間限制設置不能為空',
            'boolean' => '啟用時間限制設置必須是布爾值',
        ],
        'settings.allow_cancel_pending' => [
            'required' => '允許取消待處理設置不能為空',
            'boolean' => '允許取消待處理設置必須是布爾值',
        ],
        'settings.allow_permission_extend' => [
            'required' => '允許權限擴展設置不能為空',
            'boolean' => '允許權限擴展設置必須是布爾值',
        ],
        'settings.notification_rules' => [
            'present' => '通知規則必須存在',
            'array' => '通知規則必須是數組',
        ],
        'steps' => [
            'required' => '流程步驟不能為空',
            'min' => '至少需要一個步驟',
        ],
        'steps.name' => [
            'required' => '步驟名稱不能為空',
            'string' => '步驟名稱必須是字符串',
            'max' => '步驟名稱不能超過50個字符',
        ],
        'steps.stepCode' => [
            'required' => '步驟編碼不能為空',
            'string' => '步驟編碼必須是字符串',
            'max' => '步驟編碼不能超過50個字符',
        ],
        'steps.groupIds' => [
            'required' => '步驟審核組不能為空',
            'array' => '步驟審核組必須是數組',
        ],
        'steps.groupIds' => [
            'required' => '步驟審核組ID不能為空',
            'integer' => '步驟審核組ID必須是整數',
            'exists' => '步驟審核組ID不存在',
        ],
        'steps.sort' => [
            'required' => '步驟排序不能為空',
            'integer' => '步驟排序必須是整數',
            'min' => '步驟排序必須大於0',
        ],
        'steps.rejectToDraft' => [
            'required' => '拒絕時返回草稿設置不能為空',
            'integer' => '拒絕時返回草稿設置必須是整數',
            'in' => '拒絕時返回草稿設置值無效',
        ],
    ],
    'update_approval_flow' => [
        'name' => [
            'required' => '審批流程名稱不能為空',
            'string' => '審批流程名稱必須是字符串',
            'max' => '審批流程名稱不能超過50個字符',
        ],
        'description' => [
            'string' => '審批流程描述必須是字符串',
            'max' => '審批流程描述不能超過200個字符',
        ],
        'scope' => [
            'required' => '流程適用範圍不能為空',
            'string' => '流程適用範圍必須是字符串',
            'max' => '流程適用範圍不能超過50個字符',
        ],
        'is_enabled' => [
            'required' => '流程啟用狀態不能為空',
            'boolean' => '流程啟用狀態必須是布爾值',
        ],
        'settings.settings.lock_published_content' => [
            'required' => '鎖定已發布內容設置不能為空',
            'boolean' => '鎖定已發布內容設置必須是布爾值',
        ],
        'settings.settings.allow_attachments' => [
            'required' => '允許附件設置不能為空',
            'boolean' => '允許附件設置必須是布爾值',
        ],
        'settings.settings.allow_planned_date' => [
            'required' => '允許計劃日期設置不能為空',
            'boolean' => '允許計劃日期設置必須是布爾值',
        ],
        'settings.settings.planned_date' => [
            'date' => '計劃日期必須是有效的日期格式',
        ],
        'settings.settings.enable_time_limit' => [
            'required' => '啟用時間限制設置不能為空',
            'boolean' => '啟用時間限制設置必須是布爾值',
        ],
        'settings.settings.allow_cancel_pending' => [
            'required' => '允許取消待審核設置不能為空',
            'boolean' => '允許取消待審核設置必須是布爾值',
        ],
        'settings.settings.allow_permission_extend' => [
            'required' => '允許權限擴展設置不能為空',
            'boolean' => '允許權限擴展設置必須是布爾值',
        ],
        'settings.settings.notification_rules' => [
            'present' => '通知規則設置不能為空',
            'array' => '通知規則設置必須是數組',
        ],
        'steps' => [
            'required' => '流程步驟不能為空',
            'array' => '流程步驟必須是數組',
            'min' => '至少需要一個流程步驟',
        ],
        'steps.*.name' => [
            'required' => '步驟名稱不能為空',
            'string' => '步驟名稱必須是字符串',
            'max' => '步驟名稱不能超過50個字符',
        ],
        'steps.*.stepCode' => [
            'required' => '步驟編碼不能為空',
            'string' => '步驟編碼必須是字符串',
            'max' => '步驟編碼不能超過50個字符',
        ],
        'steps.*.sort' => [
            'required' => '步驟排序不能為空',
            'integer' => '步驟排序必須是整數',
            'min' => '步驟排序必須大於0',
        ],
        'steps.*.groupIds' => [
            'required' => '步驟審核組不能為空',
            'array' => '步驟審核組必須是數組',
        ],
        'steps.*.groupIds.*' => [
            'required' => '步驟審核組ID不能為空',
            'integer' => '步驟審核組ID必須是整數',
            'exists' => '步驟審核組ID不存在',
        ],
    ],
    'list_approval_member' => [
        'page' => [
            'integer' => '頁碼必須是整數',
            'min' => '頁碼必須大於等於1'
        ],
        'limit' => [
            'integer' => '每頁數量必須是整數',
            'min' => '每頁數量必須大於等於1',
            'max' => '每頁數量不能超過100'
        ],
        'keyword' => [
            'string' => '關鍵詞必須是字符串',
            'max' => '關鍵詞不能超過50個字符'
        ]
    ],
    'store_approval_member' => [
        'members' => [
            'required' => '成員列表不能為空',
            'array' => '成員列表必須是數組',
            'min' => '至少需要添加一個成員'
        ],
        'members.*.name' => [
            'required' => '用戶名稱不能為空',
            'string' => '用戶名稱必須是字符串',
            'max' => '用戶名稱不能超過50個字符'
        ],
        'members.*.user_id' => [
            'required' => '用戶ID不能為空',
            'integer' => '用戶ID必須是整數',
            'min' => '用戶ID必須大於0',
            'unique' => '該用戶已存在於當前審核組'
        ],
        'members.*.position' => [
            'string' => '職位必須是字符串',
            'max' => '職位不能超過50個字符'
        ],
        'members.*.email' => [
            'email' => '郵箱格式不正確',
            'max' => '郵箱不能超過100個字符'
        ],
        'members.*.is_enabled' => [
            'required' => '啟用狀態不能為空',
            'boolean' => '啟用狀態必須是布爾值'
        ],
        'groupId' => [
            'required' => '分組ID不能為空',
            'integer' => '分組ID必須是整數',
            'min' => '分組ID必須大於0'
        ]
    ],
    'update_approval_member_status' => [
        'status' => [
            'required' => '狀態不能為空',
            'string' => '狀態必須是字符串',
            'in' => '狀態值無效',
        ],
        'isEnabled' => [
            'required' => '狀態不能為空',
            'boolean' => '狀態值無效',
        ],
    ],
    'list_approval_group' => [
        'keyword' => [
            'string' => '關鍵詞必須是字符串',
            'max' => '關鍵詞不能超過50個字符'
        ],
        'sort_field' => [
            'string' => '排序字段必須是字符串'
        ],
        'sort_order' => [
            'string' => '排序方向必須是字符串',
            'in' => '排序方向必須是 asc 或 desc'
        ],
        'page' => [
            'integer' => '頁碼必須是整數',
            'min' => '頁碼必須大於等於1'
        ],
        'limit' => [
            'integer' => '每頁數量必須是整數',
            'min' => '每頁數量必須大於等於1',
            'max' => '每頁數量不能超過100'
        ]
    ],
    'store_approval_group' => [
        'name' => [
            'required' => '名稱不能為空',
            'max' => '名稱不能超過50個字符'
        ],
        'code' => [
            'required' => '編碼不能為空', 
            'max' => '編碼不能超過50個字符',
            'unique' => '編碼已存在'
        ],
        'is_inherit' => [
            'required' => '繼承標識不能為空',
            'boolean' => '繼承標識必須是布爾值'
        ],
        'description' => [
            'max' => '描述不能超過200個字符'
        ],
        'status' => [
            'required' => '狀態不能為空',
            'boolean' => '狀態必須是布爾值'
        ],
        'permissions' => [
            'present' => '權限欄位不能為空',
            'array' => '權限必須是陣列格式'
        ],
        'permissions.*.id' => [
            'required' => '權限ID不能為空',
            'string' => '權限ID必須是字符串'
        ],
        'permissions.*.name' => [
            'required' => '權限名稱不能為空',
            'string' => '權限名稱必須是字符串'
        ],
        'permissions.*.description' => [
            'string' => '權限描述必須是字符串',
            'max' => '權限描述不能超過200個字符'
        ],
        'permissions.*.actions' => [
            'required' => '權限操作不能為空',
            'array' => '權限操作必須是陣列格式'
        ],
        'permissions.*.actions.*' => [
            'required' => '操作不能為空',
            'string' => '操作必須是字符串',
            'in' => '操作值無效'
        ],
        'members' => [
            'required' => '成員不能為空',
            'array' => '成員必須是數組'
        ],
        'members.*' => [
            'name' => [
                'required' => '成員名稱不能為空',
                'max' => '成員名稱不能超過50個字符'
            ],
            'position' => [
                'required' => '職位不能為空',
                'max' => '職位不能超過50個字符'
            ],
            'email' => [
                'required' => '郵箱不能為空',
                'email' => '郵箱格式不正確',
                'max' => '郵箱不能超過100個字符'
            ],
            'is_enabled' => [
                'required' => '啟用狀態不能為空',
                'boolean' => '啟用狀態必須是布爾值'
            ]
        ]
    ],
    'update_approval_group' => [
        'name' => [
            'required' => '名稱不能為空',
            'max' => '名稱不能超過50個字符'
        ],
        'code' => [
            'required' => '編碼不能為空',
            'max' => '編碼不能超過50個字符',
            'unique' => '編碼已存在'
        ],
        'is_inherit' => [
            'required' => '繼承標識不能為空',
            'boolean' => '繼承標識必須是布爾值'
        ],
        'description' => [
            'max' => '描述不能超過200個字符'
        ],
        'status' => [
            'required' => '狀態不能為空',
            'boolean' => '狀態必須是布爾值'
        ],
        'permissions' => [
            'array' => '權限必須是數組'
        ],
        'permissions.*' => [
            'id' => [
                'required' => 'ID不能為空',
                'string' => 'ID必須是字符串'
            ],
            'name' => [
                'required' => '名稱不能為空',
                'string' => '名稱必須是字符串'
            ],
            'description' => [
                'string' => '描述必須是字符串',
                'max' => '描述不能超過200個字符'
            ],
            'actions' => [
                'required' => '操作不能為空',
                'array' => '操作必須是數組'
            ],
            'actions.*' => [
                'required' => '操作不能為空',
                'string' => '操作必須是字符串',
                'in' => '操作值無效'
            ]
        ],
        'members' => [
            'required' => '成員不能為空',
            'array' => '成員必須是數組'
        ],
        'members.*' => [
            'name' => [
                'required' => '成員名稱不能為空',
                'max' => '成員名稱不能超過50個字符'
            ],
            'position' => [
                'required' => '職位不能為空',
                'max' => '職位不能超過50個字符'
            ],
            'email' => [
                'required' => '郵箱不能為空',
                'email' => '郵箱格式不正確',
                'max' => '郵箱不能超過100個字符'
            ],
            'is_enabled' => [
                'required' => '啟用狀態不能為空',
                'boolean' => '啟用狀態必須是布爾值'
            ]
        ]
    ],
    'batch_approval_group' => [
        'ids' => 'ID列表',
        'id' => 'ID',
        'ids.required' => 'ID列表不能為空',
        'ids.array' => 'ID列表必須是數組',
        'ids.*.required' => '數組中的每個ID都是必需的',
        'ids.*.exists' => '一個或多個所選ID無效'
    ],
    'store_approval_role' => [
        'name' => [
            'required' => '請輸入角色名稱',
            'string' => '角色名稱必須是字符串',
            'max' => '角色名稱不能超過50個字符',
        ],
        'code' => [
            'required' => '請輸入角色編碼',
            'string' => '角色編碼必須是字符串',
            'max' => '角色編碼不能超過50個字符',
            'unique' => '該角色編碼已被使用',
        ],
        'level' => [
            'required' => '請輸入角色級別',
            'integer' => '角色級別必須是整數',
            'min' => '角色級別不能小於1',
        ],
        'is_inherit' => [
            'required' => '請選擇是否繼承',
            'boolean' => '繼承標識必須是布爾值'
        ],
        'permissions' => [
            'required' => '權限不能為空',
            'array' => '權限必須是數組'
        ],
        'description' => [
            'required' => '請輸入描述',
            'string' => '描述必須是字符串',
            'max' => '描述不能超過255個字符',
        ],
        'status' => [
            'required' => '請選擇狀態',
            'boolean' => '狀態必須是布爾值'
        ]
    ],
    'batch_approval_role' => [
        'ids' => [
            'required' => '請選擇要操作的角色',
            'array' => '角色ID列表格式錯誤',
        ],
        'ids.*' => [
            'required' => '角色ID不能為空',
            'integer' => '角色ID必須是整數',
            'min' => '角色ID必須大於0',
        ],
    ],
    'list_approval_role' => [
        'keyword' => [
            'string' => '關鍵詞必須是字符串',
            'max' => '關鍵詞不能超過50個字符'
        ],
        'status' => [
            'boolean' => '狀態必須是布爾值'
        ],
        'sort_field' => [
            'string' => '排序字段必須是字符串',
            'in' => '排序字段無效'
        ],
        'sort_order' => [
            'string' => '排序方向必須是字符串',
            'in' => '排序方向必須是 asc 或 desc'
        ],
        'per_page' => [
            'integer' => '每頁數量必須是整數',
            'min' => '每頁數量不能小於1',
            'max' => '每頁數量不能大於100'
        ]
    ],
    'update_approval_role' => [
        'name' => [
            'required' => '請輸入角色名稱',
            'string' => '角色名稱必須是字符串',
            'max' => '角色名稱不能超過50個字符',
        ],
        'code' => [
            'required' => '請輸入角色編碼',
            'string' => '角色編碼必須是字符串',
            'max' => '角色編碼不能超過50個字符',
            'unique' => '角色編碼已存在',
        ],
        'level' => [
            'integer' => '角色級別必須是整數',
            'min' => '角色級別不能小於1',
        ],
        'is_inherit' => [
            'boolean' => '繼承標識必須是布爾值'
        ],
        'permissions' => [
            'array' => '權限必須是數組'
        ],
        'description' => [
            'string' => '描述必須是字符串',
            'max' => '描述不能超過255個字符',
        ],
        'status' => [
            'boolean' => '狀態必須是布爾值'
        ]
    ],
    'store_approval_product_flow' => [
        'product_table' => [
            'required' => '產品表名不能為空',
            'string' => '產品表名必須為字符串',
        ],
        'product_id' => [
            'required' => '產品ID不能為空',
            'integer' => '產品ID必須為整數',
            'exists' => '產品不存在',
        ],
        'flow_id' => [
            'required' => '審批流程ID不能為空',
            'integer' => '審批流程ID必須為整數',
            'exists' => '審批流程不存在',
        ],
        'remark' => [
            'string' => '備註必須為字符串',
            'max' => '備註最大長度為500個字符',
        ],
    ],
    'store_approval_product_record' => [
        'action' => [
            'required' => '請選擇審批動作',
            'integer' => '審批動作格式不正確',
            'in' => '無效的審批動作',
        ],
        'comment' => [
            'string' => '審批意見必須是字符串',
            'max' => '審批意見不能超過1000個字符',
        ],
        'attachments' => [
            'array' => '附件必須是數組',
            '_string' => '每個附件必須是字符串',
        ],
    ],
    'error' => [
        // 通用错误码
        'noRelatedTranslation' => '無相關翻譯',
        'userNotFound' => '用戶未找到',
        'duplicateEmail' => '郵箱已存在',
        'userNameMismatch' => '用戶名稱不匹配',

        // 審批流程相關錯誤碼
        'flowNotFound' => '流程未找到',
        'flowAlreadyExists' => '流程已存在',
        'flowCannotBeDeleted' => '流程不能刪除',
        'flowCannotBeUpdated' => '流程不能更新',
        'flowStepNotFound' => '流程步驟未找到',
        'flowStepDataError' => '流程步驟數據錯誤',
        'flowIsNotEnabled' => '流程未打開',

        // 審批步驟相關錯誤碼
        'stepNotFound' => '步驟未找到',
        'stepAlreadyExists' => '步驟已存在',
        'stepCannotBeDeleted' => '步驟不能刪除',
        'stepCannotBeUpdated' => '步驟不能更新',

        // 審批角色相關錯誤碼
        'roleNotFound' => '角色未找到',
        'roleAlreadyExists' => '角色已存在',
        'roleCannotBeDeleted' => '角色不能刪除',
        'roleCannotBeUpdated' => '角色不能更新',
        'roleInUse' => '角色正在使用中',
        'roleCodeExists' => '角色代碼已存在',

        // 審批成員相關錯誤碼
        'memberNotFound' => '成員未找到',
        'memberAlreadyExists' => '成員已存在',
        'memberCannotBeDeleted' => '成員不能刪除',
        'memberCannotBeUpdated' => '成員不能更新',

        // 審批組相關錯誤碼
        'groupNotFound' => '審批組未找到',
        'groupCreateFailed' => '審批組創建失敗',
        'groupUpdateFailed' => '審批組更新失敗',
        'groupDeleteFailed' => '審批組刪除失敗',
        'groupBatchDeleteFailed' => '審批組批量刪除失敗',
        'groupStatusUpdateFailed' => '審批組狀態更新失敗',
        'userNotInApprovalGroup' => '用戶不在審批組中',
        'groupInUse' => '審批組正在使用中',

        // 產品審批相關錯誤碼
        'productFlowNotFound' => '產品流程未找到',
        'productFlowAlreadyExists' => '產品流程已存在',
        'productFlowCannotDelete' => '產品流程不能刪除',
        'productFlowHasRecords' => '產品流程存在審批記錄',
        'productFlowCreateFailed' => '產品流程創建失敗',

        // 審批記錄相關錯誤碼
        'recordNotFound' => '審批記錄未找到',
        'recordCreateFailed' => '審批記錄創建失敗',
        'recordAlreadyApproved' => '審批記錄已審批',
        'logNotFound' => '審批日誌未找到',

        // 審批流程相關錯誤碼
        'flowHasUnfinishedRecords' => '該審批流程存在未完成的審批記錄，無法關閉',
        'flowCannotBeSwitched' => '該審批流程無法切換狀態',
        'recordCannotBeDeleted' => '審批記錄不能刪除',
        'recordDeleteFailed' => '審批記錄刪除失敗',
    ],
    'list_dashboard' => [
        'page' => [
            'integer' => '頁碼必須是整數',
            'min' => '頁碼必須大於等於1',
        ],
        'limit' => [
            'integer' => '每頁數量必須是整數',
            'min' => '每頁數量必須大於等於1',
            'max' => '每頁數量不能超過100',
        ],
        'status' => [
            'string' => '狀態必須是字符串',
            'in' => '無效的狀態值',
        ],
        'keyword' => [
            'string' => '關鍵詞必須是字符串',
        ],
    ],
];
