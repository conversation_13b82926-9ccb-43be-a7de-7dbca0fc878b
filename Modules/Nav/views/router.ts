import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
    {
        path: '/nav',
        component: () => import('/admin/layout/index.vue'),
        meta: { title: '導航管理', icon: 'nav' },
        children: [
            {
                path: 'navigation',
                name: 'navigation',
                meta: { title: '導航列表' },
                component: () => import('./ui/nav/list.vue'),
            },
            {
                path: 'add',
                name: 'add',
                meta: { title: '新增導航' },
                component: () => import('./ui/nav/add.vue'),
            },
        ]
    }
]

export default router