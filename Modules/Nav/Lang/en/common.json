{"list": {"filter": {"button": "Filter", "title": "Article Title", "title_placeholder": "Please enter article title", "status": "Status", "status_placeholder": "Please select status", "status_all": "All Status", "status_enabled": "Enabled", "status_disabled": "Disabled", "reset": "Reset"}, "refresh": "Refresh", "add_nav": "Add Navigation", "empty": "No Data", "table": {"title": "Article Title", "related_pages": "Related Pages", "page_count": "Page Count", "status": "Status", "created_at": "Created Date"}, "operations": {"view": "View", "edit": "Edit", "copy": "Copy", "delete": "Delete"}, "dialog": {"copy_confirm": "Are you sure to copy navigation \"{name}\"?", "copy_title": "Copy Confirmation", "delete_confirm": "Are you sure to delete navigation \"{name}\"? This action cannot be undone.", "delete_title": "Delete Confirmation", "confirm": "Confirm", "cancel": "Cancel"}, "message": {"copy_success": "Copy successful", "copy_failed": "Co<PERSON> failed", "delete_success": "Delete successful", "delete_failed": "Delete failed", "status_enabled": "Enabled", "status_disabled": "Disabled", "status_update_failed": "Status update failed"}}, "pagination": {"page_size": "Items per page", "total": "Total {total} items"}, "add": {"nav_settings": "Navigation Settings", "name": {"label": "Navigation Name", "placeholder": "Home Navigation"}, "description": {"label": "Navigation Description", "placeholder": "Navigation Display"}, "positions": {"label": "Navigation Position", "main": "Main Header Navigation", "footer": "Footer Navigation", "side": "Sidebar"}, "menu_items": {"title": "Available Navigation Items", "pages": {"title": "Pages", "search": "Search pages...", "add": "Add to Navigation"}, "custom_link": {"title": "Custom Link", "url": {"label": "URL", "placeholder": "https://example.com"}, "text": {"label": "Link Text", "placeholder": "Navigation Label"}, "add": "Add to Navigation"}, "categories": {"title": "Categories", "search": "Search categories...", "add": "Add to Navigation"}}, "preview": {"title": "Navigation Structure", "actions": {"edit": "Edit", "remove": "Remove"}}}}