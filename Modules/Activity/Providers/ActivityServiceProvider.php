<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-27 14:43:35
 * @LastEditTime: 2025-06-19 12:01:30
 * @LastEditors: <PERSON>ron
 * @Description:
 */

declare(strict_types=1);

namespace Modules\Activity\Providers;

use Bingo\Providers\BingoModuleServiceProvider;
use Illuminate\Support\Arr;
use Modules\Iam\Enums\MenuType;

class ActivityServiceProvider extends BingoModuleServiceProvider
{

    /**
     * 启动服务
     */
    public function boot(): void
    {
        $path = dirname(__DIR__, 2) . DIRECTORY_SEPARATOR . $this->moduleName() . DIRECTORY_SEPARATOR . 'Lang';
        $this->loadTranslationsFrom($path, $this->moduleName());

        // $this->loadViewsFrom(dirname(__DIR__).DIRECTORY_SEPARATOR.'Views', $this->moduleName());

        $this->registerNavigation();
        $this->registerModulePermissions();
        $this->registerModuleSchedule();
        $this->registerConsole();
    }

    /**
     * 获取模块名称
     */
    public function moduleName(): string
    {
        return 'Activity';
    }

    /**
     * 注册模块设置
     *
     * @return array<string, string>
     */
    public function registerSettings(): array
    {
        return [];
    }


    /**
     * 注册计划任务
     *
     * @return array<string, array>
     */
    public function registerSchedule(): array
    {
        return [
            // 活动状态更新任务
            'activity.status_update' => [
                'name' => T('Activity::schedule.activity_status_update'),
                'command' => 'activity:status:update',
                'spec' => '*/10 * * * *', // 每10分钟执行
                'description' => T('Activity::schedule.activity_status_update_desc'),
                'type' => 'artisan',
                'status' => 'waiting',
                'platform' => PHP_OS_FAMILY === 'Windows' ? 'windows' : 'linux',
                'creator_id' => 1,
                'lang' => config('app.locale', 'zh_CN')
            ],
        ];
    }

    /**
     * 注册控制台命令
     * @return void
     */
    protected function registerConsole(): void
    {
        $this->commands([
            \Modules\Activity\Commands\ActivityStatusUpdateCommand::class,
        ]);
    }

    /**
     * 注册导航
     *
     * @return array<string, mixed>
     */
    protected function navigation(): array
    {
        return [
            [
                "key" => "activity",
                "parent" => "application",
                "nav_name" => T("Activity::nav.activity"),
                "path" => "/activity",
                "icon" => "Nav/Asset/menu_icon/activity.png",
                "order" => 1,
                "children" => [
                    [
                        "key" => "activity_list",
                        "parent" => "activity",
                        "nav_name" => T("Activity::nav.activity_list"),
                        "path" => "/activity/list",
                        "icon" => "List",
                        "order" => 1
                    ],
                    [
                        "key" => "activity_calendar",
                        "parent" => "activity",
                        "nav_name" => T("Activity::nav.activity_calendar"),
                        "path" => "/activity/calendar",
                        "icon" => "Calendar",
                        "order" => 2
                    ],
                    // [
                    //     "key" => "activity_groups",
                    //     "parent" => "activity",
                    //     "nav_name" => T("Activity::nav.activity_groups"),
                    //     "path" => "/activity/groups",
                    //     "icon" => "Collection",
                    //     "order" => 2,
                    //     'children' => [
                    //         [
                    //             "key" => "activity_invitation",
                    //             "parent" => "activity_groups",
                    //             "nav_name" => T("Activity::nav.activity_invitation"),
                    //             "path" => "/activity/invitation",
                    //             "icon" => "",
                    //             "order" => 1
                    //         ],
                    //         [
                    //             "key" => "activity_participant_list",
                    //             "parent" => "activity_groups",
                    //             "nav_name" => T("Activity::nav.activity_participant_list"),
                    //             "path" => "/activity/participant/list",
                    //             "icon" => "",
                    //             "order" => 2
                    //         ],
                    //     ]
                    // ]
                    // [
                    //     "key" => "activity_settings_ticket",
                    //     "parent" => "activity",
                    //     "nav_name" => T("Activity::nav.activity_settings_ticket"),
                    //     "path" => "/activity/settings/ticket",
                    //     "icon" => "Tickets",
                    //     "order" => 1
                    // ],

                    // [
                    //     "key" => "activity_admin_remind",
                    //     "parent" => "activity",
                    //     "nav_name" => T("Activity::nav.activity_admin_remind"),
                    //     "path" => "/activity/admin/remind",
                    //     "icon" => "Bell",
                    //     "order" => 3
                    // ],

                    // [
                    //     "key" => "activity_email_template",
                    //     "parent" => "activity",
                    //     "nav_name" => T("Activity::nav.activity_email_template"),
                    //     "path" => "/activity/email/template",
                    //     "icon" => "Document",
                    //     "order" => 2
                    // ],
                    // [
                    //     "key" => "activity_forms",
                    //     "parent" => "activity",
                    //     "nav_name" => T("Activity::nav.activity_forms"),
                    //     "path" => "/activity/forms",
                    //     "icon" => "Document",
                    //     "order" => 3
                    // ],
                    [
                        "key" => "activity_rule_template",
                        "parent" => "activity",
                        "nav_name" => T("Activity::nav.activity_rule_template"),
                        "path" => "/activity/rule/template",
                        "icon" => "Document",
                        "order" => 3
                    ],
                ]
            ]

        ];
    }

    /**
     * 权限设置
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function registerPermissions(): array
    {
        $admin = [
            'activity' => [
                'permission_name' => T("Activity::permission.activity"),
                'route' => '/activity',
                'parent_id' => 0,
                'permission_mark' => 'activity',
                'component' => '/admin/layout/index.vue',
                'type' => MenuType::Top->value(),
                'sort' => 1,
                'children' => [
                    [
                        'permission_name' => T("Activity::permission.activity_list"),
                        'route' => 'activity_list',
                        'parent_id' => 'activity_list',
                        'permission_mark' => 'activity_list',
                        'component' => '/admin/views/AMisPage.vue',
                        'type' => MenuType::Menu->value(),
                        'sort' => 1,
                        'actions' => [
                            [
                                'permission_name' => T("Activity::permission.activity_list"),
                                'route' => '',
                                'permission_mark' => 'activity_list@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 1,
                                'parent_id' => 'activity_list'
                            ]
                        ],
                    ],
                ],
            ],
        ];
        $frontend = [];
        return array_merge(["admin" => $admin], ["frontend" => $frontend]);
    }
}
