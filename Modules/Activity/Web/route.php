<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| 活动模块的Web路由
|
*/

Route::group(['prefix' => 'activity'], function () {
    // 活动列表页
    Route::get('/', 'ActivityController@index');
    
    // 活动详情页
    Route::get('/{id}', 'ActivityController@show');
    
    // 活动报名页
    Route::get('/{id}/register', 'ActivityController@registerForm');
    Route::post('/{id}/register', 'ActivityController@register');
    
    // 我的活动页
    Route::get('/my', 'ActivityController@myActivities');
}); 