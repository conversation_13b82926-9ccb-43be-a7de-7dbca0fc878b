<?php

declare(strict_types=1);

namespace Modules\Activity\Commands;

use Illuminate\Console\Command;
use Modules\Activity\Services\ActivityStatusService;
use Modules\Activity\Enums\ActivityErrorCode;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Bingo\Exceptions\BizException;

class ActivityStatusUpdateCommand extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'activity:status:update {--force}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '检测活动状态并自动更新：已创建->已发布，已发布->已完成';

    /**
     * 锁定键名
     */
    private const LOCK_KEY = 'activity:status:update:lock';

    /**
     * 锁定超时时间（秒）
     */
    private const LOCK_TIMEOUT = 3600;

    /**
     * 活动状态服务
     *
     * @var ActivityStatusService
     */
    private ActivityStatusService $activityStatusService;

    /**
     * 构造函数
     *
     * @param ActivityStatusService $activityStatusService
     */
    public function __construct(ActivityStatusService $activityStatusService)
    {
        parent::__construct();
        $this->activityStatusService = $activityStatusService;
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle(): int
    {
        if (!$this->acquireLock(self::LOCK_KEY)) {
            $this->error('另一个实例正在运行中');
            return self::FAILURE;
        }

        try {
            $startTime = microtime(true);
            $this->info('开始检测活动状态...');
            
            $result = $this->activityStatusService->updateActivityStatuses();
            
            $executionTime = round(microtime(true) - $startTime, 2);
            
            // 显示详细的更新统计
            $this->info("活动状态更新完成:");
            $this->info("  - 已创建 -> 已发布: {$result['published']} 个活动");
            $this->info("  - 已发布 -> 已完成: {$result['completed']} 个活动");
            $this->info("  - 总计更新: {$result['total']} 个活动");
            $this->info("  - 执行耗时: {$executionTime} 秒");
            
            return self::SUCCESS;
        } catch (BizException $e) {
            $this->logError('活动状态更新失败', [
                'error' => $e->getMessage()
            ]);
            return self::FAILURE;
        } catch (\Exception $e) {
            $this->logError('活动状态更新失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return self::FAILURE;
        } finally {
            $this->releaseLock(self::LOCK_KEY);
        }
    }

    /**
     * 获取锁
     *
     * @param string $key
     * @return bool
     */
    private function acquireLock(string $key): bool
    {
        if ($this->option('force')) {
            Cache::forget($key);
            return true;
        }
        return Cache::add($key, true, self::LOCK_TIMEOUT);
    }

    /**
     * 释放锁
     *
     * @param string $key
     * @return void
     */
    private function releaseLock(string $key): void
    {
        Cache::forget($key);
    }

    /**
     * 记录错误日志
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    private function logError(string $message, array $context = []): void
    {
        Log::error($message, $context);
        $this->error($message);
    }
} 