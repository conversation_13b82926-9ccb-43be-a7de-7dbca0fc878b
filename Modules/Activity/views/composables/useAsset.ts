import { getCurrentInstance } from 'vue'

/**
 * 获取资源路径的组合式函数
 * 
 * 这个函数解决了在Vue 3 setup语法中使用全局属性$asset的问题
 * 提供了后备方案，确保即使$asset函数不可用时也能正常工作
 * 
 * @example
 * ```typescript
 * import { useAsset } from '@/composables/useAsset'
 * 
 * const asset = useAsset()
 * const iconPath = asset('Cms/Asset/EditIcon.png')
 * ```
 */
export function useAsset() {
  const instance = getCurrentInstance()
  const $asset = instance?.appContext.config.globalProperties.$asset
  
  return (path: string): string => {
    if ($asset && typeof $asset === 'function') {
      return $asset(path)
    }
    
    // 后备方案：直接使用Vendor路径
    // 这与$asset函数的实现逻辑一致
    return `/Vendor/${path}`
  }
}

/**
 * 获取常用图标路径的组合式函数
 * 
 * @example
 * ```typescript
 * import { useCommonIcons } from '@/composables/useAsset'
 * 
 * const icons = useCommonIcons()
 * // icons.edit, icons.delete, icons.copy, icons.view
 * ```
 */
export function useCommonIcons() {
  const asset = useAsset()
  
  return {
    edit: asset('Cms/Asset/EditIcon.png'),
    delete: asset('Cms/Asset/DeleteIcon.png'),
    copy: asset('Cms/Asset/CopyDocumentIcon.png'),
    view: asset('Cms/Asset/view-icon.png'),
    plus: asset('Cms/Asset/PlusIcon.png'),
    noPhoto: asset('Cms/Asset/no-photo.png'),
  }
} 