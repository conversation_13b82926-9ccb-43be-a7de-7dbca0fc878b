// 活动类型
export interface IEvent {
  id: number
  title: string
  category_str: string
  localized_id: number
  start_time: string | null
  end_time: string | null
  publish_time: string
  ticket_delivery_method_str: string
  status: 'created' | 'published' | 'finished' | 'cancelled'
  status_str: string
  created_at: string
  updated_at: string
  // 兼容旧字段
  name?: string
  code?: string
  type?: 'online' | 'offline'
  startTime?: string
  endTime?: string
  currentParticipants?: number
  maxParticipants?: number
  description?: string
  location?: string
  platform?: string
  meetingUrl?: string
  organizerId?: number
  createdAt?: string
  updatedAt?: string
}

// 票务类型
export interface ITicket {
  id: number
  eventId: number
  name: string
  code: string
  price: number
  quantity: number
  soldQuantity: number
  type: 'normal' | 'vip' | 'free'
  startTime: string
  endTime: string
  description?: string
  createdAt: string
  updatedAt: string
}

// 参与者类型
export interface IParticipant {
  id: number | string
  eventId?: number
  userId?: number
  name: string
  company: string
  position: string
  email: string
  phone?: string
  ticketId?: number
  ticketCode?: string
  status: string  // 使用字符串类型，兼容更多状态值
  participantPath?: string
  checkedIn?: boolean
  checkedInTime?: string
  createdAt?: string
  updatedAt?: string
  createTime?: string // 兼容性字段
  [key: string]: any
}

// 等待名单类型
export interface IWaitingList {
  id: number
  eventId: number
  userId: number
  name: string
  email: string
  phone?: string
  position: number
  status: 'waiting' | 'approved' | 'rejected'
  remark?: string
  reviewRemark?: string
  reviewedAt?: string
  reviewer?: {
    id: number
    name: string
  }
  event?: IEvent
  createdAt: string
  updatedAt: string
}

// 邀请类型
export interface IInvitation {
  id: number
  eventId: number
  email: string
  name: string
  code: string
  status: 'pending' | 'accepted' | 'expired'
  expiredAt: string
  createdAt: string
  updatedAt: string
}

// 规则模板类型
export interface IRuleTemplate {
  id: number
  name: string
  code: string
  type: 'online' | 'offline' | 'both'
  settings: {
    registration?: {
      requireApproval: boolean
      allowWaitingList: boolean
      waitingListMode: 'auto' | 'manual'
    }
    ticket?: {
      types: Array<{
        name: string
        price: number
        quantity: number
      }>
    }
    notification?: {
      enableEmail: boolean
      enableSMS: boolean
      templates: Array<{
        type: string
        subject: string
        content: string
      }>
    }
  }
  createdAt: string
  updatedAt: string
}

// 分页请求参数
export interface IPageParams {
  page: number
  per_page: number
  keyword?: string
  status?: string  // 活动状态过滤参数
  [key: string]: any
}

// 分页响应数据
export interface IPageData<T> {
  list: T[]
  total: number
  page: number
  per_page: number
}

export interface IRuleTemplate {
  id: number
  name: string
  scene: string
  status: number
  updateTime: string
  [key: string]: any
}

export interface IParticipantAction {
  id?: number | string
  participantId?: number | string
  time: string
  type: string  // 'invite', 'confirm', 'reject', 'register'
  desc: string
  operationBy?: string
  operationType?: string
  ip?: string
  createdAt?: string
  updatedAt?: string
  [key: string]: any
}

export interface IParticipantInvitation {
  id?: number | string
  participantId?: number | string
  sender: string
  senderEmail: string
  confirmTime: string
  status: string
  expiredAt?: string
  createdAt?: string
  updatedAt?: string
  [key: string]: any
}

// 邮件模板类型
export interface IEmailTemplate {
  id: number | string
  name: string
  scene: string  // 使用场景
  subject: string  // 邮件主题
  content: string  // 邮件内容
  status: number  // 0: 禁用, 1: 启用
  updateTime: string
  createdAt?: string
  updatedAt?: string
  [key: string]: any
}

// 邮件模板列表请求参数
export interface IEmailTemplateParams {
  page: number
  per_page: number
  keyword?: string
  status?: number
  scene?: string
}

// 群组
export interface IGroup {
  name: string
  code: string
  max_members: string
  can_register: string
  start_time: string
  end_time: string
}