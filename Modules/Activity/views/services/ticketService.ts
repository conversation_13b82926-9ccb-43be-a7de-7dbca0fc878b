import http from '/admin/support/http'
import type { IPageParams } from '../types'

const baseUrl = ''

// 票务列表查询参数接口
interface TicketListParams {
  page?: number
  per_page?: number
}

export const ticketService = {
  
  // 获取票务类型详情
  getDetail(id: number) {
    return http.get(`${baseUrl}/activity/ticket/${id}`)
  },

  // 获取票务列表（全局）
  getList(params: IPageParams) {
    return http.get(`${baseUrl}/activity/ticket`, { ...params })
  },

  // 获取指定活动的票务列表
  getListByActivity(activityId: string | number, params?: TicketListParams) {
    return http.get(`${baseUrl}/activity/ticket/activity/${activityId}`, { params })
  },

  // 创建票务
  create(eventId: number, data: any) {
    return http.post(`${baseUrl}/activity/ticket/store`, data)
  },

  // 更新票务
  update(eventId: number, ticketId: number, data: any) {
    return http.post(`${baseUrl}/activity/ticket/${ticketId}`, data)
  },

  // 删除票务
  delete(ticketId: number) {
    return http.delete(`${baseUrl}/activity/ticket/${ticketId}`)
  }
}