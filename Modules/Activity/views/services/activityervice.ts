import http from '/admin/support/http'
import type { IEvent, IPageParams, IPageData, IWaitingList, IGroup } from '../types'


export const activityervice = {
  // 获取活动列表
  getList(params: IPageParams) {
    return http.get(`/activity`, { ...params })
  },

  // 获取活动详情
  getDetail(id: number) {
    return http.get(`/activity/${id}`)
  },

  // 创建活动
  create(data: Partial<IEvent>) {
    return http.post(`/activity/store`, data)
  },

  // 更新活动
  update(id: number, data: Partial<IEvent>) {
    return http.post(`/activity/save/${id}`, data)
  },

  // 删除活动
  delete(id: number) {
    return http.delete(`/activity/${id}`)
  },

  // 获取活动 配置信息
  getActivityType() {
    return http.get(`/activity/config`)
  },

  // 获取活动日历数据
  getCalendar(start_date: string, end_date: string) {
    return http.get(`/activity/calendar/?start_date=${start_date}&end_date=${end_date}`)
  },
  // 复制活动
  copy(id: number) {
    return http.post(`/activity/copy/${id}`)
  }
}

// 群组
export const groupService = {
  // 获取群组板列表
  getList(params: IPageParams) {
    return http.get(`/activity/group`, { ...params })
  },

  // 创建群组模板
  create(data: IGroup) {
    return http.post(`/activity/group/store`, data)
  },

  // 更新群组模板
  update(id: number, data: any) {
    return http.post(`/activity/group/${id}`, data)
  },
  getGroup(id:number){
   return http.get(`/activity/group/${id}`)
  }
}

// 票务服务
export const ticketService = {
  // 获取票务列表
  getList(params: IPageParams) {
    return http.get(`/activity/tickets`, { ...params })
  },

  // 获取票务详情
  getDetail(ticketId: number) {
    return http.get(`/activity/ticket/${ticketId}`)
  },

  getDetailByActivity(ticketId: number,activity_id: number) {
    return http.get(`/activity/ticket/${activity_id}/${ticketId}`);
  },


  // 创建票务
  create(eventId: number, data: any) {
        return http.post(`/activity/ticket/store`, data)
  },

  // 更新票务
  update(eventId: number, ticketId: number, data: any) {
    return http.post(`/activity/ticket/${ticketId}`, data)
  },

  // 删除票务
  delete(eventId: number, ticketId: number) {
    return http.delete(`/activity/ticket/${ticketId}`)
  }
}

// 参与者服务
export const participantService = {
  // 获取参与者列表
  getList(eventId: number, params: IPageParams) {
    return http.get(`/activity/${eventId}/participants`, { params })
  },

  // 更新参与者状态
  updateStatus(eventId: number, participantId: number, status: string) {
    return http.put(`/activity/${eventId}/participants/${participantId}/status`, { status })
  },

  // 签到
  checkIn(eventId: number, participantId: number) {
    return http.post(`/activity/${eventId}/participants/${participantId}/check-in`)
  }
}

// 等待名单服务
export const waitingListService = {
  // 获取等待名单
  getList(eventId: number, params: IPageParams) {
    return http.get(`/activity/${eventId}/waiting-list`, { params })
  },

  // 获取等待名单详情
  getDetail(id: number) {
    return http.get(`/waiting-list/${id}`)
  },

  // 审核等待名单
  review(eventId: number, waitingId: number, status: 'approved' | 'rejected', reviewRemark?: string) {
    return http.post(`/activity/${eventId}/waiting-list/${waitingId}/review`, {
      status,
      reviewRemark
    })
  }
}

// 邀请服务
export const invitationService = {
  // 获取邀请列表
  getList(eventId: number, params: IPageParams) {
    return http.get(`/activity/${eventId}/invitations`, { params })
  },

  // 创建邀请
  create(eventId: number, data: any) {
    return http.post(`/activity/${eventId}/invitations`, data)
  },

  // 重新发送邀请
  resend(eventId: number, invitationId: number) {
    return http.post(`/activity/${eventId}/invitations/${invitationId}/resend`)
  }
}

// 规则模板服务
export const ruleTemplateService = {
  // 获取规则模板列表
  getList(params: IPageParams) {
    return http.get(`/rule-templates`, { params })
  },

  // 创建规则模板
  create(data: any) {
    return http.post(`/rule-templates`, data)
  },

  // 更新规则模板
  update(id: number, data: any) {
    return http.put(`/rule-templates/${id}`, data)
  },

  // 删除规则模板
  delete(id: number) {
    return http.delete(`/rule-templates/${id}`)
  }
} 

