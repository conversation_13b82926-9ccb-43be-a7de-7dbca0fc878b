import http from '/admin/support/http'

/**
 * 邀请服务接口
 */
export interface InvitationListParams {
  activity_id: string | number
  page?: number
  per_page?: number
  search?: string
}

export interface InvitationCreateData {
  activity_id: string | number
  title: string
  description?: string
  is_default?: number
  is_group?: number
  group_type?: string
  members?: any[]
  [key: string]: any
}

export interface InvitationUpdateData {
  activity_id?: string | number
  title?: string
  description?: string
  is_default?: number
  is_group?: number
  group_type?: string
  members?: any[]
  [key: string]: any
}

/**
 * 成员列表查询参数
 */
export interface MemberListParams {
  page?: number
  per_page?: number
  name?: string
  email?: string
  phone?: string
  status?: number
  sort_field?: string
  sort_order?: 'asc' | 'desc'
}

/**
 * 成员数据
 */
export interface MemberData {
  activity_id: string | number
  name: string
  phone: string
  email: string
  company: string
  position?: string
}

/**
 * 成员详情
 */
export interface MemberDetail {
  id: number
  name: string
  phone: string
  email: string
  company: string
  position: string
  status: number
  created_at: string
  logs?: any[]
}

export const invitationService = {
  /**
   * 获取邀请列表
   * @param params 查询参数
   */
  getList: (params: InvitationListParams) => {
    return http.get('/activity/invitation', { params })
  },

  /**
   * 创建邀请
   * @param data 邀请数据
   */
  create: (data: InvitationCreateData) => {
    return http.post('/activity/invitation/store', data)
  },

  /**
   * 更新邀请
   * @param id 邀请ID
   * @param data 更新数据
   */
  update: (id: number | string, data: InvitationUpdateData) => {
    return http.post(`/activity/invitation/${id}`, data)
  },

  /**
   * 删除邀请
   * @param id 邀请ID
   */
  delete: (id: number | string) => {
    return http.delete(`/activity/invitation/${id}`)
  },

  /**
   * 获取邀请详情
   * @param id 邀请ID
   */
  getDetail: (id: number | string) => {
    return http.get(`/activity/invitation/${id}`)
  },

  /**
   * 复制邀请
   * @param id 邀请ID
   */
  copy: (id: number | string) => {
    return http.post(`/activity/invitation/copy/${id}`)
  },

  /**
   * 批量删除邀请
   * @param ids 邀请ID数组
   */
  batchDelete: (ids: (number | string)[]) => {
    return http.post('/activity/invitation/batch-delete', { ids })
  },

  /**
   * 发送邀请
   * @param id 邀请ID
   */
  send: (id: number | string) => {
    return http.post(`/activity/invitation/send/${id}`)
  },

  // ========== 成员管理相关接口 ==========

  /**
   * 获取邀请成员列表
   * @param invitationId 邀请ID
   * @param params 查询参数
   */
  getMemberList: (invitationId: string | number, params?: MemberListParams) => {
    return http.get(`/activity/invitation/${invitationId}/members`, { params })
  },

  /**
   * 创建邀请成员
   * @param invitationId 邀请ID
   * @param data 成员数据
   */
  createMember: (invitationId: string | number, data: MemberData) => {
    return http.post(`/activity/invitation/${invitationId}/members/store`, data)
  },

  /**
   * 更新邀请成员
   * @param invitationId 邀请ID
   * @param memberId 成员ID
   * @param data 成员数据
   */
  updateMember: (invitationId: string | number, memberId: string | number, data: MemberData) => {
    return http.post(`/activity/invitation/${invitationId}/members/${memberId}`, data)
  },

  /**
   * 获取成员详情
   * @param invitationId 邀请ID
   * @param memberId 成员ID
   */
  getMemberDetail: (invitationId: string | number, memberId: string | number) => {
    return http.get(`/activity/invitation/${invitationId}/members/${memberId}`)
  },

  /**
   * 删除邀请成员
   * @param memberId 成员ID
   */
  deleteMember: (memberId: string | number) => {
    return http.delete(`/activity/invitation/members/${memberId}`)
  },

  /**
   * 下载成员上传模板
   */
  downloadTemplate: () => {
    return http.get('/activity/invitation/export-template', {
      responseType: 'blob'
    })
  },

  /**
   * 导入成员
   * @param invitationId 邀请ID
   * @param file 文件
   */
  importMembers: (activityId: string | number, file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return http.post(`/activity/invitation/import-members/${activityId}`, formData)
  },

  /**
   * 导出成员列表
   * @param invitationId 邀请ID
   */
  exportMembers: (invitationId: string | number) => {
    return http.get(`/activity/invitation/export-members/${invitationId}`, {
      responseType: 'blob'
    })
  }
} 