import http from '/admin/support/http'

// 提醒类型定义
export interface ReminderRecipient {
  email: string
  phone: string
}

export interface ReminderSettings {
  registration_deadline: boolean
  participant_limit: boolean
  activity_start_end: boolean
  ticket_overdue: boolean
}

export interface CreateReminderRequest {
  name: string
  type: string[]
  recipients: ReminderRecipient[]
  activity_id: number | string
  settings: ReminderSettings
  status: number
}

export interface UpdateReminderRequest extends Partial<CreateReminderRequest> {}

export interface ReminderListParams {
  activity_id: number | string
  page?: number
  per_page?: number
}

export interface ReminderItem {
  id: number
  name: string
  type: string[]
  recipients: ReminderRecipient[]
  activity_id: number
  settings: ReminderSettings
  status: number
  created_at: string
  updated_at: string
}

export interface ReminderListResponse {
  data: ReminderItem[]
  total: number
  current_page: number
  per_page: number
  last_page: number
}

export const reminderService = {
  // 获取提醒列表
  getList: (params: ReminderListParams) => 
    http.get('/activity/reminders', { params }),
  
  // 创建提醒
  create: (data: CreateReminderRequest) => 
    http.post('/activity/reminders', data),
  
  // 更新提醒
  update: (id: number | string, data: UpdateReminderRequest) => 
    http.put(`/activity/reminders/${id}`, data),
  
  // 删除提醒
  delete: (id: number | string) => 
    http.delete(`/activity/reminders/${id}`),
  
  // 获取提醒详情
  getDetail: (id: number | string) => 
    http.get(`/activity/reminders/${id}`),
  
  // 更新状态 (通过更新整个对象)
  updateStatus: (id: number | string, status: number) => 
    http.put(`/activity/reminders/${id}`, { status }),
  
  // 批量删除 (如果需要)
  batchDelete: (ids: (number | string)[]) => 
    http.delete('/activity/reminders/batch', { data: { ids } })
} 