import http from '/admin/support/http'
import type { IPageParams, IPageData, IRuleTemplate } from '../types/index'

export const ruleTemplateService = {
  // 获取列表
  getList(params: IPageParams): Promise<any> {
    return http.get('/activity/rule', { params })
  },

  // 获取详情
  getDetail(id: number) {
    return http.get(`/activity/rule/${id}`)
  },

  // 创建模版
  create(data: any) {
    return http.post('/activity/rule/store', data)
  },

  // 更新模版
  update(id: number, data: any) {
    return http.post(`/activity/rule/${id}`, data)
  },

  // 删除模版
  delete(id: number) {
    return http.delete(`/activity/rule/${id}`)
  },

  // 复制模版
  copy(id: number) {
    return http.post(`/activity/rule/${id}/copy`)
  },

  // 批量复制
  batchCopy(ids: number[]) {
    return http.post('/activity/rule/batch-copy', { ids })
  },

  // 更新状态
  updateStatus(id: number, status: number) {
    return http.put(`/activity/rule/${id}/status`, { status })
  },

  // 获取邮件模板列表
  getEmailTemplates() {
    return http.get(`/edm/templates/type/7`)
  }
} 