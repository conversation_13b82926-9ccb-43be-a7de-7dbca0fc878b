import http from '/admin/support/http'

/**
 * 参与者列表查询参数
 */
export interface ParticipantListParams {
  activity_id: string | number
  per_page?: number
  page?: number
  status?: string
  notification_type?: string
  search?: string
}

/**
 * 参与者创建数据
 */
export interface ParticipantCreateData {
  activity_id: string | number
  name: string
  email: string
  company?: string
  position?: string
  participant_path?: string
  [key: string]: any
}

/**
 * 参与者更新数据
 */
export interface ParticipantUpdateData {
  name?: string
  email?: string
  company?: string
  position?: string
  participant_path?: string
  [key: string]: any
}

export const participantService = {
  /**
   * 获取参与者列表
   * @param params 查询参数
   */
  getList: (params: ParticipantListParams) => {
    return http.get('/activity/participants', { params })
  },

  /**
   * 获取参与者详情
   * @param id 参与者ID
   */
  getDetail: (id: number | string) => {
    return http.get(`/activity/participants/${id}`)
  },

  /**
   * 获取参与者统计数据
   * @param activityId 活动ID
   */
  getStats: (activityId: number | string) => {
    return http.get(`/activity/participants/stats/${activityId}`)
  },

  /**
   * 删除参与者
   * @param id 参与者ID
   */
  delete: (id: number | string) => {
    return http.delete(`/activity/participants/${id}`)
  },

  /**
   * 创建参与者
   * @param data 参与者数据
   */
  create: (data: ParticipantCreateData) => {
    return http.post('/activity/participants', data)
  },
  

  /**
   * 更新参与者
   * @param id 参与者ID
   * @param data 更新数据
   */
  update: (id: number | string, data: ParticipantUpdateData) => {
    return http.put(`/activity/participants/${id}`, data)
  },

  confirm: (id: number | string) => http.post(`/api/participants/${id}/confirm`),
  reject: (id: number | string) => http.post(`/api/participants/${id}/reject`),
  batchConfirm: (ids: (number | string)[]) => http.post('/api/participants/batch-confirm', { ids }),
  batchReject: (ids: (number | string)[]) => http.post('/api/participants/batch-reject', { ids }),
  export: () => http.get('/api/participants/export', { responseType: 'blob' }),
  updateStatus: (id: number | string, status: string) => http.put(`/api/participants/${id}/status`, { status }),
  batchUpdateStatus: (ids: (number | string)[], status: string) => http.post('/api/participants/batch-update-status', { ids, status }),
  resendInvitation: (id: number | string) => http.post(`/api/participants/${id}/resend-invitation`),
  addToBlacklist: (id: number | string) => http.post(`/api/participants/${id}/blacklist`),
  getActionHistory: (id: number | string) => http.get(`/api/participants/${id}/actions`)
} 