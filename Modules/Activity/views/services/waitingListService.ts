import http from '/admin/support/http'

export const waitingListService = {
  getList: (params: any) => http.get('/api/waiting-list', params),
  getStats: (params: any) => http.get('/api/waiting-list/stats', params),
  approve: (id: number | string) => http.post(`/api/waiting-list/${id}/approve`),
  reject: (id: number | string) => http.post(`/api/waiting-list/${id}/reject`),
  delete: (id: number | string) => http.delete(`/api/waiting-list/${id}`),
  create: (data: any) => http.post('/api/waiting-list', data),
  update: (id: number | string, data: any) => http.put(`/api/waiting-list/${id}`, data),
  getDetail: (id: number | string) => http.get(`/api/waiting-list/${id}`),
  batchApprove: (ids: (number | string)[]) => http.post('/api/waiting-list/batch-approve', { ids }),
  batchReject: (ids: (number | string)[]) => http.post('/api/waiting-list/batch-reject', { ids }),
  updatePriority: (id: number | string, priority: number) => http.put(`/api/waiting-list/${id}/priority`, { priority })
}