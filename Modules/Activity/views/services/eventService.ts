import http from '/admin/support/http'
import type { IEvent, IPageParams, IPageData, IWaitingList } from '../types'

const baseUrl = ''

export const eventService = {
  // 获取活动列表
  getList(params: IPageParams) {
    return http.get<IPageData<IEvent>>('/activity', { params })
  },

  // 获取活动详情
  getDetail(id: number) {
    return http.get<IEvent>(`${baseUrl}/activity/${id}`)
  },

  // 创建活动
  create(data: Partial<IEvent>) {
    return http.post<IEvent>(`${baseUrl}/activity`, data)
  },

  // 更新活动
  update(id: number, data: Partial<IEvent>) {
    return http.put<IEvent>(`${baseUrl}/activity/${id}`, data)
  },

  // 删除活动
  delete(id: number) {
    return http.delete(`${baseUrl}/activity/${id}`)
  }
}

// 票务服务
export const ticketService = {
  // 获取票务列表
  getList(eventId: number, params: IPageParams) {
    return http.get(`${baseUrl}/events/${eventId}/tickets`, { params })
  },

  // 创建票务
  create(eventId: number, data: any) {
    return http.post(`${baseUrl}/events/${eventId}/tickets`, data)
  },

  // 更新票务
  update(eventId: number, ticketId: number, data: any) {
    return http.put(`${baseUrl}/events/${eventId}/tickets/${ticketId}`, data)
  },

  // 删除票务
  delete(eventId: number, ticketId: number) {
    return http.delete(`${baseUrl}/events/${eventId}/tickets/${ticketId}`)
  }
}

// 参与者服务
export const participantService = {
  // 获取参与者列表
  getList(eventId: number, params: IPageParams) {
    return http.get(`${baseUrl}/events/${eventId}/participants`, { params })
  },

  // 更新参与者状态
  updateStatus(eventId: number, participantId: number, status: string) {
    return http.put(`${baseUrl}/events/${eventId}/participants/${participantId}/status`, { status })
  },

  // 签到
  checkIn(eventId: number, participantId: number) {
    return http.post(`${baseUrl}/events/${eventId}/participants/${participantId}/check-in`)
  }
}

// 等待名单服务
export const waitingListService = {
  // 获取等待名单
  getList(eventId: number, params: IPageParams) {
    return http.get<IPageData<IWaitingList>>(`${baseUrl}/events/${eventId}/waiting-list`, { params })
  },

  // 获取等待名单详情
  getDetail(id: number) {
    return http.get<IWaitingList>(`${baseUrl}/waiting-list/${id}`)
  },

  // 审核等待名单
  review(eventId: number, waitingId: number, status: 'approved' | 'rejected', reviewRemark?: string) {
    return http.post(`${baseUrl}/events/${eventId}/waiting-list/${waitingId}/review`, {
      status,
      reviewRemark
    })
  }
}

// 邀请服务
export const invitationService = {
  // 获取邀请列表
  getList(eventId: number, params: IPageParams) {
    return http.get(`${baseUrl}/events/${eventId}/invitations`, { params })
  },

  // 创建邀请
  create(eventId: number, data: any) {
    return http.post(`${baseUrl}/events/${eventId}/invitations`, data)
  },

  // 重新发送邀请
  resend(eventId: number, invitationId: number) {
    return http.post(`${baseUrl}/events/${eventId}/invitations/${invitationId}/resend`)
  }
}

// 规则模板服务
export const ruleTemplateService = {
  // 获取规则模板列表
  getList(params: IPageParams) {
    return http.get(`${baseUrl}/rule-templates`, { params })
  },

  // 创建规则模板
  create(data: any) {
    return http.post(`${baseUrl}/rule-templates`, data)
  },

  // 更新规则模板
  update(id: number, data: any) {
    return http.put(`${baseUrl}/rule-templates/${id}`, data)
  },

  // 删除规则模板
  delete(id: number) {
    return http.delete(`${baseUrl}/rule-templates/${id}`)
  }
} 