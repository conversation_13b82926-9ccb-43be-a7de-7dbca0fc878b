import http from '/admin/support/http'
import type { IEmailTemplate, IEmailTemplateParams } from '../types'

export const emailTemplateService = {
  // 获取邮件模板列表
  getList: (params: IEmailTemplateParams) => http.get('/api/email-templates', { params }),
  
  // 获取邮件模板详情
  getDetail: (id: number | string) => http.get(`/api/email-templates/${id}`),
  
  // 创建邮件模板
  create: (data: Partial<IEmailTemplate>) => http.post('/api/email-templates', data),
  
  // 更新邮件模板
  update: (id: number | string, data: Partial<IEmailTemplate>) => http.put(`/api/email-templates/${id}`, data),
  
  // 删除邮件模板
  delete: (id: number | string) => http.delete(`/api/email-templates/${id}`),
  
  // 更新邮件模板状态
  updateStatus: (id: number | string, status: number) => http.put(`/api/email-templates/${id}/status`, { status }),
  
  // 复制邮件模板
  copy: (id: number | string) => http.post(`/api/email-templates/${id}/copy`),
  
  // 批量复制邮件模板
  batchCopy: (ids: (number | string)[]) => http.post('/api/email-templates/batch-copy', { ids }),
  
  // 预览邮件模板
  preview: (id: number | string) => http.get(`/api/email-templates/${id}/preview`),
  
  // 发送测试邮件
  sendTest: (id: number | string, email: string) => http.post(`/api/email-templates/${id}/send-test`, { email })
} 