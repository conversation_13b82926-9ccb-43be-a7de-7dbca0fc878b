import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
  {
    path: '/activity',
    component: () => import('/admin/layout/index.vue'),
    meta: { title: 'Activity.router.module.title', icon: 'activity' },
    children: [
      // 活动管理
      {
        path: 'list',
        name: 'ActivityEvent',
        meta: { title: 'Activity.router.event.title' },
        children: [
          {
            path: '',
            name: 'EventList',
            meta: { title: 'Activity.router.event.list' },
            component: () => import('./ui/activity/EventList.vue'),
          },
          // {
          //     path: 'dashboard',
          //     name: 'EventDashboard',
          //     meta: { title: '活动管理Dashboard' },
          //     component: () => import('./ui/event/EventDashboard.vue'),
          // },
          {
            path: 'create',
            name: 'EventCreate',
            meta: { title: 'Activity.router.event.create' },
            component: () => import('./ui/activity/EventCreate.vue'),
          },
          {
            path: ':id/edit',
            name: 'EventEdit',
            meta: { title: 'Activity.router.event.edit' },
            component: () => import('./ui/activity/EventCreate.vue'),
          },
          {
            path: ':id',
            name: 'EventDetail',
            meta: { title: 'Activity.router.event.detail' },
            component: () => import('./ui/activity/EventDetail.vue'),
          },
          // {
          //     path: ':id/edit',
          //     name: 'EventEdit',
          //     meta: { title: '编辑活动' },
          //     component: () => import('./ui/event/EventEdit.vue'),
          // }
        ],
      },
      // 票务管理
      {
        path: 'settings/ticket',
        name: 'ActivityTicket',
        meta: { title: 'Activity.router.ticket.title' },
        children: [
          {
            path: '',
            name: 'TicketList',
            meta: { title: 'Activity.router.ticket.list' },
            component: () => import('./ui/ticket/TicketList copy.vue'),
          },
          {
            path: 'create',
            name: 'TicketCreate',
            meta: { title: 'Activity.router.ticket.create' },
            component: () => import('./ui/ticket/create/index.vue'),
          },
          // {
          //     path: ':id/edit',
          //     name: 'TicketEdit',
          //     meta: { title: '编辑票务' },
          //     component: () => import('./ui/ticket/TicketEdit.vue'),
          // }
        ],
      },
      // 参与者管理
      {
        path: 'participant',
        name: 'ActivityParticipant',
        meta: { title: 'Activity.router.participant.title' },
        children: [
          {
            path: 'list',
            name: 'ParticipantList',
            meta: { title: 'Activity.router.participant.list' },
            component: () => import('./ui/participant/ParticipantList copy.vue'),
          },
          {
            path: ':id',
            name: 'ParticipantDetail',
            meta: { title: 'Activity.router.participant.detail' },
            component: () => import('./ui/participant/ParticipantDetail.vue'),
          },
          {
            path: 'create',
            name: 'ParticipantCreate',
            meta: { title: 'Activity.router.participant.create' },
            component: () => import('./ui/participant/ParticipantCreate.vue'),
          },
        ],
      },
      // 等待名单管理
      {
        path: 'waiting-list',
        name: 'ActivityWaitingList',
        meta: { title: 'Activity.router.waitlist.title' },
        children: [
          {
            path: '',
            name: 'WaitingList',
            meta: { title: 'Activity.router.waitlist.list' },
            component: () => import('./ui/waiting-list/WaitingList copy.vue'),
          },
          {
            path: ':id/review',
            name: 'WaitingListReview',
            meta: { title: 'Activity.router.waitlist.review' },
            component: () => import('./ui/waiting-list/WaitingListReview.vue'),
          },
        ],
      },
      // 邀请管理
      {
        path: 'invitation',
        name: 'ActivityInvitation',
        meta: { title: 'Activity.router.invitation.title' },
        children: [
          {
            path: '',
            name: 'InvitationList',
            meta: { title: 'Activity.router.invitation.list' },
            component: () => import('./ui/invitation/InvitationList copy.vue'),
          },
          {
            path: 'create',
            name: 'InvitationCreate',
            meta: { title: 'Activity.router.invitation.create' },
            component: () => import('./ui/invitation/InvitationCreate.vue'),
          },
          {
            path: ':id/edit',
            name: 'InvitationEdit',
            meta: { title: 'Activity.router.invitation.edit' },
            component: () => import('./ui/invitation/InvitationCreate.vue'),
          },
          {
            path: 'details',
            name: 'InvitationDetails',
            meta: { title: 'Activity.router.invitation.detail' },
            component: () => import('./ui/invitation/details.vue'),
          },
        ],
      },
      // 规则模板管理
      {
        path: 'rule',
        name: 'ActivityRuleTemplate',
        meta: { title: 'Activity.router.rule_template.title' },
        children: [
          {
            path: 'template',
            name: 'RuleTemplateList',
            meta: { title: 'Activity.router.rule_template.list' },
            component: () => import('./ui/rule-template/RuleTemplateList.vue'),
          },
          {
            path: 'create',
            name: 'RuleTemplateCreate',
            meta: { title: 'Activity.router.rule_template.create' },
            component: () => import('./ui/rule-template/RuleTemplateCreate.vue'),
          },
          // {
          //     path: ':id/edit',
          //     name: 'RuleTemplateEdit',
          //     meta: { title: 'Activity.router.rule_template.edit' },
          //     component: () => import('./ui/rule-template/RuleTemplateEdit.vue'),
          // }
        ],
      },
      // 活动详情页面（带tabs）
      {
        path: '/activity/detail/:id',
        name: 'ActivityDetailTabs',
        meta: { title: 'Activity.router.activity_detail_tabs' },
        component: () => import('./ui/activity/ActivityDetailTabs.vue'),
      },
      // 提醒管理
      {
        path: 'reminder',
        name: 'ActivityReminder',
        meta: { title: 'Activity.router.reminder.title' },
        children: [
          {
            path: '',
            name: 'ReminderList',
            meta: { title: 'Activity.router.reminder.list' },
            component: () => import('./ui/reminder/ReminderList copy.vue'),
          },
          {
            path: 'create',
            name: 'ReminderCreate',
            meta: { title: 'Activity.router.reminder.create' },
            component: () => import('./ui/reminder/creata/index.vue'),
          },
          {
            path: ':id/edit',
            name: 'ReminderEdit',
            meta: { title: 'Activity.router.reminder.edit' },
            component: () => import('./ui/reminder/creata/index.vue'),
          },
        ],
      },
      // 表单模板管理
      {
        path: 'forms',
        name: 'ActivityFormTemplate',
        meta: { title: 'Activity.router.form_template.title' },
        children: [
          {
            path: '',
            name: 'FormTemplateList',
            meta: { title: 'Activity.router.form_template.list' },
            component: () => import('./ui/form-template/FormTemplateList.vue'),
          },
          {
            path: 'create',
            name: 'FormTemplateCreate',
            meta: { title: 'Activity.router.form_template.create' },
            component: () => import('./ui/form-template/FormTemplateCreate.vue'),
          },
          // {
          //     path: ':id/edit',
          //     name: 'FormTemplateEdit',
          //     meta: { title: 'Activity.router.form_template.edit' },
          //     component: () => import('./ui/form-template/FormTemplateEdit.vue'),
          // }
        ],
      },
      // 邮件模板管理
      {
        path: 'email',
        name: 'ActivityEmailTemplate',
        meta: { title: 'Activity.router.email_template.title' },
        children: [
          {
            path: 'template',
            name: 'EmailTemplateList',
            meta: { title: 'Activity.router.email_template.list' },
            component: () => import('./ui/email-template/EmailTemplateList.vue'),
          },
          {
            path: 'create',
            name: 'EmailTemplateCreate',
            meta: { title: 'Activity.router.email_template.create' },
            component: () => import('./ui/email-template/EmailTemplateForm.vue'),
          },
          {
            path: ':id/edit',
            name: 'EmailTemplateEdit',
            meta: { title: 'Activity.router.email_template.edit' },
            component: () => import('./ui/email-template/EmailTemplateForm.vue'),
          },
        ],
      },
      // 活动日历
      {
        path: 'calendar',
        name: 'CalendarView',
        meta: { title: 'Activity.router.calendar' },
        component: () => import('./ui/calendar/index.vue'),
      },
    ],
  },
]

export default router
