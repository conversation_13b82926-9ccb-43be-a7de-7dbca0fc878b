<template>
  <div class="participant-list-container">
    <el-table :data="participantList" style="width: 100%;" v-loading="loading">
      <template #empty>
        <el-empty :description="$t('Activity.participant_list.empty.description')" image-size="100px" />
      </template>
      <el-table-column 
        prop="user.name" 
        :label="$t('Activity.participant_list.table.name')" 
        min-width="120"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.user?.name || scope.row.user?.email || $t('Activity.participant_list.empty.no_name') }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="user.email" 
        :label="$t('Activity.participant_list.table.email')" 
        min-width="200"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.user?.email || '-' }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="user.phone" 
        :label="$t('Activity.participant_list.table.phone')" 
        min-width="150"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.user?.phone || '-' }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="registration_time_formatted" 
        :label="$t('Activity.participant_list.table.registration_time')" 
        min-width="180"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.registration_time_formatted }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="status" 
        :label="$t('Activity.participant_list.table.status')" 
        min-width="100"
      >
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" :label="$t('Activity.participant_list.table.created_at')" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" :label="$t('Activity.participant_list.table.actions')" width="150">
        <template #default="scope">
          <div class="operate-btn-box">
            <div class="operate-btn" @click="handleEdit(scope.row)">
              <el-icon>
                <img :src="$asset('Cms/Asset/EditIcon.png')" alt="" />
              </el-icon>
            </div>
            <el-button class="operate-btn del-btn" type="text" @click="handleDelete(scope.row)">
              <el-icon>
                <img :src="$asset('Cms/Asset/DeleteIcon.png')" alt="" />
              </el-icon>
            </el-button>

          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 删除确认弹窗 -->
    <el-dialog
      v-model="deleteDialogVisible"
      :title="$t('Activity.participant_list.delete_dialog.title')"
      class="el-dialog-common-cls"
      width="400px"
    >
      <div class="delete-content">
        <p>{{ $t('Activity.participant_list.delete_dialog.content', { name: currentDeleteItem?.user?.name || currentDeleteItem?.user?.email || $t('Activity.participant_list.empty.no_name') }) }}</p>
        <p class="warning-text">{{ $t('Activity.participant_list.delete_dialog.warning') }}</p>
      </div>
      <template #footer>
        <div class="flex justify-center">
          <el-button class="el-button-default" @click="deleteDialogVisible = false">
            {{ $t('Activity.participant_list.delete_dialog.cancel') }}
          </el-button>
          <el-button class="button-no-border" type="danger" @click="confirmDelete" :loading="deleteLoading">
            {{ $t('Activity.participant_list.delete_dialog.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { participantService, type ParticipantListParams } from "../../services/participantService"

const { t } = useI18n()

// Props - 接收活动ID和分页信息
interface Props {
  activityId?: string | number
  pagination?: {
    page: number
    pageSize: number
    total: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  activityId: '',
  pagination: () => ({ page: 1, pageSize: 10, total: 0 })
})

// Emits - 发送事件
const emit = defineEmits<{
  'update-total': [total: number]
}>()

// 等待名单项接口 - 更新为实际API结构
interface IParticipant {
  id: number
  activity_id: number
  user_id: number
  registration_time: number
  registration_time_formatted: string
  status: string
  check_in_time: number | null
  check_in_time_formatted: string | null
  feedback: string | null
  creator_id: number
  created_at: string
  updated_at: string
  deleted_at: number
  user: {
    id: number
    name: string
    email: string
    phone: string
    gender: string
    photo: string
  }
}

// API 响应类型
interface ParticipantListResponse {
  items: IParticipant[]
  total: number
}

const router = useRouter()
const route = useRoute()

// 表格数据
const participantList = ref<IParticipant[]>([])
const loading = ref(false)

// 删除相关
const deleteDialogVisible = ref(false)
const deleteLoading = ref(false)
const currentDeleteItem = ref<IParticipant | null>(null)

// 获取状态类型
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    'registered': 'primary',
    'confirmed': 'success',
    'cancelled': 'danger',
    'attended': 'success',
    'waiting': 'warning'
  }
  return map[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  return t(`Activity.participant_list.status.${status}`) || t('Activity.participant_list.status.unknown')
}

// 查看详情
const handleEdit = (row: IParticipant) => {
  router.push({
    path: '/activity/participant/details',
    query: {
      id: row.id.toString(),
      activityId: props.activityId?.toString() || route.params.id
    }
  })
}

// 删除
const handleDelete = (row: IParticipant) => {
  currentDeleteItem.value = row
  deleteDialogVisible.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!currentDeleteItem.value) return
  
  deleteLoading.value = true
  try {
    await participantService.delete(currentDeleteItem.value.id)
    ElMessage.success(t('Activity.participant_list.messages.delete_success'))
    deleteDialogVisible.value = false
    currentDeleteItem.value = null
    
    // 重新加载数据
    await getTableDataList()
  } catch (error) {
    ElMessage.error(t('Activity.participant_list.messages.delete_failed'))
  } finally {
    deleteLoading.value = false
  }
}

// 日期转换函数 - 转换为"yyyy-mm-dd"格式
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '-'
  
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  return `${year}-${month}-${day}`
}

// 查询列表
const getTableDataList = async () => {
  loading.value = true
  try {
    const params: ParticipantListParams = {
      activity_id: props.activityId || route.params.id as string,
      page: props.pagination?.page || 1,
      per_page: props.pagination?.pageSize || 10
    }
    
    const res = await participantService.getList(params)
    
    // 根据实际API返回结构解析数据
    if (res.data && res.data.code === 200 && res.data.data) {
      participantList.value = res.data.data.items || []
      const total = res.data.data.total || 0
      emit('update-total', total)
    } else {
      // 兼容其他可能的返回结构
      participantList.value = res.data?.data || []
      emit('update-total', participantList.value.length)
    }
  } catch (error) {
    console.error(t('Activity.participant_list.messages.get_list_failed'), error)
    ElMessage.error(t('Activity.participant_list.messages.get_list_failed'))
  } finally {
    loading.value = false
  }
}

// 暴露刷新方法给父组件
const refresh = () => {
  getTableDataList()
}

// 暴露方法
defineExpose({
  refresh
})

// 监听分页变化
watch(
  () => props.pagination,
  (newPagination, oldPagination) => {
    if (newPagination && oldPagination) {
      if (
        newPagination.page !== oldPagination.page ||
        newPagination.pageSize !== oldPagination.pageSize
      ) {
        getTableDataList()
      }
    }
  },
  { deep: true }
)

onMounted(() => {
  getTableDataList()
})
</script>

<style lang="scss" scoped>
.participant-list-container {
  width: 100%;
  
  // 操作按钮样式
  .operate-btn-box {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .operate-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #dcdfe6;
    background: #fff;

    &:hover {
      border-color: #409eff;
      background: #ecf5ff;
      
      .el-icon {
        color: #409eff;
      }
    }

    .el-icon {
      font-size: 16px;
      color: #606266;
    }
  }

  .del-btn {
    &:hover {
      border-color: #f56c6c;
      background: #fef0f0;
      
      .el-icon {
        color: #f56c6c;
      }
    }
  }
}

// 删除弹窗样式
.delete-content {
  text-align: center;
  padding: 20px 0;
  
  p {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #303133;
  }
  
  .warning-text {
    font-size: 14px;
    color: #f56c6c;
  }
}

.flex {
  display: flex;
  gap: 12px;
}

.justify-center {
  justify-content: center;
}
</style>