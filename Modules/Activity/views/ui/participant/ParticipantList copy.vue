<template>
  <div class="bwms-module">
    <!-- 页面标题 -->
    <div class="module-header">
     
        <el-dropdown trigger="click" @command="handleStatusDropdown">
          <el-button>
            修改状态 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="undecided">未决定</el-dropdown-item>
              <el-dropdown-item command="confirmed">确认参与</el-dropdown-item>
              <el-dropdown-item command="rejected">拒绝参与</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        
        <el-dropdown trigger="click" @command="handleBatchOperations">
          <el-button>
            批量操作 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="import">导入参与者数据</el-dropdown-item>
              <el-dropdown-item command="export">导出参与者数据</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <!-- 添加参与者按钮 -->
    <el-button type="primary" @click="handleAddParticipant">添加参与者</el-button>

     
    </div>

    
    <!-- 表格区域 -->
    <div class="module-con">
      <div class="box">
        
        <!-- 表格 -->
        <el-table
          v-loading="loading"
          :data="tableData"
          @selection-change="handleSelectionChange"
        >
          <template #empty>
              <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column type="selection" width="55" />
          <el-table-column 
            prop="name" 
            label="姓名" 
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="company" 
            label="公司" 
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="position" 
            label="职位" 
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="email" 
            label="邮箱" 
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="participantPath" 
            label="参与途径" 
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="status" 
            label="状态" 
            min-width="100"
          >
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column width="140" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button class="edit-btn" type="primary" text circle>
                  <el-icon><edit /></el-icon>
                </el-button>
                <el-button class="email-btn" type="primary" text circle>
                  <el-icon><message /></el-icon>
                </el-button>
                <el-button class="more-btn" type="primary" text circle>
                  <el-icon><more /></el-icon>
                </el-button>
                <el-dropdown trigger="click" class="action-dropdown">
                  <span class="el-dropdown-link">
                    <el-icon><more /></el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleDetail(row)">查看参与者journey</el-dropdown-item>
                      <el-dropdown-item @click="handleAddToBlacklist(row)">加入黑名单</el-dropdown-item>
                      <el-dropdown-item @click="handleResendEmail(row)">重新发送邀请邮件</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="per_page"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Filter, More, Edit, Message, ArrowDown } from '@element-plus/icons-vue'
import { participantService } from '../../services/participantService'
import type { IParticipant } from '../../types'

const router = useRouter()
const loading = ref(false)
const tableData = ref<IParticipant[]>([])
const selectedRows = ref<IParticipant[]>([])
const currentPage = ref(1)
const per_page = ref(10)
const total = ref(0)

// 获取状态类型
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    'pending': 'warning',
    'confirmed': 'success',
    'rejected': 'danger',
    'undecided': 'info'
  }
  return map[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    'pending': '邀请',
    'confirmed': '确认参与',
    'rejected': '拒绝参与',
    'undecided': '未决定',
    'no_response': '无回应'
  }
  return map[status] || '未知'
}

// 获取列表数据
const fetchData = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      per_page: per_page.value
    }
    const { data } = await participantService.getList(params)
    tableData.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 查看详情
const handleDetail = (row: IParticipant) => {
  router.push({ 
    name: 'ParticipantDetail',
    params: { id: row.id }
  })
}

// 添加到黑名单
const handleAddToBlacklist = (row: IParticipant) => {
  ElMessage.info('加入黑名单功能开发中')
}

// 重新发送邮件
const handleResendEmail = (row: IParticipant) => {
  ElMessage.info('重新发送邀请邮件功能开发中')
}

// 添加参与者
const handleAddParticipant = () => {
  router.push({ name: 'ParticipantCreate' })
}

// 批量操作
const handleBatchOperations = (command: string) => {
  if (command === 'import') {
    ElMessage.info('导入参与者数据功能开发中')
  } else if (command === 'export') {
    ElMessage.info('导出参与者数据功能开发中')
  }
}

// 状态修改下拉菜单
const handleStatusDropdown = (command: string) => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择参与者')
    return
  }
  
  const statusMap: Record<string, string> = {
    'undecided': '未决定',
    'confirmed': '确认参与',
    'rejected': '拒绝参与'
  }
  
  ElMessageBox.confirm(
    `确定将所选参与者状态修改为"${statusMap[command]}"吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const ids = selectedRows.value.map(row => row.id)
      await participantService.batchUpdateStatus(ids, command)
      ElMessage.success('状态修改成功')
      fetchData()
    } catch (error) {
      console.error('状态修改失败:', error)
      ElMessage.error('状态修改失败')
    }
  }).catch(() => {})
}

// 选择变更
const handleSelectionChange = (rows: IParticipant[]) => {
  selectedRows.value = rows
}

// 页码变更
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchData()
}

// 分页大小变更
const handleSizeChange = (val: number) => {
  per_page.value = val
  fetchData()
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  margin-top: 10px;
  .module-header {
    display: flex;
    gap: 15px;
    align-items: center;
  }

  .module-con {
    
    .box {
      padding-top: 20px;

      .action-buttons {
        display: flex;
        align-items: center;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: end;
      }
    }
  }
}

.ml-2 {
  margin-left: 8px;
}
</style>