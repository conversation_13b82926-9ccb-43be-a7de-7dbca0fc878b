<template>
  <div class="bwms-module">
    <!-- 面包屑导航 -->
   

    <!-- 页面标题和操作 -->
    <div class="module-header">
     
        <el-button @click="goBack">{{ $t('Activity.participant_create.buttons.back') }}</el-button>
    </div>

    <!-- 表单内容 -->
    <div class="module-con">
      <div class="box">
        <el-form 
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="participant-form"
      >
        <div class="form-section">
          <div class="section-title">{{ $t('Activity.participant_create.sections.basic_info') }}</div>
          <el-form-item :label="$t('Activity.participant_create.form.name')" prop="name">
            <el-input v-model="form.name" :placeholder="$t('Activity.participant_create.form.name_placeholder')" />
          </el-form-item>
          <el-form-item :label="$t('Activity.participant_create.form.company')" prop="company">
            <el-input v-model="form.company" :placeholder="$t('Activity.participant_create.form.company_placeholder')" />
          </el-form-item>
          <el-form-item :label="$t('Activity.participant_create.form.position')" prop="position">
            <el-input v-model="form.position" :placeholder="$t('Activity.participant_create.form.position_placeholder')" />
          </el-form-item>
          <el-form-item :label="$t('Activity.participant_create.form.email')" prop="email">
            <el-input v-model="form.email" :placeholder="$t('Activity.participant_create.form.email_placeholder')" />
          </el-form-item>
          <el-form-item :label="$t('Activity.participant_create.form.phone')" prop="phone">
            <el-input v-model="form.phone" :placeholder="$t('Activity.participant_create.form.phone_placeholder')" />
          </el-form-item>
          <el-form-item :label="$t('Activity.participant_create.form.participant_path')" prop="participantPath">
            <el-select v-model="form.participantPath" :placeholder="$t('Activity.participant_create.form.participant_path_placeholder')">
              <el-option :label="$t('Activity.participant_create.options.participant_path.registration')" value="registration" />
              <el-option :label="$t('Activity.participant_create.options.participant_path.invitation')" value="invitation" />
              <el-option :label="$t('Activity.participant_create.options.participant_path.recommendation')" value="recommendation" />
              <el-option :label="$t('Activity.participant_create.options.participant_path.other')" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('Activity.participant_create.form.status')" prop="status">
            <el-select v-model="form.status" :placeholder="$t('Activity.participant_create.form.status_placeholder')">
              <el-option :label="$t('Activity.participant_create.options.status.undecided')" value="undecided" />
              <el-option :label="$t('Activity.participant_create.options.status.confirmed')" value="confirmed" />
              <el-option :label="$t('Activity.participant_create.options.status.rejected')" value="rejected" />
              <el-option :label="$t('Activity.participant_create.options.status.pending')" value="pending" />
            </el-select>
          </el-form-item>
        </div>

        <div class="form-section" v-if="form.participantPath === 'invitation'">
          <div class="section-title">{{ $t('Activity.participant_create.sections.invitation_info') }}</div>
          <el-form-item :label="$t('Activity.participant_create.form.invitation_sender')" prop="invitation.sender">
            <el-input v-model="form.invitation.sender" :placeholder="$t('Activity.participant_create.form.invitation_sender_placeholder')" />
          </el-form-item>
          <el-form-item :label="$t('Activity.participant_create.form.invitation_sender_email')" prop="invitation.senderEmail">
            <el-input v-model="form.invitation.senderEmail" :placeholder="$t('Activity.participant_create.form.invitation_sender_email_placeholder')" />
          </el-form-item>
          <el-form-item :label="$t('Activity.participant_create.form.send_invitation')" prop="invitation.sendNow">
            <el-switch v-model="form.invitation.sendNow" />
          </el-form-item>
        </div>

        <div class="form-section">
          <div class="section-title">{{ $t('Activity.participant_create.sections.additional_info') }}</div>
          <el-form-item :label="$t('Activity.participant_create.form.remark')" prop="remark">
            <el-input 
              v-model="form.remark" 
              type="textarea" 
              rows="3"
              :placeholder="$t('Activity.participant_create.form.remark_placeholder')" 
            />
          </el-form-item>
        </div>

        <div class="form-actions">
          <el-button @click="goBack">{{ $t('Activity.participant_create.buttons.cancel') }}</el-button>
          <el-button type="primary" @click="submitForm(formRef)">{{ $t('Activity.participant_create.buttons.save') }}</el-button>
        </div>
      </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { participantService } from '../../services/participantService'

const router = useRouter()
const { t } = useI18n()
const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const form = reactive({
  name: '',
  company: '',
  position: '',
  email: '',
  phone: '',
  participantPath: 'registration',
  status: 'undecided',
  remark: '',
  invitation: {
    sender: '',
    senderEmail: '',
    sendNow: true
  }
})

// 表单验证规则
const rules = computed<FormRules>(() => ({
  name: [
    { required: true, message: t('Activity.participant_create.validation.name_required'), trigger: 'blur' },
    { min: 2, max: 50, message: t('Activity.participant_create.validation.name_length'), trigger: 'blur' }
  ],
  email: [
    { required: true, message: t('Activity.participant_create.validation.email_required'), trigger: 'blur' },
    { type: 'email', message: t('Activity.participant_create.validation.email_format'), trigger: 'blur' }
  ],
  company: [
    { required: true, message: t('Activity.participant_create.validation.company_required'), trigger: 'blur' }
  ],
  position: [
    { required: true, message: t('Activity.participant_create.validation.position_required'), trigger: 'blur' }
  ],
  participantPath: [
    { required: true, message: t('Activity.participant_create.validation.participant_path_required'), trigger: 'change' }
  ],
  status: [
    { required: true, message: t('Activity.participant_create.validation.status_required'), trigger: 'change' }
  ],
  'invitation.sender': [
    { 
      required: true, 
      message: t('Activity.participant_create.validation.invitation_sender_required'), 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (form.participantPath === 'invitation' && !value) {
          callback(new Error(t('Activity.participant_create.validation.invitation_sender_required')))
        } else {
          callback()
        }
      }
    }
  ],
  'invitation.senderEmail': [
    { 
      required: true, 
      message: t('Activity.participant_create.validation.invitation_sender_email_required'), 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (form.participantPath === 'invitation' && !value) {
          callback(new Error(t('Activity.participant_create.validation.invitation_sender_email_required')))
        } else {
          callback()
        }
      }
    },
    { 
      type: 'email', 
      message: t('Activity.participant_create.validation.invitation_sender_email_format'), 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (form.participantPath === 'invitation' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          callback(new Error(t('Activity.participant_create.validation.invitation_sender_email_format')))
        } else {
          callback()
        }
      }
    }
  ]
}))

// 返回列表
const goBack = () => {
  router.back()
}

// 提交表单
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  try {
    const valid = await formEl.validate()
    if (valid) {
      loading.value = true
      // 添加 activity_id，这里需要从路由参数或 props 获取
      const formData = {
        ...form,
        activity_id: 1 // 这里应该从路由参数或 props 获取实际的 activity_id
      }
      await participantService.create(formData)
      ElMessage.success(t('Activity.participant_create.messages.create_success'))
      router.push({ name: 'ParticipantList' })
    }
  } catch (error) {
    if (error instanceof Error && error.message.includes('validation')) {
      ElMessage.error(t('Activity.participant_create.messages.fill_required'))
    } else {
      console.error(t('Activity.participant_create.messages.create_failed'), error)
      ElMessage.error(t('Activity.participant_create.messages.create_failed'))
    }
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.bwms-module {
  .breadcrumb {
    margin-bottom: 16px;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .title {
      font-size: 20px;
      font-weight: 500;
    }

    .actions {
      display: flex;
      gap: 12px;
    }
  }

  .info-card {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 24px;
  }

  .form-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 20px;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 30px;
  }

  .participant-form {
    max-width: 700px;
  }
}
</style> 