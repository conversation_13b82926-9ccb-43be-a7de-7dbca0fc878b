<template>
  <div class="bwms-module">
    <!-- 面包屑导航 -->

    <!-- 页面标题和操作 -->
    <div class="module-header">
      <div class="title">{{ participant.name }} - {{ getStatusText(participant.status) }}</div>
      <div class="actions">
        <el-dropdown trigger="click" @command="handleOperation">
          <el-button>
            {{ $t('Activity.participant_detail.buttons.operations') }} <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="resendEmail">{{ $t('Activity.participant_detail.operations.resend_email') }}</el-dropdown-item>
              <el-dropdown-item command="export">{{ $t('Activity.participant_detail.operations.export') }}</el-dropdown-item>
              <el-dropdown-item command="print">{{ $t('Activity.participant_detail.operations.print') }}</el-dropdown-item>
              <el-dropdown-item command="checkJourney">{{ $t('Activity.participant_detail.operations.check_journey') }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button @click="goBack">{{ $t('Activity.participant_detail.buttons.back') }}</el-button>
      </div>
    </div>

    <!-- 内容主体区 -->
    <div class="content-wrapper">
      <!-- 左侧受邀者信息 -->
      <div class="left-panel info-card">
        <div class="card-title">{{ $t('Activity.participant_detail.sections.invitee_info') }}</div>
        <div class="info-form">
          <div class="form-item">
            <label>{{ $t('Activity.participant_detail.fields.name') }}</label>
            <div class="form-value">{{ participant.name }}</div>
          </div>
          <div class="form-item">
            <label>{{ $t('Activity.participant_detail.fields.email') }}</label>
            <div class="form-value">{{ participant.email }}</div>
          </div>
          <div class="form-item">
            <label>{{ $t('Activity.participant_detail.fields.status') }}</label>
            <div class="form-value">
              <el-tag :type="getStatusType(participant.status)">
                {{ getStatusText(participant.status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧动作轨迹 -->
      <div class="right-panel info-card">
        <div class="card-title">{{ $t('Activity.participant_detail.sections.action_timeline') }}</div>
        <div class="timeline-container">
          <el-timeline>
            <el-timeline-item
              v-for="(action, index) in actions"
              :key="index"
              :timestamp="action.time"
              :color="getActionColor(action.type)"
            >
              <div class="timeline-content">
                <div class="action-type">{{ action.desc }}</div>
                <div class="action-detail">
                  <div v-if="action.operationBy" class="operation-by">
                    {{ $t('Activity.participant_detail.fields.operation_desc') }}：{{ action.operationBy }}
                  </div>
                  <div v-if="action.operationType" class="operation-type">
                    {{ $t('Activity.participant_detail.fields.operation_type') }}：{{ action.operationType }}
                  </div>
                  <div v-if="action.ip" class="operation-ip">
                    {{ $t('Activity.participant_detail.fields.operation_ip') }}：{{ action.ip }}
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>

    <!-- 邀请操作信息 -->
    <div class="invitation-info info-card">
      <div class="card-title">{{ $t('Activity.participant_detail.sections.invitation_info') }}</div>
      <div class="info-form">
        <div class="form-item">
          <label>{{ $t('Activity.participant_detail.fields.invitation_sender') }}</label>
          <div class="form-value">{{ invitation.sender }}</div>
        </div>
        <div class="form-item">
          <label>{{ $t('Activity.participant_detail.fields.invitation_sender_email') }}</label>
          <div class="form-value">{{ invitation.senderEmail }}</div>
        </div>
        <div class="form-item">
          <label>{{ $t('Activity.participant_detail.fields.confirm_time') }}</label>
          <div class="form-value">{{ invitation.confirmTime }}</div>
        </div>
        <div class="form-item">
          <label>{{ $t('Activity.participant_detail.fields.status') }}</label>
          <div class="form-value">
            <el-input v-model="invitation.status" disabled />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import { participantService } from '../../services/participantService'
import { IParticipantAction, IParticipantInvitation } from '../../types'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const loading = ref(false)
const participantId = route.params.id as string

// 参与者信息
const participant = reactive({
  id: '',
  name: '',
  email: '',
  company: '',
  position: '',
  status: ''
})

// 邀请信息
const invitation = reactive<IParticipantInvitation>({
  sender: '',
  senderEmail: '',
  confirmTime: '',
  status: ''
})

// 操作历史
const actions = ref<IParticipantAction[]>([])

// 获取状态类型
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    'pending': 'warning',
    'confirmed': 'success',
    'rejected': 'danger',
    'undecided': 'info'
  }
  return map[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    'pending': t('Activity.participant_detail.status.pending'),
    'confirmed': t('Activity.participant_detail.status.confirmed'),
    'rejected': t('Activity.participant_detail.status.rejected'),
    'undecided': t('Activity.participant_detail.status.undecided'),
    'no_response': t('Activity.participant_detail.status.no_response')
  }
  return map[status] || t('Activity.participant_detail.status.unknown')
}

// 获取动作颜色
const getActionColor = (type: string) => {
  const map: Record<string, string> = {
    'invite': '#409EFF',
    'confirm': '#67C23A',
    'reject': '#F56C6C',
    'register': '#E6A23C'
  }
  return map[type] || '#909399'
}

// 获取参与者详情
const fetchParticipantDetail = async () => {
  try {
    loading.value = true
    const { data } = await participantService.getDetail(participantId)
    
    // 更新参与者信息
    Object.assign(participant, data.participant)
    
    // 更新邀请信息
    if (data.invitation) {
      Object.assign(invitation, {
        sender: data.invitation.sender || '',
        senderEmail: data.invitation.senderEmail || '',
        confirmTime: data.invitation.confirmTime || '',
        status: data.invitation.status || ''
      })
    }
    
  } catch (error) {
    console.error(t('Activity.participant_detail.messages.get_detail_failed'), error)
    ElMessage.error(t('Activity.participant_detail.messages.get_detail_failed'))
  } finally {
    loading.value = false
  }
}

// 获取操作历史
const fetchActionHistory = async () => {
  try {
    loading.value = true
    const { data } = await participantService.getActionHistory(participantId)
    actions.value = data.actions || []
  } catch (error) {
    console.error(t('Activity.participant_detail.messages.get_action_history_failed'), error)
    ElMessage.error(t('Activity.participant_detail.messages.get_action_history_failed'))
  } finally {
    loading.value = false
  }
}

// 返回列表
const goBack = () => {
  router.push({ name: 'ParticipantList' })
}

// 处理操作
const handleOperation = async (command: string) => {
  try {
    if (command === 'resendEmail') {
      await participantService.resendInvitation(participantId)
      ElMessage.success(t('Activity.participant_detail.messages.resend_email_success'))
    } else if (command === 'export') {
      ElMessage.info(t('Activity.participant_detail.messages.export_developing'))
    } else if (command === 'print') {
      window.print()
    } else if (command === 'checkJourney') {
      ElMessage.info(t('Activity.participant_detail.messages.check_journey_developing'))
    }
  } catch (error) {
    console.error(t('Activity.participant_detail.messages.operation_failed'), error)
    ElMessage.error(t('Activity.participant_detail.messages.operation_failed'))
  }
}

// 页面初始化
onMounted(() => {
  fetchParticipantDetail()
  fetchActionHistory()
})
</script>

<style lang="scss" scoped>
.bwms-module {



  .content-wrapper {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;

    .left-panel, .right-panel {
      flex: 1;
    }
  }

  .info-card {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 24px;

    .card-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 20px;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }

    .info-form {
      .form-item {
        margin-bottom: 16px;
        display: flex;

        label {
          width: 120px;
          color: #606266;
        }

        .form-value {
          flex: 1;
        }
      }
    }
  }

  .timeline-container {
    padding: 0 10px;

    .timeline-content {
      .action-type {
        font-weight: 500;
        margin-bottom: 8px;
      }

      .action-detail {
        color: #606266;
        font-size: 13px;
        
        > div {
          margin-bottom: 4px;
        }
      }
    }
  }
}

@media print {
  .actions, .el-dropdown {
    display: none !important;
  }
}
</style> 