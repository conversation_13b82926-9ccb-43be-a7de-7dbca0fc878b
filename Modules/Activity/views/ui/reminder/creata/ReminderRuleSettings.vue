<template>
  <div class="reminder-rule-settings">
    <div class="rules-content">
      <!-- 活动提醒规则 -->
      <div class="rule-section">
        <h3 class="section-title">{{ $t('Activity.reminder_rule.sections.activity') }}</h3>
        
        <div class="rule-item">
          <div class="rule-info">
            <span class="rule-name">{{ $t('Activity.reminder_rule.rules.registration_deadline.name') }}</span>
            <span class="rule-desc">{{ $t('Activity.reminder_rule.rules.registration_deadline.desc') }}</span>
          </div>
          <el-switch v-model="localSettings.registration_deadline" class="custom-switch" />
        </div>
        
        <div class="rule-item">
          <div class="rule-info">
            <span class="rule-name">{{ $t('Activity.reminder_rule.rules.participant_limit.name') }}</span>
            <span class="rule-desc">{{ $t('Activity.reminder_rule.rules.participant_limit.desc') }}</span>
          </div>
          <el-switch v-model="localSettings.participant_limit" class="custom-switch" />
        </div>
        
        <div class="rule-item">
          <div class="rule-info">
            <span class="rule-name">{{ $t('Activity.reminder_rule.rules.activity_start_end.name') }}</span>
            <span class="rule-desc">{{ $t('Activity.reminder_rule.rules.activity_start_end.desc') }}</span>
          </div>
          <el-switch v-model="localSettings.activity_start_end" class="custom-switch" />
        </div>
      </div>

      <!-- 票务提醒规则 -->
      <div class="rule-section">
        <h3 class="section-title">{{ $t('Activity.reminder_rule.sections.ticket') }}</h3>
        
        <div class="rule-item">
          <div class="rule-info">
            <span class="rule-name">{{ $t('Activity.reminder_rule.rules.ticket_overdue.name') }}</span>
            <span class="rule-desc">{{ $t('Activity.reminder_rule.rules.ticket_overdue.desc') }}</span>
          </div>
          <el-switch v-model="localSettings.ticket_overdue" class="custom-switch" />
        </div>
      </div>

      <!-- 提醒设置说明 -->
      <div class="settings-note">
        <el-alert
          :title="$t('Activity.reminder_rule.alert.title')"
          type="info"
          :description="$t('Activity.reminder_rule.alert.desc')"
          show-icon
          :closable="false"
        />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="op-box">
      <el-button @click="handlePrevStep">{{ $t('Activity.reminder_rule.buttons.prev') }}</el-button>
      <el-button @click="handleCancel">{{ $t('Activity.reminder_rule.buttons.cancel') }}</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ $t('Activity.reminder_rule.buttons.submit') }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { ReminderSettings } from '../../../services/reminderService'

const { t } = useI18n()

interface LocalSettings extends ReminderSettings {}

const props = defineProps<{
  modelValue: LocalSettings
  loading: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: LocalSettings]
  'prev-step': []
  'cancel': []
  'submit': []
}>()

// 本地设置数据
const localSettings = reactive<LocalSettings>({
  registration_deadline: false,
  participant_limit: false,
  activity_start_end: false,
  ticket_overdue: false
})

// 处理上一步
const handlePrevStep = () => {
  emit('prev-step')
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}

// 处理提交
const handleSubmit = () => {
  emit('submit')
}

// 监听props变化，同步到本地数据
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(localSettings, newValue)
  }
}, { immediate: true, deep: true })

// 监听本地数据变化，同步到父组件
watch(localSettings, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })
</script>

<style scoped>
.reminder-rule-settings {
  padding: 20px 0;
}

.rules-content {
  margin-bottom: 40px;
}

.rule-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  margin-bottom: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  transition: all 0.3s ease;
}

.rule-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.rule-info {
  flex: 1;
}

.rule-name {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.rule-desc {
  display: block;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.custom-switch {
  --el-switch-on-color: #13ce66;
  --el-switch-off-color: #dcdfe6;
}

.settings-note {
  margin-top: 32px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
}

.op-box {
  display: flex;
  justify-content: end;
  gap: 16px;
  padding: 30px 0;
  margin-top: 30px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-alert) {
  border: none;
  background: transparent;
}

:deep(.el-alert__icon) {
  color: #409eff;
}

:deep(.el-alert__title) {
  color: #409eff;
  font-weight: 500;
}

:deep(.el-alert__description) {
  color: #606266;
  margin-top: 8px;
}
</style> 