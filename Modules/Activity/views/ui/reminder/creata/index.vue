<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <div class="btn-list">
        <el-button @click="goBack">{{ $t('Activity.reminder_create.buttons.back') }}</el-button>
      </div>
    </div>
    
    <div class="module-con" v-loading="pageLoading">
      <div class="box">
        <el-tabs v-model="activeTab">
          <el-tab-pane :label="$t('Activity.reminder_create.tabs.basic')" name="basic">
            <ReminderBasicSettings 
              ref="basicSettingsRef"
              v-model="formData"
              :loading="loading"
              @update:modelValue="handleBasicSettingsUpdate"
              @next-step="handleNext"
              @cancel="handleCancel"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('Activity.reminder_create.tabs.rules')" name="rules" :disabled="!isStepCompleted('basic')">
            <ReminderRuleSettings 
              ref="ruleSettingsRef"
              v-model="reminderSettings"
              :loading="loading"
              @update:modelValue="handleRuleSettingsUpdate"
              @prev-step="handlePrevStep"
              @cancel="handleCancel"
              @submit="handleSubmit"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { reminderService, type CreateReminderRequest, type UpdateReminderRequest, type ReminderItem } from '../../../services/reminderService'
// 使用 defineAsyncComponent 来动态导入组件，避免 TypeScript 错误
import { defineAsyncComponent } from 'vue'

const { t } = useI18n()

const ReminderBasicSettings = defineAsyncComponent(() => import('./ReminderBasicSettings.vue'))
const ReminderRuleSettings = defineAsyncComponent(() => import('./ReminderRuleSettings.vue'))

const router = useRouter()
const route = useRoute()
const activeTab = ref('basic')

// 判断是否为编辑模式
const isEditMode = computed(() => !!route.params.id)
const reminderId = computed(() => route.params.id as string)
const activityId = computed(() => route.query.activityId as string)

// 子组件引用
const basicSettingsRef = ref()
const ruleSettingsRef = ref()

// Loading状态
const pageLoading = ref(false)
const loading = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  type: [] as string[],
  recipients: [
    { email: '', phone: '' }
  ]
})

// 提醒设置数据
const reminderSettings = reactive({
  registration_deadline: false,
  participant_limit: false,
  activity_start_end: false,
  ticket_overdue: false
})

// 记录已完成的步骤
const completedSteps = ref<Set<string>>(new Set())

// 检查步骤是否完成
const isStepCompleted = (stepName: string): boolean => {
  return completedSteps.value.has(stepName)
}

// 标记步骤为完成
const markStepCompleted = (stepName: string) => {
  completedSteps.value.add(stepName)
}

// 获取提醒详情（编辑模式）
const fetchReminderDetail = async () => {
  if (!isEditMode.value || !reminderId.value) return

  pageLoading.value = true
  try {
    const res = await reminderService.getDetail(reminderId.value)
    
    if (res.data && res.data.data) {
      const detail: ReminderItem = res.data.data

      // 填充基本设置数据
      formData.name = detail.name || ''
      formData.type = detail.type || []
      formData.recipients = detail.recipients || [{ email: '', phone: '' }]

      // 填充提醒规则数据
      if (detail.settings) {
        Object.assign(reminderSettings, detail.settings)
      }

      // 编辑模式下，如果已有数据则标记第一步为完成
      if (detail.name && detail.type?.length) {
        markStepCompleted('basic')
      }
      
      ElMessage.success(t('Activity.reminder_create.messages.load_success'))
    }
  } catch (error) {
    console.error(t('Activity.reminder_create.messages.load_failed'), error)
    ElMessage.error(t('Activity.reminder_create.messages.load_failed'))
  } finally {
    pageLoading.value = false
  }
}

// 处理基本设置数据更新
const handleBasicSettingsUpdate = (newData: typeof formData) => {
  // 深拷贝更新表单数据，避免引用污染
  formData.name = newData.name
  formData.type = [...newData.type]
  formData.recipients = newData.recipients.map(r => ({ ...r }))
}

// 处理提醒规则数据更新
const handleRuleSettingsUpdate = (newData: typeof reminderSettings) => {
  // 更新提醒设置数据
  Object.assign(reminderSettings, newData)
}

// 下一步处理
const handleNext = () => {
  // 标记当前步骤为完成
  markStepCompleted(activeTab.value)
  
  // 切换到下一步
  if (activeTab.value === 'basic') {
    activeTab.value = 'rules'
  }
}

// 上一步处理
const handlePrevStep = () => {
  if (activeTab.value === 'rules') {
    activeTab.value = 'basic'
  }
}

// 验证所有表单
const validateAllForms = async (): Promise<boolean> => {
  try {
    // 验证基本设置表单
    if (basicSettingsRef.value?.validate) {
      const basicValid = await basicSettingsRef.value.validate()
      if (!basicValid) {
        activeTab.value = 'basic'
        ElMessage.error(t('Activity.reminder_create.messages.form_incomplete'))
        return false
      }
    }

    // 提醒规则不需要强制验证
    
    return true
  } catch (error) {
    console.error('Form validation error:', error)
    return false
  }
}

// 提交处理
const handleSubmit = async () => {
  loading.value = true
  try {
    // 验证所有表单数据
    const isValid = await validateAllForms()
    if (!isValid) {
      ElMessage.error(t('Activity.reminder_create.messages.check_form'))
      return
    }

    const submitData = {
      name: formData.name,
      type: formData.type,
      recipients: formData.recipients,
      activity_id: activityId.value,
      settings: reminderSettings,
      status: 1 // 默认启用
    }

    if (isEditMode.value) {
      // 编辑模式
      await reminderService.update(reminderId.value, submitData)
      ElMessage.success(t('Activity.reminder_create.messages.update_success'))
    } else {
      // 创建模式
      await reminderService.create(submitData)
      ElMessage.success(t('Activity.reminder_create.messages.create_success'))
    }
    // 返回列表页
    router.go(-1)
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(isEditMode.value ? t('Activity.reminder_create.messages.update_failed') : t('Activity.reminder_create.messages.create_failed'))
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  router.back()
}

const goBack = () => {
  router.back()
}

// 初始化
onMounted(() => {
  if (!activityId.value) {
    ElMessage.error(t('Activity.reminder_create.messages.missing_activity_id'))
    router.back()
    return
  }

  if (isEditMode.value) {
    fetchReminderDetail()
  }
})
</script>

<style scoped>
/* 参考InvitationCreate.vue的禁用tab样式 */
:deep(.el-tabs__nav-wrap .el-tabs__item.is-disabled) {
  cursor: not-allowed;
  opacity: 0.6;
}

:deep(.el-tabs__nav-wrap .el-tabs__item.is-disabled:hover) {
  color: inherit;
}

.table-page {
  padding: 20px;
}

.module-header {
  margin-bottom: 20px;
}

.btn-list {
  display: flex;
  gap: 12px;
}

.module-con {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.box {
  padding: 20px;
}
</style>
