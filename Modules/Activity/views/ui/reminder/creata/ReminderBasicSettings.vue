<template>
  <div class="reminder-basic-settings">
    <el-form :model="localFormData" ref="formRef" label-position="top" :rules="rules">
      <el-form-item :label="$t('Activity.reminder_basic.fields.name')" prop="name" required>
        <el-input v-model="localFormData.name" :placeholder="$t('Activity.reminder_basic.fields.name_placeholder')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('Activity.reminder_basic.fields.type')" prop="type" required>
        <el-checkbox-group v-model="localFormData.type">
          <el-checkbox label="email">{{ $t('Activity.reminder_basic.fields.email') }}</el-checkbox>
          <el-checkbox label="sms">{{ $t('Activity.reminder_basic.fields.sms') }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <div class="recipients-section">
        <div class="recipients-header">
          <label class="form-label">{{ $t('Activity.reminder_basic.fields.recipients') }}</label>
          <el-button type="primary" link @click="addRecipient">
            <el-icon><Plus /></el-icon>{{ $t('Activity.reminder_basic.fields.add_recipient') }}
          </el-button>
        </div>
        
        <div v-for="(item, index) in localFormData.recipients" :key="index" class="recipient-item">
          <el-row :gutter="20">
            <el-col :span="10">
              <el-form-item :label="$t('Activity.reminder_basic.fields.recipient_email', { index: index + 1 })" :prop="`recipients.${index}.email`" required>
                <el-input v-model="item.email" :placeholder="$t('Activity.reminder_basic.fields.recipient_email_placeholder')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item :label="$t('Activity.reminder_basic.fields.recipient_phone', { index: index + 1 })" :prop="`recipients.${index}.phone`" required>
                <el-input v-model="item.phone" :placeholder="$t('Activity.reminder_basic.fields.recipient_phone_placeholder')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4" class="recipient-actions">
              <el-button 
                v-if="localFormData.recipients.length > 1" 
                type="danger" 
                link 
                @click="deleteRecipient(index)"
              >
                <el-icon><Delete /></el-icon>{{ $t('Activity.reminder_basic.buttons.delete') }}
              </el-button>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="op-box">
      <el-button @click="handleCancel">{{ $t('Activity.reminder_basic.buttons.cancel') }}</el-button>
      <el-button type="primary" @click="handleNext" :loading="loading">
        {{ $t('Activity.reminder_basic.buttons.next') }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import type { ReminderRecipient } from '../../../services/reminderService'

const { t } = useI18n()

interface FormData {
  name: string
  type: string[]
  recipients: ReminderRecipient[]
}

const props = defineProps<{
  modelValue: FormData
  loading: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: FormData]
  'next-step': []
  'cancel': []
}>()

const formRef = ref()

// 本地表单数据 - 使用深拷贝避免引用污染
const localFormData = reactive<FormData>({
  name: props.modelValue?.name || '',
  type: props.modelValue?.type || [],
  recipients: props.modelValue?.recipients || [{ email: '', phone: '' }]
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: t('Activity.reminder_basic.validation.name_required'), trigger: 'blur' },
    { min: 2, max: 100, message: t('Activity.reminder_basic.validation.name_length'), trigger: 'blur' }
  ],
  type: [
    { required: true, message: t('Activity.reminder_basic.validation.type_required'), trigger: 'change' }
  ],
  'recipients.*.email': [
    { required: true, message: t('Activity.reminder_basic.validation.email_required'), trigger: 'blur' },
    { type: 'email', message: t('Activity.reminder_basic.validation.email_format'), trigger: 'blur' }
  ],
  'recipients.*.phone': [
    { required: true, message: t('Activity.reminder_basic.validation.phone_required'), trigger: 'blur' },
    { pattern: /^[\+]?[0-9\s\-\(\)]{8,20}$/, message: t('Activity.reminder_basic.validation.phone_format'), trigger: 'blur' }
  ]
}

// 添加接收者
const addRecipient = () => {
  localFormData.recipients.push({ email: '', phone: '' })
}

// 删除接收者
const deleteRecipient = (index: number) => {
  if (localFormData.recipients.length > 1) {
    localFormData.recipients.splice(index, 1)
  }
}

// 表单验证
const validate = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    return false
  }
}

// 处理下一步
const handleNext = async () => {
  const isValid = await validate()
  if (!isValid) {
    ElMessage.error(t('Activity.reminder_basic.validation.form_incomplete'))
    return
  }
  
  emit('next-step')
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}

// 防止循环更新的标志
let isUpdatingFromProps = false

// 深度比较两个对象是否相等
const isDataEqual = (a: FormData, b: FormData): boolean => {
  if (a.name !== b.name) return false
  if (JSON.stringify(a.type) !== JSON.stringify(b.type)) return false
  if (JSON.stringify(a.recipients) !== JSON.stringify(b.recipients)) return false
  return true
}

// 监听props变化，同步到本地数据
watch(() => props.modelValue, (newValue) => {
  if (newValue && !isUpdatingFromProps) {
    // 检查数据是否真的发生了变化
    if (!isDataEqual(newValue, localFormData)) {
      isUpdatingFromProps = true
      
      // 使用深拷贝避免引用污染，只更新存在的字段
      localFormData.name = newValue.name ?? localFormData.name
      localFormData.type = newValue.type ? [...newValue.type] : localFormData.type
      localFormData.recipients = newValue.recipients ? 
        newValue.recipients.map(r => ({ ...r })) : localFormData.recipients
      
      // 重置标志
      nextTick(() => {
        isUpdatingFromProps = false
      })
    }
  }
}, { immediate: true, deep: true })

// 监听本地数据变化，同步到父组件 - 防抖处理避免频繁更新
let updateTimer: ReturnType<typeof setTimeout> | null = null
watch(localFormData, (newValue) => {
  // 只有在不是从props更新时才发送到父组件
  if (!isUpdatingFromProps) {
    if (updateTimer) clearTimeout(updateTimer)
    updateTimer = setTimeout(() => {
      emit('update:modelValue', { 
        name: newValue.name,
        type: [...newValue.type],
        recipients: newValue.recipients.map(r => ({ ...r }))
      })
    }, 100)
  }
}, { deep: true })

// 暴露验证方法
defineExpose({
  validate
})
</script>

<style scoped>
.reminder-basic-settings {
  padding: 20px 0;
}

.recipients-section {
  margin: 24px 0;
}

.recipients-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.recipient-item {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.recipient-actions {
  display: flex;
  align-items: end;
  padding-bottom: 24px;
  justify-content: center;
}

.op-box {
  display: flex;
  justify-content: end;
  gap: 16px;
  padding: 30px 0;
  margin-top: 30px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-checkbox-group) {
  display: flex;
  gap: 24px;
}
</style> 