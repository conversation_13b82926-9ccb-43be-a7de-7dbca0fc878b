<template>
  <div class="reminder-list-container">
    <el-table :data="reminderList" style="width: 100%;" v-loading="loading">
      <template #empty>
        <el-empty :description="$t('Activity.reminder_list.empty.description')" image-size="100px" />
      </template>
      <el-table-column 
        prop="name" 
        :label="$t('Activity.reminder_list.table.name')" 
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column 
        prop="recipient" 
        :label="$t('Activity.reminder_list.table.recipient')" 
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column 
        prop="reminder_type" 
        :label="$t('Activity.reminder_list.table.reminder_type')" 
        width="120"
      />
      <el-table-column 
        prop="send_time" 
        :label="$t('Activity.reminder_list.table.send_time')" 
        width="180"
      />
      <el-table-column 
        prop="status" 
        :label="$t('Activity.reminder_list.table.status')" 
        width="100"
      >
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column 
        prop="is_active" 
        :label="$t('Activity.reminder_list.table.is_active')" 
        width="100"
      >
        <template #default="{ row }">
          <el-switch 
            v-model="row.is_active" 
            @change="handleStatusChange(row)"
            :loading="row.statusLoading"
          />
        </template>
      </el-table-column>
      <el-table-column prop="created_at" :label="$t('Activity.reminder_list.table.created_at')" width="180"></el-table-column>
      <el-table-column fixed="right" :label="$t('Activity.reminder_list.table.actions')" width="150">
        <template #default="scope">
          <div class="operate-btn-box">
            <div class="operate-btn" @click="handleEdit(scope.row)">
              <el-icon>
                <img :src="$asset('Cms/Asset/EditIcon.png')" alt="" />
              </el-icon>
            </div>
            <el-button class="operate-btn del-btn" type="text" @click="handleDelete(scope.row)">
              <el-icon>
                <img :src="$asset('Cms/Asset/DeleteIcon.png')" alt="" />
              </el-icon>
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 删除确认弹窗 -->
    <el-dialog
      v-model="deleteDialogVisible"
      :title="$t('Activity.reminder_list.delete_dialog.title')"
      class="el-dialog-common-cls"
      width="400px"
    >
      <div class="delete-content">
        <p>{{ $t('Activity.reminder_list.delete_dialog.content', { name: currentDeleteItem?.name }) }}</p>
        <p class="warning-text">{{ $t('Activity.reminder_list.delete_dialog.warning') }}</p>
      </div>
      <template #footer>
        <div class="flex justify-center">
          <el-button class="el-button-default" @click="deleteDialogVisible = false">
            {{ $t('Activity.reminder_list.delete_dialog.cancel') }}
          </el-button>
          <el-button class="button-no-border" type="danger" @click="confirmDelete" :loading="deleteLoading">
            {{ deleteLoading ? $t('Activity.reminder_list.delete_dialog.deleting') : $t('Activity.reminder_list.delete_dialog.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { CopyDocument } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { 
  reminderService, 
  type ReminderItem, 
  type ReminderListResponse,
  type ReminderListParams 
} from "../../services/reminderService"

const { t } = useI18n()

// Props - 接收活动ID和分页信息
interface Props {
  activityId?: string | number
  pagination?: {
    page: number
    pageSize: number
    total: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  activityId: '',
  pagination: () => ({ page: 1, pageSize: 10, total: 0 })
})

// Emits - 发送事件
const emit = defineEmits<{
  'update-total': [total: number]
}>()

// 扩展导入的 ReminderItem 类型以适配表格显示
interface TableReminderItem extends ReminderItem {
  // 添加表格显示需要的字段
  recipient?: string
  reminder_type?: string
  send_time?: string
  is_active?: boolean
  statusLoading?: boolean
}

const router = useRouter()
const route = useRoute()

// 表格数据
const reminderList = ref<TableReminderItem[]>([])
const loading = ref(false)

// 删除相关
const deleteDialogVisible = ref(false)
const deleteLoading = ref(false)
const currentDeleteItem = ref<TableReminderItem | null>(null)

// 状态处理函数
const getStatusType = (status: string | number) => {
  // 先将数字状态映射为字符串状态
  const statusString = getStatusString(status)
  const map: Record<string, string> = {
    'pending': 'info',
    'sent': 'success',
    'failed': 'danger',
    'scheduled': 'warning'
  }
  return map[statusString] || 'info'
}

// 将数字状态映射为字符串状态
const getStatusString = (status: string | number): string => {
  const statusMap: Record<number, string> = {
    0: 'pending',    // 待发送
    1: 'sent',       // 已发送
    2: 'failed',     // 发送失败
    3: 'scheduled'   // 已排程
  }
  
  if (typeof status === 'number') {
    return statusMap[status] || 'unknown'
  }
  
  // 如果已经是字符串，直接返回
  return status || 'unknown'
}

const getStatusText = (status: string | number) => {
  const statusString = getStatusString(status)
  const statusKey = `Activity.reminder_list.status.${statusString}`
  return t(statusKey) || t('Activity.reminder_list.status.unknown')
}

// 操作方法
const handleEdit = (row: TableReminderItem) => {
  router.push({
    name: 'ReminderEdit',
    params: {
      id: row.id.toString()
    },
    query: {
      activityId: props.activityId?.toString() || route.params.id
    }
  })
}

// 新增创建方法
const handleCreate = () => {
  router.push({
    name: 'ReminderCreate',
    query: {
      activityId: props.activityId?.toString() || route.params.id
    }
  })
}

// 删除
const handleDelete = (row: TableReminderItem) => {
  currentDeleteItem.value = row
  deleteDialogVisible.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!currentDeleteItem.value) return
  
  deleteLoading.value = true
  try {
    await reminderService.delete(currentDeleteItem.value.id)
    ElMessage.success(t('Activity.reminder_list.messages.delete_success'))
    deleteDialogVisible.value = false
    currentDeleteItem.value = null
    
    // 重新加载数据
    await getTableDataList()
  } catch (error) {
    ElMessage.error(t('Activity.reminder_list.messages.delete_failed'))
  } finally {
    deleteLoading.value = false
  }
}



// 处理启用状态切换
const handleStatusChange = async (row: TableReminderItem) => {
  if (!row.statusLoading) {
    row.statusLoading = true
  }
  try {
    // 根据实际API，这里可能需要调整为 status 而不是 is_active
    const status = row.is_active ? 1 : 0
    await reminderService.updateStatus(row.id, status)
    ElMessage.success(row.is_active ? t('Activity.reminder_list.messages.enable_success') : t('Activity.reminder_list.messages.disable_success'))
  } catch (error) {
    // 如果失败，恢复原状态
    if (row.is_active !== undefined) {
      row.is_active = !row.is_active
    }
    ElMessage.error(t('Activity.reminder_list.messages.status_update_failed'))
  } finally {
    row.statusLoading = false
  }
}

// 查询列表
const getTableDataList = async () => {
  loading.value = true
  try {
    const activityId = props.activityId || route.params.id
    // 确保 activity_id 是 string 或 number 类型
    const params: ReminderListParams = {
      activity_id: Array.isArray(activityId) ? activityId[0] : activityId,
      page: props.pagination?.page || 1,
      per_page: props.pagination?.pageSize || 10
    }
    const res = await reminderService.getList(params)
    
    // 根据实际API响应结构处理数据
    const responseData = res.data.data || res.data
    const items = responseData.data || responseData.items || responseData || []
    
    // 将API数据转换为表格显示数据
    reminderList.value = items.map((item: ReminderItem) => ({
      ...item,
      // 根据API返回的数据结构映射到表格显示字段
      recipient: item.recipients?.map(r => r.email).join(', ') || '',
      reminder_type: item.type?.join(', ') || '',
      send_time: item.created_at || '',
      is_active: item.status === 1,
      statusLoading: false
    }))
    
    // 更新总数到父组件
    const total = responseData.total || items.length || 0
    emit('update-total', total)
  } catch (error) {
    console.error('获取提醒列表失败:', error)
    ElMessage.error(t('Activity.reminder_list.messages.get_list_failed'))
  } finally {
    loading.value = false
  }
}

// 暴露刷新方法给父组件
const refresh = () => {
  getTableDataList()
}

// 暴露方法
defineExpose({
  refresh,
  handleCreate
})

// 监听分页变化
watch(
  () => props.pagination,
  (newPagination, oldPagination) => {
    if (newPagination && oldPagination) {
      if (
        newPagination.page !== oldPagination.page ||
        newPagination.pageSize !== oldPagination.pageSize
      ) {
        getTableDataList()
      }
    }
  },
  { deep: true }
)

onMounted(() => {
  getTableDataList()
})
</script>

<style lang="scss" scoped>
.reminder-list-container {
  width: 100%;
  
  // 操作按钮样式
  .operate-btn-box {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .operate-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #dcdfe6;
    background: #fff;

    &:hover {
      border-color: #409eff;
      background: #ecf5ff;
      
      .el-icon {
        color: #409eff;
      }
    }

    .el-icon {
      font-size: 16px;
      color: #606266;
    }
  }

  .del-btn {
    &:hover {
      border-color: #f56c6c;
      background: #fef0f0;
      
      .el-icon {
        color: #f56c6c;
      }
    }
  }
}

// 删除弹窗样式
.delete-content {
  text-align: center;
  padding: 20px 0;
  
  p {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #303133;
  }
  
  .warning-text {
    font-size: 14px;
    color: #f56c6c;
  }
}

.flex {
  display: flex;
  gap: 12px;
}

.justify-center {
  justify-content: center;
}
</style> 