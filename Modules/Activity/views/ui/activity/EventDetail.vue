<template>
  <div class="bwms-module">
    <div class="module-header">
      <div class="btn-list">
        <el-button  @click="handleBack">返回</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </div>

    <div class="module-con">
      <div class="box">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="活动名称">{{ event?.name }}</el-descriptions-item>
          <el-descriptions-item label="活动类型">
            {{ event?.type === 'online' ? '线上活动' : '线下活动' }}
          </el-descriptions-item>
          <el-descriptions-item label="活动时间">
            {{ formatDateTime(event?.startTime) }} 至 {{ formatDateTime(event?.endTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="活动地点" v-if="event?.type === 'offline'">
            {{ event?.location }}
          </el-descriptions-item>
          <template v-else>
            <el-descriptions-item label="线上平台">{{ event?.platform }}</el-descriptions-item>
            <el-descriptions-item label="会议链接">{{ event?.meetingUrl }}</el-descriptions-item>
          </template>
          <el-descriptions-item label="参与人数">
            {{ event?.currentParticipants }}/{{ event?.maxParticipants }}
          </el-descriptions-item>
          <el-descriptions-item label="活动状态">
            <el-tag :type="getStatusType(event?.status)">{{ getStatusText(event?.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="活动描述" :span="2">
            {{ event?.description }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 票务信息 -->
        <div class="section">
          <div class="section-header">
            <h3>票务信息</h3>
            <el-button type="primary" link @click="handleTickets">查看详情</el-button>
          </div>
          <el-table :data="tickets" stripe>
            <el-table-column prop="name" label="票种名称" />
            <el-table-column prop="price" label="价格">
              <template #default="{ row }">
                {{ row.price === 0 ? '免费' : '¥' + row.price }}
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" />
            <el-table-column prop="soldQuantity" label="已售" />
            <el-table-column label="销售时间">
              <template #default="{ row }">
                {{ formatDateTime(row.startTime) }} 至 {{ formatDateTime(row.endTime) }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 参与者信息 -->
        <div class="section">
          <div class="section-header">
            <h3>参与者信息</h3>
            <el-button type="primary" link @click="handleParticipants">查看详情</el-button>
          </div>
          <el-table :data="participants" stripe>
            <el-table-column prop="name" label="姓名" />
            <el-table-column prop="email" label="邮箱" />
            <el-table-column prop="phone" label="电话" />
            <el-table-column prop="ticketCode" label="票号" />
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="getParticipantStatusType(row.status)">
                  {{ getParticipantStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="checkedIn" label="签到状态">
              <template #default="{ row }">
                <el-tag :type="row.checkedIn ? 'success' : 'info'">
                  {{ row.checkedIn ? '已签到' : '未签到' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { IEvent, ITicket, IParticipant } from '../../types'
import { eventService, ticketService, participantService } from '../../services/eventService'

const router = useRouter()
const route = useRoute()
const eventId = Number(route.params.id)

const event = ref<IEvent>()
const tickets = ref<ITicket[]>([])
const participants = ref<IParticipant[]>([])

// 获取活动详情
const fetchEventDetail = async () => {
  try {
    const { data } = await eventService.getDetail(eventId)
    event.value = data
  } catch (error) {
    console.error('获取活动详情失败:', error)
    ElMessage.error('获取活动详情失败')
  }
}

// 获取票务列表
const fetchTickets = async () => {
  try {
    const { data } = await ticketService.getList(eventId, { page: 1, limit: 9999 })
    tickets.value = data.list
  } catch (error) {
    console.error('获取票务列表失败:', error)
    ElMessage.error('获取票务列表失败')
  }
}

// 获取参与者列表
const fetchParticipants = async () => {
  try {
    const { data } = await participantService.getList(eventId, { page: 1, limit: 9999 })
    participants.value = data.list
  } catch (error) {
    console.error('获取参与者列表失败:', error)
    ElMessage.error('获取参与者列表失败')
  }
}

// 格式化日期时间
const formatDateTime = (datetime: string | undefined) => {
  if (!datetime) return '--'
  return new Date(datetime).toLocaleString()
}

// 获取活动状态类型
const getStatusType = (status: string | undefined) => {
  switch (status) {
    case 'pending':
      return 'info'
    case 'ongoing':
      return 'success'
    case 'finished':
      return ''
    default:
      return 'info'
  }
}

// 获取活动状态文本
const getStatusText = (status: string | undefined) => {
  switch (status) {
    case 'pending':
      return '未开始'
    case 'ongoing':
      return '进行中'
    case 'finished':
      return '已结束'
    default:
      return '--'
  }
}

// 获取参与者状态类型
const getParticipantStatusType = (status: string) => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'confirmed':
      return 'success'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取参与者状态文本
const getParticipantStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '待确认'
    case 'confirmed':
      return '已确认'
    case 'cancelled':
      return '已取消'
    default:
      return '--'
  }
}

// 处理编辑
const handleEdit = () => {
  router.push('/activity/event/' + eventId + '/edit')
}

// 处理查看票务
const handleTickets = () => {
  router.push('/activity/ticket?eventId=' + eventId)
}

// 处理查看参与者
const handleParticipants = () => {
  router.push('/activity/participant?eventId=' + eventId)
}

// 处理返回
const handleBack = () => {
  router.back()
}

onMounted(() => {
  fetchEventDetail()
  fetchTickets()
  fetchParticipants()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;

      .section {
        margin-top: 30px;

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style> 