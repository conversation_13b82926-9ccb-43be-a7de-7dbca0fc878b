<template>
  <el-dialog
    v-model="props.visible"
    :title="props.mode === 'create' ? $t('Activity.ticket.dialog.create_title') : $t('Activity.ticket.dialog.edit_title')"
    width="800px"
    :close-on-click-modal="false"
    :show-close="true"
    @close="handleCancel"
    @closed="handleDialogClosed"
  >
    <el-form :model="form" label-position="top" :rules="rules" ref="formRef">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('Activity.ticket.form.name')" prop="name" required>
            <el-input 
              v-model="form.name" 
              :placeholder="$t('Activity.ticket.form.name_placeholder')"
              :disabled="props.mode === 'edit'"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('Activity.ticket.form.code')" prop="code" required>
            <el-input 
              v-model="form.code" 
              :placeholder="$t('Activity.ticket.form.code_placeholder')"
              :disabled="props.mode === 'edit'"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item :label="$t('Activity.ticket.form.quota')" prop="quota" required>
        <el-input 
          v-model="form.quota" 
          type="number" 
          :placeholder="$t('Activity.ticket.form.quota_placeholder')"
        ></el-input>
      </el-form-item>
      
      <el-form-item :label="$t('Activity.ticket.form.groups')" prop="groups" required>
        <el-select 
          v-model="selectedGroupIds" 
          :placeholder="$t('Activity.ticket.form.groups_placeholder')" 
          multiple
          style="width: 100%"
          @change="handleGroupChange"
        >
          <el-option 
            v-for="item in groupList" 
            :key="item.id" 
            :label="item.name" 
            :value="item.id"
          >
            <div>
              <div style="font-weight: bold;">{{ item.name }}</div>
              <div style="font-size: 12px; color: #999;">
                {{ item.description || $t('Activity.common.no_data') }}
              </div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item :label="$t('Activity.ticket.form.is_external_sale')" prop="is_external_sale" required>
        <el-radio-group v-model="form.is_external_sale" :disabled="props.mode === 'edit'">
          <el-radio :label="1">{{ $t('Activity.ticket.form.yes') }}</el-radio>
          <el-radio :label="0">{{ $t('Activity.ticket.form.no') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item :label="$t('Activity.ticket.form.is_fee_required')" prop="is_fee_required" required>
        <el-radio-group v-model="form.is_fee_required" :disabled="props.mode === 'edit'">
          <el-radio :label="1">{{ $t('Activity.ticket.form.yes') }}</el-radio>
          <el-radio :label="0">{{ $t('Activity.ticket.form.no') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item :label="$t('Activity.ticket.form.subtypes')" prop="subtypes" required>        
        <div v-for="(ticket, index) in form.subtypes" :key="index" class="ticket-type-item">
          <el-row :gutter="10" class="mb-10">
            <el-col :span="4">
              <el-input 
                v-model="ticket.name" 
                :placeholder="$t('Activity.ticket.form.subtype.name_placeholder')"
              ></el-input>
            </el-col>
            <el-col :span="6">
              <el-input 
                v-model="ticket.price" 
                type="number" 
                :placeholder="$t('Activity.ticket.form.subtype.price_placeholder')"
              ></el-input>
            </el-col>
            <el-col :span="2">
              <el-input 
                v-model="ticket.currency" 
                :placeholder="$t('Activity.ticket.form.subtype.currency_placeholder')"
                :disabled="true"
              ></el-input>
            </el-col>
            <el-col :span="10">
              <el-date-picker 
                v-model="ticket.dateRange"
                type="daterange" 
                :range-separator="$t('Activity.ticket.form.subtype.date_separator')"
                :start-placeholder="$t('Activity.ticket.form.subtype.date_start_placeholder')"
                :end-placeholder="$t('Activity.ticket.form.subtype.date_end_placeholder')"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                style="width: 100%;"
              ></el-date-picker>
            </el-col>
            <el-col :span="2">
              <span
                class="delete-btn"
                @click="removeSubtype(index)"
                v-if="form.subtypes.length > 1"
              >
                <el-icon>
                  <Delete />
                </el-icon>
              </span>
            </el-col>
          </el-row>
        </div>
        
        <div class="add-subtype-container">
          <el-button 
            type="primary" 
            :icon="Plus" 
            @click="addSubtype"
            class="add-subtype-btn"
          >
            {{ $t('Activity.ticket.form.add_subtype') }}
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="isCreating">
          {{ $t('Activity.ticket.button.cancel') }}
        </el-button>
        <el-button type="primary" @click="handleCreate" :loading="isCreating">
          {{ isCreating 
            ? (props.mode === 'edit' 
              ? $t('Activity.ticket.dialog.editing') 
              : $t('Activity.ticket.dialog.creating'))
            : (props.mode === 'edit' 
              ? $t('Activity.ticket.button.edit') 
              : $t('Activity.ticket.button.create')) 
          }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
  
<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { InfoFilled, Plus, Delete } from '@element-plus/icons-vue'
import { groupService } from '../../../services/activityervice'
import { useI18n } from 'vue-i18n'

const props = defineProps<{
  visible: boolean
  mode?: 'create' | 'edit'
  ticketData?: any
  currentLocalization?: any
}>()

const emit = defineEmits(['update:visible', 'cancel', 'create'])

const { t } = useI18n()

const groupList = ref<any[]>([])
const formRef = ref<FormInstance>()
const selectedGroupIds = ref<number[]>([]) // 用于群组下拉选择的ID数组
const isCreating = ref(false) // 创建状态

const form = ref({
  name: '',
  code: '',
  groups: [] as any[], // 存储完整的群组对象数组
  quota: '', // 数量限制
  is_external_sale: '',
  is_fee_required: '',
  subtypes: [
    {
      name: t('Activity.ticket.form.subtype.early_bird'),
      price: 10,
      currency: 'HKD',
      dateRange: []
    },
    {
      name: t('Activity.ticket.form.subtype.regular'),
      price: 15,
      currency: 'HKD',
      dateRange: null
    }
  ]
})

const rules = ref<FormRules>({
  name: [{ required: true, message: t('Activity.ticket.validation.name_required'), trigger: 'blur' }],
  code: [{ required: true, message: t('Activity.ticket.validation.code_required'), trigger: 'blur' }],
  quota: [{ required: true, message: t('Activity.ticket.validation.quota_required'), trigger: 'blur' }],
  groups: [{ required: true, message: t('Activity.ticket.validation.groups_required'), trigger: 'change' }],
  is_external_sale: [{ required: true, message: t('Activity.ticket.validation.external_sale_required'), trigger: 'change' }],
  is_fee_required: [{ required: true, message: t('Activity.ticket.validation.fee_required'), trigger: 'change' }]
})

// 监听本地化区域变化，更新货币
watch(() => props.currentLocalization, (newLocalization) => {
  if (newLocalization && newLocalization.currency_symbol) {
    // 更新所有subtypes的货币
    form.value.subtypes = form.value.subtypes.map(subtype => ({
      ...subtype,
      currency: newLocalization.currency_symbol
    }))
  }
}, { immediate: true })

// 监听ticketData变化，在编辑模式下回填数据
watch(() => props.ticketData, (newData) => {
  if (props.mode === 'edit' && newData) {
    // 回填数据
    form.value.name = newData.name || ''
    form.value.code = newData.code || ''
    form.value.is_external_sale = newData.is_external_sale
    form.value.is_fee_required = newData.is_fee_required
    form.value.quota = '' // 数量限制每次都要重新输入
    
    // 回填群组数据
    if (newData.groups) {
      form.value.groups = newData.groups
      selectedGroupIds.value = newData.groups.map((group: any) => group.id)
    }
    
    // 回填细分类型
    if (newData.subtypes && newData.subtypes.length > 0) {
      form.value.subtypes = newData.subtypes.map((subtype: any) => ({
        name: subtype.name || '',
        price: subtype.price || 0,
        currency: subtype.currency || 'HKD',
        dateRange: subtype.start_date && subtype.end_date ? [subtype.start_date, subtype.end_date] : []
      }))
    }
  }
}, { immediate: true })

// 处理群组选择变化
const handleGroupChange = (selectedIds: number[]) => {
  // 根据选中的ID从groupList中找到完整的群组对象
  form.value.groups = selectedIds.map(id => {
    return groupList.value.find(group => group.id === id)
  }).filter((group): group is any => group !== undefined) // 过滤掉undefined
}

const handleCancel = () => {
  isCreating.value = false // 重置创建状态
  emit('update:visible', false)
  emit('cancel')
}

// 处理对话框完全关闭事件
const handleDialogClosed = () => {
  isCreating.value = false // 重置创建状态
  
  // 重置表单数据
  if (props.mode === 'create') {
    const defaultCurrency = props.currentLocalization?.currency_symbol || 'HKD'
    form.value = {
      name: '',
      code: '',
      groups: [],
      quota: '',
      is_external_sale: '',
      is_fee_required: '',
      subtypes: [
        {
          name: t('Activity.ticket.form.subtype.early_bird'),
          price: 10,
          currency: defaultCurrency,
          dateRange: []
        },
        {
          name: t('Activity.ticket.form.subtype.regular'),
          price: 15,
          currency: defaultCurrency,
          dateRange: null
        }
      ]
    }
    selectedGroupIds.value = []
  } else {
    // 编辑模式只重置数量限制
    form.value.quota = ''
  }
}

const handleCreate = async () => {
  if (!formRef.value || isCreating.value) return
  
  try {
    isCreating.value = true
    
    // 在编辑模式下，只验证数量限制字段
    if (props.mode === 'edit') {
      if (!form.value.quota) {
        ElMessage.error('請輸入數量限制')
        isCreating.value = false
        return
      }
      
      // 直接返回数量限制数据
      emit('create', {
        quota: Number(form.value.quota)
      })
      return
    }
    
    // 创建模式下的完整验证
    await formRef.value.validate((valid) => {
      if (valid) {
        // 确保数值类型正确
        const processedForm = {
          ...form.value,
          quota: Number(form.value.quota), // 确保数量是数字类型
          is_external_sale: Number(form.value.is_external_sale), // 确保是数字类型
          is_fee_required: Number(form.value.is_fee_required), // 确保是数字类型
          subtypes: form.value.subtypes.map(subtype => ({
            ...subtype,
            price: Number(subtype.price), // 确保价格是数字类型
            start_date: subtype.dateRange && subtype.dateRange[0] ? subtype.dateRange[0] : '',
            end_date: subtype.dateRange && subtype.dateRange[1] ? subtype.dateRange[1] : '',
            dateRange: undefined // 移除临时的dateRange字段
          }))
        }
        
        emit('create', processedForm)
      } else {
        isCreating.value = false
      }
    })
  } catch (error) {
    console.error('Form validation error:', error)
    isCreating.value = false
  }
}

// 添加细分类型
const addSubtype = () => {
  const defaultCurrency = props.currentLocalization?.currency_symbol || 'HKD'
  form.value.subtypes.push({
    name: '',
    price: 0,
    currency: defaultCurrency,
    dateRange: []
  })
}

// 删除细分类型
const removeSubtype = (index: number) => {
  if (form.value.subtypes.length > 1) {
    form.value.subtypes.splice(index, 1)
  }
}

// 获取 群组
const getGroupList = () => {
  groupService.getList({ page: 1, per_page: 99999, limit: 99999 })
  .then((res: any) => {
    if (res.status === 200 && res?.data?.code === 200) {
      console.log(res.data.data)
      groupList.value = res.data.data?.items || []
    }
  })
  .catch((error: any) => {
    console.error('Get group list error:', error)
  })
}

onMounted(() => {
  getGroupList()
})

// 暴露给父组件
defineExpose({
  form
})
</script>

<script lang="ts">
export default {
  name: 'AddPiaoWu'
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.ticket-type-item {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.mb-10 {
  margin-bottom: 10px;
}

.mt-10 {
  margin-top: 10px;
}


.add-subtype-container {
  margin-top: 10px;
  text-align: left;
}

.add-subtype-btn {
  border-radius: 4px;
}

.info-icon {
  margin-left: 5px;
  color: #909399;
  font-size: 16px;
  vertical-align: middle;
}

:deep(.el-dialog__header) {
  background: #f8f9fa;
}
.delete-btn {
  display: block;
  width: 30px;
  height: 30px;
  padding: 0;
  border-radius: 50%;
  background-color: #f56c6c;
  color: #fff;
  cursor: pointer;
  text-align: center;
  line-height: 34px;
  &:hover {
    background-color: #e65d5d;
  }
}
:deep(.el-col-2.is-guttered){
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
