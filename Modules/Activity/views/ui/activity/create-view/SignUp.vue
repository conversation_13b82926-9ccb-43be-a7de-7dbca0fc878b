<template>
  <div class="sign-up-box">
    <el-form :model="formData" ref="formRef" :rules="rules" label-position="top" label-width="120px">
      
      <!-- 活動信息卡片 -->
      <div class="signup-card">
        <div class="card-header">
          <h3 class="card-title">
            <el-icon><Document /></el-icon>
            {{ $t('Activity.signup.card.activity_info') }}
          </h3>
        </div>
        <div class="card-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="$t('Activity.signup.form.registration_deadline')" prop="registration_deadline">
                <el-date-picker 
                  style="width: 100%;" 
                  format="YYYY-MM-DD HH:mm" 
                  value-format="YYYY-MM-DD HH:mm"
                  v-model="formData.registration_deadline" 
                  type="datetime" 
                  :placeholder="$t('Activity.signup.placeholder.registration_deadline')" 
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('Activity.signup.form.target_participants')">
                <el-input-number v-model="formData.target_participants" style="width: 100%;" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item :label="$t('Activity.signup.form.registration_limit')">
            <el-radio-group v-model="formData.registration_limit_type">
              <el-radio :value="0">{{ $t('Activity.signup.options.unlimited') }}</el-radio>
              <el-radio :value="1">
                <span>{{ $t('Activity.signup.options.set_limit') }}</span>
                <el-input-number v-model="formData.registration_limit" style="margin-left: 10px;" />
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>

      <!-- 活動組織者卡片 -->
      <div class="signup-card">
        <div class="card-header">
          <h3 class="card-title">{{ $t('Activity.signup.card.organizer_settings') }}</h3>
        </div>
        <div class="card-content">
          <el-form-item :label="$t('Activity.signup.form.registration_type')">
            <el-radio-group v-model="formData.registration_type">
              <el-radio :value="0">{{ $t('Activity.signup.options.unlimited') }}</el-radio>
              <el-radio :value="1">
                <div style="margin-top: 10px;">
                  <el-select 
                    style="width: 100%;" 
                    v-model="selectedGroupIds" 
                    :placeholder="$t('Activity.signup.placeholder.groups')" 
                    multiple
                    :loading="groupListLoading"
                    :loading-text="$t('Activity.signup.loading.groups')"
                    @change="handleGroupChange"
                  >
                    <el-option 
                      v-for="item in groupList" 
                      :key="item.id" 
                      :label="item.name" 
                      :value="item.id"
                    >
                      <div>
                        <div style="font-weight: bold;">{{ item.name }}</div>
                        <div style="font-size: 12px; color: #999;">
                          {{ item.description || $t('Activity.signup.group_details.no_description') }}
                        </div>
                      </div>
                    </el-option>
                    <template #footer>
                      <el-button text bg size="small" @click="onAddOption">
                        {{ $t('Activity.signup.button.new_group') }}
                      </el-button>
                    </template>
                  </el-select>
                </div>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          
          <!-- 显示已选择的群组 -->
          <div v-if="formData.groups.length > 0" class="selected-groups-display">
            <div class="selected-title">{{ $t('Activity.signup.selected.groups_title') }}</div>
            <div class="selected-groups-container">
              <div 
                v-for="group in formData.groups" 
                :key="group.id"
                class="group-card"
                @click="editGroup(group.id)"
              >
                <div class="group-header">
                  <div class="group-info">
                    <span class="group-name">{{ group.name }}</span>
                    <span class="group-code">({{ group.code }})</span>
                  </div>
                  <div class="group-actions">
                    <el-button 
                      type="primary" 
                      text 
                      size="small" 
                      @click.stop="editGroup(group.id)"
                      class="edit-btn"
                    >
                      <el-icon><Edit /></el-icon>
                      {{ $t('Activity.signup.button.edit') }}
                    </el-button>
                    <el-button 
                      type="danger" 
                      text 
                      size="small" 
                      @click.stop="removeGroup(group.id)"
                      class="remove-btn"
                    >
                      <el-icon><Delete /></el-icon>
                      {{ $t('Activity.signup.button.delete') }}
                    </el-button>
                  </div>
                </div>
                <div class="group-details">
                  <div class="group-detail-item">
                    <span class="detail-label">{{ $t('Activity.signup.group_details.member_limit') }}</span>
                    <span class="detail-value">{{ group.max_members || $t('Activity.signup.group_details.not_set') }}人</span>
                  </div>
                  <div class="group-detail-item">
                    <span class="detail-label">{{ $t('Activity.signup.group_details.can_register') }}</span>
                    <el-tag :type="Number(group.can_register) === 1 ? 'success' : 'danger'" size="small">
                      {{ Number(group.can_register) === 1 ? $t('Activity.signup.options.yes') : $t('Activity.signup.options.no') }}
                    </el-tag>
                  </div>
                  <div class="group-detail-item">
                    <span class="detail-label">{{ $t('Activity.signup.group_details.registration_time') }}</span>
                    <span class="detail-value">{{ formatDateRange(group.start_time, group.end_time) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 名額分配卡片 -->
      <div class="signup-card">
        <div class="card-header">
          <h3 class="card-title">{{ $t('Activity.signup.card.quota_allocation') }}</h3>
        </div>
        <div class="card-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('Activity.signup.form.public_limit')">
                <el-input-number v-model="formData.public_limit" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('Activity.signup.form.hidden_limit')">
                <el-input-number v-model="formData.hidden_limit" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('Activity.signup.form.reserved_limit')">
                <el-input-number v-model="formData.reserved_limit" style="width: 100%;" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 是否需要收費卡片 -->
      <div class="signup-card">
        <div class="card-header">
          <h3 class="card-title">{{ $t('Activity.signup.card.fee_settings') }}</h3>
        </div>
        <div class="card-content">
          <el-form-item :label="$t('Activity.signup.form.is_fee_required')">
            <el-radio-group v-model="formData.is_fee_required">
              <el-radio :value="1">{{ $t('Activity.signup.options.yes') }}</el-radio>
              <el-radio :value="0">{{ $t('Activity.signup.options.no') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <template v-if="formData.is_fee_required === 1">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('Activity.signup.form.invoice_prefix')">
                  <el-input v-model="formData.invoice_prefix" :placeholder="$t('Activity.signup.placeholder.invoice_prefix')" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('Activity.signup.form.target_income')">
                  <el-input-number v-model="formData.target_income" style="width: 100%;" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item :label="$t('Activity.signup.form.ticket_category')">
              <el-select 
                style="width: 100%;" 
                v-model="selectedTicketIds" 
                :placeholder="$t('Activity.signup.placeholder.ticket_type')" 
                multiple
                @change="handleTicketChange"
                @visible-change="handleSelectVisibleChange"
              >
                <el-option 
                  v-for="item in ticketTypeList" 
                  :key="item.id" 
                  :label="`${item.name} (${item.code})`" 
                  :value="item.id"
                  @click="handleOptionClick(item)"
                >
                  <div>
                    <div style="font-weight: bold;">{{ item.name }} ({{ item.code }})</div>
                    <div style="font-size: 12px; color: #999;">
                      {{ $t('Activity.signup.selected.subtypes_title') }} {{ item.subtypes?.map(sub => sub.name).join(', ') || $t('Activity.signup.ticket_tags.no_subtypes') }}
                    </div>
                  </div>
                </el-option>
                <template #footer>
                  <el-button text bg size="small" @click="onAddPiaoWu">
                    {{ $t('Activity.signup.button.new_ticket') }}
                  </el-button>
                </template>
              </el-select>
            </el-form-item>
            
            <!-- 显示已选择的票务详情 -->
            <div v-if="formData.ticket.length > 0" class="selected-tickets-display">
              <div class="selected-title">{{ $t('Activity.signup.selected.tickets_title') }}</div>
              <div class="selected-tickets-container">
                <div v-for="(ticketItem, ticketIndex) in formData.ticket" :key="ticketItem.id || ticketIndex" class="ticket-item">
                  <div class="ticket-header">
                    <span class="ticket-name">{{ ticketItem.name }} ({{ ticketItem.code }})</span>
                    <el-button 
                      type="text" 
                      size="small" 
                      @click="removeTicketByIndex(ticketIndex)"
                      class="remove-btn"
                    >
                      ✕
                    </el-button>
                  </div>
                  <div class="ticket-tags">
                    <el-tag :type="ticketItem.is_external_sale ? 'success' : 'info'" size="small">
                      {{ ticketItem.is_external_sale ? $t('Activity.signup.ticket_tags.external_sale') : $t('Activity.signup.ticket_tags.internal_sale') }}
                    </el-tag>
                    <el-tag :type="ticketItem.is_fee_required ? 'warning' : 'success'" size="small">
                      {{ ticketItem.is_fee_required ? $t('Activity.signup.ticket_tags.paid') : $t('Activity.signup.ticket_tags.free') }}
                    </el-tag>
                  </div>
                  <div v-if="ticketItem.subtypes && ticketItem.subtypes.length > 0" class="subtypes">
                    <div class="subtype-title">{{ $t('Activity.signup.selected.subtypes_title') }}</div>
                    <el-tag 
                      v-for="subtype in ticketItem.subtypes" 
                      :key="subtype.id" 
                      size="small" 
                      class="subtype-tag"
                    >
                      {{ subtype.name }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 報名數量限制卡片 -->
      <div class="signup-card">
        <div class="card-header">
          <h3 class="card-title">{{ $t('Activity.signup.card.registration_method') }}</h3>
        </div>
        <div class="card-content">
          <el-form-item :label="$t('Activity.signup.form.registration_method')">
            <el-radio-group v-model="formData.registration_method">
              <el-radio value="auto_close">{{ $t('Activity.signup.options.auto_close') }}</el-radio>
              <el-radio value="waiting_list">{{ $t('Activity.signup.options.waiting_list') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item v-if="formData.registration_method === 'waiting_list'" :label="$t('Activity.signup.form.waiting_list_rules')">
            <el-radio-group v-model="waitingListMode">
              <el-radio value="auto_promote">{{ $t('Activity.signup.options.auto_promote') }}</el-radio>
              <el-radio value="manual_review">{{ $t('Activity.signup.options.manual_review') }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>

      <!-- 票務設置卡片 -->
      <div v-if="formData.is_fee_required === 1" class="signup-card">
        <div class="card-header">
          <h3 class="card-title">{{ $t('Activity.signup.card.ticket_settings') }}</h3>
        </div>
        <div class="card-content">
          <el-form-item :label="$t('Activity.signup.form.ticket_delivery')">
            <el-radio-group v-model="formData.ticket_delivery_method">
              <el-radio value="electronic">{{ $t('Activity.signup.options.electronic') }}</el-radio>
              <el-radio value="physical">{{ $t('Activity.signup.options.physical') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item :label="$t('Activity.signup.form.email_template')">
            <el-select style="width: 100%;" v-model="formData.email_template" :placeholder="$t('Activity.signup.placeholder.email_template')">
              <el-option :label="$t('Activity.signup.options.festival')" value="festival"></el-option>
              <el-option :label="$t('Activity.signup.options.charity')" value="charity"></el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>
    </el-form>

    <div class="op-box">
      <el-button @click="handlePrev">{{ $t('Activity.signup.button.prev_step') }}</el-button>
      <el-button type="primary" @click="handleNext">{{ $t('Activity.signup.button.next_step') }}</el-button>
    </div>

    <AddGrounp 
      v-model:visible="dialogVisible"
      :mode="groupEditMode"
      :group-data="editingGroupData"
      @cancel="handleCancel" 
      @create="handleCreate" 
    />
    <AddPiaoWu 
      v-model:visible="dialogVisiblePiaowu" 
      :mode="piaoWuMode"
      :ticket-data="editingTicketData"
      :currentLocalization="props.currentLocalization"
      @cancel="piaoHandleCancel" 
      @create="piaoHandleCreate" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, watch, nextTick } from 'vue'
import AddGrounp from "./AddGrounp.vue"
import AddPiaoWu from "./AddPiaoWu.vue"
import { groupService } from '../../../services/activityervice'
import { ticketService } from '../../../services/ticketService'
import { ElMessage } from 'element-plus'
import { Edit, Delete } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import type { FormInstance } from 'element-plus'
import type { SignUpFormData, TicketType, Group } from '../types'

const props = defineProps({
  activityConfig: {
    type: Object,
    default: () => ({})
  },
  currentLocalization: {
    type: Object,
    default: () => null
  },
  activityId: {
    type: [String, Number],
    default: ''
  }
})

const emit = defineEmits(['prev-step', 'next-step', 'form-data-change'])

const { t } = useI18n()

const formRef = ref<FormInstance>()
const formData = ref<any>({
  registration_deadline: '',
  registration_limit_type: 0,
  registration_limit: 0,
  target_participants: 0,
  registration_type: 0,
  public_limit: 0,
  hidden_limit: 0,
  reserved_limit: 0,
  is_fee_required: 0,
  invoice_prefix: '',
  target_income: 0,
  registration_method: '',
  ticket_delivery_method: '',
  ticket: [],
  groups: [], // 改为数组
  email_template: '',
  registration_method_rules: {
    auto_promote: true,
    notification_enabled: true
  }
})

const groupList = ref<Group[]>([])
const ticketTypeList = ref<TicketType[]>([]) // 保留这个变量，但不再从接口获取数据
const selectedTicketIds = ref<number[]>([]) // 用于下拉选择的ID数组
const selectedGroupIds = ref<number[]>([]) // 用于群组下拉选择的ID数组
const dialogVisible = ref(false)
const dialogVisiblePiaowu = ref(false)
const piaoWuMode = ref<'create' | 'edit'>('create') // 票务弹窗模式
const editingTicketData = ref<TicketType | null>(null) // 正在编辑的票务数据
const waitingListMode = ref<'auto_promote' | 'manual_review'>('auto_promote') // waiting list处理模式
const groupListLoading = ref(false) // 群组列表加载状态
const groupEditMode = ref<'create' | 'edit'>('create') // 群组弹窗模式
const editingGroupData = ref<Group | { id: number } | null>(null) // 正在编辑的群组数据
const groupDetailLoading = ref(false) // 群组详情加载状态

// 添加数据加载状态标记
const isDataLoading = ref(false)

// 验证规则
const rules = {
  registration_deadline: [{ required: true, message: t('Activity.signup.validation.registration_deadline_required'), trigger: 'blur' }]
}

// 表单验证方法
const validateForm = async (): Promise<boolean> => {
  if (!formRef.value) return false
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    console.error('Form validation failed:', error)
    return false
  }
}

// 处理上一步
const handlePrev = () => {
  emit('prev-step')
}

// 处理下一步
const handleNext = async () => {
  const isValid = await validateForm()
  if (isValid) {
    emit('next-step')
  }
}

const onAddOption = () => {
  groupEditMode.value = 'create'
  editingGroupData.value = null
  dialogVisible.value = true
}

const onAddPiaoWu = () => {
  piaoWuMode.value = 'create'
  editingTicketData.value = null
  dialogVisiblePiaowu.value = true
}

// 记录上一次的选择状态
const lastSelectedTicketIds = ref<number[]>([])

// 处理下拉框可见性变化
const handleSelectVisibleChange = (visible: boolean) => {
  if (visible) {
    // 下拉框打开时，记录当前状态
    lastSelectedTicketIds.value = [...selectedTicketIds.value]
  }
}

// 处理选项点击
const handleOptionClick = (item: TicketType) => {
  // 检查是否已经选中
  const isAlreadySelected = selectedTicketIds.value.includes(item.id)
  const isAlreadyInForm = formData.value.ticket.some((ticket: any) => ticket.id === item.id)
  
  // 如果没有选中且不在表单中，说明这是新选择的
  if (!isAlreadySelected && !isAlreadyInForm) {
    // 设置为编辑模式并传入票务数据
    piaoWuMode.value = 'edit'
    editingTicketData.value = item
    
    // 延迟一下再打开弹窗，确保UI更新完成
    setTimeout(() => {
      dialogVisiblePiaowu.value = true
    }, 100)
  }
}

// 获取票务类型列表
const getTicketTypeList = async () => {
  if (!props.activityId) return // 新建时不需要获取列表
  
  try {
    const params = {
      page: 1,
      per_page: 99999
    }
    
    const activityId = Number(props.activityId)
    const res = await ticketService.getListByActivity(activityId, params)
    
    if (res.data && res.data.code === 200 && res.data.data) {
      ticketTypeList.value = res.data.data.items || []
    } else {
      ticketTypeList.value = res.data?.data?.items || res.data?.data || []
    }
  } catch (error: any) {
    console.error('Get ticket type list error:', error)
    ElMessage.error(t('Activity.signup.message.ticket_list_error'))
  }
}

// 处理票务选择变化
const handleTicketChange = (selectedIds: number[]) => {
  // 找出新增的票务ID（当前选中但之前未选中的）
  const newlySelectedIds = selectedIds.filter(id => !lastSelectedTicketIds.value.includes(id))
  
  // 如果有新选中的票务，直接打开编辑弹窗
  if (newlySelectedIds.length > 0) {
    const newTicketId = newlySelectedIds[0] // 取第一个新选中的
    const selectedTicket = ticketTypeList.value.find(ticket => ticket.id === newTicketId)
    
    if (selectedTicket) {
      // 设置为编辑模式并传入票务数据
      piaoWuMode.value = 'edit'
      editingTicketData.value = selectedTicket
      
      // 先恢复之前的选择状态（移除刚选中的）
      selectedTicketIds.value = [...lastSelectedTicketIds.value]
      
      // 延迟一下再打开弹窗，确保UI更新完成
      setTimeout(() => {
        dialogVisiblePiaowu.value = true
      }, 100)
      
      return
    }
  }
  
  // 处理移除的情况（正常的多选逻辑）
  formData.value.ticket = selectedIds.map(id => {
    return ticketTypeList.value.find(ticket => ticket.id === id)
  }).filter((ticket): ticket is TicketType => ticket !== undefined)
  
  // 更新记录的选择状态
  lastSelectedTicketIds.value = [...selectedIds]
}

// 处理群组选择变化
const handleGroupChange = (selectedIds: number[]) => {
  // 根据选中的ID从groupList中找到完整的群组对象
  formData.value.groups = selectedIds.map(id => {
    return groupList.value.find(group => group.id === id)
  }).filter((group): group is Group => group !== undefined) // 过滤掉undefined并提供类型守卫
}

// 通过索引移除票务（用于新建模式）
const removeTicketByIndex = (index: number) => {
  if (index >= 0 && index < formData.value.ticket.length) {
    const ticketToRemove = formData.value.ticket[index]
    
    // 从formData.ticket中移除
    formData.value.ticket.splice(index, 1)
    
    // 如果有对应的临时ID，从相关数组中移除
    if (index < selectedTicketIds.value.length) {
      const tempId = selectedTicketIds.value[index]
      selectedTicketIds.value.splice(index, 1)
      
      // 如果是临时添加的票务，从ticketTypeList中移除
      if (typeof tempId === 'string' && (tempId as string).startsWith('temp_')) {
        ticketTypeList.value = ticketTypeList.value.filter(ticket => String(ticket.id) !== String(tempId))
      }
    }
  }
}

// 移除选中的票务（用于编辑模式）
const removeTicket = (ticketId: number | string) => {
  // 从formData.ticket中直接移除
  const ticketIndex = formData.value.ticket.findIndex((ticket: any) => {
    // 新建模式的票务可能没有id，通过名称和代码匹配
    if (!ticket.id) {
      // 通过其他唯一字段匹配，比如name + code组合
      return false // 新建模式需要通过index来删除
    }
    // 编辑模式的票务通过id匹配
    return String(ticket.id) === String(ticketId)
  })
  
  if (ticketIndex !== -1) {
    formData.value.ticket.splice(ticketIndex, 1)
  }
  
  // 从selectedTicketIds中移除
  selectedTicketIds.value = selectedTicketIds.value.filter(id => String(id) !== String(ticketId))
  
  // 从ticketTypeList中移除（如果是临时添加的）
  if (typeof ticketId === 'string' && ticketId.startsWith('temp_')) {
    ticketTypeList.value = ticketTypeList.value.filter(ticket => String(ticket.id) !== String(ticketId))
  }
}

// 移除选中的群组
const removeGroup = (groupId: number) => {
  // 从selectedGroupIds中移除
  selectedGroupIds.value = selectedGroupIds.value.filter(id => id !== groupId)
  // 触发群组变化处理
  handleGroupChange(selectedGroupIds.value)
}

// 群组
const handleCancel = () => {
  dialogVisible.value = false
  groupEditMode.value = 'create'
  editingGroupData.value = null
}

const handleCreate = async (newGroupData: any) => {
  console.log('群组操作成功，数据：', newGroupData)
  
  if (groupEditMode.value === 'edit') {
    // 编辑模式：更新本地数据
    const index = formData.value.groups.findIndex((g: any) => g.id === editingGroupData.value?.id)
    if (index !== -1) {
      formData.value.groups[index] = { ...formData.value.groups[index], ...newGroupData }
    }
    ElMessage.success(t('Activity.signup.message.group_update_success'))
  } else {
    // 创建模式：刷新群组列表
    await getGroupList()
    ElMessage.success(t('Activity.signup.message.group_create_success'))
  }
  
  dialogVisible.value = false
  groupEditMode.value = 'create'
  editingGroupData.value = null
}

// 票务
const piaoHandleCancel = () => {
  dialogVisiblePiaowu.value = false
}

const piaoHandleCreate = async (newTicketData: any) => {
  if (!newTicketData) {
    dialogVisiblePiaowu.value = false
    return
  }

  try {
    if (piaoWuMode.value === 'create') {
      if (props.activityId) {
        // 编辑模式：调用创建接口
        const activityId = Number(props.activityId)
        const response = await ticketService.create(activityId, newTicketData)
        
        if (response.status === 200 && response?.data?.code === 200) {
          ElMessage.success(t('Activity.signup.message.ticket_create_success'))
          // 重新获取票务列表
          await getTicketTypeList()
        } else {
          ElMessage.error(response.data?.message || t('Activity.signup.message.ticket_create_error'))
        }
      } else {
        // 新建模式：直接添加到表单数据中，不添加ID
        const newTicketForForm = {
          ...newTicketData,
          subtypes: newTicketData.subtypes || []
        }
        
        // 为前端显示生成临时标识，但不包含在实际数据中
        const tempDisplayId = `temp_${Date.now()}`
        
        // 添加到已选择的票务中
        formData.value.ticket.push(newTicketForForm)
        // 使用临时标识用于前端操作，但不影响提交的数据
        selectedTicketIds.value.push(tempDisplayId as any)
        ticketTypeList.value.push({
          ...newTicketForForm,
          id: tempDisplayId as any // 只在列表中添加临时ID用于显示
        })
        
        ElMessage.success(t('Activity.signup.message.ticket_add_form_success'))
      }
      dialogVisiblePiaowu.value = false
    } else if (piaoWuMode.value === 'edit') {
      // 编辑模式：直接添加到表单数据中
      if (editingTicketData.value) {
        // 合并原有数据和新的数量限制
        const ticketForForm = {
          ...editingTicketData.value,
          quota: newTicketData.quota // 只更新数量限制
        }
        
        // 添加到已选择的票务中
        formData.value.ticket.push(ticketForForm)
        selectedTicketIds.value.push(editingTicketData.value.id)
        
        ElMessage.success(t('Activity.signup.message.ticket_add_success'))
        dialogVisiblePiaowu.value = false
      }
    }
  } catch (error: any) {
    console.error('Handle ticket error:', error)
    ElMessage.error(error.message || t('Activity.signup.message.operation_failed'))
  }
}

// 获取 群组
const getGroupList = async () => {
  try {
    groupListLoading.value = true // 开始loading
    
    const params = {
      page: 1,
      per_page: 99999
    }
    
    const res = await groupService.getList(params)
    
    if (res.status === 200 && res?.data?.code === 200) {
      console.log('获取群组列表成功：', res.data.data)
      groupList.value = res.data.data?.items || []
    } else {
      console.error('获取群组列表失败：', res.data?.message)
      ElMessage.error(res.data?.message || t('Activity.signup.message.group_list_error'))
    }
  } catch (error: any) {
    console.error('Get group list error:', error)
    ElMessage.error(t('Activity.signup.message.group_list_retry'))
  } finally {
    groupListLoading.value = false // 结束loading
  }
}

// 监听是否收费变化，重置相关字段
watch(() => formData.value.is_fee_required, (newValue) => {
  if (newValue === 0) {
    // 不收费时重置相关字段
    formData.value.invoice_prefix = ''
    formData.value.target_income = 0
    formData.value.ticket_delivery_method = ''
    formData.value.email_template = ''
    formData.value.ticket = []
    selectedTicketIds.value = []
  }
})

// 监听waiting list模式变化，更新registration_method_rules
watch(() => waitingListMode.value, (newMode) => {
  if (formData.value.registration_method === 'waiting_list') {
    if (newMode === 'auto_promote') {
      formData.value.registration_method_rules = {
        auto_promote: true,
        notification_enabled: false
      }
    } else if (newMode === 'manual_review') {
      formData.value.registration_method_rules = {
        auto_promote: false,
        notification_enabled: true
      }
    }
  }
})

// 监听registration_method变化，重置waiting list设置
watch(() => formData.value.registration_method, (newMethod) => {
  if (newMethod !== 'waiting_list') {
    // 如果不是waiting list模式，重置相关设置
    formData.value.registration_method_rules = {
      auto_promote: true,
      notification_enabled: true
    }
    waitingListMode.value = 'auto_promote'
  }
})

// 监听表单数据变化，向父组件发送数据
watch(formData, (newVal) => {
  emit('form-data-change', newVal)
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  validateForm,
  formData,
  updateFormData: (data: any) => {
    if (data) {
      // 设置数据加载状态，防止watch触发
      isDataLoading.value = true
      
      // 深度合并数据，保持响应式
      Object.keys(data).forEach(key => {
        if (data[key] !== undefined) {
          (formData.value as any)[key] = data[key]
        }
      })
      
      // 更新选择状态
      if (data.ticket && Array.isArray(data.ticket)) {
        selectedTicketIds.value = data.ticket.map((t: any) => t.id).filter(Boolean)
      }
      if (data.groups && Array.isArray(data.groups)) {
        selectedGroupIds.value = data.groups.map((g: any) => g.id).filter(Boolean)
      }
      
      // 更新waiting list模式
      if (data.registration_method === 'waiting_list' && data.registration_method_rules) {
        waitingListMode.value = data.registration_method_rules.auto_promote ? 'auto_promote' : 'manual_review'
      }
      
      // 延迟重置数据加载状态，确保数据回填完成
      nextTick(() => {
        setTimeout(() => {
          isDataLoading.value = false
        }, 100)
      })
    }
  }
})

// 格式化日期范围显示
const formatDateRange = (startTime: string, endTime: string) => {
  if (!startTime || !endTime) return t('Activity.signup.group_details.not_set')
  const start = new Date(startTime).toLocaleDateString()
  const end = new Date(endTime).toLocaleDateString()
  return `${start} ${t('Activity.signup.date_separator')} ${end}`
}

// 编辑群组
const editGroup = async (groupId: number) => {
  // 立即打开弹窗，设置编辑模式和传递群组ID
  editingGroupData.value = { id: groupId } // 只传递ID，详情在弹窗内获取
  groupEditMode.value = 'edit'
  dialogVisible.value = true
}

onMounted(() => {
  getTicketTypeList() // 编辑时获取票务列表
  getGroupList()
  // 初始化选择状态记录
  lastSelectedTicketIds.value = [...selectedTicketIds.value]
})
</script>

<script lang="ts">
export default {
  name: 'SignUp'
}
</script>

<style lang="scss" scoped>
.sign-up-box {
  height: 100%;
  overflow: scroll;
  overflow-y: auto;
  padding: 0;
  margin: 0;
  width: 100%;
  
  // 注册卡片样式 - 参考Info.vue的info-card
  .signup-card {
    background: #FFFFFF;
    box-shadow: 0px 1px 1px #0000000D;
    border-radius: 10px;
    margin-bottom: 20px;
    width: 100%;
    
    // 第一个卡片样式 - 与tabs无缝连接
    &:first-child {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      margin-top: 0;
    }
    
    .card-header {
      padding: 20px 20px 0 20px;
      border-bottom: none;
      
      .card-title {
        font: normal normal normal 16px/21px Microsoft JhengHei;
        letter-spacing: 0px;
        color: #000000;
        margin: 0 0 20px 0;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
    
    .card-content {
      padding: 0 20px 20px 20px;
    }
  }
}

.op-box {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
  padding-bottom: 30px;
}

.title {
  font-size: 24px;
  display: flex;
  align-items: center;
  margin: 20px 0px;
}

// 选中项显示区域
.selected-groups-display,
.selected-tickets-display {
  margin-top: 15px;
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.selected-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
}

// 群组标签容器 - 横向排列，换行
.selected-groups-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

// 票务容器 - 横向排列，换行
.selected-tickets-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

// 群组卡片样式
.group-card {
  padding: 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  position: relative;
  flex: 1;
  min-width: 280px;
  max-width: 350px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// 票务项样式
.ticket-item {
  padding: 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  position: relative;
  flex: 1;
  min-width: 280px;
  max-width: 350px;
}

.group-header,
.ticket-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.group-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.group-name,
.ticket-name {
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.group-code {
  font-size: 12px;
  color: #999;
}

.group-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.edit-btn {
  color: #409eff;
  padding: 0;
  min-width: auto;
  
  &:hover {
    background-color: #f0faff;
  }
}

.remove-btn {
  color: #f56c6c;
  padding: 0;
  min-width: auto;
  
  &:hover {
    background-color: #fef0f0;
  }
}

.group-details {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.group-detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-label {
  font-size: 12px;
  color: #666;
}

.detail-value {
  font-size: 14px;
  color: #333;
}

.ticket-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.subtypes {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.subtype-title {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.subtype-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

// 群组和票务标签样式
.selected-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

:deep(.el-form) {
  width: 100% !important;
}

// 响应式调整
@media (max-width: 768px) {
  .selected-groups-container {
    flex-direction: column;
  }
  
  .group-card {
    min-width: auto;
    max-width: none;
  }
  
  .selected-groups-display,
  .selected-tickets-display {
    padding: 12px;
  }
}
</style> 