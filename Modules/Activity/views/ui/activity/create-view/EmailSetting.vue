<template>
  <div class="reminder-container">
    <div class="reminder-switch-section">
      <h3>活動前郵件</h3>
      
      <div class="switch-item">
        <div>
            <span>邀請郵件</span>
            <p>發送方式：自動發送至邀請列表成員郵箱</p>
        </div>
        
        <el-switch v-model="reminderSettings.activityEndReminder" class="custom-switch" />
      </div>
      
      <div class="switch-item">
        <div>
            <span>邀請提醒郵件</span>
            <p>發送方式：自動發送至邀請列表成員郵箱</p>
        </div>
        <el-switch v-model="reminderSettings.participantLimitReminder" class="custom-switch" />
      </div>
      
      <div class="switch-item">
        <div>
            <span>waitlist 提醒郵件</span>
            <p>發送方式：自動發送至邀請列表成員郵箱</p>
        </div>
        <el-switch v-model="reminderSettings.activityStartEndReminder" class="custom-switch" />
      </div>
      
      <h3>報名後郵件</h3>
      
      <div class="switch-item">
        <div>
            <span>待報名通過通知</span>
            <p>發送方式：自動發送至unapproved報名者郵箱</p>
        </div>
        <el-switch v-model="reminderSettings.specialVerificationReminder" class="custom-switch" />
      </div>
      
      <div class="switch-item">
        <div>
            <span>活動提醒郵件</span>
            <p>發送方式：自動發送至已通過報名者郵箱</p>
        </div>
        <el-switch v-model="reminderSettings.waitlistLimitReminder" class="custom-switch" />
      </div>      
      <div class="switch-item">
        <div>
            <span>報名成功確認郵件</span>
            <p>發送方式：自動發送至已通過報名者郵箱</p>
        </div>
        <el-switch v-model="reminderSettings.ticketLimitReminder" class="custom-switch" />
      </div>
    
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
const router = useRouter()

const displayMode = ref('expanded')

const reminderSettings = ref({
  activityEndReminder: true,
  participantLimitReminder: true,
  activityStartEndReminder: false,
  specialVerificationReminder: false,
  waitlistLimitReminder: false,
  ticketLimitReminder: true,
  ticketCostRateReminder: false
})
</script>

<style scoped>
.reminder-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background-color: #f5f7fa;
  padding: 0;
  margin: 20px;
}

.reminder-form-section, .reminder-switch-section {
  background-color: white;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.header-controls {
  display: flex;
  align-items: center;
}

.recipient-section {
  margin-bottom: 15px;
}

.recipient-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.label-placeholder {
  width: 120px;
}

.add-button {
  margin-left: 10px;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
}

.switch-item:last-child {
  border-bottom: none;
}

.custom-switch {
  --el-switch-on-color: #13ce66;
}

h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 15px 0;
}
</style>
