<template>
  <div class="info-big-box">
    <el-form :model="formData" ref="formRef" :rules="rules" label-position="top" label-width="120px">
      
      <!-- 活動基本信息卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h3 class="card-title">{{ $t('Activity.info.card.basic_info') }}</h3>
        </div>
        <div class="card-content">
          <el-form-item :label="$t('Activity.info.form.title')" prop="title">
            <el-input v-model="formData.title" :placeholder="$t('Activity.info.placeholder.title')"></el-input>
          </el-form-item>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="$t('Activity.info.form.category')" prop="category">
                <el-select v-model="formData.category" :placeholder="$t('Activity.info.placeholder.category')" style="width: 100%;">
                  <el-option 
                    v-for="item in Object.keys(props?.activityConfig?.categories || {})" 
                    :label="props?.activityConfig?.categories[item]" 
                    :value="item" 
                    :key="item"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('Activity.info.form.localized_id')" prop="localized_id">
                <el-select v-model="formData.localized_id" :placeholder="$t('Activity.info.placeholder.localized_id')" style="width: 100%;">
                  <el-option v-for="item in props?.activityConfig?.localizations || []" :label="item.name" :value="item.id" :key="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 活動組織者信息卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h3 class="card-title">{{ $t('Activity.info.card.organizer_info') }}</h3>
        </div>
        <div class="card-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-space>
                <el-form-item :label="$t('Activity.info.form.organizer_last_name')" prop="organizer_last_name">
                  <el-input v-model="formData.organizer_last_name" :placeholder="$t('Activity.info.placeholder.organizer_last_name')"></el-input>
                </el-form-item>
                <el-form-item :label="$t('Activity.info.form.organizer_first_name')" prop="organizer_first_name">
                  <el-input v-model="formData.organizer_first_name" :placeholder="$t('Activity.info.placeholder.organizer_first_name')"></el-input>
                </el-form-item>
              </el-space>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('Activity.info.form.organizer_email')" prop="organizer_email">
                <el-input v-model="formData.organizer_email" :placeholder="$t('Activity.info.placeholder.organizer_email')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 活動地點設置卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h3 class="card-title">{{ $t('Activity.info.card.location_settings') }}</h3>
        </div>
        <div class="card-content">
          <el-form-item :label="$t('Activity.info.form.type')" prop="type">
            <el-select v-model="formData.type" :placeholder="$t('Activity.info.placeholder.type')" style="width: 100%;">
              <el-option v-for="item in Object.keys(props?.activityConfig?.types || {})" :label="props?.activityConfig?.types[item]" :value="item" :key="item"></el-option>
            </el-select>
          </el-form-item>
          
          <!-- 线下活动字段 -->
          <template v-if="isOfflineActivity || isMixedActivity">
            <el-form-item :label="$t('Activity.info.form.location')" prop="location">
              <el-input v-model="formData.location" :placeholder="$t('Activity.info.placeholder.location')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('Activity.info.form.address')" prop="address">
              <el-input v-model="formData.address" :placeholder="$t('Activity.info.placeholder.address')"></el-input>
            </el-form-item>
          </template>
          
          <!-- 线上活动字段 -->
          <template v-if="isOnlineActivity || isMixedActivity">
            <el-form-item :label="$t('Activity.info.form.online_platform')" prop="online_platform">
              <el-input v-model="formData.online_platform" :placeholder="$t('Activity.info.placeholder.online_platform')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('Activity.info.form.online_platform_url')" prop="online_platform_url">
              <el-input v-model="formData.online_platform_url" :placeholder="$t('Activity.info.placeholder.online_platform_url')"></el-input>
            </el-form-item>
          </template>
        </div>
      </div>

      <!-- 活動時間設置卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h3 class="card-title">{{ $t('Activity.info.card.schedule_settings') }}</h3>
        </div>
        <div class="card-content">
          <el-form-item :label="$t('Activity.info.form.repeat_type')" prop="schedule.repeat_type">
            <el-select v-model="formData.schedule.repeat_type" :placeholder="$t('Activity.info.placeholder.repeat_type')" style="width: 100%;">
              <el-option v-for="item in Object.keys(props?.activityConfig?.repeat_patterns || {})" :label="props?.activityConfig?.repeat_patterns[item]" :value="item" :key="item"></el-option>
            </el-select>
          </el-form-item>
          
          <!-- 每月 -->
          <template v-if="formData.schedule.repeat_type === 'monthly'">
            <el-form-item :label="$t('Activity.info.form.repeat_frequency')" prop="schedule.repeat_frequency">
              <el-space>
                <span>{{ $t('Activity.info.frequency.every') }}</span>
                  <el-input-number v-model="formData.schedule.repeat_frequency" :min="1" />
                <span>{{ $t('Activity.info.frequency.month_repeat') }}</span>
              </el-space>
            </el-form-item>
            <el-form-item :label="$t('Activity.info.form.repeat_pattern')">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="$t('Activity.info.form.monthly_week')" prop="monthly_week">
                    <el-select v-model="formData.monthly_week" :placeholder="$t('Activity.info.placeholder.monthly_week')" style="width: 100%;">
                      <el-option :label="$t('Activity.info.options.week.1')" :value="1" :key="1"></el-option>
                      <el-option :label="$t('Activity.info.options.week.2')" :value="2" :key="2"></el-option>
                      <el-option :label="$t('Activity.info.options.week.3')" :value="3" :key="3"></el-option>
                      <el-option :label="$t('Activity.info.options.week.4')" :value="4" :key="4"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('Activity.info.form.monthly_day')" prop="monthly_day">
                    <el-select v-model="formData.monthly_day" :placeholder="$t('Activity.info.placeholder.monthly_day')" style="width: 100%;">
                      <el-option :label="$t('Activity.info.options.weekday.1')" :value="1" :key="1"></el-option>
                      <el-option :label="$t('Activity.info.options.weekday.2')" :value="2" :key="2"></el-option>
                      <el-option :label="$t('Activity.info.options.weekday.3')" :value="3" :key="3"></el-option>
                      <el-option :label="$t('Activity.info.options.weekday.4')" :value="4" :key="4"></el-option>
                      <el-option :label="$t('Activity.info.options.weekday.5')" :value="5" :key="5"></el-option>
                      <el-option :label="$t('Activity.info.options.weekday.6')" :value="6" :key="6"></el-option>
                      <el-option :label="$t('Activity.info.options.weekday.7')" :value="7" :key="7"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item :label="$t('Activity.info.form.schedule_dates')">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="$t('Activity.info.form.start_date')" prop="schedule.start_date">
                    <el-date-picker 
                      v-model="formData.schedule.start_date" 
                      style="width: 100%;" 
                      type="datetime" 
                      :placeholder="$t('Activity.info.placeholder.start_date')"
                      format="YYYY-MM-DD HH:mm"
                      value-format="YYYY-MM-DD HH:mm"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('Activity.info.form.end_date')" prop="schedule.end_date">
                    <el-date-picker 
                      v-model="formData.schedule.end_date" 
                      style="width: 100%;" 
                      type="datetime" 
                      :placeholder="$t('Activity.info.placeholder.end_date')"
                      format="YYYY-MM-DD HH:mm"
                      value-format="YYYY-MM-DD HH:mm"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
            <el-checkbox v-model="breakPeriods" :label="$t('Activity.info.form.add_break')" size="large" />
            <template v-if="breakPeriods">
              <el-form-item v-for="(item, index) in formData.schedule.break_periods" :key="index" :label="$t('Activity.info.form.break_periods')">
                <el-row :gutter="20" style="align-items: center; width: 100%;">
                  <el-col :span="10">
                    <el-form-item :label="$t('Activity.info.form.start_time')" :prop="`schedule.break_periods.${index}.start_date`">
                      <el-date-picker 
                        format="YYYY-MM-DD HH:mm" 
                        value-format="YYYY-MM-DD HH:mm"  
                        style="width: 100%;" 
                        v-model="item.start_date" 
                        type="datetime" 
                        :placeholder="$t('Activity.info.placeholder.start_time')" 
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="10">
                    <el-form-item :label="$t('Activity.info.form.end_time')" :prop="`schedule.break_periods.${index}.end_date`">
                      <el-date-picker 
                        format="YYYY-MM-DD HH:mm" 
                        value-format="YYYY-MM-DD HH:mm" 
                        style="width: 100%;" 
                        v-model="item.end_date" 
                        type="datetime" 
                        :placeholder="$t('Activity.info.placeholder.end_time')" 
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4" style="padding-top: 25px;" >
                    <div 
                      class="circle-btn circle-btn-danger"
                      @click="removeBreakPeriod(index)"
                      style="margin-bottom: 18px;"
                    >
                      <el-icon><Delete /></el-icon>
                    </div>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item>
                <div 
                  class="circle-btn circle-btn-primary"
                  @click="addBreakPeriod"
                >
                  <el-icon><CirclePlusFilled /></el-icon>
                </div>
                <span style="margin-left: 8px;">{{ $t('Activity.info.button.add_break_period') }}</span>
              </el-form-item>
            </template>
          </template>
          
          <!-- 每周 -->
          <template v-if="formData.schedule.repeat_type === 'weekly'">
            <el-form-item :label="$t('Activity.info.form.repeat_frequency')" prop="schedule.repeat_frequency">
              <el-space>
                <span>{{ $t('Activity.info.frequency.every') }}</span>
                <el-input-number v-model="formData.schedule.repeat_frequency" :min="1" />
                <span>{{ $t('Activity.info.frequency.week_repeat') }}</span>
              </el-space>
            </el-form-item>
            <el-form-item :label="$t('Activity.info.form.time_settings')">
              <el-row v-for="(item, index) in formData.schedule.time_slots" :key="index" :gutter="20" style="margin-bottom: 16px; align-items: center;">
                <el-col :span="6">
                  <el-form-item :label="$t('Activity.info.form.day_of_week')" :prop="`schedule.time_slots.${index}.day_of_week`">
                    <el-select v-model="item.day_of_week" :placeholder="$t('Activity.info.placeholder.day_of_week')" style="width: 100%;">
                      <el-option :label="$t('Activity.info.options.weekday_long.1')" :value="1" :key="1"></el-option>
                      <el-option :label="$t('Activity.info.options.weekday_long.2')" :value="2" :key="2"></el-option>
                      <el-option :label="$t('Activity.info.options.weekday_long.3')" :value="3" :key="3"></el-option>
                      <el-option :label="$t('Activity.info.options.weekday_long.4')" :value="4" :key="4"></el-option>
                      <el-option :label="$t('Activity.info.options.weekday_long.5')" :value="5" :key="5"></el-option>
                      <el-option :label="$t('Activity.info.options.weekday_long.6')" :value="6" :key="6"></el-option>
                      <el-option :label="$t('Activity.info.options.weekday_long.7')" :value="7" :key="7"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="6">
                  <el-form-item :label="$t('Activity.info.form.start_time')" :prop="`schedule.time_slots.${index}.start_time`">
                    <el-time-picker 
                      v-model="item.start_time" 
                      :placeholder="$t('Activity.info.placeholder.time_start')" 
                      format="HH:mm"
                      value-format="HH:mm"
                      style="width: 100%;"
                    />
                  </el-form-item>
                </el-col>

                <el-col :span="6">
                  <el-form-item :label="$t('Activity.info.form.end_time')" :prop="`schedule.time_slots.${index}.end_time`">
                    <el-time-picker 
                      v-model="item.end_time" 
                      :placeholder="$t('Activity.info.placeholder.time_end')" 
                      format="HH:mm"
                      value-format="HH:mm"
                      style="width: 100%;"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6" style="padding-top: 25px;" >
                  <div 
                    class="circle-btn circle-btn-primary"
                    @click="addTimeSlots"
                    style="margin-bottom: 18px; margin-right: 8px;"
                  >
                    <el-icon><CirclePlusFilled /></el-icon>
                  </div>
                  <div 
                    v-if="formData.schedule.time_slots.length > 1"
                    class="circle-btn circle-btn-danger"
                    @click="removeTimeSlot(index)"
                    style="margin-bottom: 18px;"
                  >
                    <el-icon><Delete /></el-icon>
                  </div>
                </el-col>
              </el-row>
            </el-form-item>

            <el-form-item :label="$t('Activity.info.form.schedule_dates')">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="$t('Activity.info.form.start_date')" prop="schedule.start_date">
                    <el-date-picker 
                      v-model="formData.schedule.start_date" 
                      style="width: 100%;" 
                      type="datetime" 
                      :placeholder="$t('Activity.info.placeholder.start_date')"
                      format="YYYY-MM-DD "
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('Activity.info.form.end_date')" prop="schedule.end_date">
                    <el-date-picker 
                      v-model="formData.schedule.end_date" 
                      style="width: 100%;" 
                      type="datetime" 
                      :placeholder="$t('Activity.info.placeholder.end_date')"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
            <el-checkbox v-model="breakPeriods" :label="$t('Activity.info.form.add_break')" size="large" />
            <template v-if="breakPeriods">
              <el-form-item v-for="(item, index) in formData.schedule.break_periods" :key="index" :label="$t('Activity.info.form.break_periods')">
                <el-row :gutter="20" style="align-items:center; width: 100%;">
                  <el-col :span="10">
                    <el-form-item :label="$t('Activity.info.form.start_time')" :prop="`schedule.break_periods.${index}.start_date`">
                      <el-date-picker 
                        format="YYYY-MM-DD HH:mm" 
                        value-format="YYYY-MM-DD HH:mm"  
                        style="width: 100%;" 
                        v-model="item.start_date" 
                        type="datetime" 
                        :placeholder="$t('Activity.info.placeholder.start_time')" 
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="10">
                    <el-form-item :label="$t('Activity.info.form.end_time')" :prop="`schedule.break_periods.${index}.end_date`">
                      <el-date-picker 
                        format="YYYY-MM-DD HH:mm" 
                        value-format="YYYY-MM-DD HH:mm" 
                        style="width: 100%;" 
                        v-model="item.end_date" 
                        type="datetime" 
                        :placeholder="$t('Activity.info.placeholder.end_time')" 
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4" style="padding-top: 25px;" >
                    <div 
                      class="circle-btn circle-btn-danger"
                      @click="removeBreakPeriod(index)"
                      style="margin-bottom: 18px;"
                    >
                      <el-icon><Delete /></el-icon>
                    </div>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item>
                <div 
                  class="circle-btn circle-btn-primary"
                  @click="addBreakPeriod"
                >
                  <el-icon><CirclePlusFilled /></el-icon>
                </div>
                <span style="margin-left: 8px;">{{ $t('Activity.info.button.add_break_period') }}</span>
              </el-form-item>
            </template>
          </template>
          
          <!-- 每日 -->
          <template v-if="formData.schedule.repeat_type === 'daily'">
            <el-form-item :label="$t('Activity.info.form.repeat_frequency')" prop="schedule.repeat_frequency">
              <el-space>
                <span>{{ $t('Activity.info.frequency.daily_repeat') }}</span>
                <el-input-number v-model="formData.schedule.repeat_frequency" :min="1" />
                <span>{{ $t('Activity.info.frequency.times') }}</span>
              </el-space>
            </el-form-item>

            <el-form-item :label="$t('Activity.info.form.time_settings')">
              <el-row v-for="(item, index) in formData.schedule.time_slots" :key="index" :gutter="20" style="margin-bottom: 16px; align-items: center;">
                <el-col :span="8">
                  <el-form-item :label="$t('Activity.info.form.start_time')" :prop="`schedule.time_slots.${index}.start_time`">
                    <el-time-picker 
                      v-model="item.start_time" 
                      :placeholder="$t('Activity.info.placeholder.time_start')" 
                      format="HH:mm"
                      value-format="HH:mm"
                      style="width: 100%;"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('Activity.info.form.end_time')" :prop="`schedule.time_slots.${index}.end_time`">
                    <el-time-picker 
                      v-model="item.end_time" 
                      :placeholder="$t('Activity.info.placeholder.time_end')" 
                      format="HH:mm"
                      value-format="HH:mm"
                      style="width: 100%;"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8" style="padding-top: 25px;" >
                  <div 
                    class="circle-btn circle-btn-primary"
                    @click="addTimeSlots"
                    style="margin-bottom: 18px; margin-right: 8px;"
                  >
                    <el-icon><CirclePlusFilled /></el-icon>
                  </div>
                  <div 
                    v-if="formData.schedule.time_slots.length > 1"
                    class="circle-btn circle-btn-danger"
                    @click="removeTimeSlot(index)"
                    style="margin-bottom: 18px;"
                  >
                    <el-icon><Delete /></el-icon>
                  </div>
                </el-col>
              </el-row>
            </el-form-item>
          </template>
          
          <!-- 单次 -->
          <template v-if="formData.schedule.repeat_type === 'once'">
            <el-form-item :label="$t('Activity.info.form.activity_time')">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="$t('Activity.info.form.start_time')" prop="schedule.start_date">
                    <el-date-picker 
                      v-model="formData.schedule.start_date" 
                      type="datetime" 
                      :placeholder="$t('Activity.info.placeholder.start_time')" 
                      format="YYYY-MM-DD HH:mm"
                      value-format="YYYY-MM-DD HH:mm"
                      style="width: 100%;"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('Activity.info.form.end_time')" prop="schedule.end_date">
                    <el-date-picker 
                      v-model="formData.schedule.end_date" 
                      type="datetime" 
                      :placeholder="$t('Activity.info.placeholder.end_time')" 
                      format="YYYY-MM-DD HH:mm"
                      value-format="YYYY-MM-DD HH:mm"
                      style="width: 100%;"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
          </template>
        </div>
      </div>

      <!-- 發佈設置卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h3 class="card-title">{{ $t('Activity.info.card.publish_settings') }}</h3>
        </div>
        <div class="card-content">
          <el-form-item :label="$t('Activity.info.form.publish_time')" prop="publish_time">
            <el-date-picker 
              style="width: 50%;" 
              v-model="formData.publish_time" 
              type="datetime" 
              :placeholder="$t('Activity.info.placeholder.publish_time')" 
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm"
            />
          </el-form-item>
        </div>
      </div>
    </el-form>

    <div class="op-box">
      <el-button type="primary" @click="handleNext">{{ $t('Activity.info.button.next_step') }}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, computed, nextTick } from 'vue'
import { ElInput, ElSelect, ElOption, ElForm, ElFormItem, ElButton } from 'element-plus'
import { Document, Location, CirclePlusFilled, Delete } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules } from 'element-plus'

interface TimeSlot {
  day_of_month: string | number | null
  start_time: string
  end_time: string
  day_of_week: string | number
}

interface BreakPeriod {
  start_date: string
  end_date: string
}

interface Schedule {
  repeat_type: string
  repeat_frequency: number
  start_date: string
  end_date: string
  time_slots: TimeSlot[]
  break_periods: BreakPeriod[]
}

interface FormData {
  title: string
  category: string
  localized_id: string
  organizer_last_name: string
  organizer_first_name: string
  organizer_email: string
  type: string
  location: string
  address: string
  online_platform: string
  online_platform_url: string
  publish_time: string
  monthly_week: number
  monthly_day: number
  schedule: Schedule
}

const props = defineProps({
  activityConfig: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['next-step', 'form-data-change'])

const { t } = useI18n()

const breakPeriods = ref(false)
const formRef = ref<FormInstance>()

const formData = ref<FormData>({
  title: '',
  category: '',
  localized_id: '',
  organizer_last_name: '',
  organizer_first_name: '',
  organizer_email: '',
  type: '',
  location: '',
  address: '',
  online_platform: '',
  online_platform_url: '',
  publish_time: '',
  monthly_week: 1,
  monthly_day: 1,
  schedule: {
    repeat_type: '',
    repeat_frequency: 1,
    start_date: '',
    end_date: '',
    time_slots: [
      {
        day_of_month: null,
        start_time: '',
        end_time: '',
        day_of_week: ''
      }
    ],
    break_periods: []
  },
})

// 计算属性：判断是否为线上活动
const isOnlineActivity = computed(() => {
  return formData.value.type === 'online'
})

// 计算属性：判断是否为线下活动
const isOfflineActivity = computed(() => {
  return formData.value.type === 'offline'
})

// 计算属性：判断是否为混合活动
const isMixedActivity = computed(() => {
  return formData.value.type === 'hybrid'
})

// 计算属性：处理schedule数据
const processedFormData = computed(() => {
  const data = JSON.parse(JSON.stringify(formData.value))
  const repeatType = data.schedule.repeat_type
  console.log('repeatType'+JSON.stringify(data))
  
  // 根据重复模式处理time_slots
  if (repeatType === 'once' || repeatType === 'monthly') {
    // 单次和每月模式不需要time_slots
    delete data.schedule.time_slots
  } else if (repeatType === 'weekly' || repeatType === 'daily') {
    // 过滤掉空的time_slots
    if (data.schedule.time_slots) {
      data.schedule.time_slots = data.schedule.time_slots.filter((slot: any) => {
        if (repeatType === 'weekly') {
          return slot.day_of_week && slot.start_time && slot.end_time
        } else if (repeatType === 'daily') {
          return slot.start_time && slot.end_time
        }
        return false
      })
    }
  } else {
    delete data.schedule.time_slots
  }
  
  return data
})

// 动态验证规则
const rules = computed((): FormRules => {
  const baseRules: FormRules = {
    title: [{ required: true, message: t('Activity.info.validation.title_required'), trigger: 'blur' }],
    category: [{ required: true, message: t('Activity.info.validation.category_required'), trigger: 'blur' }],
    organizer_last_name: [{ required: true, message: t('Activity.info.validation.organizer_last_name_required'), trigger: 'blur' }],
    organizer_first_name: [{ required: true, message: t('Activity.info.validation.organizer_first_name_required'), trigger: 'blur' }],
    organizer_email: [
      { required: true, message: t('Activity.info.validation.organizer_email_required'), trigger: 'blur' },
      { type: 'email' as const, message: t('Activity.info.validation.organizer_email_format'), trigger: 'blur' }
    ],
    type: [{ required: true, message: t('Activity.info.validation.type_required'), trigger: 'blur' }],
    'schedule.repeat_type': [{ required: true, message: t('Activity.info.validation.repeat_type_required'), trigger: 'blur' }],
    publish_time: [{ required: true, message: t('Activity.info.validation.publish_time_required'), trigger: 'blur' }],
  }

  // 根据重复模式动态添加日期验证规则
  const repeatType = formData.value.schedule.repeat_type
  if (repeatType) {
    if (repeatType === 'once') {
      // 单次活动需要开始和结束时间
      baseRules['schedule.start_date'] = [{ required: true, message: t('Activity.info.validation.start_date_required'), trigger: 'blur' }]
      baseRules['schedule.end_date'] = [{ required: true, message: t('Activity.info.validation.end_date_required'), trigger: 'blur' }]
    } else if (repeatType === 'weekly') {
      // 每周活动需要开始和结束日期，以及时间段
      baseRules['schedule.start_date'] = [{ required: true, message: t('Activity.info.validation.start_date_activity_required'), trigger: 'blur' }]
      baseRules['schedule.end_date'] = [{ required: true, message: t('Activity.info.validation.end_date_activity_required'), trigger: 'blur' }]
      baseRules['schedule.repeat_frequency'] = [{ required: true, message: t('Activity.info.validation.repeat_frequency_required'), trigger: 'blur' }]
      // 验证时间段
      if (formData.value.schedule.time_slots && formData.value.schedule.time_slots.length > 0) {
        formData.value.schedule.time_slots.forEach((_, index) => {
          baseRules[`schedule.time_slots.${index}.day_of_week`] = [{ required: true, message: t('Activity.info.validation.day_of_week_required'), trigger: 'blur' }]
          baseRules[`schedule.time_slots.${index}.start_time`] = [{ required: true, message: t('Activity.info.validation.start_time_required'), trigger: 'blur' }]
          baseRules[`schedule.time_slots.${index}.end_time`] = [{ required: true, message: t('Activity.info.validation.end_time_required'), trigger: 'blur' }]
        })
      }
    } else if (repeatType === 'monthly') {
      // 每月活动需要开始和结束日期，以及重复频率和具体时间
      baseRules['schedule.start_date'] = [{ required: true, message: t('Activity.info.validation.start_date_activity_required'), trigger: 'blur' }]
      baseRules['schedule.end_date'] = [{ required: true, message: t('Activity.info.validation.end_date_activity_required'), trigger: 'blur' }]
      baseRules['schedule.repeat_frequency'] = [{ required: true, message: t('Activity.info.validation.repeat_frequency_required'), trigger: 'blur' }]
      baseRules['monthly_week'] = [{ required: true, message: t('Activity.info.validation.monthly_week_required'), trigger: 'blur' }]
      baseRules['monthly_day'] = [{ required: true, message: t('Activity.info.validation.monthly_day_required'), trigger: 'blur' }]
    } else if (repeatType === 'daily') {
      // 每日活动需要重复频率和时间段
      baseRules['schedule.repeat_frequency'] = [{ required: true, message: t('Activity.info.validation.daily_repeat_frequency_required'), trigger: 'blur' }]
      // 验证时间段
      if (formData.value.schedule.time_slots && formData.value.schedule.time_slots.length > 0) {
        formData.value.schedule.time_slots.forEach((_, index) => {
          baseRules[`schedule.time_slots.${index}.start_time`] = [{ required: true, message: t('Activity.info.validation.start_time_required'), trigger: 'blur' }]
          baseRules[`schedule.time_slots.${index}.end_time`] = [{ required: true, message: t('Activity.info.validation.end_time_required'), trigger: 'blur' }]
        })
      }
    }
    
    // 验证休息时间段（如果有）
    if (breakPeriods.value && formData.value.schedule.break_periods && formData.value.schedule.break_periods.length > 0) {
      formData.value.schedule.break_periods.forEach((_, index) => {
        baseRules[`schedule.break_periods.${index}.start_date`] = [{ required: true, message: t('Activity.info.validation.break_start_date_required'), trigger: 'blur' }]
        baseRules[`schedule.break_periods.${index}.end_date`] = [{ required: true, message: t('Activity.info.validation.break_end_date_required'), trigger: 'blur' }]
      })
    }
  }

  // 根据活动形式动态添加验证规则
  if (isMixedActivity.value) {
    // 混合模式需要验证所有字段
    return {
      ...baseRules,
      location: [{ required: true, message: t('Activity.info.validation.location_required'), trigger: 'blur' }],
      address: [{ required: true, message: t('Activity.info.validation.address_required'), trigger: 'blur' }],
      online_platform: [{ required: true, message: t('Activity.info.validation.online_platform_required'), trigger: 'blur' }],
      online_platform_url: [
        { required: true, message: t('Activity.info.validation.online_platform_url_required'), trigger: 'blur' },
        { 
          pattern: /^https?:\/\/.+/, 
          message: t('Activity.info.validation.online_platform_url_format'), 
          trigger: 'blur' 
        }
      ],
    }
  } else if (isOfflineActivity.value) {
    return {
      ...baseRules,
      location: [{ required: true, message: t('Activity.info.validation.location_required'), trigger: 'blur' }],
      address: [{ required: true, message: t('Activity.info.validation.address_required'), trigger: 'blur' }],
    }
  } else if (isOnlineActivity.value) {
    return {
      ...baseRules,
      online_platform: [{ required: true, message: t('Activity.info.validation.online_platform_required'), trigger: 'blur' }],
      online_platform_url: [
        { required: true, message: t('Activity.info.validation.online_platform_url_required'), trigger: 'blur' },
        { 
          pattern: /^https?:\/\/.+/, 
          message: t('Activity.info.validation.online_platform_url_format'), 
          trigger: 'blur' 
        }
      ],
    }
  }

  return baseRules
})

// 添加时间段操作
const addTimeSlots = () => {
  formData.value.schedule.time_slots.push({
    day_of_month: null,
    start_time: '',
    end_time: '',
    day_of_week: formData.value.schedule.repeat_type === 'weekly' ? '' : ''
  })
}

// 删除时间段操作
const removeTimeSlot = (index: number) => {
  if (formData.value.schedule.time_slots.length > 1) {
    formData.value.schedule.time_slots.splice(index, 1)
  }
}

// 月的时间操作
const addBreakPeriod = () => {
  formData.value.schedule.break_periods.push({
    start_date: '',
    end_date: ''
  })
}

// 删除休息时间段
const removeBreakPeriod = (index: number) => {
  formData.value.schedule.break_periods.splice(index, 1)
  // 如果删完了所有break periods，取消勾选复选框
  if (formData.value.schedule.break_periods.length === 0) {
    breakPeriods.value = false
  }
}

// 表单验证方法
const validateForm = async (): Promise<boolean> => {
  if (!formRef.value) return false
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    console.error('Form validation failed:', error)
    return false
  }
}

// 处理下一步
const handleNext = async () => {
  const isValid = await validateForm()
  if (isValid) {
    emit('next-step')
  }
}

// 监听表单数据变化，向父组件发送数据
watch(formData, () => {
  emit('form-data-change', processedFormData.value)
}, { deep: true })

// 重复次数变化，每日模式时处理时间数据
watch(() => formData.value.schedule.repeat_frequency, (newVal) => {
  // 如果正在数据加载中，不执行重置逻辑
  if (isDataLoading.value) {
    return
  }
  
  if (formData.value.schedule.repeat_type === 'daily') {
    const currentLength = formData.value.schedule.time_slots.length
    
    if (newVal > currentLength) {
      // 需要增加time_slots
      const slotsToAdd = newVal - currentLength
      for (let i = 0; i < slotsToAdd; i++) {
        formData.value.schedule.time_slots.push({
          day_of_month: null,
          start_time: '',
          end_time: '',
          day_of_week: '' // 每日模式不使用day_of_week
        })
      }
    } else if (newVal < currentLength) {
      // 需要减少time_slots
      formData.value.schedule.time_slots = formData.value.schedule.time_slots.slice(0, newVal)
    }
  }
})

watch(() => breakPeriods.value, (newVal) => {
  if (newVal && formData.value.schedule.break_periods.length === 0) {
    formData.value.schedule.break_periods.push({
      start_date: '',
      end_date: ''
    })
  } else if (!newVal) {
    // 取消选中时清空所有break periods
    formData.value.schedule.break_periods = []
  }
})

// 监听活动形式变化，清空相关字段
watch(() => formData.value.type, (newType, oldType) => {
  // 只有在用户主动切换类型时才清空字段（oldType 不为空表示不是初始化）
  // 增加额外的保护：确保不是在数据回填过程中
  if (oldType && oldType !== newType && !isDataLoading.value) {
    // 清空所有地点相关字段
    formData.value.location = ''
    formData.value.address = ''
    formData.value.online_platform = ''
    formData.value.online_platform_url = ''
  }
})

// 监听重复模式变化，重置time_slots
watch(() => formData.value.schedule.repeat_type, (newRepeatType) => {
  // 如果正在数据加载中，不执行重置逻辑
  if (isDataLoading.value) {
    return
  }
  
  // 根据重复模式重置time_slots
  if (newRepeatType === 'weekly') {
    // 每周模式：重置为单个包含day_of_week的time_slot
    formData.value.schedule.time_slots = [
      {
        day_of_month: null,
        start_time: '',
        end_time: '',
        day_of_week: ''
      }
    ]
  } else if (newRepeatType === 'daily') {
    // 每日模式：根据repeat_frequency设置time_slots数量，不包含day_of_week
    const frequency = formData.value.schedule.repeat_frequency || 1
    formData.value.schedule.time_slots = Array.from({ length: frequency }, () => ({
      day_of_month: null,
      start_time: '',
      end_time: '',
      day_of_week: ''
    }))
  } else {
    // 单次或每月模式：清空time_slots但保持数组结构以避免Vue响应式错误
    formData.value.schedule.time_slots = []
  }
})

watch(() => formData.value.schedule.break_periods, (newVal) => {
  if (newVal.length > 0) {
    breakPeriods.value = true
  } else {
    breakPeriods.value = false
  }
})

// 添加数据加载状态标记
const isDataLoading = ref(false)

// 修改updateFormData方法，增加数据加载状态控制
defineExpose({
  validateForm,
  formData,
  updateFormData: (data: any) => {
    if (data) {
      // 设置数据加载状态，防止watch触发
      isDataLoading.value = true
      
      // 深度合并数据，保持响应式
      Object.keys(data).forEach(key => {
        if (key === 'schedule' && data[key]) {
          // 特殊处理 schedule 对象
          const scheduleData = data[key]
          
          // 基础字段
          formData.value.schedule.repeat_type = scheduleData.repeat_type || ''
          formData.value.schedule.repeat_frequency = scheduleData.repeat_frequency || 1
          formData.value.schedule.start_date = scheduleData.start_date || ''
          formData.value.schedule.end_date = scheduleData.end_date || ''
          
          // 特殊处理 time_slots
          if (scheduleData.time_slots && Array.isArray(scheduleData.time_slots)) {
            formData.value.schedule.time_slots = scheduleData.time_slots.map((slot: any) => ({
              day_of_month: slot.day_of_month || null,
              start_time: slot.start_time || '',
              end_time: slot.end_time || '',
              day_of_week: slot.day_of_week || ''
            }))
          }
          
          // 特殊处理 break_periods
          if (scheduleData.break_periods && Array.isArray(scheduleData.break_periods)) {
            formData.value.schedule.break_periods = scheduleData.break_periods.map((period: any) => ({
              start_date: period.start_date || '',
              end_date: period.end_date || ''
            }))
          }
        } else if (data[key] !== undefined) {
          (formData.value as any)[key] = data[key]
        }
      })
      
      console.log('Updated formData:', JSON.stringify(formData.value, null, 2))
      
      // 延迟重置数据加载状态，确保数据回填完成
      nextTick(() => {
        setTimeout(() => {
          isDataLoading.value = false
        }, 100)
      })
    }
  }
})

</script>

<script lang="ts">
export default {
  name: 'Info'
}
</script>

<style lang="scss" scoped>
.info-big-box {
  height: 100%;
  overflow: scroll;
  overflow-y: auto;
  padding: 0;
  margin: 0;
  width: 100%;
  
  // 信息卡片样式 - 参考form.vue的chart-container
  .info-card {
    background: #FFFFFF;
    box-shadow: 0px 1px 1px #0000000D;
    border-radius: 10px;
    margin-bottom: 20px;
    width: 100%;
    
    // 第一个卡片样式 - 与tabs无缝连接
    &:first-child {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      margin-top: 0;
    }
    
    .card-header {
      padding: 20px 20px 0 20px;
      border-bottom: none;
      
      .card-title {
        font: normal normal normal 16px/21px Microsoft JhengHei;
        letter-spacing: 0px;
        color: #000000;
        margin: 0 0 20px 0;
        font-weight: 600;
      }
    }
    
    .card-content {
      padding: 0 20px 20px 20px;
    }
  }
  
  // 添加样式
  .title {
    font-size: 24px;
    display: flex;
    align-items: center;
    margin: 20px 0px;
  }
  .op-box {
    display: flex;
    justify-content: center;
    padding-bottom: 30px;
    margin-top: 30px;
  }
}

// 圆形按钮样式
.circle-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  
  &.circle-btn-primary {
    background-color: #409EFF;
    color: white;
    
    &:hover {
      background-color: #66b1ff;
      transform: scale(1.1);
    }
    
    &:active {
      background-color: #337ecc;
      transform: scale(0.95);
    }
  }
  
  &.circle-btn-danger {
    background-color: #F56C6C;
    color: white;
    
    &:hover {
      background-color: #f78989;
      transform: scale(1.1);
    }
    
    &:active {
      background-color: #dd6161;
      transform: scale(0.95);
    }
  }
  
  .el-icon {
    font-size: 16px;
  }
}
:deep(.el-form) {
 width: 100% !important;
}
</style> 