<template>
  <div class="application-box">
    <el-form :model="formData" ref="formRef" label-position="top" label-width="120px">
      
      <!-- 應用規則卡片 -->
      <div class="application-card">
        <div class="card-header">
          <h3 class="card-title">{{ $t('Activity.application.card.rule_settings') }}</h3>
        </div>
        <div class="card-content">
          <el-form-item :label="$t('Activity.application.form.rule_template')">
            <el-select 
              v-model="formData.rule_id" 
              :placeholder="$t('Activity.application.form.rule_template_placeholder')"
              style="width: 750px;"
              clearable
              :loading="ruleTemplateLoading"
              :loading-text="$t('Activity.application.form.loading_text')"
            >
              <el-option 
                v-for="item in ruleTemplateList" 
                :key="item.id" 
                :label="item.name" 
                :value="item.id"
              >
                <div>
                  <div style="font-weight: bold;">{{ item.name }}</div>
                  <div style="font-size: 12px; color: #999;">
                    {{ item.scene || $t('Activity.application.form.general_rule') }} | 
                    {{ $t('Activity.application.form.status') }}: 
                    {{ item.status === 1 
                      ? $t('Activity.application.form.enabled') 
                      : $t('Activity.application.form.disabled') 
                    }}
                  </div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>
    </el-form>

    <div class="op-box">
      <el-button @click="handlePrev">{{ $t('Activity.application.button.prev') }}</el-button>
      <el-button @click="handlePublish" type="primary">
        {{ isEditMode 
          ? $t('Activity.application.button.modify') 
          : $t('Activity.application.button.publish') 
        }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, computed, onMounted } from 'vue'
import type { FormInstance } from 'element-plus'
import { ruleTemplateService } from '../../../services/ruleTemplateService'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface ApplicationFormData {
  rule_id: string | number
}

const props = defineProps({
  activityConfig: {
    type: Object,
    default: () => ({})
  },
  isEditMode: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['prev-step', 'publish', 'form-data-change'])

const formRef = ref<FormInstance>()
const ruleTemplateList = ref<any[]>([])
const ruleTemplateLoading = ref(false)

const formData = ref<ApplicationFormData>({
  rule_id: ''
})

// 获取规则模板列表
const getRuleTemplateList = async () => {
  try {
    ruleTemplateLoading.value = true
    
    const res = await ruleTemplateService.getList({
      page: 1,
      per_page: 99999,
      status: '1'
    })
    
    if (res.status === 200 && res?.data?.code === 200) {
      console.log('获取规则模板列表成功：', res.data.data)
      ruleTemplateList.value = res.data.data?.items || []
    } else {
      console.error('获取规则模板列表失败：', res.data?.message)
      ElMessage.error(res.data?.message || t('Activity.application.message.get_template_failed'))
    }
  } catch (error: any) {
    console.error('Get rule template list error:', error)
    ElMessage.error(t('Activity.application.message.get_template_failed') + ', ' + t('Activity.application.message.try_later'))
  } finally {
    ruleTemplateLoading.value = false
  }
}

// 表单验证方法
const validateForm = async (): Promise<boolean> => {
  if (!formRef.value) return false
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    console.error('Form validation failed:', error)
    ElMessage.error(t('Activity.application.message.validation_failed'))
    return false
  }
}

// 处理上一步
const handlePrev = () => {
  emit('prev-step')
}

// 处理发布/修改
const handlePublish = async () => {
  const isValid = await validateForm()
  if (isValid) {
    emit('publish')
  }
}

// 监听表单数据变化，向父组件发送数据
watch(formData, (newVal) => {
  emit('form-data-change', newVal)
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  validateForm,
  formData,
  updateFormData: (data: any) => {
    if (data) {
      formData.value = {
        ...formData.value,
        ...data
      }
    }
  }
})

onMounted(() => {
  getRuleTemplateList()
})
</script>

<script lang="ts">
export default {
  name: 'Application'
}
</script>

<style lang="scss" scoped>
.application-box {
  height: 100%;
  overflow: scroll;
  overflow-y: auto;
  padding: 0;
  margin: 0;
  width: 100%;
  
  // 应用卡片样式 - 参考Info.vue的info-card
  .application-card {
    background: #FFFFFF;
    box-shadow: 0px 1px 1px #0000000D;
    border-radius: 10px;
    margin-bottom: 20px;
    width: 100%;
    
    // 第一个卡片样式 - 与tabs无缝连接
    &:first-child {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      margin-top: 0;
    }
    
    .card-header {
      padding: 20px 20px 0 20px;
      border-bottom: none;
      
      .card-title {
        font: normal normal normal 16px/21px Microsoft JhengHei;
        letter-spacing: 0px;
        color: #000000;
        margin: 0 0 20px 0;
        font-weight: 600;
      }
    }
    
    .card-content {
      padding: 0 20px 20px 20px;
    }
  }
}

:deep(.el-form) {
  width: 100% !important;
}

.op-box {
  margin-top: 20px;
  display: flex;
  width: 100%;
  justify-content: center;
  gap: 20px;
  padding-bottom: 30px;
}
</style> 