<template>
  <el-dialog
    v-model="props.visible"
    :title="props.mode === 'edit' ? $t('Activity.group.dialog.edit_title') : $t('Activity.group.dialog.create_title')"
    width="600px"
    @close="handleCancel"
  >
    <!-- 编辑模式加载状态 -->
    <div v-if="props.mode === 'edit' && detailLoading" class="loading-container">
      <el-skeleton :rows="6" animated />
      <div class="loading-text">{{ $t('Activity.group.dialog.loading_text') }}</div>
    </div>
    
    <!-- 表单内容 -->
    <el-form 
      v-show="!(props.mode === 'edit' && detailLoading)"
      :model="formData" 
      :rules="rules" 
      ref="formRef" 
      label-position="top"
    >
      <el-form-item :label="$t('Activity.group.form.name')" prop="name" required>
        <el-input 
          v-model="formData.name" 
          :placeholder="$t('Activity.group.form.name_placeholder')"
          :disabled="loading"
        />
      </el-form-item>
      
      <el-form-item :label="$t('Activity.group.form.code')" prop="code" required>
        <el-input 
          v-model="formData.code" 
          :placeholder="$t('Activity.group.form.code_placeholder')"
          :disabled="loading || props.mode === 'edit'"
        />
      </el-form-item>
      
      <el-form-item :label="$t('Activity.group.form.max_members')" prop="max_members" required>
        <el-input-number 
          v-model="formData.max_members" 
          :min="1" 
          :max="9999"
          :placeholder="$t('Activity.group.form.max_members_placeholder')"
          style="width: 100%;"
          :disabled="loading"
        />
      </el-form-item>
      
      <el-form-item :label="$t('Activity.group.form.can_register')" prop="can_register" required>
        <el-radio-group v-model="formData.can_register" :disabled="loading">
          <el-radio :label="1">{{ $t('Activity.group.form.can_register_yes') }}</el-radio>
          <el-radio :label="0">{{ $t('Activity.group.form.can_register_no') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item :label="$t('Activity.group.form.start_time')" prop="start_time" required>
        <el-date-picker 
          v-model="formData.start_time" 
          type="datetime" 
          :placeholder="$t('Activity.group.form.start_time_placeholder')"
          style="width: 100%;"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :disabled="loading"
        />
      </el-form-item>
      
      <el-form-item :label="$t('Activity.group.form.end_time')" prop="end_time" required>
        <el-date-picker 
          v-model="formData.end_time" 
          type="datetime" 
          :placeholder="$t('Activity.group.form.end_time_placeholder')"
          style="width: 100%;"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :disabled="loading"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading || detailLoading">
          {{ $t('Activity.group.button.cancel') }}
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="loading"
          :disabled="loading || detailLoading"
        >
          {{ loading 
            ? (props.mode === 'edit' 
              ? $t('Activity.group.button.updating') 
              : $t('Activity.group.button.creating'))
            : (props.mode === 'edit' 
              ? $t('Activity.group.button.update') 
              : $t('Activity.group.button.create')) 
          }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { groupService } from '../../../services/activityervice'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface GroupFormData {
  name: string
  code: string
  max_members: number | null
  can_register: number
  start_time: string
  end_time: string
}

const props = defineProps<{
  visible: boolean
  mode?: 'create' | 'edit'
  groupData?: any
}>()

const emit = defineEmits(['update:visible', 'cancel', 'create'])

const formRef = ref<FormInstance>()
const loading = ref(false)
const detailLoading = ref(false)

const formData = ref<GroupFormData>({
  name: '',
  code: '',
  max_members: null,
  can_register: 1,
  start_time: '',
  end_time: ''
})

// 获取群组详情
const fetchGroupDetail = async (groupId: number) => {
  try {
    detailLoading.value = true
    
    const response = await groupService.getGroup(groupId)
    
    if (response.status === 200 && response.data?.code === 200) {
      const data = response.data.data
      formData.value = {
        name: data.name || '',
        code: data.code || '',
        max_members: data.max_members ? Number(data.max_members) : null,
        can_register: data.can_register ? Number(data.can_register) : 1,
        start_time: data.start_time || '',
        end_time: data.end_time || ''
      }
    } else {
      ElMessage.error(t('Activity.group.message.get_detail_failed'))
      emit('update:visible', false)
    }
  } catch (error: any) {
    console.error('Fetch group detail error:', error)
    ElMessage.error(t('Activity.group.message.get_detail_failed') + ', ' + t('Activity.group.message.retry_later'))
    emit('update:visible', false)
  } finally {
    detailLoading.value = false
  }
}

watch([() => props.visible, () => props.mode, () => props.groupData], ([visible, mode, groupData]) => {
  if (visible) {
    if (mode === 'edit' && groupData?.id) {
      fetchGroupDetail(groupData.id)
    } else if (mode === 'create') {
      resetForm()
    }
  }
}, { immediate: true })

// 表单验证规则
const rules = ref<FormRules<GroupFormData>>({
  name: [
    { required: true, message: t('Activity.group.validation.name_required'), trigger: 'blur' },
    { min: 2, max: 50, message: t('Activity.group.validation.name_length'), trigger: 'blur' }
  ],
  code: [
    { required: true, message: t('Activity.group.validation.code_required'), trigger: 'blur' },
    { min: 2, max: 20, message: t('Activity.group.validation.code_length'), trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: t('Activity.group.validation.code_format'), trigger: 'blur' }
  ],
  max_members: [
    { required: true, message: t('Activity.group.validation.max_members_required'), trigger: 'blur' },
    { type: 'number', min: 1, max: 9999, message: t('Activity.group.validation.max_members_range'), trigger: 'blur' }
  ],
  can_register: [
    { required: true, message: t('Activity.group.validation.can_register_required'), trigger: 'change' }
  ],
  start_time: [
    { required: true, message: t('Activity.group.validation.start_time_required'), trigger: 'blur' }
  ],
  end_time: [
    { required: true, message: t('Activity.group.validation.end_time_required'), trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value && formData.value.start_time && new Date(value) <= new Date(formData.value.start_time)) {
          callback(new Error(t('Activity.group.validation.end_time_after_start')))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  formData.value = {
    name: '',
    code: '',
    max_members: null,
    can_register: 1,
    start_time: '',
    end_time: ''
  }
}

const handleCancel = () => {
  if (loading.value || detailLoading.value) return
  
  if (props.mode === 'create') {
    resetForm()
  }
  emit('update:visible', false)
  emit('cancel')
}

const handleSubmit = async () => {
  if (!formRef.value || loading.value || detailLoading.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    const submitData = {
      name: formData.value.name,
      code: formData.value.code,
      max_members: String(formData.value.max_members || 0),
      can_register: String(formData.value.can_register),
      start_time: formData.value.start_time,
      end_time: formData.value.end_time
    }
    
    let response
    if (props.mode === 'edit' && props.groupData?.id) {
      response = await groupService.update(props.groupData.id, submitData)
    } else {
      response = await groupService.create(submitData)
    }
    
    if (response.status === 200 && response.data?.code === 200) {
      ElMessage.success(props.mode === 'edit' 
        ? t('Activity.group.message.update_success')
        : t('Activity.group.message.create_success'))
      emit('create', response.data.data)
      
      if (props.mode === 'create') {
        resetForm()
      }
      emit('update:visible', false)
    } else {
      ElMessage.error(response.data?.message || (props.mode === 'edit' 
        ? t('Activity.group.message.update_failed')
        : t('Activity.group.message.create_failed')))
    }
  } catch (error: any) {
    console.error('Submit group failed:', error)
    if (error.message && error.message.includes('validation')) {
      ElMessage.error(t('Activity.group.message.form_validation_failed'))
    } else {
      ElMessage.error(error.message || (props.mode === 'edit'
        ? t('Activity.group.message.update_failed') + ', ' + t('Activity.group.message.retry_later')
        : t('Activity.group.message.create_failed') + ', ' + t('Activity.group.message.retry_later')))
    }
  } finally {
    loading.value = false
  }
}
</script>

<script lang="ts">
export default {
  name: 'AddGrounp'
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  margin-right: 0;
}

:deep(.el-form-item.is-required .el-form-item__label::before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

// Loading状态下的样式调整
:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f7fa;
}

:deep(.el-input-number.is-disabled .el-input-number__decrease),
:deep(.el-input-number.is-disabled .el-input-number__increase) {
  background-color: #f5f7fa;
}

:deep(.el-radio.is-disabled .el-radio__label) {
  color: #c0c4cc;
}

// 加载容器样式
.loading-container {
  padding: 20px;
  text-align: center;
}

.loading-text {
  margin-top: 15px;
  color: #909399;
  font-size: 14px;
}
</style> 