<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <div class="btn-list">
        <el-button @click="handleBack">{{ $t('Activity.event_create.button.back') }}</el-button>
      </div>
    </div>

    <div class="module-con">
      <div class="box">
       <div class="content-box">
        <!-- 骨架屏Loading -->
        <div v-if="pageLoading" class="skeleton-container">
          <el-skeleton :rows="8" animated class="skeleton-content">
            <template #template>
              <div class="skeleton-header">
                <el-skeleton-item variant="text" style="width: 30%; height: 40px; margin-bottom: 20px;" />
              </div>
              <div class="skeleton-tabs">
                <div class="skeleton-tab-header">
                  <el-skeleton-item variant="button" style="width: 100px; height: 40px; margin-right: 20px;" />
                  <el-skeleton-item variant="button" style="width: 120px; height: 40px; margin-right: 20px;" />
                  <el-skeleton-item variant="button" style="width: 100px; height: 40px;" />
                </div>
                <div class="skeleton-form">
                  <div class="skeleton-form-row">
                    <el-skeleton-item variant="text" style="width: 15%; height: 20px; margin-bottom: 8px;" />
                    <el-skeleton-item variant="p" style="width: 100%; height: 40px; margin-bottom: 20px;" />
                  </div>
                  <div class="skeleton-form-row-double">
                    <div class="skeleton-form-col">
                      <el-skeleton-item variant="text" style="width: 30%; height: 20px; margin-bottom: 8px;" />
                      <el-skeleton-item variant="p" style="width: 100%; height: 40px; margin-bottom: 20px;" />
                    </div>
                    <div class="skeleton-form-col">
                      <el-skeleton-item variant="text" style="width: 30%; height: 20px; margin-bottom: 8px;" />
                      <el-skeleton-item variant="p" style="width: 100%; height: 40px; margin-bottom: 20px;" />
                    </div>
                  </div>
                  <div class="skeleton-form-row-triple">
                    <div class="skeleton-form-col">
                      <el-skeleton-item variant="text" style="width: 40%; height: 20px; margin-bottom: 8px;" />
                      <el-skeleton-item variant="p" style="width: 100%; height: 40px; margin-bottom: 20px;" />
                    </div>
                    <div class="skeleton-form-col">
                      <el-skeleton-item variant="text" style="width: 40%; height: 20px; margin-bottom: 8px;" />
                      <el-skeleton-item variant="p" style="width: 100%; height: 40px; margin-bottom: 20px;" />
                    </div>
                    <div class="skeleton-form-col">
                      <el-skeleton-item variant="text" style="width: 50%; height: 20px; margin-bottom: 8px;" />
                      <el-skeleton-item variant="p" style="width: 100%; height: 40px; margin-bottom: 20px;" />
                    </div>
                  </div>
                  <div class="skeleton-form-row">
                    <el-skeleton-item variant="text" style="width: 15%; height: 20px; margin-bottom: 8px;" />
                    <el-skeleton-item variant="p" style="width: 100%; height: 40px; margin-bottom: 20px;" />
                  </div>
                  <div class="skeleton-form-row">
                    <el-skeleton-item variant="text" style="width: 15%; height: 20px; margin-bottom: 8px;" />
                    <el-skeleton-item variant="p" style="width: 50%; height: 40px; margin-bottom: 20px;" />
                  </div>
                  <div class="skeleton-form-footer">
                    <el-skeleton-item variant="button" style="width: 80px; height: 40px;" />
                  </div>
                </div>
              </div>
            </template>
          </el-skeleton>
        </div>
        
        <!-- 主要内容 -->
        <el-tabs v-else v-model="activeName">
          <el-tab-pane :label="$t('Activity.event_create.tab.basic_info')" name="first">
            <Info 
              ref="InfoRef" 
              :activityConfig="activityConfig" 
              @next-step="handleNextStep"
              @form-data-change="handleInfoDataChange"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('Activity.event_create.tab.registration')" name="second" :disabled="!isStepCompleted('first')">
            <SignUp 
              ref="SignUpRef" 
              :activityConfig="activityConfig" 
              :currentLocalization="currentLocalization"
              @prev-step="handlePrevStep"
              @next-step="handleNextStep"
              @form-data-change="handleSignUpDataChange"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('Activity.event_create.tab.rules')" name="third" :disabled="!isStepCompleted('second')">
            <Application 
              ref="ApplicationRef" 
              :activityConfig="activityConfig" 
              :isEditMode="isEditMode"
              @prev-step="handlePrevStep"
              @publish="handlePublish"
              @form-data-change="handleApplicationDataChange"
            />
          </el-tab-pane>
        </el-tabs>
       </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Info from "./create-view/Info.vue"
import SignUp from "./create-view/SignUp.vue"
import Application from "./create-view/Application.vue"
import { ElMessage } from 'element-plus'
import { activityervice } from "../../services/activityervice"
import { useI18n } from 'vue-i18n'
import type { 
  InfoFormData, 
  SignUpFormData, 
  ApplicationFormData, 
  FormDataStore 
} from './types'

const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const activeName = ref('first')
const InfoRef = ref()
const SignUpRef = ref()
const ApplicationRef = ref()
const activityConfig = ref({})
const pageLoading = ref(true)

const handleBack = () => {
  router.back()
}

// 判断是否为编辑模式
const isEditMode = computed(() => !!route.params.id)

// 存储各个步骤的表单数据
const formDataStore = ref<FormDataStore>({
  info: {},
  signUp: {},
  application: {}
})

// 记录已完成的步骤
const completedSteps = ref<Set<string>>(new Set())

// 当前选中的本地化区域信息
const currentLocalization = ref<any>(null)

// 检查步骤是否完成
const isStepCompleted = (stepName: string): boolean => {
  return completedSteps.value.has(stepName)
}

// 标记步骤为完成
const markStepCompleted = (stepName: string) => {
  completedSteps.value.add(stepName)
}

// 处理下一步
const handleNextStep = async () => {
  const tabOrder = ['first', 'second', 'third']
  const currentIndex = tabOrder.indexOf(activeName.value)
  
  // 验证当前步骤的表单
  let isCurrentStepValid = false
  
  if (activeName.value === 'first' && InfoRef.value?.validateForm) {
    isCurrentStepValid = await InfoRef.value.validateForm()
  } else if (activeName.value === 'second' && SignUpRef.value?.validateForm) {
    isCurrentStepValid = await SignUpRef.value.validateForm()
  } else if (activeName.value === 'third' && ApplicationRef.value?.validateForm) {
    isCurrentStepValid = await ApplicationRef.value.validateForm()
  }
  
  if (!isCurrentStepValid) {
    ElMessage.error(t('Activity.event_create.message.form_incomplete'))
    return
  }
  
  // 标记当前步骤为完成
  markStepCompleted(activeName.value)
  
  // 切换到下一步
  if (currentIndex < tabOrder.length - 1) {
    activeName.value = tabOrder[currentIndex + 1]
  }
}

// 处理上一步
const handlePrevStep = () => {
  const tabOrder = ['first', 'second', 'third']
  const currentIndex = tabOrder.indexOf(activeName.value)
  
  if (currentIndex > 0) {
    activeName.value = tabOrder[currentIndex - 1]
  }
}

// 处理各个组件的数据变化
const handleInfoDataChange = (data: Partial<InfoFormData>) => {
  formDataStore.value.info = data
}

// 监听本地化区域变化
watch(
  () => formDataStore.value.info.localized_id,
  (newLocalizedId) => {
    if (newLocalizedId && (activityConfig.value as any).localizations) {
      const localization = (activityConfig.value as any).localizations.find(
        (loc: any) => loc.id === Number(newLocalizedId)
      )
      if (localization) {
        currentLocalization.value = localization
      }
    }
  },
  { immediate: true }
)

const handleSignUpDataChange = (data: Partial<SignUpFormData>) => {
  formDataStore.value.signUp = data
}

const handleApplicationDataChange = (data: Partial<ApplicationFormData>) => {
  formDataStore.value.application = data
}

// 获取活动详情
const getActivityDetail = async (id: number) => {
  try {
    const res = await activityervice.getDetail(id)
    console.log(res)
    if (res.status === 200 && res?.data?.code === 200) {
      const activityData = res.data.data
      console.log(activityData)
      
      // 更新表单数据
      formDataStore.value = {
        info: {
          title: activityData.title,
          category: activityData.category,
          localized_id: activityData.localized_id,
          organizer_last_name: activityData.organizer_last_name,
          organizer_first_name: activityData.organizer_first_name,
          organizer_email: activityData.organizer_email,
          type: activityData.type,
          location: activityData.location || '',
          address: activityData.address || '',
          online_platform: activityData.online_platform || '',
          online_platform_url: activityData.online_platform_url || '',
          publish_time: activityData.publish_time,
          schedule: {
            repeat_type: activityData.schedule.repeat_type,
            repeat_frequency: activityData.schedule.repeat_frequency || 1,
            start_date: activityData.schedule.start_date,
            end_date: activityData.schedule.end_date,
            time_slots: activityData.schedule.time_slots || [],
            break_periods: activityData.schedule.break_periods || []
          },
        },
        signUp: {
          registration_deadline: activityData.registration_deadline,
          registration_limit_type: activityData.registration_limit_type,
          registration_limit: activityData.registration_limit,
          target_participants: activityData.target_participants,
          registration_type: activityData.registration_type,
          public_limit: activityData.public_limit,
          hidden_limit: activityData.hidden_limit,
          reserved_limit: activityData.reserved_limit,
          is_fee_required: activityData.is_fee_required,
          invoice_prefix: activityData.invoice_prefix || '',
          target_income: activityData.target_income || 0,
          registration_method: activityData.registration_method,
          ticket_delivery_method: activityData.ticket_delivery_method,
          email_template: activityData.email_template || '',
          ticket: activityData.ticket || [],
          groups: activityData.groups || [],
          registration_method_rules: activityData.registration_method_rules || {
            auto_promote: false,
            notification_enabled: true
          }
        },
        application: {
          rule_id: activityData.rule_id || '',
          eventType: 'none'
        }
      }

      // 设置当前本地化信息
      if (activityData.localization) {
        currentLocalization.value = activityData.localization
      }
      
      // 标记所有步骤为完成
      completedSteps.value = new Set(['first', 'second', 'third'])

      // 延迟通知子组件更新数据，确保loading状态完成后再执行
      setTimeout(() => {
        nextTick(() => {
          if (InfoRef.value?.updateFormData) {
            InfoRef.value.updateFormData(formDataStore.value.info)
          }
          if (SignUpRef.value?.updateFormData) {
            SignUpRef.value.updateFormData(formDataStore.value.signUp)
          }
          if (ApplicationRef.value?.updateFormData) {
            ApplicationRef.value.updateFormData(formDataStore.value.application)
          }
        })
      }, 400)
    } else {
      ElMessage.error(t('Activity.event_create.message.detail_failed'))
    }
  } catch (error: any) {
    console.error('Get activity detail error:', error)
    ElMessage.error(t('Activity.event_create.message.detail_failed'))
  }
}

// 处理发布
const handlePublish = async () => {
  try {
    // 验证所有表单数据
    const isValid = await validateAllForms()
    if (!isValid) {
      ElMessage.error(t('Activity.event_create.message.please_check_form'))
      return
    }

    // 合并所有数据
    const requestData = mergeFormData()
    console.log('Final request data:', requestData)
    
    let response
    if (isEditMode.value) {
      // 编辑模式：调用更新接口
      response = await activityervice.update(Number(route.params.id), requestData)
    } else {
      // 新建模式：调用创建接口
      response = await activityervice.create(requestData)
    }
    
    if (response.status === 200 && response?.data?.code === 200) {
      ElMessage.success(isEditMode.value 
        ? t('Activity.event_create.message.update_success')
        : t('Activity.event_create.message.create_success'))
      // 跳转到活动列表页面
      setTimeout(() => {
        router.push({ name: 'EventList' })
      }, 1500)
    } else {
      ElMessage.error(response.data?.message || (isEditMode.value 
        ? t('Activity.event_create.message.update_failed')
        : t('Activity.event_create.message.create_failed')))
    }
  } catch (error: any) {
    console.error('Handle activity error:', error)
    ElMessage.error(error.message || (isEditMode.value 
      ? t('Activity.event_create.message.update_failed') + ', ' + t('Activity.event_create.message.try_later')
      : t('Activity.event_create.message.create_failed') + ', ' + t('Activity.event_create.message.try_later')))
  }
}

// 验证所有表单
const validateAllForms = async (): Promise<boolean> => {
  try {
    // 验证Info表单
    if (InfoRef.value?.validateForm) {
      const infoValid = await InfoRef.value.validateForm()
      if (!infoValid) {
        activeName.value = 'first'
        ElMessage.error(t('Activity.event_create.message.basic_info_incomplete'))
        return false
      }
    }

    // 验证SignUp表单
    if (SignUpRef.value?.validateForm) {
      const signUpValid = await SignUpRef.value.validateForm()
      if (!signUpValid) {
        activeName.value = 'second'
        ElMessage.error(t('Activity.event_create.message.registration_incomplete'))
        return false
      }
    }

    // 验证Application表单
    if (ApplicationRef.value?.validateForm) {
      const applicationValid = await ApplicationRef.value.validateForm()
      if (!applicationValid) {
        activeName.value = 'third'
        ElMessage.error(t('Activity.event_create.message.rules_incomplete'))
        return false
      }
    }

    return true
  } catch (error) {
    console.error('Form validation error:', error)
    return false
  }
}

// 合并表单数据
const mergeFormData = () => {
  const info = formDataStore.value.info
  const signUp = formDataStore.value.signUp
  const application = formDataStore.value.application

  // 处理活动类型，确保兼容性
  const activityType = info.type
  let mappedType: 'online' | 'offline' | undefined
  if (activityType === 'online') {
    mappedType = 'online'
  } else if (activityType === 'offline') {
    mappedType = 'offline'
  } else {
    mappedType = undefined
  }

  // 基础数据结构
  const requestData: any = {
    // 基本信息
    title: info.title || '',
    category: info.category || '',
    localized_id: Number(info.localized_id) || 0,
    organizer_last_name: info.organizer_last_name || '',
    organizer_first_name: info.organizer_first_name || '',
    organizer_email: info.organizer_email || '',
    type: mappedType,
    location: info.location || '',
    address: info.address || '',
    online_platform: info.online_platform || '',
    online_platform_url: info.online_platform_url || '',
    publish_time: info.publish_time || '',
    schedule: info.schedule || {},
    
    // 报名设置
    registration_deadline: signUp.registration_deadline || '',
    registration_limit_type: Number(signUp.registration_limit_type) || 0,
    registration_limit: Number(signUp.registration_limit) || 0,
    target_participants: Number(signUp.target_participants) || 0,
    registration_type: Number(signUp.registration_type) || 0,
    public_limit: Number(signUp.public_limit) || 0,
    hidden_limit: Number(signUp.hidden_limit) || 0,
    reserved_limit: Number(signUp.reserved_limit) || 0,
    is_fee_required: Number(signUp.is_fee_required) || 0,
    registration_method: signUp.registration_method || '',
    
    // 应用规则
    ...application,
    
    // 添加原始的活动类型用于后端处理
    original_type: info.type || ''
  }

  // 根据是否收费添加相关字段
  if (Number(signUp.is_fee_required) === 1) {
    requestData.invoice_prefix = signUp.invoice_prefix || ''
    requestData.target_income = Number(signUp.target_income) || 0
    requestData.groups = signUp.groups || []
    requestData.ticket_delivery_method = signUp.ticket_delivery_method || ''
    requestData.email_template = signUp.email_template || ''
    requestData.ticket = signUp.ticket || []
  }
  
  // 根据报名超限处理方式添加相关字段
  if (signUp.registration_method === 'waiting_list' && (signUp as any).registration_method_rules) {
    requestData.registration_method_rules = (signUp as any).registration_method_rules
  }

  return requestData
}

onMounted(async () => {
  pageLoading.value = true
  
  try {
    // 并行获取活动配置和详情（如果是编辑模式）
    const promises = []
    
    // 获取活动配置
    promises.push(
      activityervice.getActivityType().then(configRes => {
        if (configRes.status === 200 && configRes?.data?.code === 200) {
          console.log(configRes.data.data)
          activityConfig.value = configRes.data.data
        } else {
          ElMessage.error(t('Activity.event_create.message.config_failed'))
        }
      }).catch(error => {
        console.error('Get activity config error:', error)
        ElMessage.error(t('Activity.event_create.message.config_failed'))
      })
    )

    // 如果是编辑模式，获取活动详情
    if (isEditMode.value) {
      promises.push(getActivityDetail(Number(route.params.id)))
    }

    // 等待所有请求完成
    await Promise.all(promises)
    
  } catch (error: any) {
    console.error('Get activity error:', error)
    ElMessage.error(t('Activity.event_create.message.config_failed'))
  } finally {
    // 延迟一点时间显示加载效果，提升用户体验
    setTimeout(() => {
      pageLoading.value = false
    }, 300)
  }
})
</script>

<style lang="scss" scoped>
.bwms-module {
  background: transparent;
  
  .module-con {
    background: transparent;
    
    .box {
     
        background: transparent;
        padding: 0;
        overflow: scroll;
        
        .content-box {
          height: 100%;
          background: transparent;
        
        // tabs 样式优化 - 与 ActivityDetailTabs.vue transparent-bg 保持一致
        :deep(.el-tabs) {
          .el-tabs__header {
            background: #FFFFFF;
            border-radius: 10px 10px 0 0;
            padding: 10px 20px 0 20px;
            margin-bottom: 0;
            position: relative;
            
            // 添加底部分隔线，左右各20px间隔
            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 20px;
              right: 20px;
              z-index: 1;
            }
            
            .el-tabs__nav-wrap {
              margin-bottom: 0;
              
              .el-tabs__nav-scroll {
                .el-tabs__nav {
                  border-bottom: none;
                  
                  .el-tabs__item {
                    padding: 0 20px;
                    height: 50px;
                    line-height: 50px;
                    border-bottom: none;
                    
                    &.is-disabled {
                      cursor: not-allowed;
                      opacity: 0.6;
                      
                      &:hover {
                        color: inherit;
                      }
                    }
                    
                  
                  }
                }
              }
              
              .el-tabs__active-bar {
                display: none;
              }
            }
          }
          
          .el-tabs__content {
            padding: 0;
            margin: 0;
            background: transparent;
            width: 100%;
            
            .el-tab-pane {
              background: transparent;
              padding: 0;
              margin: 0;
              width: 100%;
            }
          }
        }
        }
      }
    }
  }


// 骨架屏样式
.skeleton-container {
  background: transparent;
  padding: 0;
  min-height: 600px;
  
  .skeleton-content {
    width: 100%;
  }
  
  .skeleton-header {
    margin-bottom: 30px;
    padding: 20px;
    background: #FFFFFF;
    box-shadow: 0px 1px 1px #0000000D;
  }
  
  .skeleton-tabs {
    .skeleton-tab-header {
      display: flex;
      align-items: center;
      margin-bottom: 0;
      padding: 15px 20px;
      background: #FFFFFF;
      box-shadow: 0px 1px 1px #0000000D;
      border-bottom: none;
    }
    
    .skeleton-form {
      max-width: 800px;
      padding: 20px;
      background: transparent;
      
      .skeleton-form-row {
        margin-bottom: 20px;
      }
      
      .skeleton-form-row-double {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
        
        .skeleton-form-col {
          flex: 1;
        }
      }
      
      .skeleton-form-row-triple {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        
        .skeleton-form-col {
          flex: 1;
        }
      }
      
      .skeleton-form-footer {
        display: flex;
        justify-content: center;
        margin-top: 40px;
        padding-top: 20px;
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .skeleton-container {
    padding: 15px;
    
    .skeleton-tabs {
      .skeleton-tab-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
      }
      
      .skeleton-form {
        .skeleton-form-row-double,
        .skeleton-form-row-triple {
          flex-direction: column;
          gap: 15px;
        }
      }
    }
  }
}
</style> 