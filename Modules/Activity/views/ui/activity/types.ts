// Activity模块类型定义

// 票务子类型接口
export interface TicketSubtype {
  id: number
  ticket_type_id: number
  name: string
  sort: number
  price?: number
  currency?: string
  start_date?: string
  end_date?: string
}

// 票务类型接口
export interface TicketType {
  id: number
  name: string
  code: string
  description: string | null
  is_external_sale: number
  is_fee_required: number
  creator_id: number
  created_at: string
  updated_at: string
  subtypes: TicketSubtype[]
}

// 时间段接口
export interface TimeSlot {
  start_time: string
  end_time: string
  day_of_week: string | number
  day_of_month: string | number | null
}

// 中断期间接口
export interface BreakPeriod {
  start_date: string
  end_date: string
}

// 活动时间安排接口
export interface Schedule {
  repeat_type: string
  repeat_frequency?: number
  start_date: string
  end_date: string
  time_slots: TimeSlot[]
  break_periods: BreakPeriod[]
}

// 活动信息表单数据接口
export interface InfoFormData {
  title: string
  category: string
  localized_id: string
  organizer_last_name: string
  organizer_first_name: string
  organizer_email: string
  type: string
  location: string
  address: string
  online_platform: string
  online_platform_url: string
  publish_time: string
  monthly_week: number
  monthly_day: number
  schedule: Schedule
}

// 报名表单数据接口
export interface SignUpFormData {
  registration_deadline: string
  registration_limit_type: number
  registration_limit: number
  target_participants: number
  registration_type: number
  public_limit: number
  hidden_limit: number
  reserved_limit: number
  is_fee_required: number
  invoice_prefix: string
  target_income: number
  registration_method: string
  ticket_delivery_method: string
  email_template: string
  ticket: any[]
  groups: any[]
  registration_method_rules: {
    auto_promote: boolean
    notification_enabled: boolean
  }
}

// 应用规则表单数据接口
export interface ApplicationFormData {
  eventType: string
  [key: string]: any
}

// 表单数据存储接口
export interface FormDataStore {
  info: Partial<InfoFormData>
  signUp: Partial<SignUpFormData>
  application: Partial<ApplicationFormData>
}

// 群组接口
export interface Group {
  id: number
  name: string
  description?: string
  created_at: string
  updated_at: string
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应接口
export interface PaginatedResponse<T = any> {
  items: T[]
  total: number
  page?: number
  per_page?: number
} 