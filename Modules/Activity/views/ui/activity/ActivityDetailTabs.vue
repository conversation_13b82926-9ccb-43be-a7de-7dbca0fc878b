<template>
  <div class="table-page bwms-module">
    <!-- 头部区域 -->
    <div class="module-header">
      <div class="btn-list">
        <el-button @click="goBack">{{ $t('Activity.activity_detail_tabs.button.back_to_list') }}</el-button>
        <el-button v-if="activeTab && !['waiting-list', 'participants'].includes(activeTab)" class="button-no-border el-button-plus" @click="handleCreate" type="primary">
          <el-icon class="el-icon-custom">
            <img :src="$asset('Cms/Asset/PlusIcon.png')" alt="" />
          </el-icon>
          <span style="color: var(--el-color-white)">{{ getCreateButtonText() }}</span>
        </el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box" :class="{ 'transparent-bg': activeTab === 'invitations' }">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="demo-tabs">
          <el-tab-pane :label="$t('Activity.activity_detail_tabs.tabs.tickets')" name="tickets" @mouseenter="preloadTabComponent('tickets')">
            <component
              :is="getTabComponent('tickets')"
              v-if="loadedTabs.has('tickets')"
              v-loading="loadingTabs.has('tickets')"
              :activity-id="activityId"
              :pagination="pagination"
              @update-total="updateTotal"
              ref="ticketsRef"
            />
          </el-tab-pane>

          <el-tab-pane :label="$t('Activity.activity_detail_tabs.tabs.participants')" name="participants" @mouseenter="preloadTabComponent('participants')">
            <component
              :is="getTabComponent('participants')"
              v-if="loadedTabs.has('participants')"
              v-loading="loadingTabs.has('participants')"
              :activity-id="activityId"
              :pagination="pagination"
              @update-total="updateTotal"
              ref="participantsRef"
            />
          </el-tab-pane>

          <el-tab-pane :label="$t('Activity.activity_detail_tabs.tabs.waiting_list')" name="waiting-list" @mouseenter="preloadTabComponent('waiting-list')">
            <component
              :is="getTabComponent('waiting-list')"
              v-if="loadedTabs.has('waiting-list')"
              v-loading="loadingTabs.has('waiting-list')"
              :activity-id="activityId"
              :pagination="pagination"
              @update-total="updateTotal"
              ref="waitingListRef"
            />
          </el-tab-pane>

          <el-tab-pane :label="$t('Activity.activity_detail_tabs.tabs.invitations')" name="invitations" @mouseenter="preloadTabComponent('invitations')">
            <component
              :is="getTabComponent('invitations')"
              v-if="loadedTabs.has('invitations')"
              v-loading="loadingTabs.has('invitations')"
              :activity-id="activityId"
              :pagination="pagination"
              @update-total="updateTotal"
              ref="invitationsRef"
            />
          </el-tab-pane>

          <el-tab-pane :label="$t('Activity.activity_detail_tabs.tabs.reminders')" name="reminders" @mouseenter="preloadTabComponent('reminders')">
            <component
              :is="getTabComponent('reminders')"
              v-if="loadedTabs.has('reminders')"
              v-loading="loadingTabs.has('reminders')"
              :activity-id="activityId"
              :pagination="pagination"
              @update-total="updateTotal"
              ref="remindersRef"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="box-footer">
        <div class="pagination-container table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Activity.activity_detail_tabs.pagination.page_size_text') }}</span>
            <el-select v-model="pagination.pageSize" class="page-size-select" @change="handleSizeChange" size="default">
              <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size" :value="size" class="page-size-option" />
              <template #empty>
                <div style="text-align: center; padding: 8px 0">{{ $t('Activity.activity_detail_tabs.pagination.no_data') }}</div>
              </template>
            </el-select>
            <span class="total-text">{{ $t('Activity.activity_detail_tabs.pagination.total_text', { total: pagination.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.page"
              background
              layout="prev, pager, next"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineAsyncComponent, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { activityervice } from '../../services/activityervice'
import type { IEvent } from '../../types'

const { t } = useI18n()

const route = useRoute()
const router = useRouter()

// 活动ID
const activityId = ref<string>(route.params.id as string)

// 当前激活的Tab
const activeTab = ref('tickets')

// 活动信息
const activityInfo = reactive<Partial<IEvent>>({
  title: '',
  description: '',
})

// 已加载的tabs
const loadedTabs = ref(new Set<string>())

// 正在加载的tabs
const loadingTabs = ref(new Set<string>())

// 缓存的组件实例
const componentCache = new Map<string, any>()

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
})

// 获取创建按钮文本
const getCreateButtonText = () => {
  const tabLabels = {
    tickets: t('Activity.activity_detail_tabs.button.new_ticket'),
    invitations: t('Activity.activity_detail_tabs.button.new_invitation'),
    reminders: t('Activity.activity_detail_tabs.button.new_reminder'),
  }
  return tabLabels[activeTab.value as keyof typeof tabLabels] || t('Activity.activity_detail_tabs.button.new_item')
}

// 处理创建操作
const handleCreate = () => {
  const createRoutes = {
    tickets: '/activity/settings/ticket/create',
    invitations: '/activity/invitation/create',
    reminders: '/activity/reminder/create',
  }

  const routePath = createRoutes[activeTab.value as keyof typeof createRoutes]
  if (routePath) {
    router.push({
      path: routePath,
      query: {
        activityId: activityId.value,
      },
    })
  }
}

// 更新总数
const updateTotal = (total: number) => {
  pagination.total = total
}

// 改变每页条数
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.page = 1 // 重置到第一页
  refreshCurrentTab()
}

// 改变页码
const handleCurrentChange = (val: number) => {
  pagination.page = val
  refreshCurrentTab()
}

// 刷新当前标签页
const refreshCurrentTab = () => {
  const currentTabRef = getCurrentTabRef()
  if (currentTabRef && currentTabRef.refresh) {
    currentTabRef.refresh()
  }
}

// 组件引用
const ticketsRef = ref()
const participantsRef = ref()
const waitingListRef = ref()
const invitationsRef = ref()
const remindersRef = ref()

// 获取当前标签页的ref
const getCurrentTabRef = () => {
  const refMap: Record<string, any> = {
    tickets: ticketsRef.value,
    participants: participantsRef.value,
    'waiting-list': waitingListRef.value,
    invitations: invitationsRef.value,
    reminders: remindersRef.value,
  }
  return refMap[activeTab.value] || null
}

// 监听标签页切换，重置分页
watch(activeTab, () => {
  pagination.page = 1
  pagination.total = 0
})

// 懒加载组件定义
const lazyComponents = {
  tickets: defineAsyncComponent({
    loader: () => import('../ticket/TicketList.vue'),
    loadingComponent: () => null, // 使用 v-loading 指令替代
    errorComponent: () => null,
    delay: 100,
    timeout: 30000,
  }),
  participants: defineAsyncComponent({
    loader: () => import('../participant/ParticipantList.vue'),
    loadingComponent: () => null,
    errorComponent: () => null,
    delay: 100,
    timeout: 30000,
  }),
  'waiting-list': defineAsyncComponent({
    loader: () => import('../waiting-list/WaitingList.vue'),
    loadingComponent: () => null,
    errorComponent: () => null,
    delay: 100,
    timeout: 30000,
  }),
  invitations: defineAsyncComponent({
    loader: () => import('../invitation/InvitationList.vue'),
    loadingComponent: () => null,
    errorComponent: () => null,
    delay: 100,
    timeout: 30000,
  }),
  reminders: defineAsyncComponent({
    loader: () => import('../reminder/ReminderList.vue'),
    loadingComponent: () => null,
    errorComponent: () => null,
    delay: 100,
    timeout: 30000,
  }),
}

// 获取tab对应的组件
const getTabComponent = (tabName: string) => {
  return lazyComponents[tabName as keyof typeof lazyComponents]
}

// 加载tab组件
const loadTabComponent = async (tabName: string) => {
  if (loadedTabs.value.has(tabName) || loadingTabs.value.has(tabName)) {
    return
  }

  try {
    loadingTabs.value.add(tabName)

    // 获取组件（触发懒加载）
    const component = getTabComponent(tabName)
    if (component) {
      // 等待组件解析完成
      await component
      // 缓存组件
      componentCache.set(tabName, component)
      loadedTabs.value.add(tabName)
    }
  } catch (error) {
    console.error(`加载 ${tabName} 组件失败:`, error)
    ElMessage.error(t('Activity.activity_detail_tabs.message.load_component_failed', { component: tabName }))
  } finally {
    loadingTabs.value.delete(tabName)
  }
}

// 预加载tab组件（鼠标悬停时）
const preloadTabComponent = (tabName: string) => {
  // 延迟一点时间再预加载，避免快速划过时不必要的加载
  setTimeout(() => {
    loadTabComponent(tabName)
  }, 300)
}

// Tab切换处理
const handleTabClick = async (tab: any) => {
  const tabName = tab.props.name
  activeTab.value = tabName

  // 懒加载当前tab的组件
  await loadTabComponent(tabName)
}

// 返回列表
const goBack = () => {
  router.push('/activity/list')
}

// 获取活动信息
const fetchActivityInfo = async () => {
  try {
    const res = await activityervice.getDetail(Number(activityId.value))
    Object.assign(activityInfo, res.data.data)
  } catch (error) {
    console.error('获取活动信息失败:', error)
    ElMessage.error(t('Activity.activity_detail_tabs.message.get_activity_info_failed'))
  }
}

// 初始化
onMounted(async () => {
  await fetchActivityInfo()
  // 默认加载第一个tab
  await loadTabComponent(activeTab.value)
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
    
      &.transparent-bg {
        background: transparent;
        box-shadow: none;
        border: none;
        padding: 0;
        :deep(.el-tabs) {
          .el-tabs__header {
            background: #FFFFFF;
            border-radius: 10px 10px 0 0;
          
            padding: 5px 20px 0 20px;
            margin-bottom: 0;
            position: relative;
            
            // 添加底部分隔线，左右各20px间隔
            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 20px;
              right: 20px;
              z-index: 1;
            }
          }
          
          .el-tabs__content {
            padding: 0;
            margin: 0;
            background: transparent;
          }
        }
      }
    }
  }

  :deep(.el-select-dropdown__item) {
    text-align: center;
  }

  .module-header {
    .search-container {
      margin-left: 10px;
      display: flex;
      align-items: center;
    }

    .el-button-plus {
      .el-icon-custom {
        margin-right: 6px;

        img {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}
</style>