<template>
  <div class="table-page bwms-module">
    <!-- 头部区域 -->
    <div class="module-header">
      <div class="btn-list">
        <el-button class="button-no-border" @click="exportDataFn">
          <el-icon>
            <img :src="$asset('Cms/Asset/DownloadIcon.png')" alt="" />
          </el-icon>
          <span>{{ $t('Activity.event_list.button.export') }}</span>
        </el-button>
        <el-button class="button-no-border" @click="importDataFn">
          <el-icon>
            <img :src="$asset('Cms/Asset/UploadIcon.png')" alt="" />
          </el-icon>
          <span>{{ $t('Activity.event_list.button.import') }}</span>
        </el-button>
        <el-button class="button-no-border" @click="handleCreate" type="primary">
          <el-icon>
            <img :src="$asset('Cms/Asset/PlusIcon.png')" alt="" />
          </el-icon>
          <span>{{ $t('Activity.event_list.button.create') }}</span>
        </el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane :label="$t('Activity.event_list.tab.draft')" name="draft" />
          <el-tab-pane :label="$t('Activity.event_list.tab.created')" name="created" />
          <el-tab-pane :label="$t('Activity.event_list.tab.published')" name="published" />
          <el-tab-pane :label="$t('Activity.event_list.tab.cancelled')" name="cancelled" />
          <el-tab-pane :label="$t('Activity.event_list.tab.completed')" name="completed" />
        </el-tabs>
        <!-- 表格 -->
        <el-table v-loading="loading" @selection-change="selectionChange" :data="tableData" style="width: 100%">
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column type="selection" width="55" />
          <el-table-column :label="$t('Activity.event_list.table.event_name')" min-width="150">
            <template #default="{ row }">
              <el-link type="primary" @click="handleSelectAction(row)" :underline="false">
                {{ row.title }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="category_str" :label="$t('Activity.event_list.table.category')" min-width="120" />
          <el-table-column :label="$t('Activity.event_list.table.event_time')" min-width="180">
            <template #default="{ row }">
              <div>
                <div v-if="row.start_time">{{ $t('Activity.event_list.table.time_start') }}{{ formatDate(row.start_time) }}</div>
                <div v-if="row.end_time">{{ $t('Activity.event_list.table.time_end') }}{{ formatDate(row.end_time) }}</div>
                <div v-if="!row.start_time && !row.end_time">{{ $t('Activity.event_list.table.time_tbd') }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Activity.event_list.table.publish_time')" min-width="160">
            <template #default="{ row }">
              {{ formatDate(row.publish_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="ticket_delivery_method_str" :label="$t('Activity.event_list.table.ticket_method')" min-width="100" />
          <el-table-column prop="status" :label="$t('Activity.event_list.table.status')" min-width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ row.status_str }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Activity.event_list.table.actions')" width="200" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleEdit(row)">
                {{ $t('Activity.event_list.button.edit') }}
              </el-button>
              <el-button link type="primary" @click="handleCopyData(row)">
                {{ $t('Activity.event_list.button.copy') }}
              </el-button>
              <el-button type="danger" style="color: #F56C6C;" text @click="handleDelete(row)">
                {{ $t('Activity.event_list.button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="box-footer">
        <div class="pagination-container table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select v-model="pagination.pageSize" class="page-size-select" @change="handleSizeChange" size="default">
              <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size" :value="size"
                class="page-size-option" />
              <template #empty>
                <div style="text-align: center; padding: 8px 0;">
                  {{ $t('Cms.list.no_data') }}
                </div>
              </template>
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: pagination.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination v-model:current-page="pagination.page" background layout="prev, pager, next"
              :page-size="pagination.pageSize" :total="pagination.total" @current-change="handleCurrentChange" />
          </div>
        </div>
      </div>
    </div>

    <!-- 操作选择对话框 -->
    <el-dialog v-model="selectActionDialogVisible" :title="$t('Activity.event_list.dialog.select_action')" width="400px"
      style="background-color: #fff;">
      <div style="text-align: center; margin: 20px 0;">
        <p style="margin-bottom: 20px;">{{ $t('Activity.event_list.dialog.select_action_tip') }}</p>
        <div style="display: flex; gap: 16px; justify-content: center;">
          <el-button class="el-button-default" @click="goToDetail" size="large" style="min-width: 120px;">
            <el-icon style="margin-right: 8px;">
              <View />
            </el-icon>
            {{ $t('Activity.event_list.button.view_detail') }}
          </el-button>
          <el-button class="button-no-border" type="primary" @click="goToEdit" size="large" style="min-width: 120px;">
            <el-icon style="margin-right: 8px;">
              <Edit />
            </el-icon>
            {{ $t('Activity.event_list.button.edit_event') }}
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 删除确认框 -->
    <el-dialog v-model="deleteDialogVisible" :title="$t('Activity.event_list.dialog.delete_title')" width="400px"
      style="background-color: #fff;">
      <p>{{ $t('Activity.event_list.dialog.delete_confirm') }}</p>
      <template #footer>
        <div class="flex justify-center">
          <el-button class="el-button-default" @click="deleteDialogVisible = false">
            {{ $t('Activity.event_list.button.cancel') }}
          </el-button>
          <el-button class="button-no-border" type="danger" @click="confirmDelete">
            {{ $t('Activity.event_list.button.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Download, Upload, View, Edit } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { activityervice } from "../../services/activityervice"
import type { IEvent } from '../../types'

const router = useRouter()
const { t } = useI18n()
const loading = ref(false)
const deleteDialogVisible = ref(false)
const deleteRow = ref<IEvent | null>(null)

// 操作选择对话框
const selectActionDialogVisible = ref(false)
const selectedRow = ref<IEvent | null>(null)

// 选择的 tabs，默认显示已创建
const activeName = ref("created")

// tab 对应的状态映射
const tabStatusMap: Record<string, string> = {
  'draft': 'draft',
  'created': 'created',
  'published': 'published',
  'cancelled': 'cancelled',
  'completed': 'completed'
}

// 搜索表单
const searchForm = reactive({ keyword: '' })

// 表格数据
const tableData = ref<IEvent[]>([])

// 表格选择项
const tableSelectList = ref<IEvent[]>([])

// 分页信息
const pagination = reactive({ page: 1, pageSize: 10, total: 0 })

const popoverVisible = ref(false)

// 格式化日期
const formatDate = (dateStr: string | null) => {
  if (!dateStr) return '--'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 获取状态类型
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    created: 'info',
    published: 'success',
    finished: 'warning',
    cancelled: 'danger'
  }
  return map[status] || 'info'
}

// 表格选择
const selectionChange = (selection: IEvent[]) => {
  tableSelectList.value = selection
}

// 查询
const handleSearch = () => {
  pagination.page = 1
  popoverVisible.value = false
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  popoverVisible.value = false
  handleSearch()
}

// 改变tabs
const handleClick = (val: any) => {
  const { name } = val.props
  activeName.value = name
  pagination.page = 1
  fetchData()
}

// 改变每页条数
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.page = 1
  fetchData()
}

// 改变页码
const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchData()
}

// 创建活动
const handleCreate = () => router.push('/activity/list/create')

// 查看活动
const handleView = (row: IEvent) => router.push(`/activity/list/${row.id}`)

// 编辑活动
const handleEdit = (row: IEvent) => router.push(`/activity/list/${row.id}/edit`)

// 查看活动详情（带tabs）
const handleViewDetail = (row: IEvent) => router.push(`/activity/detail/${row.id}`)

// 点击活动名称时显示选择对话框
const handleSelectAction = (row: IEvent) => {
  selectedRow.value = row
  selectActionDialogVisible.value = true
}

// 跳转到详情页面
const goToDetail = () => {
  if (selectedRow.value) {
    selectActionDialogVisible.value = false
    handleViewDetail(selectedRow.value)
  }
}

// 跳转到编辑页面
const goToEdit = () => {
  if (selectedRow.value) {
    selectActionDialogVisible.value = false
    handleEdit(selectedRow.value)
  }
}

// 复制
const handleCopyData = (row: IEvent) => {
  loading.value = true
  activityervice.copy(row.id).then(res => {
    ElMessage.success(t('Activity.event_list.message.copy_success'))
    fetchData()
  }).finally(() => {
    loading.value = false
  })
}

// 删除活动
const handleDelete = (row: IEvent) => {
  deleteRow.value = row
  deleteDialogVisible.value = true
}

// 导入
const importDataFn = () => {
  // TODO: 实现导入功能
}

// 导出 
const exportDataFn = () => {
  // TODO: 实现导出功能
}

// 确认删除
const confirmDelete = async () => {
  if (!deleteRow.value) return
  try {
    await activityervice.delete(deleteRow.value.id)
    ElMessage.success(t('Activity.event_list.message.delete_success'))
    deleteDialogVisible.value = false
    fetchData()
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error(t('Activity.event_list.message.delete_failed'))
  }
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const currentStatus = tabStatusMap[activeName.value]
    
    const params = {
      page: pagination.page,
      per_page: pagination.pageSize,
      status: currentStatus,
      ...searchForm
    }
    
    console.log('活动列表请求参数:', params)
    const res = await activityervice.getList(params)
    tableData.value = res.data.data.items
    pagination.total = res.data.data.total
  } catch (error) {
    console.error('获取活动列表失败:', error)
    ElMessage.error(t('Activity.event_list.message.get_list_failed'))
  } finally {
    loading.value = false
  }
}

// 初始化
fetchData()
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
    
      overflow: scroll;
      .el-tabs{
        height: 85px;
      }
    }
  }
}

.search-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style> 