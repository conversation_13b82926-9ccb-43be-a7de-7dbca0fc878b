<template>
  <div class="bwms-module">
    <div class="module-header">
      <div class="title">{{ $t('Activity.common.waiting_list.review.title') }}</div>
      <div class="actions">
        <template v-if="detail.status === 'waiting'">
          <el-button type="success" @click="handleApprove">{{ $t('Activity.common.waiting_list.review.buttons.approve') }}</el-button>
          <el-button type="danger" @click="handleReject">{{ $t('Activity.common.waiting_list.review.buttons.reject') }}</el-button>
        </template>
        <el-button @click="handleBack">{{ $t('Activity.common.waiting_list.review.buttons.back') }}</el-button>
      </div>
    </div>

    <div class="module-con">
      <div class="box">
        <el-descriptions
          v-loading="loading"
          :title="$t('Activity.common.waiting_list.review.sections.activity_info')"
          :column="2"
          border
        >
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.activity_name')">
            {{ detail.event?.name }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.activity_time')">
            {{ detail.event?.startTime ? formatDateTime(detail.event.startTime) : '--' }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.activity_location')">
            {{ detail.event?.location || '--' }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.activity_status')">
            <el-tag>{{ detail.event?.status || '--' }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.current_participants')">
            {{ detail.event?.currentParticipants || 0 }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.max_participants')">
            {{ detail.event?.maxParticipants || '--' }}
          </el-descriptions-item>
        </el-descriptions>

        <el-divider />

        <el-descriptions
          :title="$t('Activity.common.waiting_list.review.sections.applicant_info')"
          :column="2"
          border
        >
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.name')">
            {{ detail.name }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.email')">
            {{ detail.email }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.phone')">
            {{ detail.phone || '--' }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.queue_position')">
            {{ detail.position }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.application_status')">
            <el-tag :type="getStatusType(detail.status)">
              {{ getStatusText(detail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.application_time')">
            {{ formatDateTime(detail.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.remark')" :span="2">
            {{ detail.remark || '--' }}
          </el-descriptions-item>
        </el-descriptions>

        <template v-if="detail.status !== 'waiting'">
          <el-divider />
          
          <el-descriptions
            :title="$t('Activity.common.waiting_list.review.sections.review_info')"
            :column="2"
            border
          >
            <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.reviewer')">
              {{ detail.reviewer?.name }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.review_time')">
              {{ formatDateTime(detail.reviewedAt) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Activity.common.waiting_list.review.fields.review_remark')" :span="2">
              {{ detail.reviewRemark || '--' }}
            </el-descriptions-item>
          </el-descriptions>
        </template>

        <template v-if="detail.status === 'waiting'">
          <el-divider />

          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="100px"
          >
            <el-form-item
              :label="$t('Activity.common.waiting_list.review.form.review_remark')"
              prop="reviewRemark"
            >
              <el-input
                v-model="form.reviewRemark"
                type="textarea"
                :rows="3"
                :placeholder="$t('Activity.common.waiting_list.review.form.review_remark_placeholder')"
              />
            </el-form-item>
          </el-form>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import type { IWaitingList } from '../../types'
import { waitingListService } from '../../services/eventService'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const router = useRouter()
const route = useRoute()

// 表单数据
const formRef = ref<FormInstance>()
const form = ref({
  reviewRemark: ''
})

// 表单校验规则
const rules = {
  reviewRemark: [
    { max: 500, message: t('Activity.common.waiting_list.review.validation.review_remark_max'), trigger: 'blur' }
  ]
}

// 详情数据
const loading = ref(false)
const detail = ref<IWaitingList>({} as IWaitingList)

// 获取详情
const fetchDetail = async () => {
  const id = route.params.id as string
  if (!id) return
  
  loading.value = true
  try {
    const { data } = await waitingListService.getDetail(Number(id))
    detail.value = data
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error(t('Activity.common.waiting_list.review.messages.get_detail_failed'))
  } finally {
    loading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (datetime: string) => {
  return new Date(datetime).toLocaleString()
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'waiting':
      return 'warning'
    case 'approved':
      return 'success'
    case 'rejected':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'waiting':
      return t('Activity.common.waiting_list.review.status.waiting')
    case 'approved':
      return t('Activity.common.waiting_list.review.status.approved')
    case 'rejected':
      return t('Activity.common.waiting_list.review.status.rejected')
    default:
      return '--'
  }
}

// 处理通过
const handleApprove = () => {
  formRef.value?.validate((valid) => {
    if (!valid) return

    ElMessageBox.confirm(
      t('Activity.common.waiting_list.review.dialogs.approve_confirm_message'),
      t('Activity.common.waiting_list.review.dialogs.approve_confirm_title'),
      {
        type: 'warning'
      }
    ).then(async () => {
      try {
        await waitingListService.review(
          detail.value.eventId,
          detail.value.id,
          'approved',
          form.value.reviewRemark
        )
        ElMessage.success(t('Activity.common.waiting_list.review.messages.operation_success'))
        fetchDetail()
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error(t('Activity.common.waiting_list.review.messages.operation_failed'))
      }
    })
  })
}

// 处理拒绝
const handleReject = () => {
  formRef.value?.validate((valid) => {
    if (!valid) return

    ElMessageBox.confirm(
      t('Activity.common.waiting_list.review.dialogs.reject_confirm_message'),
      t('Activity.common.waiting_list.review.dialogs.reject_confirm_title'),
      {
        type: 'warning'
      }
    ).then(async () => {
      try {
        await waitingListService.review(
          detail.value.eventId,
          detail.value.id,
          'rejected',
          form.value.reviewRemark
        )
        ElMessage.success(t('Activity.common.waiting_list.review.messages.operation_success'))
        fetchDetail()
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error(t('Activity.common.waiting_list.review.messages.operation_failed'))
      }
    })
  })
}

// 处理返回
const handleBack = () => {
  router.back()
}

onMounted(() => {
  fetchDetail()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;

      .el-descriptions {
        margin-bottom: 20px;
      }
    }
  }
}
</style> 