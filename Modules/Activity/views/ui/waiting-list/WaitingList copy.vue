<template>
  <div class="waiting-list-page">
    <!-- 数据卡片展示 -->
    <div class="data-cards">
      <div class="card">
        <div class="card-title">報名總數/限製人數</div>
        <div class="card-value">100/100</div>
      </div>
      <div class="card">
        <div class="card-title">等待名單人數</div>
        <div class="card-value">10</div>
      </div>
      <div class="card">
        <div class="card-value">45</div>
        <div class="card-title">等待名單轉名成功人數</div>
      </div>
    </div>

    <!-- 等待名单选项卡 -->
    <div class="tabs-container">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="待審批" name="pending"></el-tab-pane>
        <el-tab-pane label="已通過" name="approved"></el-tab-pane>
        <el-tab-pane label="已駁回/取消" name="rejected"></el-tab-pane>
      </el-tabs>

      <!-- 搜索框 -->
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索"
          prefix-icon="Search"
        >
        </el-input>
      </div>
    </div>

    <!-- 表格数据 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column label="姓名" min-width="80">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="聯繫方式" min-width="120">
          <template #default="scope">
            {{ scope.row.email }}
          </template>
        </el-table-column>
        <el-table-column label="申請時間" min-width="120">
          <template #default="scope">
            {{ scope.row.date }}
          </template>
        </el-table-column>
        <el-table-column label="排序" min-width="60">
          <template #default="scope">
            #{{ scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click="handleApprove(scope.row)"
            >
              加入waitlist
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-button :disabled="currentPage <= 1" @click="prevPage">
          上一頁
        </el-button>
        <span class="page-info">{{ currentPage }}/{{ totalPages }}</span>
        <el-button :disabled="currentPage >= totalPages" @click="nextPage">
          下一頁
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 活动标签页
const activeTab = ref('pending')

// 搜索关键词
const searchKeyword = ref('')

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: 'A',
    email: '<EMAIL>',
    date: '2025-01-01 01:01'
  },
  {
    id: 2,
    name: 'B',
    email: '<EMAIL>',
    date: '2025-01-01 01:02'
  },
  {
    id: 3,
    name: 'C',
    email: '<EMAIL>',
    date: '2025-01-01 01:03'
  },
  {
    id: 4,
    name: 'D',
    email: '<EMAIL>',
    date: '2025-01-01 01:04'
  },
  {
    id: 5,
    name: 'E',
    email: '<EMAIL>',
    date: '2025-01-01 01:05'
  }
])

// 加载状态
const loading = ref(false)

// 分页信息
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(5)
const totalPages = computed(() => Math.ceil(total.value / pageSize.value))

// 上一页
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    fetchData()
  }
}

// 下一页
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    fetchData()
  }
}

// 获取数据
const fetchData = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 处理加入等待名单
const handleApprove = (row) => {
  ElMessage.success(`已将 ${row.name} 加入等待名单`)
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.waiting-list-page {
  padding: 20px;
}

.breadcrumb {
  font-size: 14px;
  color: #606266;
  margin-bottom: 20px;
}

.data-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.card {
  flex: 1;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;
  text-align: center;
}

.card-title {
  font-size: 14px;
  color: #606266;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.tabs-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-box {
  width: 200px;
}

.table-container {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.page-info {
  margin: 0 15px;
}
</style>
