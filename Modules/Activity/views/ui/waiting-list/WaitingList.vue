<template>
  <div class="waiting-list-container">
    <!-- 数据卡片展示 -->
    <div class="data-cards">
      <div class="card">
        <div class="card-icon blue-bg">
          <el-icon><User /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">{{ $t('Activity.waiting_list.cards.total_registration') }}</div>
          <div class="card-value">{{ statsData.total }}/{{ statsData.limit }}</div>
        </div>
      </div>
      <div class="card">
        <div class="card-icon green-bg">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">{{ $t('Activity.waiting_list.cards.waiting_count') }}</div>
          <div class="card-value">{{ statsData.waiting }}</div>
        </div>
      </div>
      <div class="card">
        <div class="card-icon purple-bg">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">{{ $t('Activity.waiting_list.cards.waiting_to_success') }}</div>
          <div class="card-value">{{ statsData.waiting_to_success }}</div>
        </div>
      </div>
    </div>

    <!-- 等待名单选项卡 -->
    <div class="tabs-container">
      <div class="custom-tabs">
        <div class="tab-item" :class="{ 'active': activeTab === 'pending' }" @click="handleTabChange('pending')">
          {{ $t('Activity.waiting_list.tabs.pending') }}
        </div>
        <div class="tab-item" :class="{ 'active': activeTab === 'approved' }" @click="handleTabChange('approved')">
          {{ $t('Activity.waiting_list.tabs.approved') }}
        </div>
        <div class="tab-item" :class="{ 'active': activeTab === 'cancelled' }" @click="handleTabChange('cancelled')">
          {{ $t('Activity.waiting_list.tabs.cancelled') }}
        </div>
      </div>
      
      <!-- 搜索框 -->
      <div class="search-container">
        <el-input 
          v-model="searchKeyword" 
          :placeholder="$t('Activity.waiting_list.search.placeholder')" 
          prefix-icon="Search" 
          @input="handleSearch" 
          clearable 
          class="custom-search-input"
        />
      </div>
    </div>

    <!-- 表格数据 -->
    <el-table :data="waitingList" style="width: 100%;" v-loading="loading">
      <template #empty>
        <el-empty :description="$t('Activity.waiting_list.empty.description')" image-size="100px" />
      </template>
      <el-table-column prop="user.name" :label="$t('Activity.waiting_list.table.name')" min-width="120"
        show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.user?.name || scope.row.user?.email || $t('Activity.waiting_list.placeholders.not_filled') }}
        </template>
      </el-table-column>
      <el-table-column prop="user.email" :label="$t('Activity.waiting_list.table.contact')" min-width="200"
        show-overflow-tooltip>
        <template #default="scope">
          <div>
            <div v-if="scope.row.user?.email">{{ scope.row.user.email }}</div>
            <div v-if="scope.row.user?.phone">{{ scope.row.user.phone }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="registration_time_formatted" :label="$t('Activity.waiting_list.table.registration_time')"
        width="300">
        <template #default="scope">
          {{ scope.row.registration_time_formatted }}
        </template>
      </el-table-column>
      <el-table-column prop="priority" :label="$t('Activity.waiting_list.table.queue_order')" width="180">
        <template #default="scope">
          #{{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="created_at" :label="$t('Activity.waiting_list.table.created_date')" width="300">
        <template #default="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" :label="$t('Activity.waiting_list.table.actions')" width="150">
        <template #default="scope">
          <div>
            <el-button class="bwms-operate-btn" @click="handleViewDetail(scope.row)" type="primary" size="small">
              {{ $t('Activity.waiting_list.table.view') }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 查看详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" :title="$t('Activity.waiting_list.detail_dialog.title')"
      class="el-dialog-common-cls" width="600px">
      <div class="detail-content" v-loading="detailLoading">
        <div v-if="participantDetail" class="detail-info">
          <!-- 基本信息 -->
          <div class="info-section">
            <h4 class="section-title">{{ $t('Activity.waiting_list.detail_sections.basic_info') }}</h4>
            <div class="info-row">
              <span class="label">{{ $t('Activity.waiting_list.detail_fields.name') }}：</span>
              <span class="value">{{ participantDetail.user?.name || $t('Activity.waiting_list.placeholders.not_filled')
                }}</span>
            </div>
            <div class="info-row">
              <span class="label">{{ $t('Activity.waiting_list.detail_fields.email') }}：</span>
              <span class="value">{{ participantDetail.user?.email ||
                $t('Activity.waiting_list.placeholders.not_filled')
                }}</span>
            </div>
            <div class="info-row">
              <span class="label">{{ $t('Activity.waiting_list.detail_fields.phone') }}：</span>
              <span class="value">{{ participantDetail.user?.phone ||
                $t('Activity.waiting_list.placeholders.not_filled')
                }}</span>
            </div>
            <div class="info-row">
              <span class="label">{{ $t('Activity.waiting_list.detail_fields.gender') }}：</span>
              <span class="value">{{ participantDetail.user?.gender ||
                $t('Activity.waiting_list.placeholders.not_filled')
                }}</span>
            </div>
          </div>

          <!-- 活动信息 -->
          <div class="info-section">
            <h4 class="section-title">{{ $t('Activity.waiting_list.detail_sections.activity_info') }}</h4>
            <div class="info-row">
              <span class="label">{{ $t('Activity.waiting_list.detail_fields.registration_time') }}：</span>
              <span class="value">{{ participantDetail.registration_time_formatted ||
                $t('Activity.waiting_list.placeholders.unknown') }}</span>
            </div>
            <div class="info-row">
              <span class="label">{{ $t('Activity.waiting_list.detail_fields.status') }}：</span>
              <span class="value">
                <el-tag :type="getStatusType(participantDetail.status)" size="small">
                  {{ getStatusText(participantDetail.status) }}
                </el-tag>
              </span>
            </div>
            <div class="info-row">
              <span class="label">{{ $t('Activity.waiting_list.detail_fields.check_in_time') }}：</span>
              <span class="value">{{ participantDetail.check_in_time_formatted ||
                $t('Activity.waiting_list.placeholders.not_check_in') }}</span>
            </div>
            <div class="info-row" v-if="participantDetail.feedback">
              <span class="label">{{ $t('Activity.waiting_list.detail_fields.feedback') }}：</span>
              <span class="value">{{ participantDetail.feedback }}</span>
            </div>
          </div>

          <!-- 时间信息 -->
          <div class="info-section">
            <h4 class="section-title">{{ $t('Activity.waiting_list.detail_sections.time_info') }}</h4>
            <div class="info-row">
              <span class="label">{{ $t('Activity.waiting_list.detail_fields.created_time') }}：</span>
              <span class="value">{{ formatDate(participantDetail.created_at) }}</span>
            </div>
            <div class="info-row">
              <span class="label">{{ $t('Activity.waiting_list.detail_fields.updated_time') }}：</span>
              <span class="value">{{ formatDate(participantDetail.updated_at) }}</span>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-center">
          <el-button class="el-button-default" @click="detailDialogVisible = false">
            {{ $t('Activity.waiting_list.detail_dialog.close') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { participantService } from "../../services/participantService"
import { User, Clock, CircleCheck } from '@element-plus/icons-vue'

const { t } = useI18n()

// Props - 接收活动ID和分页信息
interface Props {
  activityId?: string | number
  pagination?: {
    page: number
    pageSize: number
    total: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  activityId: '',
  pagination: () => ({ page: 1, pageSize: 10, total: 0 })
})

// Emits - 发送事件
const emit = defineEmits<{
  'update-total': [total: number]
}>()

// 等待名单项接口
interface WaitingListItem {
  id: number
  activity_id: number
  user_id: number
  registration_time: number
  registration_time_formatted: string
  status: string
  check_in_time: number | null
  check_in_time_formatted: string | null
  feedback: string | null
  creator_id: number
  created_at: string
  updated_at: string
  deleted_at: number
  user: {
    id: number
    name: string
    email: string
    phone: string
    gender: string
    photo: string
  }
}

// 统计数据
interface StatsData {
  total?: number
  limit?: number
  waiting?: number
  waiting_to_success?: number
  status_group?: any[]
}


const router = useRouter()
const route = useRoute()

// 当前激活的选项卡
const activeTab = ref('pending')

// 搜索关键词
const searchKeyword = ref('')

// 表格数据
const waitingList = ref<WaitingListItem[]>([])
const loading = ref(false)

// 统计数据
const statsData = ref<StatsData>({
  total: 0,
  limit: 0,
  waiting: 0,
  waiting_to_success: 0,
  status_group: []
})

// 查看详情相关
const detailDialogVisible = ref(false)
const detailLoading = ref(false)
const participantDetail = ref<WaitingListItem | null>(null)

// 获取状态类型
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    'pending': 'warning',
    'approved': 'success',
    'cancelled': 'danger'
  }
  return map[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    'pending': t('Activity.waiting_list.status.pending'),
    'approved': t('Activity.waiting_list.status.approved'),
    'cancelled': t('Activity.waiting_list.status.cancelled')
  }
  return map[status] || t('Activity.waiting_list.status.unknown')
}

// 处理选项卡切换
const handleTabChange = (tabName?: string) => {
  if (tabName) {
    activeTab.value = tabName
  }
  searchKeyword.value = ''
  getTableDataList()
}

// 处理搜索
const handleSearch = () => {
  // 防抖处理
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  searchTimeout.value = window.setTimeout(() => {
    getTableDataList()
  }, 500)
}

let searchTimeout = ref<number>()

// 查看详情
const handleViewDetail = async (row: WaitingListItem) => {
  detailDialogVisible.value = true
  detailLoading.value = true

  try {
    const res = await participantService.getDetail(row.id)
    participantDetail.value = res.data.data
  } catch (error) {
    ElMessage.error(t('Activity.waiting_list.messages.get_detail_failed'))
  } finally {
    detailLoading.value = false
  }
}

// 获取统计数据
const getStatsData = async () => {
  try {
    const activityId = (props.activityId || route.params.id) as string | number
    const res = await participantService.getStats(activityId)
    // 更新统计数据
    statsData.value = res.data.data
  } catch (error) {
    ElMessage.error(t('Activity.waiting_list.messages.get_stats_failed'))
  }
}

// 获取通知状态类型
const getNotificationType = (tabName: string) => {
  const map: Record<string, string> = {
    'pending': 'pending',
    'approved': 'notified',
    'cancelled': 'expired'
  }
  return map[tabName] || ''
}

// 查询列表
const getTableDataList = async () => {
  loading.value = true
  try {
    const params = {
      activity_id: (props.activityId || route.params.id) as string | number,
      page: props.pagination?.page || 1,
      per_page: props.pagination?.pageSize || 10,
      status: activeTab.value === 'pending' ? 'waiting' : activeTab.value,
      notification_type: getNotificationType(activeTab.value),
      search: searchKeyword.value
    }
    const res = await participantService.getList(params)
    // 正确获取数据 - 接口返回的数据在 data.items 中
    waitingList.value = res.data.data.items || []

    // 更新总数到父组件
    const total = res.data.data.total || 0
    emit('update-total', total)
  } catch (error) {
    ElMessage.error(t('Activity.waiting_list.messages.get_list_failed'))
  } finally {
    loading.value = false
  }
}

// 暴露刷新方法给父组件
const refresh = () => {
  getTableDataList()
  getStatsData()
}

// 外部搜索处理（来自父组件）
const handleExternalSearch = (keyword: string) => {
  searchKeyword.value = keyword
  getTableDataList()
}

// 暴露方法
defineExpose({
  refresh,
  handleSearch: handleExternalSearch
})

// 监听分页变化
watch(
  () => props.pagination,
  (newPagination, oldPagination) => {
    if (newPagination && oldPagination) {
      if (
        newPagination.page !== oldPagination.page ||
        newPagination.pageSize !== oldPagination.pageSize
      ) {
        getTableDataList()
      }
    }
  },
  { deep: true }
)

// 日期转换函数 - 转换为"yyyy-mm-dd"格式
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '-'

  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

onMounted(() => {
  getTableDataList()
  getStatsData()
})
</script>

<style lang="scss" scoped>
.waiting-list-container {
  width: 100%;

  // 数据卡片样式
  .data-cards {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
  }

  .card {
    flex: 1;
    border: 1px solid #EBEEF5;
    border-radius: 8px;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 16px;

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 1;
      flex-shrink: 0;

      &.blue-bg {
        background: #DBEAFE;
        
        .el-icon {
          font-size: 28px;
          color: #3B82F6;
        }
      }
      
      &.green-bg {
        background: #DCFCE7;
        
        .el-icon {
          font-size: 28px;
          color: #10B981;
        }
      }
      
      &.purple-bg {
        background: #F3E8FF;
        
        .el-icon {
          font-size: 28px;
          color: #8B5CF6;
        }
      }
    }

    .card-content {
      flex: 1;
      text-align: left;
      
      .card-title {
        font: normal normal normal 16px/21px "Microsoft JhengHei", sans-serif;
        letter-spacing: 0px;
        color: #000000;
        opacity: 1;
        margin-bottom: 4px;
      }

      .card-value {
        font: normal normal bold 30px/36px "Inter", sans-serif;
        letter-spacing: 0px;
        color: #303030;
        opacity: 1;
      }
    }
  }

  // 选项卡容器
  .tabs-container {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    position: relative;
    
    // 添加底部边框，延伸整个容器宽度
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #e4e7ed;
      z-index: 1;
    }
  }

  // 自定义选项卡
  .custom-tabs {
    display: flex;
    position: relative;
    z-index: 2;

    .tab-item {
      padding: 12px 20px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #606266;
      border-bottom: 2px solid transparent;
      transition: all 0.3s;
      position: relative;

      &:hover {
        color: #409eff;
      }

      &.active {
        color: #409eff;
        border-bottom-color: #409eff;
      }
    }
  }

  // 搜索容器
  .search-container {
    margin-left: 20px;
    margin-bottom: 12px;
    position: relative;
    z-index: 2;
    
    .custom-search-input {
      width: 286px;
      
      :deep(.el-input__wrapper) {
        width: 286px;
        height: 40px;
        background: #FFFFFF;
        border: 1px solid #DFDFDF;
        border-radius: 4px;
        opacity: 1;
        box-shadow: none;
        
        &:hover {
          border-color: #DFDFDF;
        }
        
        &.is-focus {
          border-color: #409eff;
        }
      }
      
      :deep(.el-input__inner) {
        height: 38px;
        line-height: 38px;
      }
    }
  }
}

// 弹窗样式
.detail-content {
  padding: 0;

  .detail-info {
    .info-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #EBEEF5;
      }

      .info-row {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          min-width: 80px;
          font-weight: 500;
          color: #606266;
          margin-right: 12px;
        }

        .value {
          flex: 1;
          color: #303133;
          word-break: break-all;
        }
      }
    }
  }
}

.flex {
  display: flex;
  gap: 12px;
}

.justify-center {
  justify-content: center;
}
</style>
