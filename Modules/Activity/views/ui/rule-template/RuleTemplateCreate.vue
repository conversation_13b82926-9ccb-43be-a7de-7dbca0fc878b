<template>
    <div class="bwms-module">
      <!-- 页面头部 -->
      <div class="module-header">
        
        <div >
          <el-button @click="handleCancel">{{ $t('Activity.rule_template_create.button.cancel') }}</el-button>
          <el-button type="primary" :loading="loading" @click="handleSave">
            {{ isEdit ? $t('Activity.rule_template_create.button.update') : $t('Activity.rule_template_create.button.save') }}
          </el-button>
        </div>
      </div>
  
      <!-- 主体内容 -->
      <div class="module-con">
        <div class="box">
          <el-tabs v-model="activeTab" class="create-tabs">
            <el-tab-pane :label="$t('Activity.rule_template_create.tabs.rule_info')" name="info">
              <RuleInfoForm
                ref="infoFormRef"
                v-model:form="formData"
                @validate="validateTab"
              />
            </el-tab-pane>
            <el-tab-pane :label="$t('Activity.rule_template_create.tabs.basic_config')" name="basic">
              <BasicConfigForm
                ref="basicFormRef"
                v-model:form="formData"
                @validate="validateTab"
              />
            </el-tab-pane>
          </el-tabs>
  
          <!-- 底部按钮 -->
          <div class="form-footer">
            <el-button v-if="showPrevButton" @click="prevStep">{{ $t('Activity.rule_template_create.button.prev_step') }}</el-button>
            <el-button
              v-if="showNextButton"
              type="primary"
              @click="nextStep"
            >
              {{ $t('Activity.rule_template_create.button.next_step') }}
            </el-button>
            <el-button
              v-else
              type="primary"
              :loading="loading"
              @click="handleSubmit"
            >
              {{ isEdit ? $t('Activity.rule_template_create.button.update') : $t('Activity.rule_template_create.button.submit') }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { useI18n } from 'vue-i18n'
  import RuleInfoForm from './components/RuleInfoForm.vue'
  import BasicConfigForm from './components/BasicConfigForm.vue'
  import { ruleTemplateService } from '../../services/ruleTemplateService'
  
  // 定义规则模板数据类型
  interface RuleTemplateForm {
    name: string
    description: string
    scene: 'onsite' | 'online'
    status: number
    config: {
      is_multilingual: boolean
      languages: string[]
      mail_event_types: string[]
      mail_templates: Record<string, any>
      result_forms: number[]
      searchable: boolean
      member_limit_type: string
    }
  }
  
  const router = useRouter()
  const route = useRoute()
  const { t } = useI18n()
  const loading = ref(false)
  const activeTab = ref('info')
  
  // 编辑模式相关
  const isEdit = computed(() => route.query.mode === 'edit')
  const editId = computed(() => route.query.id ? Number(route.query.id) : null)
  
  const tabs = ['info', 'basic']
  
  // 表单引用
  const infoFormRef = ref()
  const basicFormRef = ref()
  
  // 表单数据 - 匹配API接口结构
  const formData = reactive<RuleTemplateForm>({
    name: '',
    description: '',
    scene: 'onsite',
    status: 1,
    config: {
      is_multilingual: false,
      languages: ['zh_CN'],
      mail_event_types: [],
      mail_templates: {},
      result_forms: [],
      searchable: true,
      member_limit_type: 'none'
    }
  })
  
  // 计算属性
  const currentTabIndex = computed(() => tabs.indexOf(activeTab.value))
  const showPrevButton = computed(() => currentTabIndex.value > 0)
  const showNextButton = computed(() => currentTabIndex.value < tabs.length - 1)
  
  // 表单验证
  const validateTab = async () => {
    const formRefs: Record<string, any> = {
      info: infoFormRef,
      basic: basicFormRef
    }
    
    await formRefs[activeTab.value].value?.validate()
  }
  
  // 取消
  const handleCancel = () => {
    ElMessageBox.confirm(
      t('Activity.rule_template_create.dialog.cancel_confirm_message'),
      t('Activity.rule_template_create.dialog.cancel_confirm_title'),
      {
        confirmButtonText: t('Activity.rule_template_create.dialog.confirm'),
        cancelButtonText: t('Activity.rule_template_create.dialog.cancel'),
        type: 'warning'
      }
    ).then(() => {
      router.back()
    })
  }
  
  // 保存
  const handleSave = async () => {
    try {
      await validateTab()
      loading.value = true

      if (isEdit.value && editId.value) {
        await ruleTemplateService.update(editId.value, formData)
        ElMessage.success(t('Activity.rule_template_create.message.update_success'))
      } else {
        await ruleTemplateService.create(formData)
        ElMessage.success(t('Activity.rule_template_create.message.save_success'))
      }
      router.push({ name: 'RuleTemplateList' })
    } catch (error) {
      console.error(isEdit.value ? '更新失败:' : '保存失败:', error)
      ElMessage.error(isEdit.value ? t('Activity.rule_template_create.message.update_failed') : t('Activity.rule_template_create.message.save_failed'))
    } finally {
      loading.value = false
    }
  }
  
  // 下一步
  const nextStep = async () => {
    await validateTab()
    const nextIndex = currentTabIndex.value + 1
    if (nextIndex < tabs.length) {
      activeTab.value = tabs[nextIndex]
    }
  }
  
  // 上一步
  const prevStep = () => {
    const prevIndex = currentTabIndex.value - 1
    if (prevIndex >= 0) {
      activeTab.value = tabs[prevIndex]
    }
  }
  
  // 提交
  const handleSubmit = async () => {
    try {
      await validateTab()
      loading.value = true

      if (isEdit.value && editId.value) {
        await ruleTemplateService.update(editId.value, formData)
        ElMessage.success(t('Activity.rule_template_create.message.update_success'))
      } else {
        await ruleTemplateService.create(formData)
        ElMessage.success(t('Activity.rule_template_create.message.create_success'))
      }
      router.push({ name: 'RuleTemplateList' })
    } catch (error) {
      console.error(isEdit.value ? '更新失败:' : '创建失败:', error)
      ElMessage.error(isEdit.value ? t('Activity.rule_template_create.message.update_failed') : t('Activity.rule_template_create.message.create_failed'))
    } finally {
      loading.value = false
    }
  }

  // 加载编辑数据
  const loadEditData = async () => {
    if (!isEdit.value || !editId.value) return
    
    try {
      loading.value = true
      const response = await ruleTemplateService.getDetail(editId.value)
      if (response?.data?.code === 200) {
        const data = response.data.data
        // 回填表单数据
        Object.assign(formData, {
          name: data.name,
          description: data.description,
          scene: data.scene,
          status: data.status,
          config: {
            is_multilingual: data.config?.is_multilingual || false,
            languages: data.config?.languages || ['zh_CN'],
            mail_event_types: data.config?.mail_event_types || [],
            mail_templates: data.config?.mail_templates || {},
            result_forms: data.config?.result_forms || [],
            searchable: data.config?.searchable !== false,
            member_limit_type: data.config?.member_limit_type || 'none'
          }
        })
      }
    } catch (error) {
      console.error('加载编辑数据失败:', error)
      ElMessage.error(t('Activity.rule_template_create.message.load_data_failed'))
    } finally {
      loading.value = false
    }
  }

  // 页面初始化
  onMounted(() => {
    console.log('编辑模式:', isEdit.value, '编辑ID:', editId.value, 'URL参数:', route.query)
    if (isEdit.value) {
      loadEditData()
    }
  })
  </script>
  
  <style lang="scss" scoped>
  .bwms-module {
   
  
    .module-con {
      .box {
        padding: 20px;
        background: #fff;
        border-radius: 4px;
  
        .create-tabs {
          margin-bottom: 20px;
        }
  
        .form-footer {
          margin-top: 40px;
          text-align: center;
          
          .el-button + .el-button {
            margin-left: 12px;
          }
        }
      }
    }
  }
  </style> 