<template>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="130px"
      class="basic-config-form"
    >
      <!-- 语言设置 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-chat-line-round"></i>
          {{ $t('Activity.basic_config_form.sections.language_settings') }}
        </h3>
        <el-form-item :label="$t('Activity.basic_config_form.fields.is_multilingual')" prop="isMultiLanguage">
          <el-radio-group v-model="form.config.is_multilingual">
            <el-radio :label="true">{{ $t('Activity.basic_config_form.options.yes') }}</el-radio>
            <el-radio :label="false">{{ $t('Activity.basic_config_form.options.no') }}</el-radio>
          </el-radio-group>
        </el-form-item>
  
        <el-form-item :label="$t('Activity.basic_config_form.fields.select_languages')" prop="languages" v-if="form.config.is_multilingual">
          <el-checkbox-group v-model="form.config.languages">
            <el-checkbox label="zh_CN">{{ $t('Activity.basic_config_form.options.languages.zh_CN') }}</el-checkbox>
            <el-checkbox label="zh_HK">{{ $t('Activity.basic_config_form.options.languages.zh_HK') }}</el-checkbox>
            <el-checkbox label="en">{{ $t('Activity.basic_config_form.options.languages.en') }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>
  
      <!-- 邮件设置 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-message"></i>
          {{ $t('Activity.basic_config_form.sections.email_settings') }}
        </h3>
                <el-form-item :label="$t('Activity.basic_config_form.fields.select_email_template')">
          <div class="template-list">
            <div class="template-item" v-for="template in emailTemplates" :key="template.id">
              <el-checkbox 
                :model-value="selectedTemplateIds.includes(template.id)"
                @change="(checked) => handleTemplateSelect(template.id, checked)"
              >
                {{ template.name || template.title }}
              </el-checkbox>
              <el-link type="primary" @click="viewTemplate(template.id)">{{ $t('Activity.basic_config_form.descriptions.preview') }}</el-link>
            </div>
          </div>
          <div v-if="emailTemplates.length === 0" class="template-loading">
            <el-text type="info">{{ $t('Activity.basic_config_form.descriptions.template_loading') }}</el-text>
          </div>
        </el-form-item>
  
        <el-form-item :label="$t('Activity.basic_config_form.fields.select_feedback_form')">
          <el-select 
            v-model="form.config.result_forms" 
            multiple
            :placeholder="$t('Activity.basic_config_form.placeholders.select_feedback_form')" 
            style="width: 100%"
            clearable
          >
            <el-option 
              v-for="formOption in feedbackFormOptions" 
              :key="formOption.id"
              :label="formOption.label" 
              :value="formOption.id"
            >
              <div class="feedback-option">
                <div class="option-header">
                  <span class="option-name">{{ formOption.label }}</span>
                  <el-tag size="small" type="info">{{ formOption.questions }}{{ $t('Activity.basic_config_form.units.questions') }}</el-tag>
                </div>
                <div class="option-details">
                  <span class="option-desc">{{ formOption.description }}</span>
                  <span class="option-time">{{ $t('Activity.basic_config_form.units.estimated_time') }} {{ formOption.estimatedTime }}</span>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </div>
  
      <!-- 活动报名设置 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-user"></i>
          {{ $t('Activity.basic_config_form.sections.registration_settings') }}
        </h3>
        <el-form-item :label="$t('Activity.basic_config_form.fields.specific_email_domain')" prop="specificEmailDomain">
          <el-radio-group v-model="form.config.member_limit_type">
            <el-radio label="email_domain">{{ $t('Activity.basic_config_form.options.member_limit_type.email_domain') }}</el-radio>
            <el-radio label="none">{{ $t('Activity.basic_config_form.options.member_limit_type.none') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
  
      <!-- 安全与隐私设置 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-lock"></i>
          {{ $t('Activity.basic_config_form.sections.security_privacy') }}
        </h3>
        <el-form-item :label="$t('Activity.basic_config_form.fields.search_engine_settings')" prop="searchEngineSettings">
          <div class="setting-description">{{ $t('Activity.basic_config_form.descriptions.search_engine_description') }}</div>
          <el-radio-group v-model="form.config.searchable">
            <el-radio :label="true">{{ $t('Activity.basic_config_form.options.searchable.true') }}</el-radio>
            <el-radio :label="false">{{ $t('Activity.basic_config_form.options.searchable.false') }}</el-radio>
          </el-radio-group>
        </el-form-item>
  
        <el-form-item :label="$t('Activity.basic_config_form.fields.page_permission')" prop="pagePermission">
          <div class="setting-description">{{ $t('Activity.basic_config_form.descriptions.page_permission_description') }}</div>
          <el-radio-group v-model="form.config.page_permission">
            <el-radio label="hidden">{{ $t('Activity.basic_config_form.options.page_permission.hidden') }}</el-radio>
            <el-radio label="needCode">{{ $t('Activity.basic_config_form.options.page_permission.needCode') }}</el-radio>
            <el-radio label="public">{{ $t('Activity.basic_config_form.options.page_permission.public') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
    </el-form>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, onMounted, watch, onUnmounted, computed } from 'vue'
  import type { FormInstance } from 'element-plus'
  import { ElMessage } from 'element-plus'
  import { useRouter } from 'vue-router'
  import { useI18n } from 'vue-i18n'
  import { ruleTemplateService } from '../../../services/ruleTemplateService'
  
  const { t } = useI18n()
  
  const props = defineProps<{
    form: Record<string, any>
  }>()
  
  const emit = defineEmits<{
    (e: 'update:form', value: Record<string, any>): void
    (e: 'validate'): Promise<void>
  }>()
  
  const formRef = ref<FormInstance>()
  
  // 缓存键名
  const CACHE_KEY = 'activity_basic_config_form_cache'
  
  // 邮件模版数据 - 从接口获取
  const emailTemplates = reactive<any[]>([])
  
  // 选中的模板IDs
  const selectedTemplateIds = ref<number[]>([])

  // 防抖计时器
  let saveTimer: ReturnType<typeof setTimeout> | null = null

  // 保存表单数据到缓存（带防抖）
  const saveFormToCache = () => {
    if (saveTimer) {
      clearTimeout(saveTimer)
    }
    
    saveTimer = setTimeout(() => {
      try {
        const cacheData = {
          form: JSON.parse(JSON.stringify(props.form)),
          selectedTemplateIds: selectedTemplateIds.value,
          timestamp: Date.now()
        }
        sessionStorage.setItem(CACHE_KEY, JSON.stringify(cacheData))
        console.log('表单数据已缓存')
      } catch (error) {
        console.warn('保存表单缓存失败:', error)
      }
    }, 500) // 500ms 防抖
  }

  // 从缓存恢复表单数据
  const restoreFormFromCache = () => {
    try {
      const cached = sessionStorage.getItem(CACHE_KEY)
      if (cached) {
        const cacheData = JSON.parse(cached)
        // 检查缓存是否过期（1小时）
        const isExpired = Date.now() - cacheData.timestamp > 60 * 60 * 1000
        
        if (!isExpired && cacheData.form) {
          // 恢复表单数据
          Object.assign(props.form, cacheData.form)
          selectedTemplateIds.value = cacheData.selectedTemplateIds || []
          
          // 通知父组件更新
          emit('update:form', props.form)

        } else if (isExpired) {
          // 清除过期缓存
          sessionStorage.removeItem(CACHE_KEY)
        }
      }
    } catch (error) {
      console.warn('恢复表单缓存失败:', error)
      sessionStorage.removeItem(CACHE_KEY)
    }
  }

  // 清除缓存
  const clearFormCache = () => {
    sessionStorage.removeItem(CACHE_KEY)
  }

  // 获取邮件模板列表
  const getEmailTemplates = async () => {
    try {
      const response = await ruleTemplateService.getEmailTemplates()
      // 根据实际接口返回结构处理数据
      if (response.data && response.data.data && response.data.data.list) {
        emailTemplates.splice(0, emailTemplates.length, ...response.data.data.list)
      }
    } catch (error) {
      console.error('获取邮件模板失败:', error)
      ElMessage.error(t('Activity.basic_config_form.messages.get_email_templates_failed'))
    }
  }

  // 处理模板选择
  const handleTemplateSelect = (templateId: number, checked: boolean) => {
    if (checked) {
      selectedTemplateIds.value.push(templateId)
    } else {
      const index = selectedTemplateIds.value.indexOf(templateId)
      if (index > -1) {
        selectedTemplateIds.value.splice(index, 1)
      }
    }
    
    // 回填到表单数据
    if (props.form.config) {
      props.form.config.mail_templates = selectedTemplateIds.value.reduce((acc: Record<string, any>, id) => {
        const template = emailTemplates.find(t => t.id === id)
        if (template && template.invoke) {
          // 使用 invoke 字段作为 key，去掉前缀 'activity.'
          const eventType = template.invoke.replace('activity.', '')
          acc[eventType] = template.id
        }
        return acc
      }, {})
    }
  }

  // 监听表单数据变化，更新邮件模板选择状态
  watch(() => props.form.config?.mail_templates, (mailTemplates) => {
    if (mailTemplates && typeof mailTemplates === 'object') {
      // 从 mail_templates 对象中提取模板ID
      selectedTemplateIds.value = Object.values(mailTemplates).filter(id => typeof id === 'number') as number[]
    }
  }, { immediate: true, deep: true })

  // 监听表单数据变化，自动保存到缓存
  watch(() => props.form, () => {
    saveFormToCache()
  }, { deep: true })

  // 监听选中的模板ID变化，自动保存到缓存
  watch(selectedTemplateIds, () => {
    saveFormToCache()
  }, { deep: true })

  // 组件挂载时获取数据和恢复缓存
  onMounted(async () => {
    await getEmailTemplates()
    // 在获取模板列表后恢复缓存数据
    restoreFormFromCache()
  })

  // 组件卸载时清理
  onUnmounted(() => {
    // 清理防抖计时器
    if (saveTimer) {
      clearTimeout(saveTimer)
    }
    // 如果需要在离开页面时清除缓存，取消注释下面这行
    // clearFormCache()
  })

  // 反馈表单选项 - 使用计算属性支持国际化
  const feedbackFormOptions = computed(() => [
    { 
      label: t('Activity.basic_config_form.feedback_forms.general_satisfaction.label'), 
      value: 'general_satisfaction',
      description: t('Activity.basic_config_form.feedback_forms.general_satisfaction.description'),
      questions: 8,
      estimatedTime: `3-5${t('Activity.basic_config_form.units.minutes')}`,
      id: 1
    },
    { 
      label: t('Activity.basic_config_form.feedback_forms.detailed_evaluation.label'), 
      value: 'detailed_evaluation',
      description: t('Activity.basic_config_form.feedback_forms.detailed_evaluation.description'),
      questions: 15,
      estimatedTime: `8-10${t('Activity.basic_config_form.units.minutes')}`,
      id: 2
    },
    { 
      label: t('Activity.basic_config_form.feedback_forms.speaker_evaluation.label'), 
      value: 'speaker_evaluation',
      description: t('Activity.basic_config_form.feedback_forms.speaker_evaluation.description'),
      questions: 12,
      estimatedTime: `5-7${t('Activity.basic_config_form.units.minutes')}`,
      id: 3 
    },
    { 
      label: t('Activity.basic_config_form.feedback_forms.meeting_effectiveness.label'), 
      value: 'meeting_effectiveness',
      description: t('Activity.basic_config_form.feedback_forms.meeting_effectiveness.description'),
      questions: 10,
      estimatedTime: `5-6${t('Activity.basic_config_form.units.minutes')}`,
      id: 4
    },
    { 
      label: t('Activity.basic_config_form.feedback_forms.training_feedback.label'), 
      value: 'training_feedback',
      description: t('Activity.basic_config_form.feedback_forms.training_feedback.description'),
      questions: 14,
      estimatedTime: `7-9${t('Activity.basic_config_form.units.minutes')}`,
      id: 5
    },
    { 
      label: t('Activity.basic_config_form.feedback_forms.quick_feedback.label'), 
      value: 'quick_feedback',
      description: t('Activity.basic_config_form.feedback_forms.quick_feedback.description'),
      questions: 5,
      estimatedTime: `2-3${t('Activity.basic_config_form.units.minutes')}`,
      id: 6
    },
    { 
      label: t('Activity.basic_config_form.feedback_forms.service_evaluation.label'), 
      value: 'service_evaluation',
      description: t('Activity.basic_config_form.feedback_forms.service_evaluation.description'),
      questions: 9,
      estimatedTime: `4-5${t('Activity.basic_config_form.units.minutes')}`,
      id: 7
    },
    { 
      label: t('Activity.basic_config_form.feedback_forms.custom.label'), 
      value: 'custom',
      description: t('Activity.basic_config_form.feedback_forms.custom.description'),
      questions: 0,
      estimatedTime: t('Activity.basic_config_form.units.to_be_determined'),
      id: 8
    }
  ])

  
  // 表单验证规则
  const rules = computed(() => ({
    'config.is_multilingual': [
      { required: true, message: t('Activity.basic_config_form.validation.is_multilingual.required'), trigger: 'change' }
    ],
    'config.languages': [
      { 
        type: 'array',
        required: true, 
        message: t('Activity.basic_config_form.validation.languages.required'), 
        trigger: 'change',
        validator: (rule: any, value: string[]) => {
          if (props.form.config?.is_multilingual && (!value || value.length === 0)) {
            return false
          }
          return true
        }
      }
    ]
  }))
  const router = useRouter()
  // 查看模版
  const viewTemplate = (templateId: number) => {
    // 跳转到EDM模块的模板详情页面
  router.push({
    name: 'templateDetail',
    params: {
      id: templateId === undefined ? 'new' : templateId,
    },
    query: {
      fromCampaign: 'true',
    },
    state: { template: emailTemplates.find(t => t.id === templateId) },
  })
  }
  
  defineExpose({
    validate: () => formRef.value?.validate(),
    // 暴露缓存相关方法给父组件
    saveCache: saveFormToCache,
    restoreCache: restoreFormFromCache,
    clearCache: clearFormCache
  })
  </script>
  
  <style lang="scss" scoped>
  .basic-config-form {
    max-width: 800px;
    // margin: 0 auto;
  
    .form-section {
      margin-bottom: 40px;
      padding: 24px;
      background: #f8f9fa;
      border-radius: 8px;
  
      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 24px;
        font-size: 16px;
        font-weight: 500;
        color: #1f2937;
  
        i {
          margin-right: 8px;
          font-size: 20px;
        }
      }
    }
  
        .template-list {
      .template-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e5e7eb;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .template-loading {
      padding: 20px;
      text-align: center;
      color: #6b7280;
    }
  
    .setting-description {
      color: #6b7280;
      font-size: 14px;
      margin-bottom: 8px;
    }
  

  
    // 反馈表单选项样式
    .feedback-option {
      padding: 4px 0;
      
      .option-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        
        .option-name {
          font-weight: 500;
          color: #1f2937;
        }
      }
      
      .option-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #6b7280;
        
        .option-desc {
          flex: 1;
          margin-right: 8px;
        }
        
        .option-time {
          color: #059669;
          font-weight: 500;
        }
      }
    }
  }
  </style> 