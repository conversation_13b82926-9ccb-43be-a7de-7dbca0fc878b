<template>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="rule-info-form"
    >
      <el-form-item :label="$t('Activity.basic_config_form.rule_info_form.fields.name')" prop="name">
        <el-input 
          v-model="form.name"
          :placeholder="$t('Activity.basic_config_form.rule_info_form.placeholders.name')"
        />
      </el-form-item>
  
      <el-form-item :label="$t('Activity.basic_config_form.rule_info_form.fields.description')" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          :placeholder="$t('Activity.basic_config_form.rule_info_form.placeholders.description')"
        />
      </el-form-item>
  
      <el-form-item :label="$t('Activity.basic_config_form.rule_info_form.fields.scene')" prop="scene">
        <el-select
          v-model="form.scene"
          :placeholder="$t('Activity.basic_config_form.rule_info_form.placeholders.scene')"
          style="width: 100%"
        >
          <el-option :label="$t('Activity.basic_config_form.rule_info_form.options.scene.onsite')" value="onsite" />
          <el-option :label="$t('Activity.basic_config_form.rule_info_form.options.scene.online')" value="online" />
        </el-select>
      </el-form-item>
    </el-form>
  </template>
  
  <script setup lang="ts">
  import { ref, computed } from 'vue'
  import type { FormInstance } from 'element-plus'
  import { useI18n } from 'vue-i18n'
  
  const { t } = useI18n()
  
  const props = defineProps<{
    form: Record<string, any>
  }>()
  
  const emit = defineEmits<{
    (e: 'update:form', value: Record<string, any>): void
    (e: 'validate'): Promise<void>
  }>()
  
  const formRef = ref<FormInstance>()
  
  const rules = computed(() => ({
    name: [
      { required: true, message: t('Activity.basic_config_form.rule_info_form.validation.name.required'), trigger: 'blur' }
    ],
    scene: [
      { required: true, message: t('Activity.basic_config_form.rule_info_form.validation.scene.required'), trigger: 'change' }
    ]
  }))
  
  defineExpose({
    validate: () => formRef.value?.validate()
  })
  </script>
  
  <style lang="scss" scoped>
  .rule-info-form {
    max-width: 800px;
    margin: 0 auto;
  }
  </style> 