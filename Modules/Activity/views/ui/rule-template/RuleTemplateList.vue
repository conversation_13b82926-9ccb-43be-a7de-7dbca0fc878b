<template>
  <div class="bwms-module table-page">
    <!-- 页面标题和操作按钮 -->
    <div class="module-header">
      <el-button class="button-no-border" @click="handleBatchCopy" :disabled="selectedRows.length === 0">
        {{ $t('Activity.rule_template_list.button.batch_copy') }}
      </el-button>

      <el-button class="button-no-border el-button-plus" @click="handleCreate" type="primary">
        <el-icon class="el-icon-custom">
          <Plus />
        </el-icon>
        <span style="color: var(--el-color-white);">{{ $t('Activity.rule_template_list.button.create_template') }}</span>
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="module-con">
      <div class="box">
  
        <!-- 表格 -->
        <el-table
          v-loading="loading"
          :data="tableData"
          @selection-change="handleSelectionChange"
          style="width: 100%; height: 100%"
        >
          <template #empty>
            <el-empty :description="$t('Activity.rule_template_list.pagination.no_data')" image-size="100px" />
          </template>
          <el-table-column type="selection" width="55" />
          <el-table-column 
            prop="name" 
            :label="$t('Activity.rule_template_list.table.template_name')" 
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="scene_text" 
            :label="$t('Activity.rule_template_list.table.usage_scene')" 
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="updated_at" 
            :label="$t('Activity.rule_template_list.table.last_modified')" 
            min-width="180"
            :formatter="(row) => row.updated_at || '-'"
          />
          <el-table-column 
            prop="status" 
            :label="$t('Activity.rule_template_list.table.status')" 
            width="80"
            align="center"
          >
            <template #default="{ row }">
              <el-switch
                v-model="row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(row)"
              />
            </template>
          </el-table-column>
          <el-table-column width="120" fixed="right" :label="$t('Activity.rule_template_list.table.actions')">
            <template #default="{ row }">
              <div class="bwms-operate-btn-box">
                <div class="bwms-operate-btn" @click="handleEdit(row)">
                  <el-icon size="15">
                    <Edit />
                  </el-icon>
                </div>
                <el-button type="text" class="bwms-operate-btn" @click="handleDelete(row)">
                  <el-icon>
                    <Delete />
                  </el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="box-footer">
        <div class="pagination-container table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Activity.rule_template_list.pagination.page_size_text') }}</span>
            <el-select v-model="pageSize" class="page-size-select" @change="handleSizeChange" size="default">
              <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size" :value="size"
                class="page-size-option" />
              <template #empty>
                <div style="text-align: center; padding: 8px 0;">
                  {{ $t('Activity.rule_template_list.pagination.no_data') }}
                </div>
              </template>
            </el-select>
            <span class="total-text">{{ $t('Activity.rule_template_list.pagination.total_text', { total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination 
              v-model:current-page="currentPage" 
              background 
              layout="prev, pager, next"
              :page-size="pageSize" 
              :total="total" 
              @current-change="handleCurrentChange" 
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Search, Filter, More, Plus, Edit, Delete } from '@element-plus/icons-vue'
  import { useI18n } from 'vue-i18n'
  import { ruleTemplateService } from '../../services/ruleTemplateService'
  import type { IRuleTemplate } from '../../types'
  
  const router = useRouter()
  const { t } = useI18n()
  const loading = ref(false)
  const tableData = ref<IRuleTemplate[]>([])
  const selectedRows = ref<IRuleTemplate[]>([])
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  
  // 获取列表数据
  const fetchData = async () => {
    try {
      loading.value = true
      const params = {
        page: currentPage.value,
        per_page: pageSize.value
      }
      const response = await ruleTemplateService.getList(params)
      console.log(response)
      if (response?.data.code === 200) {
        tableData.value = response.data?.data.items || []
        total.value = response.data?.data.total || 0
      } else {
        tableData.value = []
        total.value = 0
      }
    } catch (error) {
      console.error('获取列表失败:', error)
      ElMessage.error(t('Activity.rule_template_list.message.get_list_failed'))
    } finally {
      loading.value = false
    }
  }
  
  // 创建模版
  const handleCreate = () => {
    router.push({ name: 'RuleTemplateCreate' })
  }
  
  // 编辑模版 - 跳转到创建页面
  const handleEdit = (row: IRuleTemplate) => {
    router.push({ 
      name: 'RuleTemplateCreate',
      query: { id: row.id, mode: 'edit' }
    })
  }
  

  
  
  // 批量复制
  const handleBatchCopy = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning(t('Activity.rule_template_list.message.select_copy_templates'))
      return
    }
    
    try {
      const ids = selectedRows.value.map(row => row.id)
      await ruleTemplateService.batchCopy(ids)
      ElMessage.success(t('Activity.rule_template_list.message.batch_copy_success'))
      fetchData()
    } catch (error) {
      console.error('批量复制失败:', error)
      ElMessage.error(t('Activity.rule_template_list.message.batch_copy_failed'))
    }
  }
  
  // 删除模版
  const handleDelete = (row: IRuleTemplate) => {
    ElMessageBox.confirm(
      t('Activity.rule_template_list.message.delete_confirm'),
      t('Activity.rule_template_list.dialog.title'),
      {
        confirmButtonText: t('Activity.rule_template_list.dialog.confirm'),
        cancelButtonText: t('Activity.rule_template_list.dialog.cancel'),
        type: 'warning'
      }
    ).then(async () => {
      try {
        await ruleTemplateService.delete(row.id)
        ElMessage.success(t('Activity.rule_template_list.message.delete_success'))
        fetchData()
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error(t('Activity.rule_template_list.message.delete_failed'))
      }
    })
  }
  
  // 状态变更
  const handleStatusChange = async (row: IRuleTemplate) => {
    try {
      await ruleTemplateService.updateStatus(row.id, row.status)
      ElMessage.success(t('Activity.rule_template_list.message.status_update_success'))
    } catch (error) {
      console.error('状态更新失败:', error)
      ElMessage.error(t('Activity.rule_template_list.message.status_update_failed'))
      // 恢复状态
      row.status = row.status === 1 ? 0 : 1
    }
  }
  
  // 选择变更
  const handleSelectionChange = (rows: IRuleTemplate[]) => {
    selectedRows.value = rows
  }
  
  // 分页大小变更
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    currentPage.value = 1
    fetchData()
  }
  
  // 页码变更
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    fetchData()
  }
  
  // 初始化
  onMounted(() => {
    fetchData()
  })
  </script>
  
  <style lang="scss" scoped>
  .bwms-module {
    // 使用与list.vue相同的样式结构
    .module-con {
      .box {
        padding: 20px;
        background: #fff;
        border-radius: 4px;
        padding-bottom: 0; // 移除底部padding，分页有自己的padding
      }
    }

    .box-footer {
     
      
      .pagination-container {
        padding: 16px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid #ebeef5;
        
        .pagination-left {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .page-size-text {
            color: #606266;
            font-size: 14px;
          }
          
          .page-size-select {
            width: 80px;
          }
          
          .total-text {
            color: #606266;
            font-size: 14px;
          }
        }
        
        .pagination-right {
          display: flex;
          align-items: center;
        }
      }
    }

    // 操作按钮样式
    .bwms-operate-btn-box {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .bwms-operate-btn {
        padding: 4px;
        border: none;
        background: transparent;
        cursor: pointer;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &:hover {
          background-color: #f5f7fa;
        }
        
        .el-icon {
          color: #606266;
        }
      }
    }

    .ml-2 {
      margin-left: 8px;
    }
  }

  // 表格样式
  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #fafafa;
          color: #606266;
          font-weight: 500;
        }
      }
    }
    
    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover > td {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }

  // 分页样式
  :deep(.el-pagination) {
    .el-pagination__total {
      color: #606266;
    }
    
    .btn-prev,
    .btn-next {
      background-color: #fff;
      border: 1px solid #dcdfe6;
      
      &:hover {
        background-color: #409eff;
        border-color: #409eff;
        color: #fff;
      }
      
      &.disabled {
        background-color: #f5f7fa;
        border-color: #e4e7ed;
        color: #c0c4cc;
      }
    }
    
    .el-pager li {
      background-color: #fff;
      border: 1px solid #dcdfe6;
      
      &:hover {
        background-color: #409eff;
        color: #fff;
      }
      
      &.is-active {
        background-color: #409eff;
        border-color: #409eff;
        color: #fff;
      }
    }
  }
  </style> 