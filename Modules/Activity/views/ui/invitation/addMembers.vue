<template>
  <el-dialog
    v-model="dialogVisible"
    :title="props.editData ? $t('Activity.add_members.title.edit') : $t('Activity.add_members.title.create')"
    width="600px"
    :close-on-click-modal="false"
    :show-close="true"
  >
    <el-form :model="form" label-position="top" :rules="rules" ref="formRef">
      <el-form-item :label="$t('Activity.add_members.fields.name')" prop="name" required>
        <el-input v-model="form.name" :placeholder="$t('Activity.add_members.placeholders.name')"></el-input>
      </el-form-item>
      
      <el-form-item :label="$t('Activity.add_members.fields.company')" prop="company" required>
        <el-input v-model="form.company" :placeholder="$t('Activity.add_members.placeholders.company')"></el-input>
      </el-form-item>
      
      <el-form-item :label="$t('Activity.add_members.fields.position')" prop="position" required>
        <el-input v-model="form.position" :placeholder="$t('Activity.add_members.placeholders.position')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('Activity.add_members.fields.phone')" prop="phone" required>
        <el-input v-model="form.phone" :placeholder="$t('Activity.add_members.placeholders.phone')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('Activity.add_members.fields.email')" prop="email" required>
        <el-input v-model="form.email" :placeholder="$t('Activity.add_members.placeholders.email')"></el-input>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">{{ $t('Activity.add_members.buttons.cancel') }}</el-button>
        <el-button type="primary" @click="handleCreate" :loading="loading">{{ props.editData ? $t('Activity.add_members.buttons.update') : $t('Activity.add_members.buttons.create') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>
  
<script setup lang="ts">
import { ref, defineProps, watch, defineEmits, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules } from 'element-plus'

const { t } = useI18n()

interface EditMember {
  id?: number
  name: string
  company: string
  position: string
  phone: string
  email: string
}

const props = defineProps<{
  visible: boolean
  editData?: EditMember | null
  isView?: boolean
}>()

const emit = defineEmits(['update:visible', 'cancel', 'create'])

const dialogVisible = ref(props.visible)
const loading = ref(false)

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  // 当弹窗打开时，如果有编辑数据则回填
  if (newVal && props.editData) {
    form.value = {
      name: props.editData.name || '',
      company: props.editData.company || '',
      position: props.editData.position || '',
      phone: props.editData.phone || '',
      email: props.editData.email || '',
    }
  } else if (newVal && !props.editData) {
    // 新增时重置表单
    resetForm()
  }
})

// 监听dialogVisible变化
watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal)
})

const formRef = ref<FormInstance>()
const form = ref({
  name: '',
  company: '',
  position: '',
  phone: '',
  email: '',
})

const rules = computed<FormRules>(() => ({
  name: [{ required: true, message: t('Activity.add_members.validation.name.required'), trigger: 'blur' }],
  company: [{ required: true, message: t('Activity.add_members.validation.company.required'), trigger: 'blur' }],
  position: [{ required: true, message: t('Activity.add_members.validation.position.required'), trigger: 'blur' }],
  phone: [{ required: true, message: t('Activity.add_members.validation.phone.required'), trigger: 'blur' }],
  email: [
    { required: true, message: t('Activity.add_members.validation.email.required'), trigger: 'blur' },
    { type: 'email', message: t('Activity.add_members.validation.email.format'), trigger: 'blur' }
  ]
}))

// 重置表单方法
const resetForm = () => {
  form.value = {
    name: '',
    company: '',
    position: '',
    phone: '',
    email: '',
  }
}

const handleCancel = () => {
  resetForm()
  dialogVisible.value = false
  emit('cancel')
}

const handleCreate = async () => {
  if (!formRef.value) return
  
  loading.value = true
  
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      emit('create', { ...form.value })
      // 重置表单
      resetForm()
      dialogVisible.value = false
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  margin-right: 0;
}

:deep(.el-form-item.is-required .el-form-item__label::before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>