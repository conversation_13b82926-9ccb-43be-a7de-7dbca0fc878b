export interface InvitationItem {
  id: number
  name: string
  email: string
  status: 'pending' | 'accepted' | 'rejected'
  created_at: string
  updated_at: string
  member_count: number
  mailbox_count: number
}

export interface InvitationQuery {
  page: number
  per_page: number
  keyword?: string
  status?: string
}

export interface InvitationForm {
  title: string
  description: string
  is_default: boolean
  is_group: boolean
  group_type?: string
}

export interface InvitationMember {
  id?: number
  name: string
  company: string
  position: string
  phone: string
  email: string
  status?: number
  created_at?: string
  updated_at?: string
}

export interface InvitationDetail {
  id: number
  activity_id: number
  title: string
  description: string
  is_default: number
  is_group: number
  group_id: number | null
  status: number
  creator_id: number
  created_at: string
  updated_at: string
  members?: InvitationMember[]
} 