<template>
  <div class="invitation-member-list">
    <!-- 标题和操作按钮 -->
    <div class="title-box">
      <div class="btn-group">
        <el-upload
          ref="uploadRef"
          :action="''"
          :auto-upload="false"
          :on-change="handleFileChange"
          :show-file-list="false"
          accept=".csv,.xlsx,.xls"
          :disabled="!props.isEdit"
        >
          <el-button type="primary" link :disabled="!props.isEdit">
            <el-icon class="el-icon--right"><Upload /></el-icon>
            {{ props.isEdit ? $t('Activity.invitation_member_list.buttons.batch_import') : $t('Activity.invitation_member_list.buttons.batch_import_disabled') }}
          </el-button>
        </el-upload>
        <el-button 
          type="primary" 
          link 
          @click="handleExportMembers"
          :disabled="!props.isEdit || !props.invitationId"
          :loading="exportLoading"
        >
          <el-icon class="el-icon--right"><Download /></el-icon>
          {{ exportLoading ? $t('Activity.invitation_member_list.buttons.exporting') : (props.isEdit ? $t('Activity.invitation_member_list.buttons.export_members') : $t('Activity.invitation_member_list.buttons.export_members_disabled')) }}
        </el-button>
      </div>
    </div>

    <!-- 优化后的搜索栏 -->
    <div class="search-container">
      <div class="search-form">
        <div class="search-row">
          <div class="search-item">
            <label class="search-label">{{ $t('Activity.invitation_member_list.search.name') }}</label>
            <el-input 
              v-model="searchQuery.name" 
              :placeholder="$t('Activity.invitation_member_list.search.name_placeholder')" 
              clearable 
              @input="handleSearch"
              class="search-input"
            />
          </div>
          <div class="search-item">
            <label class="search-label">{{ $t('Activity.invitation_member_list.search.email') }}</label>
            <el-input 
              v-model="searchQuery.email" 
              :placeholder="$t('Activity.invitation_member_list.search.email_placeholder')" 
              clearable 
              @input="handleSearch"
              class="search-input"
            />
          </div>
          <div class="search-item">
            <label class="search-label">{{ $t('Activity.invitation_member_list.search.phone') }}</label>
            <el-input 
              v-model="searchQuery.phone" 
              :placeholder="$t('Activity.invitation_member_list.search.phone_placeholder')" 
              clearable 
              @input="handleSearch"
              class="search-input"
            />
          </div>
          <div class="search-actions">
            <el-button type="primary" @click="filterMemberList">{{ $t('Activity.invitation_member_list.buttons.search') }}</el-button>
            <el-button @click="resetSearch">{{ $t('Activity.invitation_member_list.buttons.reset') }}</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 成员表格 -->
    <el-table :data="tableData" style="width: 100%" v-loading="loading">
      <template #empty>
        <el-empty description="No Data" image-size="100px" />
      </template>
      <el-table-column prop="name" label="名字" />
      <el-table-column prop="company" label="公司" />
      <el-table-column prop="position" label="職位" width="120" />
      <el-table-column prop="phone" label="工作電話" width="140" />
      <el-table-column prop="email" label="email" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 0" type="info">未回复</el-tag>
          <el-tag v-else-if="scope.row.status === 1" type="success">已报名</el-tag>
          <el-tag v-else-if="scope.row.status === 2" type="danger">已拒绝</el-tag>
          <el-tag v-else type="warning">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="150">
        <template #default="scope">
          <el-button type="primary" size="small" text @click="handleEditMember(scope.row)">编辑</el-button>
          <el-button type="danger" style='color: #f56c6c;' size="small" text @click="handleDeleteMember(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加成员按钮 -->
    <el-button style="width: 100%;" type="primary" text @click="onAddItem">
      添加成員<el-icon><Plus /></el-icon>
    </el-button>

    <!-- 参考Info.vue的按钮样式 -->
    <div class="op-box">
      <el-button @click="handlePrevStep">上一步</el-button>
      <el-button @click="handleCancel">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleSubmit"
      > 
        保存并提交 
      </el-button>
    </div>

    <!-- 添加/编辑成员弹窗 -->
    <AddMembers 
      v-model:visible="addMembersVis" 
      :edit-data="editingMember"
      :is-view="isViewMode"
      @cancel="handleCancelMember" 
      @create="handleCreateMember" 
    />

    <!-- 删除成员确认弹窗 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="刪除確認"
      class="el-dialog-common-cls"
      width="400px"
      :close-on-click-modal="!deleteLoading"
      :close-on-press-escape="!deleteLoading"
      :show-close="!deleteLoading"
    >
      <div class="delete-content">
        <p>確定要刪除成員「{{ currentDeleteMember?.name }}」嗎？</p>
        <p class="warning-text">此操作不可恢復，請謹慎操作。</p>
      </div>
      <template #footer>
        <div class="flex justify-center">
          <el-button class="el-button-default" @click="cancelDeleteMember" :disabled="deleteLoading">
            取消
          </el-button>
          <el-button class="button-no-border" type="danger" @click="confirmDeleteMember" :loading="deleteLoading">
            {{ deleteLoading ? '刪除中...' : '確認刪除' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Plus, Upload } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import type { InvitationMember } from './types'
import AddMembers from './addMembers.vue'
import { invitationService, type MemberListParams } from '../../services/invitationService'

const { t } = useI18n()

const props = defineProps<{
  activityId: string
  invitationId?: string
  isEdit: boolean
  // 接收分页相关props
  currentPage: number
  pageSize: number
  total: number
}>()

const emit = defineEmits<{
  'update:memberList': [members: InvitationMember[]]
  'prev-step': []
  'cancel': []
  'submit': []
  // 分页相关事件
  'page-change': [page: number]
  'size-change': [size: number]
}>()

// 成员列表数据
const tableData = ref<InvitationMember[]>([])
const allMemberData = ref<InvitationMember[]>([]) // 存储所有数据，用于前端搜索
const memberTotal = ref(0)
const loading = ref(false)

// 搜索查询条件 - 移除分页相关字段
const searchQuery = reactive<Omit<MemberListParams, 'page' | 'per_page'>>({
  name: '',
  email: '',
  phone: '',
  status: undefined
})

// 成员弹窗相关
const addMembersVis = ref(false)
const editingMember = ref<InvitationMember | null>(null)
const isViewMode = ref(false)

// 删除成员弹窗相关
const deleteDialogVisible = ref(false)
const deleteLoading = ref(false)
const currentDeleteMember = ref<InvitationMember | null>(null)

// 创建邀请列表相关
const createLoading = ref(false)

// 导出成员相关
const exportLoading = ref(false)

// 前端搜索逻辑 - 使用props中的分页参数
const filterMemberList = () => {
  let filteredData = [...allMemberData.value]
  
  // 按姓名搜索
  if (searchQuery.name && searchQuery.name.trim()) {
    filteredData = filteredData.filter(item => 
      item.name.toLowerCase().includes(searchQuery.name!.toLowerCase())
    )
  }
  
  // 按邮箱搜索
  if (searchQuery.email && searchQuery.email.trim()) {
    filteredData = filteredData.filter(item => 
      item.email.toLowerCase().includes(searchQuery.email!.toLowerCase())
    )
  }
  
  // 按电话搜索
  if (searchQuery.phone && searchQuery.phone.trim()) {
    filteredData = filteredData.filter(item => 
      item.phone.includes(searchQuery.phone!)
    )
  }
  
  // 按状态搜索
  if (searchQuery.status !== undefined) {
    filteredData = filteredData.filter(item => item.status === searchQuery.status)
  }
  
  // 更新总数
  memberTotal.value = filteredData.length
  
  // 分页处理 - 使用props中的分页参数
  const page = props.currentPage || 1
  const perPage = props.pageSize || 10
  const start = (page - 1) * perPage
  const end = start + perPage
  tableData.value = filteredData.slice(start, end)
}

// 搜索防抖
let searchTimer: any = null
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    // 搜索时重置到第一页
    emit('page-change', 1)
    filterMemberList()
  }, 300)
}

// 重置搜索
const resetSearch = () => {
  searchQuery.name = ''
  searchQuery.email = ''
  searchQuery.phone = ''
  searchQuery.status = undefined
  emit('page-change', 1)
  filterMemberList()
}

// 获取成员列表 - 修复接口调用逻辑
const fetchMemberList = async () => {
  console.log('fetchMemberList调用:', {
    activityId: props.activityId,
    isEdit: props.isEdit,
    invitationId: props.invitationId
  })
  
  // 新建模式下不调用接口，直接返回
  if (!props.isEdit) {
    console.log('新建模式，不调用接口获取成员列表')
    return
  }
  
  if (!props.activityId) {
    console.warn('缺少activityId参数')
    return
  }

  // 如果是编辑模式，需要有invitationId才能获取数据
  if (props.isEdit && !props.invitationId) {
    console.warn('编辑模式但缺少invitationId')
    return
  }

  loading.value = true
  try {
    console.log('开始调用接口获取成员列表')
    // 编辑模式下调用接口获取数据，使用props中的分页参数
    const queryParams = {
      page: props.currentPage,
      per_page: props.pageSize,
      ...searchQuery
    }
    const res = await invitationService.getMemberList(props.invitationId!, queryParams)
    console.log('接口返回数据:', res.data)
    
    if (res.data && res.data.code === 200 && res.data.data) {
      allMemberData.value = res.data.data.items || []
      memberTotal.value = res.data.data.total || 0
      console.log('成员列表更新成功:', allMemberData.value.length, '条记录')
      // 重新应用搜索过滤
      filterMemberList()
    } else {
      console.warn('接口返回数据格式异常:', res.data)
      ElMessage.warning('获取成员列表数据格式异常')
    }
  } catch (error) {
    console.error('获取成员列表失败:', error)
    ElMessage.error('获取成员列表失败')
  } finally {
    loading.value = false
  }
}

// 文件上传
const handleFileChange = async (file: any) => {
  // 新建模式下禁用文件上传功能
  if (!props.isEdit) {
    ElMessage.warning('请先保存邀请信息后再导入成员')
    return
  }
  
  if (!props.invitationId) {
    ElMessage.warning('请先保存邀请信息后再导入成员')
    return
  }
  
  if (!props.activityId) {
    ElMessage.error('缺少活动ID参数')
    return
  }
  
  try {
    const res = await invitationService.importMembers(props.activityId || '', file.raw)
    if (res.data && res.data.code === 200) {
      ElMessage.success(`导入成功 ${res.data.data.success_count} 条，失败 ${res.data.data.fail_count} 条`)
      fetchMemberList()
    }
  } catch (error) {
    console.error('导入成员失败:', error)
    ElMessage.error('导入成员失败')
  }
}

// 导出成员
const handleExportMembers = async () => {
  if (!props.invitationId) {
    ElMessage.warning('请先保存邀请信息后再导出成员')
    return
  }
  
  exportLoading.value = true
  try {
    const response = await invitationService.exportMembers(props.invitationId)
    
    // 创建下载链接
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    
    // 从响应头获取文件名，如果没有则使用默认名称
    const contentDisposition = response.headers['content-disposition']
    let fileName = '邀请成员列表.xlsx'
    if (contentDisposition) {
      const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (fileNameMatch && fileNameMatch[1]) {
        fileName = fileNameMatch[1].replace(/['"]/g, '')
      }
    }
    
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    
    // 清理
    window.URL.revokeObjectURL(url)
    document.body.removeChild(link)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出成员失败:', error)
    ElMessage.error('导出成员失败')
  } finally {
    exportLoading.value = false
  }
}

// 编辑成员
const handleEditMember = (member: InvitationMember) => {
  editingMember.value = member
  isViewMode.value = false
  addMembersVis.value = true
}

// 查看成员详情
const handleViewMember = (member: InvitationMember) => {
  editingMember.value = { ...member }
  isViewMode.value = true
  addMembersVis.value = true
}

// 删除成员
const handleDeleteMember = (member: InvitationMember) => {
  if (!member.id) return
  currentDeleteMember.value = member
  deleteDialogVisible.value = true
}

// 取消删除成员
const cancelDeleteMember = () => {
  if (deleteLoading.value) return // 防止在删除过程中取消
  deleteDialogVisible.value = false
  currentDeleteMember.value = null
}

// 确认删除成员
const confirmDeleteMember = async () => {
  if (!currentDeleteMember.value?.id) return
  
  deleteLoading.value = true
  try {
    // 新建模式下直接从本地删除
    if (!props.isEdit) {
      // 从本地列表中移除
      const index = allMemberData.value.findIndex(item => item.id === currentDeleteMember.value?.id)
      if (index > -1) {
        allMemberData.value.splice(index, 1)
        ElMessage.success('删除成员成功')
        
        // 给用户一点时间看到成功消息，然后关闭弹窗
        setTimeout(() => {
          deleteDialogVisible.value = false
          currentDeleteMember.value = null
        }, 800)
        
        filterMemberList() // 重新过滤显示
        emit('update:memberList', allMemberData.value)
      }
      return
    }
    
    // 编辑模式下调用接口删除
    await invitationService.deleteMember(currentDeleteMember.value.id)
    
    // 删除成功后先显示成功消息
    ElMessage.success('删除成员成功')
    
    // 给用户一点时间看到成功消息，然后关闭弹窗
    setTimeout(() => {
      deleteDialogVisible.value = false
      currentDeleteMember.value = null
    }, 800)
    
    // 从本地列表中移除
    const index = tableData.value.findIndex(item => item.id === currentDeleteMember.value?.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      emit('update:memberList', tableData.value)
    }
    
    // 重新获取数据确保同步
    fetchMemberList()
  } catch (error) {
    console.error('删除成员失败:', error)
    ElMessage.error('刪除成員失敗，請稍後重試')
  } finally {
    deleteLoading.value = false
  }
}

// 添加成员
const onAddItem = () => {
  editingMember.value = null
  isViewMode.value = false
  addMembersVis.value = true
}

// 取消成员操作
const handleCancelMember = () => {
  editingMember.value = null
  isViewMode.value = false
  addMembersVis.value = false
}

// 创建/更新成员
const handleCreateMember = async (val: InvitationMember) => {
  addMembersVis.value = false
   
  if (!props.activityId) {
    ElMessage.error('缺少活动ID参数')
    return
  }

  // 新建模式下直接操作本地数据
  if (!props.isEdit) {
    const memberData = {
      id: Date.now(), // 临时ID，用于前端操作
      activity_id: props.activityId,
      name: val.name,
      phone: val.phone,
      email: val.email,
      company: val.company,
      position: val.position || '',
      status: 0, // 默认状态
      invitation_id: 0, // 新建时没有invitation_id
      user_id: 0,
      code: '',
      creator_id: 0,
      created_at: new Date().toISOString().slice(0, 16).replace('T', ' '),
      updated_at: new Date().toISOString().slice(0, 16).replace('T', ' ')
    }
    
    if (editingMember.value && editingMember.value.id) {
      // 编辑本地成员
      const index = allMemberData.value.findIndex(item => item.id === editingMember.value?.id)
      if (index > -1) {
        allMemberData.value[index] = { ...allMemberData.value[index], ...memberData }
        ElMessage.success('更新成员成功')
      }
    } else {
      // 添加到本地列表
      allMemberData.value.push(memberData as InvitationMember)
      ElMessage.success('添加成员成功')
    }
    
    editingMember.value = null
    isViewMode.value = false
    filterMemberList() // 重新过滤显示
    return
  }

  // 编辑模式下调用接口
  try {
    const memberData = {
      activity_id: props.activityId,
      name: val.name,
      phone: val.phone,
      email: val.email,
      company: val.company,
      position: val.position || ''
    }
    
    if (editingMember.value && editingMember.value.id) {
      // 编辑模式
      await invitationService.updateMember(props.invitationId!, editingMember.value.id, memberData)
      ElMessage.success('更新成员成功')
    } else {
      // 创建模式
      await invitationService.createMember(props.invitationId!, memberData)
      ElMessage.success('添加成员成功')
    }
    
    editingMember.value = null
    isViewMode.value = false
    fetchMemberList()
  } catch (error) {
    console.error('保存成员失败:', error)
    ElMessage.error('保存成员失败')
  }
}

// 获取当前成员列表 - 修复返回数据问题
const getMemberList = () => {
  // 新建模式下返回本地数据，编辑模式下返回当前显示的数据
  if (!props.isEdit) {
    return allMemberData.value.map(member => ({
      name: member.name,
      phone: member.phone,
      email: member.email,
      company: member.company,
      position: member.position || '',
      status: member.status || 0
    }))
  }
  return tableData.value
}

// 按钮事件处理
const handlePrevStep = () => {
  emit('prev-step')
}

const handleCancel = () => {
  emit('cancel')
}

const handleSubmit = () => {
  emit('submit')
}

// 监听props变化
watch(() => [props.invitationId, props.currentPage, props.pageSize], () => {
  if (props.isEdit && props.invitationId) {
    fetchMemberList()
  } else {
    filterMemberList()
  }
})

// 新建模式下的成员数据实时同步给父组件
watch(() => allMemberData.value, (newMembers) => {
  if (!props.isEdit) {
    emit('update:memberList', newMembers)
  }
}, { deep: true })

// 初始化数据
const initMemberList = (members: InvitationMember[]) => {
  if (!props.isEdit) {
    // 新建模式下，初始化本地数据
    allMemberData.value = members || []
    filterMemberList()
  } else {
    // 编辑模式下，初始化表格数据
    tableData.value = members || []
  }
}

onMounted(() => {

    fetchMemberList()

})

defineExpose({
  initMemberList,
  getMemberList,
  fetchMemberList
})
</script>

<style scoped>
.title-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}

.btn-group {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 5px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.search-form {
  width: 100%;
}

.search-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr auto;
  gap: 10px;
  align-items: end;
}

.search-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.search-input {
  width: 100%;
}

.search-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

@media (max-width: 1200px) {
  .search-row {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
  
  .search-actions {
    grid-column: 1 / -1;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .search-row {
    grid-template-columns: 1fr;
  }
  
  .search-container {
    padding: 16px;
  }
}

/* 删除弹窗样式 */
.delete-content {
  text-align: center;
  padding: 20px 0;
}

.delete-content p {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.delete-content .warning-text {
  font-size: 14px;
  color: #f56c6c;
}

.flex {
  display: flex;
  gap: 12px;
}

.justify-center {
  justify-content: center;
}

/* 参考Info.vue的按钮样式 */
.op-box {
  display: flex;
  justify-content: end;
  gap: 16px;
  padding: 30px 0;
  margin-top: 30px;
  border-top: 1px solid #ebeef5;
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 10;
}
</style> 