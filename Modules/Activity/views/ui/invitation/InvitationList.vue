<template>
  <div class="invitation-list-container">
    <!-- 邀请列表基础设置 -->
    <div class="invitation-settings-section">
      <h3 class="section-title">{{ $t('Activity.invitation_list.settings.title') }}</h3>
      
      <div class="settings-form">
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">{{ $t('Activity.invitation_list.settings.list_name') }}</label>
            <el-input 
              v-model="invitationSettings.listName"
              :placeholder="$t('Activity.invitation_list.settings.list_name_placeholder')"
              class="form-input"
            />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">{{ $t('Activity.invitation_list.settings.list_description') }}</label>
            <el-input 
              v-model="invitationSettings.description"
              :placeholder="$t('Activity.invitation_list.settings.description_placeholder')"
              class="form-input"
            />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group half-width">
            <label class="form-label">{{ $t('Activity.invitation_list.settings.is_default_list') }}</label>
            <div class="radio-group">
              <el-radio v-model="invitationSettings.isDefault" :label="true">
                {{ $t('Activity.invitation_list.settings.yes') }}
              </el-radio>
              <el-radio v-model="invitationSettings.isDefault" :label="false">
                {{ $t('Activity.invitation_list.settings.no') }}
              </el-radio>
            </div>
          </div>
          
          <div class="form-group half-width">
            <label class="form-label">{{ $t('Activity.invitation_list.settings.can_register_group') }}</label>
            <div class="radio-group">
              <el-radio v-model="invitationSettings.canRegisterGroup" :label="true">
                {{ $t('Activity.invitation_list.settings.yes') }}
              </el-radio>
              <el-radio v-model="invitationSettings.canRegisterGroup" :label="false">
                {{ $t('Activity.invitation_list.settings.no') }}
              </el-radio>
            </div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">{{ $t('Activity.invitation_list.settings.export_format') }}</label>
            <el-select 
              v-model="invitationSettings.exportFormat" 
              :placeholder="$t('Activity.invitation_list.settings.export_format_placeholder')"
              class="form-select"
            >
              <el-option label="VIP" value="vip" />
              <el-option label="CSV" value="csv" />
              <el-option label="Excel" value="excel" />
            </el-select>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <h3 class="section-title">{{ $t('Activity.invitation_list.members.title') }}</h3>
      <div class="search-actions">
        <el-input 
          v-model="searchKeyword"
          :placeholder="$t('Activity.invitation_list.members.search_placeholder')"
          prefix-icon="Search"
          @input="handleSearch"
          clearable
          class="search-input"
        />
        <el-button type="text" class="advanced-search-btn">
          {{ $t('Activity.invitation_list.members.advanced_search') }}
        </el-button>
      </div>
    </div>

    <!-- 成员列表 -->
    <div class="members-list-section">
      <div class="section-header">
        <div class="section-actions">
          <el-button class="custom-action-btn">
            {{ $t('Activity.invitation_list.members.remove') }}
          </el-button>
          <el-button class="custom-action-btn">
            {{ $t('Activity.invitation_list.members.transfer_to') }}
          </el-button>
        </div>
      </div>

      <el-table :data="membersList" style="width: 100%;" v-loading="loading">
        <template #empty>
          <el-empty :description="$t('Activity.invitation_list.empty.description')" image-size="100px" />
        </template>
        
        <el-table-column type="selection" width="55" />
        
        <el-table-column 
          prop="name" 
          :label="$t('Activity.invitation_list.table.member_name')" 
          min-width="120"
          show-overflow-tooltip
        />
        
        <el-table-column 
          prop="company" 
          :label="$t('Activity.invitation_list.table.company')" 
          min-width="150"
          show-overflow-tooltip
        />
        
        <el-table-column 
          prop="position" 
          :label="$t('Activity.invitation_list.table.position')" 
          min-width="120"
          show-overflow-tooltip
        />
        
        <el-table-column 
          prop="work_number" 
          :label="$t('Activity.invitation_list.table.work_number')" 
          min-width="120"
          show-overflow-tooltip
        />
        
        <el-table-column 
          prop="email" 
          :label="$t('Activity.invitation_list.table.email')" 
          min-width="200"
          show-overflow-tooltip
        />
        
        <el-table-column 
          prop="status" 
          :label="$t('Activity.invitation_list.table.status')" 
          width="100"
        >
          <template #default="{ row }">
            <el-tag :type="getMemberStatusType(row.status)">
              {{ getMemberStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 删除确认弹窗 -->
    <el-dialog
      v-model="deleteDialogVisible"
      :title="$t('Activity.invitation_list.delete_dialog.title')"
      class="el-dialog-common-cls"
      width="400px"
      :close-on-click-modal="!deleteLoading"
      :close-on-press-escape="!deleteLoading"
      :show-close="!deleteLoading"
    >
      <div class="delete-content">
        <p>{{ $t('Activity.invitation_list.delete_dialog.content', { title: currentDeleteItem?.name }) }}</p>
        <p class="warning-text">{{ $t('Activity.invitation_list.delete_dialog.warning') }}</p>
      </div>
      <template #footer>
        <div class="flex justify-center">
          <el-button class="el-button-default" @click="cancelDelete" :disabled="deleteLoading">
            {{ $t('Activity.invitation_list.delete_dialog.cancel') }}
          </el-button>
          <el-button class="button-no-border" type="danger" @click="confirmDelete" :loading="deleteLoading">
            {{ deleteLoading ? $t('Activity.invitation_list.delete_dialog.deleting') : $t('Activity.invitation_list.delete_dialog.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { invitationService, type InvitationListParams } from "../../services/invitationService"

const { t } = useI18n()

// Props - 接收活动ID和分页信息
interface Props {
  activityId?: string | number
  pagination?: {
    page: number
    pageSize: number
    total: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  activityId: '',
  pagination: () => ({ page: 1, pageSize: 10, total: 0 })
})

// Emits - 发送事件
const emit = defineEmits<{
  'update-total': [total: number]
}>()

// 邀请设置数据结构
interface InvitationSettings {
  listName: string
  description: string
  isDefault: boolean
  canRegisterGroup: boolean
  exportFormat: string
}

// 成员项接口
interface MemberItem {
  id: number
  name: string
  company: string
  position: string
  work_number: string
  email: string
  status: string
}

// 邀请项接口 - 保留原有结构用于兼容
interface InvitationItem {
  id: number
  activity_id: number
  title: string
  description: string
  is_default: number
  is_group: number
  group_id: number | null
  status: number
  creator_id: number
  created_at: string
  updated_at: string
}

const router = useRouter()
const route = useRoute()

// 邀请设置数据
const invitationSettings = reactive<InvitationSettings>({
  listName: '2025年高端商業論壇人員名單',
  description: 'NGO工作人員',
  isDefault: true,
  canRegisterGroup: true,
  exportFormat: 'vip'
})

// 成员列表数据
const membersList = ref<MemberItem[]>([
  {
    id: 1,
    name: 'John Doe',
    company: '紅十字會',
    position: '項目經理',
    work_number: '+852 1234 5678',
    email: '<EMAIL>',
    status: '未邀請'
  },
  {
    id: 2,
    name: 'Kevin Kim',
    company: '聖雅之光',
    position: '運動部長',
    work_number: '-',
    email: '<EMAIL>',
    status: '未邀請'
  },
  {
    id: 3,
    name: 'Lydia Lau',
    company: '康復生命NGO',
    position: '-',
    work_number: '+852 3333 5555',
    email: '<EMAIL>',
    status: '未邀請'
  },
  {
    id: 4,
    name: 'Ken Wong',
    company: '愛華三所',
    position: '生康部部長',
    work_number: '+852 4668 1029',
    email: '<EMAIL>',
    status: '未邀請'
  }
])

// 搜索关键词
const searchKeyword = ref('')

// 表格数据 - 保留原有变量名用于兼容
const invitationList = ref<InvitationItem[]>([])
const loading = ref(false)

// 删除相关
const deleteDialogVisible = ref(false)
const deleteLoading = ref(false)
const currentDeleteItem = ref<MemberItem | null>(null)

// 搜索处理
const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value)
  // TODO: 实现搜索逻辑
}

// 成员状态处理
const getMemberStatusType = (status: string) => {
  const map: Record<string, string> = {
    '未邀请': 'info',
    '已邀请': 'warning',
    '已接受': 'success',
    '已拒绝': 'danger'
  }
  return map[status] || 'info'
}

const getMemberStatusText = (status: string) => {
  return status || '未知'
}

// 成员操作方法
const handleEditMember = (row: MemberItem) => {
  console.log('编辑成员:', row)
  // TODO: 实现编辑成员逻辑
}

const handleDeleteMember = (row: MemberItem) => {
  currentDeleteItem.value = row
  deleteDialogVisible.value = true
}

// 保留原有的状态处理函数用于兼容
const getStatusType = (status: number) => {
  const map: Record<number, string> = {
    0: 'danger',
    1: 'success',
    2: 'warning'
  }
  return map[status] || 'info'
}

const getStatusText = (status: number) => {
  const map: Record<number, string> = {
    0: t('Activity.invitation_list.status.disabled'),
    1: t('Activity.invitation_list.status.enabled'), 
    2: t('Activity.invitation_list.status.draft')
  }
  return map[status] || '未知'
}

// 保留原有操作方法用于兼容
const handleEdit = (row: InvitationItem) => {
  router.push({
    name: 'InvitationEdit',
    params: {
      id: row.id.toString()
    },
    query: {
      activityId: props.activityId?.toString() || route.params.id
    }
  })
}

const handleDelete = (row: InvitationItem) => {
  // 兼容旧的删除逻辑
}

const handleView = (row: InvitationItem) => {
  router.push({
    path: '/activity/invitation/details',
    query: {
      id: row.id.toString(),
      activityId: props.activityId?.toString() || route.params.id
    }
  })
}

// 删除确认
const cancelDelete = () => {
  if (deleteLoading.value) return
  deleteDialogVisible.value = false
  currentDeleteItem.value = null
}

const confirmDelete = async () => {
  if (!currentDeleteItem.value) return
  
  deleteLoading.value = true
  try {
    // TODO: 调用删除成员的API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    
    ElMessage.success(t('Activity.invitation_list.messages.delete_success'))
    
    setTimeout(() => {
      deleteDialogVisible.value = false
      currentDeleteItem.value = null
    }, 800)
    
    // 重新加载数据
    await getTableDataList()
  } catch (error) {
    console.error('删除成员失败:', error)
    ElMessage.error(t('Activity.invitation_list.messages.delete_failed'))
  } finally {
    deleteLoading.value = false
  }
}

// 查询列表 - 保留原有方法用于兼容
const getTableDataList = async () => {
  loading.value = true
  try {
    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 更新总数
    emit('update-total', membersList.value.length)
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error(t('Activity.invitation_list.messages.get_list_failed'))
  } finally {
    loading.value = false
  }
}

// 暴露刷新方法给父组件
const refresh = () => {
  getTableDataList()
}

// 暴露方法
defineExpose({
  refresh
})

// 监听分页变化
watch(
  () => props.pagination,
  (newPagination, oldPagination) => {
    if (newPagination && oldPagination) {
      if (
        newPagination.page !== oldPagination.page ||
        newPagination.pageSize !== oldPagination.pageSize
      ) {
        getTableDataList()
      }
    }
  },
  { deep: true }
)

onMounted(() => {
  getTableDataList()
})
</script>

<style lang="scss" scoped>
.invitation-list-container {
  width: 100%;
  
  // 邀请设置区域
  .invitation-settings-section {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 24px 0;
      padding-bottom: 12px;
      border-bottom: 1px solid #EBEEF5;
    }
    
    .settings-form {
      .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
      
      .form-group {
        flex: 1;
        
        &.half-width {
          flex: 0 0 calc(50% - 10px);
        }
        
        .form-label {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: #606266;
          margin-bottom: 8px;
        }
        
        .form-input, .form-select {
          width: 100%;
        }
        
        .radio-group {
          display: flex;
          gap: 24px;
          
          :deep(.el-radio) {
            margin-right: 0;
          }
        }
      }
    }
  }
  
  // 搜索区域
  .search-section {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }

    .search-actions {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-input {
        width: 250px;
      }

      .advanced-search-btn {
        padding: 0;
        font-size: 14px;
        color: #409eff;
        text-decoration: none;
        
        &:hover {
          color: #66b1ff;
        }
      }
    }
  }

  // 成员列表区域
  .members-list-section {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    .section-header {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-bottom: 16px;
      
      .section-actions {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .custom-action-btn {
          width: 99px;
          height: 40px;
          background: #FFFFFF;
          border: 1px solid #DEDEDE;
          border-radius: 5px;
          opacity: 1;
          font-size: 14px;
          color: #606266;
          
          &:hover {
            background: #F5F5F5;
            border: 1px solid #DEDEDE;
            color: #606266;
          }
          
          &:active {
            background: #F5F5F5;
            border: 1px solid #DEDEDE;
          }
        }
      }
    }
  }
  
  // 操作按钮样式
  .operate-btn-box {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .operate-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #dcdfe6;
    background: #fff;

    &:hover {
      border-color: #409eff;
      background: #ecf5ff;
      
      .el-icon {
        color: #409eff;
      }
    }

    .el-icon {
      font-size: 16px;
      color: #606266;
    }
  }

  .del-btn {
    &:hover {
      border-color: #f56c6c;
      background: #fef0f0;
      
      .el-icon {
        color: #f56c6c;
      }
    }
  }
}

// 删除弹窗样式
.delete-content {
  text-align: center;
  padding: 20px 0;
  
  p {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #303133;
  }
  
  .warning-text {
    font-size: 14px;
    color: #f56c6c;
  }
}

.flex {
  display: flex;
  gap: 12px;
}

.justify-center {
  justify-content: center;
}
</style> 