<template>
  <div class="invitation-list-settings">
    <el-form ref="formRef" label-position="top" :model="formData" style="width: 100%" class="invitation-form">
      <div class="title">
        <el-icon><Tickets /></el-icon>
        {{ $t('Activity.invitation_list_settings.title') }}
      </div>

      <el-form-item :label="$t('Activity.invitation_list_settings.form.list_name')" prop="title" required>
        <el-input v-model="formData.title" :placeholder="$t('Activity.invitation_list_settings.form.list_name_placeholder')" />
      </el-form-item>

      <el-form-item :label="$t('Activity.invitation_list_settings.form.description')" prop="description">
        <el-input v-model="formData.description" type="textarea" :placeholder="$t('Activity.invitation_list_settings.form.description_placeholder')" :maxlength="500" show-word-limit />
      </el-form-item>

      <el-form-item :label="$t('Activity.invitation_list_settings.form.set_as_default')">
        <el-radio-group v-model="formData.is_default">
          <el-radio :label="true">{{ $t('Activity.invitation_list_settings.options.yes') }}</el-radio>
          <el-radio :label="false">{{ $t('Activity.invitation_list_settings.options.no') }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item :label="$t('Activity.invitation_list_settings.form.assign_group')">
        <el-radio-group v-model="formData.is_group">
          <el-radio :label="true">{{ $t('Activity.invitation_list_settings.options.yes') }}</el-radio>
          <el-radio :label="false">{{ $t('Activity.invitation_list_settings.options.no') }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item :label="$t('Activity.invitation_list_settings.form.group_type')" v-if="formData.is_group">
        <el-select v-model="formData.group_type">
          <el-option :label="$t('Activity.invitation_list_settings.options.vip')" value="vip" />
          <el-option :label="$t('Activity.invitation_list_settings.options.normal')" value="normal" />
        </el-select>
      </el-form-item>
    </el-form>

    <!-- 参考Info.vue的按钮样式 -->
    <div class="op-box">
      <el-button @click="handleCancel">{{ $t('Activity.invitation_list_settings.buttons.cancel') }}</el-button>
      <el-button 
        type="primary" 
        @click="handleNext"
        :disabled="!isFormValid"
      > 
        {{ $t('Activity.invitation_list_settings.buttons.next_step') }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineExpose } from 'vue'
import { useI18n } from 'vue-i18n'
import type { FormInstance } from 'element-plus'
import type { InvitationForm } from './types'
import { Tickets } from '@element-plus/icons-vue'

const { t } = useI18n()

// 移除defineOptions，使用标准的export default
// defineOptions({
//   name: 'InvitationListSettings'
// })

const props = defineProps<{
  modelValue: InvitationForm
}>()

const emit = defineEmits<{
  'update:modelValue': [value: InvitationForm]
  'next-step': []
  'cancel': []
}>()

const formRef = ref<FormInstance>()

// 响应式表单数据
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单验证状态
const isFormValid = computed(() => {
  return formData.value.title?.trim() !== '' && 
         formData.value.description?.trim() !== ''
})

// 验证表单
const validate = async () => {
  return await formRef.value?.validate()
}

// 处理下一步
const handleNext = async () => {
  const isValid = await validate()
  if (isValid) {
    emit('next-step')
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}

defineExpose({
  validate
})
</script>

<style scoped>
.title {
  font-size: 22px;
  display: flex;
  align-items: center;
  margin: 0px 0px 15px 0px;
}

.invitation-form {
  max-width: 800px;
}

/* 参考Info.vue的按钮样式 */
.op-box {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding-bottom: 30px;
  margin-top: 30px;
}
</style> 