<template>
  <div class="bwms-module">
    <div class="module-header">
      <el-button @click="handleReset">重置</el-button>
      <el-button type="primary" @click="handleCreate">
        新建邀请列表
      </el-button>
    </div>
    <div class="module-con">
      <div class="box">
        <!-- 数据表格 -->
        <el-table
          v-loading="loading"
          :data="invitationList"
          style="width: 100%"
        >
          <template #empty>
            <el-empty description="暂无数据" image-size="100px" />
          </template>
          <el-table-column prop="name" label="列表名" />
          <el-table-column prop="email" label="邮箱" width="200" />
          <el-table-column prop="member_count" label="成员数" width="120" />
          <el-table-column prop="mailbox_count" label="邮箱数" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180" />          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" :icon="Edit" circle size="small" @click="handleEdit(row)"></el-button>
              <el-button type="danger" :icon="Delete" circle size="small" @click="handleDelete(row)"></el-button>
              <el-button type="info" :icon="Document" circle size="small" @click="handleView(row)"></el-button>
              <el-button type="info" :icon="Share" circle size="small" @click="handleShare(row)"></el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="queryParams.page"
            v-model:page-size="queryParams.per_page"
            :total="total"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
import type { InvitationItem, InvitationQuery } from './types'
import { Edit, Delete, Document, Share } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 查询参数
const queryParams = reactive<InvitationQuery>({
  page: 1,
  per_page: 10,
  keyword: ''
})

const loading = ref(false)
const total = ref(0)
const invitationList = ref<InvitationItem[]>([])

// 状态处理函数
const getStatusType = (status: string) => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'accepted':
      return 'success'
    case 'rejected':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '待处理'
    case 'accepted':
      return '已接受'
    case 'rejected':
      return '已拒绝'
    default:
      return '未知'
  }
}

// 获取列表数据
const getList = async () => {
  try {
    loading.value = true
    
    // 实际使用时，请替换为真实的API调用
    // import { invitationService } from '../../services/invitationService'
    // const { data } = await invitationService.getList(queryParams)
    
    // 临时模拟数据，确保是数组格式
    const mockData = {
      code: 200,
      data: {
        items: [
          { 
            id: 1, 
            name: '邀请列表1', 
            email: '<EMAIL>',
            status: 'pending' as const,
            created_at: '2024-01-01 10:00:00',
            updated_at: '2024-01-01 10:00:00',
            member_count: 10, 
            mailbox_count: 8 
          },
          { 
            id: 2, 
            name: '邀请列表2', 
            email: '<EMAIL>',
            status: 'accepted' as const,
            created_at: '2024-01-02 10:00:00',
            updated_at: '2024-01-02 10:00:00',
            member_count: 15, 
            mailbox_count: 12 
          }
        ],
        total: 2
      }
    }
    
    if (mockData.code === 200) {
      // 确保 invitationList 接收的是数组
      invitationList.value = Array.isArray(mockData.data.items) ? mockData.data.items : []
      total.value = mockData.data.total || 0
    } else {
      // 如果API返回错误，确保设置为空数组
      invitationList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取邀请列表失败:', error)
    ElMessage.error('获取邀请列表失败')
    // 发生错误时也要确保设置为空数组
    invitationList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 重置
const handleReset = () => {
  queryParams.keyword = ''
  queryParams.page = 1
  getList()
}

// 新建
const handleCreate = () => {
  router.push({
    name: 'InvitationCreate'
  })
}

// 编辑
const handleEdit = (row: InvitationItem) => {
  router.push({
    path: '/activity/invitation/edit',
    query: {
      id: row.id
    }
  })
}

// 详情
const handleView = (row: InvitationItem) => {
  router.push({
    path: '/activity/invitation/details',
    query: {
      id: row.id
    }
  })
}

// 复制
const handleCopy = async (row: InvitationItem) => {
  try {
    // TODO: 实现复制逻辑
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 分享
const handleShare = (row: InvitationItem) => {
  // TODO: 实现分享逻辑
  ElMessage.info('分享功能待实现')
}

// 删除
const handleDelete = (row: InvitationItem) => {
  ElMessageBox.confirm(
    '确认删除该邀请列表吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // TODO: 实现删除逻辑
      // await invitationService.delete(row.id)
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

// 分页
const handleSizeChange = (val: number) => {
  queryParams.per_page = val
  queryParams.page = 1 // 重置到第一页
  getList()
}

const handleCurrentChange = (val: number) => {
  queryParams.page = val
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.bwms-module{
  margin-top: 10px;
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}
.invitation-list {
  padding: 20px;
}

.search-wrapper {
  padding: 16px 0;
  display: flex;
  align-items: center;
}

.search-input {
  width: 300px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 