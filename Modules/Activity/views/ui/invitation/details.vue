<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <div class="btn-list">
        <el-button @click="handleBack">{{ $t('Activity.invitation_details.buttons.back') }}</el-button>
      </div>
    </div>
    <div class="module-con">
      <div class="box">
        <div class="details-container" v-loading="loading">
          <!-- 过滤表单部分 -->
          <el-card class="filter-card">
            <el-form label-position="top" :model="filterForm" label-width="120px">
              <el-form-item :label="$t('Activity.invitation_details.form.list_name')">
                <el-input v-model="filterForm.listName" disabled />
              </el-form-item>
              <el-form-item :label="$t('Activity.invitation_details.form.description')">
                <el-input v-model="filterForm.description" disabled />
              </el-form-item> 
              <el-form-item :label="$t('Activity.invitation_details.form.is_ngo')">
                <el-radio-group v-model="filterForm.isNGO" disabled>
                  <el-radio :label="true">{{ $t('Activity.invitation_details.options.yes') }}</el-radio>
                  <el-radio :label="false">{{ $t('Activity.invitation_details.options.no') }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('Activity.invitation_details.form.is_member_group')">
                <el-radio-group v-model="filterForm.isMemberGroup" disabled>
                  <el-radio :label="true">{{ $t('Activity.invitation_details.options.yes') }}</el-radio>
                  <el-radio :label="false">{{ $t('Activity.invitation_details.options.no') }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('Activity.invitation_details.form.group_type')">
                <el-input v-model="filterForm.groupType" disabled />
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 表格部分 -->
          <el-card class="table-card">
            <div class="table-header">
              <div class="title">
                {{ $t('Activity.invitation_details.table.title') }}
                <span class="member-count" v-if="searchText.trim()"> ({{ $t('Activity.invitation_details.table.search_result') }}: {{ filteredTableData.length }} / {{ tableData.length }}) </span>
                <span class="member-count" v-else> ({{ $t('Activity.invitation_details.table.total_members', { count: tableData.length }) }}) </span>
              </div>
              <div class="actions">
                <el-input v-model="searchText" :placeholder="$t('Activity.invitation_details.table.search_placeholder')" style="width: 300px" clearable>
                  <template #suffix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <!-- <el-button type="primary">移除</el-button> -->
                <!-- <el-button type="primary">轉移至</el-button> -->
              </div>
            </div>

            <el-table :data="filteredTableData" style="width: 100%" @selection-change="handleSelectionChange" v-loading="loading">
              <template #empty>
                <el-empty :description="searchText.trim() ? $t('Activity.invitation_details.table.no_match') : $t('Activity.invitation_details.table.no_data')" />
              </template>
              <el-table-column type="selection" width="55" />
              <el-table-column prop="name" :label="$t('Activity.invitation_details.table.columns.name')" />
              <el-table-column prop="company" :label="$t('Activity.invitation_details.table.columns.company')" />
              <el-table-column prop="position" :label="$t('Activity.invitation_details.table.columns.position')" />
              <el-table-column prop="phone" :label="$t('Activity.invitation_details.table.columns.phone')" />
              <el-table-column prop="email" :label="$t('Activity.invitation_details.table.columns.email')" />
              <el-table-column prop="status" :label="$t('Activity.invitation_details.table.columns.status')" />
            </el-table>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { invitationService } from '../../services/invitationService'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

interface InvitationDetail {
  id: number
  activity_id: number
  title: string
  description: string
  is_default: number
  is_group: number
  group_id: number | null
  status: number
  creator_id: number
  created_at: string
  updated_at: string
}

interface MemberItem {
  id?: number
  name: string
  company: string
  position: string
  phone: string
  email: string
  status: string
}

const filterForm = ref({
  listName: '',
  description: '',
  isNGO: false,
  isMemberGroup: false,
  groupType: '',
})

const searchText = ref('')
const tableData = ref<MemberItem[]>([])
const loading = ref(false)

// 计算属性：根据搜索条件筛选成员列表
const filteredTableData = computed(() => {
  if (!searchText.value.trim()) {
    return tableData.value
  }

  const searchKeyword = searchText.value.toLowerCase().trim()

  return tableData.value.filter(item => {
    // 搜索名字、公司、职位、电话、邮箱（移除状态筛选）
    return (
      (item.name || '').toLowerCase().includes(searchKeyword) ||
      (item.company || '').toLowerCase().includes(searchKeyword) ||
      (item.position || '').toLowerCase().includes(searchKeyword) ||
      (item.phone || '').toLowerCase().includes(searchKeyword) ||
      (item.email || '').toLowerCase().includes(searchKeyword)
    )
  })
})

const handleBack = () => {
  router.back()
}

// 获取详情数据
const getInvitationDetail = async () => {
  const id = route.query.id
  if (!id) {
    ElMessage.error(t('Activity.invitation_details.messages.missing_params'))
    return
  }

  loading.value = true
  try {
    const res = await invitationService.getDetail(id as string)
    if (res.data?.code === 200 && res.data?.data) {
      const detail = res.data.data as InvitationDetail
      filterForm.value = {
        listName: detail.title || '',
        description: detail.description || '',
        isNGO: detail.is_group === 1,
        isMemberGroup: detail.is_default === 1,
        groupType: detail.group_id ? t('Activity.invitation_details.options.group') : t('Activity.invitation_details.options.single'),
      }

      // 获取成员列表
      const memberRes = await invitationService.getMemberList(detail.id)
      if (memberRes.data?.code === 200 && memberRes.data?.data) {
        tableData.value = memberRes.data.data.items || []
      }
    } else {
      ElMessage.error(t('Activity.invitation_details.messages.get_detail_failed'))
    }
  } catch (error) {
    console.error('获取邀请详情失败:', error)
      ElMessage.error(t('Activity.invitation_details.messages.get_invitation_failed'))
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (val: any[]) => {
  console.log('selected', val)
}

onMounted(() => {
  getInvitationDetail()
})
</script>

<style scoped>
.box {
  overflow-y: scroll;
}
.details-container {
  padding-top: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-header .title {
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
}

.member-count {
  font-size: 14px;
  color: #909399;
  font-weight: normal;
}

.table-header .actions {
  display: flex;
  gap: 10px;
  align-items: center;
}
</style>
