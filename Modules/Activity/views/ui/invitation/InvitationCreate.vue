<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <div class="btn-list">
        <el-button @click="goBack">{{ $t('Activity.invitation_create.buttons.back') }}</el-button>
      </div>
    </div>
    <div class="module-con">
      <div class="box">
        <el-tabs v-model="activeTab">
          <el-tab-pane :label="$t('Activity.invitation_create.tabs.list_settings')" name="list">
            <InvitationListSettings 
              ref="listSettingsRef"
              v-model="formData"
              @next-step="handleNext"
              @cancel="handleCancel"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('Activity.invitation_create.tabs.member_list')" name="members" :disabled="!isStepCompleted('list')">
            <InvitationMemberList 
              ref="memberListRef"
              :activity-id="activityId"
              :invitation-id="invitationId"
              :is-edit="isEdit"
              :current-page="pagination.page"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              @update:memberList="handleMemberListUpdate"
              @prev-step="handlePrevStep"
              @cancel="handleCancel"
              @submit="handleSubmit"
              @page-change="handlePageChange"
              @size-change="handleSizeChange"
            />
          </el-tab-pane>
        </el-tabs>
     
      </div>

      <!-- 分页 - 只在成员列表tab时显示 -->
      <div class="box-footer" v-if="activeTab === 'members'">
        <div class="pagination-container table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Activity.invitation_create.pagination.total', { count: pagination.total }) }}</span>
            <el-select v-model="pagination.pageSize" class="page-size-select" @change="handleSizeChange" size="default">
              <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="$t('Activity.invitation_create.pagination.per_page', { size })" :value="size"
                class="page-size-option" />
              <template #empty>
                <div style="text-align: center; padding: 8px 0;">
                  {{ $t('Activity.invitation_create.pagination.no_data') }}
                </div>
              </template>
            </el-select>
          </div>
          <div class="pagination-right">
            <el-pagination 
              v-model:current-page="pagination.page" 
              background 
              layout="prev, pager, next"
              :page-size="pagination.pageSize" 
              :total="pagination.total" 
              @current-change="handlePageChange" 
            />
            <span style="margin-left: 24px;">{{ $t('Activity.invitation_create.pagination.go_to') }}</span>
            <el-input 
              :model-value="pagination.page" 
              @change="handleGoToPage"
              style="width: 60px; margin-left: 8px;"
              size="small"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type { InvitationForm, InvitationMember, InvitationDetail } from './types'
import InvitationListSettings from './InvitationListSettings.vue'
import InvitationMemberList from './InvitationMemberList.vue'
import { invitationService } from '../../services/invitationService'

const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const activeTab = ref('list')

// 判断是否为编辑模式 - 通过路由名称或路由参数判断
const isEdit = computed(() => route.name === 'InvitationEdit' || !!route.params.id)
const invitationId = computed(() => route.params.id as string)
const activityId = computed(() => route.query.activityId as string)

// 子组件引用
const listSettingsRef = ref()
const memberListRef = ref()

const goBack = () => {
  router.back()
}

// 表单数据
const formData = reactive<InvitationForm>({
  title: '',
  description: '',
  is_default: false,
  is_group: false,
  group_type: 'vip',
})

// 成员列表数据
const memberList = ref<InvitationMember[]>([])

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 记录已完成的步骤
const completedSteps = ref<Set<string>>(new Set())

// 检查步骤是否完成
const isStepCompleted = (stepName: string): boolean => {
  return completedSteps.value.has(stepName)
}

// 标记步骤为完成
const markStepCompleted = (stepName: string) => {
  completedSteps.value.add(stepName)
}

// 处理成员列表更新
const handleMemberListUpdate = (members: InvitationMember[]) => {
  memberList.value = members
}

// 分页处理方法
const handlePageChange = (page: number) => {
  pagination.page = page
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
}

const handleGoToPage = (page: string | number) => {
  const pageNum = typeof page === 'string' ? parseInt(page) : page
  if (pageNum && pageNum > 0 && pageNum <= Math.ceil(pagination.total / pagination.pageSize)) {
    pagination.page = pageNum
  }
}

// 获取邀请详情（编辑模式）
const fetchInvitationDetail = async () => {
  if (!isEdit.value || !invitationId.value) return

  try {
    const res = await invitationService.getDetail(invitationId.value)
    if (res.data && res.data.code === 200 && res.data.data) {
      const detail: InvitationDetail = res.data.data

      // 填充表单数据
      formData.title = detail.title
      formData.description = detail.description
      formData.is_default = !!detail.is_default
      formData.is_group = !!detail.is_group
      formData.group_type = detail.group_id ? 'vip' : 'normal' // 根据实际需求调整

      // 填充成员数据（如果有）
      if (detail.members) {
        memberList.value = detail.members
        // 初始化成员列表组件
        memberListRef.value?.initMemberList(detail.members)
      }

      // 编辑模式下，如果已有数据则标记第一步为完成
      if (detail.title && detail.description) {
        markStepCompleted('list')
      }
    }
  } catch (error) {
    console.error('获取邀请详情失败:', error)
    ElMessage.error(t('Activity.invitation_create.messages.get_invitation_failed'))
  }
}

// 下一步处理 - 直接切换到下一个tab
const handleNext = () => {
  // 标记当前步骤为完成
  markStepCompleted(activeTab.value)
  
  // 切换到下一步
  if (activeTab.value === 'list') {
    activeTab.value = 'members'
  }
}

// 上一步处理
const handlePrevStep = () => {
  if (activeTab.value === 'members') {
    activeTab.value = 'list'
  }
}

// 验证所有表单
const validateAllForms = async (): Promise<boolean> => {
  try {
    // 验证列表设置表单
    if (listSettingsRef.value?.validate) {
      const listValid = await listSettingsRef.value.validate()
      if (!listValid) {
        activeTab.value = 'list'
        ElMessage.error(t('Activity.invitation_create.messages.list_settings_incomplete'))
        return false
      }
    }

    // 成员列表不需要强制验证，可以为空
    
    return true
  } catch (error) {
    console.error('Form validation error:', error)
    return false
  }
}

// 提交处理
const handleSubmit = async () => {
  console.log('handleSubmit 开始')
  try {
    // 验证所有表单数据
    const isValid = await validateAllForms()
    if (!isValid) {
      ElMessage.error(t('Activity.invitation_create.messages.check_form_data'))
      return
    }

    // 获取当前成员列表
    const currentMembers = memberListRef.value?.getMemberList() || []
    console.log('获取到的成员数据:', currentMembers)

    const submitData = {
      activity_id: activityId.value,
      title: formData.title,
      description: formData.description,
      is_default: formData.is_default ? 1 : 0,
      is_group: formData.is_group ? 1 : 0,
      group_type: formData.group_type,
      members: currentMembers
    }

    console.log('提交的数据:', submitData)

    if (isEdit.value) {
      // 编辑模式
      console.log('编辑模式，邀请ID:', invitationId.value)
      await invitationService.update(invitationId.value, submitData)
      ElMessage.success(t('Activity.invitation_create.messages.update_success'))
    } else {
      // 创建模式
      console.log('创建模式')
      await invitationService.create(submitData)
      ElMessage.success(t('Activity.invitation_create.messages.create_success'))
    }

    // 返回列表页
    router.push({
      path: '/activity/detail/' + activityId.value,
      query: { tab: 'invitations' },
    })
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(isEdit.value ? t('Activity.invitation_create.messages.update_failed') : t('Activity.invitation_create.messages.create_failed'))
  }
}

const handleCancel = () => {
  router.back()
}

// 初始化
onMounted(() => {
  if (!activityId.value) {
    ElMessage.error(t('Activity.invitation_create.messages.missing_activity_id'))
    router.back()
    return
  }

  if (isEdit.value) {
    fetchInvitationDetail()
  }
})
</script>

<style scoped>
/* 参考EventCreate.vue的禁用tab样式 */
:deep(.el-tabs__nav-wrap .el-tabs__item.is-disabled) {
  cursor: not-allowed;
  opacity: 0.6;
}

:deep(.el-tabs__nav-wrap .el-tabs__item.is-disabled:hover) {
  color: inherit;
}

/* 分页样式 - 参考EventList.vue */
.box-footer {
  border-top: 1px solid #ebeef5;
  padding: 16px 20px;
  border-radius: 0 0 4px 4px;
}

.pagination-container.table-pagination-style {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: #5d5d5d;
}

.pagination-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-size-text {
  font-size: 14px;
  color: #606266;
}

.page-size-select {
  width: 80px;
  height: 38px;
}

:deep(.page-size-select .el-input__wrapper) {
  height: 38px;
  line-height: 38px;
  box-shadow: 0 0 0 1px #e4e4e4 inset;
  padding: 1px 11px;
}

:deep(.page-size-select .el-input__inner) {
  text-align: center;
  font-size: 14px;
}


</style>
