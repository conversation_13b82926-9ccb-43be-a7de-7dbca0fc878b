<template>
  <div class="bwms-module">
    <!-- 页面标题 -->
    <div class="module-header">
      <el-button type="primary" @click="handleCreate">
        新增邮件模版
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 搜索和操作区 -->
        <div class="table-header">
          <div class="left">
            <el-input
              v-model="searchQuery"
              placeholder="搜索模板名称"
              class="search-input"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button class="filter-button">
              <el-icon><Filter /></el-icon>
            </el-button>
          </div>
          <div class="right">
            <el-button @click="handleBatchCopy" :disabled="!selectedRows.length">批量复制</el-button>
          </div>
        </div>

        <!-- 表格 -->
        <el-table
          v-loading="loading"
          :data="tableData"
          @selection-change="handleSelectionChange"
        >
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column type="selection" width="55" />
          <el-table-column 
            prop="name" 
            label="模版名称" 
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="scene" 
            label="使用场景" 
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="updateTime" 
            label="最后修改时间" 
            min-width="180"
            :formatter="formatDateTime"
          />
          <el-table-column width="200" fixed="right">
            <template #default="{ row }">
              <el-link type="primary" @click="handleEdit(row)">编辑</el-link>
              <el-link type="danger" class="ml-2" @click="handleDelete(row)">删除</el-link>
              <el-switch
                v-model="row.status"
                class="ml-2"
                :active-value="1"
                :inactive-value="0"
                @change="() => handleStatusChange(row)"
              />
              <el-dropdown trigger="click" class="ml-2">
                <el-icon><More /></el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleCopy(row)">复制</el-dropdown-item>
                    <el-dropdown-item @click="handlePreview(row)">预览</el-dropdown-item>
                    <el-dropdown-item @click="handleSendTest(row)">发送测试</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 测试发送对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="发送测试邮件"
      width="30%"
    >
      <el-form :model="testForm" label-width="80px">
        <el-form-item label="接收邮箱">
          <el-input v-model="testForm.email" placeholder="请输入接收测试邮件的邮箱" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="testDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="sendTestEmail">发送</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="邮件模板预览"
      width="60%"
    >
      <div class="preview-container">
        <div class="preview-subject">
          <h3>{{ previewData.subject }}</h3>
        </div>
        <div class="preview-content" v-html="previewData.content"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Filter, More } from '@element-plus/icons-vue'
import { emailTemplateService } from '../../services/emailTemplateService'
import type { IEmailTemplate } from '../../types'

const router = useRouter()
const loading = ref(false)
const searchQuery = ref('')
const tableData = ref<IEmailTemplate[]>([])
const selectedRows = ref<IEmailTemplate[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 测试邮件相关
const testDialogVisible = ref(false)
const testForm = ref({
  email: '',
  templateId: ''
})

// 预览相关
const previewDialogVisible = ref(false)
const previewData = ref({
  subject: '',
  content: ''
})

// 格式化日期时间
const formatDateTime = (row: IEmailTemplate) => {
  if (!row.updateTime) return ''
  return new Date(row.updateTime).toLocaleString()
}

// 添加防抖搜索
let searchTimer: number | null = null
const handleSearch = () => {
  if (searchTimer) clearTimeout(searchTimer)
  searchTimer = window.setTimeout(() => {
    currentPage.value = 1
    fetchData()
  }, 300)
}

// 获取列表数据
const fetchData = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      keyword: searchQuery.value
    }
    const { data } = await emailTemplateService.getList(params)
    tableData.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 创建模版
const handleCreate = () => {
  router.push({ name: 'EmailTemplateCreate' })
}

// 编辑模版
const handleEdit = (row: IEmailTemplate) => {
  router.push({ 
    name: 'EmailTemplateEdit',
    params: { id: row.id }
  })
}

// 复制模版
const handleCopy = async (row: IEmailTemplate) => {
  try {
    await emailTemplateService.copy(row.id)
    ElMessage.success('复制成功')
    fetchData()
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 批量复制
const handleBatchCopy = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择需要复制的邮件模板')
    return
  }
  
  try {
    const ids = selectedRows.value.map(row => row.id)
    await emailTemplateService.batchCopy(ids)
    ElMessage.success('批量复制成功')
    fetchData()
  } catch (error) {
    console.error('批量复制失败:', error)
    ElMessage.error('批量复制失败')
  }
}

// 删除模版
const handleDelete = (row: IEmailTemplate) => {
  ElMessageBox.confirm(
    '确定要删除该邮件模版吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await emailTemplateService.delete(row.id)
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 状态变更
const handleStatusChange = async (row: IEmailTemplate) => {
  try {
    await emailTemplateService.updateStatus(row.id, row.status)
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
    // 恢复状态
    row.status = row.status === 1 ? 0 : 1
  }
}

// 预览模板
const handlePreview = async (row: IEmailTemplate) => {
  try {
    const { data } = await emailTemplateService.preview(row.id)
    previewData.value = {
      subject: data.subject,
      content: data.content
    }
    previewDialogVisible.value = true
  } catch (error) {
    console.error('预览失败:', error)
    ElMessage.error('预览失败')
  }
}

// 发送测试邮件
const handleSendTest = (row: IEmailTemplate) => {
  testForm.value.templateId = row.id as string
  testDialogVisible.value = true
}

// 发送测试邮件
const sendTestEmail = async () => {
  if (!testForm.value.email) {
    ElMessage.warning('请输入接收邮箱')
    return
  }
  
  try {
    await emailTemplateService.sendTest(
      testForm.value.templateId, 
      testForm.value.email
    )
    ElMessage.success('测试邮件发送成功')
    testDialogVisible.value = false
  } catch (error) {
    console.error('发送失败:', error)
    ElMessage.error('发送失败')
  }
}

// 选择变更
const handleSelectionChange = (rows: IEmailTemplate[]) => {
  selectedRows.value = rows
}

// 分页大小变更
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchData()
}

// 页码变更
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchData()
}

// 初始化
onMounted(() => {
  // fetchData()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-header {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 16px;
  }

  .module-con {
    margin-top: 16px;
    
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;

      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .left {
          display: flex;
          align-items: center;
          gap: 12px;

          .search-input {
            width: 240px;
          }

          .filter-button {
            padding: 8px;
          }
        }

        .right {
          display: flex;
          gap: 12px;
        }
      }

      .el-link {
        font-size: 14px;
        
        & + .el-link {
          margin-left: 16px;
        }
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
}

.preview-container {
  padding: 20px;
  border: 1px solid #eaeaea;
  border-radius: 4px;
  
  .preview-subject {
    border-bottom: 1px solid #eaeaea;
    padding-bottom: 10px;
    margin-bottom: 15px;
    
    h3 {
      margin: 0;
    }
  }
  
  .preview-content {
    min-height: 300px;
  }
}

.ml-2 {
  margin-left: 8px;
}
</style> 