<template>
  <div class="bwms-module">

    <!-- 页面标题 -->
    <div class="module-header">
      
      <div class="actions">
        <el-dropdown trigger="click" @command="handleOperation">
          <el-button>
            操作 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="preview">预览</el-dropdown-item>
              <el-dropdown-item command="send_test">发送测试</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button type="primary" @click="submitForm(formRef)">保存</el-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="module-con">
      <div class="box">
        <el-form 
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="template-form"
          v-loading="loading"
        >
          <!-- 邮件标题 -->
          <el-form-item label="邮件标题" prop="subject">
            <el-input v-model="form.subject" placeholder="请输入邮件标题" />
          </el-form-item>
          
          <!-- 邮件正文 -->
          <el-form-item label="邮件正文" prop="content">
            <Editor editType="tinyMce" v-model="form.content" />
          </el-form-item>
          
          <!-- 隐藏字段，保存但不显示在表单中 -->
          <div class="hidden-fields" style="display: none;">
            <el-form-item label="模板名称" prop="name">
              <el-input v-model="form.name" />
            </el-form-item>
            <el-form-item label="使用场景" prop="scene">
              <el-select v-model="form.scene">
                <el-option label="收集反馈" value="feedback_collection" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="邮件模板预览"
      width="60%"
      z-index='9999'
    >
      <div class="preview-container">
        <div class="preview-subject">
          <h3>{{ form.subject }}</h3>
        </div>
        <div class="preview-content" v-html="form.content"></div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 测试发送对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="发送测试邮件"
      width="30%"
      z-index='9999'
    >
      <el-form :model="testForm" label-width="80px">
        <el-form-item label="接收邮箱">
          <el-input v-model="testForm.email" placeholder="请输入接收测试邮件的邮箱" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="testDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="sendTestEmail">发送</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import { emailTemplateService } from '../../services/emailTemplateService'
import Editor from '@/module/Editor/views/index.vue'

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()
const loading = ref(false)

// 判断是否是编辑模式
const isEdit = computed(() => {
  return route.name === 'EmailTemplateEdit'
})

// 获取模板ID
const templateId = computed(() => {
  return route.params.id as string
})

// 预览相关
const previewDialogVisible = ref(false)

// 测试邮件相关
const testDialogVisible = ref(false)
const testForm = ref({
  email: '',
  templateId: ''
})

// 表单数据
const form = reactive({
  name: '',
  scene: 'feedback_collection',  // 默认为收集反馈
  subject: '',
  content: '',
  status: 1  // 默认启用
})

// 表单验证规则
const rules = reactive<FormRules>({
  subject: [
    { required: true, message: '请输入邮件标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入邮件正文', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  scene: [
    { required: true, message: '请选择使用场景', trigger: 'change' }
  ]
})

// 处理操作
const handleOperation = (command: string) => {
  if (command === 'preview') {
    previewTemplate()
  } else if (command === 'send_test') {
    testForm.value.templateId = templateId.value
    testDialogVisible.value = true
  }
}

// 预览模板
const previewTemplate = () => {
  if (!form.subject || !form.content) {
    ElMessage.warning('请先填写邮件主题和内容')
    return
  }
  previewDialogVisible.value = true
}

// 发送测试邮件
const sendTestEmail = async () => {
  if (!testForm.value.email) {
    ElMessage.warning('请输入接收邮箱')
    return
  }
  
  try {
    if (isEdit.value) {
      // 如果是编辑模式，直接发送测试邮件
      await emailTemplateService.sendTest(
        templateId.value, 
        testForm.value.email
      )
      ElMessage.success('测试邮件发送成功')
    } else {
      // 如果是创建模式，先临时保存，再发送测试邮件
      ElMessage.warning('请先保存模板后再发送测试邮件')
    }
    testDialogVisible.value = false
  } catch (error) {
    console.error('发送失败:', error)
    ElMessage.error('发送失败')
  }
}

// 获取模板详情
const fetchTemplateDetail = async () => {
  if (!isEdit.value) return
  
  try {
    loading.value = true
    const { data } = await emailTemplateService.getDetail(templateId.value)
    
    // 更新表单数据
    Object.assign(form, {
      name: data.name,
      scene: data.scene,
      subject: data.subject,
      content: data.content,
      status: data.status
    })
  } catch (error) {
    console.error('获取模板详情失败:', error)
    ElMessage.error('获取模板详情失败')
    router.push({ name: 'EmailTemplateList' })
  } finally {
    loading.value = false
  }
}

// 提交表单
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  // 如果是创建模式且未填写名称，则使用标题作为名称
  if (!isEdit.value && !form.name && form.subject) {
    form.name = form.subject
  }
  
  await formEl.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        
        if (isEdit.value) {
          // 编辑模式
          await emailTemplateService.update(templateId.value, form)
          ElMessage.success('更新邮件模板成功')
        } else {
          // 创建模式
          await emailTemplateService.create(form)
          ElMessage.success('创建邮件模板成功')
        }
        
        router.push({ name: 'EmailTemplateList' })
      } catch (error) {
        console.error(isEdit.value ? '更新失败:' : '创建失败:', error)
        ElMessage.error(isEdit.value ? '更新邮件模板失败' : '创建邮件模板失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 初始化
onMounted(() => {
  if (isEdit.value) {
    fetchTemplateDetail()
  }
})
</script>

<style lang="scss" scoped>
.bwms-module {


  .actions {
      display: flex;
      gap: 12px;
    }

  .module-con {
    margin-top: 16px;
    
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;
    }
  }

  .template-form {
    width: 100%;
  }
}

.preview-container {
  padding: 20px;
  border: 1px solid #eaeaea;
  border-radius: 4px;
  
  .preview-subject {
    border-bottom: 1px solid #eaeaea;
    padding-bottom: 10px;
    margin-bottom: 15px;
    
    h3 {
      margin: 0;
    }
  }
  
  .preview-content {
    min-height: 300px;
  }
}
</style> 