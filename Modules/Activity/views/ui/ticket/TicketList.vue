<template>
  <div class="ticket-list-container">
    <el-table :data="ticketList" style="width: 100%;" v-loading="loading">
      <template #empty>
        <el-empty :description="$t('Activity.ticket_list.empty.description')" image-size="100px" />
      </template>
      <el-table-column :label="$t('Activity.ticket_list.table.name')" min-width="300">
        <template #default="scope">
          {{ scope.row.ticket_type?.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('Activity.ticket_list.table.code')" width="300">
        <template #default="scope">
          {{ scope.row.ticket_type?.code || '-' }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('Activity.ticket_list.table.quota')" width="200">
        <template #default="scope">
          {{ scope.row.quota || '-' }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('Activity.ticket_list.table.remaining_quota')" width="200">
        <template #default="scope">
          {{ scope.row.remaining_quota ?? '-' }}
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('Activity.ticket_list.table.subtypes_count')" width="120">
        <template #default="scope">
          {{ scope.row.subtypes?.length || 0 }}
        </template>
      </el-table-column> -->
      <!-- <el-table-column :label="$t('Activity.ticket_list.table.external_sale')" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.ticket_type?.is_external_sale ? 'success' : 'info'">
            {{ scope.row.ticket_type?.is_external_sale ? $t('Activity.ticket_list.status.yes') : $t('Activity.ticket_list.status.no') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Activity.ticket_list.table.fee_required')" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.ticket_type?.is_fee_required ? 'warning' : 'info'">
            {{ scope.row.ticket_type?.is_fee_required ? $t('Activity.ticket_list.status.yes') : $t('Activity.ticket_list.status.no') }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column prop="created_at" :label="$t('Activity.ticket_list.table.created_at')" width="200"></el-table-column>
      <el-table-column fixed="right" :label="$t('Activity.ticket_list.table.actions')" width="300">
        <template #default="scope">
          <div class="operate-btn-box">
            <div class="operate-btn" @click="handleEdit(scope.row)">
              <el-icon>
                <img :src="iconPaths.edit" alt="" />
              </el-icon>
            </div>
            <el-button class="operate-btn del-btn" type="text" @click="handleDelete(scope.row)">
              <el-icon>
                <img :src="iconPaths.delete" alt="" />
              </el-icon>
            </el-button>
            <el-button class="operate-btn" type="text" @click="handleView(scope.row)">
              <el-icon>
                <CopyDocument />
              </el-icon>
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 删除确认弹窗 -->
    <el-dialog
      v-model="deleteDialogVisible"
      :title="$t('Activity.ticket_list.delete_dialog.title')"
      class="el-dialog-common-cls"
      width="400px"
    >
      <div class="delete-content">
        <p>{{ $t('Activity.ticket_list.delete_dialog.content', { name: currentDeleteItem?.ticket_type?.name }) }}</p>
        <p class="warning-text">{{ $t('Activity.ticket_list.delete_dialog.warning') }}</p>
      </div>
      <template #footer>
        <div class="flex justify-center">
          <el-button class="el-button-default" @click="deleteDialogVisible = false">
            {{ $t('Activity.ticket_list.delete_dialog.cancel') }}
          </el-button>
          <el-button class="button-no-border" type="danger" @click="confirmDelete" :loading="deleteLoading">
            {{ deleteLoading ? $t('Activity.ticket_list.delete_dialog.deleting') : $t('Activity.ticket_list.delete_dialog.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { CopyDocument } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ticketService } from "../../services/ticketService"
import { useCommonIcons } from "../../composables/useAsset"

const { t } = useI18n()

// Props - 接收活动ID和分页信息
interface Props {
  activityId?: string | number
  pagination?: {
    page: number
    pageSize: number
    total: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  activityId: '',
  pagination: () => ({ page: 1, pageSize: 10, total: 0 })
})

// Emits - 发送事件
const emit = defineEmits<{
  'update-total': [total: number]
}>()

// 获取常用图标
const icons = useCommonIcons()

// 图标路径计算属性
const iconPaths = computed(() => ({
  edit: icons.edit,   
  delete: icons.delete
}))

// 定义接口类型
interface TicketSubtype {
  name: string
  price: string
  start_date?: string
  end_date?: string
}

interface TicketType {
  id: number
  name: string
  code: string
  is_external_sale: number
  is_fee_required: number
}

interface TicketItem {
  id: number
  activity_id: number
  ticket_type_id: number
  quota: number
  remaining_quota: number | null
  subtypes: TicketSubtype[]
  sort: number
  creator_id: number
  created_at: string
  updated_at: string
  ticket_type: TicketType
}

// API 响应类型
interface TicketListResponse {
  items: TicketItem[]
  total: number
}

const router = useRouter()
const route = useRoute()

// 表格数据
const ticketList = ref<TicketItem[]>([])
const loading = ref(false)

// 删除相关
const deleteDialogVisible = ref(false)
const deleteLoading = ref(false)
const currentDeleteItem = ref<TicketItem | null>(null)

// 操作方法
const handleEdit = (row: TicketItem) => {
  router.push({
    path: '/activity/settings/ticket/create',
    query: {
      id: row.ticket_type.id.toString(),
      activityId: props.activityId?.toString() || route.params.id
    }
  })
}

// 删除
const handleDelete = (row: TicketItem) => {
  currentDeleteItem.value = row
  deleteDialogVisible.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!currentDeleteItem.value) return
  console.log();
  
  deleteLoading.value = true
  try {
    await ticketService.delete(currentDeleteItem.value.ticket_type_id)
    ElMessage.success(t('Activity.ticket_list.messages.delete_success'))
    deleteDialogVisible.value = false
    currentDeleteItem.value = null
    
    // 重新加载数据
    await getTableDataList()
  } catch (error) {
    ElMessage.error(t('Activity.ticket_list.messages.delete_failed'))
  } finally {
    deleteLoading.value = false
  }
}

// 查看
const handleView = (row: TicketItem) => {
  console.log('查看', row)
  // TODO: 实现查看逻辑
}

// 查询列表
const getTableDataList = async () => {
  loading.value = true
  try {
    const activityId = props.activityId || route.params.id as string
    const params = {
      page: props.pagination?.page || 1,
      per_page: props.pagination?.pageSize || 10
    }
    
    // 使用新的按活动ID获取票务列表的接口
    const res = await ticketService.getListByActivity(activityId, params)
    
    // 根据API返回结构解析数据
    if (res.data && res.data.code === 200 && res.data.data) {
      ticketList.value = res.data.data.items || []
      const total = res.data.data.total || 0
      emit('update-total', total)
    } else {
      // 兼容其他可能的返回结构
      ticketList.value = res.data?.data?.items || res.data?.data || []
      emit('update-total', ticketList.value.length)
    }
  } catch (error) {
    console.error('获取票务列表失败:', error)
    ElMessage.error(t('Activity.ticket_list.messages.get_list_failed'))
  } finally {
    loading.value = false
  }
}

// 暴露刷新方法给父组件
const refresh = () => {
  getTableDataList()
}

// 暴露方法
defineExpose({
  refresh
})

// 监听分页变化
watch(
  () => props.pagination,
  (newPagination, oldPagination) => {
    if (newPagination && oldPagination) {
      if (
        newPagination.page !== oldPagination.page ||
        newPagination.pageSize !== oldPagination.pageSize
      ) {
        getTableDataList()
      }
    }
  },
  { deep: true }
)

onMounted(() => {
  getTableDataList()
})
</script>

<style lang="scss" scoped>
.ticket-list-container {
  width: 100%;
  
  // 操作按钮样式
  .operate-btn-box {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .operate-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #dcdfe6;
    background: #fff;

    &:hover {
      border-color: #409eff;
      background: #ecf5ff;
      
      .el-icon {
        color: #409eff;
      }
    }

    .el-icon {
      font-size: 16px;
      color: #606266;
    }
  }

  .del-btn {
    &:hover {
      border-color: #f56c6c;
      background: #fef0f0;
      
      .el-icon {
        color: #f56c6c;
      }
    }
  }
}

// 删除弹窗样式
.delete-content {
  text-align: center;
  padding: 20px 0;
  
  p {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #303133;
  }
  
  .warning-text {
    font-size: 14px;
    color: #f56c6c;
  }
}

.flex {
  display: flex;
  gap: 12px;
}

.justify-center {
  justify-content: center;
}
</style> 