<template>
  <div class="bwms-module">
    <div class="module-header">
      <!-- 编辑模式时显示顶部操作按钮 -->
     <div class="btn-list">
      <el-button @click="handleGoBack" :disabled="isCreating" style="margin-right: 10px;" >
          <el-icon><ArrowLeft /></el-icon>
          {{ $t('Activity.ticket_create.buttons.back') }}
        </el-button>
      <div v-if="isEditMode" >
        <el-button 
          type="primary" 
          @click="handleUpdate" 
          :loading="isCreating"
          :disabled="isCreating"
        >
          {{ isCreating ? $t('Activity.ticket_create.buttons.updating') : $t('Activity.ticket_create.buttons.update') }}
        </el-button>
     </div>
      </div>
    </div>

   <div class="module-con">
    <div class="box">
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane :label="isEditMode ? $t('Activity.ticket_create.tabs.basic_settings_edit') : $t('Activity.ticket_create.tabs.basic_settings')" name="base" :disabled="true">
          <TicketBase 
            :contentSave='contentSave' 
            :initialData="formData"
            @next="handleNextStep" 
            @update:formData="contentSave"
          />
        </el-tab-pane>
        <el-tab-pane :label="isEditMode ? $t('Activity.ticket_create.tabs.ticket_type_edit') : $t('Activity.ticket_create.tabs.ticket_type')" name="type" :disabled="true">
          <TicketType 
            :contentSave='contentSave' 
            :initialData="formData"
            :isCreating="isCreating"
            :isEditMode="isEditMode"
            @previous="handlePreviousStep" 
            @create="handleCreate" 
            @update:formData="contentSave"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
   </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ticketService } from '../../../services/activityervice'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import TicketBase from "./base.vue"
import TicketType from "./type.vue"

const { t } = useI18n()

// 强制组件注册
const components = {
  TicketBase,
  TicketType
}

const route = useRoute()
const router = useRouter()

const activeName = ref('base')
const formData = ref<any>({})
const isCreating = ref(false)
const isEditMode = ref(false)
const ticketId = ref<number | null>(null)
const activityId = ref<number | null>(null)

// 表单内容，回调
const contentSave = (val: any) => {
  // 使用深度比较避免不必要的更新，防止递归
  const newFormData = { ...formData.value, ...val }
  const hasChanged = JSON.stringify(formData.value) !== JSON.stringify(newFormData)
  if (hasChanged) {
    formData.value = newFormData
  }
}

// 切换到下一步（票務類型）
const handleNextStep = () => {
  activeName.value = 'type'
}

// 切换到上一步（基礎設置）
const handlePreviousStep = () => {
  activeName.value = 'base'
}

// 创建或更新票券
const handleCreate = async () => {
  if (isCreating.value) return
  
  try {
    isCreating.value = true    
    // 整合数据格式
    const requestData = {
      name: formData.value.ticketName, // 来自 base.vue
      activity_id: activityId.value, // 来自 base.vue
      code: formData.value.ticketCode, // 来自 base.vue  
      quota: formData.value.quantityLimit, // 来自 type.vue
      is_external_sale: formData.value.is_external_sale, // 来自 type.vue
      is_fee_required: formData.value.is_fee_required, // 来自 type.vue
      groups: formData.value.groups || [], // 来自 base.vue
      subtypes: (formData.value.subtypes || []).map((subtype: any) => ({
        name: subtype.name,
        price:subtype.price,
        start_date:subtype?.dateRange[0],
        end_date:subtype?.dateRange[1],
      })) // 来自 type.vue，只保留 name 字段
    }
        
    let response
    if (isEditMode.value && ticketId.value) {
      // 调用更新票务接口
      response = await ticketService.update(0, ticketId.value, requestData)
    } else {
      // 调用创建票务接口
      response = await ticketService.create(0, requestData)
    }
    
    if (response?.data?.code === 200) {
      ElMessage.success(isEditMode.value ? t('Activity.ticket_create.messages.update_success') : t('Activity.ticket_create.messages.create_success'))
      // 返回上一页或刷新列表
      router.go(-1)
    } else {
      ElMessage.error(response?.data?.message || (isEditMode.value ? t('Activity.ticket_create.messages.update_failed') : t('Activity.ticket_create.messages.create_failed')))
    }
    
  } catch (error: any) {
    ElMessage.error(error?.response?.data?.message || (isEditMode.value ? t('Activity.ticket_create.messages.update_failed_retry') : t('Activity.ticket_create.messages.create_failed_retry')))
  } finally {
    isCreating.value = false
  }
  isCreating.value = false
}

// 获取票务详情数据
const getTicketDetail = async (id: number, activity_id: number) => {
  try {
    const response = await ticketService.getDetailByActivity(id, activity_id)
    
    if (response?.data?.code === 200) {
      const ticketData = response.data.data
      
      // 根据实际API返回结构回填表单数据
      formData.value = {
        ticketName: ticketData.ticket_type.name,
        ticketCode: ticketData.ticket_type.code,
        quantityLimit: ticketData.quota,
        is_external_sale: ticketData.ticket_type.is_external_sale,
        is_fee_required: ticketData.ticket_type.is_fee_required,
        // 如果需要groups数据，需要后端返回相应字段
        groups: (ticketData.ticket_type.groups || []).map((item: any) => ({
          id: item.group_id
        })),
        // subtypes字段为null时设置为空数组
        subtypes: ticketData.subtypes || []
      }

    } else {
      ElMessage.error(response?.data?.message || t('Activity.ticket_create.messages.get_detail_failed'))
      router.go(-1) // 获取失败则返回列表
    }
  } catch (error: any) {
    ElMessage.error(error?.response?.data?.message || t('Activity.ticket_create.messages.get_detail_failed_retry'))
    router.go(-1) // 获取失败则返回列表
  }
}
// 返回上一页
const handleGoBack = () => {
  router.go(-1)
}

// 更新票券（顶部按钮）
const handleUpdate = async () => {
  // 复用handleCreate方法
  await handleCreate()
}

onMounted(() => {  
  // 检查是否为编辑模式
  const id = route.query.id
  const activity_id = route.query.activityId
  activityId.value = parseInt(activity_id as string)
  if (id) {
    isEditMode.value = true
    ticketId.value = parseInt(id as string)
   
    // 获取票务详情数据
    getTicketDetail(ticketId.value,activityId.value)
  }
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .box {
   
  
  }
}

.form-title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}

.form-title .el-icon {
  margin-right: 8px;
}

.limit-text {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
}

</style>