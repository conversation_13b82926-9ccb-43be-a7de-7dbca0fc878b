<template>
  <div class="form-container">
    <div class="form-title">
      <!-- <el-icon><Ticket /></el-icon> -->
      {{ $t('Activity.ticket_base.title') }}
    </div>

    <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
      <el-form-item :label="$t('Activity.ticket_base.fields.name')" prop="ticketName" required>
        <el-input v-model="form.ticketName" :placeholder="$t('Activity.ticket_base.placeholders.name')" />
      </el-form-item>

      <el-form-item :label="$t('Activity.ticket_base.fields.code')" prop="ticketCode" required>
        <el-input v-model="form.ticketCode" :placeholder="$t('Activity.ticket_base.placeholders.code')" />
      </el-form-item>

      <el-form-item :label="$t('Activity.ticket_base.fields.description')" prop="ticketDescription">
        <el-input 
          v-model="form.ticketDescription" 
          type="textarea" 
          :rows="3" 
          :placeholder="$t('Activity.ticket_base.placeholders.description')"
          maxlength="500"
          show-word-limit
        />
        <div class="limit-text">500{{ $t('Activity.ticket_base.character_limit') }}</div>
      </el-form-item>
      <el-form-item :label="$t('Activity.ticket_base.fields.group_limit')" prop="isGroupLimited" required>
        <el-radio-group v-model="form.isGroupLimited">
          <el-radio :label="true">{{ $t('Activity.ticket_base.options.yes') }}</el-radio>
          <el-radio :label="false">{{ $t('Activity.ticket_base.options.no') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.isGroupLimited" :label="$t('Activity.ticket_base.fields.select_groups')" prop="groups" required>
        <el-select 
          v-model="selectedGroupIds" 
          :placeholder="$t('Activity.ticket_base.placeholders.select_groups')" 
          multiple
          @change="handleGroupChange"
        >
          <el-option 
            v-for="item in groupList" 
            :key="item.id" 
            :label="item.name" 
            :value="item.id"
          >
            <div>
              <div style="font-weight: bold;">{{ item.name }}</div>
              <div style="font-size: 12px; color: #999;">
                {{ item.description || $t('Activity.ticket_base.no_description') }}
              </div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <!-- 底部按鈕 -->
    <div class="form-actions">
      <el-button type="primary" @click="handleNext">{{ $t('Activity.ticket_base.buttons.next') }}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, watch, computed } from 'vue'
import { Ticket } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { groupService } from '../../../services/activityervice'

const { t } = useI18n()

const formRef = ref<FormInstance>()
const groupList = ref<any[]>([])
const selectedGroupIds = ref<number[]>([]) // 用于群组下拉选择的ID数组

const form = ref({
  ticketName: '',
  ticketCode: '',
  ticketDescription: '',
  isGroupLimited: true,
  groups: [] as any[] // 存储完整的群组对象数组
})

// 表單驗證規則
const rules = computed<FormRules>(() => ({
  ticketName: [
    { required: true, message: t('Activity.ticket_base.validation.name.required'), trigger: 'blur' },
    { min: 2, max: 50, message: t('Activity.ticket_base.validation.name.length'), trigger: 'blur' }
  ],
  ticketCode: [
    { required: true, message: t('Activity.ticket_base.validation.code.required'), trigger: 'blur' },
    { min: 2, max: 20, message: t('Activity.ticket_base.validation.code.length'), trigger: 'blur' },
    { pattern: /^[A-Za-z0-9]+$/, message: t('Activity.ticket_base.validation.code.pattern'), trigger: 'blur' }
  ],
  ticketDescription: [
    { max: 500, message: t('Activity.ticket_base.validation.description.length'), trigger: 'blur' }
  ],
  isGroupLimited: [
    { required: true, message: t('Activity.ticket_base.validation.group_limit.required'), trigger: 'change' }
  ],
  groups: [
    { 
      required: true, 
      message: t('Activity.ticket_base.validation.groups.required'), 
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        if (form.value.isGroupLimited && (!value || value.length === 0)) {
          callback(new Error(t('Activity.ticket_base.validation.groups.required')))
        } else {
          callback()
        }
      }
    }
  ]
}))

const props = defineProps({
  contentSave: Function,
  initialData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits<{
  (e: 'next'): void
  (e: 'update:formData', data: any): void
}>()

// 处理群组选择变化
const handleGroupChange = (selectedIds: number[]) => {
  // 根据选中的ID从groupList中找到完整的群组对象
  form.value.groups = selectedIds.map(id => {
    return groupList.value.find(group => group.id === id)
  }).filter((group): group is any => group !== undefined) // 过滤掉undefined
}

// 获取群组列表
const getGroupList = async () => {
  try {
    const res = await groupService.getList({ page: 1, per_page: 99999, limit: 99999 })
    if (res.status === 200 && res?.data?.code === 200) {
      groupList.value = res.data.data?.items || []
      
      // 群组列表加载完成后，如果有初始数据需要回填，立即触发回填
      if (props.initialData && props.initialData.ticketName && props.initialData.groups && props.initialData.groups.length > 0) {
        const groupIds = props.initialData.groups.map((group: any) => group.id)
        fillGroupData(groupIds)
      }
    }
  } catch (error: any) {
    // 错误处理
  }
}

const handleNext = async () => {
  // 驗證表單
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    // 验证通过，处理下一步逻辑
    // 通知父组件保存数据并切换到下一步
    if (props.contentSave) {
      props.contentSave(form.value)
    }
    emit('next')
  } catch (error) {
    // 表单验证失败
  }
}

// 数据回填
const fillFormData = () => {
  if (props.initialData && props.initialData.ticketName) {
    form.value.ticketName = props.initialData.ticketName || ''
    form.value.ticketCode = props.initialData.ticketCode || ''
    form.value.ticketDescription = props.initialData.ticketDescription || ''
    form.value.isGroupLimited = props.initialData.groups && props.initialData.groups.length > 0
    
    if (props.initialData.groups && props.initialData.groups.length > 0) {
      // 从已转换的groups数据中提取id
      const groupIds = props.initialData.groups.map((group: any) => group.id)
      
      // 确保群组列表已加载，然后进行回填
      fillGroupData(groupIds)
    } else {
      // 没有群组数据时清空选择
      selectedGroupIds.value = []
      form.value.groups = []
    }
  }
}

// 单独处理群组数据回填
const fillGroupData = (groupIds: number[]) => {
  if (groupList.value.length === 0) {
    // 群组列表还没加载完成，等待一下再试
    setTimeout(() => fillGroupData(groupIds), 200)
    return
  }
  
  // 设置选中的群组ID
  selectedGroupIds.value = groupIds
  
  // 根据ID找到完整的群组对象
  form.value.groups = groupIds.map(id => {
    const foundGroup = groupList.value.find(group => group.id === id)
    return foundGroup
  }).filter((group): group is any => group !== undefined)
  
}

// 组件挂载时获取群组列表
onMounted(async () => {
  // 先加载群组列表
  await getGroupList()
  
  // 然后填充基础数据（非群组数据）
  if (props.initialData && props.initialData.ticketName) {
    form.value.ticketName = props.initialData.ticketName || ''
    form.value.ticketCode = props.initialData.ticketCode || ''
    form.value.ticketDescription = props.initialData.ticketDescription || ''
    form.value.isGroupLimited = props.initialData.groups && props.initialData.groups.length > 0
  }
  
  // 延迟启用watch监听，避免初始化时的递归调用
  setTimeout(() => {
    isInitializing = false
  }, 100)
})

// 监听 initialData 的变化，当数据更新时重新填充
watch(() => props.initialData, (newData) => {
  if (newData && newData.ticketName && groupList.value.length > 0) {
    fillFormData()
  }
}, { deep: true })

// 监听 form 数据变化，实时传递给父组件
let isInitializing = true
watch(() => form.value, (newFormData) => {
  // 跳过初始化时的触发，避免递归调用
  if (!isInitializing) {
    emit('update:formData', newFormData)
  }
}, { deep: true })

// 添加默认导出以支持组件导入
defineOptions({
  name: 'TicketBase'
})
</script>

<style lang="scss" scoped>
.form-container {
  max-width: 800px;
  margin: 0;
  padding: 0;
}

.form-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #000000;
  margin-bottom: 20px;
}

.form-title .el-icon {
  margin-right: 8px;
}

.limit-text {
  font-size: 16px;
  color: #7F7F7F;
  margin-top: 5px;
}

.form-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: 30px;
  padding-top: 20px;
}

// 使用deep实现样式穿透
:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    text-align: left;
    justify-content: flex-start;
    padding-bottom: 8px;
    font-size: 16px;
    font-weight: normal;
    color: #000000;
  }
}

:deep(.el-radio__label) {
  font-size: 16px;
  font-weight: normal;
  color: #000000;
}

// 普通输入框样式
:deep(.el-input) {
  width: 754px;
  
  .el-input__inner {
    height: 40px;
    line-height: 40px;
  }
}

// 富文本输入框样式
:deep(.el-textarea) {
  width: 754px;
  
  .el-textarea__inner {
    height: 85px;
    resize: none;
  }
}

// 下拉框样式
:deep(.el-select) {
  width: 220px;
  
  .el-input__inner {
    height: 40px;
    line-height: 40px;
  }
}

// 单选框组样式
:deep(.el-radio-group) {
  .el-radio {
    margin-right: 20px;
  }
}

:deep(.el-select .el-select__wrapper) {
  min-height: 40px;
  line-height: 40px;
}
</style>