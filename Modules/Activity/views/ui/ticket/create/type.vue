<template>
  <div class="ticket-status-setting">
    <div class="setting-title">
      <el-icon><Tickets /></el-icon>
      {{ $t('Activity.ticket_type.title') }}
    </div>

    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
      <!-- 票務類型數量限制 -->
      <el-form-item :label="$t('Activity.ticket_type.form.quantity_limit')">
        <el-input v-model="formData.quantityLimit" type="number" :placeholder="$t('Activity.ticket_type.form.quantity_limit_placeholder')" class="w-100" />
      </el-form-item>

      <!-- 是否對外發售 -->
      <el-form-item :label="$t('Activity.ticket_type.form.external_sale')" prop="is_external_sale" required>
        <el-radio-group v-model="formData.is_external_sale">
          <el-radio :label="1">{{ $t('Activity.ticket_type.options.yes') }}</el-radio>
          <el-radio :label="0">{{ $t('Activity.ticket_type.options.no') }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 是否收費 -->
      <el-form-item :label="$t('Activity.ticket_type.form.fee_required')" prop="is_fee_required" required>
        <el-radio-group v-model="formData.is_fee_required">
          <el-radio :label="1">{{ $t('Activity.ticket_type.options.yes') }}</el-radio>
          <el-radio :label="0">{{ $t('Activity.ticket_type.options.no') }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 貨幣選擇 -->
      <!-- <el-form-item :label="$t('Activity.common.ticket_type.form.currency_type')" prop="currencyCode" required>
        <div class="currency-selector">
          <span class="currency-symbol">{{ selectedCurrencySymbol }}</span>
          <el-select v-model="formData.currencyCode" :placeholder="$t('Activity.common.ticket_type.form.currency_placeholder')" class="currency-select" @change="handleCurrencyChange">
            <el-option v-for="currency in currencyOptions" :key="currency.id" :label="`${currency.currency_code} (${currency.currency_symbol})`" :value="currency.currency_code" />
          </el-select>
        </div>
      </el-form-item> -->

      <!-- 細分類型 -->
      <el-form-item :label="$t('Activity.ticket_type.form.subtypes')" prop="subtypes" required>
        <div v-for="(ticket, index) in formData.subtypes" :key="index" class="ticket-type-item">
          <el-row :gutter="10" class="mb-10">
            <el-col :span="4">
              <el-input v-model="ticket.name" :placeholder="$t('Activity.ticket_type.form.subtype_name_placeholder')"></el-input>
            </el-col>
            <el-col :span="4">
              <el-input v-model="ticket.price" type="number" :placeholder="$t('Activity.ticket_type.form.subtype_price_placeholder')"></el-input>
            </el-col>
            <!-- <el-col :span="4">
              <el-input v-model="ticket.currency" :placeholder="$t('Activity.common.ticket_type.form.subtype_currency_placeholder')" :disabled="true"></el-input>
            </el-col> -->
            <el-col :span="10">
              <el-date-picker
                v-model="ticket.dateRange"
                type="daterange"
                :range-separator="$t('Activity.ticket_type.form.date_separator')"
                :start-placeholder="$t('Activity.ticket_type.form.date_start_placeholder')"
                :end-placeholder="$t('Activity.ticket_type.form.date_end_placeholder')"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                class="date-range-picker"
                @change="handleDateRangeChange(index, $event)"
              ></el-date-picker>
            </el-col>
            <el-col :span="2">
              <el-button type="danger" :icon="Delete" @click="removeSubtype(index)" :disabled="formData.subtypes.length <= 1" class="delete-btn"></el-button>
            </el-col>
          </el-row>
        </div>
      </el-form-item>
      <div class="add-subtype-container">
        <el-button type="primary" :icon="Plus" @click="addSubtype" class="add-subtype-btn"> {{ $t('Activity.ticket_type.form.add_subtype') }} </el-button>
      </div>
    </el-form>

    <!-- 底部按鈕 - 编辑模式下隐藏 -->
    <div class="form-actions">
      <el-button @click="handlePrevious" :disabled="props.isCreating">{{ $t('Activity.ticket_type.buttons.previous') }}</el-button>
      <el-button v-if="!props.isEditMode" type="primary" @click="handleCreate" :loading="props.isCreating" :disabled="props.isCreating">
        {{ props.isCreating ? $t('Activity.ticket_type.buttons.creating') : $t('Activity.ticket_type.buttons.create') }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineEmits, defineProps, watch } from 'vue'
import { Tickets, Plus, Delete } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface CurrencyOption {
  id: number
  locale: string
  currency_code: string
  timezone: string
  currency_symbol: string
  name: string
}

interface ActivityTypeResponse {
  code: number
  message: string
  data: {
    types: Record<string, string>
    categories: Record<string, string>
    repeat_patterns: Record<string, string>
    localizations: CurrencyOption[]
  }
}

interface FormData {
  quantityLimit: number
  is_external_sale: number
  is_fee_required: number
  currencyCode: string
  subtypes: Array<{
    name: string
    price: string | number
    dateRange: string[]
  }>
}

const props = defineProps({
  contentSave: Function,
  isCreating: {
    type: Boolean,
    default: false,
  },
  isEditMode: {
    type: Boolean,
    default: false,
  },
  initialData: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits<{
  (e: 'previous'): void
  (e: 'create'): void
  (e: 'update:formData', data: any): void
}>()

const formRef = ref<FormInstance>()

const formData = ref<FormData>({
  quantityLimit: 0,
  is_external_sale: 1,
  is_fee_required: 1,
  currencyCode: '',
  subtypes: [{
    name: '',
    price: '',
    dateRange: [],
  }]
})

const currencyOptions = ref<CurrencyOption[]>([])

// 表單驗證規則
const rules = computed<FormRules>(() => ({
  is_external_sale: [{ required: true, message: t('Activity.ticket_type.validation.external_sale_required'), trigger: 'change' }],
  is_fee_required: [{ required: true, message: t('Activity.ticket_type.validation.fee_required_required'), trigger: 'change' }],
  currencyCode: [{ required: true, message: t('Activity.ticket_type.validation.currency_required'), trigger: 'change' }],
  subtypes: [{ required: true, message: t('Activity.ticket_type.validation.subtypes_required'), trigger: 'change' }],
}))

// 計算當前選中貨幣的符號
const selectedCurrencySymbol = computed(() => {
  const currency = currencyOptions.value.find(item => item.currency_code === formData.value.currencyCode)
  return currency?.currency_symbol || '$'
})

// 添加细分类型
const addSubtype = () => {
  const newSubtype = {
    name: '',
    price: 0,
    // currency: formData.value.currencyCode,
    dateRange: [],
  }
  formData.value.subtypes.push(newSubtype)
}

// 删除细分类型
const removeSubtype = (index: number) => {
  if (formData.value.subtypes.length > 1) {
    formData.value.subtypes.splice(index, 1)
  }
}

// 处理日期范围变化
const handleDateRangeChange = (index: number, dateRange: string[] | null) => {
  if (formData.value.subtypes[index]) {
    // 直接更新对应索引的dateRange
    formData.value.subtypes[index].dateRange = dateRange || []
    
    // 确保响应式更新
    const updatedSubtypes = [...formData.value.subtypes]
    updatedSubtypes[index] = { ...updatedSubtypes[index], dateRange: dateRange || [] }
    formData.value.subtypes = updatedSubtypes
    
  }
}

// 獲取活動類型配置數據
const getActivityType = async (): Promise<void> => {
  try {
    // 這裡替換為實際的API調用
    const response = await fetch('/activity/config')
    const data: ActivityTypeResponse = await response.json()

    if (data.code === 200) {
      currencyOptions.value = data.data.localizations
      // 設置默認貨幣為港幣
      if (currencyOptions.value.length > 0) {
        const hkdCurrency = currencyOptions.value.find(item => item.currency_code === 'HKD')
        const defaultCurrency = hkdCurrency?.currency_code || currencyOptions.value[0].currency_code

        // 设置主货币代码
        if (!formData.value.currencyCode) {
          formData.value.currencyCode = defaultCurrency
        }

        // 更新所有细分类型的货币
        formData.value.subtypes = formData.value.subtypes.map(subtype => ({
          ...subtype,
          currency: formData.value.currencyCode,
        }))
      }
    }
  } catch (error) {
    console.error(t('Activity.ticket_type.messages.get_config_failed'), error)
    // 使用模擬數據作為後備
    currencyOptions.value = [
      {
        id: 4,
        locale: 'zh_CN',
        currency_code: 'CNY',
        timezone: 'Asia/Shanghai',
        currency_symbol: '¥',
        name: t('Activity.ticket_type.currency.china_mainland'),
      },
      {
        id: 5,
        locale: 'zh_HK',
        currency_code: 'HKD',
        timezone: 'Asia/Hong_Kong',
        currency_symbol: 'HK$',
        name: t('Activity.ticket_type.currency.hong_kong'),
      },
      {
        id: 6,
        locale: 'en',
        currency_code: 'USD',
        timezone: 'America/New_York',
        currency_symbol: '$',
        name: t('Activity.ticket_type.currency.united_states'),
      },
    ]
  }
}

// 處理貨幣變更（更新所有细分类型的货币）
const handleCurrencyChange = (value: string): void => {
  console.log(t('Activity.ticket_type.messages.currency_changed'), value)
  formData.value.subtypes = formData.value.subtypes.map(subtype => ({
    ...subtype,
    currency: value,
  }))
}

// 事件處理
const handlePrevious = (): void => {
    console.log(t('Activity.ticket_type.buttons.previous'))
  emit('previous')
}

const handleCreate = async (): Promise<void> => {
  // 驗證表單
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    // 验证通过，处理创建逻辑
    // 通知父组件保存数据并创建票券
    if (props.contentSave) {
      props.contentSave(formData.value)
    }
    emit('create')
  } catch (error) {
    console.log(t('Activity.ticket_type.messages.form_validation_failed'), error)
  }
}

// 数据回填
let hasInitialized = false
const fillFormData = () => {
  if (props.initialData && Object.keys(props.initialData).length > 0) {
    formData.value.quantityLimit = props.initialData.quantityLimit ?? 0
    formData.value.is_external_sale = props.initialData.is_external_sale ?? 1
    formData.value.is_fee_required = props.initialData.is_fee_required ?? 1

    // 只在首次初始化时填充subtypes数据，避免覆盖用户输入
    if (!hasInitialized && props.initialData.subtypes && props.initialData.subtypes.length > 0) {
      formData.value.subtypes = props.initialData.subtypes.map((subtype: any) => ({
        name: subtype.name || '',
        price: Number(subtype.price) || 0,
        dateRange: (subtype.start_date && subtype.end_date) ? [subtype.start_date, subtype.end_date] : [],
      }))
      hasInitialized = true
    } else if (!hasInitialized) {
      // 如果没有subtypes数据，确保至少有一个空的subtype
      formData.value.subtypes = [{
        name: '',
        price: 0,
        dateRange: [],
      }]
      hasInitialized = true
    }
  }
}

// 監聽 initialData 變化
watch(
  () => props.initialData,
  (newData) => {
    if (newData && Object.keys(newData).length > 0) {
      fillFormData()
    }
  },
  { immediate: true, deep: true }
)

// 监听 formData 数据变化，实时传递给父组件
let isInitializing = true
watch(() => formData.value, (newFormData) => {
  // 跳过初始化时的触发，避免递归调用
  if (!isInitializing) {
    emit('update:formData', newFormData)
  }
}, { deep: true })

// 移除专门的日期范围监听器，避免与handleDateRangeChange冲突
// 日期变化现在通过handleDateRangeChange函数直接处理



// 組件掛載時獲取數據
onMounted(() => {
  // getActivityType()

  // 立即尝试填充数据
  fillFormData()
  
  // 延迟启用watch监听，避免初始化时的递归调用
  setTimeout(() => {
    isInitializing = false
  }, 100)
})

// 添加默认导出以支持组件导入
defineOptions({
  name: 'TicketType',
})
</script>

<style lang="scss" scoped>
.ticket-status-setting {
  max-width: 800px;
  margin: 0;
  padding: 0;
}

.setting-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
}

.setting-title .el-icon {
  margin-right: 8px;
}

.price-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.currency-symbol {
  font-size: 16px;
  font-weight: 500;
  color: #606266;
  min-width: 20px;
}

.price-input {
  flex: 1;
}

.currency-select {
  width: 120px;
}

.add-btn {
  padding: 8px 12px;
}

.form-actions {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.ticket-type-item {
  margin-bottom: 10px;
  padding: 10px 0;
}

.mb-10 {
  margin-bottom: 10px;
}

.delete-btn {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 50%;
}

.add-subtype-container {
  margin-top: 10px;
  text-align: left;
}

.add-subtype-btn {
  border-radius: 4px;
}

.currency-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.currency-symbol {
  font-size: 16px;
  font-weight: 500;
  color: #606266;
  min-width: 30px;
}

.currency-select {
  width: 200px;
}

// 使用deep实现样式穿透
:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    text-align: left;
    justify-content: flex-start;
    padding-bottom: 8px;
    font-size: 16px;
    font-weight: 500;
  }
}

:deep(.el-radio__label) {
  font-size: 16px;
  font-weight: 400;
}

// 数量限制输入框样式 - 220px宽度，40px高度
:deep(.el-input) {
  &.w-100 {
    width: 220px;
  }
  
  .el-input__inner {
    height: 40px;
    line-height: 40px;
  }
}

// 细分类型输入框样式 - 保持原有宽度
:deep(.ticket-type-item .el-input) {
  .el-input__inner {
    height: 40px;
    line-height: 40px;
  }
}

// 日期选择器样式 - 保持原有宽度
:deep(.date-range-picker) {
  width: 100%;
  
  .el-input__inner {
    height: 40px;
    line-height: 40px;
  }
}

:deep(.el-range-editor.el-input__wrapper) {
  height: 40px !important;
  min-height: 40px;
}

:deep(.el-range-input) {
  height: 40px;
  line-height: 40px;
}

// 单选框组样式
:deep(.el-radio-group) {
  .el-radio {
    margin-right: 20px;
  }
}

:deep(.el-col-2.is-guttered) {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 表单项必填标记
:deep(.el-form-item.is-required .el-form-item__label::before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}
</style>
