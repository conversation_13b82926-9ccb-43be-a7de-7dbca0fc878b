<template>
  <div class="bwms-module table-page">
    <div class="module-header">
      <div class="actions">
        <el-button class="button-no-border el-button-plus" @click="handleCreate" type="primary">
          <el-icon class="el-icon-custom">
            <img :src="iconPaths.plus" alt="" />
          </el-icon>
          <span style="color: var(--el-color-white);">新建票務</span>
        </el-button>
      </div>
    </div>
    <div class="module-con">
      <div class='box'>
        <el-table :data="ticketList" style="width: 100%; height: 100%;" v-loading="loading">
          <template #empty>
            <el-empty description="暂无数据" image-size="100px" />
          </template>
          <el-table-column prop="name" label="名稱"></el-table-column>
          <el-table-column prop="code" label="票務代碼" width="180"></el-table-column>
          <el-table-column label="子票種數量" width="120">
            <template #default="scope">
              {{ scope.row.subtypes?.length || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="對外銷售" width="120">
            <template #default="scope">
              {{ scope.row.is_external_sale ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column label="收費" width="120">
            <template #default="scope">
              {{ scope.row.is_fee_required ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="創建日期" width="180"></el-table-column>
          <el-table-column fixed="right" label="操作" width="150">
            <template #default="scope">
              <div class="bwms-operate-btn-box">
                <div class="bwms-btn bwms-operate-btn" @click="handleEdit(scope.row)">
                  <el-icon>
                    <img :src="iconPaths.edit" alt="" />
                  </el-icon>
                </div>
                <el-button class="bwms-btn del-btn bwms-operate-btn" type="text" @click="handleDelete(scope.row)">
                  <el-icon>
                    <img :src="iconPaths.delete" alt="" />
                  </el-icon>
                </el-button>
                <el-button class="bwms-btn bwms-operate-btn" type="text" @click="handleView(scope.row)">
                  <el-icon>
                    <CopyDocument />
                  </el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

      </div>
              
      <div class="box-footer">
          <div class="pagination-container table-pagination-style">
            <div class="pagination-left">
              <span class="page-size-text">每頁顯示</span>
              <el-select v-model="pageSize" class="page-size-select" @change="handlePageSizeChange" size="default">
                <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size" :value="size" class="page-size-option" />
                <template #empty>
                  <div style="text-align: center; padding: 8px 0;">
                    暂无数据
                  </div>
                </template>
              </el-select>
              <span class="total-text">共 {{ total }} 項</span>
            </div>
            <div class="pagination-right">
              <el-pagination 
                v-model:current-page="currentPage" 
                background 
                layout="prev, pager, next"
                :page-size="pageSize" 
                :total="total" 
                @current-change="handleCurrentChange" 
              />
            </div>
          </div>
        </div>
    </div>
    
    <!-- 删除确认弹窗 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="刪除確認"
      class="el-dialog-common-cls"
      width="400px"
    >
      <div class="delete-content">
        <p>確定要刪除票務「{{ currentDeleteItem?.name }}」嗎？</p>
        <p class="warning-text">此操作不可恢復，請謹慎操作。</p>
      </div>
      <template #footer>
        <div class="flex justify-center">
          <el-button class="el-button-default" @click="deleteDialogVisible = false">
            取消
          </el-button>
          <el-button class="button-no-border" type="danger" @click="confirmDelete" :loading="deleteLoading">
            確認刪除
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Delete, Document, CopyDocument } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { ticketService } from "../../services/ticketService"
import type { IPageParams } from "../../types"

// 图标路径计算属性
const iconPaths = computed(() => ({
  plus: $asset('Cms/Asset/PlusIcon.png'),
  edit: $asset('Cms/Asset/EditIcon.png'), 
  delete: $asset('Cms/Asset/DeleteIcon.png')
}))

// 定义接口类型
interface TicketSubtype {
  id: number
  ticket_type_id: number
  name: string
  sort: number
}

interface TicketItem {
  id: number
  name: string
  code: string
  description: string | null
  is_external_sale: number
  is_fee_required: number
  creator_id: number
  created_at: string
  updated_at: string
  subtypes: TicketSubtype[]
}

// API 响应类型
interface TicketListResponse {
  items: TicketItem[]
  total: number
}

const router = useRouter()

// 表格数据
const ticketList = ref<TicketItem[]>([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const totalPages = computed(() => Math.ceil(total.value / pageSize.value))

const loading = ref(false)

// 删除相关
const deleteDialogVisible = ref(false)
const deleteLoading = ref(false)
const currentDeleteItem = ref<TicketItem | null>(null)

// 操作方法
const handleEdit = (row: TicketItem) => {
  router.push({
    path: '/activity/settings/ticket/create',
    query: {
      id: row.id.toString()
    }
  })
}

// 删除
const handleDelete = (row: TicketItem) => {
  currentDeleteItem.value = row
  deleteDialogVisible.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!currentDeleteItem.value) return
  
  deleteLoading.value = true
  try {
    await ticketService.delete(currentDeleteItem.value.id)
    ElMessage.success('刪除成功')
    deleteDialogVisible.value = false
    currentDeleteItem.value = null
    
    // 重新加载数据
    await getTableDataList()
  } catch (error) {
    ElMessage.error('刪除失敗')
  } finally {
    deleteLoading.value = false
  }
}

// 查看
const handleView = (row: TicketItem) => {
  console.log('查看', row)
  // TODO: 实现查看逻辑
}

// 分页变化处理
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getTableDataList()
}

// 页面大小变化处理
const handlePageSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1 // 重置到第一页
  getTableDataList()
}

// 创建票务
const handleCreate = () => {
  router.push('/activity/settings/ticket/create')
}

// 查询列表
const getTableDataList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      per_page: pageSize.value
    }
    const res = await ticketService.getList(params)
    ticketList.value = res.data.data.items
    total.value = res.data.data.total
  } catch (error) {
    ElMessage.error('获取票务列表失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getTableDataList()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  margin-top: 10px;
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 16px 0;
}

.pagination-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-text, .total-text {
  font-size: 14px;
  color: #606266;
}

.page-size-select {
  width: 80px;
}

.pagination-right {
  display: flex;
  align-items: center;
}

// 操作按钮样式
.bwms-operate-btn-box {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bwms-operate-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #dcdfe6;
  background: #fff;

  &:hover {
    border-color: #409eff;
    background: #ecf5ff;
    
    .el-icon {
      color: #409eff;
    }
  }

  .el-icon {
    font-size: 16px;
    color: #606266;
  }
}

.del-btn {
  &:hover {
    border-color: #f56c6c;
    background: #fef0f0;
    
    .el-icon {
      color: #f56c6c;
    }
  }
}

// Box footer 样式
.box-footer {
  border-top: 1px solid #ebeef5;
  background: #fff;
}

// 分页样式优化
:deep(.el-pagination) {
  .el-pager li {
    min-width: 32px;
    height: 32px;
    line-height: 30px;
  }
  
  .btn-prev, .btn-next {
    min-width: 32px;
    height: 32px;
    line-height: 30px;
  }
}

:deep(.el-select-dropdown__item) {
  text-align: center;
}

// 删除弹窗样式
.delete-content {
  text-align: center;
  padding: 20px 0;
  
  p {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #303133;
  }
  
  .warning-text {
    font-size: 14px;
    color: #f56c6c;
  }
}

.flex {
  display: flex;
  gap: 12px;
}

.justify-center {
  justify-content: center;
}
</style> 