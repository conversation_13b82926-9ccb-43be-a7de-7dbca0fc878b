<template>
  <!-- 侧边面板容器 -->
  <div v-if="dialogVisible" class="side-panel" :class="{ 'slide-in': dialogVisible }">
    <!-- 面板头部 -->
    <div class="panel-header">
      <div class="panel-title">{{ formattedDate }}</div>
      <el-button class="close-btn" type="text" @click="dialogVisible = false">
        <el-icon>
          <Close />
        </el-icon>
      </el-button>
    </div>

    <!-- 面板内容 -->
    <div class="panel-content">
      <el-timeline>
        <el-timeline-item v-for="(activity, index) in activities" :key="index" :color="activity.color" size="large">
          <div class="timeline-item-content">
            <div class="timeline-title">
              {{ activity.timeRange }} <span>{{ activity.title }}</span>
            </div>
            <div class="timeline-detail">
              <div class="detail-item">
                <span class="detail-label">{{ $t('Activity.calendar_details.detail.activity_type') }}</span>
                <span class="detail-value">{{ activity.type }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ $t('Activity.calendar_details.detail.activity_name') }}</span>
                <span class="detail-value">{{ activity.name }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ $t('Activity.calendar_details.detail.activity_status') }}</span>
                <span class="detail-value">{{ activity.status }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">{{ $t('Activity.calendar_details.detail.organizer') }}</span>
                <span class="detail-value">{{ activity.organizer }}</span>
              </div>
              <div class="detail-item" v-if="activity.location">
                <span class="detail-label">{{ $t('Activity.calendar_details.detail.activity_location') }}</span>
                <span class="detail-value">{{ activity.location }}</span>
              </div>
              <div class="detail-item" v-if="activity.address">
                <span class="detail-label">{{ $t('Activity.calendar_details.detail.detailed_address') }}</span>
                <span class="detail-value">{{ activity.address }}</span>
              </div>
              <div class="detail-item"
                v-if="activity.platform !== $t('Activity.calendar_details.activity_type.offline')">
                <span class="detail-label">{{ $t('Activity.calendar_details.detail.activity_platform') }}</span>
                <span class="detail-value">{{ activity.platform }}</span>
              </div>
              <div class="detail-item zoom-link" v-if="activity.link !== '无链接'">
                {{ activity.link }}
              </div>
              <div class="detail-item" v-if="activity.registration_deadline">
                <span class="detail-label">{{ $t('Activity.calendar_details.detail.registration_deadline') }}</span>
                <span class="detail-value">{{ activity.registration_deadline }}</span>
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 面板底部 -->
    <div class="panel-footer">
      <el-button type="primary" size="small" @click="addActivity">
        <el-icon>
          <Plus />
        </el-icon>
        {{ $t('Activity.calendar_details.button.add_activity') }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue'
import { ElButton, ElTimeline, ElTimelineItem, ElIcon } from 'element-plus'
import { Close, Plus } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
const router = useRouter()
interface Activity {
  timeRange: string
  title: string
  type: string
  name: string
  platform: string
  link: string
  color: string
}

interface CalendarEvent {
  id: number
  name: string
  timeRange: string
  type: string
  platform: string
  link: string
  category: string
  location?: string
  address?: string
  status: string
  registration_deadline: string
  organizer_name: string
  organizer_email: string
}

const props = defineProps({
  visible: Boolean,
  selectedObj: Object
})

const { t } = useI18n()

console.log(props.visible)

const emit = defineEmits(['update:visible', 'add-activity'])

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const formattedDate = computed(() => {
  if (props.selectedObj && props.selectedObj.fullDate) {
    const date = props.selectedObj.fullDate
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year} ${t('Activity.calendar_details.date_format.year')} ${month} ${t('Activity.calendar_details.date_format.month')} ${day} ${t('Activity.calendar_details.date_format.day')}`
  }
  return t('Activity.calendar_details.dialog.default_title')
})

const activities = computed(() => {
  if (props.selectedObj && props.selectedObj.events && props.selectedObj.events.length > 0) {
    return props.selectedObj.events.map((event: CalendarEvent) => ({
      timeRange: event.timeRange || t('Activity.calendar_details.timeline.time_tbd'),
      title: t('Activity.calendar_details.timeline.view_details'),
      type: getTypeLabel(event.type),
      name: event.name || t('Activity.calendar_details.detail.activity_name'),
      platform: event.platform || t('Activity.calendar_details.activity_type.offline'),
      link: event.link || '无链接',
      color: getEventColor(event.category),
      category: event.category,
      location: event.location,
      address: event.address,
      status: getStatusLabel(event.status),
      organizer: event.organizer_name,
      registration_deadline: event.registration_deadline
    }))
  }
  return []
})

// 获取活动类型标签
const getTypeLabel = (type: string) => {
  const typeMap: { [key: string]: string } = {
    'online': t('Activity.calendar_details.activity_type.online'),
    'offline': t('Activity.calendar_details.activity_type.offline'),
    'hybrid': t('Activity.calendar_details.activity_type.hybrid')
  }
  return typeMap[type] || type
}

// 获取活动状态标签
const getStatusLabel = (status: string) => {
  const statusMap: { [key: string]: string } = {
    'created': t('Activity.calendar_details.activity_status.created'),
    'published': t('Activity.calendar_details.activity_status.published'),
    'ongoing': t('Activity.calendar_details.activity_status.ongoing'),
    'completed': t('Activity.calendar_details.activity_status.completed'),
    'cancelled': t('Activity.calendar_details.activity_status.cancelled')
  }
  return statusMap[status] || status
}

// 根据分类获取事件颜色
const getEventColor = (category: string) => {
  const colorMap: { [key: string]: string } = {
    'meeting': '#409EFF',
    'conference': '#67C23A',
    'workshop': '#E6A23C',
    'seminar': '#F56C6C',
    'holiday': '#909399'
  }
  return colorMap[category] || '#409EFF'
}

const addActivity = () => {
  router.push('/activity/list')
}
</script>

<style scoped lang="scss">
/* 侧边面板容器 */
.side-panel {
  position: fixed;
  top: 50%;
  right: 0;
  transform: translateY(-50%) translateX(100%);
  width: 390px;
  height: 750px;
  background-color: #ffffff;
  border-radius: 12px 0 0 12px;
  border-left: 1px solid #e4e7ed;
  border-top: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: -2px 0 12px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease-in-out;
  z-index: 9999;

  &.slide-in {
    transform: translateY(-50%) translateX(0);
  }
}

/* 面板头部 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #2C629A;
  color: white;
  border-radius: 8px 0 0 0;
  flex-shrink: 0;
}

.panel-title {
  font: normal normal bold 18px/24px Microsoft JhengHei;
  color: white;
}

.close-btn {
  color: white !important;
  padding: 0 !important;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  .el-icon {
    font-size: 18px;
  }
}

/* 面板内容 */
.panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    transition: background 0.3s ease;

    &:hover {
      background: #a8a8a8;
    }
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

/* 面板底部 */
.panel-footer {
  padding: 0px 20px ;
  text-align: center;
  flex-shrink: 0;
  min-height: 60px;
  display: flex;
  align-items: center;


  .el-button {
    width: 120px;
    height: 40px;
    /* UI Properties */
    background: #007EE5 0% 0% no-repeat padding-box;
    border-radius: 5px;
    opacity: 1;

    .el-icon {
      font-size: 14px;
    }
  }
}

/* 时间轴内容样式 */
.timeline-item-content {
  padding-bottom: 20px;
}

.timeline-title {
  margin-bottom: 15px;
  font: normal normal bold 18px/24px Microsoft JhengHei;
  display: flex;
  align-items: center;
  color: #007EE5;

  span {
    color: #1565c0;
    cursor: pointer;
    margin-left: 8px;
  }
}

.timeline-detail {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  border: none;
  margin-left: 0;
}

.detail-item {
  display: flex;
  margin-bottom: 6px;
  font-size: 14px;
  line-height: 1.6;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  width: 80px;
  font: normal normal normal 14px/19px Microsoft JhengHei;
  letter-spacing: 0px;
  color: #7F7F7F;
}

.detail-value {
  flex: 1;
  font: normal normal normal 14px/19px Microsoft JhengHei;
  letter-spacing: 0px;
  color: #000000;
  opacity: 1;
}

.zoom-link {
  color: #1565c0;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 80px;
  word-break: break-all;
  text-decoration: underline;
  cursor: pointer;

  &:hover {
    color: #0d47a1;
  }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-timeline) {
  padding-left: 10px;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 25px;
  position: relative;
}

:deep(.el-timeline-item__tail) {
  border-left: 2px solid #e4e7ed;
  left: -6px;
  top: 10px;
  height: calc(100% - 35px);
}

:deep(.el-timeline-item:last-child .el-timeline-item__tail) {
  display: none;
}

:deep(.el-timeline-item__node) {
  left: -10px;
  width: 10px;
  height: 10px;
  background-color: #1565c0 !important;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #1565c0;
}

:deep(.el-button--primary) {
  background-color: #1565c0 !important;
  border-color: #1565c0 !important;
  color: white !important;
  min-height: 32px !important;
  padding: 8px 16px !important;

  &:hover,
  &:focus {
    background-color: #0d47a1 !important;
    border-color: #0d47a1 !important;
  }
}

/* 响应式调整 */
@media (max-width: 480px) {
  .side-panel {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    border: none;
  }

  .panel-header {
    border-radius: 0;
  }
}
</style>
