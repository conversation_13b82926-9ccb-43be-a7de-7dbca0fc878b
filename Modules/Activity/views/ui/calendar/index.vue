<template>
  <div class="bwms-module">
    <div class="calendar-header">
      <div class="header-left">
        <div class="date-navigation">
          <el-button class="nav-btn prev-btn" @click="prevMonth">
            <el-icon>
              <ArrowLeft />
            </el-icon>
          </el-button>
          <div class="current-date">{{ currentDateLabel }}</div>
          <el-button class="nav-btn next-btn" @click="nextMonth">
            <el-icon>
              <ArrowRight />
            </el-icon>
          </el-button>
        </div>
        <!-- <el-button class="today-btn" @click="goToday">{{ $t('Activity.event_create.tab.calendar.header.today') }}</el-button> -->
      </div>
      <div class="header-right">
        <div class="view-type-selector">
          <div class="custom-toggle-container">
            <div class="toggle-slider" :class="{ 'slider-right': viewType === 'year' }"></div>
            <div class="toggle-option" :class="{ 'active': viewType === 'month' }" @click="viewType = 'month'">
              {{ $t('Activity.event_create.tab.calendar.header.month') }}
            </div>
            <div class="toggle-option" :class="{ 'active': viewType === 'year' }" @click="viewType = 'year'">
              {{ $t('Activity.event_create.tab.calendar.header.year') }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="module-con">
      <div class="box calendar-body">
        <!-- 月视图 -->
        <div v-if="viewType === 'month'" class="month-view">
          <div class="calendar-week-header">
            <div class="week-day" v-for="day in weekDays" :key="day">{{ day }}</div>
          </div>

          <div class="calendar-grid">
            <div v-for="(day, index) in calendarDays" :key="index" class="day-cell" :class="{
              'prev-month': day.isPrevMonth,
              'next-month': day.isNextMonth,
              'today': day.isToday,
              'has-events': day.events && day.events.length > 0,
              'current-day': day.isToday
            }" @click="onDayClick(day)">
              <div class="date-container">
                <div class="day-number">{{ day.date }}</div>
                <div class="lunar-date">{{ getLunarDate(day) }}</div>
              </div>
              <div class="event-list" v-if="day.events && day.events.length > 0">
                <!-- 显示前2个活动 -->
                <div v-for="(event, eventIndex) in day.events.slice(0, 2)" :key="eventIndex" class="event-item"
                  @click.stop="showDetails(event, day)">
                  <div class="event-dot"></div>
                  <div class="event-name">{{ event.name }}</div>
                </div>
                <!-- 如果有更多活动，显示省略号 -->
                <div v-if="day.events.length > 2" class="event-more-ellipsis"
                  @click.stop="showDetails(day.events[0], day)">
                  ...
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 年视图 -->
        <div v-else-if="viewType === 'year'" class="year-view">
          <div class="year-grid">
            <div v-for="month in 12" :key="month" class="month-card" @click="selectMonth(month - 1)">
              <div class="month-header">{{ getMonthName(month - 1) }}</div>
              <div class="mini-calendar">
                <div class="mini-days">
                  <div v-for="day in getMiniCalendarDays(month - 1)" :key="day.key" class="mini-day" :class="{
                    'mini-today': day.isToday,
                    'mini-has-events': day.hasEvents,
                    'mini-other-month': day.isOtherMonth
                  }">
                    {{ day.date }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情弹框组件 -->
    <ActivityDetails :visible="detailsVisible" :selectedObj="selectedDate" @update:visible="detailsVisible = $event"
      @add-activity="handleAddActivity" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import ActivityDetails from './details.vue'
import { activityervice } from '../../services/activityervice'

const { t } = useI18n()

const viewType = ref('month')
const currentDate = ref(new Date()) // 使用当前系统日期
const detailsVisible = ref(false)
const selectedDate = ref(null)
const calendarEvents = ref({}) // 存储从接口获取的活动数据
const yearCalendarEvents = ref({}) // 存储年视图的活动数据
const loading = ref(false)

const weekDays = computed(() => [
  t('Activity.event_create.tab.calendar.weekdays.sunday'),
  t('Activity.event_create.tab.calendar.weekdays.monday'),
  t('Activity.event_create.tab.calendar.weekdays.tuesday'),
  t('Activity.event_create.tab.calendar.weekdays.wednesday'),
  t('Activity.event_create.tab.calendar.weekdays.thursday'),
  t('Activity.event_create.tab.calendar.weekdays.friday'),
  t('Activity.event_create.tab.calendar.weekdays.saturday')
])

const currentDateLabel = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth() + 1
  return `${year}${t('Activity.event_create.tab.calendar_details.date_format.year')}${month.toString().padStart(2, '0')}${t('Activity.event_create.tab.calendar_details.date_format.month')}`
})

const showDetails = (event, day) => {
  detailsVisible.value = true;
  selectedDate.value = {
    ...event,
    ...day,
    fullDate: new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), day.date)
  };
}

// 处理添加活动
const handleAddActivity = (data) => {
  console.log('添加活动:', data)
  // 这里可以调用添加活动的接口
  detailsVisible.value = false
}

// 生成日历数据
const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)

  const days = []

  // 上个月的日期
  const prevMonthDays = firstDay.getDay()
  const prevMonthLastDate = new Date(year, month, 0).getDate()

  for (let i = prevMonthDays - 1; i >= 0; i--) {
    days.push({
      date: prevMonthLastDate - i,
      isPrevMonth: true,
      isNextMonth: false,
      isToday: false,
      events: []
    })
  }

  // 当前月的日期
  const today = new Date()
  for (let i = 1; i <= lastDay.getDate(); i++) {
    const isToday =
      today.getDate() === i &&
      today.getMonth() === month &&
      today.getFullYear() === year

    const dateKey = formatDateKey(year, month, i)

    const events = calendarEvents.value[dateKey] || []
    console.log(i + 'events: ' + dateKey + '______' + JSON.stringify(events));

    days.push({
      date: i,
      isPrevMonth: false,
      isNextMonth: false,
      isToday: isToday,
      events: events
    })
  }

  // 下个月的日期
  const nextMonthDays = 42 - days.length
  for (let i = 1; i <= nextMonthDays; i++) {
    days.push({
      date: i,
      isPrevMonth: false,
      isNextMonth: true,
      isToday: false,
      events: []
    })
  }

  return days
})

// 格式化日期作为key
const formatDateKey = (year, month, day) => {
  // 参数验证
  if (!year || !month || !day) {
    console.warn('formatDateKey: 参数不完整', { year, month, day })
    return ''
  }

  // 注意：JavaScript Date构造函数的月份是从0开始的
  // 如果传入的month是1-12，需要减1；如果已经是0-11，则不需要减1
  // 这里假设传入的month是1-12（常见情况）
  const date = new Date(year, month, day)

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('formatDateKey: 无效日期', { year, month, day })
    return ''
  }

  // 使用本地时间格式化，避免时区问题
  const formattedDate = date.getFullYear() + '-' +
    String(date.getMonth() + 1).padStart(2, '0') + '-' +
    String(date.getDate()).padStart(2, '0')

  return formattedDate // 返回 YYYY-MM-DD 格式
}

// 获取日历数据
const fetchCalendarData = async () => {
  try {
    loading.value = true
    const year = currentDate.value.getFullYear()

    let startDate, endDate

    if (viewType.value === 'month') {
      // 月视图：只获取当月数据
      const month = currentDate.value.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      startDate = firstDay.toISOString().split('T')[0]
      endDate = lastDay.toISOString().split('T')[0]
    } else {
      // 年视图：获取整年数据
      startDate = `${year}-01-01`
      endDate = `${year}-12-31`
    }

    const response = await activityervice.getCalendar(startDate, endDate)

    // 重组数据，以日期为key
    const eventsMap = {}
    if (response.data && typeof response.data.data === 'object') {
      Object.keys(response.data.data).forEach(dateKey => {
        const dayData = response.data.data[dateKey]

        if (dayData.activities && Array.isArray(dayData.activities)) {
          eventsMap[dateKey] = dayData.activities.map(activity => ({
            id: activity.id,
            name: activity.title,
            timeRange: activity.start_time ? activity.start_time : t('Activity.event_create.tab.calendar.event.time_tbd'),
            type: activity.type,
            platform: activity.online_platform || t('Activity.event_create.tab.calendar.event.offline'),
            link: activity.online_platform_url || '',
            category: activity.category,
            location: activity.location,
            address: activity.address,
            status: activity.status,
            registration_deadline: activity.registration_deadline,
            organizer_name: `${activity.organizer_last_name}${activity.organizer_first_name}`,
            organizer_email: activity.organizer_email
          }))
        }
      })
    }

    if (viewType.value === 'month') {
      calendarEvents.value = eventsMap
    } else {
      yearCalendarEvents.value = eventsMap
    }
  } catch (error) {
    console.error('获取日历数据失败:', error)
    ElMessage.error(t('Activity.event_create.tab.calendar.message.fetch_calendar_failed'))
  } finally {
    loading.value = false
  }
}

// 切换月份
const prevMonth = () => {
  currentDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth() - 1,
    1
  )
}

const nextMonth = () => {
  currentDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth() + 1,
    1
  )
}

// 跳转到今天
const goToday = () => {
  const today = new Date()
  currentDate.value = new Date(today.getFullYear(), today.getMonth(), 1)

  // 如果是年视图，切换到月视图
  if (viewType.value === 'year') {
    viewType.value = 'month'
  }

  // 确保获取当月数据
  setTimeout(() => {
    const todayElement = document.querySelector('.today')
    if (todayElement) {
      todayElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }, 100)
}

// 点击日期
const onDayClick = (day) => {
  if (day.isPrevMonth) {
    prevMonth()
  } else if (day.isNextMonth) {
    nextMonth()
  } else if (day.events && day.events.length > 0) {
    showDetails(day.events[0], day)
  }
}

// 获取月份名称
const getMonthName = (monthIndex) => {
  const months = [
    t('Activity.event_create.tab.calendar.months.january'),
    t('Activity.event_create.tab.calendar.months.february'),
    t('Activity.event_create.tab.calendar.months.march'),
    t('Activity.event_create.tab.calendar.months.april'),
    t('Activity.event_create.tab.calendar.months.may'),
    t('Activity.event_create.tab.calendar.months.june'),
    t('Activity.event_create.tab.calendar.months.july'),
    t('Activity.event_create.tab.calendar.months.august'),
    t('Activity.event_create.tab.calendar.months.september'),
    t('Activity.event_create.tab.calendar.months.october'),
    t('Activity.event_create.tab.calendar.months.november'),
    t('Activity.event_create.tab.calendar.months.december')
  ]
  return months[monthIndex]
}

// 选择月份（从年视图切换到月视图）
const selectMonth = (monthIndex) => {
  currentDate.value = new Date(currentDate.value.getFullYear(), monthIndex, 1)
  viewType.value = 'month'
}

// 获取年视图中每个月的迷你日历数据
const getMiniCalendarDays = (monthIndex) => {
  const year = currentDate.value.getFullYear()
  const firstDay = new Date(year, monthIndex, 1)
  const lastDay = new Date(year, monthIndex + 1, 0)
  const firstDayOfWeek = firstDay.getDay()
  const daysInMonth = lastDay.getDate()

  const days = []
  const today = new Date()

  // 上个月的日期
  const prevMonth = new Date(year, monthIndex, 0)
  const daysInPrevMonth = prevMonth.getDate()

  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    const date = daysInPrevMonth - i
    days.push({
      key: `prev-${date}`,
      date: date,
      isOtherMonth: true,
      isToday: false,
      hasEvents: false
    })
  }

  // 当前月的日期
  for (let date = 1; date <= daysInMonth; date++) {
    const isToday = today.getDate() === date &&
      today.getMonth() === monthIndex &&
      today.getFullYear() === year

    const dateKey = formatDateKey(year, monthIndex, date)
    const hasEvents = yearCalendarEvents.value[dateKey] &&
      yearCalendarEvents.value[dateKey].length > 0

    days.push({
      key: `current-${date}`,
      date: date,
      isOtherMonth: false,
      isToday: isToday,
      hasEvents: hasEvents
    })
  }

  // 下个月的日期，补齐42个位置
  const remainingDays = 42 - days.length
  for (let date = 1; date <= remainingDays; date++) {
    days.push({
      key: `next-${date}`,
      date: date,
      isOtherMonth: true,
      isToday: false,
      hasEvents: false
    })
  }

  return days
}



// 农历计算相关
const lunarInfo = [
  0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2,
  0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977,
  0x04970, 0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970,
  0x06566, 0x0d4a0, 0x0ea50, 0x06e95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950,
  0x0d4a0, 0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557,
  0x06ca0, 0x0b550, 0x15355, 0x04da0, 0x0a5d0, 0x14573, 0x052d0, 0x0a9a8, 0x0e950, 0x06aa0,
  0x0aea6, 0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0,
  0x096d0, 0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b5a0, 0x195a6,
  0x095b0, 0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570,
  0x04af5, 0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x055c0, 0x0ab60, 0x096d5, 0x092e0,
  0x0c960, 0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5,
  0x0a950, 0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930,
  0x07954, 0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530,
  0x05aa0, 0x076a3, 0x096d0, 0x04bd7, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45,
  0x0b5a0, 0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0
]

const lunarMonths = ['正', '二', '三', '四', '五', '六', '七', '八', '九', '十', '冬', '腊']
const lunarDays = [
  '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
  '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
  '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'
]

// 获取农历日期
const getLunarDate = (day) => {
  try {
    const year = currentDate.value.getFullYear()
    const month = currentDate.value.getMonth()

    let targetYear = year
    let targetMonth = month
    let targetDay = day.date

    if (day.isPrevMonth) {
      if (month === 0) {
        targetYear = year - 1
        targetMonth = 11
      } else {
        targetMonth = month - 1
      }
    } else if (day.isNextMonth) {
      if (month === 11) {
        targetYear = year + 1
        targetMonth = 0
      } else {
        targetMonth = month + 1
      }
    }

    const solarDate = new Date(targetYear, targetMonth, targetDay)
    const lunar = solarToLunar(solarDate)

    if (lunar.day === 1) {
      return `${lunar.month}月`
    }
    return lunar.dayStr
  } catch (error) {
    return ''
  }
}

// 阳历转农历（简化版本）
const solarToLunar = (date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  // 简化的农历计算
  // 使用一个简单的近似算法
  const baseDate = new Date(2024, 0, 1) // 2024年1月1日
  const baseLunar = { year: 2023, month: 11, day: 20 } // 对应农历2023年冬月二十

  const daysDiff = Math.floor((date - baseDate) / (24 * 60 * 60 * 1000))

  // 简单的循环计算
  let lunarYear = baseLunar.year
  let lunarMonth = baseLunar.month
  let lunarDay = baseLunar.day + daysDiff

  // 调整月份和年份
  while (lunarDay > 30) {
    lunarDay -= 30
    lunarMonth += 1
    if (lunarMonth > 12) {
      lunarMonth = 1
      lunarYear += 1
    }
  }

  while (lunarDay <= 0) {
    lunarDay += 30
    lunarMonth -= 1
    if (lunarMonth <= 0) {
      lunarMonth = 12
      lunarYear -= 1
    }
  }

  return {
    year: lunarYear,
    month: lunarMonths[lunarMonth - 1] || '正',
    day: lunarDay,
    dayStr: lunarDays[lunarDay - 1] || '初一'
  }
}

// 监听当前日期和视图类型变化，重新获取数据
watch([currentDate, viewType], () => {
  fetchCalendarData()
}, { immediate: true })

// 组件挂载时获取数据
onMounted(() => {
  fetchCalendarData()
})
</script>

<style scoped>
/* 覆盖Element Plus默认样式 */
:deep(.el-button) {
  border: none;
  background: none;
  padding: 0;
  margin: 0;
}

:deep(.el-button--text) {
  border: none;
  background: none;
  padding: 0;
  margin: 0;
}


/* 日历头部样式 - 使用 module-header 公共样式 */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  margin: 50px 0px 10px 0px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* 日期导航样式 */
.date-navigation {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-btn {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background-color: #fff;
  border: 1px solid #DCDFE6;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
}

.nav-btn:hover {
  background-color: #f5f7fa;
  border-color: #409EFF;
}

.nav-btn .el-icon {
  font-size: 18px;
  color: #606266;
}

.nav-btn:hover .el-icon {
  color: #409EFF;
}

.current-date {
  font-size: 25px;
  font-weight: bold;
  color: #303133;
  min-width: 150px;
  text-align: center;
}

.today-btn {
  background-color: #409EFF;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.today-btn:hover {
  background-color: #337ecc;
}

/* 视图类型选择器样式 */
.view-type-selector {
  width: 170px;
  height: 45px;
}

.custom-toggle-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #E5E7EB;
  border-radius: 8px;
  display: flex;
  align-items: center;
  overflow: hidden;
  padding: 4px;
}

.toggle-slider {
  position: absolute;
  top: 4px;
  left: 4px;
  width: 77px;
  height: 37px;
  background-color: #fff;
  border-radius: 6px;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.slider-right {
  transform: translateX(85px);
}

.toggle-option {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  color: #909399;
  cursor: pointer;
  transition: color 0.3s ease;
  position: relative;
  z-index: 2;
  user-select: none;
}

.toggle-option.active {
  color: #409EFF;
}

.toggle-option:hover:not(.active) {
  color: #666;
}

/* 日历主体样式 */
.calendar-body {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.calendar-week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  border-bottom: 1px solid #EBEEF5;
  padding: 10px 0;
  font: normal normal normal 18px/49px Microsoft JhengHei;
  letter-spacing: -0.09px;
  color: #0A0A0A;
  opacity: 1;
  text-align: left;
}

/* 月视图样式 */
.month-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(6, 1fr);
  flex-grow: 1;
  height: 0;
  /* 配合 flex-grow 使用 */
}

.day-cell {
  height: 120px;
  padding: 6px;
  position: relative;
  border: none;
}

.prev-month,
.next-month {
  background-color: transparent;
}

.prev-month .day-number,
.next-month .day-number {
  font-size: 20px;
  font-weight: normal;
  color: #909399;
}

.prev-month .lunar-date,
.next-month .lunar-date {
  color: #909399;
}

/* 日期容器样式 */
.date-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  min-height: 40px;
}

.day-number {
  font-size: 20px;
  font-weight: normal;
  color: #303133;
  position: relative;
  z-index: 2;
  line-height: 1;
}

.lunar-date {
  font-size: 20px;
  font-weight: normal;
  color: #909399;
  line-height: 1;
}

.event-list {
  margin-top: 10px;
  margin-left: 10px;
}

.event-item {
  font-size: 12px;
  margin-top: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #909399;
  max-width: 100%;
}

.event-name {
  line-height: 19px;
  font: normal normal 300 14px Microsoft JhengHei;
  letter-spacing: -0.07px;
  color: #303030;
  opacity: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.event-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #409EFF;
  margin-right: 4px;
  flex-shrink: 0;
}

.event-more-ellipsis {
  margin-top: 3px;
  cursor: pointer;
  color: #909399;
  font-size: 12px;
  text-align: center;
  line-height: 1;
  padding: 2px 0;
  letter-spacing: 1px;
  transition: color 0.2s ease;
}

.event-more-ellipsis:hover {
  color: #409EFF;
}

.has-events {
  background: #F3F6F9 0% 0% no-repeat padding-box;
  position: relative;
}

.has-events .date-container {
  align-items: center;
  gap: 8px;
  min-height: 40px;
}

.has-events .day-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-weight: normal;
  flex-shrink: 0;
}

.has-events .lunar-date {
  background: none;
  box-shadow: none;
  color: #909399;
  font-weight: normal;
}

/* 年视图样式 */
.year-view {
  height: 100%;
  padding: 20px 0;
  overflow-y: auto;
}

.year-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 0 20px;
}

@media (max-width: 1200px) {
  .year-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .year-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
}

.month-card {
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  padding: 15px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.month-card:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.month-header {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10px;
  color: #303133;
}

.mini-calendar {
  width: 100%;
}

.mini-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.mini-day {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  color: #606266;
  border-radius: 2px;
}

.mini-today {
  background-color: #409EFF;
  color: white;
  font-weight: bold;
  border-radius: 50%;
  width: 20px;
  height: 20px;
}

.mini-has-events {
  background-color: #F2F8FE;
  position: relative;
}

.mini-has-events::after {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: #409EFF;
  border-radius: 50%;
}

.mini-other-month {
  color: #C0C4CC;
}

/* 移除今天的特殊样式，保持与普通日期一致 */

/* 中等屏幕优化 - 1300px到1920px之间等比例缩小 */
@media (min-width: 1300px) and (max-width: 1919px) {
  .day-cell {
    height: 100px;
    padding: 4px;
  }

  .date-container {
    min-height: 30px;
    gap: 6px;
  }

  .day-number {
    font-size: 16px;
  }

  .lunar-date {
    font-size: 16px;
  }

  .current-date {
    font-size: 22px;
    min-width: 140px;
  }

  .nav-btn {
    width: 40px;
    height: 40px;
  }

  .nav-btn .el-icon {
    font-size: 16px;
  }

  .view-type-selector {
    width: 150px;
    height: 40px;
  }

  .toggle-slider {
    width: 67px;
    height: 32px;
  }

  .slider-right {
    transform: translateX(75px);
  }

  .toggle-option {
    font-size: 12px;
  }

  .calendar-week-header {
    font-size: 16px;
    padding: 8px 0;
  }

  .event-item {
    font-size: 11px;
    margin-top: 2px;
  }

  .event-name {
    font-size: 11px;
    line-height: 15px;
  }

  .event-dot {
    width: 4px;
    height: 4px;
    margin-right: 3px;
  }

  .event-list {
    margin-top: 6px;
    margin-left: 6px;
  }

  .has-events .day-number {
    width: 30px;
    height: 30px;
    font-size: 14px;
  }

  .has-events .lunar-date {
    font-size: 14px;
  }

  .event-more-ellipsis {
    font-size: 10px;
    margin-top: 2px;
  }

  /* 年视图优化 */
  .year-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
  }

  .month-card {
    padding: 12px;
  }

  .month-header {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .mini-day {
    width: 16px;
    height: 16px;
    font-size: 10px;
  }

  .mini-today {
    width: 16px;
    height: 16px;
  }
}

/* 1920px及以上保持当前样式，不做任何缩放 */
@media (min-width: 1920px) {
  /* 年视图可以显示更多列 */
  .year-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 20px;
  }
}
</style>
