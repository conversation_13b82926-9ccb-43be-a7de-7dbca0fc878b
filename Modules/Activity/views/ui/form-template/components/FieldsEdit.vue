<template>
  <div class="fields-edit">
    <!-- 已添加的字段列表 -->
    <div class="fields-list">
      <div 
        v-for="field in formData.fields" 
        :key="field.id"
        class="field-item"
      >
        <div class="field-editor">
          <div class="field-input">
            <el-input
              v-model="field.title"
              placeholder="请输入题目"
              class="title-input"
            >
              <template #append>
                <el-select v-model="field.type" class="field-type-select">
                  <el-option label="单选题" value="radio" />
                  <el-option label="多选题" value="checkbox" />
                  <el-option label="是否题" value="boolean" />
                  <el-option label="文字题" value="text" />
                </el-select>
              </template>
            </el-input>
          </div>

          <div class="options-container" v-if="['radio', 'checkbox', 'boolean'].includes(field.type)">
            <!-- 是否题的选项 -->
            <div v-if="field.type === 'boolean'" class="boolean-options">
              <el-radio-group v-model="field.defaultValue" disabled>
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </div>

            <!-- 单选和多选的选项 -->
            <template v-else>
              <div 
                v-for="(option, index) in field.options" 
                :key="index"
                class="option-item"
              >
                <el-radio v-if="field.type === 'radio'" :label="index" disabled>
                  <el-input 
                    v-model="field.options[index]" 
                    placeholder="选项内容"
                    class="option-input"
                  />
                </el-radio>
                <el-checkbox v-else :label="index" disabled>
                  <el-input 
                    v-model="field.options[index]" 
                    placeholder="选项内容"
                    class="option-input"
                  />
                </el-checkbox>
                <el-button 
                  type="danger" 
                  link 
                  @click="removeOption(field, index)"
                  class="remove-option"
                >
                  删除
                </el-button>
              </div>

              <div class="option-item add-option-btn">
                <el-button link type="primary" @click="addOption(field)">
                  <el-icon><Plus /></el-icon> 添加选项
                </el-button>
              </div>
              
              <div class="option-item">
                <el-radio v-if="field.type === 'radio'" :model-value="false">
                  <el-button link type="primary" @click="addOtherOption(field)">
                    添加其他
                  </el-button>
                </el-radio>
                <el-checkbox v-else :model-value="false">
                  <el-button link type="primary" @click="addOtherOption(field)">
                    添加其他
                  </el-button>
                </el-checkbox>
              </div>
            </template>
          </div>

          <div class="field-actions">
            <el-button type="danger" @click="deleteField(field.id)">删除字段</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加字段按钮 -->
    <div class="add-field-button">
      <el-button type="primary" @click="addNewField">
        <el-icon><Plus /></el-icon> 添加字段
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import type { FormTemplateData, FormField } from '../types'

const props = defineProps<{
  formData: FormTemplateData
}>()

const formRef = ref<FormInstance>()

// 创建新字段的默认值
const createDefaultField = (): FormField => ({
  id: Date.now().toString(),
  type: 'radio',
  label: '',
  title: '',
  required: false,
  options: ['选项1', '选项2'],
  defaultValue: null
})

const addNewField = () => {
  props.formData.fields.push(createDefaultField())
}

const addOption = (field: FormField) => {
  if (!field.options) field.options = []
  field.options.push(`选项${field.options.length + 1}`)
}

const addOtherOption = (field: FormField) => {
  if (!field.options) field.options = []
  field.options.push('其他')
}

const removeOption = (field: FormField, index: number) => {
  field.options?.splice(index, 1)
}

const deleteField = (id: string) => {
  const index = props.formData.fields.findIndex(f => f.id === id)
  if (index > -1) {
    props.formData.fields.splice(index, 1)
  }
}

defineExpose({
  validate: () => formRef.value?.validate()
})
</script>

<style scoped>
.fields-edit {
  padding: 20px;
}

.field-editor {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.field-input {
  margin-bottom: 20px;
}

.title-input {
  width: 100%;
}

.field-type-select {
  width: 120px;
}

.options-container {
  padding: 16px;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.option-input {
  width: 300px;
  margin-left: 8px;
}

.remove-option {
  margin-left: 8px;
}

.add-option-btn {
  margin-left: 24px;
}

.field-actions {
  margin-top: 20px;
  text-align: right;
}

.add-field-button {
  margin-top: 20px;
  text-align: center;
}

.boolean-options {
  padding: 16px;
}
</style> 