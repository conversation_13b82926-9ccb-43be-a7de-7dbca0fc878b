<template>
  <el-form
    ref="formRef"
    :model="formData.settings"
    label-width="120px"
  >
    <!-- 提交后操作设置 -->
    <el-form-item label="提交后操作设置">
      <el-select v-model="formData.settings.afterSubmitAction" class="w-full">
        <el-option 
          label="跳转至感谢页面，显示弹窗信息，表单清空并刷新" 
          value="redirect_and_show" 
        />
        <el-option 
          label="显示弹窗信息，表单清空并刷新" 
          value="show_and_refresh" 
        />
      </el-select>
    </el-form-item>

    <el-form-item 
      label="目标页面URL" 
      v-if="formData.settings.afterSubmitAction === 'redirect_and_show'"
    >
      <el-input 
        v-model="formData.settings.redirectUrl" 
        placeholder="请输入目标页面URL"
      />
    </el-form-item>

    <!-- 通知与提醒设置 -->
    <el-form-item label="通知方式">
      <el-radio-group v-model="formData.settings.notificationType">
        <el-radio label="email">E-mail</el-radio>
        <el-radio label="sms">SMS</el-radio>
      </el-radio-group>
    </el-form-item>

    <!-- 邮件模版选择 -->
    <el-form-item label="选择邮件模版">
      <el-select v-model="formData.settings.emailTemplateVersion" class="w-full">
        <el-option label="感谢邮件模版1" value="1" />
      </el-select>
    </el-form-item>

    <!-- 高级设置 -->
    <div class="advanced-settings">
      <h3>高级设置</h3>
      
      <el-form-item label="防止重复提交">
        <el-switch v-model="formData.settings.preventDuplicate" />
      </el-form-item>

      <el-form-item label="限制提交次数">
        <el-switch v-model="formData.settings.submitLimit" />
      </el-form-item>

      <el-form-item 
        label="最大提交次数" 
        v-if="formData.settings.submitLimit"
      >
        <el-input-number
          v-model="formData.settings.maxSubmitCount"
          :min="1"
          :max="99"
          class="w-32"
        />
      </el-form-item>

      <el-form-item label="提交时间范围">
        <el-switch v-model="formData.settings.timeLimit" />
      </el-form-item>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormInstance } from 'element-plus'
import type { FormTemplateData } from '../types'

const props = defineProps<{
  formData: FormTemplateData
}>()

// 确保 settings 对象中包含所有必要的默认值
if (!props.formData.settings) {
  props.formData.settings = {
    afterSubmitAction: 'show_and_refresh',
    redirectUrl: '',
    notificationType: 'email',
    emailTemplateVersion: '1',
    preventDuplicate: false,
    submitLimit: false,
    maxSubmitCount: 1,
    timeLimit: false
  }
}

const formRef = ref<FormInstance>()

defineExpose({
  validate: () => formRef.value?.validate()
})
</script>

<style scoped>
.advanced-settings {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.advanced-settings h3 {
  margin-bottom: 16px;
  font-size: 16px;
  color: #606266;
}

.w-full {
  width: 100%;
}

.w-32 {
  width: 128px;
}
</style> 