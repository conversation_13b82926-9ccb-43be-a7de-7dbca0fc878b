<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="120px"
  >
    <el-form-item label="表单标题" prop="name" required>
      <el-input v-model="formData.name" placeholder="请输入" />
    </el-form-item>
    
    <el-form-item label="表单描述" prop="description">
      <el-input
        v-model="formData.description"
        type="textarea"
        :rows="4"
        placeholder="请输入"
      />
    </el-form-item>
    
    <el-form-item label="使用场景选择" prop="scene" required>
      <el-select v-model="formData.scene" placeholder="活动报名，活动反馈">
        <el-option label="活动报名" value="signup" />
        <el-option label="活动反馈" value="feedback" />
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormInstance } from 'element-plus'
import type { FormTemplateData } from '../types'

const props = defineProps<{
  formData: FormTemplateData
}>()

const formRef = ref<FormInstance>()

const rules = {
  name: [{ required: true, message: '请输入表单标题', trigger: 'blur' }],
  scene: [{ required: true, message: '请选择使用场景', trigger: 'change' }]
}

defineExpose({
  validate: () => formRef.value?.validate()
})
</script> 