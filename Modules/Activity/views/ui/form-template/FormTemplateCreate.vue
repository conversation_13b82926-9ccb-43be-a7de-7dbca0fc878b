<template>
  <div class="bwms-module">
    <div class="module-header">
      <h2>新建表单模版</h2>
    </div>

    <div class="module-con">
      <div class="box">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基础信息" name="basic">
            <BasicInfo ref="basicInfoRef" v-model:formData="formData" />
          </el-tab-pane>
          <el-tab-pane label="字段编辑" name="fields">
            <FieldsEdit ref="fieldsEditRef" v-model:formData="formData" />
          </el-tab-pane>
          <el-tab-pane label="表单提交设置" name="settings">
            <SubmitSettings ref="submitSettingsRef" v-model:formData="formData" />
          </el-tab-pane>
        </el-tabs>

        <div class="form-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSubmit">下一步</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import BasicInfo from './components/BasicInfo.vue'
import FieldsEdit from './components/FieldsEdit.vue'
import SubmitSettings from './components/SubmitSettings.vue'
import type { FormTemplateData } from './types'

const router = useRouter()
const activeTab = ref('basic')

const basicInfoRef = ref()
const fieldsEditRef = ref()
const submitSettingsRef = ref()

const formData = ref<FormTemplateData>({
  name: '',
  description: '',
  scene: '',
  fields: [],
  settings: {
    submitLimit: false,
    submitCount: 1,
    needAudit: false
  }
})

const handleCancel = () => {
  router.back()
}

const handleSubmit = async () => {
  try {
    if (activeTab.value === 'basic') {
      const valid = await basicInfoRef.value?.validate()
      if (valid) {
        activeTab.value = 'fields'
      }
    } else if (activeTab.value === 'fields') {
      const valid = await fieldsEditRef.value?.validate()
      if (valid) {
        activeTab.value = 'settings'
      }
    } else {
      const valid = await submitSettingsRef.value?.validate()
      if (valid) {
        await saveFormTemplate()
      }
    }
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

const saveFormTemplate = async () => {
  try {
    // TODO: 实现保存表单模板的API调用
    await fetch('/api/activity/form-templates', {
      method: 'POST',
      body: JSON.stringify(formData.value)
    })
    ElMessage.success('创建成功')
    router.push({ name: 'FormTemplateList' })
  } catch (error) {
    ElMessage.error('创建失败')
  }
}
</script>

<style scoped>
.form-actions {
  margin-top: 24px;
  text-align: right;
}
</style> 