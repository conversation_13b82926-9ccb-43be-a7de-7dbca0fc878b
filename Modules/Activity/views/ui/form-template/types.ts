export interface FormTemplateItem {
  id: number
  name: string
  description: string
  created_at: string
  updated_at: string
}

export interface FormTemplateData {
  name: string
  description: string
  scene: string
  fields: FormField[]
  settings: FormSettings
}

export interface FormField {
  id: string
  type: string
  label: string
  title?: string
  required: boolean
  options?: string[]
  // 其他字段属性
}

export interface FormSettings {
  afterSubmitAction: 'redirect_and_show' | 'show_and_refresh'
  redirectUrl: string
  notificationType: 'email' | 'sms'
  emailTemplateVersion: string
  preventDuplicate: boolean
  submitLimit: boolean
  maxSubmitCount: number
  timeLimit: boolean
} 