<template>
    <div class="bwms-module">
      <div class="module-header">
       
        <el-button type="primary" @click="$router.push({ name: 'FormTemplateCreate' })">
          创建表单模板
        </el-button>
      </div>
  
    <div class="module-con">
        <div class="box">
            <div class="table-header">
                <div class="left">
                    <el-input
                        v-model="searchKeyword"
                        placeholder="请输入"
                        class="search-input"
                        :prefix-icon="Search"
                    >
                        <template #append>
                            <el-button :icon="Filter" />
                        </template>
                    </el-input>
                </div>
                <div class="right">
                    <el-button>移动至</el-button>
                    <el-button>批量复制</el-button>
                </div>
            </div>
        <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%"
        >
        <template #empty>
          <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
        </template>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="模板名称" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="created_at" label="创建时间" width="180" />
        <el-table-column prop="updated_at" label="更新时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              @click="$router.push({ name: 'FormTemplateEdit', params: { id: row.id }})"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
        </div>
    </div>
    </div>
  </template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Filter } from '@element-plus/icons-vue'
import type { FormTemplateItem } from './types'

const tableData = ref<FormTemplateItem[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')

const fetchData = async () => {
  loading.value = true
  try {
    // TODO: 替换为实际的API调用
    const response = await fetch(`/api/activity/form-templates?page=${currentPage.value}&limit=${pageSize.value}`)
    const data = await response.json()
    tableData.value = data.items
    total.value = data.total
  } catch (error) {
    ElMessage.error('获取表单模板列表失败')
  } finally {
    loading.value = false
  }
}

const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除该表单模板吗？', '提示', {
      type: 'warning'
    })
    // TODO: 替换为实际的删除API调用
    await fetch(`/api/activity/form-templates/${id}`, { method: 'DELETE' })
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchData()
}

onMounted(() => {
  // fetchData()
})
</script>



<style scoped>
.form-template-list {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.left {
    display: flex;
    align-items: center;
}

.right {
    display: flex;
    gap: 8px;
}

.search-input {
    width: 240px;
}
</style> 