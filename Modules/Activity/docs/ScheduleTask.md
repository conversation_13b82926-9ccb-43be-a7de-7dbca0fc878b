# 活动状态更新计划任务

## 概述

活动模块提供了一个自动化的计划任务来检测和更新活动状态。该任务会每10分钟执行一次，自动处理两种状态转换：
1. 将到达发布时间的活动状态从 `created` 更新为 `published`
2. 将已过期的活动状态从 `published` 更新为 `completed`

## 功能特性

### 🕒 执行频率
- **自动执行**: 每10分钟执行一次
- **手动执行**: 支持通过命令行手动触发
- **防重叠**: 使用锁机制防止同时运行多个实例

### 🎯 检测逻辑

#### 活动发布判断规则
- 检查状态为 `created` 的活动
- 当当前时间 >= `publish_time` 时，活动状态更新为 `published`

#### 活动过期判断规则
1. **有时间安排的活动**: 检查活动的所有时间段，当最晚的结束时间过期时，活动被标记为过期
2. **无时间安排的活动**: 检查活动的报名截止时间，当截止时间过期时，活动被标记为过期

#### 状态更新
- 将状态为 `created` 且到达发布时间的活动更新为 `published`
- 将状态为 `published` 的过期活动更新为 `completed`
- 只处理未删除的活动（`deleted_at = 0`）

### 🔧 技术实现

#### 文件结构
```
Modules/Activity/
├── Commands/
│   └── ActivityStatusUpdateCommand.php     # 计划任务命令
├── Services/
│   └── ActivityStatusService.php           # 业务逻辑服务
├── Enums/
│   └── ActivityErrorCode.php               # 错误码定义
├── Lang/
│   ├── zh_CN/
│   │   ├── enums.php                       # 错误码中文翻译
│   │   └── schedule.php                    # 计划任务中文翻译
│   ├── en/
│   │   ├── enums.php                       # 错误码英文翻译
│   │   └── schedule.php                    # 计划任务英文翻译
│   └── zh_HK/
│       ├── enums.php                       # 错误码繁体中文翻译
│       └── schedule.php                    # 计划任务繁体中文翻译
└── Providers/
    └── ActivityServiceProvider.php         # 服务提供者
```

#### 核心组件

1. **ActivityStatusUpdateCommand**: 计划任务命令类
   - 处理命令行参数和选项
   - 管理锁机制防止重复执行
   - 记录执行时间和结果

2. **ActivityStatusService**: 业务逻辑服务类
   - 批量更新活动状态（发布和完成）
   - 检查活动是否过期和是否到达发布时间
   - 提供即将发布和即将过期活动查询

3. **ActivityErrorCode**: 错误码枚举
   - 定义活动状态更新相关错误码
   - 支持多语言错误信息

## 使用方法

### 自动执行
任务会自动在系统中每10分钟执行一次，无需手动干预。

### 手动执行
```bash
# 正常执行
php artisan activity:status:update

# 强制执行（忽略锁机制）
php artisan activity:status:update --force
```

### 查看执行日志
任务执行的详细信息会记录在Laravel日志中：
- 成功发布的活动信息
- 成功完成的活动信息
- 错误和异常信息
- 执行时间和更新统计

## 配置说明

### 计划任务配置
在 `ActivityServiceProvider` 中注册的计划任务配置：

```php
'activity.status_update' => [
    'name' => T('Activity::schedule.activity_status_update'),
    'command' => 'activity:status:update',
    'spec' => '*/10 * * * *', // 每10分钟执行
    'description' => T('Activity::schedule.activity_status_update_desc'),
    'type' => 'artisan',
    'status' => 'waiting',
    'platform' => PHP_OS_FAMILY === 'Windows' ? 'windows' : 'linux',
    'creator_id' => 1,
    'lang' => config('app.locale', 'zh_CN')
],
```

### Laravel 调度器配置
在 `app/Console/Kernel.php` 中的调度配置：

```php
$schedule->command('activity:status:update')
         ->everyTenMinutes()
         ->withoutOverlapping()
         ->runInBackground();
```

## 监控和维护

### 性能考虑
- 任务使用了锁机制防止重叠执行
- 批量处理活动，避免逐个查询
- 使用事务确保数据一致性

### 错误处理
- 单个活动更新失败不会影响其他活动
- 所有错误都会记录详细日志
- 支持多语言错误信息

### 扩展功能
- 支持查询即将发布的活动（用于提醒功能）
- 支持查询即将过期的活动（用于提醒功能）
- 可以轻松扩展支持其他状态转换
- 预留了邮件通知等扩展接口

## 注意事项

1. **时区问题**: 确保服务器时区设置正确
2. **数据库性能**: 大量活动时考虑添加索引优化
3. **状态转换**: 处理 `created` -> `published` 和 `published` -> `completed` 的转换
4. **锁机制**: 使用Redis或数据库缓存作为锁存储
5. **日志管理**: 定期清理执行日志避免磁盘空间不足

## 故障排除

### 常见问题
1. **命令未注册**: 确保 `ActivityServiceProvider` 正确加载
2. **锁机制失效**: 检查缓存配置是否正常
3. **时间判断错误**: 验证活动时间数据格式
4. **权限问题**: 确保Laravel有数据库写入权限

### 调试方法
```bash
# 查看命令是否注册
php artisan list | grep activity

# 手动执行查看详细输出
php artisan activity:status:update -v

# 强制执行忽略锁
php artisan activity:status:update --force
``` 