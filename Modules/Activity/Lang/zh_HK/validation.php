<?php

return [
    'activity' => [
        'title' => [
            'required' => '活動標題不能為空',
            'string' => '活動標題必須為字符串',
            'max' => '活動標題不能超過限定長度'
        ],
        'category' => [
            'required' => '活動分類不能為空',
            'string' => '活動分類必須為字符串',
            'max' => '活動分類不能超過限定長度'
        ],
        'localized_id' => [
            'required' => '本地化ID不能為空',
            'integer' => '本地化ID必須為整數'
        ],
        'organizer_email' => [
            'email' => '組織者電子郵件格式不正確'
        ],
        'type' => [
            'required' => '活動類型不能為空',
            'in' => '活動類型值無效'
        ],
        'repeat_pattern' => [
            'required' => '重複模式不能為空',
            'in' => '重複模式值無效'
        ],
        'repeat_interval' => [
            'required' => '重複間隔不能為空',
            'min' => '重複間隔不能小於最小值'
        ],
        'start_time' => [
            'required' => '開始時間不能為空',
            'date' => '開始時間格式不正確'
        ],
        'end_time' => [
            'required' => '結束時間不能為空',
            'date' => '結束時間格式不正確',
            'after' => '結束時間必須晚於開始時間'
        ],
        'registration_deadline' => [
            'required' => '報名截止時間不能為空',
            'date' => '報名截止時間格式不正確',
            'before' => '報名截止時間必須早於活動開始時間'
        ],
        // 組織者信息
        'organizer_name' => [
            'required' => '組織者姓名不能為空',
            'string' => '組織者姓名必須為字符串',
            'max' => '組織者姓名不能超過100個字符'
        ],
        'organizer_phone' => [
            'required' => '組織者電話不能為空',
            'string' => '組織者電話必須為字符串',
            'max' => '組織者電話不能超過20個字符'
        ],
        'organizer_email' => [
            'required' => '組織者郵箱不能為空',
            'email' => '組織者郵箱格式不正確',
            'max' => '組織者郵箱不能超過100個字符'
        ],
        // 活動地點
        'location' => [
            'required' => '活動地點不能為空',
            'string' => '活動地點必須為字符串',
            'max' => '活動地點不能超過200個字符'
        ],
        'address' => [
            'required' => '詳細地址不能為空',
            'string' => '詳細地址必須為字符串',
            'max' => '詳細地址不能超過500個字符'
        ],
        'latitude' => [
            'numeric' => '緯度必須為數字',
            'between' => '緯度必須在-90到90之間'
        ],
        'longitude' => [
            'numeric' => '經度必須為數字',
            'between' => '經度必須在-180到180之間'
        ],
        // 活動時間
        'start_date' => [
            'required' => '開始日期不能為空',
            'date' => '開始日期必須為有效日期'
        ],
        'end_date' => [
            'required_if' => '當重複類型為一次時，結束日期不能為空',
            'date' => '結束日期必須為有效日期'
        ],
        // 報名設置
        'registration_limit_type' => [
            'required' => '報名限制類型不能為空',
            'integer' => '報名限制類型必須為整數',
            'in' => '報名限制類型無效'
        ],
        'registration_limit' => [
            'required_if' => '當啟用報名限制時，報名限制數量不能為空',
            'integer' => '報名限制數量必須為整數',
            'min' => '報名限制數量不能小於0'
        ],
        'target_participants' => [
            'integer' => '目標參與人數必須為整數',
            'min' => '目標參與人數不能小於0'
        ],
        'registration_type' => [
            'required' => '報名類型不能為空',
            'integer' => '報名類型必須為整數',
            'in' => '報名類型無效'
        ],
        'public_limit' => [
            'required' => '公開報名限制不能為空',
            'integer' => '公開報名限制必須為整數',
            'min' => '公開報名限制不能小於0'
        ],
        'hidden_limit' => [
            'required' => '隱藏報名限制不能為空',
            'integer' => '隱藏報名限制必須為整數',
            'min' => '隱藏報名限制不能小於0'
        ],
        'reserved_limit' => [
            'required' => '預留限制不能為空',
            'integer' => '預留限制必須為整數',
            'min' => '預留限制不能小於0'
        ],
        'is_fee_required' => [
            'required' => '是否收費不能為空',
            'boolean' => '是否收費必須為布爾值'
        ],
        'status' => [
            'required' => '狀態不能為空',
            'in' => '狀態無效'
        ],
        'publish_time' => [
            'required' => '發布時間不能為空',
            'date' => '發布時間必須為有效日期',
            'before' => '發布時間必須早於活動開始時間'
        ],
        // 票務相關
        'ticket' => [
            'required_if' => '當活動收費時，票務信息不能為空',
            'array' => '票務信息必須為數組'
        ],
        // 活動組
        'groups' => [
            'array' => '活動組必須為數組'
        ],
        'group' => [
            'name' => [
                'string' => '活動組名稱必須為字符串',
                'max' => '活動組名稱不能超過100個字符'
            ],
            'start_time' => [
                'date' => '活動組開始時間必須為有效日期'
            ],
            'end_time' => [
                'date' => '活動組結束時間必須為有效日期',
                'after' => '活動組結束時間必須晚於開始時間'
            ]
        ],
        // 規則
        'rule_id' => [
             'integer' => '規則ID必須為整數'
         ],
    ],
    'activity_group' => [
        'code' => [
            'required' => '活動組編碼不能為空',
            'string' => '活動組編碼必須為字符串',
            'max' => '活動組編碼不能超過20個字符',
            'unique' => '活動組編碼已存在'
        ],
        'name' => [
            'required' => '活動組名稱不能為空',
            'max' => '活動組名稱不能超過50個字符'
        ],
        'max_members' => [
            'required' => '最大成員數不能為空',
            'integer' => '最大成員數必須為整數',
            'min' => '最大成員數不能小於0'
        ],
        'can_register' => [
            'required' => '註冊狀態不能為空',
            'integer' => '註冊狀態必須為整數',
            'in' => '註冊狀態值只能為0或1'
        ],
        'start_time' => [
            'date' => '開始時間格式不正確'
        ],
        'end_time' => [
            'date' => '結束時間格式不正確',
            'after' => '結束時間必須晚於開始時間'
        ]
    ],
    'copy_activity' => [
        'title' => [
            'string' => '活動標題必須為字符串',
            'max' => '活動標題最大長度為255個字符',
        ],
        'copy_schedules' => [
            'boolean' => '複製時間安排必須為布爾值',
        ],
        'copy_tickets' => [
            'boolean' => '複製票務信息必須為布爾值',
        ],
        'copy_groups' => [
            'boolean' => '複製分組信息必須為布爾值',
        ],
    ],

    'ticket' => [
        'name' => [
            'required' => '票務名稱不能為空',
            'string' => '票務名稱必須為字符串',
            'max' => '票務名稱不能超過100個字符'
        ],
        'code' => [
            'required' => '票務編碼不能為空',
            'string' => '票務編碼必須為字符串',
            'max' => '票務編碼不能超過50個字符'
        ],
        'quota' => [
            'required' => '票務配額不能為空',
            'integer' => '票務配額必須為整數',
            'min' => '票務配額不能小於0'
        ],
        'subtypes' => [
            'array' => '票務子類型必須為數組'
        ],
        'subtypes.*.name' => [
            'required' => '子類型名稱不能為空',
            'string' => '子類型名稱必須為字符串',
            'max' => '子類型名稱不能超過100個字符'
        ],
        'subtypes.*.price' => [
            'required' => '子類型價格不能為空',
            'numeric' => '子類型價格必須為數字',
            'min' => '子類型價格不能小於0'
        ],
        'subtypes.*.sort' => [
            'required' => '子類型排序不能為空',
            'integer' => '子類型排序必須為整數',
            'min' => '子類型排序不能小於0'
        ],
        'subtypes.*.start_date' => [
            'required' => '子類型開始日期不能為空',
            'date' => '子類型開始日期必須為有效日期'
        ],
        'subtypes.*.end_date' => [
            'required' => '子類型結束日期不能為空',
            'date' => '子類型結束日期必須為有效日期',
            'after' => '子類型結束日期必須晚於開始日期'
        ]
    ]
];
