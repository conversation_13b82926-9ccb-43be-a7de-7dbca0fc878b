<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-04-11 17:18:54
 * @LastEditTime: 2025-06-07 15:39:00
 * @LastEditors: Chiron
 * @Description: 
 */
use Modules\Activity\Enums\ActivityErrorCode;

return [
    ActivityErrorCode::class => [
        ActivityErrorCode::ACTIVITY_NOT_FOUND->name => '活動不存在',
        ActivityErrorCode::ACTIVITY_ALREADY_ENDED->name => '活動已結束',
        ActivityErrorCode::ACTIVITY_NOT_STARTED->name => '活動未開始',
        ActivityErrorCode::ACTIVITY_DELETE_FAILED->name => '刪除失敗',
        ActivityErrorCode::ACTIVITY_COPY_FAILED->name => '活動複製失敗',
        ActivityErrorCode::FORM_TEMPLATE_NOT_FOUND->name => '表單模板不存在',
        ActivityErrorCode::FORM_TEMPLATE_CREATE_FAILED->name => '表單模板創建失敗',
        ActivityErrorCode::FORM_TEMPLATE_UPDATE_FAILED->name => '表單模板更新失敗',
        ActivityErrorCode::FORM_TEMPLATE_DELETE_FAILED->name => '表單模板刪除失敗',
        ActivityErrorCode::RULE_NOT_FOUND->name => '規則不存在',
        ActivityErrorCode::RULE_SAVE_FAILED->name => '規則保存失敗',
        ActivityErrorCode::PARTICIPANT_NOT_FOUND->name => '參與者不存在',
        ActivityErrorCode::ACTIVITY_STATUS_UPDATE_FAILED->name => '活動狀態更新失敗',
        ActivityErrorCode::ACTIVITY_SCHEDULE_NOT_FOUND->name => '活動時間安排不存在',
    ],
];