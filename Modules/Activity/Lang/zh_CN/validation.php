<?php

return [
    'activity' => [
        'title' => [
            'required' => '活动标题不能为空',
            'string' => '活动标题必须为字符串',
            'max' => '活动标题不能超过限定长度'
        ],
        'category' => [
            'required' => '活动分类不能为空',
            'string' => '活动分类必须为字符串',
            'max' => '活动分类不能超过限定长度'
        ],
        'localized_id' => [
            'required' => '本地化ID不能为空',
            'integer' => '本地化ID必须为整数'
        ],
        // 组织者信息
        'organizer_last_name' => [
            'string' => '组织者姓氏必须为字符串',
            'max' => '组织者姓氏不能超过50个字符'
        ],
        'organizer_first_name' => [
            'string' => '组织者名字必须为字符串',
            'max' => '组织者名字不能超过50个字符'
        ],
        'organizer_email' => [
            'string' => '组织者邮箱必须为字符串',
            'email' => '组织者邮箱格式不正确',
            'max' => '组织者邮箱不能超过100个字符'
        ],
        // 活动地点
        'type' => [
            'required' => '活动类型不能为空',
            'string' => '活动类型必须为字符串',
            'in' => '活动类型值无效'
        ],
        'location' => [
            'string' => '活动地点必须为字符串',
            'max' => '活动地点不能超过255个字符'
        ],
        'address' => [
            'string' => '活动地址必须为字符串',
            'max' => '活动地址不能超过255个字符'
        ],
        'online_platform' => [
            'string' => '在线平台必须为字符串',
            'max' => '在线平台不能超过20个字符'
        ],
        'online_platform_url' => [
            'string' => '在线平台链接必须为字符串',
            'url' => '在线平台链接必须为有效的URL',
            'max' => '在线平台链接不能超过255个字符'
        ],
        // 活动时间
        'repeat_pattern' => [
            'required' => '重复模式不能为空',
            'in' => '重复模式值无效'
        ],
        'repeat_interval' => [
            'required' => '重复间隔不能为空',
            'min' => '重复间隔不能小于最小值'
        ],
        'start_time' => [
            'required' => '开始时间不能为空',
            'date' => '开始时间格式不正确'
        ],
        'end_time' => [
            'required' => '结束时间不能为空',
            'date' => '结束时间格式不正确',
            'after' => '结束时间必须晚于开始时间'
        ],
        'schedule' => [
            'required' => '活动时间安排不能为空',
            'array' => '活动时间安排必须为数组'
        ],
        'schedule.repeat_type' => [
            'required' => '重复类型不能为空',
            'string' => '重复类型必须为字符串',
            'in' => '重复类型值无效'
        ],
        'schedule.start_date' => [
            'required_if' => '当重复类型为单次时，开始日期不能为空',
            'date' => '开始日期格式不正确'
        ],
        'schedule.end_date' => [
            'required_if' => '当重复类型为单次时，结束日期不能为空',
            'date' => '结束日期格式不正确'
        ],
        // 报名设置
        'registration_deadline' => [
            'required' => '报名截止时间不能为空',
            'date' => '报名截止时间格式不正确',
            'before' => '报名截止时间必须早于活动开始时间'
        ],
        'registration_limit_type' => [
            'required' => '报名限制类型不能为空',
            'integer' => '报名限制类型必须为整数',
            'in' => '报名限制类型值无效'
        ],
        'registration_limit' => [
            'required_if' => '当启用报名限制时，报名人数上限不能为空',
            'integer' => '报名人数上限必须为整数',
            'min' => '报名人数上限不能小于0'
        ],
        'target_participants' => [
            'integer' => '目标参与人数必须为整数',
            'min' => '目标参与人数不能小于0'
        ],
        'registration_type' => [
            'required' => '报名类型不能为空',
            'integer' => '报名类型必须为整数',
            'in' => '报名类型值无效'
        ],
        'public_limit' => [
            'required' => '公开报名人数上限不能为空',
            'integer' => '公开报名人数上限必须为整数',
            'min' => '公开报名人数上限不能小于0'
        ],
        'hidden_limit' => [
            'required' => '隐藏报名人数上限不能为空',
            'integer' => '隐藏报名人数上限必须为整数',
            'min' => '隐藏报名人数上限不能小于0'
        ],
        'reserved_limit' => [
            'required' => '预留名额不能为空',
            'integer' => '预留名额必须为整数',
            'min' => '预留名额不能小于0'
        ],
        'is_fee_required' => [
            'required' => '是否收费不能为空',
            'boolean' => '是否收费必须为布尔值'
        ],
        'status' => [
            'required' => '状态不能为空',
            'in' => '状态值无效'
        ],
        'publish_time' => [
            'required' => '发布时间不能为空',
            'date' => '发布时间格式不正确',
            'before' => '发布时间必须早于活动开始时间'
        ],
        // 票务相关
        'ticket' => [
            'required_if' => '当活动为收费活动时，票务信息不能为空',
            'array' => '票务信息必须为数组'
        ],
        // 活动组
        'groups' => [
            'array' => '活动组必须为数组'
        ],
        'group' => [
            'name' => [
                'string' => '活动组名称必须为字符串',
                'max' => '活动组名称不能超过100个字符'
            ],
            'start_time' => [
                'date' => '活动组开始时间格式不正确'
            ],
            'end_time' => [
                'date' => '活动组结束时间格式不正确',
                'after' => '活动组结束时间必须晚于开始时间'
            ]
        ],
        // 规则
        'rule_id' => [
            'integer' => '规则ID必须为整数'
        ],
    ],
    'activity_group' => [
        'code' => [
            'required' => '活动组编码不能为空',
            'string' => '活动组编码必须为字符串',
            'max' => '活动组编码不能超过50个字符',
            'unique' => '活动组编码已存在'
        ],
        'name' => [
            'required' => '活动组名称不能为空',
            'max' => '活动组名称不能超过50个字符'
        ],
        'max_members' => [
            'required' => '最大成员数不能为空',
            'integer' => '最大成员数必须为整数',
            'min' => '最大成员数不能小于0'
        ],
        'can_register' => [
            'required' => '注册状态不能为空',
            'integer' => '注册状态必须为整数',
            'in' => '注册状态值只能为0或1'
        ],
        'start_time' => [
            'date' => '开始时间格式不正确'
        ],
        'end_time' => [
            'date' => '结束时间格式不正确',
            'after' => '结束时间必须晚于开始时间'
        ]
    ],
    'copy_activity' => [
        'title' => [
            'string' => '活动标题必须为字符串',
            'max' => '活动标题最大长度为255个字符',
        ],
        'copy_schedules' => [
            'boolean' => '复制时间安排必须为布尔值',
        ],
        'copy_tickets' => [
            'boolean' => '复制票务信息必须为布尔值',
        ],
        'copy_groups' => [
            'boolean' => '复制分组信息必须为布尔值',
        ],
    ],
    'ticket' => [
        'name' => [
            'string' => '票务名称必须为字符串',
            'max' => '票务名称不能超过100个字符'
        ],
        'code' => [
            'string' => '票务编码必须为字符串',
            'max' => '票务编码不能超过100个字符'
        ],
        'quota' => [
            'required_if' => '当活动为收费活动时，票务配额不能为空',
            'integer' => '票务配额必须为整数'
        ],
        'subtypes' => [
            'required_if' => '当活动为收费活动时，票务子类型不能为空',
            'array' => '票务子类型必须为数组'
        ],
        'subtype' => [
            'name' => [
                'string' => '票务子类型名称必须为字符串'
            ],
            'start_date' => [
                'date' => '票务子类型开始日期格式不正确'
            ],
            'end_date' => [
                'date' => '票务子类型结束日期格式不正确'
            ]
        ]
    ],
];
