<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-04-11 17:16:00
 * @LastEditTime: 2025-04-11 17:17:07
 * @LastEditors: Chiron
 * @Description: 
 */

use Modules\Activity\Enums\ActivityErrorCode;

return [
    ActivityErrorCode::class => [
        ActivityErrorCode::ACTIVITY_NOT_FOUND->name => '活动不存在',
        ActivityErrorCode::ACTIVITY_ALREADY_ENDED->name => '活动已结束',
        ActivityErrorCode::ACTIVITY_NOT_STARTED->name => '活动未开始',
        ActivityErrorCode::ACTIVITY_DELETE_FAILED->name => '删除失败',
        ActivityErrorCode::ACTIVITY_COPY_FAILED->name => '活动复制失败',
        ActivityErrorCode::FORM_TEMPLATE_NOT_FOUND->name => '表单模板不存在',
        ActivityErrorCode::FORM_TEMPLATE_CREATE_FAILED->name => '表单模板创建失败',
        ActivityErrorCode::FORM_TEMPLATE_UPDATE_FAILED->name => '表单模板更新失败',
        ActivityErrorCode::FORM_TEMPLATE_DELETE_FAILED->name => '表单模板删除失败',
        ActivityErrorCode::RULE_NOT_FOUND->name => '规则不存在',
        ActivityErrorCode::RULE_SAVE_FAILED->name => '规则保存失败',
        ActivityErrorCode::PARTICIPANT_NOT_FOUND->name => '参与者不存在',
        ActivityErrorCode::ACTIVITY_STATUS_UPDATE_FAILED->name => '活动状态更新失败',
        ActivityErrorCode::ACTIVITY_SCHEDULE_NOT_FOUND->name => '活动时间安排不存在',
    ],
];