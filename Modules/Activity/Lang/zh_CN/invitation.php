<?php

return [
    'nav' => [
        'invitation' => '邀请管理',
        'invitation_list' => '邀请列表',
        'invitation_members' => '邀请成员',
    ],
    'permission' => [
        'invitation' => '邀请管理',
        'invitation_list' => '邀请列表',
        'invitation_members' => '邀请成员',
    ],
    'fields' => [
        'title' => '邀请标题',
        'description' => '邀请描述',
        'is_default' => '默认列表',
        'is_group' => '分配群组',
        'group_id' => '群组',
        'status' => '状态',
        'code' => '邀请码',
        'name' => '姓名',
        'phone' => '手机号',
        'email' => '邮箱',
        'company' => '公司',
        'position' => '职位',
        'creator_id' => '创建人',
        'created_at' => '创建时间',
        'updated_at' => '更新时间',
    ],
    'example' => [
        'name' => '张三',
        'phone' => '13800138000',
        'email' => 'zhang<PERSON>@example.com',
        'company' => '示例公司',
        'position' => '产品经理',
    ],
    'buttons' => [
        'create' => '创建邀请',
        'edit' => '编辑邀请',
        'delete' => '删除邀请',
        'import' => '导入成员',
        'export' => '导出成员',
        'export_template' => '导出模板',
        'send' => '发送邀请',
        'add_member' => '添加成员',
    ],
    'status' => [
        'enabled' => '启用',
        'disabled' => '禁用',
        'pending' => '待接受',
        'accepted' => '已接受',
        'declined' => '已拒绝',
        'expired' => '已过期',
    ],
    'member_status' => [
        'not_replied' => '未回复',
        'registered' => '已报名',
        'declined' => '已拒绝',
    ],
    'messages' => [
        'create_success' => '邀请创建成功',
        'update_success' => '邀请更新成功',
        'delete_success' => '邀请删除成功',
        'not_found' => '邀请不存在',
        'import_success' => '成功导入 {success_count} 条数据，失败 {fail_count} 条',
        'export_success' => '导出成功',
        'export_error' => '导出数据失败，请稍后再试',
        'send_success' => '成功发送 {success_count} 条邀请，失败 {fail_count} 条',
        'no_data' => '没有可导出的数据',
        'file_format_error' => '不支持的文件格式',
        'file_parse_error' => '文件解析失败',
        'param_error' => '参数错误',
        'member_not_found' => '成员不存在',
        'save_error' => '保存失败',
    ],
    'validation' => [
        'activity_id' => [
            'required' => '活动ID不能为空',
            'integer' => '活动ID必须为整数',
            'exists' => '活动不存在',
        ],
        'title' => [
            'required' => '邀请标题不能为空',
            'string' => '邀请标题必须为字符串',
            'max' => '邀请标题最大长度为64个字符',
        ],
        'description' => [
            'string' => '邀请描述必须为字符串',
        ],
        'is_default' => [
            'integer' => '默认列表标志必须为整数',
            'in' => '默认列表标志值不正确',
        ],
        'is_group' => [
            'integer' => '分配群组标志必须为整数',
            'in' => '分配群组标志值不正确',
        ],
        'group_id' => [
            'integer' => '群组ID必须为整数',
            'exists' => '群组不存在',
        ],
        'status' => [
            'integer' => '状态必须为整数',
            'in' => '状态值不正确',
        ],
        'members' => [
            'array' => '成员数据必须为数组',
        ],
        'member' => [
            'id' => [
                'integer' => '成员ID必须为整数',
            ],
            'name' => [
                'required' => '成员姓名不能为空',
                'string' => '成员姓名必须为字符串',
                'max' => '成员姓名最大长度为64个字符',
            ],
            'phone' => [
                'string' => '成员手机号必须为字符串',
                'max' => '成员手机号最大长度为30个字符',
            ],
            'email' => [
                'email' => '成员邮箱格式不正确',
                'max' => '成员邮箱最大长度为100个字符',
            ],
            'company' => [
                'string' => '成员公司必须为字符串',
            ],
            'position' => [
                'string' => '成员职位必须为字符串',
            ],
        ],

        'member_exist' => '成员已存在',
    ],
]; 