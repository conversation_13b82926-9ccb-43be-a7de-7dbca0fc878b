<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-04-11 17:17:36
 * @LastEditTime: 2025-06-07 19:19:22
 * @LastEditors: <PERSON>ron
 * @Description: 
 */

use Modules\Activity\Enums\ActivityErrorCode;

return [
    ActivityErrorCode::class => [
        ActivityErrorCode::ACTIVITY_NOT_FOUND->name => 'Activity not found',
        ActivityErrorCode::ACTIVITY_ALREADY_ENDED->name => 'Activity already ended',
        ActivityErrorCode::ACTIVITY_NOT_STARTED->name => 'Activity not started',
        ActivityErrorCode::ACTIVITY_DELETE_FAILED->name => 'Activity delete failed',
        ActivityErrorCode::ACTIVITY_COPY_FAILED->name => 'Activity copy failed',
        ActivityErrorCode::FORM_TEMPLATE_NOT_FOUND->name => 'Form template not found',
        ActivityErrorCode::FORM_TEMPLATE_CREATE_FAILED->name => 'Failed to create form template',
        ActivityErrorCode::FORM_TEMPLATE_UPDATE_FAILED->name => 'Failed to update form template',
        ActivityErrorCode::FORM_TEMPLATE_DELETE_FAILED->name => 'Failed to delete form template',
        ActivityErrorCode::RULE_NOT_FOUND->name => 'Rule not found',
        ActivityErrorCode::RULE_SAVE_FAILED->name => 'Rule save failed',
        ActivityErrorCode::PARTICIPANT_NOT_FOUND->name => 'Participant not found',
        ActivityErrorCode::ACTIVITY_STATUS_UPDATE_FAILED->name => 'Activity status update failed',
        ActivityErrorCode::ACTIVITY_SCHEDULE_NOT_FOUND->name => 'Activity schedule not found',
    ],
];