<?php

return [
    'activity' => [
        'title' => [
            'required' => 'Activity title is required',
            'string' => 'Activity title must be a string',
            'max' => 'Activity title cannot exceed the maximum length'
        ],
        'category' => [
            'required' => 'Activity category is required',
            'string' => 'Activity category must be a string',
            'max' => 'Activity category cannot exceed the maximum length'
        ],
        'localized_id' => [
            'required' => 'Localized ID is required',
            'integer' => 'Localized ID must be an integer'
        ],
        // Organizer information
        'organizer_last_name' => [
            'string' => 'Organizer last name must be a string',
            'max' => 'Organizer last name cannot exceed 50 characters'
        ],
        'organizer_first_name' => [
            'string' => 'Organizer first name must be a string',
            'max' => 'Organizer first name cannot exceed 50 characters'
        ],
        'organizer_email' => [
            'string' => 'Organizer email must be a string',
            'email' => 'Organizer email is invalid',
            'max' => 'Organizer email cannot exceed 100 characters'
        ],
        // Activity location
        'type' => [
            'required' => 'Activity type is required',
            'string' => 'Activity type must be a string',
            'in' => 'Activity type is invalid'
        ],
        'location' => [
            'string' => 'Activity location must be a string',
            'max' => 'Activity location cannot exceed 255 characters'
        ],
        'address' => [
            'string' => 'Activity address must be a string',
            'max' => 'Activity address cannot exceed 255 characters'
        ],
        'online_platform' => [
            'string' => 'Online platform must be a string',
            'max' => 'Online platform cannot exceed 20 characters'
        ],
        'online_platform_url' => [
            'string' => 'Online platform URL must be a string',
            'url' => 'Online platform URL must be a valid URL',
            'max' => 'Online platform URL cannot exceed 255 characters'
        ],
        // Activity time
        'schedule' => [
            'required' => 'Activity schedule is required',
            'array' => 'Activity schedule must be an array'
        ],
        'schedule.repeat_type' => [
            'required' => 'Repeat type is required',
            'string' => 'Repeat type must be a string',
            'in' => 'Repeat type is invalid'
        ],
        'schedule.start_date' => [
            'required_if' => 'Start date is required when repeat type is once',
            'date' => 'Start date must be a valid date'
        ],
        'schedule.end_date' => [
            'required_if' => 'End date is required when repeat type is once',
            'date' => 'End date must be a valid date'
        ],
        'repeat_pattern' => [
            'required' => 'Repeat pattern is required',
            'in' => 'Repeat pattern is invalid'
        ],
        'repeat_interval' => [
            'required' => 'Repeat interval is required',
            'min' => 'Repeat interval cannot be less than the minimum value'
        ],
        'start_time' => [
            'required' => 'Start time is required',
            'date' => 'Start time must be a valid date'
        ],
        'end_time' => [
            'required' => 'End time is required',
            'date' => 'End time must be a valid date',
            'after' => 'End time must be after start time'
        ],
        // Registration settings
        'registration_deadline' => [
            'required' => 'Registration deadline is required',
            'date' => 'Registration deadline must be a valid date',
            'before' => 'Registration deadline must be before the start time'
        ],
        'registration_limit_type' => [
            'required' => 'Registration limit type is required',
            'integer' => 'Registration limit type must be an integer',
            'in' => 'Registration limit type is invalid'
        ],
        'registration_limit' => [
            'required_if' => 'Registration limit is required when registration limit is enabled',
            'integer' => 'Registration limit must be an integer',
            'min' => 'Registration limit cannot be less than 0'
        ],
        'target_participants' => [
            'integer' => 'Target participants must be an integer',
            'min' => 'Target participants cannot be less than 0'
        ],
        'registration_type' => [
            'required' => 'Registration type is required',
            'integer' => 'Registration type must be an integer',
            'in' => 'Registration type is invalid'
        ],
        'public_limit' => [
            'required' => 'Public registration limit is required',
            'integer' => 'Public registration limit must be an integer',
            'min' => 'Public registration limit cannot be less than 0'
        ],
        'hidden_limit' => [
            'required' => 'Hidden registration limit is required',
            'integer' => 'Hidden registration limit must be an integer',
            'min' => 'Hidden registration limit cannot be less than 0'
        ],
        'reserved_limit' => [
            'required' => 'Reserved limit is required',
            'integer' => 'Reserved limit must be an integer',
            'min' => 'Reserved limit cannot be less than 0'
        ],
        'is_fee_required' => [
            'required' => 'Fee requirement is required',
            'boolean' => 'Fee requirement must be a boolean value'
        ],
        'status' => [
            'required' => 'Status is required',
            'in' => 'Status is invalid'
        ],
        'publish_time' => [
            'required' => 'Publish time is required',
            'date' => 'Publish time must be a valid date',
            'before' => 'Publish time must be before the activity start time'
        ],
        // Ticket related
        'ticket' => [
            'required_if' => 'Ticket information is required when activity is paid',
            'array' => 'Ticket information must be an array'
        ],
        // Activity groups
        'groups' => [
            'array' => 'Activity groups must be an array'
        ],
        'group' => [
            'name' => [
                'string' => 'Activity group name must be a string',
                'max' => 'Activity group name cannot exceed 100 characters'
            ],
            'start_time' => [
                'date' => 'Activity group start time must be a valid date'
            ],
            'end_time' => [
                'date' => 'Activity group end time must be a valid date',
                'after' => 'Activity group end time must be after start time'
            ]
        ],
        // Rules
        'rule_id' => [
            'integer' => 'Rule ID must be an integer'
        ],

    ],
    'activity_group' => [
        'code' => [
            'required' => 'Activity group code is required',
            'string' => 'Activity group code must be a string',
            'max' => 'Activity group code cannot exceed 20 characters',
            'unique' => 'Activity group code already exists'
        ],
        'name' => [
            'required' => 'Activity group name is required',
            'max' => 'Activity group name cannot exceed 50 characters'
        ],
        'max_members' => [
            'required' => 'Maximum members is required',
            'integer' => 'Maximum members must be an integer',
            'min' => 'Maximum members cannot be less than 0'
        ],
        'can_register' => [
            'required' => 'Can register is required',
            'integer' => 'Can register must be an integer',
            'in' => 'Can register is invalid'
        ],
        'start_time' => [
            'date' => 'Start time must be a valid date'
        ],
        'end_time' => [
            'date' => 'End time must be a valid date',
            'after' => 'End time must be after start time',
            'after_or_equal' => 'End time must be after or equal to start time'
        ]
    ],
    'copy_activity' => [
        'title' => [
            'string' => 'Activity title must be a string',
            'max' => 'Activity title cannot exceed 255 characters',
        ],
        'copy_schedules' => [
            'boolean' => 'Copy schedules must be a boolean value',
        ],
        'copy_tickets' => [
            'boolean' => 'Copy tickets must be a boolean value',
        ],
        'copy_groups' => [
            'boolean' => 'Copy groups must be a boolean value',
        ],
    ],

    'ticket' => [
        'name' => [
            'required' => 'Ticket name is required',
            'string' => 'Ticket name must be a string',
            'max' => 'Ticket name cannot exceed 100 characters'
        ],
        'code' => [
            'required' => 'Ticket code is required',
            'string' => 'Ticket code must be a string',
            'max' => 'Ticket code cannot exceed 50 characters'
        ],
        'quota' => [
            'required' => 'Ticket quota is required',
            'integer' => 'Ticket quota must be an integer',
            'min' => 'Ticket quota cannot be less than 0'
        ],
        'subtypes' => [
            'array' => 'Ticket subtypes must be an array'
        ],
        'subtypes.*.name' => [
            'required' => 'Subtype name is required',
            'string' => 'Subtype name must be a string',
            'max' => 'Subtype name cannot exceed 100 characters'
        ],
        'subtypes.*.price' => [
            'required' => 'Subtype price is required',
            'numeric' => 'Subtype price must be a number',
            'min' => 'Subtype price cannot be less than 0'
        ],
        'subtypes.*.sort' => [
            'required' => 'Subtype sort order is required',
            'integer' => 'Subtype sort order must be an integer',
            'min' => 'Subtype sort order cannot be less than 0'
        ],
        'subtypes.*.start_date' => [
            'required' => 'Subtype start date is required',
            'date' => 'Subtype start date must be a valid date'
        ],
        'subtypes.*.end_date' => [
            'required' => 'Subtype end date is required',
            'date' => 'Subtype end date must be a valid date',
            'after' => 'Subtype end date must be after start date'
        ]
    ]
];