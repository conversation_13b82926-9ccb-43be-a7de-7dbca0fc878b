<?php

return [
    'nav' => [
        'invitation' => 'Invitation Management',
        'invitation_list' => 'Invitation List',
        'invitation_members' => 'Invitation Members',
    ],
    'permission' => [
        'invitation' => 'Invitation Management',
        'invitation_list' => 'Invitation List',
        'invitation_members' => 'Invitation Members',
    ],
    'fields' => [
        'title' => 'Title',
        'description' => 'Description',
        'is_default' => 'Default List',
        'is_group' => 'Assign Group',
        'group_id' => 'Group',
        'status' => 'Status',
        'code' => 'Invitation Code',
        'name' => 'Name',
        'phone' => 'Phone',
        'email' => 'Email',
        'company' => 'Company',
        'position' => 'Position',
        'creator_id' => 'Creator',
        'created_at' => 'Created At',
        'updated_at' => 'Updated At',
    ],
    'example' => [
        'name' => 'John Doe',
        'phone' => '1234567890',
        'email' => '<EMAIL>',
        'company' => 'Example Company',
        'position' => 'Product Manager',
    ],
    'buttons' => [
        'create' => 'Create Invitation',
        'edit' => 'Edit Invitation',
        'delete' => 'Delete Invitation',
        'import' => 'Import Members',
        'export' => 'Export Members',
        'export_template' => 'Export Template',
        'send' => 'Send Invitation',
        'add_member' => 'Add Member',
    ],
    'status' => [
        'enabled' => 'Enabled',
        'disabled' => 'Disabled',
        'pending' => 'Pending',
        'accepted' => 'Accepted',
        'declined' => 'Declined',
        'expired' => 'Expired',
    ],
    'member_status' => [
        'not_replied' => 'Not Replied',
        'registered' => 'Registered',
        'declined' => 'Declined',
    ],
    'messages' => [
        'create_success' => 'Invitation created successfully',
        'update_success' => 'Invitation updated successfully',
        'delete_success' => 'Invitation deleted successfully',
        'not_found' => 'Invitation not found',
        'import_success' => 'Successfully imported {success_count} records, failed {fail_count} records',
        'export_success' => 'Export successful',
        'export_error' => 'Failed to export data, please try again later',
        'send_success' => 'Successfully sent {success_count} invitations, failed {fail_count}',
        'no_data' => 'No data to export',
        'file_format_error' => 'Unsupported file format',
        'file_parse_error' => 'File parsing failed',
        'param_error' => 'Parameter error',
        'member_not_found' => 'Member not found',
        'save_error' => 'Save failed',
    ],
    'validation' => [
        'activity_id' => [
            'required' => 'Activity ID is required',
            'integer' => 'Activity ID must be an integer',
            'exists' => 'Activity does not exist',
        ],
        'title' => [
            'required' => 'Invitation title is required',
            'string' => 'Invitation title must be a string',
            'max' => 'Invitation title maximum length is 64 characters',
        ],
        'description' => [
            'string' => 'Invitation description must be a string',
        ],
        'is_default' => [
            'integer' => 'Default list flag must be an integer',
            'in' => 'Default list flag value is incorrect',
        ],
        'is_group' => [
            'integer' => 'Group assignment flag must be an integer',
            'in' => 'Group assignment flag value is incorrect',
        ],
        'group_id' => [
            'integer' => 'Group ID must be an integer',
            'exists' => 'Group does not exist',
        ],
        'status' => [
            'integer' => 'Status must be an integer',
            'in' => 'Status value is incorrect',
        ],
        'members' => [
            'array' => 'Member data must be an array',
        ],
        'member' => [
            'id' => [
                'integer' => 'Member ID must be an integer',
            ],
            'name' => [
                'required' => 'Member name is required',
                'string' => 'Member name must be a string',
                'max' => 'Member name maximum length is 64 characters',
            ],
            'phone' => [
                'string' => 'Member phone must be a string',
                'max' => 'Member phone maximum length is 30 characters',
            ],
            'email' => [
                'email' => 'Member email format is incorrect',
                'max' => 'Member email maximum length is 100 characters',
            ],
            'company' => [
                'string' => 'Member company must be a string',
            ],
            'position' => [
                'string' => 'Member position must be a string',
            ],
        ],
    ],
]; 