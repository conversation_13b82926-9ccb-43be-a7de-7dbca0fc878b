{"router": {"module": {"title": "Activity Management"}, "event": {"title": "Event Management", "list": "Event List", "create": "Create Event", "detail": "Event Details", "edit": "Edit Event"}, "ticket": {"title": "Ticket Management", "list": "Ticket List", "create": "Create Ticket", "edit": "Edit Ticket"}, "participant": {"title": "Participant Management", "list": "Participant List", "detail": "Participant Details", "create": "Add Participant"}, "waitlist": {"title": "Waitlist Management", "list": "Waitlist", "review": "Waitlist Review"}, "invitation": {"title": "Invitation Management", "list": "Invitation List", "create": "Create Invitation", "edit": "Edit Invitation", "detail": "Invitation Details"}, "rule_template": {"title": "Rule Template Management", "list": "Rule Template List", "create": "Create Rule Template", "edit": "Edit Rule Template"}, "reminder": {"title": "Reminder Management", "list": "Reminder List", "create": "Create Reminder", "edit": "<PERSON>minder"}, "form_template": {"title": "Form Template Management", "list": "Form Template List", "create": "Create Form Template", "edit": "Edit Form Template"}, "email_template": {"title": "Email Template Management", "list": "Email Template List", "create": "Create <PERSON><PERSON>", "edit": "Edit <PERSON><PERSON>late"}, "calendar": "Event Calendar", "activity_detail_tabs": "Activity Details"}, "common": {"add": "Add", "edit": "Edit", "delete": "Delete", "view": "View", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "back": "Back", "search": "Search", "filter": "Filter", "reset": "Reset", "export": "Export", "import": "Import", "status": "Status", "actions": "Actions", "create_time": "Created Time", "update_time": "Updated Time", "create_success": "Created successfully", "update_success": "Updated successfully", "delete_success": "Deleted successfully", "operation_success": "Operation successful", "operation_failed": "Operation failed", "confirm_delete": "Confirm Delete", "delete_confirm_message": "Are you sure you want to delete this item?", "no_data": "No data available", "loading": "Loading...", "select_placeholder": "Please select", "input_placeholder": "Please enter", "ticket_delete_failed_unique": "Cannot delete this ticket type, some activities only have this one ticket type. Please add other ticket types to related activities first, or delete related activities.", "ticket_delete_success": "Ticket type deleted successfully"}, "group": {"dialog": {"create_title": "Create Group", "edit_title": "Edit Group", "loading_text": "Loading group details..."}, "form": {"name": "Group Name", "name_placeholder": "Please enter group name", "code": "Group ID", "code_placeholder": "Please enter group ID", "max_members": "Maximum Members", "max_members_placeholder": "Please enter maximum members", "can_register": "Allow Registration", "can_register_yes": "Yes", "can_register_no": "No", "start_time": "Registration Start Date", "start_time_placeholder": "Please select registration start date", "end_time": "Registration End Date", "end_time_placeholder": "Please select registration end date"}, "validation": {"name_required": "Please enter group name", "name_length": "Group name must be between 2 and 50 characters", "code_required": "Please enter group ID", "code_length": "Group ID must be between 2 and 20 characters", "code_format": "Group ID can only contain letters, numbers, underscores and hyphens", "max_members_required": "Please enter maximum members", "max_members_range": "Maximum members must be between 1 and 9999", "can_register_required": "Please select whether to allow registration", "start_time_required": "Please select registration start date", "end_time_required": "Please select registration end date", "end_time_after_start": "Registration end date must be after start date"}, "message": {"create_success": "Group created successfully", "update_success": "Group updated successfully", "create_failed": "Failed to create group", "update_failed": "Failed to update group", "get_detail_failed": "Failed to get group details", "form_validation_failed": "Form validation failed, please check your input", "retry_later": "Please try again later"}, "button": {"cancel": "Cancel", "confirm": "Confirm", "creating": "Creating...", "updating": "Updating...", "update": "Update", "create": "Create"}}, "event_list": {"button": {"export": "Export", "import": "Import", "create": "Create Event", "edit": "Edit", "copy": "Copy", "delete": "Delete", "cancel": "Cancel", "confirm": "Confirm", "view_detail": "View Details", "edit_event": "Edit Event"}, "tab": {"draft": "Draft", "created": "Created", "published": "Published", "cancelled": "Cancelled", "completed": "Completed"}, "table": {"event_name": "Event Name", "category": "Category", "event_time": "Event Time", "publish_time": "Publish Time", "ticket_method": "Ticket Method", "status": "Status", "actions": "Actions", "time_start": "Start: ", "time_end": "End: ", "time_tbd": "Time TBD"}, "dialog": {"select_action": "Select Action", "select_action_tip": "Please select an action to perform:", "delete_title": "Confirm Delete", "delete_confirm": "Are you sure you want to delete this event? This action cannot be undone."}, "message": {"copy_success": "<PERSON><PERSON>d successfully", "delete_success": "Deleted successfully", "delete_failed": "Failed to delete", "get_list_failed": "Failed to get event list"}}, "event_create": {"button": {"back": "Back"}, "tab": {"basic_info": "Event Information", "registration": "Registration & Tickets", "rules": "Application Rules", "calendar": {"header": {"today": "Today", "month": "Month", "year": "Year"}, "weekdays": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "event": {"time_tbd": "Time TBD", "offline": "Offline", "more": "more"}, "message": {"fetch_calendar_failed": "Failed to fetch calendar data"}}, "calendar_details": {"date_format": {"year": "-", "month": "", "day": ""}}}, "calendar_details": {"dialog": {"title": "Activity Details", "default_title": "Activity Details"}, "timeline": {"view_details": "View Activity Details", "time_tbd": "Time TBD"}, "detail": {"activity_type": "Activity Type", "activity_name": "Activity Name", "activity_status": "Activity Status", "organizer": "Organizer", "activity_location": "Activity Location", "detailed_address": "Detailed Address", "activity_platform": "Activity Platform", "registration_deadline": "Registration Deadline"}, "activity_type": {"online": "Online", "offline": "Offline", "hybrid": "Hybrid"}, "activity_status": {"created": "Created", "published": "Published", "ongoing": "Ongoing", "completed": "Completed", "cancelled": "Cancelled"}, "button": {"add_activity": "Add Activity"}, "date_format": {"year": "Year", "month": "Month", "day": "Day"}}, "loading": {"getting_config": "Getting configuration...", "getting_detail": "Getting event details..."}, "message": {"config_failed": "Failed to get configuration", "detail_failed": "Failed to get event details", "create_success": "Event created successfully", "update_success": "Event updated successfully", "create_failed": "Failed to create", "update_failed": "Failed to update", "form_incomplete": "Please complete all required fields in current step", "basic_info_incomplete": "Event information is incomplete", "registration_incomplete": "Registration and ticket information is incomplete", "rules_incomplete": "Application rules information is incomplete", "please_check_form": "Please check the form data", "try_later": "Please try again later"}}, "ticket": {"dialog": {"create_title": "Create Ticket Type", "edit_title": "Edit Ticket Quantity", "creating": "Creating...", "editing": "Adding..."}, "form": {"name": "Ticket Type Name", "name_placeholder": "Please enter ticket type name", "code": "Type ID", "code_placeholder": "Please enter type ID", "quota": "Quantity Limit", "quota_placeholder": "Please enter ticket limit", "groups": "Group Restrictions (Which groups can select this ticket)", "groups_placeholder": "Please select groups", "is_external_sale": "Available for Public Sale", "is_fee_required": "Requires Payment", "yes": "Yes", "no": "No", "subtypes": "Subtypes", "subtype": {"name_placeholder": "Ticket Name", "price_placeholder": "Price", "currency_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "date_range": "Date Range", "date_start_placeholder": "Start Date", "date_end_placeholder": "End Date", "date_separator": "to", "early_bird": "Early Bird", "regular": "Regular"}, "add_subtype": "Add Subtype"}, "validation": {"name_required": "Please enter ticket type name", "code_required": "Please enter type ID", "quota_required": "Please enter quantity limit", "groups_required": "Please select group restrictions", "external_sale_required": "Please select if available for public sale", "fee_required": "Please select if payment is required"}, "button": {"cancel": "Cancel", "confirm": "Confirm", "create": "Create", "edit": "Add"}}, "application": {"card": {"rule_settings": "Application Rules"}, "form": {"rule_template": "Select Rule Template", "rule_template_placeholder": "Please select a rule template (optional)", "loading_text": "Loading rule templates...", "general_rule": "General Rule", "status": "Status", "enabled": "Enabled", "disabled": "Disabled"}, "button": {"prev": "Previous", "publish": "Publish", "modify": "Modify"}, "message": {"get_template_failed": "Failed to get rule template list", "try_later": "Please try again later", "validation_failed": "Form validation failed"}}, "info": {"card": {"basic_info": "Basic Information", "organizer_info": "Organizer Information", "location_settings": "Location Settings", "schedule_settings": "Schedule Settings", "publish_settings": "Publish Settings"}, "form": {"title": "Event Name", "category": "Event Category", "localized_id": "Localization Region", "organizer_last_name": "Organizer Last Name", "organizer_first_name": "Organizer First Name", "organizer_email": "Organizer Email", "type": "Event Type", "location": "Event Venue", "address": "Event Address", "online_platform": "Online Platform", "online_platform_url": "Event Link", "repeat_type": "<PERSON><PERSON>", "repeat_frequency": "Repeat Fr<PERSON>quency", "repeat_pattern": "<PERSON><PERSON>", "monthly_week": "Which Week", "monthly_day": "Day of Week", "schedule_dates": "Event Start/End Date", "start_date": "Start Date", "end_date": "End Date", "break_periods": "Break Periods", "start_time": "Start Time", "end_time": "End Time", "time_settings": "Time Settings", "day_of_week": "Day of Week", "activity_time": "Event Time", "publish_time": "Publish Date", "add_break": "Add break"}, "placeholder": {"title": "Please enter event name", "category": "Please select event category", "localized_id": "Please select localization region", "organizer_last_name": "Last Name", "organizer_first_name": "First Name", "organizer_email": "Please enter email", "type": "Please select event type", "location": "Please enter event venue", "address": "Please enter event address", "online_platform": "Please enter online platform", "online_platform_url": "Please enter event link", "repeat_type": "Please select repeat pattern", "monthly_week": "Please select which week", "monthly_day": "Please select day of week", "start_date": "Start Date", "end_date": "End Date", "start_time": "Please select start time", "end_time": "Please select end time", "day_of_week": "Please select", "time_start": "Start", "time_end": "End", "publish_time": "Please select publish date"}, "options": {"week": {"1": "Week 1", "2": "Week 2", "3": "Week 3", "4": "Week 4"}, "weekday": {"1": "Monday", "2": "Tuesday", "3": "Wednesday", "4": "Thursday", "5": "Friday", "6": "Saturday", "7": "Sunday"}, "weekday_long": {"1": "Monday", "2": "Tuesday", "3": "Wednesday", "4": "Thursday", "5": "Friday", "6": "Saturday", "7": "Sunday"}}, "frequency": {"every": "Every", "month_repeat": "month(s) repeat", "week_repeat": "week(s) repeat", "daily_repeat": "daily repeat", "times": "time(s)"}, "button": {"next_step": "Next Step", "add_break_period": "Add break period"}, "validation": {"title_required": "Please enter event name", "category_required": "Please select event category", "organizer_last_name_required": "Please enter organizer last name", "organizer_first_name_required": "Please enter organizer first name", "organizer_email_required": "Please enter organizer email", "organizer_email_format": "Please enter a valid email format", "type_required": "Please select event type", "location_required": "Please enter event venue", "address_required": "Please enter event address", "online_platform_required": "Please enter online platform", "online_platform_url_required": "Please enter event link", "online_platform_url_format": "Event link must start with http:// or https://", "repeat_type_required": "Please select repeat pattern", "repeat_frequency_required": "Please enter repeat frequency", "monthly_week_required": "Please select which week", "monthly_day_required": "Please select day of week", "start_date_required": "Please select event start time", "end_date_required": "Please select event end time", "start_date_activity_required": "Please select event start date", "end_date_activity_required": "Please select event end date", "daily_repeat_frequency_required": "Please enter daily repeat frequency", "day_of_week_required": "Please select day of week", "start_time_required": "Please select start time", "end_time_required": "Please select end time", "break_start_date_required": "Please select break start time", "break_end_date_required": "Please select break end time", "publish_time_required": "Please select publish date"}}, "signup": {"title": "Event Registration Settings", "ticket_settings_title": "Ticket <PERSON>s", "card": {"activity_info": "Activity Information", "organizer_settings": "Organizer Settings", "quota_allocation": "Quota Allocation", "fee_settings": "<PERSON><PERSON>", "registration_method": "Registration Method", "ticket_settings": "Ticket <PERSON>s"}, "form": {"registration_deadline": "Registration Deadline", "registration_limit": "Registration Limit", "target_participants": "Target Participants", "registration_type": "Who Can Register", "quota_allocation": "Quota Allocation", "public_limit": "Public Quota", "hidden_limit": "Hidden Quota", "reserved_limit": "Reserved <PERSON><PERSON><PERSON>", "is_fee_required": "Is Payment Required?", "invoice_prefix": "Invoice Prefix Setting", "target_income": "Target Income", "registration_method": "Registration Overflow Handling", "waiting_list_rules": "Waiting List Processing Rules", "ticket_category": "Select Ticket Category", "ticket_delivery": "Select Ticket Delivery Method", "email_template": "Select Email Template"}, "placeholder": {"registration_deadline": "Please select deadline", "groups": "Please select groups", "ticket_type": "Please select ticket type", "invoice_prefix": "Please enter invoice prefix", "email_template": "Please select"}, "options": {"unlimited": "Unlimited", "set_limit": "<PERSON>", "yes": "Yes", "no": "No", "auto_close": "Auto Close Registration", "waiting_list": "Enable Waiting List", "auto_promote": "Auto Promote", "manual_review": "Manual Review", "electronic": "Electronic Ticket", "physical": "Physical Pickup", "festival": "Festival Activity", "charity": "Charity Event"}, "loading": {"groups": "Loading groups..."}, "button": {"prev_step": "Previous Step", "next_step": "Next Step", "new_group": "New Group", "new_ticket": "New Ticket Category", "edit": "Edit", "delete": "Delete"}, "selected": {"groups_title": "Selected Groups:", "tickets_title": "Selected Ticket Types:", "subtypes_title": "Subtypes:"}, "group_details": {"member_limit": "Member Limit:", "can_register": "Can Register:", "registration_time": "Registration Time:", "not_set": "Not Set", "no_description": "No Description"}, "date_separator": "to", "ticket_tags": {"external_sale": "External Sale", "internal_sale": "Internal Sale", "paid": "Paid", "free": "Free", "no_subtypes": "None"}, "validation": {"registration_deadline_required": "Please select registration deadline"}, "message": {"group_create_success": "Group created successfully", "group_update_success": "Group updated successfully", "group_list_error": "Failed to get group list", "group_list_retry": "Failed to get group list, please try again later", "ticket_create_success": "Ticket type created successfully", "ticket_create_error": "Failed to create ticket type", "ticket_add_success": "Ticket added successfully", "ticket_add_form_success": "Ticket type added successfully", "ticket_list_error": "Failed to get ticket list", "operation_failed": "Operation failed"}}, "activity_detail_tabs": {"button": {"back_to_list": "Back to List", "new_ticket": "New Ticket", "new_invitation": "New Invitation", "new_reminder": "<PERSON> Reminder", "new_item": "New"}, "tabs": {"tickets": "Ticket Management", "participants": "Participant Management", "waiting_list": "Waiting List", "invitations": "Invitation Management", "reminders": "Reminder Management"}, "search": {"placeholder": "Search"}, "pagination": {"page_size_text": "Items per page", "total_text": "Total {total} items", "no_data": "No data available"}, "message": {"load_component_failed": "Failed to load {component} component", "get_activity_info_failed": "Failed to get activity information"}}, "rule_template_list": {"button": {"batch_copy": "<PERSON><PERSON> Copy", "create_template": "Create Rule Template"}, "table": {"template_name": "Template Name", "usage_scene": "Usage Scene", "last_modified": "Last Modified", "status": "Status", "actions": "Actions"}, "pagination": {"page_size_text": "Show per page", "total_text": "Total {total} items", "no_data": "No data"}, "message": {"select_copy_templates": "Please select templates to copy", "batch_copy_success": "Batch copy successful", "batch_copy_failed": "Batch copy failed", "delete_confirm": "Are you sure you want to delete this template?", "delete_success": "Delete successful", "delete_failed": "Delete failed", "status_update_success": "Status update successful", "status_update_failed": "Status update failed", "get_list_failed": "Failed to get list"}, "dialog": {"title": "Tip", "confirm": "Confirm", "cancel": "Cancel"}}, "rule_template_create": {"button": {"cancel": "Cancel", "save": "Save", "update": "Update", "submit": "Submit", "prev_step": "Previous", "next_step": "Next"}, "tabs": {"rule_info": "Rule Information", "basic_config": "Basic Configuration"}, "dialog": {"cancel_confirm_title": "Tip", "cancel_confirm_message": "Are you sure you want to cancel? Unsaved content will be lost", "confirm": "Confirm", "cancel": "Cancel"}, "message": {"save_success": "Saved successfully", "update_success": "Updated successfully", "create_success": "Created successfully", "save_failed": "Save failed", "update_failed": "Update failed", "create_failed": "Create failed", "load_data_failed": "Failed to load data"}}, "add_members": {"title": {"create": "Add Invitee Information", "edit": "Edit Invitee Information"}, "fields": {"name": "Invitee Name", "company": "Invitee Company", "position": "Invitee Position", "phone": "Invitee Phone", "email": "Invitee Email"}, "placeholders": {"name": "Please enter invitee name", "company": "Please enter invitee company", "position": "Please enter invitee position", "phone": "Please enter invitee phone", "email": "Please enter invitee email"}, "buttons": {"cancel": "Cancel", "create": "Create", "update": "Update"}, "validation": {"name": {"required": "Please enter invitee name"}, "company": {"required": "Please enter invitee company"}, "position": {"required": "Please enter invitee position"}, "phone": {"required": "Please enter invitee phone"}, "email": {"required": "Please enter invitee email", "format": "Please enter correct email format"}}}, "invitation_details": {"buttons": {"back": "Back"}, "form": {"list_name": "List Name", "description": "List Description", "is_ngo": "Is NGO List", "is_member_group": "Is Member Assignment Group", "group_type": "Group Type"}, "options": {"yes": "Yes", "no": "No", "group": "Group", "single": "Single"}, "table": {"title": "Member List", "search_placeholder": "Search by name/company/position/phone/email", "search_result": "Search Results", "total_members": "Total {count} members", "no_match": "No matching members found", "no_data": "No data available", "columns": {"name": "Name", "company": "Company", "position": "Position", "phone": "Work Phone", "email": "Email", "status": "Status"}}, "messages": {"missing_params": "Missing required parameters", "get_detail_failed": "Failed to get details", "get_invitation_failed": "Failed to get invitation details"}}, "invitation_create": {"buttons": {"back": "Back to List"}, "tabs": {"list_settings": "List Settings", "member_list": "Member List"}, "pagination": {"total": "Total {count}", "per_page": "{size}/page", "no_data": "No Data", "go_to": "Go to"}, "messages": {"missing_activity_id": "Missing activity ID parameter, please enter from activity detail page", "get_invitation_failed": "Failed to get invitation details", "list_settings_incomplete": "List settings are incomplete", "check_form_data": "Please check form data", "update_success": "Update successful", "create_success": "Create successful", "update_failed": "Update failed", "create_failed": "Create failed"}}, "invitation_list": {"settings": {"title": "Invitation List Basic Settings", "list_name": "List Name", "list_name_placeholder": "Please enter list name", "list_description": "List Description (Content Only)", "description_placeholder": "Please enter list description", "is_default_list": "Is Default Invitation List", "can_register_group": "Allow Group Registration", "export_format": "Export Format", "export_format_placeholder": "Please select export format", "yes": "Yes", "no": "No"}, "members": {"title": "Member List", "search_placeholder": "Search by name, email or phone", "advanced_search": "Advanced Search", "remove": "Remove", "transfer_to": "Transfer To", "bulk_import": "Bulk Import", "add_member": "Add Member"}, "table": {"member_name": "Member Name", "company": "Company", "position": "Position", "work_number": "Work Phone", "email": "Email", "status": "Status", "actions": "Actions", "edit": "Edit", "delete": "Delete", "title": "Title", "description": "Description", "is_default": "<PERSON>", "is_group": "Group Registration", "created_at": "Created Date"}, "status": {"enabled": "Enabled", "disabled": "Disabled", "draft": "Draft"}, "group_type": {"group": "Group", "single": "Single"}, "default_list": {"yes": "Yes", "no": "No"}, "empty": {"description": "No data available"}, "delete_dialog": {"title": "Confirm Delete", "content": "Are you sure you want to delete invitation list \"{title}\"?", "warning": "This action cannot be undone, please proceed with caution.", "cancel": "Cancel", "confirm": "Confirm Delete", "deleting": "Deleting..."}, "messages": {"delete_success": "Deleted successfully", "delete_failed": "Delete failed, please try again later", "get_list_failed": "Failed to get invitation list"}}, "invitation_list_settings": {"title": "Invitation List Basic Settings", "form": {"list_name": "List Name", "list_name_placeholder": "2025 Charity Gala Invitation List", "description": "List Description", "description_placeholder": "NGO Staff", "set_as_default": "Set as Default List", "assign_group": "Assign Group to List Members", "group_type": "Group Type"}, "options": {"yes": "Yes", "no": "No", "vip": "VIP", "normal": "Regular Member"}, "buttons": {"cancel": "Cancel", "next_step": "Next Step"}, "validation": {"list_name_required": "Please enter list name", "description_required": "Please enter list description"}}, "invitation_member_list": {"title": "Invitation Member List", "buttons": {"batch_import": "Batch Import", "batch_import_disabled": "Batch Import (Available after save)", "export_members": "Export Members", "export_members_disabled": "Export Members (Available after save)", "exporting": "Exporting...", "add_member": "Add Member", "edit": "Edit", "delete": "Delete", "search": "Search", "reset": "Reset", "prev_step": "Previous", "cancel": "Cancel", "save_submit": "Save and Submit"}, "search": {"name": "Search by Name", "name_placeholder": "Please enter name", "email": "Search by <PERSON><PERSON>", "email_placeholder": "Please enter email", "phone": "Search by Phone", "phone_placeholder": "Please enter phone"}, "table": {"name": "Name", "company": "Company", "position": "Position", "phone": "Work Phone", "email": "Email", "status": "Status", "actions": "Actions"}, "status": {"not_replied": "Not Replied", "registered": "Registered", "declined": "Declined", "unknown": "Unknown"}, "empty": {"description": "No data available"}, "delete_dialog": {"title": "Confirm Delete", "content": "Are you sure you want to delete member \"{name}\"?", "warning": "This action cannot be undone, please proceed with caution.", "cancel": "Cancel", "confirm": "Confirm Delete", "deleting": "Deleting..."}, "messages": {"import_success": "Import successful: {success_count} items, {fail_count} failed", "import_failed": "Import members failed", "export_success": "Export successful", "export_failed": "Export members failed", "delete_success": "Delete member successful", "delete_failed": "Delete member failed, please try again later", "update_success": "Update member successful", "add_success": "Add member successful", "save_failed": "Save member failed", "get_list_failed": "Failed to get member list", "save_first": "Please save invitation information first before importing members", "save_first_export": "Please save invitation information first before exporting members", "missing_activity_id": "Missing activity ID parameter", "data_format_error": "Member list data format error"}}, "participant_create": {"title": "Add Participant", "buttons": {"back": "Back", "cancel": "Cancel", "save": "Save"}, "sections": {"basic_info": "Basic Information", "invitation_info": "Invitation Information", "additional_info": "Additional Information"}, "form": {"name": "Name", "name_placeholder": "Please enter participant name", "company": "Company", "company_placeholder": "Please enter company name", "position": "Position", "position_placeholder": "Please enter position", "email": "Email", "email_placeholder": "Please enter email address", "phone": "Phone", "phone_placeholder": "Please enter phone number", "participant_path": "Participation Method", "participant_path_placeholder": "Please select participation method", "status": "Participation Status", "status_placeholder": "Please select participation status", "remark": "Remark", "remark_placeholder": "Please enter remark information", "invitation_sender": "Inviter", "invitation_sender_placeholder": "Please enter inviter name", "invitation_sender_email": "<PERSON><PERSON><PERSON>", "invitation_sender_email_placeholder": "Please enter inviter email", "send_invitation": "Send Invitation"}, "options": {"participant_path": {"registration": "Registration", "invitation": "Invitation", "recommendation": "Recommendation", "other": "Other"}, "status": {"undecided": "Undecided", "confirmed": "Confirmed", "rejected": "Rejected", "pending": "Pending"}}, "validation": {"name_required": "Please enter participant name", "name_length": "Length should be between 2 and 50 characters", "email_required": "Please enter email address", "email_format": "Please enter correct email format", "company_required": "Please enter company name", "position_required": "Please enter position", "participant_path_required": "Please select participation method", "status_required": "Please select participation status", "invitation_sender_required": "Please enter inviter name", "invitation_sender_email_required": "Please enter inviter email", "invitation_sender_email_format": "Please enter correct email format"}, "messages": {"create_success": "Participant added successfully", "create_failed": "Failed to add participant", "fill_required": "Please fill in required fields"}}, "participant_detail": {"buttons": {"operations": "Operations", "back": "Back"}, "operations": {"resend_email": "Resend Invitation Email", "export": "Export Participant Information", "print": "Print Contact Information", "check_journey": "View User Journey"}, "sections": {"invitee_info": "Invitee Information", "action_timeline": "Action Timeline", "invitation_info": "Invitation Operation Information"}, "fields": {"name": "Name", "email": "Email", "status": "Status", "invitation_sender": "Invitation Email <PERSON>er", "invitation_sender_email": "Invitation Email Address", "confirm_time": "Status Confirmation Time", "operation_desc": "Operation Description", "operation_type": "Operation Type", "operation_ip": "Operation IP"}, "status": {"pending": "Invitation", "confirmed": "Confirmed Participation", "rejected": "Rejected Participation", "undecided": "Undecided", "no_response": "No Response", "unknown": "Unknown"}, "messages": {"get_detail_failed": "Failed to get participant details", "get_action_history_failed": "Failed to get action history", "resend_email_success": "Invitation email sent successfully", "export_developing": "Export feature is under development", "check_journey_developing": "View user journey feature is under development", "operation_failed": "Operation failed"}}, "participant_list": {"table": {"name": "Name", "email": "Email", "phone": "Phone", "registration_time": "Registration Time", "status": "Status", "created_at": "Created Date", "actions": "Actions"}, "status": {"registered": "Registered", "confirmed": "Confirmed", "cancelled": "Cancelled", "attended": "Attended", "waiting": "Waiting List", "unknown": "Unknown"}, "empty": {"description": "No data available"}, "delete_dialog": {"title": "Delete Confirmation", "content": "Are you sure you want to delete participant '{name}'?", "warning": "This action cannot be undone. Please proceed with caution.", "cancel": "Cancel", "confirm": "Confirm Delete", "deleting": "Deleting..."}, "messages": {"delete_success": "Deleted successfully", "delete_failed": "Delete failed", "get_list_failed": "Failed to get participant list"}}, "reminder_create": {"tabs": {"basic": "Basic Settings", "rules": "Reminder Rules"}, "buttons": {"back": "Back to List", "next": "Next", "prev": "Previous", "submit": "Submit", "cancel": "Cancel"}, "messages": {"load_success": "Data loaded successfully", "load_failed": "Failed to get reminder details", "create_success": "Created successfully", "create_failed": "Create failed", "update_success": "Updated successfully", "update_failed": "Update failed", "form_incomplete": "Basic settings are incomplete", "check_form": "Please check the form data", "missing_activity_id": "Missing activity ID parameter, please enter from activity detail page"}}, "reminder_basic": {"fields": {"name": "Reminder Name", "name_placeholder": "Please enter reminder name, e.g. 2025 Charity Gala Reminder", "type": "Select Channel", "email": "Email", "sms": "SMS", "recipients": "Recipient List", "add_recipient": "Add Recipient", "recipient_email": "Recipient {index} Email", "recipient_email_placeholder": "Please enter email address", "recipient_phone": "Recipient {index} Phone", "recipient_phone_placeholder": "Please enter phone number"}, "buttons": {"delete": "Delete", "cancel": "Cancel", "next": "Next"}, "validation": {"name_required": "Please enter reminder name", "name_length": "Reminder name must be 2-100 characters", "type_required": "Please select at least one channel", "email_required": "Please enter email address", "email_format": "Please enter a valid email address", "phone_required": "Please enter phone number", "phone_format": "Please enter a valid phone number", "form_incomplete": "Please complete the form information"}}, "reminder_rule": {"sections": {"activity": "Activity Reminders", "ticket": "Ticket Reminders"}, "rules": {"registration_deadline": {"name": "Registration Deadline Reminder", "desc": "Send a reminder when the registration deadline is approaching"}, "participant_limit": {"name": "Participant Lim<PERSON>", "desc": "Send a reminder when the participant limit is reached"}, "activity_start_end": {"name": "Activity Start/End Reminder", "desc": "Send a reminder when the activity starts or ends"}, "ticket_overdue": {"name": "Ticket Overdue Reminder", "desc": "Send a reminder when the ticket is overdue"}}, "alert": {"title": "Remind<PERSON> Settings Description", "desc": "After enabling the corresponding reminder rules, the system will automatically send reminders according to the set conditions. You can modify these settings at any time."}, "buttons": {"prev": "Previous", "cancel": "Cancel", "submit": "Save and Submit"}}, "reminder_list": {"table": {"name": "Reminder Name", "recipient": "Recipient", "reminder_type": "Reminder Type", "send_time": "Send Time", "status": "Status", "is_active": "Active Status", "created_at": "Created Date", "actions": "Actions"}, "status": {"pending": "Pending", "sent": "<PERSON><PERSON>", "failed": "Failed", "scheduled": "Scheduled", "unknown": "Unknown"}, "empty": {"description": "No Data"}, "delete_dialog": {"title": "Delete Confirmation", "content": "Are you sure you want to delete reminder \"{name}\"?", "warning": "This operation cannot be undone, please proceed with caution.", "cancel": "Cancel", "confirm": "Confirm Delete", "deleting": "Deleting..."}, "messages": {"delete_success": "Delete successful", "delete_failed": "Delete failed", "enable_success": "Enable successful", "disable_success": "Disable successful", "status_update_failed": "Status update failed", "get_list_failed": "Failed to get reminder list"}}, "basic_config_form": {"sections": {"language_settings": "Language Settings", "email_settings": "<PERSON><PERSON>s", "registration_settings": "Event Registration Settings", "security_privacy": "Security & Privacy Settings"}, "fields": {"is_multilingual": "Is it a multilingual event", "select_languages": "Please select event languages", "select_email_template": "Select Email Template", "select_feedback_form": "Select Event Feedback Form", "specific_email_domain": "Enable specific email domain restriction", "search_engine_settings": "Search Engine Settings", "page_permission": "Page Permission Settings"}, "options": {"yes": "Yes", "no": "No", "languages": {"zh_CN": "Chinese (Simplified)", "zh_HK": "Chinese (Traditional)", "en": "English"}, "member_limit_type": {"email_domain": "Yes", "none": "No"}, "searchable": {"true": "Yes", "false": "No"}, "page_permission": {"hidden": "Hidden", "needCode": "Require Event Invitation Code", "public": "Public"}}, "descriptions": {"search_engine_description": "Whether this event should appear in search engines", "page_permission_description": "Affects permission settings for other viewers", "template_loading": "Loading email templates...", "preview": "Preview"}, "feedback_forms": {"general_satisfaction": {"label": "General Satisfaction Survey", "description": "Includes overall satisfaction, recommendation willingness and other basic questions"}, "detailed_evaluation": {"label": "Detailed Event Evaluation", "description": "In-depth evaluation of each aspect of the event"}, "speaker_evaluation": {"label": "Speaker/Instructor Evaluation", "description": "Evaluation form specifically for speaker performance"}, "meeting_effectiveness": {"label": "Meeting Effectiveness Evaluation", "description": "Evaluate meeting goal achievement and organizational effectiveness"}, "training_feedback": {"label": "Training Course Feedback", "description": "Feedback form for training events"}, "quick_feedback": {"label": "Quick Feedback Form", "description": "Short and quick feedback collection to improve response rate"}, "service_evaluation": {"label": "Customer Service Evaluation", "description": "Focus on evaluating event service quality"}, "custom": {"label": "[Custom Form]", "description": "Create exclusive feedback form based on event characteristics"}}, "placeholders": {"select_feedback_form": "Please select feedback form"}, "validation": {"is_multilingual": {"required": "Please select whether it is a multilingual event"}, "languages": {"required": "Please select at least one language"}}, "messages": {"get_email_templates_failed": "Failed to get email templates", "currency_changed": "Currency changed to"}, "units": {"questions": "questions", "estimated_time": "Estimated", "minutes": "minutes", "to_be_determined": "TBD"}, "rule_info_form": {"fields": {"name": "Rule Template", "description": "Form Description", "scene": "Usage Scenario Selection"}, "placeholders": {"name": "Free-Standard Rule Template", "description": "Suitable for regular events with low volume requirements", "scene": "On-site Registration"}, "options": {"scene": {"onsite": "On-site Registration", "online": "Online Registration"}}, "validation": {"name": {"required": "Please enter rule template name"}, "scene": {"required": "Please select usage scenario"}}}}, "ticket_list": {"table": {"name": "Ticket Name", "code": "Ticket Code", "quota": "Already registered", "remaining_quota": "Open registration", "subtypes_count": "Subtype Count", "external_sale": "External Sale", "fee_required": "<PERSON><PERSON> Required", "created_at": "Created Date", "actions": "Actions"}, "status": {"yes": "Yes", "no": "No"}, "empty": {"description": "No Data"}, "delete_dialog": {"title": "Delete Confirmation", "content": "Are you sure you want to delete ticket \"{name}\"?", "warning": "This operation cannot be undone, please proceed with caution.", "cancel": "Cancel", "confirm": "Confirm Delete", "deleting": "Deleting..."}, "messages": {"delete_success": "Delete successful", "delete_failed": "Delete failed", "get_list_failed": "Failed to get ticket list"}}, "ticket_type": {"title": "Ticket Status Settings", "form": {"quantity_limit": "Ticket Type Quantity Limit", "quantity_limit_placeholder": "Please enter limit quantity", "external_sale": "Available for Public Sale", "fee_required": "Requires Payment", "currency_type": "Currency Type", "currency_placeholder": "Please select currency", "subtypes": "Subtypes", "subtype_name_placeholder": "Ticket Name", "subtype_price_placeholder": "Price", "subtype_currency_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "date_range": "Date Range", "date_start_placeholder": "Start Date", "date_end_placeholder": "End Date", "date_separator": "to", "add_subtype": "Add Subtype"}, "options": {"yes": "Yes", "no": "No"}, "buttons": {"previous": "Previous", "create": "Create", "creating": "Creating..."}, "validation": {"external_sale_required": "Please select if available for public sale", "fee_required_required": "Please select if payment is required", "currency_required": "Please select currency type", "subtypes_required": "Please add at least one subtype"}, "messages": {"get_config_failed": "Failed to get activity type configuration", "form_validation_failed": "Form validation failed", "currency_changed": "Currency changed to"}, "currency": {"china_mainland": "China Mainland", "hong_kong": "Hong Kong", "united_states": "United States"}}, "currency": {"china_mainland": "China Mainland", "hong_kong": "Hong Kong", "united_states": "United States"}, "ticket_create": {"buttons": {"back": "Back", "update": "Update Ticket", "updating": "Updating..."}, "tabs": {"basic_settings": "Basic Settings", "basic_settings_edit": "Basic Settings (Edit)", "ticket_type": "Ticket Type", "ticket_type_edit": "Ticket Type (Edit)"}, "messages": {"create_success": "Ticket created successfully", "update_success": "Ticket updated successfully", "create_failed": "Create failed", "update_failed": "Update failed", "update_failed_retry": "Update failed, please try again later", "create_failed_retry": "Create failed, please try again later", "get_detail_failed": "Failed to get ticket details", "get_detail_failed_retry": "Failed to get ticket details, please try again later"}}, "ticket_base": {"title": "Ticket Basic Settings", "fields": {"name": "Ticket Name", "code": "Ticket Code", "description": "Ticket Description", "group_limit": "Limit which groups can select this ticket", "select_groups": "Please select group types"}, "placeholders": {"name": "Please enter ticket name", "code": "Please enter ticket code", "description": "Please enter ticket description", "select_groups": "Please select groups"}, "options": {"yes": "Yes", "no": "No"}, "buttons": {"next": "Next"}, "validation": {"name": {"required": "Please enter ticket name", "length": "Ticket name length should be between 2 and 50 characters"}, "code": {"required": "Please enter ticket code", "length": "Ticket code length should be between 2 and 20 characters", "pattern": "Ticket code can only contain letters and numbers"}, "description": {"length": "Ticket description cannot exceed 500 characters"}, "group_limit": {"required": "Please select whether to limit groups"}, "groups": {"required": "Please select group types"}}, "no_description": "No description", "character_limit": " character limit"}, "waiting_list": {"cards": {"total_registration": "Total Registration/Limit", "waiting_count": "Waiting List Count", "waiting_to_success": "Waiting to Success Count"}, "search": {"placeholder": "Search by name, email or phone"}, "tabs": {"pending": "Pending", "approved": "Notified", "cancelled": "Expired/Cancelled"}, "table": {"name": "Name", "contact": "Contact", "registration_time": "Registration Time", "queue_order": "Queue Order", "created_date": "Created Date", "actions": "Actions", "view": "View"}, "detail_dialog": {"title": "Participant Details", "close": "Close"}, "detail_sections": {"basic_info": "Basic Information", "activity_info": "Activity Information", "time_info": "Time Information"}, "detail_fields": {"name": "Name", "email": "Email", "phone": "Phone", "gender": "Gender", "registration_time": "Registration Time", "status": "Status", "check_in_time": "Check-in Time", "feedback": "<PERSON><PERSON><PERSON>", "created_time": "Created Time", "updated_time": "Updated Time"}, "status": {"pending": "Pending", "approved": "Notified", "cancelled": "Expired/Cancelled", "unknown": "Unknown"}, "placeholders": {"not_filled": "Not filled", "unknown": "Unknown", "not_check_in": "Not checked in"}, "empty": {"description": "No Data"}, "messages": {"get_detail_failed": "Failed to get details", "get_stats_failed": "Failed to get statistics", "get_list_failed": "Failed to get waiting list"}, "review": {"title": "Waitlist Review", "buttons": {"approve": "Approve", "reject": "Reject", "back": "Back"}, "sections": {"activity_info": "Activity Information", "applicant_info": "Applicant Information", "review_info": "Review Information"}, "fields": {"activity_name": "Activity Name", "activity_time": "Activity Time", "activity_location": "Activity Location", "activity_status": "Activity Status", "current_participants": "Current Participants", "max_participants": "Max Participants", "name": "Name", "email": "Email", "phone": "Phone", "queue_position": "Queue Position", "application_status": "Application Status", "application_time": "Application Time", "remark": "Remark", "reviewer": "Reviewer", "review_time": "Review Time", "review_remark": "Review Remark"}, "status": {"waiting": "Waiting", "approved": "Approved", "rejected": "Rejected"}, "form": {"review_remark": "Review Remark", "review_remark_placeholder": "Please enter review remark"}, "validation": {"review_remark_max": "Review remark cannot exceed 500 characters"}, "dialogs": {"approve_confirm_title": "Tip", "approve_confirm_message": "Are you sure you want to approve this application?", "reject_confirm_title": "Tip", "reject_confirm_message": "Are you sure you want to reject this application?"}, "messages": {"get_detail_failed": "Failed to get details", "operation_success": "Operation successful", "operation_failed": "Operation failed"}}}, "calendar_details": {"dialog": {"title": "Activity Details", "default_title": "Activity Details"}, "timeline": {"view_details": "View Activity Details", "time_tbd": "Time TBD"}, "detail": {"activity_type": "Activity Type", "activity_name": "Activity Name", "activity_status": "Activity Status", "organizer": "Organizer", "activity_location": "Activity Location", "detailed_address": "Detailed Address", "activity_platform": "Activity Platform", "registration_deadline": "Registration Deadline"}, "activity_type": {"online": "Online", "offline": "Offline", "hybrid": "Hybrid"}, "activity_status": {"created": "Created", "published": "Published", "ongoing": "Ongoing", "completed": "Completed", "cancelled": "Cancelled"}, "button": {"add_activity": "Add Activity"}, "date_format": {"year": "Year", "month": "Month", "day": "Day"}}}