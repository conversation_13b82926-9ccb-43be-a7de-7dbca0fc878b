<?php

return [
    'nav' => [
        'activity' => 'Activity Management',
        'activity_list' => 'Activity List',
        'activity_calendar' => 'Activity Calendar',
        'activity_settings' => 'Activity Settings',
        'activity_template' => 'Template Management',
        'activity_settings_ticket' => 'Ticket Settings',
        'activity_admin_remind' => 'Admin Reminder',
        'activity_groups' => 'Activity Group Management',
        'activity_forms' => 'Form Management',
        'activity_email_template' => 'Email Template',
        'activity_rule_template' => 'Rule Template',
        'activity_invitation' => 'Invitation List',
        'activity_participant_list' => 'Participant List'
    ],
    'messages' => [
        'create_success' => 'Activity created successfully',
        'update_success' => 'Activity updated successfully',
        'delete_success' => 'Activity deleted successfully',
        'not_found' => 'Activity not found',
        'already_ended' => 'Activity has ended',
        'not_started' => 'Activity has not started yet',
        'status_update_failed' => 'Failed to update activity status',
        'cannot_end_activity' => 'Activity has not ended yet, cannot set status to completed',
        
        // Ticket deletion related
        'ticket_delete_failed_unique' => 'Cannot delete ticket type ":ticket_name", activity ":activity_name" only has this one ticket type. Please add other ticket types to the activity first, or delete the activity.',
        'ticket_delete_success' => 'Ticket type deleted successfully',
    ],
    'category' => [
        'holiday' => 'Holiday',
        'season' => 'Season',
        'workshop' => 'Workshop',
        'training' => 'Training',
        'meeting' => 'Meeting',
    ],
    'type' => [
        'online' => 'Online',
        'offline' => 'Offline',
        'hybrid' => 'Hybrid',
    ],
    'repeat_pattern' => [
        'once' => 'Once',
        'daily' => 'Daily',
        'weekly' => 'Weekly',
        'monthly' => 'Monthly',
    ],
    'status' => [
        'draft' => 'Draft',
        'created' => 'Created',
        'published' => 'Published',
        'cancelled' => 'Cancelled',
        'closed' => 'Closed',
        'completed' => 'Completed',
    ],
    'ticket_delivery_method' => [
        'electronic' => 'Electronic',
        'physical' => 'Physical',
    ],
    'activity_group' => [
        'name' => 'Activity Group Name',
        'status' => 'Status',
        'max_members' => 'Max Members',
        'can_register' => 'Can Register',
        'start_time' => 'Start Time',
        'end_time' => 'End Time',
    ],
    'scene' => [
        'activity_signup' => 'Activity Signup',
        'activity_feedback' => 'Activity Feedback',
        'onsite' => 'Onsite Rule',
        'online' => 'Online Rule',
        'hybrid' => 'Hybrid Rule',
    ],
    'localizations' => [
        'zh_CN' => 'Mainland China',
        'zh_HK' => 'Hong Kong',
        'en' => 'United States',
    ],
];
