<?php

declare(strict_types=1);

namespace Modules\Activity\Enums;

use Bingo\Enums\Traits\EnumEnhance;

/**
 * 活动模块错误码
 */
enum ActivityErrorCode: int
{
    use EnumEnhance;

    case ACTIVITY_NOT_FOUND = 150001;
    case ACTIVITY_ALREADY_ENDED = 150002;
    case ACTIVITY_NOT_STARTED = 150003;
    case ACTIVITY_FULL = 150004;
    case ALREADY_REGISTERED = 150005;
    case REGISTRATION_CLOSED = 150006;
    case CHECKIN_FAILED = 150007;
    case INVALID_ACTIVITY_DATA = 150008;
    case ACTIVITY_CREATION_FAILED = 150009;
    case ACTIVITY_UPDATE_FAILED = 150010;
    case ACTIVITY_DELETE_FAILED = 150011;
    case ACTIVITY_COPY_FAILED = 150012;
    case FORM_TEMPLATE_NOT_FOUND = 150101;
    case FORM_TEMPLATE_CREATE_FAILED = 150102;
    case FORM_TEMPLATE_UPDATE_FAILED = 150103;
    case FORM_TEMPLATE_DELETE_FAILED = 150104;
    case RULE_NOT_FOUND = 150201;
    case RULE_SAVE_FAILED = 150202;

    /**
     * 参与者不存在
     */
    case PARTICIPANT_NOT_FOUND = 14001;

    /**
     * 活动状态更新相关错误码
     */
    case ACTIVITY_STATUS_UPDATE_FAILED = 150301;
    case ACTIVITY_SCHEDULE_NOT_FOUND = 150302;

    public function httpCode(): int
    {
        return match ($this) {
            self::PARTICIPANT_NOT_FOUND => 404,
            default => 400,
        };
    }
} 