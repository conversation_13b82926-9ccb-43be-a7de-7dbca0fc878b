<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| 活动模块的API路由
|
*/

Route::group(['prefix' => 'activity'], function () {
    // 活动相关路由
    Route::get('/', 'ActivityController@index');
    Route::get('/{id}', 'ActivityController@show');
    
    // 活动报名相关路由
    Route::post('/{id}/register', 'ActivityController@register');
    Route::get('/{id}/registrations', 'ActivityController@registrations');
    
    // 活动签到相关路由
    Route::post('/{id}/checkin', 'ActivityController@checkin');
    
    // 活动日历相关路由
    Route::group(['prefix' => 'calendar'], function () {
        Route::get('/data', 'ActivityCalendarController@getActivitiesByDateRange');
        Route::get('/overlaps', 'ActivityCalendarController@checkTimeOverlaps');
        Route::get('/counts', 'ActivityCalendarController@getActivityCounts');
        Route::get('/activity/{activityId}', 'ActivityCalendarController@getActivitySchedules');
    });
}); 