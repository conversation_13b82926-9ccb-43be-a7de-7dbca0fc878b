<?php

declare(strict_types=1);

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel;
/**
 * 活动本地化选项模型
 *
 * @property int $id
 * @property string $locale 区域代码
 * @property string $name 区域名称
 * @property string $timezone 时区
 * @property string $currency_code 货币代码
 * @property string $currency_symbol 货币符号
 * @property int $creator_id 创建人ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间
 */
class ActivityLocalization extends BingoModel
{
    /**
     * 关联的数据表
     *
     * @var string
     */
    protected $table = 'activity_localizations';

    /**
     * 可以批量赋值的属性
     *
     * @var array<string>
     */
    protected $fillable = [
        'locale',
        'name',
        'timezone',
        'currency_code',
        'currency_symbol',
        'creator_id',
    ];

    /**
     * 应该被转换的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'id' => 'integer',
        'creator_id' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
        'deleted_at' => 'integer',
    ];
} 