<?php

declare(strict_types=1);

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;

/**
 * 活动邀请模型
 * 
 * @property int $id
 * @property int $activity_id 活动ID
 * @property string $title 名称
 * @property string $description 描述
 * @property int $is_default 是否默认 0-否 1-是
 * @property int $is_group 是否分配群组 0-否 1-是
 * @property int $group_id 群组ID
 * @property int $status 状态 0-禁用 1-启用
 * @property int $creator_id 创建人ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间
 */
class ActivityInvitation extends Model
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'activity_invitations';

    /**
     * 可以批量赋值的属性
     *
     * @var array<string>
     */
    protected $fillable = [
        'activity_id',
        'title',
        'description',
        'is_default',
        'is_group',
        'group_id',
        'status',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * 应该被转换的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'activity_id' => 'integer',
        'is_default' => 'integer',
        'is_group' => 'integer',
        'group_id' => 'integer',
        'status' => 'integer',
        'creator_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * 关联活动
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function activity()
    {
        return $this->belongsTo(Activity::class, 'activity_id');
    }

    /**
     * 关联群组
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function group()
    {
        return $this->belongsTo(ActivityGroup::class, 'group_id');
    }

    /**
     * 关联邀请成员
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function members()
    {
        return $this->hasMany(ActivityInvitationMember::class, 'invitation_id');
    }
} 