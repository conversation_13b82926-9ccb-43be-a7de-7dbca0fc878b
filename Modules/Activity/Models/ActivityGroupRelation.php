<?php

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;

class ActivityGroupRelation extends Model
{
    /**
     * 指定表名
     *
     * @var string
     */
    protected $table = 'activity_group_relations';

    public $timestamps = true;
    /**
     * 可批量赋值的属性
     *
     * @var array<string>
     */
    protected $fillable = [
        'activity_id',
        'group_id',
        'register_start_time',
        'register_end_time',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 属性类型转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'activity_id' => 'integer',
        'group_id' => 'integer',
        'creator_id' => 'integer',
    ];

    /**
     * 自定义时间戳的存储格式
     *
     * @var string
     */
    public $dateFormat = 'U';

    /**
     * 获取关联的群组
     */
    public function group()
    {
        return $this->hasOne(ActivityGroup::class, 'id', 'group_id');
    }

    /**
     * 获取关联的活动
     */
    public function activity()
    {
        return $this->belongsTo(Activity::class, 'activity_id');
    }
}
