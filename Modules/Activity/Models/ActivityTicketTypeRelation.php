<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-03-31 15:32:14
 * @LastEditTime: 2025-06-20 16:38:05
 * @LastEditors: <PERSON><PERSON>
 * @Description: 
 */
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-03-31 15:08:24
 * @LastEditTime: 2025-03-31 15:16:46
 * @LastEditors: Chiron
 * @Description: 票务群组关联表模型
 */

declare(strict_types=1);

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;
use Modules\Activity\Models\Activity;

class ActivityTicketTypeRelation extends Model
{
    protected $table = 'activity_ticket_type_relations';

    protected $fillable = [
        'activity_id',
        'ticket_type_id',
        'quota',
        'remaining_quota',
        'subtypes',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    protected $casts = [
        'subtypes' => 'array'
    ];

    public function ticketType()
    {
        return $this->belongsTo(ActivityTicketTypes::class, 'ticket_type_id')->select('id','name','code','is_external_sale','is_fee_required');
    }

    public function activity()
    {
        return $this->belongsTo(Activity::class, 'activity_id')->select('id','title','status');
    }
}
