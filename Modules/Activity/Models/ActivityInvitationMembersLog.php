<?php

declare(strict_types=1);

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;

/**
 * 活动邀请成员操作日志模型
 * 
 * @property int $id
 * @property int $activity_id 活动ID
 * @property int $invitation_id 邀请ID
 * @property int $member_id 成员ID
 * @property string $action 操作类型
 * @property string $remark 备注
 * @property string $ip 操作IP
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间
 */
class ActivityInvitationMembersLog extends Model
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'activity_invitation_members_logs';

    /**
     * 可以批量赋值的属性
     *
     * @var array<string>
     */
    protected $fillable = [
        'activity_id',
        'invitation_id',
        'member_id',
        'action',
        'remark',
        'ip',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * 应该被转换的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'activity_id' => 'integer',
        'invitation_id' => 'integer',
        'member_id' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
        'deleted_at' => 'integer'
    ];

    /**
     * 关联活动
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function activity()
    {
        return $this->belongsTo(Activity::class, 'activity_id');
    }

    /**
     * 关联邀请
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function invitation()
    {
        return $this->belongsTo(ActivityInvitation::class, 'invitation_id');
    }

    /**
     * 关联成员
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function member()
    {
        return $this->belongsTo(ActivityInvitationMember::class, 'member_id');
    }
} 