<?php

declare(strict_types=1);

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;

/**
 * 活动邀请成员模型
 * 
 * @property int $id
 * @property int $activity_id 活动ID
 * @property int $invitation_id 邀请ID
 * @property int $user_id 用户ID
 * @property string $name 姓名
 * @property string $phone 手机号
 * @property string $email 邮箱
 * @property string $company 公司
 * @property string $position 职位
 * @property string $code 邀请码
 * @property int $status 状态 0-未回复 1-已报名 2-已拒绝
 * @property int $creator_id 创建人ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间
 */
class ActivityInvitationMember extends Model
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'activity_invitation_members';

    /**
     * 可以批量赋值的属性
     *
     * @var array<string>
     */
    protected $fillable = [
        'activity_id',
        'invitation_id',
        'user_id',
        'name',
        'phone',
        'email',
        'company',
        'position',
        'code',
        'status',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * 应该被转换的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'activity_id' => 'integer',
        'invitation_id' => 'integer',
        'user_id' => 'integer',
        'status' => 'integer',
        'creator_id' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
        'deleted_at' => 'integer'
    ];

    /**
     * 关联活动
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function activity()
    {
        return $this->belongsTo(Activity::class, 'activity_id');
    }

    /**
     * 关联邀请
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function invitation()
    {
        return $this->belongsTo(ActivityInvitation::class, 'invitation_id');
    }

    /**
     * 关联操作日志
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function logs()
    {
        return $this->hasMany(ActivityInvitationMembersLog::class, 'member_id');
    }
} 