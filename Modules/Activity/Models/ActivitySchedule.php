<?php

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;
use Carbon\Carbon;

class ActivitySchedule extends Model
{
    protected $table = 'activity_schedules';
    
    protected $fillable = [
        'activity_id',
        'start_date',
        'end_date',
        'repeat_type',
        'repeat_frequency',
        'creator_id',
        'created_at',      // 创建时间
        'updated_at',      // 更新时间
        'deleted_at',      // 删除时间

    ];
   

    protected $casts = [
        'start_date' => 'datetime:Y-m-d',
        'end_date' => 'datetime:Y-m-d'
    ];


    

    public function activity()
    {
        return $this->belongsTo(Activity::class, 'activity_id');
    }

    public function timeSlots()
    {
        return $this->hasMany(ActivityScheduleTimeSlot::class, 'schedule_id');
    }

    public function breakPeriods()
    {
        return $this->hasMany(ActivityScheduleBreakPeriod::class, 'schedule_id');
    }
}
