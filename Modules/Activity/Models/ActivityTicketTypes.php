<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-03-31 14:39:54
 * @LastEditTime: 2025-06-21 15:52:49
 * @LastEditors: Chiron
 * @Description: 活动票务模型
 */

declare(strict_types=1);

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;

class ActivityTicketTypes extends Model
{

    protected $table = 'activity_ticket_types';

    public $timestamps = true;

    protected $fillable = [
        'name',
        'code',
        'description',
        'is_external_sale',
        'is_fee_required',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];


    /**
     * 票务群组
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function groups()
    {
        return $this->hasMany(ActivityTicketGroupsRelation::class, 'ticket_id', 'id');
    }
    /**
     * 票务活动
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function activities()
    {
        return $this->hasMany(Activity::class, 'ticket_id', 'id');
    }

    /** 票务子类型 */
    public function subtypes()
    {
        return $this->hasMany(ActivityTicketSubtypes::class, 'ticket_type_id', 'id')->select('id','ticket_type_id','name','sort','sort')->orderBy('sort', 'asc');
    }

    /**
     * 所属活动
     */
    public function activity()
    {
        return $this->belongsTo(Activity::class, 'activity_id');
    }
}
