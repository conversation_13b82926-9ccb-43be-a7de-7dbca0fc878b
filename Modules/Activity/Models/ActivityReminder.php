<?php

declare(strict_types=1);

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;

final class ActivityReminder extends Model
{
    protected $table = 'activity_reminders';

    protected $fillable = [
        'name',
        'type',
        'recipients',
        'activity_id',
        'settings',
        'status',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    protected $casts = [
        'type' => 'array',
        'recipients' => 'array',
        'settings' => 'array',
        'status' => 'integer',
    ];
} 