<?php

declare(strict_types=1);

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class ActivityParticipant extends BingoModel
{
    use HasFactory;

    protected $table = 'activity_participants';

    // 通知状态常量
    public const NOTIFICATION_TYPE_EMPTY = '';
    public const NOTIFICATION_TYPE_PENDING = 'pending';
    public const NOTIFICATION_TYPE_NOTIFIED = 'notified';
    public const NOTIFICATION_TYPE_EXPIRED = 'expired';

    // 参与状态常量
    public const STATUS_REGISTERED = 'registered';
    public const STATUS_CONFIRMED = 'confirmed';
    public const STATUS_CANCELLED = 'cancelled';
    public const STATUS_ATTENDED = 'attended';
    public const STATUS_WAITING = 'waiting';

    protected $fillable = [
        'activity_id',
        'user_id',
        'registration_time',
        'status',
        'check_in_time',
        'feedback',
        'notification_type',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $casts = [
        'activity_id' => 'integer',
        'user_id' => 'integer',
        'registration_time' => 'integer',
        'check_in_time' => 'integer',
        'creator_id' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
        'deleted_at' => 'integer',
    ];

    /**
     * 获取格式化的报名时间
     *
     * @return string|null
     */
    public function getRegistrationTimeAttribute(): ?string
    {
        $timestamp = $this->attributes['registration_time'] ?? null;
        return $timestamp ? Carbon::createFromTimestamp($timestamp)->format('Y-m-d H:i:s') : null;
    }

    /**
     * 获取格式化的签到时间
     *
     * @return string|null
     */
    public function getCheckInTimeAttribute(): ?string
    {
        $timestamp = $this->attributes['check_in_time'] ?? null;
        return $timestamp ? Carbon::createFromTimestamp($timestamp)->format('Y-m-d H:i:s') : null;
    }

    /**
     * 获取原始报名时间戳
     *
     * @return int|null
     */
    public function getRegistrationTimestamp(): ?int
    {
        return $this->attributes['registration_time'] ?? null;
    }

    /**
     * 获取原始签到时间戳
     *
     * @return int|null
     */
    public function getCheckInTimestamp(): ?int
    {
        return $this->attributes['check_in_time'] ?? null;
    }

    /**
     * 检查是否在等待名单中
     *
     * @return bool
     */
    public function isWaiting(): bool
    {
        return $this->status === self::STATUS_WAITING;
    }

    /**
     * 检查通知状态是否有效（仅在等待名单时有效）
     *
     * @return bool
     */
    public function isNotificationTypeValid(): bool
    {
        return $this->isWaiting();
    }

    /**
     * 设置通知状态为待通知（仅在等待名单时有效）
     *
     * @return bool
     */
    public function setPendingNotification(): bool
    {
        if (!$this->isWaiting()) {
            return false;
        }
        
        $this->notification_type = self::NOTIFICATION_TYPE_PENDING;
        return $this->save();
    }

    /**
     * 设置通知状态为已通知（仅在等待名单时有效）
     *
     * @return bool
     */
    public function setNotified(): bool
    {
        if (!$this->isWaiting()) {
            return false;
        }
        
        $this->notification_type = self::NOTIFICATION_TYPE_NOTIFIED;
        return $this->save();
    }

    /**
     * 设置通知状态为过期/放弃（仅在等待名单时有效）
     *
     * @return bool
     */
    public function setExpired(): bool
    {
        if (!$this->isWaiting()) {
            return false;
        }
        
        $this->notification_type = self::NOTIFICATION_TYPE_EXPIRED;
        return $this->save();
    }

    /**
     * 获取所有可用的通知状态
     *
     * @return array
     */
    public static function getNotificationTypes(): array
    {
        return [
            self::NOTIFICATION_TYPE_EMPTY => '未设置',
            self::NOTIFICATION_TYPE_PENDING => '待通知',
            self::NOTIFICATION_TYPE_NOTIFIED => '已通知',
            self::NOTIFICATION_TYPE_EXPIRED => '过期/放弃',
        ];
    }

    /**
     * 获取所有参与状态
     *
     * @return array
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_REGISTERED => '已报名',
            self::STATUS_CONFIRMED => '已确认',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_ATTENDED => '已出席',
            self::STATUS_WAITING => '等待名单',
        ];
    }
} 