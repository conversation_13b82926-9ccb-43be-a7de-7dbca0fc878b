<?php

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel;

class Activity extends BingoModel
{


    /**
     * 数据表名称
     *
     * @var string
     */
    protected $table = 'activity';

    /**
     * 可批量赋值的属性
     *
     * @var array<string>
     */
    protected $fillable = [
        // 活动基本信息
        'title',
        'category',
        'localized_id',
        'organizer_last_name',
        'organizer_first_name',
        'organizer_email',

        // 活动地点信息
        'type',
        'location',
        'address',
        'online_platform',
        'online_platform_url',

        // 活动时间设置
        'repeat_pattern',
        'repeat_interval',
        'repeat_days',
        'repeat_week_of_month',
        'repeat_day_of_week',
        'repeat_times_per_day',
        'repeat_end_date',
        'skip_non_existent_dates',
        'move_to_nearest_date',
        'break_start_time',
        'break_end_time',
        'start_time',
        'end_time',
        'publish_time',

        // 报名设置
        'registration_deadline',
        'registration_limit',
        'target_participants',
        'registration_type',
        'public_limit',
        'hidden_limit',
        'reserved_limit',
        'is_fee_required',
        'invoice_prefix',
        'target_income',
        'currency',
        'registration_method',
        'registration_method_rules',

        // 票务设置
        'ticket_delivery_method',

        // 状态
        'status',
        //创建着
        'creator_id',

        'rule_id',

        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 启用时间戳
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 应该被转换成日期的属性
     *
     * @var array<string>
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 应该被转换成的数据类型
     *
     * @var array<string, string>
     */
    protected $casts = [
        'max_participants' => 'integer',
        'current_participants' => 'integer',
        'status' => 'string',
        'registration_method_rules' => 'json',
        'repeat_days' => 'json',
        'repeat_interval' => 'integer',
        'repeat_day_of_week' => 'integer',
        'repeat_times_per_day' => 'integer',
        'skip_non_existent_dates' => 'integer',
        'move_to_nearest_date' => 'integer',
        'registration_limit' => 'integer',
        'target_participants' => 'integer',
        'registration_type' => 'integer',
        'public_limit' => 'integer',
        'hidden_limit' => 'integer',
        'reserved_limit' => 'integer',
        'is_fee_required' => 'integer',
        'target_income' => 'decimal:2'
    ];

    // 活动分类
    public static function categories(): array
    {
        return [
            'holiday' => T('Activity::category.holiday'),
            'season' => T('Activity::category.season'),
            'workshop' => T('Activity::category.workshop'),
            'training' => T('Activity::category.training'),
            'meeting' => T('Activity::category.meeting'),
            'exhibition' => T('Activity::category.exhibition'),
            'performance' => T('Activity::category.performance'),
            'other' => T('Activity::category.other')
        ];
    }

    // 活动形式
    public static function types(): array
    {
        return [
            'online' => T('Activity::type.online'),
            'offline' => T('Activity::type.offline'),
            'hybrid' => T('Activity::type.hybrid'),
        ];
    }

    public static function statuses(): array
    {
        return [
            'draft' => T('Activity::status.draft'),
            'created' => T('Activity::status.created'),
            'published' => T('Activity::status.published'),
            'cancelled' => T('Activity::status.cancelled'),
            'closed' => T('Activity::status.closed'),
            'completed' => T('Activity::status.completed'),
        ];
    }
    // 活动时间重复模式
    public static function repeatPatterns(): array
    {
        return [
            'once' => T('Activity::repeat_pattern.once'),
            'daily' => T('Activity::repeat_pattern.daily'),
            'weekly' => T('Activity::repeat_pattern.weekly'),
            'monthly' => T('Activity::repeat_pattern.monthly'),
        ];
    }
    /**
     * 获取活动票务方式字符串
     *
     * @return string
     */
    public static function ticketDeliveryMethods(): array
    {
        return [
            'electronic' => T('Activity::ticket_delivery_method.electronic'),
            'physical' => T('Activity::ticket_delivery_method.physical'),
        ];
    }

    /**
     * 获取活动分类字符串
     *
     * @return string
     */
    public function getCategoryStrAttribute(): string
    {
        return self::categories()[$this->category] ?? '';
    }

    /**
     * 获取活动状态字符串
     *
     * @return string
     */
    public function getStatusStrAttribute(): string
    {
        return self::statuses()[$this->status] ?? '';
    }

    /**
     * 获取活动类型字符串
     *
     * @return string
     */
    public function getTypeStrAttribute(): string
    {
        return self::types()[$this->type] ?? '';
    }


    /**
     * 获取活动票务方式字符串
     *
     * @return string
     */
    public function getTicketDeliveryMethodStrAttribute(): string
    {
        return self::ticketDeliveryMethods()[$this->ticket_delivery_method] ?? '';
    }

    /**
     * 获取活动关联的群组关系记录
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function groups()
    {
        return $this->hasMany(ActivityGroupRelation::class, 'activity_id');
    }


    /**
     * 获取活动本地化信息
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function localization()
    {
        return $this->hasOne(ActivityLocalization::class, 'id', 'localized_id')
            ->select(['id', 'locale', 'name', 'timezone', 'currency_code', 'currency_symbol']);
    }
    /**
     * 获取活动关联的时间规则记录
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function schedules()
    {
        return $this->hasOne(ActivitySchedule::class, 'activity_id');
    }

    /**
     * 获取活动关联的票务记录
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     * 
     */
    public function ticket()
    {
        return $this->hasMany(ActivityTicketTypeRelation::class, 'activity_id');
    }
}
