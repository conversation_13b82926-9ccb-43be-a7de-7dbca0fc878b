<?php

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;

class ActivityGroup extends Model
{
    /**
     * 指定表名
     *
     * @var string
     */
    protected $table = 'activity_groups';

    /**
     * 启用时间戳
     *
     * @var bool
     */
    public $timestamps = true;

        /**
     * 自定义时间戳的存储格式
     *
     * @var string
     */
    public $dateFormat = 'U';


    /**
     * 可批量赋值的属性
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'code',
        'max_members',
        'can_register',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 属性类型转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'max_members' => 'integer',
        'can_register' => 'integer',
        'creator_id' => 'integer',
    ];
    /**
     * 应该被转换成日期的属性
     *
     * @var array<string>
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 获取群组成员关系
     */
    public function members()
    {
        return $this->hasMany(ActivityGroupMember::class, 'group_id');
    }

    /**
     * 获取活动关联关系
     */
    public function activityRelations()
    {
        return $this->hasMany(ActivityGroupRelation::class, 'group_id');
    }

    /**
     * 获取群组关联的所有活动
     */
    public function activities()
    {
        return $this->belongsToMany(
            Activity::class,
            'activity_group_relations',
            'group_id',
            'activity_id'
        )->withPivot(['register_start_time', 'register_end_time']);
    }
}
