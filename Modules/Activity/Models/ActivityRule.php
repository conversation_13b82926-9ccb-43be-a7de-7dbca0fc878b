<?php

declare(strict_types=1);

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;

/**
 * 活动规则模型
 */
class ActivityRule extends Model
{
    /**
     * 表名
     * @var string
     */
    protected $table = 'activity_rules';

    /**
     * 可批量赋值字段
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'scene',
        'status',
        'config',
        'creator_id',      // 创建人ID
        'created_at',      // 创建时间
        'updated_at',      // 更新时间
        'deleted_at',      // 删除时间
    ];

    protected $casts = [
        'config' => 'array',
    ];
    
    /**
     * 使用场景 key 列表
     */
    public const SCENE_KEYS = [
        'onsite',
        'online',
        'hybrid',
        // 可根据实际业务继续扩展
    ];

    protected $appends = ['scene_text'];

    /**
     * 获取所有场景key及多语言label
     */
    public static function sceneList(): array
    {
        $result = [];
        foreach (self::SCENE_KEYS as $key) {
            $result[] = [
                'value' => $key,
                'label' => T('Activity::scene.' . $key),
            ];
        }
        return $result;
    }

    /**
     * 获取场景文本（多语言）
     */
    public function getSceneTextAttribute(): string
    {
        return T('Activity::scene.' . $this->scene);
    }
}
