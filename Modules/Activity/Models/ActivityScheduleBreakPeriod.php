<?php

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;

class ActivityScheduleBreakPeriod extends Model
{
    protected $table = 'activity_schedules_break_periods';

    public $timestamps = true;

    protected $fillable = [
        'schedule_id',
        'start_date',
        'end_date',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $casts = [
       'start_date' => 'datetime:Y-m-d',
        'end_date' => 'datetime:Y-m-d',
    ];
}

