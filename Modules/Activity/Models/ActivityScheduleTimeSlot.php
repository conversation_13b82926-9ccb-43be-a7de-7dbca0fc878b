<?php

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;

class ActivityScheduleTimeSlot extends Model
{
    protected $table = 'activity_schedules_time_slots';

    /**
     * 启用时间戳
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 自定义时间戳的存储格式
     *
     * @var string
     */

    protected $fillable = [
        'schedule_id',
        'start_time',
        'end_time',
        'day_of_week',
        'day_of_month',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $casts = [
        'day_of_week' => 'integer',
        'day_of_month' => 'json',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
    ];
}
