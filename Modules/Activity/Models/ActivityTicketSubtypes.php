<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-03-31 15:13:24
 * @LastEditTime: 2025-03-31 16:04:25
 * @LastEditors: <PERSON>ron
 * @Description: 
 */

declare(strict_types=1);

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;

class ActivityTicketSubtypes extends Model
{

    protected $table = 'activity_ticket_subtypes';

    public $timestamps = true;

    protected $fillable = [
        'ticket_type_id',
        'name',
        'sort',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $casts = [];


}
