<?php

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;
class ActivityGroupMember extends Model
{
    /**
     * 指定表名
     *
     * @var string
     */
    protected $table = 'activity_group_members';

    /**
     * 可批量赋值的属性
     *
     * @var array<string>
     */
    protected $fillable = [
        'group_id',
        'user_id',
        'join_time',
        'status',
        'creator_id',
    ];

    /**
     * 属性类型转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'group_id' => 'integer',
        'user_id' => 'integer',
        'join_time' => 'integer',
        'creator_id' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
        'deleted_at' => 'integer',
    ];

    /**
     * 自定义时间戳的存储格式
     *
     * @var string
     */
    public $dateFormat = 'U';

    /**
     * 状态常量定义
     */
    public const STATUS_ACTIVE = 'active';
    public const STATUS_INACTIVE = 'inactive';
    public const STATUS_REMOVED = 'removed';

    /**
     * 获取所属群组
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(ActivityGroup::class, 'group_id');
    }
} 