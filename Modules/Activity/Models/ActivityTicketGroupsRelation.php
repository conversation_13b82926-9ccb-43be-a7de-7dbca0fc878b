<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-03-31 15:08:24
 * @LastEditTime: 2025-03-31 15:16:46
 * @LastEditors: <PERSON>ron
 * @Description: 票务群组关联表模型
 */

declare(strict_types=1);

namespace Modules\Activity\Models;

use Bingo\Base\BingoModel as Model;

class ActivityTicketGroupsRelation extends Model
{
    protected $table = 'activity_ticket_groups_relations';

    protected $fillable = [
        'ticket_id',
        'group_id',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function ticket()
    {
        return $this->belongsTo(ActivityTicketTypes::class, 'ticket_id');
    }
}
