<?php

namespace Modules\Activity\Admin\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Activity\Models\Activity;

class ActivityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        /** @var Activity $this */
        return [
            'id' => $this->id,
            // 活动基本信息
            'title' => $this->title,
            'category_str' => $this->category_str,
            'localized_id' => $this->localized_id,

            // 活动时间
            'schedules' => $this->schedules,
            'start_time' => $this->schedules?->start_date->toDateString(),
            'end_time' => $this->schedules?->end_date->toDateString(),
            'publish_time' => $this->publish_time,
            'ticket_delivery_method_str' => $this->ticket_delivery_method_str,

            // 状态信息
            'status' => $this->status,
            'status_str' => $this->status_str,

            // 时间戳
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
