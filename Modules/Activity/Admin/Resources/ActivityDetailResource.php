<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-03-26 09:47:08
 * @LastEditTime: 2025-06-24 14:04:27
 * @LastEditors: Chiron
 * @Description: 
 */
/*
 * @Author: <PERSON>ron
 * @Date: 2025-03-26 09:47:08
 * @LastEditTime: 2025-03-27 17:44:39
 * @LastEditors: Chiron
 * @Description: 
 */

namespace Modules\Activity\Admin\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ActivityDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {

        return [
            'id' => $this->id,
            // 活动基本信息
            'title' => $this->title, // 活动标题
            'category' => $this->category, // 活动分类
            'category_str' => $this->category_str, // 活动分类
            'localized_id' => $this->localized_id, // 活动本地化ID
            'localization' => $this->whenLoaded('localization', function () {
                return [
                    'locale' => $this->localization->locale, // 本地化语言
                    'name' => $this->localization->name, // 本地化名称
                    'timezone' => $this->localization->timezone, // 时区
                    'currency_code' => $this->localization->currency_code, // 货币代码
                    'currency_symbol' => $this->localization->currency_symbol, // 货币符号
                ];
            }),
            'organizer_last_name' => $this->organizer_last_name, // 组织者姓氏
            'organizer_first_name' => $this->organizer_first_name, // 组织者名字
            'organizer_email' => $this->organizer_email, // 组织者邮箱

            // 活动地点
            'type' => $this->type, // 活动类型
            'type_str' => $this->type_str, // 活动类型字符串
            'location' => $this->location, // 活动地点
            'address' => $this->address, // 活动地址
            'online_platform_url' => $this->online_platform_url, // 在线平台URL
            'online_platform' => $this->online_platform, // 在线平台

            // 活动时间安排
            'schedule' => $this->whenLoaded('schedules', function ($schedule) {

                $repeat_type = $schedule->repeat_type ?? 'once';
                if ($repeat_type == 'once') {
                    $start_date = $schedule->start_date?->format('Y-m-d');
                    $end_date =  $schedule->end_date?->format('Y-m-d');
                }

                $schedule_data = [
                    'repeat_type' => $schedule->repeat_type,
                    'repeat_frequency' => $schedule->repeat_frequency,
                    'start_date' => $start_date ?? '',
                    'end_date' => $end_date ?? '',
                    'break_periods' => $schedule->breakPeriods->map(function ($period) {
                        return [
                            'start_date' => $period->start_date->format('Y-m-d'),
                            'end_date' => $period->end_date->format('Y-m-d')
                        ];
                    })
                ];
                if ($repeat_type == 'once') {
                    $timeSlots = $schedule->timeSlots->first();
                    $schedule_data['start_date'] = $start_date . ' ' . $timeSlots->start_time->format('H:i');
                    $schedule_data['end_date'] = $end_date . ' ' . $timeSlots->end_time->format('H:i');
                } else {
                    $schedule_data['time_slots'] = $schedule->timeSlots->map(function ($slot) {
                        return [
                            'start_time' => $slot->start_time->format('H:i'),
                            'end_time' => $slot->end_time->format('H:i'),
                            'day_of_week' => $slot->day_of_week,
                            'day_of_month' => $slot->day_of_month
                        ];
                    });
                }
                return $schedule_data;
            }),

            // 报名信息
            'registration_limit' => $this->registration_limit,
            'registration_limit_type' => ($this->registration_limit > 0 ? 1 : 0),

            'registration_type' => $this->registration_type,
            'target_participants' => $this->target_participants,
            'registration_deadline' => $this->registration_deadline,


            'public_limit' => $this->public_limit,
            'hidden_limit' => $this->hidden_limit,
            'reserved_limit' => $this->reserved_limit,

            'is_fee_required' => $this->is_fee_required,
            'invoice_prefix' => $this->invoice_prefix,
            'target_income' => $this->target_income,
            'currency' => $this->currency,
            'registration_method' => $this->registration_method,
            'registration_method_rules' => $this->registration_method_rules,
            'ticket_delivery_method' => $this->ticket_delivery_method,

            //群组
            'groups' => $this->whenLoaded('groups', function () {
                return $this->groups->map(function ($group) {
                    return [
                        'group_id' => $group->group_id,
                        'group_name' => $group->group->name ?? '',
                        'group_code' => $group->group->code ?? '',
                        'register_start_time' => $group->register_start_time,
                        'register_end_time' => $group->register_end_time,
                    ];
                });
            }),
            // 活动票务
            'ticket' => $this->whenLoaded('ticket', function () {
                return $this->ticket->map(function ($ticketType) {
                    return [
                        'id' => $ticketType->ticket_type_id,
                        'name' => $ticketType->ticketType->name ?? '',
                        'quota' => $ticketType->quota,
                        'remaining_quota' => $ticketType->remaining_quota ?? 0,
                        'subtypes' => $ticketType->subtypes,

                    ];
                });
            }),



            'publish_time' => $this->publish_time,

            'ticket_delivery_method_str' => $this->ticket_delivery_method_str,

            // 状态信息
            'status' => $this->status,
            'status_str' => $this->status_str,
            'rule_id' => $this->rule_id,

            // 时间戳
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
