<?php

declare(strict_types=1);

namespace Modules\Activity\Admin\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Iam\Models\IamUsers;

class ActivityParticipantResource extends JsonResource
{

    protected ?int $waitingNumber = null;

    public function __construct($resource, $waitingNumber = null)
    {
        parent::__construct($resource);
        $this->waitingNumber = $waitingNumber;
    }

    /**
     * @param \Modules\Activity\Models\ActivityParticipant $resource
     */
    public function toArray($request): array
    {
        // 获取用户信息
        $user = IamUsers::find($this->user_id);
        $data = [
            'id' => $this->id,
            'activity_id' => $this->activity_id,
            'user_id' => $this->user_id,
            'registration_time' => $this->getRegistrationTimestamp(), // 原始时间戳
            'registration_time_formatted' => $this->registration_time, // 格式化时间
            'status' => $this->status,
            'check_in_time' => $this->getCheckInTimestamp(),
            'check_in_time_formatted' => $this->check_in_time,
            'feedback' => $this->feedback,
            'notification_type' => $this->notification_type,
            'creator_id' => $this->creator_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->deleted_at,

            // 用户信息
            'user' => $user ? [
                'id' => $user->id,
                'name' => $user->name ?? '',
                'email' => $user->email ?? '',
                'phone' => $user->phone ?? '',
                'gender' => $user->gender ?? '',
                'photo' => $user->photo ?? '',
            ] : null,
        ];

        if ($this->waitingNumber) {
            $data['waiting_number'] = $this->waitingNumber;
        }

        return $data;
    }
}
