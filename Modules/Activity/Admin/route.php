<?php
/*
 * @Author: <PERSON>ron
 * @Date: 2025-03-17 19:17:16
 * @LastEditTime: 2025-06-18 16:43:51
 * @LastEditors: Chiron
 * @Description: 
 */

use Illuminate\Support\Facades\Route;

use Modules\Activity\Admin\Controllers\ActivityController;
use Modules\Activity\Admin\Controllers\ActivityGroupController;
use Modules\Activity\Admin\Controllers\ActivityTicketController;
use Modules\Activity\Admin\Controllers\ActivityCalendarController;
use Modules\Activity\Admin\Controllers\ActivityInvitationController;
use Modules\Activity\Admin\Controllers\ActivityFormController;
use Modules\Activity\Admin\Controllers\ActivityRuleController;
use Modules\Activity\Admin\Controllers\ActivityReminderController;
use Modules\Activity\Admin\Controllers\ActivityParticipantController;
/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| 活动模块的管理后台路由
|
*/

// 不需要认证的路由 - 明确指定不使用任何中间件
Route::middleware([])->withoutMiddleware(['auth.iam'])->group(function () {});

Route::group(['prefix' => 'activity'], function () {

        // 活动列表
    Route::get('/', [ActivityController::class, 'index'])->name('admin.activity.index');

    Route::get('/config', [ActivityController::class, 'getConfig'])->name('admin.activity.config');
    // 保存新活动
    Route::post('/store', [ActivityController::class, 'save'])->name('admin.activity.store');
    // 更新活动
    Route::post('/save/{id}', [ActivityController::class, 'save'])->name('admin.activity.update');

    // 复制活动
    Route::post('/copy/{id}', [ActivityController::class, 'copy'])->name('admin.activity.copy');

    // 保存草稿
    Route::post('/draft/save', [ActivityController::class, 'saveDraft'])->name('admin.activity.draft.save');
    // 获取草稿
    Route::get('/draft', [ActivityController::class, 'getDraft'])->name('admin.activity.draft.get');

    // 获取活动详情
    Route::get('/{id}', [ActivityController::class, 'detail'])->where('id', '[0-9]+')->name('admin.activity.detail');


    // 删除活动
    Route::delete('/{id}', [ActivityController::class, 'destroy'])->name('admin.activity.destroy');
    // 活动状态切换
    Route::put('/{id}/status', [ActivityController::class, 'updateStatus'])->name('admin.activity.status');

    // 活动群组
    Route::group(['prefix' => 'group'], function () {
        Route::get('/', [ActivityGroupController::class, 'index'])->name('admin.activity.group.index');
        Route::get('/{id}', [ActivityGroupController::class, 'detail'])->name('admin.activity.group.detail');
        Route::post('/store', [ActivityGroupController::class, 'save'])->name('admin.activity.group.save');
        Route::post('/{id}', [ActivityGroupController::class, 'save'])->name('admin.activity.group.edit');
    });

    // 票务
    Route::group(['prefix' => 'ticket'], function () {
        Route::get('/', [ActivityTicketController::class, 'index'])->name('admin.activity.ticket.index');
        Route::get('/{id}', [ActivityTicketController::class, 'detail'])->name('admin.activity.ticket.detail');
        
        Route::post('/store', [ActivityTicketController::class, 'save'])->name('admin.activity.ticket.save');
        Route::post('/{id}', [ActivityTicketController::class, 'save'])->name('admin.activity.ticket.edit');
        Route::post('/copy/{id}', [ActivityTicketController::class, 'copy'])->name('admin.activity.ticket.copy');
        Route::delete('/{id}', [ActivityTicketController::class, 'delete'])->name('admin.activity.ticket.delete');

        Route::get('/activity/{activity_id}', [ActivityTicketController::class, 'activityTickets'])->name('admin.activity.ticket.activity-tickets');
        Route::delete('/relation/{activity_id}/{ticket_type_id}', [ActivityTicketController::class, 'deleteActivityTicketRelation'])
            ->name('admin.activity.ticket.relation-delete');

        // 获取活动下的票务信息
        Route::get('/{activity_id}/{id}', [ActivityTicketController::class, 'tickets'])->name('admin.activity.ticket.tickets');
            
    });

    // 活动日历
    Route::group(['prefix' => 'calendar'], function () {
        Route::get('/', [ActivityCalendarController::class, 'getActivitiesByDateRange'])->name('admin.activity.calendar.data');
        Route::get('/overlaps', [ActivityCalendarController::class, 'checkTimeOverlaps'])->name('admin.activity.calendar.overlaps');
        Route::get('/counts', [ActivityCalendarController::class, 'getActivityCounts'])->name('admin.activity.calendar.counts');
        Route::get('/activity/{activityId}', [ActivityCalendarController::class, 'getActivitySchedules'])->name('admin.activity.calendar.schedules');
    });

    // 邀请管理
    Route::group(['prefix' => 'invitation'], function () {
        // 邀请管理
        Route::get('/', [ActivityInvitationController::class, 'index'])->name('admin.activity.invitation.index');
        Route::post('/store', [ActivityInvitationController::class, 'save'])->name('admin.activity.invitation.save');
        Route::post('/{id}', [ActivityInvitationController::class, 'save'])->name('admin.activity.invitation.edit');
        Route::post('/copy/{id}', [ActivityInvitationController::class, 'copy'])->name('admin.activity.invitation.copy');

        Route::get('/{id}', [ActivityInvitationController::class, 'detail'])->where('id', '[0-9]+')->name('admin.activity.invitation.detail');
        Route::delete('/{id}', [ActivityInvitationController::class, 'delete'])->where('id', '[0-9]+')->name('admin.activity.invitation.delete');

        // 邀请成员管理
        Route::get('/{id}/members', [ActivityInvitationController::class, 'members'])->name('admin.activity.invitation.members');
        Route::get('/{invitation_id}/members/{member_id}', [ActivityInvitationController::class, 'detailMember'])->name('admin.activity.invitation.memberDetail');
        Route::post('/{invitation_id}/members/store', [ActivityInvitationController::class, 'memberSave'])->name('admin.activity.invitation.memberSave');
        Route::post('/{invitation_id}/members/{id}', [ActivityInvitationController::class, 'memberSave'])->name('admin.activity.invitation.memberSave');
        // 导入邀请成员
        Route::post('/import-members/{id}', [ActivityInvitationController::class, 'importMembers'])->name('admin.activity.invitation.import');
        // 导出邀请成员
        Route::get('/export-members/{id}', [ActivityInvitationController::class, 'exportMembers'])->name('admin.activity.invitation.export');

        // 导出邀请成员模板
        Route::get('/export-template', [ActivityInvitationController::class, 'exportTemplate'])->name('admin.activity.invitation.export-template');

        // 删除邀请成员
        Route::delete('/members/{id}', [ActivityInvitationController::class, 'deleteMember'])->name('admin.activity.invitation.deleteMember');
    });

    // 表单管理
    Route::group(['prefix' => 'form'], function () {
        Route::get('/', [ActivityFormController::class, 'index'])->name('admin.activity.form.index');
        Route::get('/{id}', [ActivityFormController::class, 'detail'])->where('id', '[0-9]+')->name('admin.activity.form.detail');
        Route::post('/store', [ActivityFormController::class, 'save'])->name('admin.activity.form.store');
        Route::post('/{id}', [ActivityFormController::class, 'save'])->where('id', '[0-9]+')->name('admin.activity.form.edit');
        Route::delete('/{id}', [ActivityFormController::class, 'delete'])->where('id', '[0-9]+')->name('admin.activity.form.delete');
        Route::post('/batch-copy', [ActivityFormController::class, 'batchCopy'])->name('admin.activity.form.batch-copy');
        Route::post('/move', [ActivityFormController::class, 'move'])->name('admin.activity.form.move');
        Route::put('/{id}/status', [ActivityFormController::class, 'updateStatus'])->where('id', '[0-9]+')->name('admin.activity.form.status');
        Route::get('/scene-list', [ActivityFormController::class, 'sceneList'])->name('admin.activity.form.scene-list');
    });

    // 规则管理
    Route::group(['prefix' => 'rule'], function () {
        Route::get('/', [ActivityRuleController::class, 'index'])->name('admin.activity.rule.index');
        Route::get('/{id}', [ActivityRuleController::class, 'detail'])->where('id', '[0-9]+')->name('admin.activity.rule.detail');
        Route::post('/store', [ActivityRuleController::class, 'save'])->name('admin.activity.rule.store');
        Route::post('/{id}', [ActivityRuleController::class, 'save'])->where('id', '[0-9]+')->name('admin.activity.rule.edit');
        Route::delete('/{id}', [ActivityRuleController::class, 'delete'])->where('id', '[0-9]+')->name('admin.activity.rule.delete');
        Route::post('/batch-copy', [ActivityRuleController::class, 'batchCopy'])->name('admin.activity.rule.batch-copy');
        Route::post('/move', [ActivityRuleController::class, 'move'])->name('admin.activity.rule.move');
        Route::put('/{id}/status', [ActivityRuleController::class, 'updateStatus'])->where('id', '[0-9]+')->name('admin.activity.rule.status');
        Route::get('/scene-list', [ActivityRuleController::class, 'sceneList'])->name('admin.activity.rule.scene-list');
    });

    // 提醒管理
    Route::prefix('reminders')->group(function () {
        Route::get('/', [ActivityReminderController::class, 'index']);
        Route::get('{id}', [ActivityReminderController::class, 'show']);
        Route::post('/', [ActivityReminderController::class, 'store']);
        Route::put('{id}', [ActivityReminderController::class, 'update']);
        Route::delete('{id}', [ActivityReminderController::class, 'destroy']);
        Route::patch('{id}/status', [ActivityReminderController::class, 'toggleStatus']);
    });

    // 参与者管理
    Route::group(['prefix' => 'participants'], function () {
        Route::get('/', [ActivityParticipantController::class, 'index']);
        Route::get('{id}', [ActivityParticipantController::class, 'show']);
        Route::delete('{id}', [ActivityParticipantController::class, 'destroy']);
        Route::patch('{id}/status', [ActivityParticipantController::class, 'updateStatus']);
        Route::get('stats/{id}', [ActivityParticipantController::class, 'stats'])->name('admin.activity.participant.stats');
    });
});
