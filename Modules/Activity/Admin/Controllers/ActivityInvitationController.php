<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-04-02 10:00:00
 * @LastEditTime: 2025-06-07 17:11:36
 * @LastEditors: Chiron
 * @Description: 活动邀请控制器
 */

declare(strict_types=1);

namespace Modules\Activity\Admin\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Activity\Admin\Requests\ActivityInvitation\StoreMemberRequest;
use Modules\Activity\Admin\Requests\ActivityInvitation\StoreRequest;
use Modules\Activity\Services\ActivityInvitationService;
use Bingo\Base\BingoController as Controller;

/**
 * 活动邀请控制器
 */
class ActivityInvitationController extends Controller
{
    /**
     * @var ActivityInvitationService
     */
    protected ActivityInvitationService $invitationService;

    /**
     * 构造函数
     *
     * @param ActivityInvitationService $invitationService
     */
    public function __construct(ActivityInvitationService $invitationService)
    {
        $this->invitationService = $invitationService;
    }

    /**
     * 获取活动邀请列表
     *
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        $params = $request->all();
        return $this->invitationService->list($params);
    }

    /**
     * 获取活动邀请详情
     *
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function detail(int $id): array
    {
        $result = $this->invitationService->detail($id);
        return $result->toArray();
    }

    /**
     * 删除活动邀请
     *
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function delete(Request $request, int $id)
    {
        $result = $this->invitationService->delete($id);
        return $result;
    }

    /**
     * 保存活动邀请信息
     *
     * @param StoreRequest $request
     * @param int|null $id
     * @return array
     */
    public function save(StoreRequest $request, ?int $id = null): array
    {
        $data = $request->validated();
        $data['creator_id'] = Auth::id();

        // 保存基本邀请信息
        $result = $this->invitationService->save($data, $id);

        return $result->toArray();
    }

    /**
     * 复制邀请
     *
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function copy(Request $request, int $id)
    {
        $result = $this->invitationService->copy($id);
        return $result->toArray();
    }

    /**
     * 导出邀请列表成员数据（无需认证）
     *
     * @param Request $request
     * @param int $id 邀请ID
     * @return \Symfony\Component\HttpFoundation\StreamedResponse|JsonResponse
     */
    public function exportMembers(Request $request, int $id)
    {
        if (!$id) {
            return response()->json(['code' => 400, 'message' => T('Activity::invitation.messages.param_error')], 400);
        }

        try {
            // 防止 CSRF 攻击，可以添加一些额外的验证
            // 例如，检查请求中是否包含有效的 token 或其他验证手段

            return $this->invitationService->exportMembers($id);
        } catch (\Exception $e) {
            // 记录错误日志但不暴露具体错误信息给外部
            \Log::error('导出成员数据失败', [
                'invitation_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['code' => 500, 'message' => T('Activity::invitation.messages.export_error')], 500);
        }
    }

    /**
     * 导出邀请成员导入模板
     * 
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse|JsonResponse
     */
    public function exportTemplate(Request $request)
    {
        try {
            return $this->invitationService->exportTemplate();
        } catch (\Exception $e) {
            // 记录错误日志但不暴露具体错误信息给外部
            \Log::error('导出成员导入模板失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['code' => 500, 'message' => T('Activity::invitation.messages.export_error')], 500);
        }
    }

    /**
     * 导入邀请成员
     *
     * @param Request $request
     * @return 
     */
    public function importMembers(Request $request, int $id)
    {
        $file = $request->file('file');
        $result = $this->invitationService->importMembers($id, $file);
        return $result;
    }

    /**
     * 获取邀请成员列表
     *
     * @param Request $request
     * @param int $id 邀请ID
     * @return array
     */
    public function members(Request $request, int $id)
    {
        $params = $request->all();
        $result = $this->invitationService->listMembers($id, $params);
        return $result;
    }
    /**
     * 保存邀请成员
     *
     * @param StoreMemberRequest $request
     * @param int $invitation_id
     * @param int|null $id 成员ID（更新时提供）
     * @return array
     */
    public function memberSave(StoreMemberRequest $request, int $invitation_id, ?int $id = null): array
    {
        $data = $request->validated();
        $data['creator_id'] = Auth::id();
        $result = $this->invitationService->saveMembers($invitation_id, $data, $id);
        return  $result->toArray();
    }

    /**
     * 删除邀请成员
     *
     * @param Request $request
     * @param int $id 成员ID
     * @return array
     */
    public function deleteMember(Request $request, int $id) {
        $result = $this->invitationService->deleteMember($id);
        return 'ok';
    }
    
    /**
     * 获取邀请成员详情
     *
     * @param Request $request
     * @param int $invitation_id
     * @param int $member_id
     */
    public function detailMember(Request $request, int $invitation_id, int $member_id) {
        $result = $this->invitationService->detailMember($invitation_id, $member_id);
        return $result->toArray();
    }
}
