<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-03-26 09:47:08
 * @LastEditTime: 2025-04-16 11:46:24
 * @LastEditors: <PERSON>ron
 * @Description: 
 */

declare(strict_types=1);

namespace Modules\Activity\Admin\Controllers;

use Illuminate\Http\JsonResponse;
use Modules\Activity\Admin\Requests\ActivityGroup\StoreRequest;
use Modules\Activity\Services\ActivityGroupService;
use Bingo\Base\BingoController as Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

/**
 * 活动组控制器
 */
class ActivityGroupController extends Controller
{
    /**
     * @var ActivityGroupService
     */
    protected ActivityGroupService $groupService;

    /**
     * 构造函数
     *
     * @param ActivityGroupService $groupService
     */
    public function __construct(ActivityGroupService $groupService)
    {
        $this->groupService = $groupService;
    }

    /**
     * 获取活动组列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): array
    {
        $params = $request->all();
        $per_page = (int)$request->input('per_page', 15);
        $result = $this->groupService->list($params, $per_page);
        return [
            'items' => $result->items(),
            'total' => $result->total()
        ];
    }
    public function detail(Request $request, int $id): array 
    {
        $result = $this->groupService->detail($id);
        return $result->toArray();
    }

    /**
     * 创建活动组
     *
     * @param StoreRequest $request
     * @return array
     */
    public function save(StoreRequest $request, ?int $id = null): array
    {
        $data = $request->validated();
        $data['creator_id'] = Auth::id();
        $result = $this->groupService->save($data, $id)->toArray();

        $request_data = [
            'group_id' => $result['id'],
            'name' => $result['name'],
            'code' => $result['code'],
        ];
        $start_time = Arr::get($data, 'start_time', '');
        $end_time = Arr::get($data, 'end_time', '');
        if ($start_time && $end_time) {
            $request_data['start_time'] = $start_time;
            $request_data['end_time'] = $end_time;
        }
        return $request_data;
    }
}
