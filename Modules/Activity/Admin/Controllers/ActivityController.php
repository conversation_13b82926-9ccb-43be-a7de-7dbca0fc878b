<?php

declare(strict_types=1);

namespace Modules\Activity\Admin\Controllers;

use Bingo\Base\BingoController as Controller;

use Modules\Activity\Admin\Requests\ListRequest;
use Modules\Activity\Admin\Requests\StoreRequest;
use Modules\Activity\Admin\Requests\CopyActivityRequest;
use Modules\Activity\Services\ActivityService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Modules\Activity\Admin\Resources\ActivityDetailResource;

final class ActivityController extends Controller
{
    /**
     * @var ActivityService
     */
    private ActivityService $activityService;

    /**
     * 构造函数
     *
     * @param ActivityService $activityService
     */
    public function __construct(ActivityService $activityService)
    {
        $this->activityService = $activityService;
    }

    /**
     * 活动列表
     *
     * @param ListRequest $request
     * @return array
     */
    public function index(Request $request): array
    {
        $result = $this->activityService->getActivityList(
            $request->all(),
            (int)$request->input('per_page', 15)
        );

        return [
            'items' => $result->items(),
            'total' => $result->total()
        ];
    }
    /**
     * 保存活动（创建或更新）
     *
     * @param StoreRequest $request
     * @param int|null $id 活动ID（更新时传入）
     * @return array
     */
    public function save(StoreRequest $request, ?int $id = null): array
    {
        $data = $request->validated();
        $user_id = Auth::guard()->user()->id;
        $data['creator_id'] = $user_id;
        $activity = $this->activityService->saveActivity($data, $id);
        return $activity->toArray();
    }

    /**
     * 复制活动
     *
     * @param CopyActivityRequest $request
     * @param int $id
     * @return array
     */
    public function copy(CopyActivityRequest $request, int $id): array
    {
        $user_id = Auth::guard()->user()->id;
        $copyData = $request->validated();
        
        $activity = $this->activityService->copyActivity($id, $user_id, $copyData);
        return $activity->toArray();
    }
    /** 
     * 保存草稿
     *
     * @param CreateRequest $request
     * @return array
     */
    public function saveDraft(Request $request): array
    {

        $data = $request->all();
        $user_id = Auth::guard()->user()->id;
        $data['creator_id'] = $user_id;
        $activity = $this->activityService->saveDraft($data);
        return $activity;
    }

    /**
     * 获取草稿
     *
     * @param Request $request
     * @return array
     */
    public function getDraft(Request $request): array
    {
        $user_id = Auth::guard()->user()->id;
        $activity = $this->activityService->getDraft($user_id);
        return $activity;
    }
    /**
     * 获取活动配置
     *
     * @return array
     */
    public function getConfig(): array
    {
        $config = $this->activityService->getConfig();
        return $config;
    }

    /**
     * 获取活动详情
     *
     * @param int $id
     * @return array
     */
    public function detail(int $id): array
    {
        $activity = $this->activityService->getActivityDetail($id);
        return $activity->toArray(request());
    }

    /**
     * 删除活动
     *
     * @param int $id
     * @return array
     */
    public function destroy(int $id): array
    {
        $result = $this->activityService->deleteActivity($id);
        return ['success' => $result];
    }

    /**
     * 更新活动状态
     *
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function updateStatus(Request $request, int $id): array
    {
        $status = $request->input('status');
        $activity = $this->activityService->updateActivityStatus($id, $status);
        return $activity->toArray();
    }
}
