<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-03-31 14:36:08
 * @LastEditTime: 2025-06-10 10:08:58
 * @LastEditors: Chiron
 * @Description: 活动票务控制器
 */

declare(strict_types=1);

namespace Modules\Activity\Admin\Controllers;

use Illuminate\Http\JsonResponse;
use Modules\Activity\Admin\Requests\ActivityTicket\StoreRequest;
use Modules\Activity\Services\ActivityTicketService;
use Bingo\Base\BingoController as Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class ActivityTicketController extends Controller
{
    /**
     * @var ActivityTicketService
     */
    protected ActivityTicketService $ticketService;

    /**
     * 构造函数
     */
    public function __construct(ActivityTicketService $ticketService)
    {
        $this->ticketService = $ticketService;
    }

    /**
     * 获取票务列表
     */
    public function index(Request $request): array
    {
        $params = $request->all();
        $result = $this->ticketService->list($params);
        return [
            'items' => $result->items(),
            'total' => $result->total()
        ];
    }

    public function detail(Request $request, int $id): array
    {

        $result = $this->ticketService->detail($id);
        return $result->toArray();
    }

    /**
     * 创建票务
     */
    public function save(StoreRequest $request, ?int $id = null)
    {
        $data = $request->validated();
        $data['creator_id'] = Auth::id();
        $result = $this->ticketService->save($data, $id);
        return $result->toArray();
    }

    /**
     * 复制票务
     */
    public function copy(Request $request, int $id)
    {
        $result = $this->ticketService->copy($id);
        return $result->toArray();
    }

    public function delete(Request $request, int $id)
    {
        $result = $this->ticketService->delete($id);
        return ['ok'];
    }

    /**
     * 获取指定活动下的票务列表
     */
    public function activityTickets(Request $request, int $activity_id)
    {
        $perPage = $request->input('per_page', 20);
        $result = $this->ticketService->getTicketsByActivity($activity_id, $perPage);
        return [
            'items' => $result->items(),
            'total' => $result->total()
        ];
    }

    /**
     * 删除指定活动下的所有票务关联
     */
    public function deleteActivityTickets(int $activity_id): array
    {
        $count = $this->ticketService->deleteTicketsByActivity($activity_id);
        return ['deleted' => $count];
    }

    /**
     * 删除指定活动下的指定票务关联
     */
    public function deleteActivityTicketRelation(int $activity_id, int $ticket_type_id): array
    {
        $count = $this->ticketService->deleteActivityTicketRelation($activity_id, $ticket_type_id);
        return ['deleted' => $count];
    }

    public function tickets(Request $request, int $activity_id, int $id)
    {
        $result = $this->ticketService->getTicketsByActivityInfo($activity_id, $id);
        return $result->toArray();
    }
}
