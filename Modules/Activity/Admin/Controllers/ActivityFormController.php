<?php

declare(strict_types=1);

namespace Modules\Activity\Admin\Controllers;

use Illuminate\Http\Request;
use Bingo\Base\BingoController as Controller;
use Modules\Activity\Services\ActivityFormService;
use Modules\Activity\Models\ActivityForm;

class ActivityFormController extends Controller
{
    protected ActivityFormService $service;

    public function __construct(ActivityFormService $service)
    {
        $this->service = $service;
    }

    // 表单列表
    public function index(Request $request): array
    {
        $result = $this->service->list($request->all());
        return [
            'items' => $result->items(),
            'total' => $result->total(),
        ];
    }

    // 表单详情
    public function detail(int $id): array
    {
        $result = $this->service->detail($id);
        return $result->toArray();
    }

    // 新建/编辑表单
    public function save(Request $request, int $id = null): array
    {
        $result = $this->service->save($request->all(), $id);
        return $result->toArray();
    }

    // 删除表单
    public function delete(Request $request, int $id = null): array
    {
        $ids = $id ? [$id] : (array)$request->input('ids', []);
        $this->service->delete($ids);
        return ['success' => true];
    }

    // 批量复制
    public function batchCopy(Request $request): array
    {
        $ids = (array)$request->input('ids', []);
        $this->service->batchCopy($ids);
        return ['success' => true];
    }

    // 移动表单
    public function move(Request $request): array
    {
        $ids = (array)$request->input('ids', []);
        $targetGroupId = $request->input('target_group_id');
        $this->service->move($ids, $targetGroupId);
        return ['success' => true];
    }

    // 状态切换
    public function updateStatus(Request $request, int $id): array
    {
        $status = (int)$request->input('status');
        $this->service->updateStatus($id, $status);
        return ['success' => true];
    }

    /**
     * 获取表单使用场景列表
     * @return array
     */
    public function sceneList(): array
    {
        return ActivityForm::sceneList();
    }
} 