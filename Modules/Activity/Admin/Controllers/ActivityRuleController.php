<?php

declare(strict_types=1);

namespace Modules\Activity\Admin\Controllers;

use Illuminate\Http\Request;
use Bingo\Base\BingoController as Controller;
use Modules\Activity\Services\ActivityRuleService;

/**
 * 活动规则管理控制器
 */
class ActivityRuleController extends Controller
{
    public function __construct(protected ActivityRuleService $service)
    {
    }

    /**
     * 规则列表
     */
    public function index(Request $request)
    {
        return $this->service->list($request->all());
    }

    /**
     * 规则详情
     */
    public function detail(int $id)
    {
        return $this->service->detail($id)->toArray();
    }

    /**
     * 新建/编辑规则
     */
    public function save(Request $request, int $id = null)
    {
        $data = $request->all();
        return $this->service->save($data, $id)->toArray();
    }

    /**
     * 删除规则
     */
    public function delete(int $id)
    {
        return $this->service->delete($id);
    }

    /**
     * 批量复制
     */
    public function batchCopy(Request $request)
    {
        $ids = $request->input('ids', []);
        return $this->service->batchCopy($ids);
    }

    /**
     * 批量移动
     */
    public function move(Request $request)
    {
        $ids = $request->input('ids', []);
        $scene = $request->input('scene');
        return $this->service->move($ids, $scene)->toArray();
    }

    /**
     * 状态切换
     */
    public function updateStatus(Request $request, int $id)
    {
        $status = $request->input('status');
        return $this->service->updateStatus($id, $status);
    }

    /**
     * 场景列表
     */
    public function sceneList()
    {
        return $this->service->sceneList();
    }
} 