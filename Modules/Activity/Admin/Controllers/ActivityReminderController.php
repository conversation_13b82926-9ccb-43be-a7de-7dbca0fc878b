<?php

declare(strict_types=1);

namespace Modules\Activity\Admin\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Activity\Services\ActivityReminderService;

final class ActivityReminderController extends Controller
{
    public function __construct(
        protected readonly ActivityReminderService $service
    ) {}

    public function index(Request $request)
    {
        return $this->service->list($request->all());
    }

    public function show(int $id)
    {
        $reminder = $this->service->get($id);
        return $reminder;
    }

    public function store(Request $request)
    {
        $reminder = $this->service->create($request->all());
        return $reminder->toArray();
    }

    public function update(Request $request, int $id)
    {
        $reminder = $this->service->update($id, $request->all());
        return $reminder->toArray();
    }

    public function destroy(int $id)
    {
        $this->service->delete($id);
        return '删除成功';
    }

    public function toggleStatus(Request $request, int $id)
    {
        $reminder = $this->service->toggleStatus($id, (bool)$request->input('status'));
        return $reminder->toArray();
    }
} 