<?php

namespace Modules\Activity\Admin\Controllers;

use Illuminate\Http\Request;
use Modules\Activity\Services\ActivityCalendarService;
use Illuminate\Support\Facades\Response;

class ActivityCalendarController
{
    protected $service;

    public function __construct(ActivityCalendarService $service)
    {
        $this->service = $service;
    }


    /**
     * 获取指定时间段内的活动并按天分组
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getActivitiesByDateRange(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        // 获取请求参数
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // 获取按天分组的活动
        $calendarData = $this->service->getActivitiesByDateRange($startDate, $endDate);

        // 返回成功响应
        return $calendarData;
    }
    
    /**
     * 检查指定日期的活动时间重叠情况
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkTimeOverlaps(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'date' => 'required|date',
        ]);

        // 获取请求参数
        $date = $request->input('date');

        // 检查时间重叠
        $overlapsData = $this->service->checkTimeOverlaps($date);

        // 返回成功响应
        return Response::json([
            'success' => true,
            'data' => $overlapsData
        ]);
    }
    
    /**
     * 获取指定日期范围内每天的活动数量统计
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getActivityCounts(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        // 获取请求参数
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // 获取活动数量统计
        $countsData = $this->service->getActivityCountsByDateRange($startDate, $endDate);

        // 返回成功响应
        return Response::json([
            'success' => true,
            'data' => $countsData
        ]);
    }
    
    /**
     * 获取指定活动在日期范围内的所有日程安排
     * 
     * @param Request $request
     * @param int $activityId 活动ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getActivitySchedules(Request $request, $activityId)
    {
        // 验证请求参数
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        // 获取请求参数
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // 获取活动日程
        $schedulesData = $this->service->getActivitySchedulesByDateRange($activityId, $startDate, $endDate);

        // 返回成功响应
        return Response::json([
            'success' => true,
            'data' => $schedulesData
        ]);
    }
}