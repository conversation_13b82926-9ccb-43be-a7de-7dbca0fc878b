<?php

declare(strict_types=1);

namespace Modules\Activity\Admin\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Activity\Services\ActivityParticipantService;
use Modules\Activity\Models\ActivityParticipant;
use Modules\Activity\Models\Activity;

final class ActivityParticipantController extends Controller
{
    public function __construct(
        protected readonly ActivityParticipantService $service
    ) {}

    public function index(Request $request)
    {
        return $this->service->list($request->all());
    }

    public function show(int $id)
    {
        return $this->service->get($id);
    }

    public function destroy(int $id)
    {
        $this->service->delete($id);
        return '删除成功';
    }

    public function updateStatus(Request $request, int $id)
    {
        return $this->service->updateStatus($id, $request->input('status'));
    }

    /**
     * 统计活动参与者相关数据
     *
     * @param int $id 活动ID
     * @param Request $request
     * @return array
     */
    public function stats(int $id, Request $request): array
    {
        // 缓存 key
        $cacheKey = 'activity:participant:stats:' . $id;
        // 60 秒缓存
        return \Cache::remember($cacheKey, 60, function () use ($id) {
            // 报名总数（不含waiting）
            $total = ActivityParticipant::where('activity_id', $id)
                ->whereIn('status', ['registered', 'confirmed', 'attended'])
                ->count();

            // 限制人数
            $limit = Activity::where('id', $id)->value('registration_limit') ?? 0;

            // 等待名单人数
            $waiting = ActivityParticipant::where('activity_id', $id)
                ->where('status', 'waiting')
                ->count();

            // 等待名单转报名成功总人数（creator_id > 0，且 status=registered/confirmed/attended）
            $waitingToSuccess = ActivityParticipant::where('activity_id', $id)
                ->whereIn('status', ['registered', 'confirmed', 'attended'])
                ->where('creator_id', '>', 0)
                ->count();

            // 其中自动转报名人数（creator_id = 1）
            $autoTransfer = ActivityParticipant::where('activity_id', $id)
                ->whereIn('status', ['registered', 'confirmed', 'attended'])
                ->where('creator_id', 1)
                ->count();

            // 管理员审核转报名人数（creator_id > 1）
            $adminTransfer = ActivityParticipant::where('activity_id', $id)
                ->whereIn('status', ['registered', 'confirmed', 'attended'])
                ->where('creator_id', '>', 1)
                ->count();

            // 各状态分组统计
            $statusGroup = ActivityParticipant::where('activity_id', $id)
                ->selectRaw('status, count(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status');

            // 返回数据增加详细注释
            return [
                // 报名总数（不含waiting）
                'total' => $total, // int 报名成功人数
                // 活动报名限制人数
                'limit' => $limit, // int 活动报名上限
                // 等待名单人数
                'waiting' => $waiting, // int 当前等待名单人数
                // 等待名单转报名成功总人数（creator_id > 0）
                'waiting_to_success' => $waitingToSuccess, // int 等待名单转报名成功总人数
                // 自动转报名人数（creator_id = 1）
                'auto_transfer' => $autoTransfer, // int 自动转报名人数
                // 管理员审核转报名人数（creator_id > 1）
                'admin_transfer' => $adminTransfer, // int 管理员审核转报名人数
                // 各状态分组统计
                'status_group' => $statusGroup, // array 各状态下的参与者数量，key为状态名，value为数量
            ];
        });
    }
} 