<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-03-31 14:40:27
 * @LastEditTime: 2025-06-21 15:42:42
 * @LastEditors: Chiron
 * @Description: 票务创建请求验证
 */

declare(strict_types=1);

namespace Modules\Activity\Admin\Requests\ActivityTicket;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    /**
     * 准备验证数据
     */
    protected function prepareForValidation()
    {
        $data = $this->all();
        
        // 处理subtypes中的数据类型转换
        if (isset($data['subtypes']) && is_array($data['subtypes'])) {
            foreach ($data['subtypes'] as $key => $subtype) {
                // 确保price字段为数值类型
                if (isset($subtype['price']) && $subtype['price'] !== null && $subtype['price'] !== '') {
                    $data['subtypes'][$key]['price'] = (float) $subtype['price'];
                }
                
                // 确保sort字段为整数类型
                if (isset($subtype['sort']) && $subtype['sort'] !== null && $subtype['sort'] !== '') {
                    $data['subtypes'][$key]['sort'] = (int) $subtype['sort'];
                } else {
                    // 如果没有设置sort，则默认为0
                    $data['subtypes'][$key]['sort'] = 0;
                }
            }
        }
        
        // 确保quota字段为整数类型
        if (isset($data['quota']) && $data['quota'] !== null && $data['quota'] !== '') {
            $data['quota'] = (int) $data['quota'];
        }
        
        $this->replace($data);
    }
    
    /**
     * 验证规则
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100|min:1',
            'code' => 'required|string|max:50|min:1|regex:/^[a-zA-Z0-9_-]+$/',
            'quota' => 'nullable|integer|min:0|max:999999',
            'activity_id' => 'nullable|integer|exists:activity,id',
            'description' => 'nullable|string|max:255',
            'is_external_sale' => ['required', 'integer', Rule::in([1, 0])],
            'is_fee_required' => ['required', 'integer', Rule::in([1, 0])],
            'subtypes' => 'required|array|min:1|max:20',
            'subtypes.*.name' => 'required|string|max:100|min:1',
            'subtypes.*.price' => 'nullable|numeric|min:0|max:999999.99',

            'subtypes.*.start_date' => 'nullable|date_format:Y-m-d H:i',
            'subtypes.*.end_date' => 'nullable|date_format:Y-m-d H:i|after_or_equal:subtypes.*.start_date',
            'subtypes.*.sort' => 'nullable|integer|min:0|max:999',
            'groups' => 'nullable|array|max:50',
            'groups.*.id' => 'required|integer|exists:activity_groups,id',
        ];
    }



    /**
     * 自定义错误消息
     */
    public function messages(): array
    {
        return [
            'name.required' => T('Activity::validation.ticket.name.required'),
            'name.min' => T('Activity::validation.ticket.name.min'),
            'name.max' => T('Activity::validation.ticket.name.max'),
            'code.required' => T('Activity::validation.ticket.code.required'),
            'code.min' => T('Activity::validation.ticket.code.min'),
            'code.max' => T('Activity::validation.ticket.code.max'),
            'code.regex' => T('Activity::validation.ticket.code.regex'),
            'quota.min' => T('Activity::validation.ticket.quota.min'),
            'quota.max' => T('Activity::validation.ticket.quota.max'),
            'subtypes.required' => T('Activity::validation.ticket.subtypes.required'),
            'subtypes.min' => T('Activity::validation.ticket.subtypes.min'),
            'subtypes.max' => T('Activity::validation.ticket.subtypes.max'),
            'subtypes.*.name.required' => T('Activity::validation.ticket.subtype.name.required'),
            'subtypes.*.name.min' => T('Activity::validation.ticket.subtype.name.min'),
            'subtypes.*.name.max' => T('Activity::validation.ticket.subtype.name.max'),
            'subtypes.*.price.min' => T('Activity::validation.ticket.subtype.price.min'),
            'subtypes.*.price.max' => T('Activity::validation.ticket.subtype.price.max'),
            'subtypes.*.sort.min' => T('Activity::validation.ticket.subtype.sort.min'),
            'subtypes.*.sort.max' => T('Activity::validation.ticket.subtype.sort.max'),
            'subtypes.*.start_date.date_format' => T('Activity::validation.ticket.subtype.start_date.date_format'),
            'subtypes.*.end_date.date_format' => T('Activity::validation.ticket.subtype.end_date.date_format'),
            'subtypes.*.end_date.after_or_equal' => T('Activity::validation.ticket.subtype.end_date.after_or_equal'),
            'activity_id.exists' => T('Activity::validation.ticket.activity_id.exists'),
            'groups.max' => T('Activity::validation.ticket.groups.max'),
            'groups.*.id.exists' => T('Activity::validation.ticket.groups.id.exists'),
        ];
    }
}
