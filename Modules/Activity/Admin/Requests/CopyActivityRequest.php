<?php

declare(strict_types=1);

namespace Modules\Activity\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class CopyActivityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'title' => 'sometimes|string|max:255',
            'copy_schedules' => 'sometimes|boolean',
            'copy_tickets' => 'sometimes|boolean',
            'copy_groups' => 'sometimes|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'title.string' => T('Activity::validation.copy_activity.title.string'),
            'title.max' => T('Activity::validation.copy_activity.title.max'),
            'copy_schedules.boolean' => T('Activity::validation.copy_activity.copy_schedules.boolean'),
            'copy_tickets.boolean' => T('Activity::validation.copy_activity.copy_tickets.boolean'),
            'copy_groups.boolean' => T('Activity::validation.copy_activity.copy_groups.boolean'),
        ];
    }
} 