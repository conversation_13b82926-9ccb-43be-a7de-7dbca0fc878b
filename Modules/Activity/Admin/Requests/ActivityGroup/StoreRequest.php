<?php

declare(strict_types=1);

namespace Modules\Activity\Admin\Requests\ActivityGroup;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;


/**
 * 活动组创建请求验证
 */
class StoreRequest extends FormRequest
{
    /**
     * 判断用户是否有权限进行此请求
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'code' => $this->id
                ? ['required', 'string', 'max:20', Rule::unique('activity_groups')->ignore($this->id)]
                : ['required', 'string', 'max:20', 'unique:activity_groups,code'],
            'name' => ['required', 'string', 'max:50'],
            'max_members' => ['required', 'integer', 'min:0'],
            'can_register' => ['required', 'integer', 'in:0,1'],
            'start_time' => ['date'],
            'end_time' => ['date', 'after:start_time'],
        ];
    }

    /**
     * 获取验证错误的自定义属性
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'code' => trans('Activity::activity_group.code'),
            'name' => trans('Activity::activity_group.name'),
            'max_members' => T('Activity::activity_group.max_members'),
            'can_register' => T('Activity::activity_group.can_register'),
            'start_time' => T('Activity::activity_group.start_time'),
            'end_time' => T('Activity::activity_group.end_time'),
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'code.required' => T('Activity::validation.activity_group.code.required'),
            'code.string' => T('Activity::validation.activity_group.code.string'),
            'code.max' => T('Activity::validation.activity_group.code.max'),
            'code.unique' => T('Activity::validation.activity_group.code.unique'),
            'name.required' => T('Activity::validation.activity_group.name.required'),
            'name.max' => T('Activity::validation.activity_group.name.max'),
            'max_members.required' => T('Activity::validation.activity_group.max_members.required'),
            'max_members.integer' => T('Activity::validation.activity_group.max_members.integer'),
            'max_members.min' => T('Activity::validation.activity_group.max_members.min'),
            'can_register.required' => T('Activity::validation.activity_group.can_register.required'),
            'can_register.integer' => T('Activity::validation.activity_group.can_register.integer'),
            'can_register.in' => T('Activity::validation.activity_group.can_register.in'),
            'start_time.required' => T('Activity::validation.activity_group.start_time.required'),
            'start_time.date' => T('Activity::validation.activity_group.start_time.date'),
            'end_time.required' => T('Activity::validation.activity_group.end_time.required'),
            'end_time.date' => T('Activity::validation.activity_group.end_time.date'),
            'end_time.after' => T('Activity::validation.activity_group.end_time.after'),
        ];
    }
}
