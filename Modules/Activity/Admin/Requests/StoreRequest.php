<?php

declare(strict_types=1);

namespace Modules\Activity\Admin\Requests;

use Bingo\Support\Facades\T;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:100'],
            'category' => ['required', 'string', 'max:50'],
            'localized_id' => ['required', 'integer'],
            'organizer_last_name' => ['nullable', 'string', 'max:50'],
            'organizer_first_name' => ['nullable', 'string', 'max:50'],
            'organizer_email' => ['nullable', 'string', 'email', 'max:100'],

            // 活动地点
            'type' => ['required', 'string', Rule::in(['online', 'offline', 'hybrid'])],
            'location' => ['nullable', 'string', 'max:255'],
            'address' => ['nullable', 'string', 'max:255'],
            'online_platform' => ['nullable', 'string', 'max:20'],
            'online_platform_url' => ['nullable', 'string', 'url', 'max:255'],

            // 活动时间
            'publish_time' => ['required', 'date'],
            'schedule' => ['required', 'array'],
            'schedule.repeat_type' => ['required', 'string', Rule::in(['once', 'daily', 'weekly', 'monthly'])],
            // 'schedule.repeat_frequency' => ['required_unless:schedule.repeat_type,once', 'nullable', 'integer', 'min:1'],
            'schedule.start_date' => ['required_if:schedule.repeat_type,once', 'nullable', 'date'],
            'schedule.end_date' => ['required_if:schedule.repeat_type,once', 'nullable', 'date'],

            'schedule.break_periods' => ['nullable', 'array'],
            'schedule.break_periods.*.start_date' => ['nullable', 'date'],
            'schedule.break_periods.*.end_date' => ['nullable', 'date', 'after:schedule.break_periods.*.start_date'],

            'schedule.time_slots' => ['required_if:schedule.repeat_type,daily,weekly,monthly', 'array'],
            'schedule.time_slots.*.start_time' => ['required_if:schedule.repeat_type,daily,weekly,monthly', 'date_format:H:i'],
            'schedule.time_slots.*.end_time' => ['required_if:schedule.repeat_type,daily,weekly,monthly', 'date_format:H:i', 'after:schedule.time_slots.*.start_time'],

            'schedule.time_slots.*.day_of_week' => [
                'required_if:schedule.repeat_type,weekly',
                'nullable',
                'integer',
                Rule::in([0, 1, 2, 3, 4, 5, 6]),
            ],
            'schedule.time_slots.*.day_of_month' => [
                'required_if:schedule.repeat_type,monthly',
                'nullable',
                'integer',
                'min:1',
                'max:31',
            ],
    

            // 活动报名设置
            'registration_deadline' => ['nullable', 'date'],
            'registration_limit_type' => ['required', 'integer', Rule::in([0, 1])],
            'registration_limit' => ['required_if:registration_limit_type,1', 'integer', 'min:0'],
            'target_participants' => ['nullable', 'integer', 'min:0'],
            'registration_type' => ['required', 'integer', Rule::in([0, 1])],
            'public_limit' => ['required', 'integer', 'min:0'],
            'hidden_limit' => ['required', 'integer', 'min:0'],
            'reserved_limit' => ['required', 'integer', 'min:0'],
            'is_fee_required' => ['required', 'boolean'],
            'invoice_prefix' => ['nullable', 'string', 'max:20'],
            'target_income' => ['nullable', 'numeric', 'min:0', 'max:9999999.99'],
            'currency' => ['nullable', 'string', 'max:10'],
            'registration_method' => ['nullable', Rule::in(['auto_close', 'waiting_list'])],
            'registration_method_rules' => ['nullable', 'array'],


            // 票务设置
            'ticket_delivery_method' => ['nullable', Rule::in(['electronic', 'physical'])],

            // 活动组
            'groups' => ['nullable', 'array'],
            'groups.*.id' => ['nullable', 'integer'],
            'groups.*.name' => ['nullable', 'string', 'max:100'],
            'groups.*.start_time' => ['nullable', 'date'],
            'groups.*.end_time' => ['nullable', 'date', 'after:groups.*.start_time'],

            //票务
            'ticket' => ['required_if:is_fee_required,1', 'array'],
            'ticket.*.id' => ['nullable', 'integer'],
            'ticket.*.name' => ['nullable', 'string', 'max:100'],
            'ticket.*.code' => ['nullable', 'string', 'max:100'],
            'ticket.*.is_external_sale' => ['nullable', 'boolean'],
            'ticket.*.is_fee_required' => ['nullable', 'boolean'],
            'ticket.*.groups' => ['nullable', 'array'],
            'ticket.*.quota' => ['required_if:is_fee_required,1', 'integer'],
            'ticket.*.subtypes' => ['required_if:is_fee_required,1', 'array'],
            'ticket.*.subtypes.*.name' => ['nullable', 'string'],
            'ticket.*.subtypes.*.price' => ['nullable'],
            'ticket.*.subtypes.*.start_date' => ['nullable', 'date'],
            'ticket.*.subtypes.*.end_date' => ['nullable', 'date'],

            'rule_id' => ['nullable', 'integer'],
        ];
    }

    /**
     * 只在 schedule.repeat_type=once 时校验 publish_time 必须 before:schedule.start_date
     */
    public function withValidator($validator)
    {
        // $validator->after(function ($validator) {
        //     $data = $this->all();
        //     if (($data['schedule']['repeat_type'] ?? null) === 'once') {
        //         $publishTime = isset($data['publish_time']) ? strtotime($data['publish_time']) : null;
        //         $startDate = isset($data['schedule']['start_date']) ? strtotime($data['schedule']['start_date']) : null;
        //         if ($publishTime !== null && $startDate !== null && $publishTime >= $startDate) {
        //             $validator->errors()->add('publish_time', T('Activity::validation.activity.publish_time.before'));
        //         }
        //     }
        // });
    }

    public function messages(): array
    {
        return [
            // 基本信息
            'title.required' => T('Activity::validation.activity.title.required'),
            'title.string' => T('Activity::validation.activity.title.string'),
            'title.max' => T('Activity::validation.activity.title.max'),
            'category.required' => T('Activity::validation.activity.category.required'),
            'category.string' => T('Activity::validation.activity.category.string'),
            'category.max' => T('Activity::validation.activity.category.max'),
            'localized_id.required' => T('Activity::validation.activity.localized_id.required'),
            'localized_id.integer' => T('Activity::validation.activity.localized_id.integer'),
            
            // 组织者信息
            'organizer_last_name.string' => T('Activity::validation.activity.organizer_last_name.string'),
            'organizer_last_name.max' => T('Activity::validation.activity.organizer_last_name.max'),
            'organizer_first_name.string' => T('Activity::validation.activity.organizer_first_name.string'),
            'organizer_first_name.max' => T('Activity::validation.activity.organizer_first_name.max'),
            'organizer_email.string' => T('Activity::validation.activity.organizer_email.string'),
            'organizer_email.email' => T('Activity::validation.activity.organizer_email.email'),
            'organizer_email.max' => T('Activity::validation.activity.organizer_email.max'),
            
            // 活动地点
            'type.required' => T('Activity::validation.activity.type.required'),
            'type.string' => T('Activity::validation.activity.type.string'),
            'type.in' => T('Activity::validation.activity.type.in'),
            'location.string' => T('Activity::validation.activity.location.string'),
            'location.max' => T('Activity::validation.activity.location.max'),
            'address.string' => T('Activity::validation.activity.address.string'),
            'address.max' => T('Activity::validation.activity.address.max'),
            'online_platform.string' => T('Activity::validation.activity.online_platform.string'),
            'online_platform.max' => T('Activity::validation.activity.online_platform.max'),
            'online_platform_url.string' => T('Activity::validation.activity.online_platform_url.string'),
            'online_platform_url.url' => T('Activity::validation.activity.online_platform_url.url'),
            'online_platform_url.max' => T('Activity::validation.activity.online_platform_url.max'),
            
            // 活动时间
            'publish_time.required' => T('Activity::validation.activity.publish_time.required'),
            'publish_time.date' => T('Activity::validation.activity.publish_time.date'),
            'schedule.required' => T('Activity::validation.activity.schedule.required'),
            'schedule.array' => T('Activity::validation.activity.schedule.array'),
            'schedule.repeat_type.required' => T('Activity::validation.activity.schedule.repeat_type.required'),
            'schedule.repeat_type.string' => T('Activity::validation.activity.schedule.repeat_type.string'),
            'schedule.repeat_type.in' => T('Activity::validation.activity.schedule.repeat_type.in'),
            'schedule.start_date.required_if' => T('Activity::validation.activity.schedule.start_date.required_if'),
            'schedule.start_date.date' => T('Activity::validation.activity.schedule.start_date.date'),
            'schedule.end_date.required_if' => T('Activity::validation.activity.schedule.end_date.required_if'),
            'schedule.end_date.date' => T('Activity::validation.activity.schedule.end_date.date'),
            
            // 报名设置
            'registration_deadline.date' => T('Activity::validation.activity.registration_deadline.date'),
            'registration_limit_type.required' => T('Activity::validation.activity.registration_limit_type.required'),
            'registration_limit_type.integer' => T('Activity::validation.activity.registration_limit_type.integer'),
            'registration_limit_type.in' => T('Activity::validation.activity.registration_limit_type.in'),
            'registration_limit.required_if' => T('Activity::validation.activity.registration_limit.required_if'),
            'registration_limit.integer' => T('Activity::validation.activity.registration_limit.integer'),
            'registration_limit.min' => T('Activity::validation.activity.registration_limit.min'),
            'target_participants.integer' => T('Activity::validation.activity.target_participants.integer'),
            'target_participants.min' => T('Activity::validation.activity.target_participants.min'),
            'registration_type.required' => T('Activity::validation.activity.registration_type.required'),
            'registration_type.integer' => T('Activity::validation.activity.registration_type.integer'),
            'registration_type.in' => T('Activity::validation.activity.registration_type.in'),
            'public_limit.required' => T('Activity::validation.activity.public_limit.required'),
            'public_limit.integer' => T('Activity::validation.activity.public_limit.integer'),
            'public_limit.min' => T('Activity::validation.activity.public_limit.min'),
            'hidden_limit.required' => T('Activity::validation.activity.hidden_limit.required'),
            'hidden_limit.integer' => T('Activity::validation.activity.hidden_limit.integer'),
            'hidden_limit.min' => T('Activity::validation.activity.hidden_limit.min'),
            'reserved_limit.required' => T('Activity::validation.activity.reserved_limit.required'),
            'reserved_limit.integer' => T('Activity::validation.activity.reserved_limit.integer'),
            'reserved_limit.min' => T('Activity::validation.activity.reserved_limit.min'),
            'is_fee_required.required' => T('Activity::validation.activity.is_fee_required.required'),
            'is_fee_required.boolean' => T('Activity::validation.activity.is_fee_required.boolean'),
            
            // 票务相关
            'ticket.required_if' => T('Activity::validation.activity.ticket.required_if'),
            'ticket.array' => T('Activity::validation.activity.ticket.array'),
            'ticket.*.name.string' => T('Activity::validation.ticket.name.string'),
            'ticket.*.name.max' => T('Activity::validation.ticket.name.max'),
            'ticket.*.code.string' => T('Activity::validation.ticket.code.string'),
            'ticket.*.code.max' => T('Activity::validation.ticket.code.max'),
            'ticket.*.quota.required_if' => T('Activity::validation.ticket.quota.required_if'),
            'ticket.*.quota.integer' => T('Activity::validation.ticket.quota.integer'),
            'ticket.*.subtypes.required_if' => T('Activity::validation.ticket.subtypes.required_if'),
            'ticket.*.subtypes.array' => T('Activity::validation.ticket.subtypes.array'),
            'ticket.*.subtypes.*.name.string' => T('Activity::validation.ticket.subtype.name.string'),
            'ticket.*.subtypes.*.start_date.date' => T('Activity::validation.ticket.subtype.start_date.date'),
            'ticket.*.subtypes.*.end_date.date' => T('Activity::validation.ticket.subtype.end_date.date'),
            
            // 活动组
            'groups.array' => T('Activity::validation.activity.groups.array'),
            'groups.*.name.string' => T('Activity::validation.activity.group.name.string'),
            'groups.*.name.max' => T('Activity::validation.activity.group.name.max'),
            'groups.*.start_time.date' => T('Activity::validation.activity.group.start_time.date'),
            'groups.*.end_time.date' => T('Activity::validation.activity.group.end_time.date'),
            'groups.*.end_time.after' => T('Activity::validation.activity.group.end_time.after'),
            
            // 规则
            'rule_id.integer' => T('Activity::validation.activity.rule_id.integer'),
        ];
    }
}
