<?php

declare(strict_types=1);

namespace Modules\Activity\Admin\Requests\ActivityInvitation;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 活动邀请保存请求验证
 */
class StoreRequest extends FormRequest
{
    /**
     * 确定用户是否有权提出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'activity_id' => 'required|integer|exists:activity,id',
            'title' => 'required|string|max:64',
            'description' => 'nullable|string',
            'is_default' => 'nullable|integer|in:0,1',
            'is_group' => 'nullable|integer|in:0,1',
            'group_id' => 'nullable|integer|exists:activity_groups,id',
            'status' => 'nullable|integer|in:0,1',
            
            // 成员数据验证规则
            'members' => 'nullable|array',
            'members.*.id' => 'nullable|integer',
            'members.*.name' => 'required|string|max:64',
            'members.*.phone' => 'nullable|string|max:30',
            'members.*.email' => 'nullable|email|max:100',
            'members.*.company' => 'nullable|string',
            'members.*.position' => 'nullable|string',
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     */
    public function messages(): array
    {
        return [
            'activity_id.required' => T('Activity::invitation.validation.activity_id.required'),
            'activity_id.integer' => T('Activity::invitation.validation.activity_id.integer'),
            'activity_id.exists' => T('Activity::invitation.validation.activity_id.exists'),
            'title.required' => T('Activity::invitation.validation.title.required'),
            'title.string' => T('Activity::invitation.validation.title.string'),
            'title.max' => T('Activity::invitation.validation.title.max'),
            'description.string' => T('Activity::invitation.validation.description.string'),
            'is_default.integer' => T('Activity::invitation.validation.is_default.integer'),
            'is_default.in' => T('Activity::invitation.validation.is_default.in'),
            'is_group.integer' => T('Activity::invitation.validation.is_group.integer'),
            'is_group.in' => T('Activity::invitation.validation.is_group.in'),
            'group_id.integer' => T('Activity::invitation.validation.group_id.integer'),
            'group_id.exists' => T('Activity::invitation.validation.group_id.exists'),
            'status.integer' => T('Activity::invitation.validation.status.integer'),
            'status.in' => T('Activity::invitation.validation.status.in'),
            
            'members.array' => T('Activity::invitation.validation.members.array'),
            'members.*.id.integer' => T('Activity::invitation.validation.member.id.integer'),
            'members.*.name.required' => T('Activity::invitation.validation.member.name.required'),
            'members.*.name.string' => T('Activity::invitation.validation.member.name.string'),
            'members.*.name.max' => T('Activity::invitation.validation.member.name.max'),
            'members.*.phone.string' => T('Activity::invitation.validation.member.phone.string'),
            'members.*.phone.max' => T('Activity::invitation.validation.member.phone.max'),
            'members.*.email.email' => T('Activity::invitation.validation.member.email.email'),
            'members.*.email.max' => T('Activity::invitation.validation.member.email.max'),
            'members.*.company.string' => T('Activity::invitation.validation.member.company.string'),
            'members.*.position.string' => T('Activity::invitation.validation.member.position.string'),
        ];
    }
} 