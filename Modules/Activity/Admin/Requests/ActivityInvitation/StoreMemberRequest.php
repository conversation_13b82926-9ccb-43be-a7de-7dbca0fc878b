<?php

declare(strict_types=1);

namespace Modules\Activity\Admin\Requests\ActivityInvitation;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 活动邀请成员保存请求验证
 */
class StoreMemberRequest extends FormRequest
{
    /**
     * 确定用户是否有权提出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'activity_id' => 'required|integer|exists:activity,id',
            'name' => 'required|string|max:64',
            'phone' => 'nullable|string|max:30',
            'email' => 'nullable|email|max:100',
            'company' => 'nullable|string',
            'position' => 'nullable|string',
            'status' => 'nullable|integer|in:0,1',
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     */
    public function messages(): array
    {
        return [
            'activity_id.required' => T('Activity::invitation.validation.activity_id.required'),
            'activity_id.integer' => T('Activity::invitation.validation.activity_id.integer'),
            'activity_id.exists' => T('Activity::invitation.validation.activity_id.exists'),
            'name.required' => T('Activity::invitation.validation.member.name.required'),
            'name.string' => T('Activity::invitation.validation.member.name.string'),
            'name.max' => T('Activity::invitation.validation.member.name.max'),
            'phone.string' => T('Activity::invitation.validation.member.phone.string'),
            'phone.max' => T('Activity::invitation.validation.member.phone.max'),
            'email.email' => T('Activity::invitation.validation.member.email.email'),
            'email.max' => T('Activity::invitation.validation.member.email.max'),
            'company.string' => T('Activity::invitation.validation.member.company.string'),
            'position.string' => T('Activity::invitation.validation.member.position.string'),
            'status.integer' => T('Activity::invitation.validation.status.integer'),
            'status.in' => T('Activity::invitation.validation.status.in'),
        ];
    }
} 