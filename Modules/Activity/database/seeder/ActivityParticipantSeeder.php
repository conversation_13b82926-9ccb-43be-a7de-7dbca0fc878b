<?php

namespace Modules\Activity\Database\Seeder;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

return new class () extends Seeder {
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 检查是否已有数据，避免重复导入
        if (DB::table('activity_participants')->count() > 0) {
            return;
        }

        $now = time();
        $participants = [];
        // php artisan bingo:seed Activity
        
        for ($i = 1; $i <= 10; $i++) {
            $participants[] = [
                'activity_id' => 55,
                'user_id' => $i,
                'registration_time' => $now - rand(0, 86400 * 30),
                'status' => collect(['registered', 'confirmed', 'cancelled', 'attended', 'waiting'])->random(),
                'check_in_time' => rand(0, 1) ? ($now - rand(0, 86400 * 30)) : null,
                'feedback' => rand(0, 1) ? '参与者反馈内容示例 ' . $i : null,
                'creator_id' => rand(1, 3),
                'created_at' => $now - rand(0, 86400 * 30),
                'updated_at' => $now,
                'deleted_at' => 0,
            ];
        }
        
        DB::table('activity_participants')->insert($participants);
    }
}; 