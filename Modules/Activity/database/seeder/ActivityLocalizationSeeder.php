<?php

namespace Modules\Activity\Database\Seeder;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

return new class () extends Seeder {
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 检查是否已有数据，避免重复导入
        if (DB::table('activity_localizations')->count() > 0) {
            return;
        }

        $now = time();
        $localizations = [
            [
                'locale' => 'zh_CN',
                'name' => '中国大陆',
                'timezone' => 'Asia/Shanghai',
                'currency_code' => 'CNY',
                'currency_symbol' => '¥',
                'creator_id' => 1,
                'created_at' => $now,
                'updated_at' => $now,
                'deleted_at' => 0,
            ],
            [
                'locale' => 'zh_HK',
                'name' => '香港',
                'timezone' => 'Asia/Hong_Kong',
                'currency_code' => 'HKD',
                'currency_symbol' => 'HK$',
                'creator_id' => 1,
                'created_at' => $now,
                'updated_at' => $now,
                'deleted_at' => 0,
            ],
            [
                'locale' => 'en',
                'name' => '海外',
                'timezone' => 'America/New_York',
                'currency_code' => 'USD',
                'currency_symbol' => '$',
                'creator_id' => 1,
                'created_at' => $now,
                'updated_at' => $now,
                'deleted_at' => 0,
            ]
        ];
        
        DB::table('activity_localizations')->insert($localizations);
        $this->command->info('ActivityLocalizations seeded successfully!');
    }
};