<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('activity_participants', function (Blueprint $table) {
            $table->enum('notification_type', ['', 'pending', 'notified', 'expired'])
                ->default('')
                ->after('feedback')
                ->comment('通知状态（仅在status=waiting时有效）：空-未设置, pending-待通知, notified-已通知, expired-过期/放弃');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('activity_participants', function (Blueprint $table) {
            $table->dropColumn('notification_type');
        });
    }
}; 