<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_forms', function (Blueprint $table) {
            $table->comment('活动表单模板表');
            $table->id();
            $table->string('title', 100)->comment('表单标题');
            $table->text('description')->nullable()->comment('表单描述');
            $table->string('scene', 50)->comment('使用场景');
            $table->json('fields')->nullable()->comment('字段配置');
            $table->json('submit_settings')->nullable()->comment('表单提交设置');
            $table->tinyInteger('status')->default(1)->comment('状态 1启用 0禁用');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('updater_id')->default(0)->comment('更新人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_forms');
    }
}; 