<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_ticket_type_relations', function (Blueprint $table) {
            $table->comment('活动票种关联表');
            $table->id();
            $table->unsignedBigInteger('activity_id')->index()->comment('活动ID');
            $table->unsignedInteger('ticket_type_id')->index()->comment('票种ID');
            $table->unsignedInteger('quota')->default(0)->comment('数量限制');
            $table->unsignedInteger('remaining_quota')->nullable()->default(0)->comment('剩余数量');
            $table->json('subtypes')->nullable()->comment('细分类型JSON');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            // 创建联合唯一索引
            $table->unique(['activity_id', 'ticket_type_id'], 'uk_activity_ticket_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_ticket_type_relations');
    }
}; 