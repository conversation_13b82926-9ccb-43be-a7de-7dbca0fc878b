<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_notifications', function (Blueprint $table) {
            $table->id();
            $table->comment('活动通知表');
            $table->unsignedInteger('user_id')->comment('接收用户ID');
            $table->unsignedInteger('activity_id')->nullable()->comment('相关活动ID');
            $table->string('title', 100)->comment('通知标题');
            $table->text('content')->comment('通知内容');
            $table->enum('type', ['system', 'activity', 'reminder'])->comment('通知类型');
            $table->boolean('is_read')->default(false)->comment('是否已读：1=是，0=否');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            $table->index('user_id', 'idx_user');
            $table->index('activity_id', 'idx_activity');
            $table->index('type', 'idx_type');
            $table->index('is_read', 'idx_read');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_notifications');
    }
};
