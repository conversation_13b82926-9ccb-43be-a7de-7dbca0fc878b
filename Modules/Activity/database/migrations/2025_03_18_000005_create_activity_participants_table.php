<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_participants', function (Blueprint $table) {
            $table->id();
            $table->comment('活动参与者表');
            $table->unsignedInteger('activity_id')->comment('活动ID');
            $table->unsignedInteger('user_id')->comment('用户ID');
            $table->unsignedInteger('registration_time')->comment('报名时间');
            $table->enum('status', ['registered', 'confirmed', 'cancelled', 'attended', 'waiting'])->default('registered')->comment('参与状态：registered-已报名, confirmed-已确认, cancelled-已取消, attended-已出席, waiting-等待名单');
            $table->unsignedInteger('check_in_time')->nullable()->comment('签到时间');
            $table->text('feedback')->nullable()->comment('参与者反馈');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            $table->unique(['activity_id', 'user_id'], 'uk_activity_user');
            $table->index('activity_id', 'idx_activity');
            $table->index('user_id', 'idx_user');
            $table->index('status', 'idx_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_participants');
    }
}; 