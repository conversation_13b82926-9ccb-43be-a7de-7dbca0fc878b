<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('activity_reminders', function (Blueprint $table) {
            $table->comment('活动提醒');
            $table->id();
            $table->string('name')->comment('提醒名称');
            $table->json('type')->comment('提醒方式（email/sms）');
            $table->json('recipients')->comment('收件人信息');
            $table->unsignedBigInteger('activity_id')->nullable()->comment('关联活动ID');
            $table->json('settings')->comment('提醒项设置');
            $table->tinyInteger('status')->default(1)->comment('启用状态：0-未启用 1-启用');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('activity_reminders');
    }
}; 