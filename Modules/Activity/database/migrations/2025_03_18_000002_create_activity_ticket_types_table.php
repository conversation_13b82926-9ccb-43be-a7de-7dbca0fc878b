<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_ticket_types', function (Blueprint $table) {
            $table->id();
            $table->comment('活动票种表');
            $table->string('name', 100)->comment('票种名称');
            $table->string('code', 20)->comment('票种代码');
            $table->string('description', 255)->nullable()->comment('票种描述');
            $table->boolean('is_external_sale')->default(false)->comment('是否对外发售：1=是，0=否');
            $table->boolean('is_fee_required')->default(false)->comment('是否收费：1=是，0=否');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            $table->index('code', 'idx_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_ticket_types');
    }
}; 