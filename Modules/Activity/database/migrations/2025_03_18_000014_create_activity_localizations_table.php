<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_localizations', function (Blueprint $table) {
            $table->id();
            $table->comment('活动本地化选项表');
            $table->string('locale', 10)->unique()->comment('区域代码，如：zh_HK');
            $table->string('name')->comment('区域名称，如：香港');
            $table->string('timezone')->comment('时区，如：Asia/Shanghai');
            $table->string('currency_code', 3)->comment('货币代码，如：CNY');
            $table->string('currency_symbol', 10)->comment('货币符号，如：¥');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_localizations');
    }
}; 