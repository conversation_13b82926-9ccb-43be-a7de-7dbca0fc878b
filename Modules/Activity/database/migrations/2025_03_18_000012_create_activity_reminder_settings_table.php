<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_reminder_settings', function (Blueprint $table) {
            $table->comment('活动提醒设置表');
            $table->id();
            $table->unsignedInteger('activity_id')->comment('活动ID');
            $table->string('title', 100)->comment('提醒标题');
            $table->json('notification_methods')->comment('通知方式(email,sms)');
            $table->unsignedInteger('reminder_template_id')->nullable()->comment('提醒模板ID');
            $table->boolean('is_enabled')->default(true)->comment('是否启用：1=是，0=否');
            $table->enum('reminder_type', [
                'activity_created',
                'activity_published',
                'registration_open',
                'registration_reminder',
                'activity_start',
                'activity_end',
                'feedback_collection'
            ])->comment('提醒类型');
            $table->unsignedInteger('trigger_time')->nullable()->comment('触发时间');
            $table->json('trigger_condition')->nullable()->comment('触发条件');
            $table->unsignedInteger('last_sent_at')->nullable()->comment('上次发送时间');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            $table->index('activity_id', 'idx_activity');
            $table->index('reminder_type', 'idx_type');
            $table->index('is_enabled', 'idx_status');
            $table->index('trigger_time', 'idx_trigger_time');

            
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_reminder_settings');
    }
}; 