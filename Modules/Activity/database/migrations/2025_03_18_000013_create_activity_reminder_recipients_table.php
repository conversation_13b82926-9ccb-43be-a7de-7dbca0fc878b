<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_reminder_recipients', function (Blueprint $table) {
            $table->comment('活动提醒接收者表');
            
            $table->id();
            $table->unsignedInteger('reminder_setting_id')->comment('提醒设置ID');
            $table->enum('recipient_type', ['user', 'role', 'department', 'group'])->comment('接收者类型');
            $table->string('recipient_value', 100)->comment('接收者值');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            $table->index('reminder_setting_id', 'idx_reminder_setting');
            $table->index(['recipient_type', 'recipient_value'], 'idx_recipient');

            
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_reminder_recipients');
    }
}; 