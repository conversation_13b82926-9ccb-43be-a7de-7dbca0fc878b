<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_email_templates', function (Blueprint $table) {
            $table->comment('活动邮件模板表');
            $table->id();
            $table->string('name', 100)->comment('模板名称');
            $table->string('subject', 200)->comment('邮件主题');
            $table->text('content')->comment('邮件内容(HTML)');
            $table->enum('scene', [
                'registration_reminder',
                'status_collection',
                'feedback_collection',
                'registration_success',
                'registration_notification'
            ])->comment('使用场景registration_reminder-报名提醒 status_collection-状态收集 feedback_collection-反馈收集 registration_success-报名成功 registration_notification-报名通知');
            $table->boolean('is_default')->default(false)->comment('是否默认模板：1=是，0=否');
            $table->boolean('status')->default(true)->comment('状态：1=启用，0=禁用');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            $table->index('scene', 'idx_scene');
            $table->index('status', 'idx_status');

            
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_email_templates');
    }
}; 