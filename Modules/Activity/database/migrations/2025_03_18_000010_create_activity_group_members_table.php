<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_group_members', function (Blueprint $table) {
            $table->comment('活动群组成员表');
            $table->id();
            $table->unsignedInteger('group_id')->index()->comment('群组ID');
            $table->unsignedInteger('user_id')->index()->comment('用户ID');
            $table->unsignedInteger('join_time')->comment('加入时间');
            $table->enum('status', ['active', 'inactive', 'removed'])->default('active')->comment('状态');
            
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            $table->unique(['group_id', 'user_id'], 'uk_group_user');
            $table->index('group_id', 'idx_group');
            $table->index('user_id', 'idx_user');
            $table->index('status', 'idx_status');

            
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_group_members');
    }
}; 