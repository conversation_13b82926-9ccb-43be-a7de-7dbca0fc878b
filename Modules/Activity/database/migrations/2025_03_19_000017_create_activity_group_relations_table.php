<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_group_relations', function (Blueprint $table) {
            $table->comment('活动群组关联表');
            $table->id();
            $table->unsignedBigInteger('activity_id')->index()->comment('活动ID');
            $table->unsignedBigInteger('group_id')->index()->comment('群组ID');
            $table->date('register_start_time')->index()->comment('报名开始时间');
            $table->date('register_end_time')->index()->comment('报名结束时间');

            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            // 创建联合唯一索引
            $table->unique(['activity_id', 'group_id'], 'uk_activity_group');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_group_relations');
    }
}; 