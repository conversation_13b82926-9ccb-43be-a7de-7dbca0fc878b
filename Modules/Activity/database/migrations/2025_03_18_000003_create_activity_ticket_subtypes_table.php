<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_ticket_subtypes', function (Blueprint $table) {
            $table->id();
            $table->comment('活动票种细分类型表');
            $table->unsignedInteger('ticket_type_id')->comment('票种ID');
            $table->string('name', 100)->comment('细分类型名称');
            $table->integer('sort')->default(0)->comment('排序');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            $table->index('ticket_type_id', 'idx_ticket_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_ticket_subtypes');
    }
}; 