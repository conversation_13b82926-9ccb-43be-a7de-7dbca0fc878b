<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_schedules', function (Blueprint $table) {
            $table->comment("活动时间规则表");
            $table->id();
            $table->unsignedBigInteger('activity_id')->index()->comment('活动ID');
            $table->enum('repeat_type', ['once', 'daily', 'weekly', 'monthly'])->default('once')->comment('重复类型once-一次,daily-每天,weekly-每周,monthly-每月');
            $table->integer('repeat_frequency')->default(1)->comment('重复频率');
            $table->date('start_date')->index()->comment('开始日期');
            $table->date('end_date')->index()->comment('结束日期');

            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_schedules');
    }
};
