<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('activity_rules', function (Blueprint $table) {
            $table->comment('活动规则'); 
            $table->id();
            $table->string('name')->comment('规则名称');
            $table->text('description')->nullable()->comment('规则描述');
            $table->string('scene')->comment('使用场景');
            $table->tinyInteger('status')->default(1)->comment('状态 0禁用 1启用');
            $table->json('config')->nullable()->comment('规则配置');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_rules');
    }
}; 