<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_schedules_time_slots', function (Blueprint $table) {
            $table->comment('活动时间表');
            $table->id();
            $table->unsignedBigInteger('schedule_id')->index()->comment('时间表ID');
            $table->time('start_time')->comment('开始时间');
            $table->time('end_time')->comment('结束时间');
            $table->tinyInteger('day_of_week')->nullable()->comment('周几(1-7)');
            $table->json('day_of_month')->nullable()->comment('每月具体日期或第几周周几');

            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_schedules_time_slots');
    }
};