<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_schedules_break_periods', function (Blueprint $table) {
            $table->comment('活动休息时间段表');
            $table->id();
            $table->unsignedBigInteger('schedule_id')->index()->comment('时间表ID');
            $table->date('start_date')->comment('休息开始日期');
            $table->date('end_date')->comment('休息结束日期');
            
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_break_periods');
    }
};