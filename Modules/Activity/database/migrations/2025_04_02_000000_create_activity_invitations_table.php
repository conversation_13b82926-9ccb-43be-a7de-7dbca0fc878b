<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_invitations', function (Blueprint $table) {
            $table->id();
            $table->comment('活动邀请表');

            // 关联信息
            $table->unsignedInteger('activity_id')->index()->comment('活动ID');

            // 邀请信息
            $table->string('title', 64)->comment('名称');
            $table->string('description')->nullable()->comment('描述');
            $table->integer('is_default')->index()->default(0)->nullable()->comment('是否为默认列表 0-否 1-是');

            $table->integer('is_group')->default(0)->nullable()->comment('是否分配群组 0-否 1-是');
            $table->unsignedInteger('group_id')->nullable()->comment('群组ID');

            // 邀请状态
            $table->integer('status')->index()->default(1)->nullable()->comment('状态 0-禁用 1-启用');

            // 时间戳
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
        });

        Schema::create('activity_invitation_members', function (Blueprint $table) {
            $table->id();
            $table->comment('活动邀请成员表');

            // 关联信息
            $table->unsignedInteger('activity_id')->index()->comment('活动ID');
            $table->unsignedInteger('invitation_id')->index()->comment('邀请ID');
            $table->unsignedInteger('user_id')->index()->default(0)->nullable()->comment('成员ID');

            // 邀请信息
            $table->string('name', 64)->comment('姓名');
            $table->string('phone', 30)->nullable()->comment('手机号');
            $table->string('email', 100)->nullable()->comment('邮箱');
            $table->string('company')->nullable()->comment('公司');
            $table->string('position')->nullable()->comment('职位');

            $table->string('code')->index()->nullable()->comment('邀请码');
            
            // 邀请状态
            $table->integer('status')->index()->default(0)->nullable()->comment('状态 0-未回复 1-已报名 2-已拒绝');

            // 时间戳
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
        });

        Schema::create('activity_invitation_members_logs', function (Blueprint $table) {
            $table->id();
            $table->comment('活动邀请成员操作日志表');

            // 关联信息
            $table->unsignedInteger('activity_id')->index()->comment('活动ID');
            $table->unsignedInteger('invitation_id')->index()->comment('邀请ID');
            $table->unsignedInteger('member_id')->index()->comment('成员ID');

            // 操作信息
            $table->string('action', 32)->comment('操作类型');
            $table->string('remark', 255)->nullable()->comment('备注');
            $table->string('ip', 255)->nullable()->comment('操作IP');

            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_invitations');
    }
};
