<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * 创建票务群组关联表
     */
    public function up(): void
    {
        Schema::create('activity_ticket_groups_relations', function (Blueprint $table) {
            $table->id();
            $table->comment('票务群组关联表');
            $table->unsignedBigInteger('ticket_id')->index()->comment('票务ID');
            $table->unsignedBigInteger('group_id')->comment('群组ID');
            
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
        });
    }

    /**
     * 删除票务群组关联表
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_ticket_groups_relations');
    }
};