<?php

declare(strict_types=1);

namespace Modules\Activity\Services;

use Modules\Activity\Models\ActivityReminder;

final class ActivityReminderService
{
    public function create(array $data): ActivityReminder
    {
        return ActivityReminder::create($data);
    }

    public function update(int $id, array $data): ActivityReminder
    {
        $reminder = ActivityReminder::findOrFail($id);
        $reminder->update($data);
        return $reminder;
    }

    public function list(array $filters = [])
    {
        $query = ActivityReminder::query();
        
        // 可根据需要添加筛选条件
        $activity_id = $filters['activity_id'] ?? 0;
        if ($activity_id) {
            $query->where('activity_id', $activity_id);
        }
        $perPage = $filters['per_page'] ?? 20;
        $data = $query->orderByDesc('id')->paginate($perPage);
        return [
            'items' => $data->items(),
            'total' => $data->total(),
        ];
    }

    public function get(int $id): ActivityReminder
    {
        return ActivityReminder::findOrFail($id);
    }

    public function delete(int $id): void
    {
        ActivityReminder::findOrFail($id)->delete();
    }

    public function toggleStatus(int $id, bool $status): ActivityReminder
    {
        $reminder = ActivityReminder::findOrFail($id);
        $reminder->status = $status;
        $reminder->save();
        return $reminder;
    }
} 