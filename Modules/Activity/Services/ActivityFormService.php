<?php

declare(strict_types=1);

namespace Modules\Activity\Services;

use Modules\Activity\Models\ActivityForm;
use Illuminate\Support\Facades\Auth;
use Bingo\Exceptions\BizException;
use Modules\Activity\Enums\ActivityErrorCode;
use Illuminate\Support\Facades\DB;

/**
 * 活动表单管理服务
 */
class ActivityFormService
{
    // 列表
    public function list(array $params)
    {
        $query = ActivityForm::query()
            ->when(!empty($params['title']), function ($q) use ($params) {
                $q->where('title', 'like', '%' . $params['title'] . '%');
            })
            ->when(isset($params['scene']), function ($q) use ($params) {
                $q->where('scene', $params['scene']);
            })
            ->when(isset($params['status']), function ($q) use ($params) {
                $q->where('status', $params['status']);
            });
        $perPage = $params['per_page'] ?? 20;
        return $query->orderByDesc('updated_at')->paginate($perPage);
    }

    // 详情
    public function detail(int $id)
    {
        $template = ActivityForm::find($id);
        if (!$template) {
            BizException::throws(ActivityErrorCode::FORM_TEMPLATE_NOT_FOUND);
        }
        return $template;
    }

    // 新建/编辑
    public function save(array $data, ?int $id = null)
    {
        if ($id) {
            $template = ActivityForm::find($id);
            if (!$template) {
                BizException::throws(ActivityErrorCode::FORM_TEMPLATE_NOT_FOUND);
            }
            $template->fill($data);
            $template->updater_id = Auth::id() ?? 0;
            $template->save();
        } else {
            $data['creator_id'] = Auth::id() ?? 0;
            $template = ActivityForm::create($data);
        }
        return $template;
    }

    // 删除
    public function delete(int|array $id): bool
    {
        $ids = is_array($id) ? $id : [$id];
        try {
            $count = ActivityForm::whereIn('id', $ids)->count();
            if ($count !== count($ids)) {
                BizException::throws(ActivityErrorCode::FORM_TEMPLATE_NOT_FOUND);
            }
            ActivityForm::whereIn('id', $ids)->delete();
            return true;
        } catch (\Exception $e) {
            BizException::throws(ActivityErrorCode::FORM_TEMPLATE_DELETE_FAILED, $e->getMessage());
        }
    }

    // 批量复制
    public function batchCopy(array $ids): bool
    {
        $templates = ActivityForm::whereIn('id', $ids)->get();
        foreach ($templates as $tpl) {
            $new = $tpl->replicate();
            $new->title = $tpl->title . ' - 复制';
            $new->creator_id = Auth::id() ?? 0;
            $new->save();
        }
        return true;
    }

    // 移动
    public function move(array $ids, $targetGroupId): bool
    {
        ActivityForm::whereIn('id', $ids)->update(['group_id' => $targetGroupId]);
        return true;
    }

    // 状态切换
    public function updateStatus(int $id, int $status): bool
    {
        try {
            $template = ActivityForm::find($id);
            if (!$template) {
                BizException::throws(ActivityErrorCode::FORM_TEMPLATE_NOT_FOUND);
            }
            $template->status = $status;
            $template->updater_id = Auth::id() ?? 0;
            $template->save();
            return true;
        } catch (\Exception $e) {
            BizException::throws(ActivityErrorCode::FORM_TEMPLATE_UPDATE_FAILED, $e->getMessage());
        }
    }
} 