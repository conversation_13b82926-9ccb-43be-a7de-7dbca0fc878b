<?php

namespace Modules\Activity\Services;

use Carbon\Carbon;
use Modules\Activity\Models\ActivitySchedule;
use Modules\Activity\Models\Activity;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\DB;

/**
 * 活动时间规则服务
 * 
 * 负责处理活动时间规则的计算和管理
 */
class ActivityScheduleService
{
    /**
     * 保存时间规则（创建或更新）
     * @param int $activityId 活动ID
     * @param array $data 时间规则数据
     * @param int $creatorId 创建者ID
     * @return ActivitySchedule
     */
    public function saveSchedule(int $activityId, array $data, int $creatorId = 0): ActivitySchedule
    {
        try {
            DB::beginTransaction();

            $breakPeriods = $data['break_periods'] ?? [];
            $timeSlots = $data['time_slots'] ?? [];
            $repeat_type = $data['repeat_type'] ?? 'once';
            if ($repeat_type == 'once') {
                $start_date = $data['start_date'];
                $end_date = $data['end_date'];
                $data['start_date'] = Carbon::parse($start_date)->format('Y-m-d');
                $data['end_date'] = Carbon::parse($end_date)->format('Y-m-d');
                $timeSlots['start_time'] = Carbon::parse($start_date)->format('H:i');
                $timeSlots['end_time'] = Carbon::parse($end_date)->format('H:i');
                $timeSlots = [$timeSlots];
            }


            // 查找或创建主时间规则
            $schedule = ActivitySchedule::firstOrNew(['activity_id' => $activityId]);

            // 转换时间格式
            $startDate = $this->formatCarbonToDateString($data['start_date']);
            $endDate = $this->formatCarbonToDateString($data['end_date']);

            // 确保所有字段都被正确格式化和转换
            $scheduleData = [
                'repeat_type' => (string)($data['repeat_type'] ?? 'once'),
                'repeat_frequency' => (int)($data['repeat_frequency'] ?? 1),
                'start_date' => $startDate,
                'end_date' => $endDate,
                'creator_id' => (int)$creatorId,
            ];
            $schedule->fill($scheduleData)->save();

            // 处理休息时段
            if (!empty($breakPeriods)) {
                $schedule->breakPeriods()->delete();
                $this->addBreakPeriods($schedule, $breakPeriods);
            }

            // 处理时间段
            if (!empty($timeSlots)) {
                $schedule->timeSlots()->delete();
                
                $this->addTimeSlots($schedule, $timeSlots);
            }

            DB::commit();
            return $schedule;
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('保存活动时间规则失败', [
                'activity_id' => $activityId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }


    /**
     * 格式化Carbon日期为日期字符串 (Y-m-d)
     * @param mixed $date Carbon日期或字符串日期
     * @return string 格式化后的日期字符串
     */
    protected function formatCarbonToDateString($date): string
    {
        return $date instanceof Carbon
            ? $date->format('Y-m-d')
            : Carbon::parse($date)->format('Y-m-d');
    }

    /**
     * 格式化Carbon时间为时间字符串 (H:i)
     * @param mixed $time Carbon时间或字符串时间
     * @return string 格式化后的时间字符串
     */
    protected function formatCarbonToTimeString($time): string
    {
        return $time instanceof Carbon
            ? $time->format('H:i')
            : Carbon::parse($time)->format('H:i');
    }

    /**
     * 添加休息时间段
     * @param ActivitySchedule $schedule
     * @param array $breakPeriods 休息时间段数组 [['start_date' => '2025-01-29', 'end_date' => '2025-02-02'], ...]
     */
    public function addBreakPeriods(ActivitySchedule $schedule, array $breakPeriods): void
    {
        foreach ($breakPeriods as $period) {
            $startDate = $this->formatCarbonToDateString($period['start_date']);
            $endDate = $this->formatCarbonToDateString($period['end_date']);

            $schedule->breakPeriods()->create([
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]);
        }
    }

    /**
     * 添加时间表
     * @param ActivitySchedule $schedule
     * @param array $timeSlots 时间表数组 [['start_time' => '10:00', 'end_time' => '12:00'], ...]
     */
    public function addTimeSlots(ActivitySchedule $schedule, array $timeSlots): void
    {

        foreach ($timeSlots as $slot) {
            $startTime = $this->formatCarbonToTimeString($slot['start_time']);
            $endTime = $this->formatCarbonToTimeString($slot['end_time']);
            $schedule->timeSlots()->create([
                'start_time' => $startTime,
                'end_time' => $endTime,
                'day_of_week' => $slot['day_of_week'] ?? null,
                'day_of_month' => $slot['day_of_month'] ?? null
            ]);
        }
    }


    /**
     * 获取指定日期范围内的活动日程
     * 
     * @param Carbon $startDate 开始日期
     * @param Carbon $endDate 结束日期
     * @param array $filters 过滤条件
     * @return \Illuminate\Support\Collection 活动日程集合
     */
    public function getSchedulesInDateRange($start_date, $end_date, array $filters = [])
    {
        $query = ActivitySchedule::with(['activity', 'timeSlots', 'breakPeriods']);

        // 应用活动状态过滤（如果需要的话）
        if (isset($filters['status'])) {
            $query->whereHas('activity', function ($q) use ($filters) {
                $q->where('status', $filters['status']);
            });
        }

        // 应用类别过滤（如果需要的话）
        if (isset($filters['category'])) {
            $query->whereHas('activity', function ($q) use ($filters) {
                $q->where('category', $filters['category']);
            });

            \Log::debug('应用类别过滤', ['category' => $filters['category']]);
        }

        // 查找日期范围内的活动
        $query->where(function ($query) use ($start_date, $end_date) {
            // 1. 开始日期在查询范围内的活动
            $query->whereBetween('start_date', [$start_date, $end_date]);

            // 2. 结束日期在查询范围内的活动
            $query->orWhereBetween('end_date', [$start_date, $end_date]);

            // 3. 跨越整个查询范围的活动（开始日期早于查询开始，结束日期晚于查询结束）
            $query->orWhere(function ($q) use ($start_date, $end_date) {
                $q->where('start_date', '<=', $start_date)
                    ->where('end_date', '>=', $end_date);
            });
        });
        $schedules = $query->get();
        
        return $schedules;
    }

    /**
     * 计算活动时间
     * 
     * @param ActivitySchedule $schedule 活动日程
     * @param Carbon $startDate 开始日期
     * @param Carbon $endDate 结束日期
     * @param array $breakPeriods 休息时间段
     * @return array 时间数组 [['start' => Carbon, 'end' => Carbon, 'slot' => object], ...]
     */
    public function calculateActivityTimes(ActivitySchedule $schedule, Carbon $startDate, Carbon $endDate, array $breakPeriods = []): array
    {
        $times = [];
        // 如果日程无效，直接返回空数组
        if (!$schedule || !$schedule->activity) {
            return $times;
        }

        // 确保日期范围在活动日程范围内
        $scheduleStart = $schedule->start_date->copy()->startOfDay();
        $scheduleEnd = $schedule->end_date->copy()->endOfDay();

        // 如果日期范围完全在活动日程范围外，直接返回空数组
        if ($endDate->lt($scheduleStart) || $startDate->gt($scheduleEnd)) {
            return $times;
        }

        // 获取有效日期范围
        $effectiveStartDate = max($scheduleStart, $startDate->copy()->startOfDay());
        $effectiveEndDate = min($scheduleEnd, $endDate->copy()->endOfDay());

        // 创建日期范围
        $period = CarbonPeriod::create($effectiveStartDate, $effectiveEndDate);

        // 获取重复类型特定的日期过滤器
        $dateFilter = $this->getDateFilterByRepeatType($schedule);
        foreach ($period as $date) {
            $day = $date->format('Y-m-d');
            // 跳过休息时间内的日期
            if ($this->isDateInBreakPeriods($date, $breakPeriods)) {
                continue;
            }
            // 根据重复类型过滤日期
            if (!$dateFilter($date, $schedule)) {
                continue;
            }

            $times[$day] = $schedule;
        }
        return $times;
    }

    /**
     * 根据重复类型获取日期过滤函数
     * 
     * @param ActivitySchedule $schedule 活动日程
     * @return \Closure 日期过滤函数
     */
    protected function getDateFilterByRepeatType(ActivitySchedule $schedule): \Closure
    {
        switch ($schedule->repeat_type) {
            case 'once':
                return function (Carbon $date, ActivitySchedule $schedule) {
                    return $date->isSameDay($schedule->start_date);
                };
                break;
            case 'daily':
                return function (Carbon $date, ActivitySchedule $schedule) {
                    // 检查日期是否符合重复频率
                    $daysDiff = $date->diffInDays($schedule->start_date);
                    return $daysDiff % $schedule->repeat_frequency === 0;
                };
                break;
            case 'weekly':
                return function (Carbon $date, ActivitySchedule $schedule) {
                    $repeatDays = $schedule->timeSlots->pluck('day_of_week')->toArray();
                    // 首先检查当前日期的星期几是否在重复的星期几列表中
                    if (!in_array($date->dayOfWeek, $repeatDays)) {
                        return false;
                    }
                    // 计算从活动开始日期到当前日期的周数差
                    $weeksDiff = $schedule->start_date->diffInWeeks($date);
                    // 如果重复频率大于1，则需要检查是否在正确的重复周内
                    if ($schedule->repeat_frequency > 1) {
                        $isValid = $weeksDiff % $schedule->repeat_frequency === 0;
                        if (!$isValid) {
                            return false;
                        }
                    }
                    // 重复频率为1，只要星期几匹配即可
                    return true;
                };
                break;
            case 'monthly':
                return function (Carbon $date, ActivitySchedule $schedule) {
                    // 获取当前日期是一个月中的第几天
                    $dayOfMonth = (int)$date->format('j');

                    // 获取当前日期是星期几 (0-6)
                    $dayOfWeek = (int)$date->dayOfWeek;

                    // 获取当前日期是当月的第几周
                    $weekOfMonth = (int)ceil($dayOfMonth / 7);

                    // 检查月度重复频率
                    $monthsDiff = $schedule->start_date->diffInMonths($date);
                    if ($schedule->repeat_frequency > 1 && $monthsDiff % $schedule->repeat_frequency !== 0) {
                        return false;
                    }

                    // 检查时间段中的 day_of_month 设置
                    foreach ($schedule->timeSlots as $slot) {
                        $daysOfMonth = $slot->day_of_month ?? [];
                        // 如果时间段没有设置 day_of_month，则跳过
                        if (empty($daysOfMonth)) {
                            continue;
                        }
                        // 如果是 JSON 字符串，则解码
                        if (is_string($daysOfMonth) && !is_array($daysOfMonth)) {
                            $daysOfMonth = json_decode($daysOfMonth, true);
                        }

                        // 处理数组数据
                        if (is_array($daysOfMonth)) {
                            // 简单数组格式：[5,6,7]
                            if ($this->isSimpleArray($daysOfMonth)) {
                                if (in_array($dayOfMonth, $daysOfMonth)) {
                                    return true;
                                }
                            }
                            // 复杂对象格式
                            else {
                                // 如果包含特定日期数组
                                if (isset($daysOfMonth['dates']) && is_array($daysOfMonth['dates'])) {
                                    if (in_array($dayOfMonth, $daysOfMonth['dates'])) {
                                        return true;
                                    }
                                }

                                // 如果使用星期几和周数的组合
                                if (isset($daysOfMonth['weekday']) && isset($daysOfMonth['week'])) {
                                    if ($dayOfWeek == $daysOfMonth['weekday'] && $weekOfMonth == $daysOfMonth['week']) {
                                        return true;
                                    }
                                }
                            }
                        }
                    }

                    return false;
                };
                break;
            default:
                return function (Carbon $date, ActivitySchedule $schedule) {
                    return false;
                };
        }
    }

    /**
     * 获取多个日程的所有活动时间
     * 
     * @param \Illuminate\Support\Collection $schedules 日程集合
     * @param Carbon $start_date 开始日期
     * @param Carbon $end_date 结束日期
     * @return array 所有活动时间 ['2023-01-01' => [活动时间数组], ...]
     */
    public function calculateAllScheduleTimes($schedules, Carbon $start_date, Carbon $end_date): array
    {
        if (!$schedules || $schedules->isEmpty() || !$start_date || !$end_date) {
            return [];
        }
        $allTimes = [];
        foreach ($schedules as $schedule) {
            $breakPeriods = $this->getBreakPeriods($schedule);
            // 计算活动时间
            $times = $this->calculateActivityTimes(
                $schedule,
                $start_date->copy(),
                $end_date->copy(),
                $breakPeriods
            );
            // 将计算出的时间按日期分组

            foreach ($times as $dayKey => $time) {
                if (!isset($allTimes[$dayKey])) {
                    $allTimes[$dayKey] = [];
                }
                \Log::info('添加活动时间', [
                    'day_key' => $dayKey,
                    'time' => $time
                ]);
                $allTimes[$dayKey][] = $time;
            }
        }

        // 按日期键（升序）排序
        ksort($allTimes);

        return $allTimes;
    }

    /**
     * 获取休息时间段
     * 
     * @param ActivitySchedule $schedule 日程
     * @return array 休息时间段数组
     */
    protected function getBreakPeriods($schedule)
    {
        $breakPeriods = [];
        $periods = $schedule->breakPeriods;

        if ($periods && $periods->isNotEmpty()) {
            foreach ($periods as $period) {
                $breakPeriods[] = [
                    'start' => Carbon::parse($period->start_date)->startOfDay(),
                    'end' => Carbon::parse($period->end_date)->endOfDay()
                ];
            }
        }

        return $breakPeriods;
    }

    /**
     * 检查日期是否在休息时间内
     * 
     * @param Carbon $date 日期
     * @param array $breakPeriods 休息时间段数组
     * @return bool 是否在休息时间内
     */
    protected function isDateInBreakPeriods($date, $breakPeriods)
    {
        if (empty($breakPeriods)) {
            return false;
        }

        // 将输入日期标准化为当天开始时间，确保比较的一致性
        $targetDate = $date->format('Y-m-d');

        foreach ($breakPeriods as $period) {
            // 获取休息时间的开始和结束时间点
            $periodStart = $period['start']->format('Y-m-d');
            $periodEnd = $period['end']->format('Y-m-d');
            if ($targetDate >= $periodStart && $targetDate <= $periodEnd) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查数组是否是简单的索引数组 (兼容PHP 8.0及以下版本)
     * 
     * @param array $array 要检查的数组
     * @return bool 是否是简单索引数组
     */
    protected function isSimpleArray(array $array): bool
    {
        if (empty($array)) {
            return true;
        }

        // PHP 8.1+ 可以直接使用 array_is_list
        if (function_exists('array_is_list')) {
            return array_is_list($array);
        }

        // 对于早期PHP版本，手动检查
        if (array_keys($array) !== range(0, count($array) - 1)) {
            return false;
        }

        return true;
    }
}
