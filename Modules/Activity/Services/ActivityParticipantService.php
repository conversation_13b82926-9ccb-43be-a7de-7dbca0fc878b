<?php

declare(strict_types=1);

namespace Modules\Activity\Services;

use Modules\Activity\Models\ActivityParticipant;
use Modules\Activity\Enums\ActivityErrorCode;
use Bingo\Exceptions\BizException;
use Modules\Activity\Admin\Resources\ActivityParticipantResource;

final class ActivityParticipantService
{
    public function list(array $filters = [])
    {
        $query = ActivityParticipant::query();

        $status = $filters['status'] ?? '';
        $notification_type = $filters['notification_type'] ?? '';

        if (!empty($filters['activity_id'])) {
            $query->where('activity_id', $filters['activity_id']);
        }
        if (!empty($status)) {
            $query->where('status', $filters['status']);
        }
        $query->when($status == 'waiting' && !empty($notification_type), function ($q) use ($status, $notification_type) {
            $q->where('notification_type', $notification_type);
        });


        $perPage = $filters['per_page'] ?? 20;

        // 判断排序方式
        if (!empty($filters['status']) && $filters['status'] === 'waiting') {
            $query->orderBy('created_at', 'desc');
        } else {
            $query->orderByDesc('id');
        }

        $data = $query->paginate($perPage);

        $items = [];
        if (!empty($filters['status']) && $filters['status'] === 'waiting') {
            $total = $data->total();
            $start = $total - ($data->currentPage() - 1) * $data->perPage();
            foreach ($data as $idx => $participant) {
                $items[] = new ActivityParticipantResource($participant, $start - $idx);
            }
        } else {
            foreach ($data as $participant) {
                $items[] = new ActivityParticipantResource($participant);
            }
        }

        return [
            'items' => $items,
            'total' => $data->total(),
        ];
    }

    public function get(int $id): ActivityParticipant
    {
        $participant = ActivityParticipant::find($id);
        if (!$participant) {
            BizException::throws(ActivityErrorCode::PARTICIPANT_NOT_FOUND);
        }
        return $participant;
    }

    public function create(array $data): ActivityParticipant
    {
        return ActivityParticipant::create($data);
    }

    public function update(int $id, array $data): ActivityParticipant
    {
        $participant = ActivityParticipant::findOrFail($id);
        $participant->update($data);
        return $participant;
    }

    public function delete(int $id): void
    {
        ActivityParticipant::findOrFail($id)->delete();
    }

    public function updateStatus(int $id, string $status): ActivityParticipant
    {
        $participant = ActivityParticipant::findOrFail($id);
        $participant->status = $status;
        $participant->save();
        return $participant;
    }
}
