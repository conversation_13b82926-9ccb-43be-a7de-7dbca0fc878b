<?php

declare(strict_types=1);

namespace Modules\Activity\Services;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Modules\Activity\Models\ActivityInvitation;
use Modules\Activity\Models\ActivityInvitationMember;
use Modules\Activity\Models\ActivityInvitationMembersLog;
use Carbon\Carbon;
use Illuminate\Support\Arr;

/**
 * 活动邀请服务类
 */
class ActivityInvitationService
{
    /**
     * 保存活动邀请
     *
     * @param array $data 邀请数据
     * @param int|null $id 邀请ID（更新时提供）
     * @return ActivityInvitation
     */
    public function save(array $data, ?int $id = null): ActivityInvitation
    {
        try {
            DB::beginTransaction();

            if ($id) {
                // 更新邀请
                $invitation = ActivityInvitation::findOrFail($id);
                $invitation->update($data);
            } else {
                $data['status'] = isset($data['status']) ? $data['status'] : 1;
                // 创建新邀请
                $invitation = ActivityInvitation::create($data);
            }
            $members = Arr::get($data, 'members', []);
            foreach ($members as $member) {

                $member['creator_id'] = $invitation->creator_id;
                $this->saveMembers($invitation->id, $member);
            }

            DB::commit();
            return $invitation;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('保存活动邀请信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception(T('Activity::invitation.messages.save_error'));
        }
    }

    /**
     * 复制邀请
     *
     * @param int $id 邀请ID
     * @return ActivityInvitation
     */
    public function copy(int $id)
    {
        $invitation = ActivityInvitation::find($id);
        if (!$invitation) {
            throw new \Exception(T('Activity::invitation.messages.not_found'));
        }
        $invitation->id = null;
        $invitation->status = 0;
        $newInvitation = $this->save($invitation->toArray());
        return $newInvitation;
    }

    /**
     * 获取活动邀请详情
     *
     * @param int $id 邀请ID
     * @return ActivityInvitation
     */
    public function detail(int $id)
    {
        $invitation = ActivityInvitation::find($id);
        if (!$invitation) {
            throw new \Exception(T('Activity::invitation.messages.not_found'));
        }
        return $invitation;
    }

    /**
     * 删除活动邀请
     *
     * @param int $id 邀请ID
     * @return bool
     */
    public function delete(int $id)
    {
        $invitation = ActivityInvitation::find($id);
        if (!$invitation) {
            throw new \Exception(T('Activity::invitation.messages.not_found'));
        }
        $invitation->delete();
        return true;
    }

    /**
     * 获取活动邀请列表
     *
     * @param array $params 查询参数
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function list(array $params = [])
    {
        $query = ActivityInvitation::query();

        // 根据活动ID筛选
        if (isset($params['activity_id'])) {
            $query->where('activity_id', $params['activity_id']);
        }

        // 根据状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 根据名称搜索
        if (isset($params['title']) && $params['title']) {
            $query->where('title', 'like', '%' . $params['title'] . '%');
        }

        // 是否为默认列表筛选
        if (isset($params['is_default'])) {
            $query->where('is_default', $params['is_default']);
        }

        // 群组关联筛选
        if (isset($params['is_group'])) {
            $query->where('is_group', $params['is_group']);

            if ($params['is_group'] == 1 && isset($params['group_id'])) {
                $query->where('group_id', $params['group_id']);
            }
        }

        // 默认按创建时间倒序
        $sort_field = $params['sort_field'] ?? 'created_at';
        $sort_order = $params['sort_order'] ?? 'desc';
        $query->orderBy($sort_field, $sort_order);

        // 获取每页显示数量，默认20条
        $per_page = $params['per_page'] ?? 20;
        $data = $query->paginate($per_page);
        return [
            'items' => $data->items(),
            'total' => $data->total()
        ];
    }



    /**
     * 获取邀请成员列表
     *
     * @param int $invitationId 邀请ID
     * @param array $params 查询参数
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function listMembers(int $invitationId, array $params = [])
    {
        $query = ActivityInvitationMember::query()->where('invitation_id', $invitationId);

        // 根据姓名搜索
        if (isset($params['name']) && $params['name']) {
            $query->where('name', 'like', '%' . $params['name'] . '%');
        }

        // 根据邮箱搜索
        if (isset($params['email']) && $params['email']) {
            $query->where('email', 'like', '%' . $params['email'] . '%');
        }

        // 根据手机号搜索
        if (isset($params['phone']) && $params['phone']) {
            $query->where('phone', 'like', '%' . $params['phone'] . '%');
        }

        // 根据状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 默认按创建时间倒序
        $sort_field = $params['sort_field'] ?? 'created_at';
        $sort_order = $params['sort_order'] ?? 'desc';
        $query->orderBy($sort_field, $sort_order);

        // 获取每页显示数量，默认20条
        $per_page = $params['per_page'] ?? 20;
        $data = $query->paginate($per_page);
        return [
            'items' => $data->items(),
            'total' => $data->total()
        ];
    }
    /**
     * 保存邀请成员
     *
     * @param int $invitationId 邀请ID
     * @param array $member 成员数据
     * @param int|null $id 成员ID（更新时提供）
     * @return ActivityInvitationMember 创建或更新的成员模型
     */
    public function saveMembers(int $invitationId, array $member, ?int $id = null): ActivityInvitationMember
    {
        $invitation = ActivityInvitation::findOrFail($invitationId);

        try {
            DB::beginTransaction();

            $member['invitation_id'] = $invitationId;
            $member['activity_id'] = $invitation->activity_id;
            $member['creator_id'] = $invitation->creator_id;

            if ($id > 0) {
                // 更新成员
                $memberModel = ActivityInvitationMember::findOrFail($id);
                $memberModel->update($member);
            } else {
                // 过滤重复导入
                $member_exist = ActivityInvitationMember::where('invitation_id', $invitationId)->where('phone', $member['phone'])->orWhere('email', $member['email'])->first();
                if ($member_exist) {
                    return $member_exist;
                }
                // 设置邀请码
                $member['code'] = $this->generateInvitationCode();
                // 创建成员
                $memberModel = ActivityInvitationMember::create($member);
            }
            DB::commit();
            return $memberModel;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('保存邀请成员失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception(T('Activity::invitation.messages.save_error'));
        }
    }

    /**
     * 导入邀请成员
     *
     * @param int $invitationId 邀请ID
     * @param UploadedFile $file 上传的文件
     * @return array 导入结果
     */
    public function importMembers(int $invitationId, UploadedFile $file): array
    {
        $invitation = ActivityInvitation::findOrFail($invitationId);

        // 处理导入文件
        $extension = $file->getClientOriginalExtension();
        $content = null;

        if ($extension === 'csv') {
            $content = $this->parseCsvFile($file);
        } elseif (in_array($extension, ['xls', 'xlsx'])) {
            $content = $this->parseExcelFile($file);
        } else {
            throw new \Exception(T('Activity::invitation.messages.file_format_error'));
        }

        if (!$content || !is_array($content)) {
            throw new \Exception(T('Activity::invitation.messages.file_parse_error'));
        }

        $successCount = 0;
        $failCount = 0;
        $errors = [];

        try {
            DB::beginTransaction();
            foreach ($content as $index => $member) {
                try {
                    $this->saveMembers($invitationId, $member);
                    $successCount++;
                } catch (\Exception $e) {
                    $failCount++;
                    $errors[] = [
                        'row' => $index + 1,
                        'message' => $e->getMessage()
                    ];
                }
            }
            DB::commit();
            return [
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'errors' => $errors
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('导入邀请成员失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception(T('Activity::invitation.messages.save_error'));
        }
    }


    /**
     * 生成邀请码
     *
     * @return string
     */
    private function generateInvitationCode(): string
    {
        return Str::random(8);
    }

    /**
     * 解析CSV文件
     *
     * @param UploadedFile $file
     * @return array
     */
    private function parseCsvFile(UploadedFile $file): array
    {
        $path = $file->getRealPath();
        $handle = fopen($path, 'r');
        $header = fgetcsv($handle);
        $data = [];

        // 处理UTF-8 BOM
        if (isset($header[0]) && strpos($header[0], "\xEF\xBB\xBF") === 0) {
            $header[0] = substr($header[0], 3);
        }

        // 映射字段名（适配导入模板和导出模板）
        $fieldMap = [
            T('Activity::invitation.fields.name') => 'name',
            T('Activity::invitation.fields.phone') => 'phone',
            T('Activity::invitation.fields.email') => 'email',
            T('Activity::invitation.fields.company') => 'company',
            T('Activity::invitation.fields.position') => 'position',
        ];

        // 标准化header
        $normalizedHeader = [];
        foreach ($header as $index => $key) {
            $cleanKey = trim($key);
            if (isset($fieldMap[$cleanKey])) {
                $normalizedHeader[$index] = $fieldMap[$cleanKey];
            } else {
                $normalizedHeader[$index] = $cleanKey;
            }
        }

        while (($row = fgetcsv($handle)) !== false) {
            $item = [];
            foreach ($normalizedHeader as $index => $key) {
                $item[$key] = $row[$index] ?? '';
            }
            $data[] = $item;
        }

        fclose($handle);
        return $data;
    }

    /**
     * 解析Excel文件
     *
     * @param UploadedFile $file
     * @return array
     */
    private function parseExcelFile(UploadedFile $file): array
    {
        // 这里可以使用 Laravel Excel 等扩展解析Excel文件
        // 示例代码，实际应根据项目中使用的Excel处理库调整
        return [];
    }


    /**
     * 导出邀请成员数据
     *
     * @param int $invitationId 邀请ID
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function exportMembers(int $invitationId)
    {
        $invitation = ActivityInvitation::findOrFail($invitationId);
        $members = ActivityInvitationMember::where('invitation_id', $invitationId)->get();

        if ($members->isEmpty()) {
            throw new \Exception(T('Activity::invitation.messages.no_data'));
        }

        // 设置文件名
        $filename = 'invitation_members_' . $invitationId . '_' . date('YmdHis') . '.csv';

        // 状态映射
        $statusMap = [
            0 => T('Activity::invitation.member_status.not_replied'),
            1 => T('Activity::invitation.member_status.registered'),
            2 => T('Activity::invitation.member_status.declined')
        ];

        // 创建流式响应
        return response()->stream(
            function () use ($members, $statusMap) {
                // 打开输出流
                $output = fopen('php://output', 'w');

                // 添加UTF-8 BOM，解决中文Excel打开乱码问题
                fputs($output, "\xEF\xBB\xBF");

                // 添加CSV头部
                fputcsv($output, [
                    T('Activity::invitation.fields.name'),
                    T('Activity::invitation.fields.phone'),
                    T('Activity::invitation.fields.email'),
                    T('Activity::invitation.fields.company'),
                    T('Activity::invitation.fields.position'),
                    T('Activity::invitation.fields.code'),
                    T('Activity::invitation.fields.status')
                ]);

                // 添加数据行
                foreach ($members as $member) {
                    fputcsv($output, [
                        $member->name,
                        $member->phone,
                        $member->email,
                        $member->company,
                        $member->position,
                        $member->code,
                        $statusMap[$member->status] ?? '未知'
                    ]);
                }

                fclose($output);
            },
            200,
            [
                'Content-Type' => 'text/csv; charset=UTF-8',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"'
            ]
        );
    }

    /**
     * 导出邀请成员导入模板
     * 
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function exportTemplate()
    {
        // 设置文件名
        $filename = 'invitation_members_template_' . date('YmdHis') . '.csv';

        // 创建流式响应
        return response()->stream(
            function () {
                // 打开输出流
                $output = fopen('php://output', 'w');

                // 添加UTF-8 BOM，解决中文Excel打开乱码问题
                fputs($output, "\xEF\xBB\xBF");

                // 添加CSV头部（与导入格式保持一致）
                fputcsv($output, [
                    T('Activity::invitation.fields.name'),
                    T('Activity::invitation.fields.phone'),
                    T('Activity::invitation.fields.email'),
                    T('Activity::invitation.fields.company'),
                    T('Activity::invitation.fields.position')
                ]);

                // 添加示例数据行
                fputcsv($output, [
                    T('Activity::invitation.example.name'),
                    T('Activity::invitation.example.phone'),
                    T('Activity::invitation.example.email'),
                    T('Activity::invitation.example.company'),
                    T('Activity::invitation.example.position')
                ]);

                fclose($output);
            },
            200,
            [
                'Content-Type' => 'text/csv; charset=UTF-8',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"'
            ]
        );
    }

    /**
     * 删除邀请成员
     *
     * @param int $id 成员ID
     * @return bool
     */
    public function deleteMember($id)
    {
        try {
            $member = ActivityInvitationMember::findOrFail($id);
            $member->delete();
        } catch (\Throwable $th) {
            Log::error('删除邀请成员失败', [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString()
            ]);
            throw new \Exception(T('Activity::invitation.messages.member_not_found'));
        }

        return true;
    }

    /**
     * 获取邀请成员详情
     *
     * @param int $invitationId 邀请ID
     * @param int $memberId 成员ID
     * @return ActivityInvitationMember
     **/
    public function detailMember($invitation_id, $member_id)
    {
        $member = ActivityInvitationMember::with('logs')->where('invitation_id', $invitation_id)->where('id', $member_id)->select('id', 'name', 'phone', 'email', 'company', 'position', 'status', 'created_at')->first();
        if (!$member) {
            throw new \Exception(T('Activity::invitation.messages.member_not_found'));
        }
        return $member;
    }
}
