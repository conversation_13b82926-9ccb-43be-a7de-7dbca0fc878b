<?php

declare(strict_types=1);

namespace Modules\Activity\Services;

use Modules\Activity\Models\ActivityRule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Bingo\Exceptions\BizException;
use Modules\Activity\Enums\ActivityErrorCode;

/**
 * 活动规则管理服务
 */
class ActivityRuleService
{
    /**
     * 规则列表
     */
    public function list(array $params)
    {
        $query = ActivityRule::query();
        if (!empty($params['keyword'])) {
            $query->where('name', 'like', '%' . $params['keyword'] . '%');
        }
        if (isset($params['scene'])) {
            $query->where('scene', $params['scene']);
        }
        $query->orderByDesc('id');
        $perPage = $params['per_page'] ?? 20;
        $data = $query->paginate($perPage);
        
        return [
            'items' => $data->items(),
            'total' => $data->total(),
        ];
    }

    /**
     * 规则详情
     */
    public function detail(int $id)
    {
        return ActivityRule::findOrFail($id);
    }

    /**
     * 新建/编辑规则
     */
    public function save(array $data, int $id = null)
    {
        try {
            if ($id) {
                $rule = ActivityRule::find($id);
                if (!$rule) {
                    BizException::throws(ActivityErrorCode::RULE_NOT_FOUND);
                }
                $rule->fill($data);
                $rule->save();
            } else {
                $data['creator_id'] = Auth::id() ?? 0;
                $rule = ActivityRule::create($data);
            }
            return $rule;
        } catch (\Exception $e) {
            BizException::throws(ActivityErrorCode::RULE_SAVE_FAILED, $e->getMessage());
        }
    }

    /**
     * 删除规则
     */
    public function delete(int $id)
    {
        return ActivityRule::destroy($id);
    }

    /**
     * 批量复制
     */
    public function batchCopy(array $ids)
    {
        $rules = ActivityRule::whereIn('id', $ids)->get();
        $newRules = [];
        DB::beginTransaction();
        try {
            foreach ($rules as $rule) {
                $new = $rule->replicate();
                $new->name = $rule->name . '_副本';
                $new->save();
                $newRules[] = $new;
            }
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
        return $newRules;
    }

    /**
     * 批量移动
     */
    public function move(array $ids, string $scene)
    {
        return ActivityRule::whereIn('id', $ids)->update(['scene' => $scene]);
    }

    /**
     * 状态切换
     */
    public function updateStatus(int $id, int $status): bool
    {
        try {
            $rule = ActivityRule::find($id);
            if (!$rule) {
                BizException::throws(ActivityErrorCode::RULE_NOT_FOUND);
            }
            $rule->status = $status;
            $rule->save();
            return true;
        } catch (\Exception $e) {
            BizException::throws(ActivityErrorCode::RULE_SAVE_FAILED, $e->getMessage());
        }
    }

    /**
     * 场景列表（可根据实际业务调整）
     */
    public function sceneList()
    {
        // 示例：可根据实际业务从配置或表中获取
        return ActivityRule::sceneList();
    }
}
