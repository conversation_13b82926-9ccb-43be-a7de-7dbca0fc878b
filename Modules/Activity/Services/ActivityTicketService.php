<?php
/*
 * @Author: <PERSON>ron
 * @Date: 2025-03-31 14:39:34
 * @LastEditTime: 2025-06-24 18:19:11
 * @LastEditors: Chiron
 * @Description: 活动票务服务
 */

declare(strict_types=1);

namespace Modules\Activity\Services;

use Modules\Activity\Models\ActivityTicketTypes;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Arr;
use Modules\Activity\Models\ActivityTicketGroupsRelation;
use Modules\Activity\Models\ActivityTicketTypeRelation;
use Modules\Activity\Models\ActivityTicketSubtypes;

use Bingo\Exceptions\BizException;
use Modules\Activity\Enums\ActivityErrorCode;
use Bingo\Support\Facades\T;

class ActivityTicketService
{
    /**
     * 获取票务列表
     */
    public function list(array $params)
    {
        // 获取每页显示数量，默认15条
        $query = ActivityTicketTypes::query()->with('subtypes');
        // $query->where('activity_id', $params['activity_id'] ?? 0);
        $query->orderBy('created_at', 'desc');
        $per_page = $params['per_page'] ?? 15;
        return $query->paginate($per_page);
    }

    /**
     * 获取票务详情
     */
    public function detail(int $id)
    {
        return ActivityTicketTypes::query()->with(['subtypes', 'groups'])->findOrFail($id);
    }

    /**
     * 保存票务信息
     */
    public function save(array $data, ?int $id = null)
    {
        try {
            DB::beginTransaction();
            $subtypes = Arr::get($data, 'subtypes', []);
            $groups = Arr::get($data, 'groups', []);
            $activity_id = Arr::get($data, 'activity_id', 0);

            if ($id) {
                // 更新活动
                $ticket_type = ActivityTicketTypes::findOrFail($id);
                $ticket_type->update($data);
            } else {
                $ticket_type = ActivityTicketTypes::create($data);
            }

            // 关联活动（如果activity_id不为0）
            if ($activity_id > 0 && $ticket_type->id > 0) {
                $data['id'] = $ticket_type->id;
                $this->saveTicketTypeRelation($activity_id, [$data], $ticket_type->creator_id);
            }

            // 保存子类型
            if (!empty($subtypes) && $ticket_type->id > 0) {
                $this->saveSubtypes($ticket_type->id, $subtypes, $ticket_type->creator_id);
            }

            // 保存票务群组
            if (!empty($groups) && $ticket_type->id > 0) {
                $this->saveTicketGroupRelation($ticket_type->id, $groups, $ticket_type->creator_id);
            }

            DB::commit();
            return $ticket_type;
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('保存活动票务信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 保存票务子类型信息
     * @param int $ticket_id 票务ID
     * @param array $subtypes 子类型数据数组
     * @param int $creator_id 创建者ID
     */
    public function saveSubtypes(int $ticket_id, array $subtypes, int $creator_id = 0)
    {
        return DB::transaction(function () use ($ticket_id, $subtypes, $creator_id) {

            ActivityTicketSubtypes::where('ticket_type_id', $ticket_id)->delete();

            // 更新或创建关联
            foreach ($subtypes as $subtype) {
                ActivityTicketSubtypes::updateOrCreate([
                    'ticket_type_id' => $ticket_id,
                    'name' => $subtype['name'] ??  ''
                ], [
                    'creator_id' => $creator_id,
                    'name' => $subtype['name'] ?? '',
                    'sort' => $subtype['sort'] ?? 0,
                ]);
            }
        });
    }


    /**
     * 保存活动票务关系（创建或更新）
     *
     * @param int $activity_id 活动ID
     * @param array $ticket_types 票务类型数据数组
     * @param int $creator_id 创建者ID
     * @return bool
     */
    public function saveTicketTypeRelation(int $activity_id, array $ticket_types, int $creator_id = 0)
    {
        return DB::transaction(function () use ($activity_id, $ticket_types, $creator_id) {
            // 更新或创建关联
            foreach ($ticket_types as $ticket_type) {

                $ticket_id = $ticket_type['id'] ?? 0;
                $ticket_type['creator_id'] = $creator_id;

                if ($ticket_id <= 0) {
                    $ticket = $this->save($ticket_type);
                    $ticket_id = $ticket->id;
                }
                ActivityTicketTypeRelation::updateOrCreate([
                    'activity_id' => $activity_id,
                    'ticket_type_id' => $ticket_id
                ], [
                    'quota' => $ticket_type['quota'] ?? 0,
                    'remaining_quota' => $ticket_type['remaining_quota'] ?? null,
                    'sort' => $ticket_type['sort'] ?? 0,
                    'creator_id' => $creator_id,
                    'subtypes' => $ticket_type['subtypes'] ?? []
                ]);
            }
            return true;
        });
    }

    /**
     * 创建票务类型与活动的关联关系
     *
     * @param int $activity_id 活动ID
     * @param int $ticket_type_id 票务类型ID
     * @param int $creator_id 创建者ID
     * @return void
     */
    private function createTicketActivityRelation(int $activity_id, int $ticket_type_id, int $creator_id): void
    {
        // 检查关联是否已存在，避免重复创建
        $existingRelation = ActivityTicketTypeRelation::where('activity_id', $activity_id)
            ->where('ticket_type_id', $ticket_type_id)
            ->first();

        if (!$existingRelation) {
            ActivityTicketTypeRelation::create([
                'activity_id' => $activity_id,
                'ticket_type_id' => $ticket_type_id,
                'quota' => 0, // 默认配额为0，可以后续修改
                'remaining_quota' => 0, // 默认剩余配额为0
                'sort' => 0, // 默认排序为0
                'creator_id' => $creator_id,
            ]);
            app('log')->info('票务类型与活动关联成功', [
                'activity_id' => $activity_id,
                'ticket_type_id' => $ticket_type_id,
                'creator_id' => $creator_id
            ]);
        }
    }

    /**
     * 保存票务组关系（创建或更新）
     *
     * @param int $ticket_id 活动ID
     * @param array $groups 活动组数据数组
     * @param int $creator_id 创建者ID
     * @return bool
     */
    public function saveTicketGroupRelation(int $ticket_id, array $groups, int $creator_id = 0)
    {
        return DB::transaction(function () use ($ticket_id, $groups, $creator_id) {
            // 先删除不存在的关联
            $existingGroupIds = collect($groups)->pluck('id')->filter()->all();
            ActivityTicketGroupsRelation::where('ticket_id', $ticket_id)
                ->when(!empty($existingGroupIds), function ($query) use ($existingGroupIds) {
                    $query->whereNotIn('group_id', $existingGroupIds);
                })
                ->delete();

            // 更新或创建关联
            foreach ($groups as $group) {
                ActivityTicketGroupsRelation::updateOrCreate([
                    'ticket_id' => $ticket_id,
                    'group_id' => $group['id']
                ], [
                    'creator_id' => $creator_id
                ]);
            }
            return true;
        });
    }

    /**
     * 复制票务
     *
     * @param int $copy_id 要复制的票务ID
     * @return ActivityTicketTypes 新创建的票务对象
     */
    public function copy(int $copy_id)
    {
        try {
            DB::beginTransaction();

            // 1. 复制主表数据
            $ticket = ActivityTicketTypes::findOrFail($copy_id);
            $new_ticket = $ticket->replicate();
            $new_ticket->save();

            // 2. 复制票务子类型
            $subtypes = ActivityTicketSubtypes::where('ticket_type_id', $copy_id)->get();
            foreach ($subtypes as $subtype) {
                $new_subtype = $subtype->replicate();
                $new_subtype->ticket_type_id = $new_ticket->id;
                $new_subtype->save();
            }

            // 3. 复制票务群组关联
            $groups = ActivityTicketGroupsRelation::where('ticket_id', $copy_id)->get();
            foreach ($groups as $group) {
                $new_group = $group->replicate();
                $new_group->ticket_id = $new_ticket->id;
                $new_group->save();
            }

            DB::commit();
            return $new_ticket;
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('复制票务失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 删除票务类型及其所有关联数据
     *
     * @param int $id 票务类型ID
     * @return bool
     */
    public function delete(int $id): bool
    {
        try {
            DB::beginTransaction();

            $ticket = ActivityTicketTypes::findOrFail($id);

            // 检查是否有活动将此票务作为唯一票务类型
            $relatedActivities = ActivityTicketTypeRelation::where('ticket_type_id', $id)
                ->with('activity')
                ->get();

            foreach ($relatedActivities as $relation) {
                if (!$relation->activity) {
                    continue;
                }

                // 检查该活动是否只有这一个票务类型
                $activityTicketCount = ActivityTicketTypeRelation::where('activity_id', $relation->activity_id)->count();

                if ($activityTicketCount <= 1) {
                    BizException::throws(
                        ActivityErrorCode::ACTIVITY_DELETE_FAILED,
                        T('Activity::messages.ticket_delete_failed_unique', [
                            'ticket_name' => $ticket->name,
                            'activity_name' => $relation->activity->title
                        ])
                    );
                }
            }

            // 1. 删除票务子类型
            $subtypeCount = ActivityTicketSubtypes::where('ticket_type_id', $id)->count();
            ActivityTicketSubtypes::where('ticket_type_id', $id)->delete();

            // 2. 删除票务群组关联
            $groupCount = ActivityTicketGroupsRelation::where('ticket_id', $id)->count();
            ActivityTicketGroupsRelation::where('ticket_id', $id)->delete();

            // 3. 删除活动票务类型关联
            $relationCount = ActivityTicketTypeRelation::where('ticket_type_id', $id)->count();
            ActivityTicketTypeRelation::where('ticket_type_id', $id)->delete();

            // 4. 最后删除票务类型本身
            $ticket->delete();

            DB::commit();

            app('log')->info(T('Activity::messages.ticket_delete_success'), [
                'ticket_type_id' => $id,
                'ticket_name' => $ticket->name,
                'ticket_code' => $ticket->code,
                'deleted_subtypes' => $subtypeCount,
                'deleted_group_relations' => $groupCount,
                'deleted_activity_relations' => $relationCount
            ]);

            return true;
        } catch (\Exception $e) {
            DB::rollBack();

            app('log')->error('删除票务类型失败', [
                'ticket_type_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 如果是业务异常，直接重新抛出
            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(ActivityErrorCode::ACTIVITY_DELETE_FAILED, $e->getMessage());
        }
    }

    /**
     * 获取指定活动下的票务列表（分页）
     * @param int $activity_id
     * @param int $perPage
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getTicketsByActivity(int $activity_id, int $perPage = 20)
    {
        return ActivityTicketTypeRelation::with('ticketType')
            ->where('activity_id', $activity_id)
            ->orderByDesc('id')
            ->paginate($perPage);
    }

    /**
     * 删除指定活动下的所有票务关联
     * @param int $activity_id
     * @return int 删除的记录数
     */
    public function deleteTicketsByActivity(int $activity_id): int
    {
        return ActivityTicketTypeRelation::where('activity_id', $activity_id)->delete();
    }

    /**
     * 删除指定活动下的指定票务关联
     * @param int $activity_id
     * @param int $ticket_type_id
     * @return int 删除的记录数
     */
    public function deleteActivityTicketRelation(int $activity_id, int $ticket_type_id): int
    {
        return ActivityTicketTypeRelation::where('activity_id', $activity_id)
            ->where('ticket_type_id', $ticket_type_id)
            ->delete();
    }

    public function getTicketsByActivityInfo(int $activity_id, int $id)
    {
        return ActivityTicketTypeRelation::with([
            'ticketType',
            'ticketType.groups',
            'ticketType.subtypes'
        ])
            ->where('activity_id', $activity_id)
            ->where('ticket_type_id', $id)
            ->first();
    }
}
