<?php

namespace Modules\Activity\Services;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Modules\Activity\Models\Activity;
use Modules\Activity\Models\ActivitySchedule;
use Modules\Activity\Models\ActivityScheduleTimeSlot;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;


class ActivityCalendarService
{
    public function getActivitiesByDateRange($startDate, $endDate, array $filters = []): array
    {
        // 处理日期参数
        $startDate = $startDate instanceof Carbon ? $startDate : Carbon::parse($startDate);
        $endDate = $endDate instanceof Carbon ? $endDate : Carbon::parse($endDate);

        // 确保开始日期和结束日期是整天（无时分秒）
        $startDate = $startDate->startOfDay();
        $endDate = $endDate->endOfDay();

        // 查询日期范围内的活动日程（包含休息时段）
        $schedules = app(ActivityScheduleService::class)->getSchedulesInDateRange($startDate, $endDate, $filters);

        // 按天分组结果
        $calendar_days = $this->initializeCalendarDays($startDate, $endDate);

        // 添加活动时间和休息时段到日历
        $calendar_days = $this->addActivityTimesToCalendar($schedules, $calendar_days, $startDate, $endDate);

        return $calendar_days;
    }

    /**
     * 初始化日历日期数组
     *
     * @param Carbon $startDate 开始日期
     * @param Carbon $endDate 结束日期
     * @return array
     */
    protected function initializeCalendarDays($startDate, $endDate)
    {
        $calendarDays = [];
        // 创建日期范围内的每一天
        $dateRange = CarbonPeriod::create($startDate, $endDate);

        // 初始化每天的活动数组
        foreach ($dateRange as $date) {
            $dayKey = $date->format('Y-m-d');
            $calendarDays[$dayKey] = [
                'date' => $date->format('Y-m-d'),
                'day_of_week' => $date->dayOfWeek,
                'day_name' => $date->translatedFormat('l'),
                'is_weekend' => in_array($date->dayOfWeek, [Carbon::SATURDAY, Carbon::SUNDAY]),
                'activities' => []
            ];
        }
        return $calendarDays;
    }

    /**
     * 处理休息时段并更新日历数据
     *
     * @param array $calendarDays 日历数据
     * @param Collection $schedules 活动日程集合
     * @return array 更新后的日历数据
     */
    protected function processBreakPeriods(array $calendarDays, $schedules): array
    {
        // 收集所有休息时段
        $allBreakPeriods = [];

        foreach ($schedules as $schedule) {
            if ($schedule->breakPeriods->isNotEmpty()) {
                foreach ($schedule->breakPeriods as $break) {
                    $allBreakPeriods[] = [
                        'schedule_id' => $schedule->id,
                        'activity_id' => $schedule->activity_id,
                        'start_date' => $break->start_date,
                        'end_date' => $break->end_date,
                        'start_time' => $break->start_time ?? '00:00',
                        'end_time' => $break->end_time ?? '23:59',
                    ];
                }
            }
        }

        // 处理每个休息时段
        foreach ($allBreakPeriods as $break) {
            $startDate = Carbon::parse($break['start_date']);
            $endDate = Carbon::parse($break['end_date']);

            // 创建休息日期的范围
            $dateRange = CarbonPeriod::create($startDate, $endDate);

            foreach ($dateRange as $date) {
                $dayKey = $date->format('Y-m-d');

                if (!isset($calendarDays[$dayKey])) {
                    continue;
                }

                // 标记休息时段
                $calendarDays[$dayKey]['break_periods'][] = [
                    'start_time' => $break['start_time'],
                    'end_time' => $break['end_time'],
                    'activity_id' => $break['activity_id'],
                    'schedule_id' => $break['schedule_id']
                ];

                // 过滤掉休息时段内的活动
                $calendarDays[$dayKey]['activities'] = array_filter(
                    $calendarDays[$dayKey]['activities'],
                    function ($activity) use ($break) {
                        return !$this->isActivityInBreakPeriod($activity, $break);
                    }
                );
            }
        }

        return $calendarDays;
    }

    /**
     * 检查活动是否在休息时段内
     *
     * @param array $activity 活动数据
     * @param array $break 休息时段数据
     * @return bool
     */
    protected function isActivityInBreakPeriod(array $activity, array $break): bool
    {
        // 如果活动不属于这个日程，跳过检查
        if ($activity['schedule_id'] != $break['schedule_id']) {
            return false;
        }

        $activityStart = Carbon::parse($activity['start_date'] . ' ' . $activity['start_time']);
        $activityEnd = Carbon::parse($activity['end_date'] . ' ' . $activity['end_time']);

        $breakStart = Carbon::parse($break['start_date'] . ' ' . $break['start_time']);
        $breakEnd = Carbon::parse($break['end_date'] . ' ' . $break['end_time']);

        // 检查活动时间是否与休息时段重叠
        return $activityStart->between($breakStart, $breakEnd) ||
            $activityEnd->between($breakStart, $breakEnd) ||
            ($activityStart <= $breakStart && $activityEnd >= $breakEnd);
    }


    protected function formatActivityData(ActivitySchedule $schedule, Carbon $date)
    {
        $activityData = [
            'schedule_id' => $schedule->id,
            'activity_id' => $schedule->activity_id,
            'start_date' => $schedule->start_date,
            'end_date' => $schedule->end_date,
            'start_time' => $schedule->start_time,
            'end_time' => $schedule->end_time,
            'has_break_periods' => $schedule->breakPeriods->isNotEmpty(),
            'break_periods' => $schedule->breakPeriods->map(function ($break) {
                return [
                    'start_date' => $break->start_date,
                    'end_date' => $break->end_date,
                    'start_time' => $break->start_time,
                    'end_time' => $break->end_time
                ];
            })->toArray()
        ];

        return $activityData;
    }


    /**
     * 计算活动时间并添加到日历中
     * 
     * @param \Illuminate\Support\Collection $schedules 日程集合
     * @param array $calendarDays 日历数据
     * @param Carbon $startDate 开始日期
     * @param Carbon $endDate 结束日期
     * @return array 更新后的日历数据
     */
    protected function addActivityTimesToCalendar($schedules, array $calendarDays, $startDate, $endDate): array
    {
        // 如果没有日程，直接返回原日历数据
        if (!$schedules || $schedules->isEmpty()) {
            \Log::info('没有找到符合条件的日程', [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'schedules_count' => 0
            ]);
            return $calendarDays;
        }
        try {
            $all_date_times = app(ActivityScheduleService::class)->calculateAllScheduleTimes($schedules, $startDate, $endDate);

            $calendar_days = [];
            \Log::info('计算出的活动时间', [
                'dates_with_activities' => count($all_date_times),
                'dates' => array_keys($all_date_times),
                'all_date_times' => $all_date_times
            ]);
            // 将计算出的时间添加到日历中
            foreach ($all_date_times as $date => $schedules) {

                if (!isset($calendar_days[$date])) {
                    $calendar_days[$date] = $calendarDays[$date];
                }
                foreach ($schedules as $schedule) {
                    \Log::info('all_date_times', [
                        'schedule' => $schedule->toArray(),
                        'dates' => $date
                    ]);

                    // 获取活动数据并添加时间段信息
                    $activity = $schedule->activity;
                    $activityData = $activity->toArray();

                    // 添加当前日程的时间段信息
                    $activityData['time_slots'] = $schedule->timeSlots->map(function ($timeSlot) {
                        return [
                            'id' => $timeSlot->id,
                            'schedule_id' => $timeSlot->schedule_id,
                            'start_time' => $timeSlot->start_time instanceof \Carbon\Carbon
                                ? $timeSlot->start_time->format('H:i')
                                : $timeSlot->start_time,
                            'end_time' => $timeSlot->end_time instanceof \Carbon\Carbon
                                ? $timeSlot->end_time->format('H:i')
                                : $timeSlot->end_time,
                            'day_of_week' => $timeSlot->day_of_week,
                            'day_of_month' => $timeSlot->day_of_month,
                            'created_at' => $timeSlot->created_at?->format('Y-m-d H:i'),
                            'updated_at' => $timeSlot->updated_at?->format('Y-m-d H:i')
                        ];
                    })->toArray();

                    // 添加开始时间到活动数据根级别（取第一个时间段的开始时间）
                    if (!empty($activityData['time_slots'])) {
                        $activityData['start_time'] = $activityData['time_slots'][0]['start_time'];
                        $activityData['end_time'] = $activityData['time_slots'][0]['end_time'];
                        $activityData['time_range'] = $activityData['start_time'] . '~' . $activityData['end_time'];
                    }

                    $calendar_days[$date]['activities'][] = $activityData;
                }
            }
        } catch (\Exception $e) {
            \Log::info('添加活动时间到日历时发生错误', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $calendar_days;
    }
}
