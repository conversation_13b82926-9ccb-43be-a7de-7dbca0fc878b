<?php

declare(strict_types=1);

namespace Modules\Activity\Services;

use Modules\Activity\Models\Activity;
use Modules\Activity\Models\ActivitySchedule;
use Modules\Activity\Enums\ActivityErrorCode;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Bingo\Exceptions\BizException;

/**
 * 活动状态服务类
 */
final class ActivityStatusService
{
    /**
     * 批量更新活动状态
     *
     * @return array 更新统计信息
     * @throws BizException
     */
    public function updateActivityStatuses(): array
    {
        try {
            $now = Carbon::now();
            $publishedCount = 0;
            $completedCount = 0;

            // 更新已创建的活动为已发布
            $publishedCount = $this->updateCreatedActivitiesToPublished($now);

            // 更新已过期的活动为已完成
            $completedCount = $this->updateExpiredActivitiesToCompleted($now);

            return [
                'published' => $publishedCount,
                'completed' => $completedCount,
                'total' => $publishedCount + $completedCount
            ];
        } catch (\Exception $e) {
            Log::error('批量更新活动状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            BizException::throws(ActivityErrorCode::ACTIVITY_STATUS_UPDATE_FAILED);
        }
    }

    /**
     * 更新已创建的活动为已发布状态
     *
     * @param Carbon $now
     * @return int 更新的活动数量
     */
    private function updateCreatedActivitiesToPublished(Carbon $now): int
    {
        $updatedCount = 0;

        // 获取所有已创建且到达发布时间的活动
        $activities = Activity::where('status', 'created')
            ->where('deleted_at', 0)
            ->where('publish_time', '<=', $now->toDateTimeString())
            ->get();

        foreach ($activities as $activity) {
            try {
                $this->updateActivityStatus($activity, 'published');
                $updatedCount++;
                
                Log::info('活动状态更新为已发布', [
                    'activity_id' => $activity->id,
                    'activity_title' => $activity->title,
                    'publish_time' => $activity->publish_time,
                    'updated_at' => $now->toDateTimeString()
                ]);
            } catch (\Exception $e) {
                Log::error('单个活动发布状态更新失败', [
                    'activity_id' => $activity->id,
                    'activity_title' => $activity->title,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        return $updatedCount;
    }

    /**
     * 更新已过期的活动为已完成状态
     *
     * @param Carbon $now
     * @return int 更新的活动数量
     */
    private function updateExpiredActivitiesToCompleted(Carbon $now): int
    {
        $updatedCount = 0;

        // 获取所有已发布的活动
        $activities = Activity::where('status', 'published')
            ->where('deleted_at', 0)
            ->with(['schedules.timeSlots'])
            ->get();

        foreach ($activities as $activity) {
            try {
                if ($this->isActivityExpired($activity, $now)) {
                    $this->updateActivityStatus($activity, 'completed');
                    $updatedCount++;
                    
                    Log::info('活动状态更新为已完成', [
                        'activity_id' => $activity->id,
                        'activity_title' => $activity->title,
                        'updated_at' => $now->toDateTimeString()
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('单个活动完成状态更新失败', [
                    'activity_id' => $activity->id,
                    'activity_title' => $activity->title,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        return $updatedCount;
    }

    /**
     * 批量更新已过期活动的状态（保持向后兼容）
     *
     * @return int 更新的活动数量
     * @throws BizException
     * @deprecated 使用 updateActivityStatuses() 替代
     */
    public function updateExpiredActivities(): int
    {
        $result = $this->updateActivityStatuses();
        return $result['completed'];
    }

    /**
     * 更新单个活动的状态
     *
     * @param Activity $activity
     * @param string $status
     * @return void
     * @throws BizException
     */
    public function updateActivityStatus(Activity $activity, string $status): void
    {
        try {
            DB::beginTransaction();
            
            $activity->update([
                'status' => $status,
                'updated_at' => time()
            ]);
            
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('单个活动状态更新失败', [
                'activity_id' => $activity->id,
                'activity_title' => $activity->title,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            
            BizException::throws(ActivityErrorCode::ACTIVITY_STATUS_UPDATE_FAILED);
        }
    }

    /**
     * 检查活动是否已过期
     *
     * @param Activity $activity
     * @param Carbon $now
     * @return bool
     */
    public function isActivityExpired(Activity $activity, Carbon $now): bool
    {
        // 如果没有时间安排，检查报名截止时间
        if ($activity->schedules === null) {
            return $now->greaterThan(Carbon::parse($activity->registration_deadline));
        }

        // 检查时间安排是否已结束
        return $this->isScheduleExpired($activity->schedules, $now);
    }

    /**
     * 检查时间安排是否已过期
     *
     * @param ActivitySchedule $schedule
     * @param Carbon $now
     * @return bool
     */
    private function isScheduleExpired(ActivitySchedule $schedule, Carbon $now): bool
    {
        $endDate = Carbon::parse($schedule->end_date);
        
        // 如果没有时间段，只检查结束日期
        if ($schedule->timeSlots->isEmpty()) {
            return $now->greaterThan($endDate->endOfDay());
        }

        // 获取最晚的结束时间
        $latestEndTime = $schedule->timeSlots->max('end_time');
        
        // 确保 $latestEndTime 是字符串格式
        if ($latestEndTime instanceof Carbon) {
            $latestEndTime = $latestEndTime->format('H:i:s');
        }
        
        // 组合日期和时间进行比较
        $scheduleEndDateTime = $endDate->copy()->setTimeFromTimeString($latestEndTime);
        
        return $now->greaterThan($scheduleEndDateTime);
    }

    /**
     * 获取即将发布的活动列表（用于提醒）
     *
     * @param int $hours 提前多少小时
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActivitiesPublishingSoon(int $hours = 24): \Illuminate\Database\Eloquent\Collection
    {
        $now = Carbon::now();
        $threshold = $now->copy()->addHours($hours);

        return Activity::where('status', 'created')
            ->where('deleted_at', 0)
            ->where('publish_time', '>', $now->toDateTimeString())
            ->where('publish_time', '<=', $threshold->toDateTimeString())
            ->get();
    }

    /**
     * 获取即将过期的活动列表（用于提醒）
     *
     * @param int $hours 提前多少小时
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActivitiesExpiringSoon(int $hours = 24): \Illuminate\Database\Eloquent\Collection
    {
        $now = Carbon::now();
        $threshold = $now->copy()->addHours($hours);

        return Activity::where('status', 'published')
            ->where('deleted_at', 0)
            ->with(['schedules.timeSlots'])
            ->get()
            ->filter(function ($activity) use ($now, $threshold) {
                return !$this->isActivityExpired($activity, $now) && 
                       $this->isActivityExpired($activity, $threshold);
            });
    }
} 