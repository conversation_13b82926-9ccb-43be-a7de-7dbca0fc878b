<?php

declare(strict_types=1);

namespace Modules\Activity\Services;

use Modules\Activity\Models\ActivityGroup;
use Modules\Activity\Models\ActivityGroupRelation;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;

/**
 * 活动组服务类
 */
class ActivityGroupService
{
    /**
     * 批量保存活动组
     * @param array $groupsData 活动组数据数组
     * @return Collection
     */
    public function save(array $data , ?int $id = null)
    {
        try {
            DB::beginTransaction();
            if ($id) {
                // 更新活动
                $group = ActivityGroup::findOrFail($id);
                $group->update($data);
            } else {
                $group = ActivityGroup::create($data);
            }
            DB::commit();
            return $group;
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('保存群组信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 保存活动组关联关系
     */
    private function saveRelations(ActivityGroup $group, array $relations): void
    {
        // 先删除旧关联
        ActivityGroupRelation::where('group_id', $group->id)->delete();
        
        // 添加新关联
        foreach ($relations as $relation) {
            ActivityGroupRelation::create([
                'group_id' => $group->id,
                'activity_id' => $relation['activity_id'],
                'sort_order' => $relation['sort_order'] ?? 0
            ]);
        }
    }

    /**
     * 获取活动组列表
     *
     * @param array $params 查询参数
     * @return Collection
     */
    public function list(array $params = [], int $per_page = 15)
    {
        $query = ActivityGroup::query();
        // 根据状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 默认按创建时间倒序
        $query->orderBy('created_at', 'desc');

        return $query->paginate($per_page);
    }

    /**
     * 保存活动组关系（创建或更新）
     *
     * @param int $activity_id 活动ID
     * @param array $groups 活动组数据数组
     * @param int $creator_id 创建者ID
     * @return bool
     */
    public function saveGroupRelation(int $activity_id, array $groups, int $creator_id = 0): bool
    {
        return DB::transaction(function() use ($activity_id, $groups, $creator_id) {
            // 先删除不存在的关联
            $existingGroupIds = collect($groups)->pluck('id')->filter()->all();
            ActivityGroupRelation::where('activity_id', $activity_id)
                ->when(!empty($existingGroupIds), function($query) use ($existingGroupIds) {
                    $query->whereNotIn('group_id', $existingGroupIds);
                })
                ->delete();

            // 更新或创建关联
            foreach ($groups as $group) {
                ActivityGroupRelation::updateOrCreate([
                    'activity_id' => $activity_id,
                    'group_id' => $group['id']
                ], [
                    'register_start_time' => $group['start_time'] ?? null,
                    'register_end_time' => $group['end_time'] ?? null,
                    'creator_id' => $creator_id,
                    'sort_order' => $group['sort_order'] ?? 0
                ]);
            }
            
            return true;
        });
    }

    /**
     * 删除活动组
     *
     * @param int $id 活动组ID
     * @return bool
     */
    public function delete(int $id): bool
    {
        $activityGroup = ActivityGroup::findOrFail($id);
        return $activityGroup->delete();
    }

    /**
     * 获取活动组详情
     *
     * @param int $id 活动组ID
     * @return ActivityGroup
     */
    public function detail(int $id): ActivityGroup
    {
        return ActivityGroup::findOrFail($id);
    }

    /**
     * 批量删除活动组
     *
     * @param array $ids 活动组ID数组
     * @return bool
     */
    public function batchDelete(array $ids): bool
    {
        return ActivityGroup::whereIn('id', $ids)->delete();
    }

    /**
     * 更新活动组状态
     *
     * @param int $id 活动组ID
     * @param int $status 状态值
     * @return bool
     */
    public function updateStatus(int $id, int $status): bool
    {
        $activityGroup = ActivityGroup::findOrFail($id);
        return $activityGroup->update(['status' => $status]);
    }
}
