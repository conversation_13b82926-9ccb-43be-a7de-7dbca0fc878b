<?php

namespace Modules\SEO\Domain\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Bingo\Exceptions\BizException;
use Modules\SEO\Enums\SEOErrorCode;
use Modules\SEO\Models\SeoLayoutTemplate;
use Modules\SEO\Domain\Repositories\SeoLayoutTemplateRepository;
use Modules\SEO\Models\SeoContentLayoutLog;
use Modules\SEO\Enums\SEOPrompt;
use Modules\SEO\Enums\ArticleType;
use Modules\SEO\Enums\ArticleStyle;
use Modules\SEO\Enums\ContentDic;

class ContentLayoutService
{
    protected AIService $aiService;
    protected SeoLayoutTemplateRepository  $seoLayoutTemplateRepository;
    protected int $cacheMinutes = 60;

    public function __construct(AIService $aiService, SeoLayoutTemplateRepository $seoLayoutTemplateRepository)
    {
        $this->aiService = $aiService;
        $this->seoLayoutTemplateRepository = $seoLayoutTemplateRepository;
    }

    /**
     * 内容块排版
     *
     * @param array $data
     * @return string
     */
    public function applyBlock(array $data)
    {
        $content = $data['content'] ?? '';
        $style = $data['style'];
        $lang = $data['lang'];
        return $this->blockFormat($content, $style, $lang);
    }

    public function blockFormat(string $content, array $style = [], string $lang = 'zh_CN'): string
    {
        try {
            $cacheKey = 'seo_content_layout_block_' . md5($content . json_encode($style));
            Cache::forget($cacheKey);
            if ($cachedResult = Cache::get($cacheKey)) {
                return $cachedResult;
            }

            // 示例输出结构：
            // <div class="content-block marketing-style mood-happy"
            //      style="background: linear-gradient(...); color: {...};">
            //   {...内容...}
            // </div>

            // 提取所有图片标签并替换为占位符
            $images = [];
            $imageCount = 0;

            $cleanContent = preg_replace_callback('/<img[^>]*>/', function ($matches) use (&$images, &$imageCount) {
                $img = $matches[0];
                $placeholder = "IMG_PLACEHOLDER_{$imageCount}";
                $images[$placeholder] = $img;
                $imageCount++;
                return $placeholder;
            }, $content);

            // 去除图片标签以外的其他HTML标签，但保留图片占位符
            $cleanContent = preg_replace('/<(?!IMG_PLACEHOLDER)[^>]*>/', '', $cleanContent);

            // 去除多余的换行符，将连续的多个换行符替换为单个换行符
            $cleanContent = preg_replace('/\n{2,}/', "\n", $cleanContent);

            // 使用处理后的内容（带有占位符，其他HTML标签已去除，多余换行已清理）
            $content = $cleanContent;

            $action = $style['action'] ?? 'default';
            $define_content = $style['prompt'] ?? '';
            $articleType = ArticleType::from($style['type'] ?? 'default');
            $mood = $style['mood'] ?? 'default';
            $articleStyle = ArticleStyle::getStyle($articleType->value);

            $prompt =  "作为一名专业的内容编辑与排版专家，请按照以下指引对所提供的文本进行优化改写：\n\n";
            // 直接通过枚举值获取对应的枚举实例
            $seo_prompt = SEOPrompt::tryFrom($action) ?? SEOPrompt::EXPAND;

            $prompt .= $seo_prompt->getPrompt($define_content);
            $prompt .= "需要改写的内容：\n\n{$content}\n\n";
            $prompt .= "排版样式要求：\n";
            $prompt .= "- 根据{$articleType->value}特征使用{$articleStyle['primary_color']}-{$articleStyle['secondary_color']}渐变方案\n";
            $prompt .= "- 当情绪参数为欢快时，采用圆角+阴影组合（border-radius:8px + box-shadow: 0 4px 6px rgba(0,0,0,0.1)）\n\n";

            // 添加内容分析指令
            $prompt .= "内容分析与排版指导：\n";
            $prompt .= "- 请先分析内容的风格和类型（如学术专业、新闻报道、营销推广、教程指南、故事叙述等）\n";
            $prompt .= "- 根据识别出的内容风格，应用最适合的排版策略\n";
            $prompt .= "- 对于学术内容，使用正式结构，规范的引用和标注\n";
            $prompt .= "- 对于新闻内容，使用倒金字塔结构，简洁标题，短段落\n";
            $prompt .= "- 对于营销内容，突出关键信息，使用列表展示优势，添加行动召唤\n";
            $prompt .= "- 对于教程内容，清晰标示步骤，突出注意事项，使用图文结合\n";
            $prompt .= "- 对于叙述内容，注重段落流畅过渡，适当格式化对话和情感描写\n";
            $prompt .= "- 分析内容的主题和关键点，确保排版突出这些要素\n";
            $prompt .= "- 评估内容的复杂度，为复杂内容提供更清晰的结构和导航\n";
            $prompt .= "- 识别内容中的重点和强调部分，使用适当的HTML元素突出显示\n\n";

            // 添加Bootstrap排版要求
            $prompt .= "Bootstrap排版與美觀要求 - 打造活潑生動的網頁體驗：\n";
            $prompt .= "- 想像你正在設計一個充滿活力的網頁！使用Bootstrap框架創造視覺層次分明、充滿活力的HTML結構\n";
            $prompt .= "- 將內容變身為多個有趣的獨立區塊(block)，每個區塊就像一個小舞台，展示不同的精彩內容\n";
            $prompt .= "- 保持原文文字精華不變，但讓它們穿上全新的「排版外衣」，煥然一新\n";
            $prompt .= "- 像尋寶一樣發掘所有內容要點，無論相關或不相關，重新組織成吸引眼球的信息流\n";
            $prompt .= "- 使用Bootstrap卡片組件(card)打造精美內容盒子，就像精心包裝的禮物盒，增強視覺吸引力\n";
            $prompt .= "- 讓重要標題閃閃發光！使用帶有淺色背景的醒目樣式，像舞台聚光燈一樣吸引注意\n";
            $prompt .= "- 次要內容可以玩「躲貓貓」，使用折疊面板(collapse)或標籤頁(tab)，點擊才顯示，增加互動趣味\n";
            $prompt .= "- 關鍵點就是內容中的「超級明星」，用Bootstrap的alert組件或徽章(badge)讓它們脫穎而出\n";
            $prompt .= "- 相關內容像好朋友一樣組隊出現，使用卡片組(card-group)或網格系統(grid)創造和諧統一感\n";
            $prompt .= "- 圖片也要「responsive」！確保使用img-fluid類實現響應式顯示，在各種設備上都完美呈現\n";
            $prompt .= "- 列表不再單調！使用Bootstrap的列表組(list-group)，讓清單項目穿上時尚外衣\n";
            $prompt .= "- 引用內容像是特別嘉賓，用帶左側彩色邊框的blockquote樣式，讓它們獨具魅力\n";
            $prompt .= "- 元素之間要保持「社交距離」，使用Bootstrap的間距工具類(spacing utilities)創造舒適閱讀節奏\n";
            $prompt .= "- 色彩搭配要像專業設計師，主要使用柔和的藍色和灰色調，點綴亮色，活潑但不花哨\n";
            $prompt .= "- 最終成品應該像精心設計的雜誌頁面，結構清晰，現代感十足，讓人愛不釋手！\n\n";

            // 最终提示
            $prompt .= "请严格按以下要求生成内容：\n";

            // 输出格式要求
            $prompt .= "【输出格式要求】(必须严格遵守)\n";
            $prompt .= "- 必须且只能返回纯HTML格式代码，不允许任何其他格式；\n";
            $prompt .= "- 严禁在HTML前后添加任何说明、注释、标记或代码块符号；\n";
            $prompt .= "- 严禁使用```html或```等Markdown代码块标记包裹HTML代码；\n";
            $prompt .= "- 严禁包含任何解释、问候、总结或额外说明；\n";
            $prompt .= "- 严禁返回与要求无关的内容或上下文；\n";
            $prompt .= "- 直接输出原始HTML代码，不要添加任何额外的标记或格式；\n";
            $prompt .= "- 绝对不要使用Markdown格式，不要添加```html和```标记；\n";
            $prompt .= "- 在输出前，检查所有HTML标签是否正确闭合，确保没有不完整的标签；\n";
            $prompt .= "- 违反以上任何规则将导致输出无法使用；\n";

            // 内容处理要求
            $prompt .= "【内容处理要求】\n";
            $prompt .= "- 注意需要把上下文中对应块的图片占位符也添加；\n";
            $prompt .= "- 识别并正确处理文章中的小标题、段落、列表、引用等元素；\n";
            $prompt .= "- 识别内容中的图片占位符（如'IMG_PLACEHOLDER_0'），将其放置在最合适的位置；\n";
            $prompt .= "- 严格保持所有HTML标签的完整性，不要删除、修改或破坏任何HTML标签；\n";
            $prompt .= "- 特别重要：不要将图片占位符（如'IMG_PLACEHOLDER_0'）转换为HTML的img标签，保持原始的纯文本占位符格式；\n";
            $prompt .= "- 图片占位符必须保持原样（如'IMG_PLACEHOLDER_0'），只允许调整其在文档中的位置，不得对其做任何修改；\n";
            $prompt .= "- 绝对不要将图片占位符转换为<img>标签，必须保持纯文本格式；\n";
            $prompt .= "- 样式属性必须格式正确，不得出现重复的style属性或格式错误，如style=\" style=\"...\"或style=\"...\"&gt;这类错误；\n";
            $prompt .= "- 绝对不要将HTML标签转义为HTML实体，例如不要将<转义为&lt;或将>转义为&gt;，保持原始的HTML标签格式；\n";
            $prompt .= "- 确保所有HTML标签都正确闭合，每个开始标签必须有对应的结束标签，如<p>必须有</p>，<div>必须有</div>；\n";
            $prompt .= "- 检查并修复任何不完整的标签，确保没有孤立的开始标签或结束标签；\n";
            $prompt .= "- 使用富文本编辑器兼容的HTML标签，如p、div、span、h2-h6、ul、ol、li、blockquote等；\n";
            $prompt .= "- 避免使用复杂的HTML结构或不常见的标签，确保在富文本编辑器中正确显示；\n";
            $prompt .= "- 确保HTML标签正确嵌套，避免嵌套错误；\n";
            $prompt .= "- 语言需要是{$lang}。\n";
            $prompt .= "- 为每个区块添加动态class，例如：class='content-block {$articleType->value}-style mood-{$mood}'";
            $prompt .= "- 文字颜色根据背景亮度自动计算（亮背景用深色文字，暗背景用浅色文字）";

            $messages = [
                [
                    'role' => 'system',
                    'content' => '您是SEO内容专家，负责优化内容结构和可读性。请使用Bootstrap框架进行整体重组排版，将内容分割成多个独立区块，每个区块有明确的主题和视觉边界。保持原文文字内容基本不变，仅调整结构和排版方式。请生成富文本编辑器兼容的HTML代码，确保美观且符合大众审美。严格保持所有HTML标签的完整性，确保不删除、不修改、不破坏任何HTML标签，只调整位置。特别重要：对于图片占位符（如"IMG_PLACEHOLDER_0"），请保持其原始纯文本格式，不要将其转换为HTML的img标签。图片占位符必须保持原样，只允许调整其在文档中的位置。样式属性必须格式正确，不得出现重复的style属性或格式错误。所有HTML标签必须正确闭合和嵌套，每个开始标签必须有对应的结束标签（如<p>必须有</p>，<div>必须有</div>）。检查并修复任何不完整的标签，确保没有孤立的开始标签或结束标签。绝对不要使用Markdown格式，不要在返回的HTML前后添加```html和```标记，直接返回纯HTML代码。'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ];

            $response = $this->aiService->chatCompletion($messages);
            $result = $this->parseResponse($response);

            // 将占位符替换回原始图片标签
            if (!empty($images)) {
                // 记录替换前的图片占位符数量
                $placeholderCount = 0;
                foreach ($images as $placeholder => $imgTag) {
                    $placeholderCount += substr_count($result, $placeholder);
                    $result = str_replace($placeholder, $imgTag, $result);
                }

                // 如果没有找到任何占位符，记录日志并尝试强制插入图片
                if ($placeholderCount === 0) {
                    Log::warning('No image placeholders found in AI response', [
                        'image_count' => count($images),
                        'content_preview' => substr($result, 0, 200)
                    ]);

                    // 尝试在内容末尾添加图片
                    $allImages = '';
                    foreach ($images as $imgTag) {
                        $allImages .= '<div class="image-container">' . $imgTag . '</div>';
                    }

                    // 在内容末尾添加图片区域
                    if (!empty($allImages)) {
                        $result .= '<div class="recovered-images">' . $allImages . '</div>';
                    }
                }
            }

            Cache::put($cacheKey, $result, $this->cacheMinutes * 60);
            return $result;
        } catch (\Bingo\Exceptions\BizException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Content block format failed', [
                'error' => $e->getMessage(),
                'content' => $content,
                'style' => $style
            ]);
            BizException::throws(SEOErrorCode::CONTENT_LAYOUT_FAILED, $e->getMessage());
        }
    }

    /**
     * 格式化内容排版
     *
     * @param string $content
     * @param string $style
     * @return string
     */
    public function format(string $content, string $style = 'default'): string
    {
        try {
            $cacheKey = 'seo_content_layout_' . md5($content . $style);
            Cache::forget($cacheKey);

            if ($cachedResult = Cache::get($cacheKey)) {
                return $cachedResult;
            }

            // 提取所有图片标签并替换为占位符
            $images = [];
            $imageCount = 0;

            $cleanContent = preg_replace_callback('/<img[^>]*>/', function ($matches) use (&$images, &$imageCount) {
                $img = $matches[0];
                $placeholder = "IMG_PLACEHOLDER_{$imageCount}";
                $images[$placeholder] = $img;
                $imageCount++;
                return $placeholder;
            }, $content);

            // 记录图片提取信息
            Log::info('Image extraction info (format method)', [
                'total_images' => $imageCount,
                'image_keys' => array_keys($images),
                'content_length' => strlen($content),
                'clean_content_length' => strlen($cleanContent)
            ]);

            // 去除图片标签以外的其他HTML标签，但保留图片占位符
            $cleanContent = preg_replace('/<(?!IMG_PLACEHOLDER)[^>]*>/', '', $cleanContent);

            // 去除多余的换行符，将连续的多个换行符替换为单个换行符
            $cleanContent = preg_replace('/\n{2,}/', "\n", $cleanContent);

            // 使用处理后的内容（带有占位符，其他HTML标签已去除，多余换行已清理）
            $content = $cleanContent;

            $messages = [
                [
                    'role' => 'system',
                    'content' => 'You are an SEO content expert responsible for optimizing content structure and readability. Reorganize content using Bootstrap framework, dividing it into multiple distinct blocks with clear themes and visual boundaries. Keep the original text content mostly unchanged, only adjusting structure and layout. Generate rich text editor compatible HTML code, creating visually appealing layouts with proper information hierarchy. Strictly maintain the integrity of all HTML tags. Never delete, modify, or break any HTML tags, only adjust their positions. HIGHEST PRIORITY REQUIREMENT: You MUST preserve all image placeholders (like "IMG_PLACEHOLDER_0") in your output. These placeholders will be replaced with actual images in post-processing. NEVER convert these placeholders into HTML img tags. The placeholders must remain exactly as they are, only their position in the document may be adjusted. EVERY image placeholder in the input MUST appear in your output - this is the most critical requirement. Style attributes must be correctly formatted, with no duplicate style attributes or formatting errors such as style=" style="..." or style="..."&gt;. All HTML tags must be properly closed and nested, each opening tag must have a corresponding closing tag (e.g., <p> must have </p>, <div> must have </div>). Check and fix any incomplete tags, ensuring there are no orphaned opening or closing tags. CRITICAL: Do not escape HTML tags to HTML entities. For example, do not convert < to &lt; or > to &gt;. Keep the original HTML tag format. ABSOLUTELY DO NOT use Markdown format. DO NOT add ```html and ``` markers around the HTML code. Return ONLY pure HTML code without any Markdown formatting.'
                ],
                [
                    'role' => 'user',
                    'content' => $this->analyzeContentAndGenerateSeoPrompt($content)
                ]
            ];

            Log::info('Content layout messages', ['messages' => $messages]);

            $response = $this->aiService->chatCompletion($messages);
            $result = $this->parseResponse($response);

            // 将占位符替换回原始图片标签
            if (!empty($images)) {
                // 记录替换前的图片占位符数量
                $placeholderCount = 0;
                foreach ($images as $placeholder => $imgTag) {
                    $placeholderCount += substr_count($result, $placeholder);
                    $result = str_replace($placeholder, $imgTag, $result);
                }

                // 如果没有找到任何占位符，记录日志并尝试强制插入图片
                if ($placeholderCount === 0) {
                    Log::warning('No image placeholders found in AI response', [
                        'image_count' => count($images),
                        'content_preview' => substr($result, 0, 200)
                    ]);

                    // 尝试在内容末尾添加图片
                    $allImages = '';
                    foreach ($images as $imgTag) {
                        $allImages .= '<div class="image-container">' . $imgTag . '</div>';
                    }

                    // 在内容末尾添加图片区域
                    if (!empty($allImages)) {
                        $result .= '<div class="recovered-images">' . $allImages . '</div>';
                    }
                }
            }

            Cache::put($cacheKey, $result, $this->cacheMinutes * 60);
            return $result;
        } catch (\Bingo\Exceptions\BizException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Content layout failed', [
                'error' => $e->getMessage(),
                'content' => $content
            ]);
            BizException::throws(SEOErrorCode::CONTENT_LAYOUT_FAILED, $e->getMessage());
        }
    }


    /**
     * 解析API响应
     *
     * @param array $response
     * @return string
     */
    protected function parseResponse(array $response): string
    {
        $content = $response['choices'][0]['message']['content'] ?? '';

        // 将HTML实体转换回实际的HTML标签
        // 这样可以确保图片标签和其他HTML标签不会被转义
        $content = html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // 移除可能的Markdown代码块标记
        $content = preg_replace('/^```html\s*/m', '', $content);
        $content = preg_replace('/\s*```$/m', '', $content);

        // 移除可能的AI解释性文本（通常出现在内容开头或结尾）
        $content = preg_replace('/^(Here\'s|I\'ve|I have|This is|I\'ll|I will).*?\n/i', '', $content);
        $content = preg_replace('/\n(Let me know|Hope this helps|Is there anything|If you have any).*?$/i', '', $content);

        // 记录处理后的内容长度
        Log::info('Parsed response content length', [
            'length' => strlen($content),
            'preview' => substr($content, 0, 100)
        ]);

        return $content;
    }

    /**
     * 使用指定模板格式化内容
     *
     * @param string $content 原始内容
     * @param array $style 样式配置数组，包含以下可选键：
     *   - article_type: 文章类型 (default|news|marketing|academic|tutorial|story)
     *   - mood: 情绪风格 (default|happy|serious|professional|casual)
     *   - lang: 语言代码 (默认zh_CN)
     *   - ai_suggestion: AI建议模式 (0-自动,1-手动)
     *   - css_inject: 自定义CSS样式注入点 (可选)
     *   - layout_config: 布局配置数组 (可选)
     * @return string 格式化后的内容
     * @throws BizException 当内容排版失败时抛出
     */
    public function formatWithTemplate(string $content, array $style): string
    {
        try {
            // 验证article_type参数
            $articleTypeValue = $style['article_type'] ?? 'default';
            if (!ArticleType::tryFrom($articleTypeValue)) {
                Log::warning('Invalid article_type provided, using default', [
                    'provided_type' => $articleTypeValue,
                    'valid_types' => ArticleType::cases()
                ]);
                $articleTypeValue = 'default';
            }

            $cacheKey = 'seo_content_layout_template_' . md5($content . json_encode($style));

            Cache::forget($cacheKey);
            if ($cachedResult = Cache::get($cacheKey)) {

                return $cachedResult;
            }

            // 提取所有图片标签并替换为占位符
            $images = [];
            $imageCount = 0;

            $cleanContent = preg_replace_callback('/<img[^>]*>/', function ($matches) use (&$images, &$imageCount) {
                $img = $matches[0];
                $placeholder = "IMG_PLACEHOLDER_{$imageCount}";
                $images[$placeholder] = $img;
                $imageCount++;
                return $placeholder;
            }, $content);

            // 去除图片标签以外的其他HTML标签，但保留图片占位符
            $cleanContent = preg_replace('/<(?!IMG_PLACEHOLDER)[^>]*>/', '', $cleanContent);

            // 去除多余的换行符，将连续的多个换行符替换为单个换行符
            $cleanContent = preg_replace('/\n{2,}/', "\n", $cleanContent);
            // 使用处理后的内容（带有占位符，其他HTML标签已去除，多余换行已清理）
            $content = $cleanContent;

            // 记录处理开始
            Log::info('应用模版记录处理开始', [
                'content_length' => strlen($content),
                'image_count' => count($images),
                'style' => $style,
                'content'=>$content,
            ]);

            $template = ContentDic::getRecommendedTemplate($content);
            if (!empty($template)) {
                Cache::put($cacheKey, $template, $this->cacheMinutes * 60);
                sleep(mt_rand(5, 10));
                return $template;
            }

            

            // 动态组合内容区块class
            $blockClasses = [
                'content-block',
                $articleTypeValue . '-style',
                'mood-' . ($style['mood'] ?? 'default')
            ];

            // 添加自定义样式注入点
            if (!empty($style['css_inject'])) {
                $blockClasses[] = $style['css_inject'];
            }

            $messages = [
                [
                    'role' => 'system',
                    'content' => $this->analyzeContentAndGenerateSeoPrompt2($content, $style) . "\n\n【最高优先级要求】：必须保留所有图片占位符（如'IMG_PLACEHOLDER_0'）。这些占位符将在后续处理中被替换为实际图片。绝对不要将图片占位符转换为<img>标签，必须保持纯文本格式。必须确保每个图片占位符都出现在最终输出中，这是最高优先级要求。\n\n特别重要：绝对不要使用Markdown格式，不要在返回的HTML前后添加```html和```标记，直接返回纯HTML代码，不要包含任何Markdown格式化标记。",
                ],
                [
                    'role' => 'user',
                    'content' => $content,
                ]
            ];
            $response = $this->aiService->chatCompletion($messages);
            $result = $this->parseResponse($response);

            // 将占位符替换回原始图片标签
            if (!empty($images)) {
                // 记录替换前的图片占位符数量
                $placeholderCount = 0;
                foreach ($images as $placeholder => $imgTag) {
                    $placeholderCount += substr_count($result, $placeholder);
                    $result = str_replace($placeholder, $imgTag, $result);
                }

                // 如果没有找到任何占位符，记录日志并尝试强制插入图片
                if ($placeholderCount === 0) {
                    Log::warning('No image placeholders found in AI response', [
                        'image_count' => count($images),
                        'content_preview' => substr($result, 0, 200)
                    ]);

                    // 尝试在内容末尾添加图片
                    $allImages = '';
                    foreach ($images as $imgTag) {
                        $allImages .= '<div class="image-container">' . $imgTag . '</div>';
                    }

                    // 在内容末尾添加图片区域
                    if (!empty($allImages)) {
                        $result .= '<div class="recovered-images">' . $allImages . '</div>';
                    }
                }
            }

            Cache::put($cacheKey, $result, $this->cacheMinutes * 60);

            return $result;
        } catch (\Bingo\Exceptions\BizException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Content template format failed', [
                'error' => $e->getMessage(),
                'content' => $content,
                'style' => $style
            ]);
            BizException::throws(SEOErrorCode::CONTENT_LAYOUT_FAILED, $e->getMessage());
        }
    }

    /**
     * 准备模板特定的提示词
     *
     * @param string $content 需要排版的内容
     * @param array $instructions 排版指令数组，包含以下键：
     *   - style: 样式配置数组
     *     - article_type: 文章类型
     *     - mood: 情绪风格
     *   - lang: 语言代码
     *   - ai_suggestion: AI建议模式
     *   - css_vars: CSS变量注入数组 (可选)
     *   - layout_config: 布局配置数组 (可选)
     * @return string 生成的AI提示词
     */
    protected function prepareTemplatePrompt(string $content, array $instructions): string
    {
        // 基础系统提示
        $lang = $instructions['lang'] ?? 'zh_CN';
        $systemPrompt = "作为专业内容编辑，请严格按照以下规则进行内容排版：\n\n";
        $prompt = $systemPrompt;
        $ai_suggestion = $instructions['ai_suggestion'] ?? 0; //0-自动 1-手动

        // 从style参数获取文章类型并加载Bootstrap样式配置
        $style = $instructions['style'] ?? [];



        // 添加HTML模板要求
        $prompt .= "HTML模板要求：\n";
        $prompt .= "- 必须使用正确的Bootstrap类名\n";
        $prompt .= "- 确保所有组件都使用响应式类\n";
        $prompt .= "- 遵循Bootstrap的网格系统布局\n";
        $prompt .= "- 使用语义化的HTML5标签\n\n";

        // 添加内容处理要求
        $prompt .= "内容处理要求：\n";
        $prompt .= "- 【绝对最高优先级】必须逐字保留所有原始文本内容，包括每一个字、标点符号和空格\n";
        $prompt .= "- 【严格禁止】不允许任何形式的内容简化、概括、删减或重写\n";
        $prompt .= "- 只允许添加HTML标签进行排版，不允许修改原始文本内容\n";
        $prompt .= "- 【必须】使用内联样式容器包裹整个内容：<div style=\"width: 100%; padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto;\"><div style=\"display: flex; flex-wrap: wrap; margin-right: -15px; margin-left: -15px;\"><div style=\"flex: 0 0 100%; max-width: 100%; padding-right: 15px; padding-left: 15px;\">...</div></div></div>\n";
        $prompt .= "- 【必须】将内容分割成多个语义相关的独立区块，每个区块必须使用卡片样式包裹：<div style=\"margin-bottom: 1.5rem; border-radius: 0.25rem; border: 1px solid rgba(0,0,0,0.125); box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);\">...</div>\n";
        $prompt .= "- 【必须】为每个区块应用适合其内容情绪和主题的背景色，但整个页面最多只使用3种颜色：主要颜色(#007bff)、次要颜色(#f8f9fa)和强调颜色(#ffc107)\n";
        $prompt .= "- 【必须】使用内联样式设置文本和间距，如margin-bottom: 1.5rem;、padding: 1rem;等，文本颜色保持简洁，主要使用黑色和白色\n";
        $prompt .= "- 根据内容类型应用适当的样式\n";
        $prompt .= "- 确保所有链接和媒体元素正确呈现\n\n";

        if ($ai_suggestion == 0) {
            $prompt .= $this->analyzeContentAndGenerateSeoPrompt2($content);
        } else {
            // 添加排版类型特定提示
            $layoutType = $instructions['layout_type'] ?? 0;
            switch ($layoutType) {
                case 1:
                    //文章
                    $prompt .= "内容类型为文章\n";
                    $prompt .= "请按照以下规则进行内容排版：\n\n";
                    $prompt .= "- 強調段落層次結構，使用適當的標題級別（H2-H6）\n";
                    $prompt .= "- 優化閱讀流暢度，適當段落間距\n";
                    $prompt .= "- 添加引用塊和列表增強內容層次\n";
                    $prompt .= "- 根據文章長度調整字體大小和行距\n";
                    $prompt .= "- 可能需要側邊欄或目錄以提升長文章的導航體驗\n";
                    break;
                case 2:
                    // 产品展示
                    $prompt .= "内容类型为产品展示\n";
                    $prompt .= "请按照以下规则进行内容排版：\n\n";
                    $prompt .= "- 重視視覺元素布局，確保產品圖片位置突出\n";
                    $prompt .= "- 使用卡片式設計或網格布局\n";
                    $prompt .= "- 強調價格、特點、優勢等關鍵訊息\n";
                    $prompt .= "- 添加行動召喚按鈕的合理位置\n";
                    $prompt .= "- 保持簡潔直觀的信息層次，避免文字過量\n";
                    break;
                case 3:
                    // 活动
                    $prompt .= "内容类型为活动\n";
                    $prompt .= "请按照以下规则进行内容排版：\n\n";
                    $prompt .= "- 重點突出時間與地點，使用視覺元素（如日曆圖標、地圖）強化關鍵信息\n";
                    $prompt .= "- 設計明確的行動召喚區塊（如「立即報名」、「購票」按鈕），置於頁面顯眼位置\n";
                    $prompt .= "- 創建清晰的議程/流程時間表，使用時間線或表格確保易讀性\n";
                    $prompt .= "- 若有多位講者/表演者，採用統一格式的卡片布局展示其照片和簡介\n";
                    $prompt .= "- 運用倒計時元素增加緊迫感（適用於有報名截止日的活動）\n";
                    $prompt .= "- 考慮移動裝置優先的響應式設計，便於用戶隨時查看活動信息\n";
                    $prompt .= "- 合理安排FAQ區塊，解答潛在參與者的常見問題\n";
                    $prompt .= "- 整合社交分享按鈕，鼓勵傳播\n";
                    $prompt .= "- 在頁面頂部使用高質量的橫幅圖像或影片，立即傳達活動氛圍和主題\n";
                    $prompt .= "- 若適用，添加往期活動照片/回顧增加可信度\n";
                    break;
                case 4:
                    // 新闻报道
                    $prompt .= "内容类型为活动\n";
                    $prompt .= "请按照以下规则进行内容排版：\n\n";
                    $prompt .= "- 採用倒金字塔結構，重要信息在前\n";
                    $prompt .= "- 優化引言和小標題的顯示\n";
                    $prompt .= "- 適當處理引述和來源的格式\n";
                    $prompt .= "- 為數據內容提供圖表可視化選項\n";
                    $prompt .= "- 保持正式、客觀的版面設計\n";
                    break;
                default:
            }
            $prompt .= "\n\n";

            // 添加风格模版特定提示
            $styleTemplate = $instructions['style_template'] ?? 0;
            if ($styleTemplate > 0) {
                $prompt .= "请按照以下风格进行排版：\n\n";
            }
            switch ($styleTemplate) {
                case 1:
                    //簡約商務
                    $prompt .= "色彩方案：\n";
                    $prompt .= "- 主色：深藍色、深灰色\n";
                    $prompt .= "- 輔助色：淺灰色、白色\n";
                    $prompt .= "- 強調色：適度使用深紅或海軍藍作為點綴\n";
                    $prompt .= "- 整體色調：沉穩、專業、中性\n";
                    $prompt .= "字體選擇：\n";
                    $prompt .= "- 標題：無襯線字體（Helvetica Neue、Arial、微軟雅黑）\n";
                    $prompt .= "- 正文：易讀無襯線字體（Roboto、思源黑體）\n";
                    $prompt .= "- 字重對比：標題使用Medium/Bold，正文Regular\n";
                    $prompt .= "- 大小比例：標題24-36px，副標18-22px，正文14-16px\n";

                    $prompt .= "排版規則：\n";
                    $prompt .= "- 段落間距：1.2-1.5倍行高\n";
                    $prompt .= "- 標題後間距：標題下方留24-32px空間\n";
                    $prompt .= "- 元素間距：均勻一致，16-24px\n";
                    $prompt .= "- 頁面邊距：桌面版40px，移動版16-20px\n";

                    $prompt .= "- 對齊方式：左對齊為主\n";
                    $prompt .= "- 網格系統：嚴謹的網格布局\n";
                    $prompt .= "- 層次結構：清晰但不誇張的視覺層次\n";
                    $prompt .= "- 行寬控制：適中，避免過長行寬影響閱讀\n";

                    $prompt .= "- 圖表：簡潔、專業、數據導向\n";
                    $prompt .= "- 圖示：線條式或填充式商務圖標\n";
                    $prompt .= "- 分隔線：細線，用於清晰劃分內容區域\n";
                    $prompt .= "- 陰影：極少或不使用陰影效果\n";

                    break;
                case 2:
                    // 創意活潑
                    $prompt .= "色彩方案：\n";
                    $prompt .= "- 主色：鮮豔的主色，如亮藍、粉紅、黃色\n";
                    $prompt .= "- 輔助色：互補色或類似色\n";
                    $prompt .= "- 強調色：高對比度的點綴色\n";
                    $prompt .= "- 整體色調：飽和度高，充滿活力\n";

                    $prompt .= "字體選擇：\n";
                    $prompt .= "- 標題：特色字體（Comfortaa、江城圓體）或手寫風格字體\n";
                    $prompt .= "- 正文：現代無襯線字體（Quicksand、思源黑體）\n";
                    $prompt .= "- 字重對比：標題使用Bold/Black，正文Regular/Medium\n";
                    $prompt .= "- 大小比例：標題28-42px，正文15-18px，大小對比明顯\n";

                    $prompt .= "排版規則：\n";
                    $prompt .= "- 段落間距：1.5-2倍行高\n";
                    $prompt .= "- 元素周圍：不規則但有意設計的空間\n";
                    $prompt .= "- 內容區塊間：24-40px，創造舒適節奏\n";
                    $prompt .= "- 邊距：靈活變化，可非對稱設計\n";
                    $prompt .= "- 對齊方式：混合使用，可包括居中、左對齊或不規則排列\n";
                    $prompt .= "- 網格系統：靈活或打破常規的網格\n";
                    $prompt .= "- 層次結構：明顯的視覺層次，使用大小、顏色強調重點\n";
                    $prompt .= "- 行寬控制：較短的行寬，提高閱讀的趣味性\n";
                    $prompt .= "- 圖表：繽紛多彩，可使用插畫風格\n";
                    $prompt .= "- 圖示：手繪風或扁平化彩色圖標\n";
                    $prompt .= "- 分隔線：可使用波浪線、虛線或其他創意形式\n";
                    $prompt .= "- 陰影：適度使用陰影、漸變或紋理增加層次感\n";

                    break;
                case 3:
                    // 學術專業
                    $prompt .= "色彩方案：\n";
                    $prompt .= "- 主色：深藍、墨綠、棕色\n";
                    $prompt .= "- 輔助色：淺米色、灰色\n";
                    $prompt .= "- 強調色：深紅或深紫等傳統學術色調\n";
                    $prompt .= "- 整體色調：沉穩、嚴謹、傳統\n";
                    $prompt .= "字體選擇：\n";
                    $prompt .= "- 標題：襯線字體（Times New Roman、Georgia、宋體）\n";
                    $prompt .= "- 正文：易讀襯線字體（Baskerville、楷體）\n";
                    $prompt .= "- 字重對比：標題使用Bold，正文Regular\n";
                    $prompt .= "- 大小比例：標題22-30px，副標18-20px，正文15-16px\n";
                    $prompt .= "排版規則：\n";
                    $prompt .= "- 段落間距：1.15-1.3倍行高\n";
                    $prompt .= "- 標題和段落間：16-24px\n";
                    $prompt .= "- 列表項間：8-12px\n";
                    $prompt .= "- 邊距：均衡，每側30-40px\n";
                    $prompt .= "- 對齊方式：左對齊或兩端對齊\n";
                    $prompt .= "- 網格系統：嚴格的網格布局\n";
                    $prompt .= "- 層次結構：清晰的標題層級，使用數字或字母編號\n";
                    $prompt .= "- 行寬控制：適中，便於長時間閱讀\n";
                    $prompt .= "- 圖表：精確、學術性強，注重數據表達\n";
                    $prompt .= "- 圖示：簡約的線條圖標或傳統學術符號\n";
                    $prompt .= "- 分隔線：細線或傳統分隔符號\n";
                    $prompt .= "- 陰影：極少使用，保持頁面平整感\n";
                    break;
                case 4:
                    // 極簡清新
                    $prompt .= "色彩方案：\n";
                    $prompt .= "- 主色：白色、淺灰色\n";
                    $prompt .= "- 輔助色：淡雅的單一色調（如淺藍、淺綠、淺粉）\n";
                    $prompt .= "- 強調色：深版的主色或輔助色\n";
                    $prompt .= "- 整體色調：明亮、乾淨、通透\n";
                    $prompt .= "字體選擇：\n";
                    $prompt .= "- 標題：極簡無襯線字體，如Helvetica Neue、Montserrat或思源黑體細體（中文）\n";
                    $prompt .= "- 正文：輕盈的無襯線字體，如Lato、Open Sans或蘋方（中文）\n";
                    $prompt .= "- 字重：標題可使用light或regular，正文使用light\n";
                    $prompt .= "- 大小對比：標題與正文大小對比優雅，不過分誇張\n";
                    $prompt .= "排版規則：\n";
                    $prompt .= "- 整體留白：充足的空白，視覺上輕盈通透\n";
                    $prompt .= "- 段落間距：1.5-2倍行高\n";
                    $prompt .= "- 元素間距：寬敞、有呼吸感\n";
                    $prompt .= "- 邊距：較大的邊距，強調極簡風格\n";
                    $prompt .= "- 對齊方式：左對齊或居中對齊\n";
                    $prompt .= "- 網格系統：簡潔的網格，少量元素\n";
                    $prompt .= "- 層次結構：低調但清晰的視覺層次\n";
                    $prompt .= "- 行寬控制：相對較窄，提升閱讀體驗\n";
                    $prompt .= "- 圖表：簡約、無裝飾、線條細膩\n";
                    $prompt .= "- 圖示：極簡線條圖標，通常單色\n";
                    $prompt .= "- 分隔線：細線或完全依靠空白分隔\n";
                    $prompt .= "- 陰影：極少或僅使用極淡的陰影\n";

                    break;
                case 5:
                    // 科技未來感
                    $prompt .= "色彩方案：\n";
                    $prompt .= "- 主色：深灰色或黑色背景\n";
                    $prompt .= "- 輔助色：電光藍、熒光紫\n";
                    $prompt .= "- 強調色：熒光綠或霓虹橙\n";
                    $prompt .= "- 整體色調：高對比度、未來感、科技感\n";
                    $prompt .= "字體選擇：\n";
                    $prompt .= "- 標題：幾何感強的無襯線字體，如Orbitron、Rajdhani或未來荷黑（中文）\n";
                    $prompt .= "- 正文：清晰的現代無襯線字體，如Roboto Mono、Space Grotesk或等寬字體\n";
                    $prompt .= "- 字重：標題使用light或bold，正文使用regular\n";
                    $prompt .= "- 大小對比：標題可使用較大字號，配合細線條設計\n";
                    $prompt .= "排版規則：\n";
                    $prompt .= "- 整體留白：有節奏的留白，類似電子界面\n";
                    $prompt .= "- 段落間距：1.3-1.5倍行高\n";
                    $prompt .= "- 元素間距：精確計算的間距，像素級精準\n";
                    $prompt .= "- 邊距：邊距清晰但不過大，保持科技感\n";
                    $prompt .= "- 對齊方式：格柵式對齊，精確到像素\n";
                    $prompt .= "- 網格系統：模塊化網格，類似UI界面\n";
                    $prompt .= "- 層次結構：使用色彩和線條建立層次\n";
                    $prompt .= "- 行寬控制：中等行寬，類似代碼編輯器\n";
                    $prompt .= "- 圖表：數據可視化風格，如雷達圖、網絡圖\n";
                    $prompt .= "- 圖示：科技感線條圖標，可帶發光效果\n";
                    $prompt .= "- 分隔線：光束效果或像素線條\n";
                    $prompt .= "- 陰影：霓虹光暈或柔和發光效果\n";
                    break;
                default:
            }
            $prompt .= "\n\n";

            $target_audience_list = [
                '1' => '一般大众',
                '2' => '商業專業人士',
                '3' => '學生/教育',
                '4' => '創意產業',
            ];

            // 添加目标用户相关提示
            $target_audience_str = $target_audience_list[$instructions['target_audience']] ?? '';
            if (!empty($target_audience_str)) {
                $prompt .= "目标用户特征：{$target_audience_str}\n";
                $prompt .= "请确保内容表述和结构符合目标用户的阅读习惯和理解水平。\n\n";
            }

            // 添加建议接受度
            $suggestion_acceptance = $instructions['suggestion_acceptance'] ?? 0;
            if ($suggestion_acceptance > 0) {
                $prompt .= "建议接受度0-100，越高越偏离原内容，越低越接近原内容：{$suggestion_acceptance}\n";
            }
            // 添加高级设置
            if (!empty($instructions['advanced_settings'])) {
                $prompt .= "高级排版要求：\n";

                // 颜色风格
                if (!empty($instructions['advanced_settings']['color_style'])) {
                    $prompt .= "- 颜色风格设置：\n";
                    $prompt .= "  * 主色调：使用 {$instructions['advanced_settings']['color_style']} 作为主要色调\n";
                    $prompt .= "  * 应用场景：\n";
                    $prompt .= "    - 标题和重要文本使用主色调\n";
                    $prompt .= "    - 关键词和重点内容使用主色调强调\n";
                    $prompt .= "    - 引用块和分割线可使用主色调的浅色版本\n";
                    $prompt .= "    - 列表符号和项目标记使用主色调\n";
                    $prompt .= "  * 色彩原则：\n";
                    $prompt .= "    - 确保颜色对比度适中，保持良好的可读性\n";
                    $prompt .= "    - 使用渐变色调营造层次感\n";
                    $prompt .= "    - 重要信息使用深色，次要信息使用浅色\n";
                }

                // 字体风格
                if (!empty($instructions['advanced_settings']['font_style'])) {
                    $prompt .= "- 字体风格设置：\n";
                    $prompt .= "  * 字体类型：{$instructions['advanced_settings']['font_style']}\n";
                    $prompt .= "  * 字体应用：\n";
                    $prompt .= "    - 标题使用粗体或半粗体，增强视觉重要性\n";
                    $prompt .= "    - 正文使用常规字重，确保清晰易读\n";
                    $prompt .= "    - 引用和强调文本可使用斜体\n";
                    $prompt .= "    - 确保中文和英文字体搭配协调\n";
                    $prompt .= "  * 字体层级：\n";
                    $prompt .= "    - 主标题：18-24px，粗体\n";
                    $prompt .= "    - 副标题：16-18px，半粗体\n";
                    $prompt .= "    - 正文：14-16px，常规\n";
                    $prompt .= "    - 注释：12-14px，浅色\n";
                }

                // 内容密度
                if (isset($instructions['advanced_settings']['density'])) {
                    $density = $instructions['advanced_settings']['density'];
                    $prompt .= "- 内容密度设置：{$density}%\n";
                    if ($density < 30) {
                        $prompt .= "  * 宽松布局要求：\n";
                        $prompt .= "    - 段落间距设置为1.8-2倍行高\n";
                        $prompt .= "    - 标题前后留出充足空白（上方2em，下方1.5em）\n";
                        $prompt .= "    - 列表项之间保持较大间距（1.2em）\n";
                        $prompt .= "    - 图片周围预留充足留白\n";
                        $prompt .= "    - 引用块四周留出较大空白区域\n";
                    } elseif ($density > 70) {
                        $prompt .= "  * 紧凑布局要求：\n";
                        $prompt .= "    - 段落间距控制在1.2-1.4倍行高\n";
                        $prompt .= "    - 标题与内容之间间距适中（上方1.2em，下方0.8em）\n";
                        $prompt .= "    - 列表项间距较小（0.5em）\n";
                        $prompt .= "    - 图片与文字之间留出最小必要的间距\n";
                        $prompt .= "    - 优化空间利用，减少不必要的留白\n";
                    } else {
                        $prompt .= "  * 平衡布局要求：\n";
                        $prompt .= "    - 段落间距保持在1.5-1.6倍行高\n";
                        $prompt .= "    - 标题与内容之间保持适中间距（上方1.5em，下方1em）\n";
                        $prompt .= "    - 列表项使用适中间距（0.8em）\n";
                        $prompt .= "    - 图文之间保持舒适的间距\n";
                        $prompt .= "    - 整体布局既不松散也不拥挤\n";
                    }
                }

                // 图文比例
                if (isset($instructions['advanced_settings']['image_text_ratio'])) {
                    $imageTextRatio = $instructions['advanced_settings']['image_text_ratio'];
                    $prompt .= "- 图文比例设置：{$imageTextRatio}%\n";
                    if ($imageTextRatio > 60) {
                        $prompt .= "  * 图片导向布局：\n";
                        $prompt .= "    - 图片尺寸可以适当放大，占据更多版面\n";
                        $prompt .= "    - 重要图片使用大尺寸全宽展示\n";
                        $prompt .= "    - 可以使用图片组合或图片墙的形式\n";
                        $prompt .= "    - 文字内容精简，以图片为主要表现形式\n";
                        $prompt .= "    - 图片说明文字简洁但信息完整\n";
                    } elseif ($imageTextRatio < 40) {
                        $prompt .= "  * 文字导向布局：\n";
                        $prompt .= "    - 图片尺寸适中，不抢占文字版面\n";
                        $prompt .= "    - 图片作为文字内容的补充说明\n";
                        $prompt .= "    - 可以使用小图标或缩略图形式\n";
                        $prompt .= "    - 重点在文字表达，图片起辅助作用\n";
                        $prompt .= "    - 图文混排时确保文字主次分明\n";
                    } else {
                        $prompt .= "  * 均衡图文布局：\n";
                        $prompt .= "    - 图片和文字篇幅大致相当\n";
                        $prompt .= "    - 图文交替出现，形成节奏感\n";
                        $prompt .= "    - 根据内容重要性调整图片大小\n";
                        $prompt .= "    - 保持图文之间的视觉平衡\n";
                        $prompt .= "    - 图文内容相互呼应，共同传达信息\n";
                    }
                }

                // 元素强调
                if (!empty($instructions['advanced_settings']['element_emphasis'])) {
                    $emphasis = $instructions['advanced_settings']['element_emphasis'];
                    $prompt .= "- 元素强调设置：\n";

                    if (!empty($emphasis['quote'])) {
                        $prompt .= "  * 引用块样式增强：\n";
                        $prompt .= "    - 使用醒目的左侧边框\n";
                        $prompt .= "    - 背景色可以使用浅色填充\n";
                        $prompt .= "    - 字体可以使用斜体处理\n";
                        $prompt .= "    - 增加适当的内边距\n";
                    }
                    if (!empty($emphasis['heading'])) {
                        $prompt .= "  * 标题层级强化：\n";
                        $prompt .= "    - 使用递进的字号大小\n";
                        $prompt .= "    - 可以使用不同的字重\n";
                        $prompt .= "    - 标题间距要有明显区分\n";
                        $prompt .= "    - 可以使用色彩或底线作为辅助\n";
                    }
                    if (!empty($emphasis['image'])) {
                        $prompt .= "  * 图片展示优化：\n";
                        $prompt .= "    - 图片可以添加浅边框\n";
                        $prompt .= "    - 鼠标悬停效果增强\n";
                        $prompt .= "    - 添加图片说明文字\n";
                        $prompt .= "    - 优化图片排列方式\n";
                    }
                    if (!empty($emphasis['keyword'])) {
                        $prompt .= "  * 关键词突出显示：\n";
                        $prompt .= "    - 使用加粗或高亮处理\n";
                        $prompt .= "    - 可以使用主题色标注\n";
                        $prompt .= "    - 适当使用底色强调\n";
                        $prompt .= "    - 确保与上下文有足够对比度\n";
                    }
                    if (!empty($emphasis['list'])) {
                        $prompt .= "  * 列表样式增强：\n";
                        $prompt .= "    - 使用醒目的列表符号\n";
                        $prompt .= "    - 可以添加项目间隔底色\n";
                        $prompt .= "    - 列表层级视觉清晰\n";
                        $prompt .= "    - 重要列表项可以特殊标记\n";
                    }
                    if (!empty($emphasis['table'])) {
                        $prompt .= "  * 表格展示优化：\n";
                        $prompt .= "    - 表头样式突出显示\n";
                        $prompt .= "    - 使用隔行底色\n";
                        $prompt .= "    - 单元格边框清晰\n";
                        $prompt .= "    - 重要数据可以特殊标记\n";
                    }
                }

                $prompt .= "\n";
            }
        }

        // 添加内容分析指令
        $prompt .= "内容分析与排版指导：\n";
        $prompt .= "- 请先分析内容的风格和类型（如学术专业、新闻报道、营销推广、教程指南、故事叙述等）\n";
        $prompt .= "- 根据识别出的内容风格，应用最适合的排版策略\n";
        $prompt .= "- 对于学术内容，使用正式结构，规范的引用和标注\n";
        $prompt .= "- 对于新闻内容，使用倒金字塔结构，简洁标题，短段落\n";
        $prompt .= "- 对于营销内容，突出关键信息，使用列表展示优势，添加行动召唤\n";
        $prompt .= "- 对于教程内容，清晰标示步骤，突出注意事项，使用图文结合\n";
        $prompt .= "- 对于叙述内容，注重段落流畅过渡，适当格式化对话和情感描写\n";
        $prompt .= "- 分析内容的主题和关键点，确保排版突出这些要素\n";
        $prompt .= "- 评估内容的复杂度，为复杂内容提供更清晰的结构和导航\n";
        $prompt .= "- 识别内容中的重点和强调部分，使用适当的HTML元素突出显示\n\n";


        // SEO优化提示
        $prompt .= "SEO优化要求：\n";
        $prompt .= "- 不需要h1标签,使用h2标签作为主要标题\n";
        $prompt .= "- 确保标题层级合理(h2->h3)\n";
        $prompt .= "- 关键词自然分布在内容中\n";
        $prompt .= "- 图片添加合适的alt属性\n";
        $prompt .= "- 确保内容结构清晰，便于搜索引擎理解\n";
        $prompt .= "- 确保排版符合SEO最佳实践，有利于搜索引擎抓取和理解\n\n";


        // 添加原始内容
        // $prompt .= "请对以下内容进行排版：\n\n{$content}\n\n";

        // 最终提示
        $prompt .= "请按照以下要求生成内容：\n\n";

        // 输出格式要求
        $prompt .= "【输出格式要求】(必须严格遵守)\n";
        $prompt .= "- 必须且只能返回纯HTML格式代码，不允许任何其他格式；\n";
        $prompt .= "- 严禁在HTML前后添加任何说明、注释、标记或代码块符号；\n";
        $prompt .= "- 严禁使用```html或```等Markdown代码块标记包裹HTML代码；\n";
        $prompt .= "- 严禁包含任何解释、问候、总结或额外说明；\n";
        $prompt .= "- 严禁返回与要求无关的内容或上下文；\n";
        $prompt .= "- 直接输出原始HTML代码，不要添加任何额外的标记或格式；\n";
        $prompt .= "- 绝对不要使用Markdown格式，不要添加```html和```标记；\n";
        $prompt .= "- 在输出前，检查所有HTML标签是否正确闭合，确保没有不完整的标签；\n";
        $prompt .= "- 违反以上任何规则将导致输出无法使用；\n\n";

        // 内容处理要求
        $prompt .= "【内容处理要求】\n";
        $prompt .= "- 【绝对最高优先级】必须逐字保留所有原始文本内容，包括每一个字、标点符号和空格；\n";
        $prompt .= "- 【严格禁止】不允许任何形式的内容简化、概括、删减或重写；\n";
        $prompt .= "- 保持原文意思完全不变；\n";
        $prompt .= "- 保持原文语言不变；\n";
        $prompt .= "- 只调整格式和结构，不改变任何文字内容；\n";
        $prompt .= "- 【必须】使用内联样式容器包裹整个内容：<div style=\"width: 100%; padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto;\"><div style=\"display: flex; flex-wrap: wrap; margin-right: -15px; margin-left: -15px;\"><div style=\"flex: 0 0 100%; max-width: 100%; padding-right: 15px; padding-left: 15px;\">...</div></div></div>；\n";
        $prompt .= "- 【必须】将内容分割成多个语义相关的独立区块，每个区块必须使用卡片样式包裹：<div style=\"margin-bottom: 1.5rem; border-radius: 0.25rem; border: 1px solid rgba(0,0,0,0.125); box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);\">...</div>；\n";
        $prompt .= "- 【必须】为每个区块应用适合其内容情绪和主题的背景色，但整个页面最多只使用3种颜色：主要颜色(#007bff)、次要颜色(#f8f9fa)和强调颜色(#ffc107)；\n";
        $prompt .= "- 【必须】使用内联样式设置文本和间距，如margin-bottom: 1.5rem;、padding: 1rem;等，文本颜色保持简洁，主要使用黑色和白色；\n";
        $prompt .= "- 严格保持所有HTML标签的完整性，不要删除、修改或破坏任何HTML标签；\n";
        $prompt .= "- 【极其重要】：必须保留所有图片占位符（如'IMG_PLACEHOLDER_0'）。这些占位符将在后续处理中被替换为实际图片；\n";
        $prompt .= "- 图片占位符必须保持原样（如'IMG_PLACEHOLDER_0'），只允许调整其在文档中的位置，不得对其做任何修改；\n";
        $prompt .= "- 绝对不要将图片占位符转换为<img>标签，必须保持纯文本格式；\n";
        $prompt .= "- 必须确保每个图片占位符都出现在最终输出中，这是最高优先级要求；\n";
        $prompt .= "- 样式属性必须格式正确，不得出现重复的style属性或格式错误，如style=\" style=\"...\"或style=\"...\"&gt;这类错误；\n";
        $prompt .= "- 绝对不要将HTML标签转义为HTML实体，例如不要将<转义为&lt;或将>转义为&gt;，保持原始的HTML标签格式；\n";
        $prompt .= "- 确保所有HTML标签都正确闭合，每个开始标签必须有对应的结束标签，如<p>必须有</p>，<div>必须有</div>；\n";
        $prompt .= "- 检查并修复任何不完整的标签，确保没有孤立的开始标签或结束标签；\n";
        $prompt .= "- 识别并正确处理文章中的小标题、段落、列表、引用等元素；\n";
        $prompt .= "- 识别内容中的图片占位符，将其放置在最合适的位置；\n";
        $prompt .= "- 根据内容上下文，合理安排图片占位符位置，使其与相关文本紧密关联；\n";
        $prompt .= "- 大图片占位符应独占一行，小图片占位符可以适当浮动或内联显示；\n";
        $prompt .= "- 使用富文本编辑器兼容的HTML标签，如p、div、span、h2-h6、ul、ol、li、blockquote等；\n";
        $prompt .= "- 避免使用复杂的HTML结构或不常见的标签，确保在富文本编辑器中正确显示；\n";
        $prompt .= "- 确保HTML标签正确嵌套，避免嵌套错误；\n";
        $prompt .= "- 语言需要是{$lang}\n\n";
        // $prompt .= "10. 输出干净的HTML代码，不要包含任何CSS样式\n";

        $prompt .= "【最高优先级要求】：必须保留所有图片占位符（如'IMG_PLACEHOLDER_0'）。这些占位符将在后续处理中被替换为实际图片。绝对不要将图片占位符转换为<img>标签，必须保持纯文本格式。必须确保每个图片占位符都出现在最终输出中，这是最高优先级要求。\n\n";

        $prompt .= "【再次强调】：必须逐字保留所有原始文本内容，包括每一个字、标点符号和空格。不允许任何形式的内容简化、概括、删减或重写。\n\n";

        $prompt .= "【内联样式结构要求】：必须使用内联样式容器包裹整个内容，必须将内容分割成多个语义相关的独立区块，每个区块必须使用卡片样式包裹，必须为每个区块应用适合其内容情绪和主题的背景色。【重要】整个页面最多只使用3种颜色：主要颜色(#007bff)、次要颜色(#f8f9fa)和强调颜色(#ffc107)。整体HTML结构必须是：\n";
        $prompt .= "<div style=\"width: 100%; padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto;\">\n";
        $prompt .= "  <div style=\"display: flex; flex-wrap: wrap; margin-right: -15px; margin-left: -15px;\">\n";
        $prompt .= "    <div style=\"flex: 0 0 100%; max-width: 100%; padding-right: 15px; padding-left: 15px;\">\n";
        $prompt .= "      <!-- 主要颜色区块示例 -->\n";
        $prompt .= "      <div style=\"margin-bottom: 1.5rem; border-radius: 0.25rem; background-color: #007bff; color: white; padding: 1rem;\">...</div>\n";
        $prompt .= "      <!-- 次要颜色区块示例 -->\n";
        $prompt .= "      <div style=\"margin-bottom: 1.5rem; border-radius: 0.25rem; background-color: #f8f9fa; padding: 1rem;\">...</div>\n";
        $prompt .= "      <!-- 强调颜色区块示例 -->\n";
        $prompt .= "      <div style=\"margin-bottom: 1.5rem; border-radius: 0.25rem; background-color: #ffc107; padding: 1rem;\">...</div>\n";
        $prompt .= "      <!-- 无背景色区块示例 -->\n";
        $prompt .= "      <div style=\"margin-bottom: 1.5rem; border-radius: 0.25rem; border: 1px solid rgba(0,0,0,0.125); padding: 1rem;\">...</div>\n";
        $prompt .= "    </div>\n";
        $prompt .= "  </div>\n";
        $prompt .= "</div>\n\n";

        $prompt .= "重要：不要将HTML标签转义为HTML实体，例如不要将<转义为&lt;或将>转义为&gt;，保持原始的HTML标签格式。";
        return $prompt;
    }

    /**
     * 生成SEO提示词（第二版）
     *
     * @param string $content 内容参数在此方法中不使用，但保留参数以保持方法签名一致性
     * @return string
     */
    protected function analyzeContentAndGenerateSeoPrompt2(string $content = '')
    {

        $prompt = '数字营销趋势HTML生成规范

📦 核心要求
- 输出<body>内容，纯内联样式
- 6个响应式区块（±1浮动）
- 主容器：max-width:960px + 浅灰背景
- 字体：Consolas等宽

🎯 样式速查
[标题] h2(1.5rem/#007bff) > h3(1.25rem) > h4(1rem)
[正文] 1rem/1.6行高 + 底部间距1rem
[区块] 白底+浅灰边框 + 圆角0.25rem + 底部间距1.5rem

🖼️ 媒体规则
- 图片：16:9比例容器（position:relative + padding-top:56.25%）
- 备用：底部显示URL + alt文字
- 说明：灰色0.875rem居中

📐 布局选项
1. 垂直流（默认）
2. 桌面双列（display:flex + gap）
3. 图文混排（flex图文并排）

✂️ 精简示例
<!-- 区块模板 -->
<div style="background:#fff; border:1px solid #ddd; border-radius:4px; margin-bottom:1.5rem;">
  <div style="background:#f8f9fa; border-bottom:1px solid #ddd; padding:8px 16px;">
    <h2 style="font-size:1.5rem; color:#007bff;">标题</h2>
  </div>

  <div style="padding:16px;">
    <p style="font-size:1rem; line-height:1.6;">内容...</p>
    <!-- 可选布局标记 -->
    <div style="display:flex; gap:1rem;">${子内容}</div>
  </div>

  <div style="background:#f8f9fa; border-top:1px solid #ddd; padding:8px 16px; font-size:0.875rem; color:#666;">
    [备用] 图片URL
  </div>
</div>

⚙️ 必选规则
1. 每区块<!-- 注释 -->标注类型
2. 移动优先（媒体查询内联实现）
3. 混合内容最多2图/区块
4. 底部保留150px间距';

        return $prompt;
    }

    protected function analyzeContentAndGenerateSeoPrompt(string $content): string
    {
        // 初始化提示词
        $prompt = "请作为内容排版专家，使用Bootstrap框架对内容进行美观的重组排版。【核心要求】：将内容分成多个独立区块，每个区块应有明确的主题和视觉边界，增加内容的区块划分，但严格保持原文文字内容不变，只调整结构和排版方式。【区块设计】：相邻区块之间必须保持足够的间距（建议至少20-30px的margin），确保内容不会粘在一起，提高可读性和视觉舒适度。每个内容区块应当有清晰的视觉分隔，可以通过背景色差异、边框或阴影来强化区块边界。【视觉风格】：标题区域可使用渐变色背景条（如从浅蓝到深蓝、从淡紫到深紫等柔和过渡的颜色），或使用单一色调但在不同区块间形成和谐的色彩序列，确保整体视觉流畅统一。标题文字使用白色或浅色，确保与背景形成舒适对比。内容区域使用浅色背景，可以是纯色或极淡的渐变，正文文字使用深色确保易读性。相邻区块的颜色应当形成自然过渡，避免生硬对比。【内容处理原则】：必须严格保持原文文字内容不变，只进行结构和排版的优化，增加内容的区块划分，使内容更有层次感和可读性。请生成富文本编辑器兼容的纯HTML代码，确保美观且符合大众审美。【最高优先级要求】：必须保留所有图片占位符（如'IMG_PLACEHOLDER_0'）。这些占位符将在后续处理中被替换为实际图片。绝对不要将图片占位符转换为<img>标签，必须保持纯文本格式。必须确保每个图片占位符都出现在最终输出中，这是最高优先级要求。【严格格式要求】：1. 必须严格保持所有HTML标签的完整性和正确性，2. 所有HTML标签必须正确闭合和嵌套，每个开始标签必须有对应的结束标签；3. 每个HTML元素的属性必须格式正确，属性值必须使用引号包裹，且引号必须成对出现；4. 在生成HTML前，必须仔细检查所有标签和属性的完整性和正确性；5. 确保所有的>和<符号都只用于HTML标签，不会出现在普通文本中导致解析错误；6. 绝对不要将HTML标签转义为HTML实体，例如不要将<转义为&lt;或将>转义为&gt;，保持原始的HTML标签格式。请确保返回的是纯HTML格式，不包含任何代码块标记或额外说明。";

        return $prompt;
    }


    /**
     * 获取模板
     *
     * @param int $creator_id
     * @param string $lang
     * @return SeoLayoutTemplate
     */
    public function getTemplateByCreatorId(int $creator_id, string $lang = '')
    {
        return $this->seoLayoutTemplateRepository->getTemplateByCreatorId($creator_id, $lang);
    }

    /**
     * 保存模板
     *
     * @param array $data
     * @return SeoLayoutTemplate
     */
    public function saveTemplate(array $data)
    {
        return $this->seoLayoutTemplateRepository->saveTemplate($data);
    }

    /**
     * 应用模板
     *
     * @param array $data
     * @return string
     */
    public function applyTemplate($data)
    {
        $content = $data['content'];
        $style = $data['style'];
        $lang = $data['lang'] ?? 'en';
        $style['lang'] = $lang;


        $template_log = $this->applyTemplateWithLog($data);
        $start_processing_time = microtime(true);
        // try {

        $formattedContent = $this->formatWithTemplate($content, $style);
        $processing_time = microtime(true) - $start_processing_time;
        $template_log->update([
            'formatted_content' => $formattedContent,
            'status' => 'success',
            'error_message' => '',
            'processing_time' => $processing_time,
        ]);
        return  [
            'content' => $formattedContent,
            'log_id' => $template_log->id,
        ];
        // } catch (\Throwable $e) {
        //     $error_message = $e->getMessage();
        //     $processing_time = microtime(true) - $start_processing_time;
        //     $template_log->update([
        //         'status' => 'failed',
        //         'error_message' => $error_message,
        //         'processing_time' => $processing_time,
        //     ]);
        //     BizException::throws(SeoErrorCode::CONTENT_LAYOUT_FAILED, $e->getMessage());
        // }
    }

    /**
     * 应用模板并记录日志
     *
     * @param array $data
     * @param string $formatted_content
     * @param string $status
     * @param string $error_message
     * @return SeoContentLayoutLog
     */
    public function applyTemplateWithLog(array $data, $formatted_content = '', $status = 'processing', $error_message = '')
    {
        $content = $data['content'];
        $content_id = $data['content_id'] ?? 0;
        $style = $data['style'] ?? [];
        $creator_id = $data['creator_id'] ?? 0;
        $lang = $data['lang'] ?? '';

        $log = SeoContentLayoutLog::create([
            'content_id' => $content_id,
            'original_content' => $content,
            'style' => $style,
            'creator_id' => $creator_id,
            'lang' => $lang,
            'formatted_content' => $formatted_content,
            'status' => $status,
            'error_message' => $error_message,
        ]);
        return $log;
    }
}
