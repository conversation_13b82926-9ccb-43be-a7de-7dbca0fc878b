<?php
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-04-10 16:33:05
 * @LastEditTime: 2025-04-10 19:47:10
 * @LastEditors: Chiron
 * @Description: 
 */

namespace Modules\SEO\Domain\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Bingo\Exceptions\BizException;
use Modules\SEO\Enums\SEOErrorCode;

class LlmService
{
  protected AIService $aiService;
  protected int $cacheMinutes = 60;


  public function __construct(AIService $aiService)
  {
    $this->aiService = $aiService;
  }


  /**
   * 分析内容
   *
   * @param array $data
   * @return array
   */
  public function analyze(array $data)
  {
    try {
      // 检查缓存
      $cacheKey = 'seo_llm_analysis_' . md5(json_encode($data));
      // Cache::forget($cacheKey);
      if ($cachedResult = Cache::get($cacheKey)) {
        return $cachedResult;
      }

      // 准备提示词
      $prompt = $this->preparePrompt($data);

      $messages = [
        [
          'role' => 'system',
          'content' => 'You are a professional SEO expert. Analyze the content and provide detailed SEO suggestions.'
        ],
        [
          'role' => 'user',
          'content' => $prompt
        ]
      ];
      $response = $this->aiService->chatCompletion($messages, [
        'max_tokens' => 1000
      ]);

      $result = $this->parseResponse($response);


      // 缓存结果
      Cache::put($cacheKey, $result, $this->cacheMinutes * 60);

      return $result;
    } catch (\Exception $e) {
      Log::info('LLM analysis failed', [
        'error' => $e->getMessage(),
        'data' => $data
      ]);
      BizException::throws(SEOErrorCode::SEO_ANALYSIS_FAILED, $e->getMessage());
    }
  }

  /**
   * 生成SEO推荐内容
   *
   * @param array $data 内容数据
   * @param array $types 需要生成的推荐类型 ['title', 'description', 'seo_title', 'seo_description', 'keywords']
   * @return array
   */
  public function generateRecommendations(array $data, array $types = []): array
  {
    try {
      // 如果没有指定类型，默认生成所有类型
      if (empty($types)) {
        $types = ['title', 'description', 'seo_title', 'seo_description', 'keywords'];
      }

      // 生成缓存键
      $cacheKey = 'seo_recommendations_' . md5(json_encode($data) . json_encode($types));

      // 检查缓存
      Cache::forget($cacheKey);
      if ($cachedResult = Cache::get($cacheKey)) {
        return $cachedResult;
      }

      // 准备提示词
      $prompt = $this->prepareRecommendationPrompt($data, $types);

      // 调用AI服务
      $messages = [
        [
          'role' => 'system',
          'content' => 'You are a professional SEO expert. Your task is to generate high-quality SEO recommendations based on the content provided. Focus on creating engaging, accurate, and search-engine friendly titles, descriptions, and keywords.'
        ],
        [
          'role' => 'user',
          'content' => $prompt
        ]
      ];

      $response = $this->aiService->chatCompletion($messages, [
        'temperature' => 0.7, // 适当的创造性
        'max_tokens' => 1000
      ]);

      // 解析响应
      $result = $this->parseRecommendationResponse($response, $types);

      // 缓存结果
      Cache::put($cacheKey, $result, 30 * 60); // 缓存30分钟

      return $result;
    } catch (\Exception $e) {
      Log::error('SEO recommendations generation failed', [
        'error' => $e->getMessage(),
        'data' => $data
      ]);
      BizException::throws(SEOErrorCode::SEO_RECOMMENDATION_FAILED, $e->getMessage());
    }
  }

  /**
   * 准备提示词
   *
   * @param array $data
   * @return string
   */
  protected function preparePrompt(array $data): string
  {
    $lang = $data['lang'] ?? 'zh_CN';

    $locale = $data['locale'] ?? 'zh_CN';

    return "您是一个专业的SEO分析工具。请分析以下内容并提供SEO评分和优化建议。
请严格按照指定的JSON格式返回结果。

内容标题: {$data['title']}
目标关键词: " . implode(', ', $data['keywords'] ?? []) . "
描述: {$data['description']}
内容正文:
{$data['content']}

请提供以下分析:
1. 总体SEO评分(0-100)
2. 内容质量评分(0-100)
3. 关键词优化评分(0-100)
4. 技术SEO评分(0-100)
5. 具体分析和改进建议
6. 具体的SEO建议和标题建议

请严格按照JSON格式生成内容，确保符合以下要求：
1. 使用双引号包裹所有键和字符串值
2. 不使用单引号、注释或JavaScript风格的尾部逗号
3. 包含明确的层级结构
4. 确保所有键和值都正确格式化

返回示例：
{
  'overall_score': 数字,
  'content_score': 数字,
  'keyword_score': 数字,
  'technical_score': 数字,
  'readability': {
    'score': 数字,
    'level': '容易/中等/困难',
    'description': '文本'
  },
  'content_depth': {
    'score': 数字,
    'word_count': 数字,
    'assessment': '文本'
  },
  'originality': {
    'score': 数字,
    'assessment': '文本'
  },
  'keyword_analysis': {
    'primary_keyword': {
      'presence': '好/中/差',
      'density': 数字,
      'assessment': '文本'
    },
    'secondary_keywords': [
      {
        'keyword': '文本',
        'presence': '好/中/差',
        'suggestions': '文本'
      }
    ]
  },
  'suggestions': {
    'title': ['建议1', '建议2'],
    'description': ['建议1', '建议2'],
    'content': ['建议1', '建议2'],
    'keywords': ['建议1', '建议2'],
    'technical': ['建议1', '建议2']
  },
  'seo_suggestions': {
    'title': ['标题1', '标题1'],
    'description': ['描述1', '描述2'],
    'seo_title': ['SEO标题1', 'SEO标题1'],
    'seo_description': ['SEO描述1', 'SEO描述2'],
    'keywords':  ['SEO关键词1', 'SEO关键词2'],
  }
}

不要包含注释和多余说明，直接返回有效JSON数组,使用紧凑JSON格式（无换行空格），禁用Unicode转义符, JSON中seo_suggestions的语言需要是{$lang},其他的文本语言需要使用{$locale},seo_keywords需要数组，seo_title和seo_description需要包含优化后的标题和描述。
。修复任何可能的JSON错误。";
  }

  /**
   * 解析API响应
   *
   * @param array $response
   * @return array
   */
  protected function parseResponse(array $response): array
  {
    $content = $response['choices'][0]['message']['content'] ?? '';

    // 预处理JSON内容
    $content = $this->preprocessJsonContent($content);

    try {
      // 增加JSON解析深度和错误检测
      Log::info('LLM preprocessed data', ['content' => $content]);
      $data = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

      return [
        'overall_score' => $data['overall_score'] ?? 0,
        'content_score' => $data['content_score'] ?? 0,
        'keyword_score' => $data['keyword_score'] ?? 0,
        'technical_score' => $data['technical_score'] ?? 0,
        'readability' => $data['readability'] ?? [],
        'content_depth' => $data['content_depth'] ?? [],
        'originality' => $data['originality'] ?? [],
        'keyword_analysis' => $data['keyword_analysis'] ?? [],
        'suggestions' => [
          'title' => $data['suggestions']['title'] ?? [],
          'description' => $data['suggestions']['description'] ?? [],
          'content' => $data['suggestions']['content'] ?? [],
          'keywords' => $data['suggestions']['keywords'] ?? [],
          'technical' => $data['suggestions']['technical'] ?? []
        ],
        'seo_suggestions' => $data['seo_suggestions'] ?? [],
      ];
    } catch (\JsonException $e) {
      Log::error('Failed to parse LLM response', [
        'error' => $e->getMessage(),
        'content' => $content,
        'json_error' => json_last_error_msg(),
        'trace' => $e->getTraceAsString()
      ]);
      BizException::throws(SEOErrorCode::AI_SERVICE_FAILED, '解析AI服务响应失败: ' . $e->getMessage());
    }
  }

  /**
   * 预处理JSON内容
   */
  protected function preprocessJsonContent(string $content): string
  {
    // 移除可能的BOM头
    $content = str_replace("\xEF\xBB\xBF", '', $content);
    $content = preg_replace('/```json/', '', $content);
    $content = preg_replace('/```/', '', $content);

    // 处理转义问题
    $content = stripslashes($content);

    // 替换中文标点为英文标点
    $content = str_replace(
      ['：', '，', '；', '。', '（', '）', '【', '】', '｛', '｝', '［', '］', '‘', '’', '“', '”'],
      [':', ',', ';', '.', '(', ')', '[', ']', '{', '}', '[', ']', '\'', '\'', '"', '"'],
      $content
    );

    // 提取JSON部分
    preg_match('/\{[\s\S]*\}/m', $content, $matches);
    $jsonString = $matches[0] ?? $content;

    // 修复可能的JSON截断
    if (substr($jsonString, -1) !== '}' && strpos($jsonString, '}') !== false) {
      $jsonString = substr($jsonString, 0, strrpos($jsonString, '}') + 1);
    }

    return trim($jsonString);
  }

  /**
   * 准备推荐内容的提示词
   *
   * @param array $data
   * @param array $types
   * @return string
   */
  protected function prepareRecommendationPrompt(array $data, array $types): string
  {
    $content = $data['content'] ?? '';
    $originalTitle = $data['title'] ?? '';
    $originalDescription = $data['description'] ?? '';
    $lang = $data['lang'] ?? 'zh_CN';


    // 构建提示词
    $prompt = "请作为专业的SEO专家，根据以下内容生成高质量的SEO推荐。\n\n";
    $prompt .= "原始标题: {$originalTitle}\n";
    $prompt .= "原始描述: {$originalDescription}\n";
    $prompt .= "内容:\n{$content}\n\n";
    $prompt .= "请严格按照JSON格式生成内容，确保符合以下要求：
1. 使用双引号包裹所有键和字符串值
2. 不使用单引号、注释或JavaScript风格的尾部逗号
3. 包含明确的层级结构
4. 确保所有键和值都正确格式化
5. 不要包含注释和多余说明，直接返回有效JSON数组,使用紧凑JSON格式（无换行空格），禁用Unicode转义符
6. 语言需要是{$lang}";


    $prompt .= "请生成以下内容（以JSON格式返回）:\n";

    if (in_array('title', $types)) {
      $prompt .= "1. 推荐标题：简洁有力，能够吸引用户点击，同时包含主要关键词\n";
    }

    if (in_array('description', $types)) {
      $prompt .= "2. 推荐描述：简明扑要地概括内容，包含关键词，吸引用户点击\n";
    }

    if (in_array('seo_title', $types)) {
      $prompt .= "3. SEO标题：针对搜索引擎优化的标题，包含主要关键词，不超过70个字符\n";
    }

    if (in_array('seo_description', $types)) {
      $prompt .= "4. SEO描述：针对搜索引擎优化的描述，包含关键词，不超过160个字符\n";
    }

    if (in_array('keywords', $types)) {
      $prompt .= "5. SEO关键词：5-8个与内容高度相关的关键词，按重要性排序，以数组形式返回\n";
    }

    $prompt .= "\n请严格按照以下JSON格式返回结果：\n";
    $prompt .= "{\n";

    $examples = [];
    if (in_array('title', $types)) {
      $examples[] = '  "title": ["推荐的标题内容1", "推荐的标题内容2"]';
    }
    if (in_array('description', $types)) {
      $examples[] = '  "description": ["推荐的描述内容1","推荐的描述内容2"]';
    }
    if (in_array('seo_title', $types)) {
      $examples[] = '  "seo_title": ["推荐的SEO标题内容1","推荐的SEO标题内容2"]';
    }
    if (in_array('seo_description', $types)) {
      $examples[] = '  "seo_description": ["推荐的SEO描述内容1", "推荐的SEO描述内容2"]';
    }
    if (in_array('keywords', $types)) {
      $examples[] = '  "keywords": ["关键词1", "关键词2", "关键词3", "关键词4", "关键词5"]';
    }

    $prompt .= implode(",\n", $examples);
    $prompt .= "\n}\n";

    return $prompt;
  }

  /**
   * 解析推荐内容的API响应
   *
   * @param array $response
   * @param array $types
   * @return array
   */
  protected function parseRecommendationResponse(array $response, array $types): array
  {
    $content = $response['choices'][0]['message']['content'] ?? '';

    // 尝试解析JSON
    try {
      // 预处理JSON内容
      $content = $this->preprocessJsonContent($content);

      $data = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

      // 确保所有请求的类型都有返回值
      $result = [];
      foreach ($types as $type) {
        $result[$type] = $data[$type] ?? null;
      }
      return $result;
    } catch (\Exception $e) {
      Log::error('Failed to parse SEO recommendations response', [
        'error' => $e->getMessage(),
        'content' => $content
      ]);
      return $result;
    }
  }
}
