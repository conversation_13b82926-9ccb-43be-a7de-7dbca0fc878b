<?php

namespace Modules\Ai\Domain;

use Exception;
use Modules\Ai\Domain\Engine\OpenAiEngine;
use Modules\Ai\Enums\AiEngine;
use Modules\Ai\Models\AiSetting;
use Illuminate\Support\Facades\Log;

class AiEngineFactory
{
    /**
     * 创建AI引擎实例
     * @throws Exception
     */
    public static function create(AiSetting $aiSetting): AiEngineInterface
    {
        try {
            return match ($aiSetting->engine) {

                AiEngine::OPENAI_CHATGPT->value => new OpenAiEngine($aiSetting),

                // 其他AI引擎实现...
                default => throw new Exception('Unsupported AI engine'),
            };
        } catch (Exception $e) {
            // 记录异常
            Log::error('Error creating AI engine', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
}
