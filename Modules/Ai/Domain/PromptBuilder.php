<?php

namespace Modules\Ai\Domain;

use Modules\Ai\Models\AiPromptTemplates;

class PromptBuilder
{
    public function build(AiPromptTemplates $template, string $content): string
    {
        return "Title: {$template->title}\n"
            ."Goal: {$template->goal}\n"
            ."Participants: {$template->participants}\n"
            ."Setting: {$template->setting}\n"
            ."Relationships: {$template->relationships}\n"
            ."Instructions: {$template->instructions}\n"
            ."Guidelines: {$template->guidelines}\n"
            ."Language: {$template->lang}\n"
            ."Style and Tone: {$template->style_tone}\n"
            ."\nContent:\n{$content}";
    }
}
