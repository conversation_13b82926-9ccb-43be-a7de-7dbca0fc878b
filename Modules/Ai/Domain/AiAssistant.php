<?php

namespace Modules\Ai\Domain;

/**
 * AI助手类，提供底层AI能力
 */
class AiAssistant
{
    protected AiEngineInterface $aiEngine;

    /**
     * 构造函数
     *
     * @param AiEngineInterface $aiEngine AI引擎实例
     */
    public function __construct(AiEngineInterface $aiEngine)
    {
        $this->aiEngine = $aiEngine;
    }

    /**
     * 生成AI响应
     *
     * @param string $prompt 提示词
     * @param array $parameters 额外参数
     * @return string 生成的内容
     */
    public function generateResponse(string $prompt, array $parameters = []): string
    {
        $response = $this->aiEngine->generateResponse($prompt, $parameters);
        return $response['content'] ?? '';
    }
}
