<?php

namespace Modules\Ai\Domain\Engine;

use Exception;
use Modules\Ai\Domain\AiEngineInterface;
use Modules\Ai\Models\AiSetting;
use Illuminate\Support\Facades\Log;

/**
 * OpenAI 引擎类，用于生成内容。
 */
class OpenAiEngine implements AiEngineInterface
{
    protected string $api_key;
    protected string $default_model;

    /**
     * 构造函数。
     *
     * @param AiSetting $aiSetting AI 设置。
     */
    public function __construct(AiSetting $aiSetting)
    {
        $this->api_key = $aiSetting->api_key;
        $this->default_model = $aiSetting->default_model;
    }

    /**
     * 生成响应。
     *
     * @param string $prompt 提示词。
     * @param array $parameters 参数数组。
     * @return array 生成的响应。
     * @throws Exception
     */
    public function generateResponse(string $prompt, array $parameters = []): array
    {
        try {
            $apiUrl = 'https://api.openai.com/v1/chat/completions';
            $requestData = [
                'model' => $this->default_model,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => (int) $parameters['max_token']
            ];

            $postFields = json_encode($requestData);
            $curl = curl_init($apiUrl);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer '.$this->api_key,
                'Content-Type: application/json'
            ]);
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postFields);

            // 设置超时时间（例如 30 秒）
            curl_setopt($curl, CURLOPT_TIMEOUT, 30);

            // 启用详细的 cURL 日志
            $logFilePath = '/tmp/curl.log';  // 设置为有效的路径
            curl_setopt($curl, CURLOPT_VERBOSE, true);
            curl_setopt($curl, CURLOPT_STDERR, fopen($logFilePath, 'w'));

            // 增加 DNS 解析超时时间
            curl_setopt($curl, CURLOPT_DNS_CACHE_TIMEOUT, 3600);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);

            // 处理响应
            if ($httpCode == 200) {
                $data = json_decode($response, true);
                return [
                    'status' => true,
                    'content' => $data['choices'][0]['message']['content']
                ];
            } else {
                Log::error('Error in OpenAiEngine', ['httpCode' => $httpCode, 'response' => $response, 'curlError' => $curlError]);
                return [
                    'status' => false,
                    'message' => $this->handleError($httpCode)
                ];
            }
        } catch (Exception $e) {
            Log::error('Error in OpenAiEngine', ['error' => $e->getMessage()]);
            throw $e;
        }
    }


    /**
     * 处理错误。
     *
     * @param int $httpCode HTTP 状态码。
     * @return string 错误信息。
     */
    protected function handleError(int $httpCode): string
    {
        return match ($httpCode) {
            401 => "Unauthorized access.",
            404 => "Resource not found.",
            429 => "You exceeded your current quota, please check your plan and billing details.",
            default => "Unable to generate content",
        };
    }
}
