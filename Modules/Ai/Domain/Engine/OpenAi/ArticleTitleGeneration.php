<?php

namespace Modules\Ai\Domain\Engine\OpenAi;

/**
 * 文章标题生成类。
 */
class ArticleTitleGeneration extends BaseGeneration
{
    /**
     * 获取上下文信息。
     *
     * @param array $parameters 参数数组。
     * @return string 上下文信息。
     */
    protected function getContext(array $parameters): string
    {
        $templates = [
            "zh-CN" => "为儿童节生成文章标题，关键词：".$parameters['priority_keywords'],
            "zh-TW" => "為兒童節生成文章標題，關鍵詞：".$parameters['priority_keywords'],
            "en" => "Generate an article title for Children's Day, keywords: ".$parameters['priority_keywords'],
        ];

        return $templates[$parameters['lang']] ?? $templates['en'];
    }

    /**
     * 获取任务目标。
     *
     * @param array $parameters 参数数组。
     * @return string 任务目标。
     */
    protected function getObjective(array $parameters): string
    {
        return "Create an engaging title.";
    }

    /**
     * 获取写作风格。
     *
     * @param array $parameters 参数数组。
     * @return string 写作风格。
     */
    protected function getStyle(array $parameters): string
    {
        return "default";
    }

    /**
     * 获取目标受众。
     *
     * @param array $parameters 参数数组。
     * @return string 目标受众。
     */
    protected function getAudience(array $parameters): string
    {
        return "general";
    }

    /**
     * 获取输出格式。
     *
     * @param array $parameters 参数数组。
     * @return string 输出格式。
     */
    protected function getResponseFormat(array $parameters): string
    {
        $templates = [
            "zh-CN" => "确保标题保持以下语调：".$this->translateEditorialTone($parameters)."\n\n",
            "zh-TW" => "確保標題保持以下語調：".$this->translateEditorialTone($parameters)."\n\n",
            "en" => "Ensure the title maintains a tone of: ".$this->translateEditorialTone($parameters)."\n\n",
        ];

        return $templates[$parameters['lang']] ?? $templates['en'];
    }
}
