<?php

namespace Modules\Ai\Domain\Engine\OpenAi;

/**
 * 简短描述生成类。
 */
class ShortDetailsGeneration extends BaseGeneration
{
    /**
     * 获取上下文信息。
     *
     * @param array $parameters 参数数组。
     * @return string 上下文信息。
     */
    protected function getContext(array $parameters): string
    {
        $templates = [
            "zh-CN" => "为儿童节生成简短描述，关键词：".$parameters['priority_keywords'],
            "zh-TW" => "為兒童節生成簡短描述，關鍵詞：".$parameters['priority_keywords'],
            "en" => "Generate short details for Children's Day, keywords: ".$parameters['priority_keywords'],
        ];

        return $templates[$parameters['lang']] ?? $templates['en'];
    }

    /**
     * 获取任务目标。
     *
     * @param array $parameters 参数数组。
     * @return string 任务目标。
     */
    protected function getObjective(array $parameters): string
    {
        return "Provide concise information.";
    }

    /**
     * 获取写作风格。
     *
     * @param array $parameters 参数数组。
     * @return string 写作风格。
     */
    protected function getStyle(array $parameters): string
    {
        return "default";
    }

    /**
     * 获取目标受众。
     *
     * @param array $parameters 参数数组。
     * @return string 目标受众。
     */
    protected function getAudience(array $parameters): string
    {
        return "general";
    }

    /**
     * 获取输出格式。
     *
     * @param array $parameters 参数数组。
     * @return string 输出格式。
     */
    protected function getResponseFormat(array $parameters): string
    {
        $templates = [
            "zh-CN" => "确保描述保持以下语调：".$this->translateEditorialTone($parameters)."\n\n",
            "zh-TW" => "確保描述保持以下語調：".$this->translateEditorialTone($parameters)."\n\n",
            "en" => "Ensure the details maintain a tone of: ".$this->translateEditorialTone($parameters)."\n\n",
        ];

        return $templates[$parameters['lang']] ?? $templates['en'];
    }
}
