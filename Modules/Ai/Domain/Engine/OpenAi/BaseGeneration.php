<?php

namespace Modules\Ai\Domain\Engine\OpenAi;

use Modules\Ai\Domain\AiFunctionalityInterface;

/**
 * 使用 CO-STAR 模板生成提示的基类。
 */
abstract class BaseGeneration implements AiFunctionalityInterface
{
    /**
     * 使用 CO-STAR 模板生成结构化提示。
     *
     * @param array $parameters 生成提示的参数。
     * @return string 结构化提示。
     */
    public function generatePrompt(array $parameters): string
    {
        $context = $this->getContext($parameters);
        $objective = $this->getObjective($parameters);
        $style = $this->getStyle($parameters);
        $tone = $this->getTone($parameters);
        $audience = $this->getAudience($parameters);
        $responseFormat = $this->getResponseFormat($parameters);

        $templates = [
            "zh-CN" => "上下文: $context\n目标: $objective\n风格: $style\n语气: $tone\n受众: $audience\n输出格式: $responseFormat\n",
            "zh-TW" => "上下文: $context\n目標: $objective\n風格: $style\n語氣: $tone\n受眾: $audience\n輸出格式: $responseFormat\n",
            "en" => "Context: $context\nObjective: $objective\nStyle: $style\nTone: $tone\nAudience: $audience\nResponse Format: $responseFormat\n",
        ];

        return $templates[$parameters['lang']] ?? $templates['en'];
    }

    /**
     * 获取上下文信息。
     *
     * @param array $parameters 参数数组。
     * @return string 上下文信息。
     */
    abstract protected function getContext(array $parameters): string;

    /**
     * 获取任务目标。
     *
     * @param array $parameters 参数数组。
     * @return string 任务目标。
     */
    abstract protected function getObjective(array $parameters): string;

    /**
     * 获取写作风格。
     *
     * @param array $parameters 参数数组。
     * @return string 写作风格。
     */
    abstract protected function getStyle(array $parameters): string;

    /**
     * 获取情感语气。
     *
     * @param array $parameters 参数数组。
     * @return string 情感语气。
     */
    protected function getTone(array $parameters): string
    {
        return $this->translateEditorialTone($parameters);
    }

    /**
     * 获取目标受众。
     *
     * @param array $parameters 参数数组。
     * @return string 目标受众。
     */
    abstract protected function getAudience(array $parameters): string;

    /**
     * 获取输出格式。
     *
     * @param array $parameters 参数数组。
     * @return string 输出格式。
     */
    abstract protected function getResponseFormat(array $parameters): string;

    /**
     * 翻译情感语气。
     *
     * @param array $parameters 参数数组。
     * @return string 翻译后的情感语气。
     */
    protected function translateEditorialTone(array $parameters): string
    {
        $tone = [
            'formal' => [
                "zh-CN" => "正式的",
                "zh-TW" => "正式的",
                "en" => "formal",
            ],
            'casual' => [
                "zh-CN" => "随意的",
                "zh-TW" => "隨意的",
                "en" => "casual",
            ],
            'intimate' => [
                "zh-CN" => "亲密",
                "zh-TW" => "親密",
                "en" => "intimate",
            ],
            'witty' => [
                "zh-CN" => "机智",
                "zh-TW" => "機智",
                "en" => "witty",
            ],
            'educational' => [
                "zh-CN" => "教育性的",
                "zh-TW" => "教育性的",
                "en" => "educational",
            ],
            'inspirational' => [
                "zh-CN" => "鼓舞人心的",
                "zh-TW" => "鼓舞人心的",
                "en" => "inspirational",
            ],
            'empathetic' => [
                "zh-CN" => "有同情心的",
                "zh-TW" => "有同理心的",
                "en" => "empathetic",
            ],
            'persuasive' => [
                "zh-CN" => "有说服力的",
                "zh-TW" => "有說服力的",
                "en" => "persuasive",
            ],
            'authoritative' => [
                "zh-CN" => "权威的",
                "zh-TW" => "權威的",
                "en" => "authoritative",
            ],
            'humorous' => [
                "zh-CN" => "幽默的",
                "zh-TW" => "幽默的",
                "en" => "humorous",
            ]
        ];

        return $tone[$parameters['editorial_tone']][$parameters['lang']] ?? $parameters['editorial_tone'];
    }
}
