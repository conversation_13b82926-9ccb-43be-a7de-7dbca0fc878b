<?php

namespace Modules\Ai\Domain\Engine\OpenAi;

/**
 * 文章生成类。
 */
class ArticleGeneration extends BaseGeneration
{
    /**
     * 获取上下文信息。
     *
     * @param array $parameters 参数数组。
     * @return string 上下文信息。
     */
    protected function getContext(array $parameters): string
    {
        $templates = [
            "zh-CN" => "在这个主题上写一篇完整的文章: ".$parameters['primary_focus'],
            "zh-TW" => "在這個主題上寫一篇完整的文章: ".$parameters['primary_focus'],
            "en" => "Write a complete article on this topic: ".$parameters['primary_focus'],
        ];

        return $templates[$parameters['lang']] ?? $templates['en'];
    }

    /**
     * 获取任务目标。
     *
     * @param array $parameters 参数数组。
     * @return string 任务目标。
     */
    protected function getObjective(array $parameters): string
    {
        $templates = [
            "zh-CN" => "创建一个引人注目的文章标题：\n\n在文章中运用以下关键词：".$parameters['priority_keywords']."\n\n",
            "zh-TW" => "創建一個引人注目的部落格標題：\n\n在文章中運用以下關鍵詞：".$parameters['priority_keywords']."\n\n",
            "en" => "Create a compelling article using the following keywords: ".$parameters['priority_keywords'],
        ];

        return $templates[$parameters['lang']] ?? $templates['en'];
    }

    /**
     * 获取写作风格。
     *
     * @param array $parameters 参数数组。
     * @return string 写作风格。
     */
    protected function getStyle(array $parameters): string
    {
        return "default";
    }

    /**
     * 获取目标受众。
     *
     * @param array $parameters 参数数组。
     * @return string 目标受众。
     */
    protected function getAudience(array $parameters): string
    {
        return "general";
    }

    /**
     * 获取输出格式。
     *
     * @param array $parameters 参数数组。
     * @return string 输出格式。
     */
    protected function getResponseFormat(array $parameters): string
    {
        $templates = [
            "zh-CN" => "确保文章保持以下语调：".$this->translateEditorialTone($parameters)."\n\n",
            "zh-TW" => "確保文章保持以下語調：".$this->translateEditorialTone($parameters)."\n\n",
            "en" => "Ensure the article maintains a tone of: ".$this->translateEditorialTone($parameters)."\n\n",
        ];

        return $templates[$parameters['lang']] ?? $templates['en'];
    }
}
