<?php
/*
 * @Author: jay <EMAIL>
 * @Date: 2025-04-12 12:00:21
 * @LastEditors: jay <EMAIL>
 * @LastEditTime: 2025-04-12 14:18:35
 * @FilePath: /bwms/Modules/Ai/Services/LLM/LLMResponseGenerator.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

namespace Modules\Ai\Services\LLM;

use Illuminate\Support\Collection;
use Modules\Ai\Services\Tools\ToolRegistry;
use Psr\Log\LoggerInterface;

class LLMResponseGenerator
{
    private LLMInterface $llm;
    private LoggerInterface $logger;
    
    public function __construct(LLMInterface $llm, LoggerInterface $logger)
    {
        $this->llm = $llm;
        $this->logger = $logger;
    }

    /**
     * 生成LLM响应
     *
     * @param string $message 用户消息
     * @param array $session 会话数据
     * @param array $options 额外选项
     * @return array 响应内容
     */
    public function generate(string $message, array $session, array $options = []): array
    {
        try {
            $messages = $this->buildMessages($session);
            $response = $this->llm->chat($messages);
            
            return [
                'success' => true,
                'content' => $response
            ];
        } catch (\Exception $e) {
            $this->logger->error('LLM回复生成失败', [
                'error' => $e->getMessage(),
                'session_id' => $session['id'] ?? null
            ]);
            
            return [
                'success' => false,
                'error' => "抱歉，我在处理您的问题时遇到了困难。请稍后再试。"
            ];
        }
    }

    /**
     * 构建消息列表
     */
    private function buildMessages(array $session): array
    {
        $messages = [];
        
        // 添加系统提示
        $messages[] = [
            'role' => 'system',
            'content' => $this->buildSystemPrompt()
        ];

        // 添加历史消息
        $recentHistory = array_slice($session['history'] ?? [], -10);
        foreach ($recentHistory as $msg) {
            $messages[] = [
                'role' => $msg['role'],
                'content' => $msg['content']
            ];
        }

        return $messages;
    }

    /**
     * 构建系统提示词
     */
    private function buildSystemPrompt(): string
    {
        $systemPrompt = "你是一个文档处理助手，专门帮助用户进行文本替换、批量修改等操作。";
        
        $toolDescriptions = Collection::make(ToolRegistry::getToolDescriptions())
            ->map(fn($tool) => "{$tool['name']}: {$tool['description']}")
            ->implode("\n");
            
        return $systemPrompt . "\n\n可用工具：\n" . $toolDescriptions;
    }
}