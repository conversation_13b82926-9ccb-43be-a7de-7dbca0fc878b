<?php

namespace Modules\Ai\Services\LLM;

use Modules\Ai\Services\LLM\Providers\OpenAIProvider;
use Modules\Ai\Services\LLM\Providers\YunwuProvider;
use Modules\Ai\Services\LLM\Providers\DeepSeekProvider;

class LLMService implements LLMInterface
{
    private LLMInterface $provider;

    public function __construct(array $config = [])
    {
        $config = $config ?: config('services.ai');
        $provider = $config['provider'] ?? 'openai';

        $this->provider = match ($provider) {
            'openai' => new OpenAIProvider($config),
            'yunwu' => new YunwuProvider($config),
            'deepseek' => new DeepSeekProvider($config),
            default => throw new \InvalidArgumentException("不支持的AI提供商: {$provider}")
        };
    }

    public function chat(array $messages, array $options = []): string
    {
        return $this->provider->chat($messages, $options);
    }

    public function complete(array $options): string
    {
        return $this->provider->complete($options);
    }
}
