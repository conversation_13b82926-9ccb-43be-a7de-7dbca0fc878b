<?php

namespace Modules\Ai\Services\LLM\Providers;

use Illuminate\Support\Facades\Log;
use Modules\Ai\Services\LLM\LLMInterface;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Arr;

class YunwuProvider implements LLMInterface
{
    private array $config;
    protected string $baseUri;
    protected string $apiKey;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->apiKey = $config['yunwu_api_key'] ?? env('YUNWU_API_KEY', '');
        $this->baseUri = env('YUNWU_API_BASE_URI', 'https://yunwu.ai');
    }


    public function chat(array $messages, array $options = []): string
    {
        try {
            $defaultParams = [
                'model' => $options['model'] ?? $this->config['model'] ?? 'gpt-3.5-turbo-instruct',
                'max_tokens' => $options['max_tokens'] ?? 1000,
                'temperature' => $options['temperature'] ?? 0.7,
            ];

            $payload = array_merge(
                $defaultParams,
                ['messages' => $messages]
            );

            // 创建HTTP客户端并设置重试逻辑
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ])
                ->timeout(60)
                ->withoutVerifying()
                ->post($this->baseUri . '/v1/chat/completions', $payload);

            if ($response->successful()) {
                $response_json = $response->json();

                $result = Arr::get($response_json, 'choices.0.message.content', '');
                app('log')->info('YunwuProvider', ['response' => $response->json(), 'result' => $result, 'payload' => $payload]);
                if (!isset($result)) {
                    throw new \Exception('无效的API响应格式');
                }
                return $result;
            }
            $errorMessage = $response->json()['error']['message'] ?? '未知错误';
            throw new \Exception("API请求失败 (HTTP " . $response->status() . "): $errorMessage");
        } catch (\Exception $e) {

            Log::error('Yunwu异常', [
                'error' => $e->getMessage(),
                'messages' => $messages
            ]);
            throw $e;
        }
    }

    public function complete(array $options): string
    {
        return $this->chat([
            ['role' => 'user', 'content' => $options['prompt']]
        ], $options);
    }

    /**
     * 将消息数组转换为提示词字符串
     *
     * @param array $messages
     * @return string
     */
    private function convertMessagesToPrompt(array $messages): string
    {
        $prompt = '';
        foreach ($messages as $message) {
            $role = $message['role'] ?? 'user';
            $content = $message['content'] ?? '';

            switch ($role) {
                case 'system':
                    $prompt .= "System: $content\n";
                    break;
                case 'assistant':
                    $prompt .= "Assistant: $content\n";
                    break;
                case 'user':
                default:
                    $prompt .= "User: $content\n";
                    break;
            }
        }
        return trim($prompt);
    }
}
