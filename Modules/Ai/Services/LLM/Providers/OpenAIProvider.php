<?php
/*
 * @Author: jay <EMAIL>
 * @Date: 2025-04-09 19:00:37
 * @LastEditors: jay <EMAIL>
 * @LastEditTime: 2025-04-11 10:50:22
 * @FilePath: /bwms/Modules/Ai/Services/LLM/Providers/OpenAIProvider.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

namespace Modules\Ai\Services\LLM\Providers;

use Modules\Ai\Services\LLM\LLMInterface;
use Illuminate\Support\Facades\Log;

class OpenAIProvider implements LLMInterface
{
    private array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    public function chat(array $messages, array $options = []): string
    {
        $apiKey = $this->config['api_key'] ?? env('OPENAI_API_KEY');
        $model = $options['model'] ?? $this->config['model'] ?? 'gpt-4';

        $payload = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => $options['max_tokens'] ?? 1000,
            'temperature' => $options['temperature'] ?? 0.7,
        ];

        if (isset($options['response_format'])) {
            $payload['response_format'] = $options['response_format'];
        }

        try {
            $ch = curl_init('https://api.openai.com/v1/chat/completions');
            
            // 设置代理
            $proxy = env('HTTP_PROXY', '127.0.0.1:7890');
            if ($proxy) {
                curl_setopt($ch, CURLOPT_PROXY, $proxy);
                curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_HTTP);
            }
            
            // 设置请求头和数据
            curl_setopt_array($ch, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . $apiKey,
                    'Content-Type: application/json',
                ],
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($payload),
                CURLOPT_TIMEOUT => 30,
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                throw new \Exception('cURL错误: ' . $error);
            }
            
            $data = json_decode($response, true);
            
            if ($httpCode === 200) {
                return $data['choices'][0]['message']['content'] ?? '';
            }
            
            throw new \Exception('OpenAI API请求失败: ' . ($data['error']['message'] ?? '未知错误'));
        } catch (\Exception $e) {
            Log::error('OpenAI调用异常', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function complete(array $options): string
    {
        return $this->chat([
            ['role' => 'user', 'content' => $options['prompt']]
        ], $options);
    }
}
