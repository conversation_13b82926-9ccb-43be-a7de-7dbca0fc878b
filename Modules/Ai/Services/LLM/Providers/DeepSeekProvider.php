<?php
/*
 * @Author: jay <EMAIL>
 * @Date: 2025-01-12 20:40:00
 * @LastEditors: jay <EMAIL>
 * @LastEditTime: 2025-01-12 20:40:00
 * @FilePath: /bwms/Modules/Ai/Services/LLM/Providers/DeepSeekProvider.php
 * @Description: DeepSeek API Provider
 */

namespace Modules\Ai\Services\LLM\Providers;

use Modules\Ai\Services\LLM\LLMInterface;
use Illuminate\Support\Facades\Log;

class DeepSeekProvider implements LLMInterface
{
    private array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    public function chat(array $messages, array $options = []): string
    {
        $apiKey = $this->config['deepseek_api_key'] ?? env('DEEPSEEK_API_KEY');
        $model = 'deepseek-chat';

        $payload = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => $options['max_tokens'] ?? 1000,
            'temperature' => $options['temperature'] ?? 0.7,
            'stream' => false,
        ];

        // DeepSeek支持的额外参数
        if (isset($options['top_p'])) {
            $payload['top_p'] = $options['top_p'];
        }

        if (isset($options['frequency_penalty'])) {
            $payload['frequency_penalty'] = $options['frequency_penalty'];
        }

        if (isset($options['presence_penalty'])) {
            $payload['presence_penalty'] = $options['presence_penalty'];
        }

        if (isset($options['stop'])) {
            $payload['stop'] = $options['stop'];
        }

        try {
            // DeepSeek API端点
            $ch = curl_init('https://api.deepseek.com/v1/chat/completions');
            
            // 设置代理（如果需要）
            $proxy = env('HTTP_PROXY');
            if ($proxy) {
                curl_setopt($ch, CURLOPT_PROXY, $proxy);
                curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_HTTP);
            }
            
            // 设置请求头和数据
            curl_setopt_array($ch, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . $apiKey,
                    'Content-Type: application/json',
                    'Accept: application/json',
                ],
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($payload),
                CURLOPT_TIMEOUT => 60, // DeepSeek可能需要更长的超时时间
                CURLOPT_CONNECTTIMEOUT => 10,
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                throw new \Exception('cURL错误: ' . $error);
            }
            
            $data = json_decode($response, true);
            
            if ($httpCode === 200) {
                if (isset($data['choices'][0]['message']['content'])) {
                    return $data['choices'][0]['message']['content'];
                } else {
                    Log::warning('DeepSeek响应格式异常', ['response' => $data]);
                    throw new \Exception('DeepSeek API响应格式异常');
                }
            }
            
            // 处理错误响应
            $errorMessage = 'DeepSeek API请求失败';
            if (isset($data['error'])) {
                if (is_array($data['error'])) {
                    $errorMessage .= ': ' . ($data['error']['message'] ?? json_encode($data['error']));
                } else {
                    $errorMessage .= ': ' . $data['error'];
                }
            } else {
                $errorMessage .= ': HTTP ' . $httpCode;
            }
            
            throw new \Exception($errorMessage);
            
        } catch (\Exception $e) {
            Log::error('DeepSeek调用异常', [
                'error' => $e->getMessage(),
                'payload' => $payload,
                'http_code' => $httpCode ?? null,
                'response' => $response ?? null
            ]);
            throw $e;
        }
    }

    public function complete(array $options): string
    {
        return $this->chat([
            ['role' => 'user', 'content' => $options['prompt']]
        ], $options);
    }

    /**
     * 获取支持的模型列表
     *
     * @return array
     */
    public function getSupportedModels(): array
    {
        return [
            'deepseek-chat' => 'DeepSeek Chat',
            'deepseek-coder' => 'DeepSeek Coder',
        ];
    }

    /**
     * 检查API密钥是否有效
     *
     * @return bool
     */
    public function validateApiKey(): bool
    {
        try {
            $this->chat([
                ['role' => 'user', 'content' => 'Hello']
            ], ['max_tokens' => 10]);
            return true;
        } catch (\Exception $e) {
            Log::warning('DeepSeek API密钥验证失败', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 获取模型信息
     *
     * @param string $model
     * @return array
     */
    public function getModelInfo(): array
    {
        $model = $model ?? $this->config['model'] ?? 'deepseek-chat';
        
        $modelInfo = [
            'deepseek-chat' => [
                'name' => 'DeepSeek Chat',
                'description' => 'DeepSeek的通用对话模型',
                'max_tokens' => 4096,
                'context_length' => 32768,
            ],
            'deepseek-coder' => [
                'name' => 'DeepSeek Coder',
                'description' => 'DeepSeek的编程专用模型',
                'max_tokens' => 4096,
                'context_length' => 16384,
            ],
        ];

        return $modelInfo[$model] ?? [
            'name' => $model,
            'description' => '未知模型',
            'max_tokens' => 4096,
            'context_length' => 4096,
        ];
    }

    /**
     * 流式聊天（如果DeepSeek支持）
     *
     * @param array $messages
     * @param callable $callback
     * @param array $options
     * @return void
     */
    public function streamChat(array $messages, callable $callback, array $options = []): void
    {
        $apiKey = $this->config['api_key'] ?? env('DEEPSEEK_API_KEY');
        $model = $options['model'] ?? $this->config['model'] ?? 'deepseek-chat';

        $payload = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => $options['max_tokens'] ?? 1000,
            'temperature' => $options['temperature'] ?? 0.7,
            'stream' => true,
        ];

        try {
            $ch = curl_init('https://api.deepseek.com/v1/chat/completions');
            
            curl_setopt_array($ch, [
                CURLOPT_RETURNTRANSFER => false,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . $apiKey,
                    'Content-Type: application/json',
                    'Accept: text/event-stream',
                ],
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($payload),
                CURLOPT_TIMEOUT => 60,
                CURLOPT_WRITEFUNCTION => function($ch, $data) use ($callback) {
                    // 处理SSE数据流
                    if (strpos($data, 'data: ') === 0) {
                        $jsonData = substr($data, 6);
                        if (trim($jsonData) === '[DONE]') {
                            return strlen($data);
                        }
                        
                        $decoded = json_decode(trim($jsonData), true);
                        if ($decoded && isset($decoded['choices'][0]['delta']['content'])) {
                            $callback($decoded['choices'][0]['delta']['content']);
                        }
                    }
                    return strlen($data);
                },
            ]);
            
            curl_exec($ch);
            curl_close($ch);
            
        } catch (\Exception $e) {
            Log::error('DeepSeek流式调用异常', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
} 