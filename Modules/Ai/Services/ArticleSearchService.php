<?php

declare(strict_types=1);

namespace Modules\Ai\Services;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Cms\Models\CmsContent;
use Modules\Cms\Models\CmsNews;
use Modules\Cms\Models\CmsPage;
use Modules\Cms\Models\CmsProduct;
use Modules\Cms\Models\CmsModel;
use Modules\Cms\Models\CmsCases; // Added CmsCases import

/**
 * 文章搜索服务
 * 
 * 提供统一的文章搜索接口，支持多种内容类型的搜索
 */
 class ArticleSearchService
{
    /**
     * 支持的模块类型映射
     */
    private const MODULE_MAPPING = [
        'news' => [
            'model' => CmsNews::class,
            'table' => 'cms_m_news',
            'name' => '新闻'
        ],
        'pages' => [
            'model' => CmsPage::class,
            'table' => 'cms_m_page',
            'name' => '页面'
        ],
        'products' => [
            'model' => CmsProduct::class,
            'table' => 'cms_m_product',
            'name' => '产品'
        ],
        'blogs' => [
            'model' => CmsNews::class, // 博客可能也使用news表
            'table' => 'cms_m_news',
            'name' => '博客'
        ],
        'cases' => [
            'model' => CmsCases::class,
            'table' => 'cms_m_cases',
            'name' => '案例'
        ]
    ];

    /**
     * 模型ID到模型类的直接映射（基于数据库实际数据）
     */
    private const MODEL_ID_MAPPING = [
        1 => CmsNews::class,      // news
        2 => CmsCases::class,     // cases  
        3 => CmsProduct::class,   // product
        4 => null,                // job (暂无对应模型)
        5 => CmsPage::class,      // page
        6 => null,                // message (暂无对应模型)
        7 => null,                // downloads (暂无对应模型)
    ];

    /**
     * 根据关键词搜索文章
     *
     * @param string $keyword 搜索关键词
     * @param string $moduleType 模块类型 (news|pages|products|blogs|all)
     * @param int $limit 返回数量限制
     * @return array 搜索结果
     */
    public function searchByKeyword(string $keyword, string $moduleType = 'all', int $limit = 50): array
    {
        // 暂时注释掉关键词检查，允许空关键词查询所有文章
        // if (empty($keyword)) {
        //     return [];
        // }

        // 简化搜索逻辑，只查询 cms_content 表
        return $this->searchInCmsContent($keyword, $moduleType, $limit);
    }

    /**
     * 在指定模块中搜索
     *
     * @param string $keyword 搜索关键词
     * @param string $moduleType 模块类型
     * @param int $limit 限制数量
     * @return array
     */
    private function searchInModule(string $keyword, string $moduleType, int $limit): array
    {
        if (!isset(self::MODULE_MAPPING[$moduleType])) {
            return [];
        }

        $config = self::MODULE_MAPPING[$moduleType];
        $results = [];

        try {
            // 1. 先从CmsContent主表搜索
            $contentResults = $this->searchInCmsContent($keyword, $moduleType, $limit);
            $results = array_merge($results, $contentResults);

            // 2. 再从具体模块表搜索
            $moduleResults = $this->searchInModuleTable($keyword, $config, $limit);
            $results = array_merge($results, $moduleResults);

            // 去重（基于ID）
            $results = $this->removeDuplicates($results);

        } catch (\Exception $e) {
            \Log::error("搜索模块 {$moduleType} 时出错: " . $e->getMessage());
        }

        return $results;
    }

    /**
     * 在CmsContent主表中搜索
     *
     * @param string $keyword 搜索关键词
     * @param string $moduleType 模块类型
     * @param int $limit 限制数量
     * @return array
     */
    private function searchInCmsContent(string $keyword, string $moduleType, int $limit): array
    {
        try {
            $query = CmsContent::query()
                ->select([
                    'id',
                    'title',
                    'model_id',
                    'created_at'
                ])
                // ->where('status', 1) // 只搜索已发布的内容
                // ->where('verify_status', CmsContent::STATUS_APPROVED) // 只搜索审核通过的内容
                ->orderBy('created_at', 'desc'); // 按创建时间倒序
                
            // 如果有关键词，添加关键词搜索条件
            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->where('title', 'like', "%{$keyword}%");
                });
            }

            // 如果指定了模块类型，通过模型ID进行过滤
            if ($moduleType !== 'all') {
                $modelIds = $this->getModelIdsByType($moduleType);
                if (!empty($modelIds)) {
                    $query->whereIn('model_id', $modelIds);
                }
            }

            $contents = $query->limit($limit)->get();
            return $contents->map(function($content) use ($keyword) {
                return $this->formatSimpleResult($content, $keyword);
            })->toArray();

        } catch (\Exception $e) {
            \Log::error("搜索CmsContent时出错: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 在具体模块表中搜索
     *
     * @param string $keyword 搜索关键词
     * @param array $config 模块配置
     * @param int $limit 限制数量
     * @return array
     */
    private function searchInModuleTable(string $keyword, array $config, int $limit): array
    {
        try {
            $modelClass = $config['model'];
            $tableName = $config['table'];

            $query = $modelClass::query()
                ->select(['id', 'content', 'created_at', 'updated_at']);
                
            // 如果有关键词，添加关键词搜索条件
            if (!empty($keyword)) {
                $query->where('content', 'like', "%{$keyword}%");
            }

            $results = $query->limit($limit)->get();

            return $results->map(function($item) use ($config) {
                return $this->formatModuleResult($item, $config);
            })->toArray();

        } catch (\Exception $e) {
            \Log::error("搜索模块表 {$config['table']} 时出错: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 根据ID获取文章详情
     *
     * @param int $articleId 文章ID
     * @param string $moduleType 模块类型
     * @return array|null
     */
    public function getArticleById(int $articleId, string $moduleType = 'all'): ?array
    {
        try {
            // 从CmsContent获取，使用简化格式
            $content = CmsContent::query()
                ->select([
                    'id',
                    'title',
                    'category_id',
                    'model_id',
                    'created_at'
                ])
                ->where('id', $articleId)
                // ->where('status', 1)
                ->whereNotNull('title')
                // ->where('verify_status', CmsContent::STATUS_APPROVED)
                ->first();
                
            if ($content) {
                return $this->formatSimpleResult($content);
            }

            return null;

        } catch (\Exception $e) {
            \Log::error("获取文章详情时出错: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取文章详细内容（包含所有字段）
     *
     * @param int $articleId 文章ID
     * @return array|null
     */
    public function getArticleDetail(int $articleId): ?array
    {
        try {
            // 从CmsContent获取基本信息
            $content = CmsContent::query()
                ->select([
                    'id',
                    'title',
                    'summary',
                    'model_id',
                    'created_at'
                ])
                ->where('id', $articleId)
                // ->where('status', 1)
                ->first();
                
            if (!$content) {
                return null;
            }

            // 获取对应模型表中的详细内容
            $modelClass = $this->getModelClassByModelId($content->model_id);
            $detailContent = '';
            
            if ($modelClass) {
                try {
                    $modelItem = $modelClass::where('id', $articleId)->first();
                    if ($modelItem && isset($modelItem->content)) {
                        $detailContent = $modelItem->content ?? '';
                    }
                } catch (\Exception $e) {
                    \Log::warning("获取模型详情时出错", [
                        'articleId' => $articleId,
                        'modelClass' => $modelClass,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            return [
                'id' => $content->id,
                'title' => $content->title ?? '',
                'summary' => $content->summary ?? '',
                'content' => $detailContent,
                'model_id' => $content->model_id,
                'created_at' => $content->created_at->format('Y-m-d H:i:s')
            ];

        } catch (\Exception $e) {
            \Log::error("获取文章详细内容时出错", [
                'articleId' => $articleId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 批量替换文章内容
     *
     * @param array $articleIds 文章ID数组
     * @param string $sourceText 原文本
     * @param string $targetText 目标文本
     * @param string $moduleType 模块类型
     * @return array 替换结果
     */
    public function batchReplaceContent(array $articleIds, string $sourceText, string $targetText, string $moduleType = 'all'): array
    {
        $results = [];
        
        foreach ($articleIds as $articleId) {
            $result = $this->replaceArticleContent($articleId, $sourceText, $targetText, $moduleType);
            $results[] = $result;
        }

        return $results;
    }

    /**
     * 替换单篇文章内容
     *
     * @param int $articleId 文章ID
     * @param string $sourceText 原文本
     * @param string $targetText 目标文本
     * @param string $moduleType 模块类型
     * @return array 替换结果
     */
    public function replaceArticleContent(int $articleId, string $sourceText, string $targetText, string $moduleType = 'all'): array
    {
        try {
            $replacementCount = 0;
            $processedSuccessfully = false;

            // 先从CmsContent主表获取文章信息
            $content = CmsContent::find($articleId);
            if (!$content) {
                return [
                    'id' => $articleId,
                    'success' => false,
                    'replacementCount' => 0,
                    'error' => '文章不存在'
                ];
            }

            // 在CmsContent主表中替换标题和摘要
            $originalTitle = $content->title ?? '';
            $originalSummary = $content->summary ?? '';
            $newTitle = str_replace($sourceText, $targetText, $originalTitle);
            $newSummary = str_replace($sourceText, $targetText, $originalSummary);
            
            $titleReplacements = substr_count($originalTitle, $sourceText);
            $summaryReplacements = substr_count($originalSummary, $sourceText);

            // 如果有替换，更新CmsContent表
            if ($titleReplacements > 0 || $summaryReplacements > 0) {
                $content->title = $newTitle;
                $content->summary = $newSummary;
                $content->save();
                
                $replacementCount += $titleReplacements + $summaryReplacements;
            }
            
            // 标记CmsContent表处理成功
            $processedSuccessfully = true;

            // 根据model_id在对应的模型表中替换内容
            $modelClass = $this->getModelClassByModelId($content->model_id);
            if ($modelClass) {
                try {
                    $modelItem = $modelClass::where('id', $articleId)->first();
                    
                    if ($modelItem && isset($modelItem->content)) {
                        $originalContent = $modelItem->content ?? '';
                        $newContent = str_replace($sourceText, $targetText, $originalContent);
                        $contentReplacements = substr_count($originalContent, $sourceText);
                        
                        // 如果有替换，更新模型表
                        if ($contentReplacements > 0) {
                            $modelItem->content = $newContent;
                            $modelItem->save();
                            
                            $replacementCount += $contentReplacements;
                        }
                    }
                } catch (\Exception $e) {
                    \Log::warning("处理模型表时出错，但主表处理成功", [
                        'articleId' => $articleId,
                        'modelClass' => $modelClass,
                        'error' => $e->getMessage()
                    ]);
                    // 即使模型表处理失败，只要主表处理成功就算成功
                }
            }

            // 成功条件：能够找到文章并成功处理（无论是否有实际替换）
            $success = $processedSuccessfully;

            return [
                'id' => $articleId,
                'success' => $success,
                'replacementCount' => $replacementCount,
                'error' => null
            ];

        } catch (\Exception $e) {
            \Log::error("替换文章内容时出错: " . $e->getMessage());
            
            return [
                'id' => $articleId,
                'success' => false,
                'replacementCount' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 格式化简单结果（只包含必要字段）
     *
     * @param mixed $content
     * @param string|null $searchKeyword 搜索关键词，用于计算匹配数
     * @return array
     */
    private function formatSimpleResult($content, ?string $searchKeyword = null): array
    {
        // 直接使用 model_id 对应的模块名称作为分类
        $moduleName = $this->getModuleNameByModelId($content->model_id);
        $categoryName = $this->getModuleDisplayName($moduleName);
        
        // 计算真实匹配数
        $matches = 0;
        if (!empty($searchKeyword)) {
            $matches = $this->calculateMatches($content->id, $searchKeyword);
        }
        
        return [
            'id' => $content->id,
            'title' => $content->title,
            'category' => $categoryName,
            'module' => $moduleName,
            'model_id' => $content->model_id,
            'matches' => $matches,
            'created_at' => $content->created_at->format('Y-m-d'),
            'source' => 'cms_content'
        ];
    }

    /**
     * 计算单篇文章的匹配数
     *
     * @param int $articleId 文章ID
     * @param string $searchKeyword 搜索关键词
     * @return int 匹配数量
     */
    private function calculateMatches(int $articleId, string $searchKeyword): int
    {
        try {
            // 获取文章详细内容
            $articleDetail = $this->getArticleDetail($articleId);
            
            if (!$articleDetail) {
                return 0;
            }
            
            $matches = 0;
            $keyword = strtolower($searchKeyword);
            
            // 在标题中搜索匹配
            if (!empty($articleDetail['title'])) {
                $matches += substr_count(strtolower($articleDetail['title']), $keyword);
            }
            
            // 在摘要中搜索匹配
            if (!empty($articleDetail['summary'])) {
                $matches += substr_count(strtolower($articleDetail['summary']), $keyword);
            }
            
            // 在内容中搜索匹配
            if (!empty($articleDetail['content'])) {
                $matches += substr_count(strtolower($articleDetail['content']), $keyword);
            }
            
            return $matches;
            
        } catch (\Exception $e) {
            \Log::warning("计算文章匹配数时出错", [
                'articleId' => $articleId,
                'searchKeyword' => $searchKeyword,
                'error' => $e->getMessage()
            ]);
            
            // 出错时返回默认值
            return 1;
        }
    }

    /**
     * 格式化模块结果
     *
     * @param mixed $item
     * @param array $config
     * @return array
     */
    private function formatModuleResult($item, array $config): array
    {
        return [
            'id' => $item->id,
            'title' => $this->extractTitleFromContent($item->content),
            'content' => $item->content,
            'summary' => $this->extractSummaryFromContent($item->content),
            'module' => strtolower($config['name']),
            'category' => '',
            'author' => '',
            'created_at' => $item->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $item->updated_at->format('Y-m-d H:i:s'),
            'source' => $config['table']
        ];
    }

    /**
     * 获取详细内容
     *
     * @param int $contentId
     * @param int $modelId
     * @return string
     */
    private function getDetailContent(int $contentId, int $modelId): string
    {
        try {
            // 根据模型ID确定具体表
            $modelInfo = $this->getModelInfo($modelId);
            if (!$modelInfo) {
                return '';
            }

            $tableName = $modelInfo['table'];
            $result = DB::table($tableName)
                ->where('id', $contentId)
                ->first();

            return $result->content ?? '';

        } catch (\Exception $e) {
            \Log::error("获取详细内容时出错: " . $e->getMessage());
            return '';
        }
    }

    /**
     * 根据模型ID获取模型信息
     *
     * @param int $modelId
     * @return array|null
     */
    private function getModelInfo(int $modelId): ?array
    {
        try {
            $model = CmsModel::find($modelId);
            if (!$model) {
                return null;
            }

            // 根据模型名称映射到具体表
            $modelName = strtolower($model->name);
            foreach (self::MODULE_MAPPING as $key => $config) {
                if (strpos($modelName, $key) !== false) {
                    return $config;
                }
            }

            return null;

        } catch (\Exception $e) {
            \Log::error("获取模型信息时出错: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 根据模块类型获取模型ID
     *
     * @param string $moduleType
     * @return array
     */
    private function getModelIdsByType(string $moduleType): array
    {
        try {
            $modelName = self::MODULE_MAPPING[$moduleType]['name'] ?? '';
            if (empty($modelName)) {
                return [];
            }

            return CmsModel::where('name', 'like', "%{$modelName}%")
                ->pluck('id')
                ->toArray();

        } catch (\Exception $e) {
            \Log::error("获取模型ID时出错: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 根据模型ID获取模块名称
     *
     * @param int $modelId
     * @return string
     */
    private function getModuleNameByModelId(int $modelId): string
    {
        try {
            $model = CmsModel::find($modelId);
            if (!$model) {
                return 'pages'; // 默认为页面类型
            }

            $modelName = strtolower($model->name);
            
            // 根据模型名称关键词匹配到对应的模块类型
            if (strpos($modelName, 'news') !== false || strpos($modelName, '新闻') !== false) {
                return 'news';
            }
            if (strpos($modelName, 'product') !== false || strpos($modelName, '产品') !== false) {
                return 'products';
            }
            if (strpos($modelName, 'blog') !== false || strpos($modelName, '博客') !== false) {
                return 'blogs';
            }
            if (strpos($modelName, 'page') !== false || strpos($modelName, '页面') !== false) {
                return 'pages';
            }
            if (strpos($modelName, 'case') !== false || strpos($modelName, '案例') !== false) {
                return 'cases';
            }

            // 如果都不匹配，默认为页面类型
            return 'pages';

        } catch (\Exception $e) {
            \Log::error("获取模块名称时出错: " . $e->getMessage());
            return 'pages'; // 出错时默认为页面类型
        }
    }

    /**
     * 根据模型ID获取对应的模型类
     *
     * @param int $modelId
     * @return string|null
     */
    private function getModelClassByModelId(int $modelId): ?string
    {
        try {
            // 优先使用直接映射
            if (isset(self::MODEL_ID_MAPPING[$modelId])) {
                return self::MODEL_ID_MAPPING[$modelId];
            }

            // 如果直接映射没有，则根据模块名称映射
            $moduleType = $this->getModuleNameByModelId($modelId);
            if ($moduleType !== 'pages') { // 排除默认的pages类型
                return self::MODULE_MAPPING[$moduleType]['model'];
            }

            return null;
        } catch (\Exception $e) {
            \Log::error("获取模型类时出错: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 从内容中提取标题
     *
     * @param string|null $content
     * @return string
     */
    private function extractTitleFromContent(?string $content): string
    {
        // 如果内容为空，返回默认标题
        if (empty($content)) {
            return '未知标题';
        }
        
        // 尝试从HTML中提取标题
        if (preg_match('/<h[1-6][^>]*>(.*?)<\/h[1-6]>/i', $content, $matches)) {
            return strip_tags($matches[1]);
        }

        // 如果没有找到标题标签，返回内容的前50个字符
        return mb_substr(strip_tags($content), 0, 50) . '...';
    }

    /**
     * 从内容中提取摘要
     *
     * @param string|null $content
     * @return string
     */
    private function extractSummaryFromContent(?string $content): string
    {
        // 如果内容为空，返回默认摘要
        if (empty($content)) {
            return '暂无摘要';
        }
        
        $plainText = strip_tags($content);
        return mb_substr($plainText, 0, 200) . '...';
    }

    /**
     * 去除重复结果
     *
     * @param array $results
     * @return array
     */
    private function removeDuplicates(array $results): array
    {
        $seen = [];
        $unique = [];

        foreach ($results as $result) {
            $key = $result['id'] . '_' . $result['source'];
            if (!isset($seen[$key])) {
                $seen[$key] = true;
                $unique[] = $result;
            }
        }

        return $unique;
    }

    /**
     * 获取支持的模块类型
     *
     * @return array
     */
    public function getSupportedModules(): array
    {
        return array_keys(self::MODULE_MAPPING);
    }

    /**
     * 获取模块显示名称
     *
     * @param string $moduleType
     * @return string
     */
    public function getModuleDisplayName(string $moduleType): string
    {
        return self::MODULE_MAPPING[$moduleType]['name'] ?? $moduleType;
    }
} 