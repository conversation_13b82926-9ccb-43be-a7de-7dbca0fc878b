<?php

namespace Modules\Ai\Services;

use Exception;

/**
 * 内容生成服务
 */
class ContentGenerationService
{
    protected AiAssistantService $aiAssistantService;

    /**
     * 构造函数
     *
     * @param AiAssistantService $aiAssistantService AI助手服务
     */
    public function __construct(AiAssistantService $aiAssistantService)
    {
        $this->aiAssistantService = $aiAssistantService;
    }

    /**
     * 生成内容
     *
     * @param array $parameters 生成参数
     * @return array 生成的内容
     * @throws Exception
     */
    public function generateContent(array $parameters): array
    {
        $response = [
            'article_details' => '',
            'short_details' => '',
            'article_title' => ''
        ];

        if ($parameters['article_details']) {
            $response['article_details'] = $this->aiAssistantService->generateContent('article_generation', $parameters)['content'];
        }

        if ($parameters['short_details']) {
            $response['short_details'] = $this->aiAssistantService->generateContent('short_details', $parameters)['content'];
        }

        if ($parameters['article_title']) {
            $response['article_title'] = $this->aiAssistantService->generateContent('article_title', $parameters)['content'];
        }

        return $response;
    }
}
