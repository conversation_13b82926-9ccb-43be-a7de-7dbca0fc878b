<?php

declare(strict_types=1);

namespace Modules\Ai\Services\Chat;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Carbon;

/**
 * 简化的会话管理服务
 * 
 * session数据结构：
 * [
 *     'id' => 'chat_{userId}',
 *     'userId' => 用户ID,
 *     'activeTool' => '当前激活的工具名称' | null,
 *     'toolData' => [ 工具相关数据 ],
 *     'messages' => [ 消息历史 ],
 *     'lastActivity' => 最后活动时间戳
 * ]
 */
class ChatSessionService
{
    // 会话缓存过期时间（小时）
    private const SESSION_EXPIRE_HOURS = 24;

    /**
     * 获取或创建会话
     *
     * @param string $sessionId 会话ID (格式: chat_{userId})
     * @return array 会话数据
     */
    public function getOrCreateSession(string $sessionId): array
    {
        // 从sessionId中提取userId
        $userId = $this->extractUserIdFromSessionId($sessionId);
        
        // 尝试从缓存获取会话
        $session = Cache::get($this->getCacheKey($sessionId));
        
        if (!$session) {
            // 创建新的会话
            $session = [
                'id' => $sessionId,
                'userId' => $userId,
                'activeTool' => null,
                'toolData' => [],
                'messages' => [],
                'lastActivity' => Carbon::now()->timestamp
            ];
        }

        // 更新最后活动时间
        $session['lastActivity'] = Carbon::now()->timestamp;
        $this->saveSession($session);

        return $session;
    }

    /**
     * 添加消息到会话
     *
     * @param array $session 会话数据
     * @param string $role 消息角色 (user/assistant)
     * @param string $content 消息内容
     * @return array 更新后的会话
     */
    public function addMessage(array $session, string $role, string $content): array
    {
        $message = [
            'role' => $role,
            'content' => $content,
            'timestamp' => Carbon::now()->timestamp
        ];

        $session['messages'][] = $message;
        $session['lastActivity'] = Carbon::now()->timestamp;
        
        $this->saveSession($session);
        return $session;
    }

    /**
     * 激活工具
     *
     * @param array $session 会话数据
     * @param string $toolName 工具名称
     * @param array $data 工具初始数据
     * @return array 更新后的会话
     */
    public function activateTool(array $session, string $toolName, array $data = []): array
    {
        $session['activeTool'] = $toolName;
        $session['toolData'] = $data;
        $session['lastActivity'] = Carbon::now()->timestamp;
        
        $this->saveSession($session);
        return $session;
    }

    /**
     * 更新工具数据
     *
     * @param array $session 会话数据
     * @param array $data 新的工具数据
     * @return array 更新后的会话
     */
    public function updateToolData(array $session, array $data): array
    {
        $session['toolData'] = array_merge($session['toolData'] ?? [], $data);
        $session['lastActivity'] = Carbon::now()->timestamp;
        
        $this->saveSession($session);
        return $session;
    }

    /**
     * 清除激活工具
     *
     * @param array $session 会话数据
     * @return array 更新后的会话
     */
    public function clearActiveTool(array $session): array
    {
        $session['activeTool'] = null;
        $session['toolData'] = [];
        $session['lastActivity'] = Carbon::now()->timestamp;
        
        $this->saveSession($session);
        return $session;
    }

    /**
     * 获取当前激活的工具
     *
     * @param array $session 会话数据
     * @return string|null 激活的工具名称
     */
    public function getActiveTool(array $session): ?string
    {
        return $session['activeTool'] ?? null;
    }

    /**
     * 获取工具数据
     *
     * @param array $session 会话数据
     * @return array 工具数据
     */
    public function getToolData(array $session): array
    {
        return $session['toolData'] ?? [];
    }

    /**
     * 检查是否有激活的工具
     *
     * @param array $session 会话数据
     * @return bool
     */
    public function hasActiveTool(array $session): bool
    {
        return !empty($session['activeTool']);
    }

    /**
     * 重置会话（保留基本信息，清除工具和消息）
     *
     * @param string $sessionId 会话ID
     * @return array 重置后的会话数据
     */
    public function resetSession(string $sessionId): array
    {
        $userId = $this->extractUserIdFromSessionId($sessionId);
        
        $session = [
            'id' => $sessionId,
            'userId' => $userId,
            'activeTool' => null,
            'toolData' => [],
            'messages' => [],
            'lastActivity' => Carbon::now()->timestamp
        ];

        $this->saveSession($session);
        return $session;
    }

    /**
     * 保存会话到缓存
     *
     * @param array $session 会话数据
     */
    private function saveSession(array $session): void
    {
        Cache::put(
            $this->getCacheKey($session['id']),
            $session,
            Carbon::now()->addHours(self::SESSION_EXPIRE_HOURS)
        );
    }

    /**
     * 删除会话
     *
     * @param string $sessionId 会话ID
     * @return bool
     */
    public function deleteSession(string $sessionId): bool
    {
        return Cache::forget($this->getCacheKey($sessionId));
    }

    /**
     * 获取缓存键
     *
     * @param string $sessionId 会话ID
     * @return string
     */
    private function getCacheKey(string $sessionId): string
    {
        return "chat_session_{$sessionId}";
    }

    /**
     * 从sessionId中提取userId
     *
     * @param string $sessionId 会话ID (格式: chat_{userId})
     * @return int|null 用户ID
     */
    private function extractUserIdFromSessionId(string $sessionId): ?int
    {
        if (preg_match('/^chat_(\d+)$/', $sessionId, $matches)) {
            return (int)$matches[1];
        }
        return null;
    }
}
