<?php

namespace Modules\Ai\Services;

use Exception;
use Modules\Ai\Domain\AiAssistant;
use Modules\Ai\Domain\AiEngineFactory;
use Modules\Ai\Domain\AiFunctionalityInterface;
use Modules\Ai\Domain\Engine\OpenAi\ArticleGeneration;
use Modules\Ai\Domain\Engine\OpenAi\ArticleTitleGeneration;
use Modules\Ai\Domain\Engine\OpenAi\ShortDetailsGeneration;
use Modules\Ai\Models\AiSetting;
use Illuminate\Support\Facades\Log;

class AiAssistantService
{
    protected AiAssistant $aiAssistant;

    /**
     * 构造函数
     *
     * @throws Exception
     */
    public function __construct()
    {
        $aiSetting = AiSetting::where('is_default', 1)->first();

        // 使用 AI 设置创建 AI 引擎实例
        $aiEngine = AiEngineFactory::create($aiSetting);

        // 将 AI 引擎传递给 AiAssistant
        $this->aiAssistant = new AiAssistant($aiEngine);
    }


    /**
     * 生成内容
     * @param array $parameters
     * @return array
     * @throws Exception
     */
    public function generateContent(array $parameters): array
    {
        $response = [
            'article_details' => '',
            'short_details' => '',
            'article_title' => ''
        ];

        try {
            if ($parameters['article_details']) {
                $response['article_details'] = $this->generateSpecificContent('article_generation', $parameters);
            }

            if ($parameters['short_details']) {
                $response['short_details'] = $this->generateSpecificContent('short_details', $parameters);
            }

            if ($parameters['article_title']) {
                $response['article_title'] = $this->generateSpecificContent('article_title', $parameters);
            }
        } catch (Exception $e) {
            Log::error('Error generating specific content', ['error' => $e->getMessage()]);
            throw $e;
        }

        return $response;
    }

    /**
     * 生成特定类型的内容
     * @param string $type
     * @param array $parameters
     * @return string
     * @throws Exception
     */
    protected function generateSpecificContent(string $type, array $parameters): string
    {
        try {
            // 根据类型生成提示词（Prompt）
            $functionality = $this->getFunctionality($type);
            $prompt = $functionality->generatePrompt($parameters);

            // 调用 generateResponse 方法并传递生成的提示词和参数
            $response = $this->aiAssistant->generateResponse($prompt, $parameters);

            // 检查响应状态并返回内容
            if ($response['status']) {
                return $response['content'];
            }

            throw new Exception($response['message']);
        } catch (Exception $e) {
            // 捕获异常并记录错误日志
            Log::error('Error generating specific content', ['type' => $type, 'error' => $e->getMessage()]);
            throw $e;
        }
    }


    /**
     * 获取功能类实例。
     *
     * @param string $type 内容类型。
     * @return AiFunctionalityInterface 功能类实例。
     * @throws Exception
     */
    protected function getFunctionality(string $type): AiFunctionalityInterface
    {
        return match ($type) {
            'article_generation' => new ArticleGeneration(),
            'short_details' => new ShortDetailsGeneration(),
            'article_title' => new ArticleTitleGeneration(),
            default => throw new Exception('Unsupported functionality type'),
        };
    }
}
