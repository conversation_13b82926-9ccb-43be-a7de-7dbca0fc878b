<?php

namespace Modules\Ai\Services;

use Modules\Ai\Repositories\PromptTemplateRepository;
use Modules\Ai\Domain\PromptBuilder;
use Modules\Ai\Domain\AiEngineFactory;
use Modules\Ai\Domain\AiAssistant;
use Modules\Ai\Models\AiSetting;
use Exception;

class PromptTemplateService
{
    protected PromptTemplateRepository $repository;
    protected PromptBuilder $promptBuilder;
    protected AiEngineFactory $aiEngineFactory;

    public function __construct(
        PromptTemplateRepository $repository,
        PromptBuilder $promptBuilder,
        AiEngineFactory $aiEngineFactory
    ) {
        $this->repository = $repository;
        $this->promptBuilder = $promptBuilder;
        $this->aiEngineFactory = $aiEngineFactory;
    }

    /**
     * 獲取所有模板
     * @return array
     */
    public function getAllTemplates(): array
    {
        return $this->repository->getAll();
    }

    /**
     * 根據ID獲取模板
     * @param int $id
     * @return mixed
     */
    public function getTemplateById(int $id)
    {
        return $this->repository->findById($id);
    }

    /**
     * 創建新模板
     * @param array $data
     * @return mixed
     */
    public function createTemplate(array $data)
    {
        return $this->repository->create($data);
    }

    /**
     * 更新模板
     * @param int $id
     * @param array $data
     * @return mixed
     */
    public function updateTemplate(int $id, array $data)
    {
        return $this->repository->update($id, $data);
    }

    /**
     * 刪除模板
     * @param int $id
     * @return bool
     */
    public function deleteTemplate(int $id): bool
    {
        return $this->repository->delete($id);
    }

    /**
     * 生成內容
     * @param int $templateId
     * @param array $fields
     * @return string
     * @throws Exception
     */
    public function generateContent(int $templateId, array $fields): string
    {
        $template = $this->getTemplateById($templateId);
        $content = $this->replaceFieldPlaceholders($template->content, $fields);
        $prompt = $this->promptBuilder->build($template, $content);

        // 獲取默認的AI設置
        $aiSetting = AiSetting::where('is_default', true)->first();
        if (! $aiSetting) {
            throw new Exception('No default AI setting found');
        }

        // 使用AiEngineFactory創建AI引擎
        $aiEngine = $this->aiEngineFactory->create($aiSetting);

        // 創建AiAssistant實例
        $aiAssistant = new AiAssistant($aiEngine);

        // 使用AI助手生成內容
        return $aiAssistant->generateResponse($prompt);
    }

    /**
     * 替換字段佔位符
     * @param string $content
     * @param array $fields
     * @return string
     */
    protected function replaceFieldPlaceholders(string $content, array $fields): string
    {
        foreach ($fields as $key => $value) {
            $content = str_replace("{{{$key}}}", $value, $content);
        }
        return $content;
    }
}
