<?php

namespace Modules\Ai\Services\Cache;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Cache\Repository;

class AICacheService
{
    // 缓存前缀
    private const PREFIX = 'ai:';
    
    // 默认缓存时间（分钟）
    private const DEFAULT_TTL = 60;
    
    // 各类型缓存的TTL配置（分钟）
    private const CACHE_TTL = [
        'intent' => 30,      // 意图识别结果
        'params' => 30,      // 参数提取结果
        'llm' => 120,        // LLM响应
        'context' => 60,     // 上下文数据
        'config' => 1440,    // 配置数据
    ];

    /**
     * 获取缓存客户端
     */
    private function client(): Repository
    {
        return Cache::store();
    }

    /**
     * 生成缓存键
     */
    private function generateKey(string $type, string $sessionId, string $additionalKey = ''): string
    {
        $keyParts = [$sessionId];
        if (!empty($additionalKey)) {
            $keyParts[] = $additionalKey;
        }
        return self::PREFIX . $type . ':' . md5(implode(':', $keyParts));
    }

    /**
     * 获取指定类型的TTL
     */
    private function getTTL(string $type): int
    {
        return self::CACHE_TTL[$type] ?? self::DEFAULT_TTL;
    }

    /**
     * 缓存意图识别结果
     */
    public function rememberIntent(string $sessionId, string $message, callable $callback): array
    {
        $key = $this->generateKey('intent', $sessionId, $message);
        try {
            return $this->client()->remember($key, $this->getTTL('intent'), $callback);
        } catch (\Throwable $e) {
            Log::error('Intent cache error', [
                'session_id' => $sessionId,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
            return $callback();
        }
    }

    /**
     * 缓存参数提取结果
     */
    public function rememberParams(string $sessionId, string $message, array $context, callable $callback): array
    {
        $key = $this->generateKey('params', $sessionId, $message . json_encode($context));
        try {
            return $this->client()->remember($key, $this->getTTL('params'), $callback);
        } catch (\Throwable $e) {
            Log::error('Params cache error', [
                'session_id' => $sessionId,
                'message' => $message,
                'context' => $context,
                'error' => $e->getMessage()
            ]);
            return $callback();
        }
    }

    /**
     * 缓存LLM响应
     */
    public function rememberLLMResponse(string $sessionId, string $prompt, array $context, callable $callback): array
    {
        $key = $this->generateKey('llm', $sessionId, $prompt . json_encode($context));
        try {
            return $this->client()->remember($key, $this->getTTL('llm'), $callback);
        } catch (\Throwable $e) {
            Log::error('LLM response cache error', [
                'session_id' => $sessionId,
                'prompt' => $prompt,
                'context' => $context,
                'error' => $e->getMessage()
            ]);
            return $callback();
        }
    }

    /**
     * 缓存上下文数据
     */
    public function rememberContext(string $sessionId, callable $callback): array
    {
        $key = $this->generateKey('context', $sessionId);
        try {
            return $this->client()->remember($key, $this->getTTL('context'), $callback);
        } catch (\Throwable $e) {
            Log::error('Context cache error', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            return $callback();
        }
    }

    /**
     * 清除指定会话的所有缓存
     */
    public function forgetSession(string $sessionId): bool
    {
        try {
            $pattern = self::PREFIX . '*' . md5($sessionId) . '*';
            $keys = $this->client()->getRedis()->keys($pattern);
            if (!empty($keys)) {
                $this->client()->getRedis()->del($keys);
            }
            return true;
        } catch (\Throwable $e) {
            Log::error('Forget session cache error', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 清除指定类型和键的缓存
     */
    public function forget(string $type, string $sessionId, string $additionalKey = ''): bool
    {
        return $this->client()->forget(
            $this->generateKey($type, $sessionId, $additionalKey)
        );
    }

    /**
     * 清除所有AI相关缓存
     */
    public function flushAll(): bool
    {
        try {
            $keys = $this->client()->getRedis()->keys(self::PREFIX . '*');
            if (!empty($keys)) {
                $this->client()->getRedis()->del($keys);
            }
            return true;
        } catch (\Throwable $e) {
            Log::error('Flush AI cache error', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 获取缓存统计信息
     */
    public function getStats(): array
    {
        try {
            $stats = [];
            foreach (array_keys(self::CACHE_TTL) as $type) {
                $keys = $this->client()->getRedis()->keys(self::PREFIX . $type . ':*');
                $stats[$type] = count($keys);
            }
            return $stats;
        } catch (\Throwable $e) {
            Log::error('Get cache stats error', ['error' => $e->getMessage()]);
            return [];
        }
    }
}
