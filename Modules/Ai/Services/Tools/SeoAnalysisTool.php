<?php

declare(strict_types=1);

namespace Modules\Ai\Services\Tools;

use Illuminate\Support\Facades\Log;
use Modules\Ai\Services\AI\Analysis\SeoLLMAnalyzer;
use Modules\Ai\Services\AI\IntentRecognition\SeoIntentRecognition;
use Modules\Ai\Services\AI\ParameterExtraction\SeoParameterExtractor;
use Modules\SEO\Enums\SeoErrorCode;
use Bingo\Exceptions\BizException;

final class SeoAnalysisTool extends BaseTool
{
    private SeoIntentRecognition $intentService;
    private SeoParameterExtractor $parameterExtractor;
    private SeoLLMAnalyzer $seoAnalyzer;

    public function __construct(
        SeoIntentRecognition $intentService,
        SeoParameterExtractor $parameterExtractor,
        SeoLLMAnalyzer $seoAnalyzer
    ) {
        $this->intentService = $intentService;
        $this->parameterExtractor = $parameterExtractor;
        $this->seoAnalyzer = $seoAnalyzer;
        $this->initializeMetadata();
    }

    public function execute(array $params, array $context = []): array
    {
        try {
            $this->validateRequiredParams($params);
            
            // 设置创建者ID
            $params['creator_id'] = $context['user_id'] ?? 0;
            
            // 1. 基础分析
            $basicAnalysis = $this->analyzeBasic($params);
            
            // 2. LLM分析
            $llmAnalysis = $this->analyzeLLM($params);
            
            // 3. 合并分析结果
            $analysisResult = $this->mergeAnalysisResults($basicAnalysis, $llmAnalysis);
            
            return $this->createResponse('success', 'SEO分析完成', $analysisResult);
        } catch (\Exception $e) {
            $this->logError('SEO分析执行失败', [
                'error' => $e->getMessage(),
                'params' => $params
            ]);
            
            return $this->createErrorResponse('SEO分析失败: ' . $e->getMessage());
        }
    }


    /**
     * 执行基础SEO分析
     *
     * @param array $data
     * @return array
     */
    protected function analyzeBasic(array $data): array
    {
        $result = [
            'meta_title' => $data['title'] ?? null,
            'meta_description' => $data['description'] ?? null,
            'meta_keywords' => is_array($data['keywords'] ?? [])
                ? implode(',', $data['keywords'] ?? [])
                : ($data['keywords'] ?? null),
            'seo_suggestions' => $data['seo_suggestions'] ?? [],
            'content_score' => 0,
            'keyword_score' => 0,
            'technical_score' => 0,
            'readability' => [],
            'originality' => [],
            'content_depth' => [],
            'word_count' => str_word_count($data['content'] ?? ''),
            'keyword_density' => [],
            'suggestions' => [],
            'creator_id' => $data['creator_id'] ?? 0,
        ];

        return $result;
    }
    
    /**
     * 使用LLM进行分析
     *
     * @param array $data
     * @return array
     */
    protected function analyzeLLM(array $data): array
    {
        return $this->seoAnalyzer->analyze($data);
    }
    

    /**
     * 合并分析结果
     *
     * @param array $basicAnalysis
     * @param array $llmAnalysis
     * @return array
     */
    protected function mergeAnalysisResults(array $basicAnalysis, array $llmAnalysis): array
    {
        return [
            ...array_filter($llmAnalysis), // 使用展开运算符，并过滤掉空值
            'llm_suggestions' => $llmAnalysis['suggestions'] ?? [],
            'analysis_data' => $llmAnalysis,
            'meta_title' => $basicAnalysis['meta_title'] ?? null,
            'meta_description' => $basicAnalysis['meta_description'] ?? null,
            'meta_keywords' => $basicAnalysis['meta_keywords'] ?? null,
            'word_count' => $basicAnalysis['word_count'] ?? 0,
        ];
    }

    private function validateRequiredParams(array $params): void
    {
        if (!isset($params['content']) || empty($params['content'])) {
            BizException::throws(SeoErrorCode::INVALID_SEO_DATA, '缺少分析内容');
        }
        
        if (!isset($params['title']) || empty($params['title'])) {
            BizException::throws(SeoErrorCode::INVALID_SEO_DATA, '缺少内容标题');
        }
    }

    private function initializeMetadata(): void
    {
        $this->metadata = [
            'name' => 'seo_analysis',
            'description' => '分析内容的SEO情况，提供优化建议和改进方案',
            'parameters' => [
                'required' => ['content', 'title'],
                'optional' => ['keywords', 'description', 'url', 'locale']
            ]
        ];

        $this->capabilities = [
            'interactive' => true,
            'batch_processing' => true
        ];
    }

    public function handleMessage(string $message, array $context = []): array
    {
        try {
            if ($this->isRestartCommand($message)) {
                return $this->handleInitialActivation();
            }

            $session = $context['session'] ?? [];
            if (empty($session)) {
                return $this->createErrorResponse('会话数据缺失');
            }

            $history = $session['history'] ?? [];
            $activeToolInfo = $session['data']['activeTool'] ?? null;
            $data = $activeToolInfo['data'] ?? [];
            $params = $data['params'] ?? [];
            
            // 识别用户意图 - 使用SeoIntentRecognition
            $intentResult = $this->intentService->recognizeIntent($message, [
                'exit_tool' => ['退出', '取消', '关闭', '结束'],
                'analyze_content' => ['分析', '检查', '评估', '优化'],
                'submit_content' => ['内容', '标题', '标题是', '内容是', '关键词'],
                'batch_analyze' => ['批量', '多个', '全部', '所有'],
                'analyze_by_id' => ['文章id', 'id是', '编号', '文章编号'],
                'analyze_by_url' => ['网址', 'url', '链接', '地址']
            ]);

            $intent = $intentResult['intent'] ?? 'unknown';
            $extractedParams = $this->parameterExtractor->extractParameters(
                $message,
                'seo_analysis',
                ['content', 'title', 'keywords', 'description', 'url', 'locale', 'article_id'],
                ['history' => $history]
            );

            if (!empty($extractedParams)) {
                $params = array_merge($params, $extractedParams);
            }
            return $this->processIntent($intent, $message, $params, $context);
        } catch (\Exception $e) {
            $this->logError('处理消息失败', [
                'error' => $e->getMessage(),
                'message' => $message,
                'context' => $context
            ]);

            return [
                'content' => '处理您的请求时发生错误，请稍后重试',
                'releaseControl' => true
            ];
        }
    }

    private function isRestartCommand(string $message): bool
    {
        $message = strtolower($message);
        $restartKeywords = ['seo分析', '重新开始', '重新分析', '重新检查', '分析内容'];

        foreach ($restartKeywords as $keyword) {
            if (str_contains($message, strtolower($keyword))) {
                return true;
            }
        }
        return false;
    }
    
    private function processIntent(string $intent, string $message, array $params, array $context): array
    {
        switch ($intent) {
            case 'exit_tool':
                return [
                    'content' => 'SEO分析工具已退出',
                    'releaseControl' => true
                ];

            case 'analyze_content':
                return $this->handleContentAnalysis($params, $context);
                
            case 'submit_content':
                return $this->handleContentSubmission($params, $message);

            case 'analyze_by_id':
                return $this->handleAnalyzeById($params, $context);

            case 'analyze_by_url':
                return $this->handleAnalyzeByUrl($params, $context);

            default:
                return $this->handleUnknownIntent($params);
        }
    }
    
    private function handleInitialActivation(array $params = []): array
    {
        return [
            'content' => "欢迎使用SEO分析工具。您可以通过以下方式进行内容分析：\n\n" .
                        "1. 直接输入内容：\n" .
                        "标题：我的网页标题\n" .
                        "内容：这是我想要分析的内容...\n\n" .
                        "2. 通过文章ID：\n" .
                        "文章ID：12345\n" .
                        "或直接输入：分析文章12345\n\n" .
                        "3. 通过URL：\n" .
                        "网址：https://example.com/article/12345\n" .
                        "或直接输入：分析 https://example.com/article/12345\n\n请选择任一方式开始分析。" ,
            'quickActions' => [
                // ['id' => 'exit', 'text' => '退出']
            ],
            'toolData' => compact('params'),
            'resetSession' => true
        ];
    }
    
    private function handleContentAnalysis(array $params, array $context): array
    {
        try {
            $analysisResult = $this->execute($params);
            $content = $this->formatAnalysisResult($analysisResult);
            if(!empty($params['article_id'])){
                $content  = "正在查询文章ID: {$params['article_id']} 的内容...\n请稍候，系统正在获取文章数据并进行分析。" .$content;
            }
            return [
                'content' => $content,
                'quickActions' => [
                    ['id' => 'new_analysis', 'text' => '新的分析'],
                    ['id' => 'exit', 'text' => '退出']
                ],
                'toolData' => [
                    'result' => $analysisResult
                ]
            ];
        } catch (\Exception $e) {
            return $this->createErrorResponse("分析失败：{$e->getMessage()}");
        }
    }

    private function handleContentSubmission(array $params, string $message): array
    {
        $hasRequiredParams = isset($params['content']) && !empty($params['content']) && 
                            isset($params['title']) && !empty($params['title']);
        
        return [
            'content' => "已接收内容" . ($hasRequiredParams ? 
                "，是否立即进行分析？" : 
                "，但仍缺少必要信息。请提供完整的标题和内容。"),
            'quickActions' => $hasRequiredParams ? [
                ['id' => 'analyze', 'text' => '开始分析'],
                ['id' => 'cancel', 'text' => '取消']
            ] : [],
            'toolData' => compact('params')
        ];
    }
    
    
    private function handleUnknownIntent(array $params): array
    {
        $hasRequiredParams = isset($params['content']) && !empty($params['content']) && 
                            isset($params['title']) && !empty($params['title']);
        
        if ($hasRequiredParams) {
            return [
                'content' => "您想对当前内容进行SEO分析吗？",
                'quickActions' => [
                    ['id' => 'analyze', 'text' => '开始分析'],
                    ['id' => 'cancel', 'text' => '取消']
                ],
                'toolData' => compact('params')
            ];
        }
        
        return [
            'content' => "请提供要分析的内容和标题。\n格式示例：\n标题：我的网页标题\n内容：这是我想要分析的内容...",
            'toolData' => compact('params')
        ];
    }
    
    private function getArticleById(int|string $articleId): ?array
    {
        // TODO: 后续接入真实数据库查询
        // 目前返回测试数据
        return [
            'id' => $articleId,
            'title' => '测试文章标题',
            'content' => '这是一篇测试文章的内容，用于SEO分析工具的开发和测试。这篇文章包含了一些基本的内容结构，比如标题、正文、关键词等。',
            'keywords' => ['SEO','测试','开发'],
            'description' => '这是一篇用于测试SEO分析工具的示例文章',
            'created_at' => '2024-01-20 10:00:00',
            'updated_at' => '2024-01-20 10:00:00'
        ];
    }

    private function handleAnalyzeById(array $params, array $context): array
    {
        try {
            $articleId = $params['article_id'] ?? null;
            
            if (empty($articleId)) {
                // 尝试从消息中提取数字ID
                if (preg_match('/\b(\d+)\b/', $params['message'] ?? '', $matches)) {
                    $articleId = $matches[1];
                }
            }

            if (empty($articleId)) {
                return [
                    'content' => '请提供有效的文章ID',
                    'releaseControl' => false
                ];
            }

            // 从数据库获取文章内容
            $article = $this->getArticleById($articleId);
            if (!$article) {
                return [
                    'content' => "未找到ID为 {$articleId} 的文章",
                    'releaseControl' => false
                ];
            }

            // 构建分析参数
            $analysisParams = [
                'title' => $article['title'],
                'content' => $article['content'],
                'keywords' => $article['keywords'],
                'description' => $article['description'],
                'article_id' => $article['id']
            ];

            // 执行分析
            return $this->handleContentAnalysis($analysisParams, $context);

        } catch (\Exception $e) {
            $this->logError('通过ID分析文章失败', [
                'error' => $e->getMessage(),
                'params' => $params
            ]);

            return [
                'content' => '分析文章时发生错误，请稍后重试',
                'releaseControl' => false
            ];
        }
    }

    private function handleAnalyzeByUrl(array $params, array $context): array
    {
        try {
            $url = $params['url'] ?? null;
            
            if (empty($url)) {
                return [
                    'content' => '请提供有效的文章URL',
                    'releaseControl' => false
                ];
            }

            // 从URL中提取content_group_id
            if (preg_match('/content_group_id=(\d+)/', $url, $matches)) {
                return $this->handleAnalyzeById(['article_id' => $matches[1]], $context);
            }

            return [
                'content' => '无法从URL中提取内容ID，请提供正确的文章URL',
                'releaseControl' => false
            ];

        } catch (\Exception $e) {
            $this->logError('通过URL分析文章失败', [
                'error' => $e->getMessage(),
                'params' => $params
            ]);

            return [
                'content' => '分析文章时发生错误，请稍后重试',
                'releaseControl' => false
            ];
        }
    }
    
    /**
     * 格式化分析结果
     */
    private function formatAnalysisResult(array $result): string
    {
        $data = $result['data'] ?? [];
        if (empty($data)) {
            return "SEO分析结果为空";
        }

        $output = "SEO分析结果：\n\n";

        // 基础评分
        if (isset($data['overall_score'])) {
            $output .= "总体评分：{$data['overall_score']}\n";
        }
        
        if (isset($data['content_score'])) {
            $output .= "内容质量评分：{$data['content_score']}\n";
        }
        
        if (isset($data['keyword_score'])) {
            $output .= "关键词优化评分：{$data['keyword_score']}\n";
        }
        
        if (isset($data['technical_score'])) {
            $output .= "技术优化评分：{$data['technical_score']}\n";
        }

        // 可读性分析
        if (!empty($data['readability'])) {
            $output .= "\n可读性分析：\n";
            $output .= "- 评分：{$data['readability']['score']}\n";
            $output .= "- 级别：{$data['readability']['level']}\n";
            $output .= "- 评估：{$data['readability']['description']}\n";
        }

        // 内容深度分析
        if (!empty($data['content_depth'])) {
            $output .= "\n内容深度分析：\n";
            $output .= "- 评分：{$data['content_depth']['score']}\n";
            $output .= "- 字数：{$data['content_depth']['word_count']}\n";
            $output .= "- 评估：{$data['content_depth']['assessment']}\n";
        }

        // 原创性分析
        if (!empty($data['originality'])) {
            $output .= "\n原创性分析：\n";
            $output .= "- 评分：{$data['originality']['score']}\n";
            $output .= "- 评估：{$data['originality']['assessment']}\n";
        }
        
        // 处理建议
        if (!empty($data['suggestions'])) {
            $output .= "\n优化建议：\n";
            foreach ($data['suggestions'] as $category => $categorySuggestions) {
                if (is_array($categorySuggestions)) {
                    $output .= "\n{$category}相关建议：\n";
                    foreach ($categorySuggestions as $suggestion) {
                        $output .= "- {$suggestion}\n";
                    }
                }
            }
        }
        
        // SEO建议
        if (!empty($data['seo_suggestions'])) {
            $output .= "\nSEO优化建议：\n";
            if (!empty($data['seo_suggestions']['title'])) {
                $output .= "\n标题建议：\n";
                foreach ($data['seo_suggestions']['title'] as $title) {
                    $output .= "- {$title}\n";
                }
            }
            
            if (!empty($data['seo_suggestions']['description'])) {
                $output .= "\n描述建议：\n";
                foreach ($data['seo_suggestions']['description'] as $desc) {
                    $output .= "- {$desc}\n";
                }
            }
            
            if (!empty($data['seo_suggestions']['keywords'])) {
                $output .= "\n建议关键词：\n";
                foreach ($data['seo_suggestions']['keywords'] as $keyword) {
                    $output .= "- {$keyword}\n";
                }
            }

            if (!empty($data['seo_suggestions']['seo_title'])) {
                $output .= "\n优化后的标题建议：\n";
                foreach ($data['seo_suggestions']['seo_title'] as $seoTitle) {
                    $output .= "- {$seoTitle}\n";
                }
            }

            if (!empty($data['seo_suggestions']['seo_description'])) {
                $output .= "\n优化后的描述建议：\n";
                foreach ($data['seo_suggestions']['seo_description'] as $seoDesc) {
                    $output .= "- {$seoDesc}\n";
                }
            }
        }
        
        return $output;
    }
}
