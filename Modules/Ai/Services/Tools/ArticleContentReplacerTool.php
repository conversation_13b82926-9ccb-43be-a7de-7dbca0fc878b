<?php

declare(strict_types=1);

namespace Modules\Ai\Services\Tools;

use Modules\Ai\Services\AI\ParameterExtraction\ArticleParameterExtractor;
use Modules\Ai\Services\AI\IntentRecognition\ArticleIntentRecognition;
use Modules\Ai\Config\ChatActions;
use Illuminate\Support\Facades\Log;
use Modules\Ai\Services\Tools\Handlers\ArticleToolActionHandler;

/**
 * 文章内容替换工具
 * 
 * 负责在文章内容中批量替换指定文本，支持预览、模块筛选、快捷替换等功能
 */
final class ArticleContentReplacerTool extends BaseTool
{
    private ArticleIntentRecognition $intentService;
    private ArticleParameterExtractor $parameterExtractor;
    
    /**
     * 构造函数 - 初始化依赖服务
     */
    public function __construct(
        ArticleIntentRecognition $intentService,
        ArticleParameterExtractor $parameterExtractor,
    ) {
        $this->intentService = $intentService;
        $this->parameterExtractor = $parameterExtractor;
        $this->initializeMetadata();
    }

    /**
     * 初始化工具元数据和能力配置
     */
    private function initializeMetadata(): void
    {
        $this->metadata = [
            'name' => 'article_content_replacer',
            'description' => '在文章内容中批量替换指定文本',
            'parameters' => [
                'required' => ['sourceText', 'targetText'],
                'optional' => ['moduleType', 'articleIds', 'preview']
            ]
        ];

        $this->capabilities = [
            'preview' => true,
            'batch_processing' => true,
            'module_selection' => true,
            'interactive' => true
        ];
    }

    /**
     * 执行文本替换操作 - 支持预览和实际替换
     */
    public function execute(array $parameters, array $context = []): array
    {
        try {
            $sourceText = $parameters['sourceText'] ?? '';
            $targetText = $parameters['targetText'] ?? '';
            $moduleType = $parameters['moduleType'] ?? 'all';
            $articleIds = $parameters['articleIds'] ?? [];
            $isPreview = $parameters['preview'] ?? true;

            // 参数验证
            if (empty($sourceText) || empty($targetText)) {
                return $this->createErrorResponse('替换文本参数不完整');
            }

            // 如果是预览模式，先搜索包含关键词的文章
            if ($isPreview) {
                $searchResults = ArticleToolActionHandler::searchArticlesByKeyword($sourceText, $moduleType);
                return ArticleToolActionHandler::formatSearchResults($searchResults, $sourceText, $targetText);
            }

            // 执行实际替换
            if (empty($articleIds)) {
                return ArticleToolActionHandler::createErrorResponse('请选择要替换的文章');
            }

            return ArticleToolActionHandler::executeReplacement($articleIds, $sourceText, $targetText);
        } catch (\Exception $e) {
            $this->logError('执行文本替换失败', [
                'error' => $e->getMessage(),
                'parameters' => $parameters
            ]);
            return $this->createErrorResponse($e->getMessage());
        }
    }

    /**
     * 处理用户消息的主入口方法
     */
    public function handleMessage(string $message, array $context = []): array
    {
        try {
            // 1. 处理重启命令
            if ($this->isRestartCommand($message)) {
                return ArticleToolActionHandler::handleInitialActivation();
            }

            // 2. 获取基础数据
            $session = $context['session'] ?? [];
            $toolData = $session['toolData'] ?? [];
            
            // 从toolData中获取payload信息
            $payload = [];
            if (isset($toolData['payload'])) {
                $payload = $toolData['payload'];
            }

            // 3. 如果有具体的reply_action，处理工具内的具体操作
            if (isset($payload['reply_action']) && $payload['reply_action'] !== ChatActions::TEXT_MESSAGE) {
                return $this->handleToolAction($payload['reply_action'], $payload, $session);
            }
            
            // 4. 处理空消息的工具激活请求
            if (empty(trim($message))) {
                return ArticleToolActionHandler::handleInitialActivation();
            }
            // 5. 处理常规文本消息（包括TEXT_MESSAGE和其他文本输入）
            return $this->handleTextMessage($message, $session);
        } catch (\Exception $e) {
            $this->logError('处理消息失败', [
                'error' => $e->getMessage(),
                'message' => $message
            ]);

            return [
                'content' => '处理您的请求时发生错误，请稍后重试',
                'releaseControl' => true
            ];
        }
    }

    /**
     * 处理常规文本消息 - 包括意图识别和参数准备
     */
    private function handleTextMessage(string $message, array $session): array
    {
        // 识别意图（同时提取参数）
        $intentResult = $this->recognizeUserIntent($message);
        $intent = $intentResult['intent'] ?? 'unknown';
        $intentParameters = $intentResult['parameters'] ?? [];
        
        // 处理意图
        return $this->processIntent($intent, $message, $session, $intentParameters);
    }





    /**
     * 识别用户意图 - 调用意图识别服务，映射到具体的ChatActions
     */
    private function recognizeUserIntent(string $message): array
    {
        $intentResult = $this->intentService->recognizeIntent($message, [
            // 基础操作
            ChatActions::EXIT_TOOL => ['退出', '取消', '关闭', '结束'],
            ChatActions::QUICK_REPLACE => ['替换', '修改', '更改', '快速替换', '快捷替换'],
            ChatActions::PREVIEW_RESULTS => ['预览', '查看', '检查'],
            ChatActions::CONFIRM_EXECUTE => ['确认', '执行', '开始替换', '确认执行'],
            ChatActions::CANCEL_OPERATION => ['取消操作', '取消', '撤销'],
            
            // 替换范围选择
            ChatActions::BATCH_REPLACE_ALL => ['全部替换', '批量替换', '替换全部', '全部', '批量'],
            ChatActions::SELECT_INDIVIDUAL_ARTICLES => ['单独替换', '选择文章', '指定文章', '设置文章', '单独', '选择'],
            
            // 搜索相关
            ChatActions::SEARCH_SPECIFIC_ARTICLE => ['搜索文章', '查找文章', '搜索', '查找'],
            ChatActions::EXECUTE_SEARCH => ['执行搜索', '开始搜索'],
            ChatActions::CANCEL_SEARCH => ['取消搜索', '停止搜索'],
            
            // 文章操作
            ChatActions::SELECT_ARTICLE => ['选择这篇', '选择此文章', '就是这篇'],
            ChatActions::SHOW_MORE_ARTICLES => ['显示更多', '更多文章', '查看更多'],
            
            // 导航操作
            ChatActions::BACK_TO_SEARCH => ['返回搜索', '返回', '返回上一步'],
            ChatActions::BACK_TO_ARTICLE_LIST => ['返回文章列表', '返回列表', '回到列表'],
            
            // 模块选择
            ChatActions::SELECT_MODULE => ['选择模块', '指定模块', '设置模块'],
            
            // 帮助和示例
            ChatActions::SHOW_HELP => ['帮助', '查看帮助', '使用说明'],
            ChatActions::SHOW_EXAMPLES => ['示例', '查看示例', '使用示例'],
            
            // 重新开始
            'NEW_REPLACEMENT' => ['重新开始', '新的替换', '开始新的替换', '重新替换'],
            'BACK_TO_MAIN' => ['返回主菜单', '主菜单', '返回首页'],
        ]);
        
        return $intentResult;
    }

    /**
     * 处理意图 - 根据用户意图调用相应的处理方法
     */
    private function processIntent(string $intent, string $message, array $session, array $intentParameters = []): array
    {
        // 如果意图识别成功，处理参数完整性
        if ($intent !== 'unknown') {
            // 对于需要替换参数的意图，确保参数完整
            if ($this->needsReplacementParameters($intent)) {
                $completeParameters = $this->ensureParametersFromToolData($intentParameters, $session);
                
                $quickActionPayload = [
                    'reply_data' => $completeParameters,
                    'action' => $intent
                ];
                return ArticleToolActionHandler::handle($intent, $quickActionPayload, $session, []);
            }
            
            // 其他意图直接处理
            $quickActionPayload = [
                'reply_data' => $intentParameters,
                'action' => $intent
            ];
            
            return ArticleToolActionHandler::handle($intent, $quickActionPayload, $session, []);
        }
        
        // 只有未知意图才需要备用处理
        return $this->handleUnknownIntent($message, $session);
    }

    /**
     * 检查意图是否需要替换参数（sourceText 和 targetText）
     */
    private function needsReplacementParameters(string $intent): bool
    {
        $replacementIntents = [
            // 基础替换操作
            ChatActions::QUICK_REPLACE,
            ChatActions::PREVIEW_RESULTS,
            ChatActions::CONFIRM_EXECUTE,
            
            // 替换范围选择
            ChatActions::BATCH_REPLACE_ALL,          // 全部替换 - 需要替换参数
            ChatActions::SELECT_INDIVIDUAL_ARTICLES, // 单独替换 - 需要替换参数
            
            // 文章操作
            ChatActions::SELECT_ARTICLE,             // 选择文章 - 需要替换参数
            
            // 搜索相关（需要知道要替换什么）
            ChatActions::SEARCH_SPECIFIC_ARTICLE,    // 搜索特定文章 - 需要替换参数
            ChatActions::BACK_TO_ARTICLE_LIST,       // 返回文章列表 - 需要替换参数
        ];
        
        return in_array($intent, $replacementIntents);
    }

    /**
     * 从toolData中确保参数完整
     */
    private function ensureParametersFromToolData(array $intentParameters, array $session): array
    {
        $toolData = $session['toolData'] ?? [];
        
        // 如果意图参数中缺少替换文本，从toolData中获取
        if (empty($intentParameters['sourceText']) && !empty($toolData['sourceText'])) {
            $intentParameters['sourceText'] = $toolData['sourceText'];
        }
        
        if (empty($intentParameters['targetText']) && !empty($toolData['targetText'])) {
            $intentParameters['targetText'] = $toolData['targetText'];
        }
        
        // 同时传递其他可能需要的参数
        if (empty($intentParameters['moduleType']) && !empty($toolData['moduleType'])) {
            $intentParameters['moduleType'] = $toolData['moduleType'];
        }
        
        // 如果有搜索结果，也传递过去
        if (empty($intentParameters['searchResults']) && !empty($toolData['searchResults'])) {
            $intentParameters['searchResults'] = $toolData['searchResults'];
        }
        
        return $intentParameters;
    }

    /**
     * 处理未知意图 - 当AI意图识别失败时的处理方法
     */
    private function handleUnknownIntent(string $message, array $session): array
    {
        // 检查是否可能是搜索关键词（简单的启发式判断）
        if ($this->looksLikeSearchKeyword($message, $session)) {
            return ArticleToolActionHandler::handleSearchInput($message, $session);
        }
        
        // 返回默认帮助信息
        return $this->getDefaultHelpResponse();
    }
    
    /**
     * 判断消息是否像搜索关键词
     */
    private function looksLikeSearchKeyword(string $message, array $session): bool
    {
        // 如果消息很短且没有明确的命令词，可能是搜索关键词
        $message = trim($message);
        
        // 过滤掉明显的命令词
        $commandWords = ['帮助', '退出', '取消', '返回', '确认', '执行'];
        foreach ($commandWords as $word) {
            if (strpos($message, $word) !== false) {
                return false;
            }
        }
        
        // 如果消息长度适中且包含中文或英文字符，可能是搜索关键词
        if (strlen($message) >= 2 && strlen($message) <= 50) {
            return (bool) preg_match('/[\x{4e00}-\x{9fa5}a-zA-Z0-9]/u', $message);
        }
        
        return false;
    }

    /**
     * 获取默认帮助响应 - 当无法理解用户指令时提供帮助信息
     */
    private function getDefaultHelpResponse(): array
    {
        return [
            'content' => '抱歉，我没有理解您的指令。<br><br>' .
                       '您可以：<br>' .
                       '• 输入替换指令，如："把AI替换成人工智能"<br>' .
                       '• 点击下方的快捷替换按钮<br>' .
                       '• 输入"帮助"查看更多使用方法',
            'quickActions' => [
                [
                    'id' => 'help',
                    'text' => '❓ 查看帮助',
                    'action' => ChatActions::SHOW_HELP
                ],
                [
                    'id' => 'examples',
                    'text' => '📝 查看示例',
                    'action' => ChatActions::SHOW_EXAMPLES
                ],
                [
                    'id' => 'restart',
                    'text' => '🔄 重新开始',
                    'action' => 'NEW_REPLACEMENT'
                ]
            ]
        ];
    }

    /**
     * 处理工具内的具体操作 - 代理给ArticleToolActionHandler处理
     */
    private function handleToolAction(string $replyAction, array $payload, array $session): array
    {
        return ArticleToolActionHandler::handle($replyAction, $payload, $session, []);
    }

    /**
     * 检查是否为重启命令 - 识别用户想要重新开始使用工具的指令
     */
    private function isRestartCommand(string $message): bool
    {
        $message = strtolower($message);
        $restartKeywords = [
            '文章替换',
            '内容替换',
            '重新开始',
            '重新替换',
            '重新操作',
            '重新检查',
            '替换内容'
        ];

        foreach ($restartKeywords as $keyword) {
            if (str_contains($message, strtolower($keyword))) {
                return true;
            }
        }
        return false;
    }


    /**
     * 记录错误日志 - 统一的错误日志记录方法
     */
    protected function logError(string $message, array $context = []): void
    {
        Log::error("[ArticleContentReplacerTool] {$message}", $context);
    }
}
