<?php

namespace Modules\Ai\Services\Tools;

use Closure;
use Illuminate\Support\Facades\Log;

/**
 * AI工具注册表类
 * 用于管理和注册所有可用的AI工具
 */
class ToolRegistry
{
    /**
     * 存储已实例化的工具对象
     * @var array<string, ToolInterface>
     */
    private static array $tools = [];

    /**
     * 存储工具的工厂函数
     * @var array<string, Closure>
     */
    private static array $toolFactories = [];

    /**
     * 注册一个新的AI工具
     *
     * @param string $name 工具的唯一标识名称
     * @param ToolInterface|Closure $tool 工具实例或返回工具实例的闭包函数
     * @return void
     */
    public static function register(string $name, ToolInterface|Closure $tool): void
    {
        if ($tool instanceof Closure) {
            self::$toolFactories[$name] = $tool;
        } else {
            self::$tools[$name] = $tool;
        }
    }

    /**
     * 获取指定名称的工具实例
     * 如果工具尚未实例化，会通过工厂函数创建实例
     *
     * @param string $name 工具名称
     * @return ToolInterface|null 返回工具实例，如果工具不存在或创建失败则返回null
     */
    public static function getTool(string $name): ?ToolInterface
    {
        try {
            if (isset(self::$tools[$name])) {
                return self::$tools[$name];
            }

            Log::error("Tool not found: {$name}");
            return null;
        } catch (\Exception $e) {
            Log::error("Error creating tool {$name}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取所有已注册工具的描述信息
     * 用于展示可用工具列表和功能说明
     *
     * @return array<string, array> 工具描述信息的数组，键为工具名称，值为工具元数据
     */
    public static function getToolDescriptions(): array
    {
        $descriptions = [];
        foreach (array_keys(self::$toolFactories) as $name) {
            $tool = self::getTool($name);
            if ($tool) {
                $descriptions[$name] = $tool->getMetadata();
            }
        }
        return $descriptions;
    }

    /**
     * 重置工具注册表
     * 清空所有已注册的工具和工厂函数
     * 主要用于测试或重新初始化系统
     *
     * @return void
     */
    public static function reset(): void
    {
        self::$tools = [];
        self::$toolFactories = [];
    }
}
