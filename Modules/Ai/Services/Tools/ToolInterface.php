<?php

namespace Modules\Ai\Services\Tools;

interface ToolInterface
{
    /**
     * 获取工具元数据
     */
    public function getMetadata(): array;

    /**
     * 验证参数
     */
    public function validateParams(array $params): bool;

    /**
     * 执行工具功能
     */
    public function execute(array $params, array $context = []): array;

    /**
     * 处理用户输入
     */
    public function handleMessage(string $message, array $context = []): array;
}
