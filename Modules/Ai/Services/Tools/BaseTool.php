<?php

namespace Modules\Ai\Services\Tools;

/**
 * 工具基类
 * 
 * 提供所有工具的基础功能实现，包括：
 * - 元数据管理
 * - 参数验证
 * - 响应格式化
 * - 错误日志记录
 */
abstract class BaseTool implements ToolInterface
{
    /**
     * 工具元数据
     * 包含工具的基本信息，如名称、描述、参数定义等
     *
     * @var array
     */
    protected array $metadata = [];

    /**
     * 工具能力声明
     * 用于描述工具支持的功能特性
     *
     * @var array
     */
    protected array $capabilities = [];

    /**
     * 获取工具元数据
     * 
     * 返回工具的完整元数据，包括基本信息和能力声明
     *
     * @return array 包含工具完整信息的数组
     */
    public function getMetadata(): array
    {
        return array_merge($this->metadata, [
            'capabilities' => $this->capabilities
        ]);
    }

    /**
     * 验证工具参数
     * 
     * 检查必需参数是否都已提供且非空
     *
     * @param array $params 需要验证的参数数组
     * @return bool 参数验证是否通过
     */
    public function validateParams(array $params): bool
    {
        $required = $this->metadata['parameters']['required'] ?? [];
        foreach ($required as $param) {
            if (!isset($params[$param]) || empty($params[$param])) {
                return false;
            }
        }
        return true;
    }

    /**
     * 创建标准响应格式
     * 
     * 生成统一的响应数据结构
     *
     * @param string $status 响应状态（success/error等）
     * @param string $message 响应消息
     * @param mixed|null $data 响应数据
     * @return array 格式化的响应数组
     */
    protected function createResponse(string $status, string $message, $data = null): array
    {
        return [
            'status' => $status,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 记录错误日志
     * 
     * 使用Laravel的日志系统记录错误信息
     *
     * @param string $message 错误消息
     * @param array $context 错误上下文信息
     * @return void
     */
    protected function logError(string $message, array $context = []): void
    {
        \Illuminate\Support\Facades\Log::error($message, $context);
    }

    /**
     * 创建错误响应
     */
    protected function createErrorResponse(string $message, $data = null): array
    {
        return $this->createResponse('error', $message, $data);
    }

    /**
     * 获取工具数据
     */
    protected function getToolData(array $context): array
    {
        $activeToolInfo = $context['activeToolInfo'] ?? [];
        return $activeToolInfo['data'] ?? [];
    }
}
