<?php

declare(strict_types=1);

namespace Modules\Ai\Services\Tools\Handlers;

use Modules\Ai\Services\Tools\ArticleContentReplacerTool;
use Modules\Ai\Config\ChatActions;
use Illuminate\Support\Facades\Log;
use Modules\Ai\Services\ArticleSearchService;

/**
 * 文章工具动作处理器
 * 
 * 负责处理文章内容替换工具内的各种具体操作
 */
 class ArticleToolActionHandler
{
    /**
     * 创建错误响应
     */
    public static function createErrorResponse(string $message, $data = null): array
    {
        return [
            'content' => "❌ 操作失敗：" . htmlspecialchars($message),
            'quickActions' => [
                [
                    'id' => 'back',
                    'text' => '🔙 返回主選單',
                    'action' => 'BACK_TO_MAIN'
                ]
            ],
            'toolData' => ['params' => compact('message', 'data')]
        ];
    }



    /**
     * 处理工具内的具体操作
     */
    public static function handle(string $replyAction, array $payload, array $session, array $params): array
    {
        switch ($replyAction) {
            case ChatActions::QUICK_REPLACE:
                // 处理快捷替换搜索请求
                $replyData = $payload['reply_data'] ?? [];
                if (isset($replyData['sourceText']) && isset($replyData['targetText'])) {
                    return self::handleQuickReplaceWithSearch($replyData['sourceText'], $replyData['targetText']);
                }
                return self::createErrorResponse('缺少必要的替換參數');
                
            case ChatActions::BATCH_REPLACE_ALL:
                return self::handleBatchReplaceConfirmation($payload);
                
            case ChatActions::SELECT_INDIVIDUAL_ARTICLES:
                return self::handleIndividualArticleSelection($payload);
                
            case ChatActions::BACK_TO_SEARCH:
                return self::handleBackToSearch();
                
            case ChatActions::SELECT_ARTICLE:
                return self::handleSingleArticleSelection($payload);
                
            case ChatActions::SHOW_MORE_ARTICLES:
                return self::handleShowMoreArticles($payload);
                
            case ChatActions::SEARCH_SPECIFIC_ARTICLE:
                return self::handleSearchSpecificArticle($payload);
                
            case ChatActions::BACK_TO_ARTICLE_LIST:
                return self::handleBackToArticleList($payload);
                
            case ChatActions::EXECUTE_SEARCH:
                return self::handleExecuteSearch($payload, $session);
                
            case ChatActions::CANCEL_SEARCH:
                return self::handleCancelSearch($payload, $session);
                
            case ChatActions::CONFIRM_EXECUTE:
                // 处理确认执行操作
                $replyData = $payload['reply_data'] ?? [];
                $sourceText = $replyData['sourceText'] ?? '';
                $targetText = $replyData['targetText'] ?? '';
                $articleIds = $replyData['articleIds'] ?? [];
                
                // 如果 articleIds 为空，尝试从 searchResults 中获取
                if (empty($articleIds) && !empty($replyData['searchResults'])) {
                    $articleIds = array_column($replyData['searchResults'], 'id');
                }
                if (!empty($articleIds) && !empty($sourceText) && !empty($targetText)) {
                    $replaceType = $replyData['replaceType'] ?? 'batch';
                    return self::executeReplacement($articleIds, $sourceText, $targetText, $replaceType);
                }
                return self::createErrorResponse('缺少必要的執行參數');
                
            case ChatActions::CANCEL_OPERATION:
                return self::handleInitialActivation();
                
            case ChatActions::EXIT_TOOL:
                return [
                    'content' => '✅ 文章內容替換工具已退出，感謝使用！',
                    'releaseControl' => true
                ];
                
            default:
                // 检查是否是"新的替换"操作
                if ($replyAction === 'NEW_REPLACEMENT') {
                    return self::handleInitialActivation();
                }
                // 对于未知的操作，返回工具主界面
                return self::handleInitialActivation();
        }
    }


        /**
     * 处理快捷替换请求并显示搜索结果
     */
    private static function handleQuickReplaceWithSearch(string $sourceText, string $targetText): array
    {
        \Log::info("执行快捷替换搜索", [
            'sourceText' => $sourceText,
            'targetText' => $targetText
        ]);
        // 搜索包含源文本的文章
        $searchResults = self::searchArticlesByKeyword($sourceText);
        
        if (empty($searchResults)) {
            return [
                'content' => "❌ 未找到包含 <strong>\"$sourceText\"</strong> 的文章",
                'quickActions' => [
                    [
                        'id' => 'back',
                        'text' => '🔙 返回主選單',
                        'action' => 'BACK_TO_MAIN'
                    ],
                    [
                        'id' => 'help',
                        'text' => '❓ 查看幫助',
                        'action' => 'SHOW_HELP'
                    ]
                ],
                'toolData' => ['params' => compact('sourceText', 'targetText')]
            ];
        }

        // 格式化搜索结果并显示
        return self::formatSearchResults($searchResults, $sourceText, $targetText);
    }


        /**
     * 处理批量替换确认（显示确认界面）
     */
    private static function handleBatchReplaceConfirmation(array $payload): array
    {
        // 从 reply_data 中获取实际数据
        $replyData = $payload['reply_data'] ?? [];
        $sourceText = $replyData['sourceText'] ?? '';
        $targetText = $replyData['targetText'] ?? '';
        $articleIds = $replyData['articleIds'] ?? [];
        
        // 如果 articleIds 为空，尝试从 searchResults 中获取
        if (empty($articleIds) && !empty($replyData['searchResults'])) {
            $articleIds = array_column($replyData['searchResults'], 'id');
        }
        
        if (empty($sourceText) || empty($targetText) || empty($articleIds)) {
            return self::createErrorResponse('替換參數不完整，請重新操作');
        }

        $totalArticles = count($articleIds);
        $totalMatches = self::calculateTotalMatches($articleIds, $sourceText);
        $estimatedTime = self::calculateEstimatedTime($totalArticles);
        // 構建確認界面內容
        $content = '您選擇了全部替換模式。請確認批量替換詳情：<br><br>';
        
        // 替換概覽
        $content .= '📊 <strong>替換概覽：</strong><br><br>';
        $content .= '原文本：<strong>"' . htmlspecialchars($sourceText) . '"</strong><br>';
        $content .= '新文本：<strong>"' . htmlspecialchars($targetText) . '"</strong><br>';
        $content .= '影響範圍：<strong>' . $totalArticles . '篇文章，' . $totalMatches . '處匹配</strong><br>';
        $content .= '預計完成時間：<strong>' . $estimatedTime . '</strong><br><br>';
        
        // 注意事項（黃色警告框）
        $content .= '<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 16px; margin: 16px 0; border-left: 4px solid #fdcb6e;">';
        $content .= '⚠️ <strong>注意事項：</strong><br>';
        $content .= '• 此操作不可撤銷<br>';
        $content .= '• 建議先備份重要內容<br>';
        $content .= '• 替換後將自動更新文章修改時間';
        $content .= '</div>';

        return [
            'content' => $content,
            'quickActions' => [
                [
                    'id' => 'confirm_batch_execute',
                    'text' => '✅ 確認執行 - 開始批量替換',
                    'action' => ChatActions::CONFIRM_EXECUTE,
                    'data' => [
                        'sourceText' => $sourceText,
                        'targetText' => $targetText,
                        'articleIds' => $articleIds,
                        'replaceType' => 'batch' // 添加替換類型標識
                    ],
                    'style' => 'primary' // 綠色確認按鈕
                ],
                [
                    'id' => 'cancel_operation',
                    'text' => '❌ 取消操作 - 返回選擇替換範圍',
                    'action' => ChatActions::CANCEL_OPERATION,
                    'data' => [
                        'sourceText' => $sourceText,
                        'targetText' => $targetText
                    ],
                    'style' => 'danger' // 紅色取消按鈕
                ]
            ],
            'toolData' => [
                'sourceText' => $sourceText,
                'targetText' => $targetText,
                'articleIds' => $articleIds,
                'totalArticles' => $totalArticles,
                'totalMatches' => $totalMatches
            ]
        ];
    }


       /**
     * 处理单独文章选择
     */
    private static function handleIndividualArticleSelection(array $payload): array
    {
        // 从 reply_data 中获取实际数据
        $replyData = $payload['reply_data'] ?? [];
        $sourceText = $replyData['sourceText'] ?? '';
        $targetText = $replyData['targetText'] ?? '';
        $searchResults = $replyData['searchResults'] ?? [];
        
        if (empty($searchResults)) {
            return self::createErrorResponse('沒有可選擇的文章');
        }

        $content = '請選擇要進行替換的文章：<br><br>';
        $content .= '將 <strong>"' . htmlspecialchars($sourceText) . '"</strong> 替換為 <strong>"' . htmlspecialchars($targetText) . '"</strong><br><br>';

        $quickActions = [];
        
        // 为每篇文章创建选择按钮，优化显示格式
        foreach ($searchResults as $article) {
            // 處理標題為空的情況
            $title = $article['title'] ?? '未知標題';
            if (empty($title) || $title === null) {
                $title = "文章 ID: {$article['id']}";
            }
            
            // 處理分類信息
            $category = $article['category'] ?? '未分類';
            
            // 處理創建時間
            $createdAt = $article['created_at'] ?? '';
            
            // 處理匹配數
            $matches = $article['matches'] ?? 0;
            
            // 按照截圖格式構建顯示文本
            $displayText = sprintf(
                "📄 %s\n分類：%s｜匹配：%s處｜發佈：%s",
                $title,
                $category,
                $matches,
                $createdAt
            );
            
            $quickActions[] = [
                'id' => "article_{$article['id']}",
                'text' => $displayText,
                'action' => ChatActions::SELECT_ARTICLE,
                'data' => [
                    'articleId' => $article['id'],
                    'sourceText' => $sourceText,
                    'targetText' => $targetText
                ],
                'isArticle' => true
            ];
        }
        
        // 添加更多操作按钮
        // $quickActions[] = [
        //     'id' => 'show_more_articles',
        //     'text' => '📄 显示更多文章',
        //     'action' => ChatActions::SHOW_MORE_ARTICLES,
        //     'data' => [
        //         'sourceText' => $sourceText,
        //         'targetText' => $targetText,
        //         'currentResults' => $searchResults
        //     ],
        //     'style' => 'secondary'
        // ];
        
        $quickActions[] = [
            'id' => 'search_specific',
            'text' => '🔍 搜尋特定文章',
            'action' => ChatActions::SEARCH_SPECIFIC_ARTICLE,
            'data' => [
                'sourceText' => $sourceText,
                'targetText' => $targetText
            ],
            'style' => 'secondary'
        ];
        
        $quickActions[] = [
            'id' => 'back_to_search_results',
            'text' => '⬅️ 返回替換範圍選擇',
            'action' => ChatActions::BACK_TO_SEARCH,
            'data' => [
                'sourceText' => $sourceText,
                'targetText' => $targetText
            ],
            'style' => 'secondary'
        ];

        return [
            'content' => $content,
            'quickActions' => $quickActions,
            'toolData' => [
                'sourceText' => $sourceText,
                'targetText' => $targetText,
                'searchResults' => $searchResults
            ]
        ];
    }


        /**
     * 处理返回搜索
     */
    private static function handleBackToSearch(): array
    {
        return self::handleInitialActivation();
    }


        /**
     * 处理初始激活
     */
    public static function handleInitialActivation(): array
    {
        \Log::info("文章替换工具初始化激活", [
            'tool' => 'article_content_replacer',
            'method' => 'handleInitialActivation'
        ]);
        
        $response = [
            'content' => self::getTemplate('textReplaceMenu'),
            'quickActions' => [
                [
                    'id' => 'ai_replace',
                    'text' => '🔄 AI → 人工智慧',
                    'action' => ChatActions::QUICK_REPLACE,
                    'data' => [
                        'sourceText' => 'AI',
                        'targetText' => '人工智慧'
                    ]
                ],
                [
                    'id' => 'ml_replace',
                    'text' => '🔄 ML → 機器學習',
                    'action' => ChatActions::QUICK_REPLACE,
                    'data' => [
                        'sourceText' => 'ML',
                        'targetText' => '機器學習'
                    ]
                ],
                [
                    'id' => 'iot_replace',
                    'text' => '🔄 IoT → 物聯網',
                    'action' => ChatActions::QUICK_REPLACE,
                    'data' => [
                        'sourceText' => 'IoT',
                        'targetText' => '物聯網'
                    ]
                ],
                // [
                //     'id' => 'help',
                //     'text' => '❓ 查看幫助',
                //     'action' => ChatActions::SHOW_HELP
                // ],
                [
                    'id' => 'exit',
                    'text' => '❌ 退出',
                    'action' => ChatActions::EXIT_TOOL
                ]
            ],
            'resetSession' => true
        ];
        
        \Log::info("文章替换工具响应", [
            'content_preview' => substr($response['content'], 0, 100),
            'quickActions_count' => count($response['quickActions']),
            'resetSession' => $response['resetSession']
        ]);
        
        return $response;
    }


        /**
     * 处理单个文章选择
     */
    private static function handleSingleArticleSelection(array $payload): array
    {
        $replyData = $payload['reply_data'] ?? [];
        $articleId = $replyData['articleId'] ?? null;
        $sourceText = $replyData['sourceText'] ?? '';
        $targetText = $replyData['targetText'] ?? '';
        
        if (empty($articleId) || empty($sourceText) || empty($targetText)) {
            return self::createErrorResponse('文章選擇參數不完整');
        }

        // 計算真實匹配數
        $matchCount = self::calculateSingleArticleMatches($articleId, $sourceText);
        
        $content = '您選擇了文章進行替換：<br><br>';
        $content .= '📄 <strong>文章ID: ' . $articleId . '</strong><br><br>';
        $content .= '📊 <strong>替換詳情：</strong><br>';
        $content .= '原文本：<strong>"' . htmlspecialchars($sourceText) . '"</strong><br>';
        $content .= '新文本：<strong>"' . htmlspecialchars($targetText) . '"</strong><br>';
        $content .= '匹配數量：<strong>' . $matchCount . '處</strong><br><br>';

        return [
            'content' => $content,
            'quickActions' => [
                [
                    'id' => 'confirm_single_replace',
                    'text' => '✅ 確認替換此文章',
                    'action' => ChatActions::CONFIRM_EXECUTE,
                    'data' => [
                        'articleIds' => [$articleId],
                        'sourceText' => $sourceText,
                        'targetText' => $targetText,
                        'replaceType' => 'single'
                    ],
                    'style' => 'primary'
                ],
                [
                    'id' => 'back_to_selection',
                    'text' => '🔙 返回文章選擇',
                    'action' => ChatActions::BACK_TO_ARTICLE_LIST,
                    'data' => [
                        'sourceText' => $sourceText,
                        'targetText' => $targetText
                    ],
                    'style' => 'secondary'
                ]
            ],
            'toolData' => ['articleId' => $articleId, 'sourceText' => $sourceText, 'targetText' => $targetText]
        ];
    }


       /**
     * 处理显示更多文章
     */
    private static function handleShowMoreArticles(array $payload): array
    {
        $replyData = $payload['reply_data'] ?? [];
        $sourceText = $replyData['sourceText'] ?? '';
        $targetText = $replyData['targetText'] ?? '';
        $currentResults = $replyData['currentResults'] ?? [];
        
        // 这里可以实现分页逻辑，暂时返回提示
        $content = '📄 <strong>顯示更多文章功能</strong><br><br>';
        $content .= '當前已顯示 ' . count($currentResults) . ' 篇文章。<br><br>';
        $content .= '💡 您可以：<br>';
        $content .= '• 搜尋特定文章標題<br>';
        $content .= '• 返回重新設置搜尋條件<br>';
        $content .= '• 從當前結果中選擇文章';

        return [
            'content' => $content,
            'quickActions' => [
                [
                    'id' => 'search_specific',
                    'text' => '🔍 搜尋特定文章',
                    'action' => ChatActions::SEARCH_SPECIFIC_ARTICLE,
                    'data' => [
                        'sourceText' => $sourceText,
                        'targetText' => $targetText
                    ],
                    'style' => 'primary'
                ],
                [
                    'id' => 'back_to_selection',
                    'text' => '🔙 返回文章選擇',
                    'action' => ChatActions::SELECT_INDIVIDUAL_ARTICLES,
                    'data' => [
                        'sourceText' => $sourceText,
                        'targetText' => $targetText,
                        'searchResults' => $currentResults
                    ],
                    'style' => 'secondary'
                ]
            ],
            'toolData' => ['sourceText' => $sourceText, 'targetText' => $targetText]
        ];
    }


        /**
     * 处理搜索特定文章
     */
    private static function handleSearchSpecificArticle(array $payload): array
    {
        $replyData = $payload['reply_data'] ?? [];
        $sourceText = $replyData['sourceText'] ?? '';
        $targetText = $replyData['targetText'] ?? '';
        
        $content = '🔍 <strong>搜尋特定文章</strong><br><br>';
        $content .= '請輸入文章標題關鍵詞進行搜尋：';

        return [
            'content' => $content,
            'inputField' => [
                'type' => 'text',
                'placeholder' => '請輸入文章標題關鍵詞...',
                'required' => true,
                'submitText' => '🔍 搜尋',
                'cancelText' => '❌ 取消'
            ],
            'quickActions' => [
                [
                    'id' => 'cancel_search',
                    'text' => '❌ 取消搜尋',
                    'action' => ChatActions::CANCEL_SEARCH,
                    'data' => [
                        'sourceText' => $sourceText,
                        'targetText' => $targetText
                    ],
                    'style' => 'secondary'
                ]
            ],
            'toolData' => [
                'sourceText' => $sourceText, 
                'targetText' => $targetText
            ]
        ];
    }


        /**
     * 处理返回文章列表
     */
    private static function handleBackToArticleList(array $payload): array
    {
        $replyData = $payload['reply_data'] ?? [];
        $sourceText = $replyData['sourceText'] ?? '';
        $targetText = $replyData['targetText'] ?? '';
        
        if (empty($sourceText) || empty($targetText)) {
            return self::createErrorResponse('缺少必要的替換參數');
        }

        // 重新搜索文章以显示列表
        $searchResults = self::searchArticlesByKeyword($sourceText);
        
        if (empty($searchResults)) {
            return [
                'content' => "❌ 未找到包含 <strong>\"$sourceText\"</strong> 的文章",
                'quickActions' => [
                    [
                        'id' => 'back_to_search',
                        'text' => '🔙 返回搜尋',
                        'action' => ChatActions::BACK_TO_SEARCH,
                        'data' => [
                            'sourceText' => $sourceText,
                            'targetText' => $targetText
                        ],
                        'style' => 'secondary'
                    ]
                ],
                'toolData' => ['sourceText' => $sourceText, 'targetText' => $targetText]
            ];
        }

        // 直接调用文章选择处理，但传入搜索结果
        return self::handleIndividualArticleSelection([
            'reply_data' => [
                'sourceText' => $sourceText,
                'targetText' => $targetText,
                'searchResults' => $searchResults
            ]
        ]);
    }



        /**
     * 处理执行搜索
     */
    private static function handleExecuteSearch(array $payload, array $session): array
    {
        $replyData = $payload['reply_data'] ?? [];
        $searchKeyword = $replyData['searchKeyword'] ?? '';
        if (empty($searchKeyword)) {
            return self::createErrorResponse('請輸入搜尋關鍵詞');
        }

        // 直接使用传入的session
        return self::handleSearchInput($searchKeyword, $session);
    }



    /**
     * 处理取消搜索
     */
    private static function handleCancelSearch(array $payload, array $session): array
    {
        $replyData = $payload['reply_data'] ?? [];
        $sourceText = $replyData['sourceText'] ?? '';
        $targetText = $replyData['targetText'] ?? '';
        
        if (empty($sourceText) || empty($targetText)) {
            return self::handleInitialActivation();
        }

        // 返回到单独文章选择界面
        $searchResults = self::searchArticlesByKeyword($sourceText);
        
        if (empty($searchResults)) {
            return self::handleInitialActivation();
        }

        return self::handleIndividualArticleSelection([
            'reply_data' => [
                'sourceText' => $sourceText,
                'targetText' => $targetText,
                'searchResults' => $searchResults
            ]
        ]);
    }



        /**
     * 处理搜索输入
     */
    public static function handleSearchInput(string $searchKeyword, array $session): array
    {
        // 从session中获取参数（简化获取逻辑）
        $toolParams = $session['toolParams'] ?? [];
        $sourceText = $toolParams['sourceText'] ?? '';
        $targetText = $toolParams['targetText'] ?? '';
        
        // 清理搜索关键词
        $searchKeyword = trim($searchKeyword);
        
        if (empty($searchKeyword)) {
            return [
                'content' => '❌ 請輸入有效的搜尋關鍵詞',
                'quickActions' => [
                    [
                        'id' => 'retry_search',
                        'text' => '🔄 重新搜尋',
                        'action' => ChatActions::SEARCH_SPECIFIC_ARTICLE,
                        'data' => [
                            'sourceText' => $sourceText,
                            'targetText' => $targetText
                        ],
                        'style' => 'primary'
                    ]
                ],
                'toolData' => ['sourceText' => $sourceText, 'targetText' => $targetText]
            ];
        }

        // 根据关键词搜索文章
        $searchResults = self::searchArticlesByKeyword($searchKeyword);
        
        if (empty($searchResults)) {
            return [
                'content' => "❌ 沒有找到包含 <strong>\"$searchKeyword\"</strong> 的文章。",
                'quickActions' => [
                    [
                        'id' => 'retry_search',
                        'text' => '🔄 重新搜尋',
                        'action' => ChatActions::SEARCH_SPECIFIC_ARTICLE,
                        'data' => [
                            'sourceText' => $sourceText,
                            'targetText' => $targetText
                        ],
                        'style' => 'primary'
                    ],
                    [
                        'id' => 'back_to_article_list',
                        'text' => '🔙 返回文章列表',
                        'action' => ChatActions::BACK_TO_ARTICLE_LIST,
                        'data' => [
                            'sourceText' => $sourceText,
                            'targetText' => $targetText
                        ],
                        'style' => 'secondary'
                    ]
                ],
                'toolData' => ['sourceText' => $sourceText, 'targetText' => $targetText]
            ];
        }

        // 顯示搜尋結果（不進行二次篩選，用戶搜尋什麼就顯示什麼）
        $content = "🔍 <strong>搜尋結果</strong><br><br>";
        $content .= "根據關鍵詞 <strong>\"$searchKeyword\"</strong> 找到了 " . count($searchResults) . " 篇文章：<br><br>";

        $quickActions = [];
        
        // 添加文章选择按钮（最多显示10篇）
        $displayArticles = array_slice($searchResults, 0, 10);
        foreach ($displayArticles as $article) {
            // 處理標題為空的情況
            $title = $article['title'] ?? '未知標題';
            if (empty($title) || $title === null) {
                $title = "文章 ID: {$article['id']}";
            }
            
            // 處理分類信息
            $category = $article['category'] ?? '未分類';
            
            // 處理創建時間
            $createdAt = $article['created_at'] ?? '';
            
            // 處理匹配數
            $matches = $article['matches'] ?? 0;
            
            // 按照和單獨篩選文章相同的格式構建顯示文本
            $displayText = sprintf(
                "📄 %s\n分類：%s｜匹配：%s處｜發佈：%s",
                $title,
                $category,
                $matches,
                $createdAt
            );
            
            $quickActions[] = [
                'id' => 'select_article_' . $article['id'],
                'text' => $displayText,
                'action' => ChatActions::SELECT_ARTICLE,
                'data' => [
                    'articleId' => $article['id'],
                    'sourceText' => $sourceText,
                    'targetText' => $targetText
                ],
                'isArticle' => true
            ];
        }

        // 添加操作按鈕
        $quickActions[] = [
            'id' => 'search_again',
            'text' => '🔍 重新搜尋',
            'action' => ChatActions::SEARCH_SPECIFIC_ARTICLE,
            'data' => [
                'sourceText' => $sourceText,
                'targetText' => $targetText
            ],
            'style' => 'secondary'
        ];

        $quickActions[] = [
            'id' => 'back_to_article_list',
            'text' => '🔙 返回文章列表',
            'action' => ChatActions::BACK_TO_ARTICLE_LIST,
            'data' => [
                'sourceText' => $sourceText,
                'targetText' => $targetText
            ],
            'style' => 'secondary'
        ];

        return [
            'content' => $content,
            'quickActions' => $quickActions,
            'toolData' => ['sourceText' => $sourceText, 'targetText' => $targetText, 'searchResults' => $searchResults]
        ];
    }


        /**
     * 计算总匹配数
     */
    private static function calculateTotalMatches(array $articleIds, string $sourceText): int
    {
        if (empty($articleIds) || empty($sourceText)) {
            return 0;
        }

        try {
            // 获取文章搜索服务实例
            $articleSearchService = app(ArticleSearchService::class);
            
            $totalMatches = 0;
            
            // 批量获取文章详情
            foreach ($articleIds as $articleId) {
                try {
                    // 获取文章详情
                    $articleDetail = $articleSearchService->getArticleDetail($articleId);
                    
                    if (!$articleDetail) {
                        continue;
                    }
                    
                    // 统计各字段中的匹配数量
                    $matches = 0;
                    
                    // 在标题中搜索匹配
                    if (!empty($articleDetail['title'])) {
                        $matches += substr_count(strtolower($articleDetail['title']), strtolower($sourceText));
                    }
                    
                    // 在摘要中搜索匹配
                    if (!empty($articleDetail['summary'])) {
                        $matches += substr_count(strtolower($articleDetail['summary']), strtolower($sourceText));
                    }
                    
                    // 在内容中搜索匹配
                    if (!empty($articleDetail['content'])) {
                        $matches += substr_count(strtolower($articleDetail['content']), strtolower($sourceText));
                    }
                    
                    $totalMatches += $matches;
                    
                    \Log::debug("文章匹配统计", [
                        'articleId' => $articleId,
                        'sourceText' => $sourceText,
                        'matches' => $matches
                    ]);
                    
                } catch (\Exception $e) {
                    \Log::warning("统计文章匹配数时出错", [
                        'articleId' => $articleId,
                        'error' => $e->getMessage()
                    ]);
                    // 如果某篇文章出错，给一个默认值
                    $totalMatches += 1;
                }
            }
            
            \Log::info("总匹配数统计完成", [
                'articleIds' => $articleIds,
                'sourceText' => $sourceText,
                'totalMatches' => $totalMatches
            ]);
            
            return $totalMatches;
            
        } catch (\Exception $e) {
            \Log::error("计算总匹配数时出错", [
                'articleIds' => $articleIds,
                'sourceText' => $sourceText,
                'error' => $e->getMessage()
            ]);
            
            // 如果出错，返回一个基于文章数量的估算值
            return count($articleIds) * 2;
        }
    }

    /**
     * 计算单篇文章的匹配数
     */
    private static function calculateSingleArticleMatches(int $articleId, string $sourceText): int
    {
        try {
            // 获取文章搜索服务实例
            $articleSearchService = app(ArticleSearchService::class);
            
            // 获取文章详情
            $articleDetail = $articleSearchService->getArticleDetail($articleId);
            
            if (!$articleDetail) {
                return 0;
            }
            
            // 统计各字段中的匹配数量
            $matches = 0;
            
            // 在标题中搜索匹配
            if (!empty($articleDetail['title'])) {
                $matches += substr_count(strtolower($articleDetail['title']), strtolower($sourceText));
            }
            
            // 在摘要中搜索匹配
            if (!empty($articleDetail['summary'])) {
                $matches += substr_count(strtolower($articleDetail['summary']), strtolower($sourceText));
            }
            
            // 在内容中搜索匹配
            if (!empty($articleDetail['content'])) {
                $matches += substr_count(strtolower($articleDetail['content']), strtolower($sourceText));
            }
            
            return $matches;
            
        } catch (\Exception $e) {
            \Log::warning("计算单篇文章匹配数时出错", [
                'articleId' => $articleId,
                'sourceText' => $sourceText,
                'error' => $e->getMessage()
            ]);
            
            // 如果出错，返回默认值
            return 1;
        }
    }

    /**
     * 计算预计完成时间
     */
    private static function calculateEstimatedTime(int $articleCount): string
    {
        // 假設每篇文章處理需要2秒
        $seconds = $articleCount * 2;
        
        if ($seconds < 60) {
            return "約{$seconds}秒";
        } elseif ($seconds < 3600) {
            $minutes = ceil($seconds / 60);
            return "約{$minutes}分鐘";
        } else {
            $hours = ceil($seconds / 3600);
            return "約{$hours}小時";
        }
    }

    /**
     * 搜索包含关键词的文章
     */
    public static function searchArticlesByKeyword(string $keyword, string $moduleType = 'all'): array
    {
        try {
            // 获取文章搜索服务实例
            $articleSearchService = app(ArticleSearchService::class);
            
            // 暂时注释掉关键词搜索，先查询所有文章保证能看到数据
            // TODO: 后续再加入关键词搜索条件
            
            // 如果关键词为空或者我们想要查看所有文章，使用一个通用的搜索词
            $searchKeyword = empty(trim($keyword)) ? '' : $keyword;
            
            // 临时方案：先获取所有文章，忽略关键词
            $results = $articleSearchService->searchByKeyword($searchKeyword, $moduleType, 50);
            
            \Log::info("搜索文章结果", [
                'keyword' => $keyword,
                'moduleType' => $moduleType,
                'resultCount' => count($results)
            ]);
            
            return $results;
            
        } catch (\Exception $e) {
            \Log::error("搜索文章时出错", [
                'keyword' => $keyword,
                'moduleType' => $moduleType,
                'error' => $e->getMessage()
            ]);
            
            // 如果出错，返回空数组
            return [];
        }
    }


      /**
     * 格式化搜索结果
     */
    public static function formatSearchResults(array $searchResults, string $sourceText = '', string $targetText = ''): array
    {
        if (empty($searchResults)) {
            return [
                'content' => '❌ 未找到包含指定文本的文章',
                'quickActions' => [
                    [
                        'id' => 'retry',
                        'text' => '🔄 重新搜尋',
                        'action' => 'RETRY_SEARCH'
                    ],
                    [
                        'id' => 'help',
                        'text' => '❓ 查看幫助',
                        'action' => 'SHOW_HELP'
                    ]
                ],
                'toolData' => ['searchResults' => []]
            ];
        }

        // 统计数据
        $totalArticles = count($searchResults);
        $articleIds = array_column($searchResults, 'id');
        $totalMatches = self::calculateTotalMatches($articleIds, $sourceText);

        // 按模块动态统计
        $moduleStats = [];
        foreach ($searchResults as $article) {
            $module = $article['module'] ?? 'pages';
            $moduleStats[$module] = ($moduleStats[$module] ?? 0) + 1;
        }

        // 構建搜尋結果內容，符合截圖樣式
        $content = '🔍 <strong>搜尋結果：</strong><br><br>';
        
        // 數據統計卡片
        $content .= '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin: 12px 0;">';
        $content .= '<div style="text-align: center; background: #f0f9ff; padding: 16px; border-radius: 8px; border: 1px solid #e0f2fe;">';
        $content .= '<div style="font-size: 24px; font-weight: 600; color: #0284c7;">' . $totalArticles . '</div>';
        $content .= '<div style="font-size: 14px; color: #64748b;">篇文章</div>';
        $content .= '</div>';
        $content .= '<div style="text-align: center; background: #f0f9ff; padding: 16px; border-radius: 8px; border: 1px solid #e0f2fe;">';
        $content .= '<div style="font-size: 24px; font-weight: 600; color: #0284c7;">' . $totalMatches . '</div>';
        $content .= '<div style="font-size: 14px; color: #64748b;">處匹配項</div>';
        $content .= '</div>';
        $content .= '</div>';
        
        // 動態分類統計
        $content .= '<strong>涉及分類：</strong><br>';
        
        // 模組名稱映射
        $moduleNameMap = [
            'news' => '新聞資訊',
            'pages' => '頁面管理',
            'products' => '產品介紹',
            'blogs' => '技術博客'
        ];
        
        // 顯示實際的分類統計
        foreach ($moduleStats as $module => $count) {
            $displayName = $moduleNameMap[$module] ?? $module;
            $content .= '• ' . $displayName . '：' . $count . '篇<br>';
        }
        
        $content .= '<br><strong>請選擇替換範圍：</strong>';
        
        // 構建快捷操作按鈕，符合截圖樣式
        $quickActions = [
            [
                'id' => 'replace_all',
                'text' => '🔄 全部替換 - 替換所有' . $totalArticles . '篇文章',
                'action' => ChatActions::BATCH_REPLACE_ALL,
                'data' => [
                    'sourceText' => $sourceText,
                    'targetText' => $targetText,
                    'articleIds' => array_column($searchResults, 'id')
                ],
                'style' => 'primary' // 主要按鈕樣式
            ],
            [
                'id' => 'select_individual',
                'text' => '📝 單獨替換 - 選擇特定文章進行替換',
                'action' => ChatActions::SELECT_INDIVIDUAL_ARTICLES,
                'data' => [
                    'sourceText' => $sourceText,
                    'targetText' => $targetText,
                    'searchResults' => $searchResults
                ],
                'style' => 'secondary' // 次要按鈕樣式
            ],
            [
                'id' => 'back_to_search',
                'text' => '⬅️ 返回上一步 - 重新輸入搜尋條件',
                'action' => ChatActions::BACK_TO_SEARCH,
                'data' => [],
                'style' => 'secondary' // 次要按鈕樣式
            ]
        ];

        return [
            'content' => $content,
            'quickActions' => $quickActions,
            'toolData' => [
                'searchResults' => $searchResults,
                'sourceText' => $sourceText,
                'targetText' => $targetText,
                'statistics' => [
                    'totalArticles' => $totalArticles,
                    'totalMatches' => $totalMatches,
                    'moduleStats' => $moduleStats
                ]
            ]
        ];
    }


       /**
     * 执行实际的替换操作
     */
    public static function executeReplacement(array $articleIds, string $sourceText, string $targetText, string $replaceType = 'batch'): array
    {
        try {
            // 获取文章搜索服务实例
            $articleSearchService = app(ArticleSearchService::class);
            
            $totalArticles = count($articleIds);
            $isSingleReplace = $replaceType === 'single';
            
            \Log::info("开始执行替换操作", [
                'articleIds' => $articleIds,
                'sourceText' => $sourceText,
                'targetText' => $targetText,
                'replaceType' => $replaceType,
                'totalArticles' => $totalArticles
            ]);
            
            // 执行批量替换
            $results = $articleSearchService->batchReplaceContent($articleIds, $sourceText, $targetText);
            // 统计替换结果
            $successCount = 0;
            $totalReplacements = 0;
            $failedArticles = [];
            
            foreach ($results as $result) {
                if ($result['success']) {
                    $successCount++;
                    $totalReplacements += $result['replacementCount'];
                } else {
                    $failedArticles[] = [
                        'id' => $result['id'],
                        'error' => $result['error']
                    ];
                }
            }
            
            // 構建簡約的完成消息
            $completionMessage = $isSingleReplace ? 
                '✅ 文章替換完成！' : 
                '✅ 文章替換完成！';
                
            $content = $completionMessage . '<br><br>';
            
            // 簡約統計信息 - 使用淺綠色背景，去掉邊框
            $content .= '<div style="background: #f0fdf4; padding: 16px; border-radius: 8px; margin: 16px 0;">';
            $content .= '<div style="text-align: center;">';
            $content .= '成功處理：<strong>' . $successCount . '/' . $totalArticles . '</strong> 篇文章<br>';
            $content .= '總替換數：<strong>' . $totalReplacements . '</strong> 處';
            $content .= '</div>';
            $content .= '</div>';
            
            // 如果有失敗的文章，簡約顯示錯誤信息
            if (!empty($failedArticles)) {
                $content .= '<div style="background: #fef2f2; padding: 12px; border-radius: 6px; margin: 12px 0; text-align: center;">';
                $content .= '失敗文章：<strong>' . count($failedArticles) . '</strong> 篇';
                $content .= '</div>';
            }
            
            return [
                'content' => $content,
                'quickActions' => [
                    [
                        'id' => 'new_replacement',
                        'text' => '🔄 開始新的替換',
                        'action' => 'NEW_REPLACEMENT',
                        'style' => 'primary'
                    ],
                    [
                        'id' => 'exit_tool',
                        'text' => '❌ 退出工具',
                        'action' => ChatActions::EXIT_TOOL,
                        'style' => 'secondary'
                    ]
                ],
                'toolData' => [
                    'completed' => true,
                    'results' => $results,
                    'statistics' => [
                        'totalArticles' => $totalArticles,
                        'successCount' => $successCount,
                        'totalReplacements' => $totalReplacements,
                        'failedCount' => count($failedArticles)
                    ]
                ]
            ];
            
        } catch (\Exception $e) {
            \Log::error("执行替换操作时出错", [
                'articleIds' => $articleIds,
                'sourceText' => $sourceText,
                'targetText' => $targetText,
                'error' => $e->getMessage()
            ]);
            
            return self::createErrorResponse('替換操作失敗：' . $e->getMessage());
        }
    }



    /**
     * 显示示例
     */
    public static function showExamples(): array
    {
        return [
            'content' => '📝 <strong>替換示例</strong><br><br>' .
                        '以下是一些常用的替換示例：',
            'quickActions' => [
                [
                    'id' => 'example1',
                    'text' => '🔄 AI → 人工智慧',
                    'action' => 'QUICK_REPLACE',
                    'data' => ['sourceText' => 'AI', 'targetText' => '人工智慧']
                ],
                [
                    'id' => 'example2',
                    'text' => '🔄 ML → 機器學習',
                    'action' => 'QUICK_REPLACE',
                    'data' => ['sourceText' => 'ML', 'targetText' => '機器學習']
                ],
                [
                    'id' => 'example3',
                    'text' => '🔄 IoT → 物聯網',
                    'action' => 'QUICK_REPLACE',
                    'data' => ['sourceText' => 'IoT', 'targetText' => '物聯網']
                ],
                [
                    'id' => 'back',
                    'text' => '🔙 返回',
                    'action' => 'BACK_TO_MAIN'
                ]
            ]
        ];
    }



    /**
     * 获取消息模板
     */
    private static function getTemplate(string $templateName): string
    {
        $templates = [
            'textReplaceMenu' => '📝 <strong>文章內容替換工具已激活</strong><br><br>🎯 我可以幫您批量替換文章中的文本內容，支持以下功能：<br><br>• 🔍 預覽受影響的文章<br>• 📊 按模塊篩選（新聞、產品、頁面、博客）<br>• ⚡ 快速替換常用術語<br>• 📈 實時查看替換統計<br><br>請告訴我您想要替換的內容，或選擇下方的快捷操作：',
            'searchResult' => '🔍 <strong>搜尋結果：</strong><br><br><div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin: 12px 0; background: #f8f9fa; padding: 16px; border-radius: 8px;"><div style="text-align: center; background: white; padding: 12px; border-radius: 8px;"><div style="font-size: 18px; font-weight: 600; color: #6BBAD2;">{{totalArticles}}</div><div style="font-size: 12px; color: #666;">篇文章</div></div><div style="text-align: center; background: white; padding: 12px; border-radius: 8px;"><div style="font-size: 18px; font-weight: 600; color: #6BBAD2;">{{totalMatches}}</div><div style="font-size: 12px; color: #666;">處匹配項</div></div></div><strong>涉及分類：</strong><br>• 技術博客：{{techBlogCount}}篇<br>• 行業資訊：{{newsCount}}篇<br>• 產品介紹：{{productCount}}篇<br><br>請選擇替換範圍：',
            'batchConfirm' => '您选择了全部替换模式。请确认批量替换详情：<br><br><div style="background: #f8f9fa; padding: 16px; border-radius: 8px; margin: 12px 0;"><p><strong>📊 替换概览：</strong></p><p>原文本：<strong>"{{fromText}}"</strong></p><p>新文本：<strong>"{{toText}}"</strong></p><p>影响范围：<strong>{{totalArticles}}篇文章，{{totalMatches}}处匹配</strong></p><p>预计完成时间：<strong>{{estimatedTime}}</strong></p></div><div style="background: #fff3cd; padding: 12px; border-radius: 8px; border-left: 4px solid #ffc107;"><p><strong>⚠️ 注意事项：</strong></p><ul style="margin: 8px 0; padding-left: 20px; font-size: 13px;"><li>此操作不可撤销</li><li>建议先备份重要内容</li><li>替换后将自动更新文章修改时间</li></ul></div>',
            'batchComplete' => '🎉 <strong>批量替换完成！</strong>所有"{{fromText}}"已成功替换为"{{toText}}"。<br><br><div style="background: #d4edda; padding: 16px; border-radius: 8px; margin: 12px 0;"><p><strong>📈 替换详情：</strong></p><ul style="margin: 8px 0; padding-left: 20px;"><li>技术博客：{{techBlogCount}}篇 → {{techBlogReplacements}}处替换 ✅</li><li>行业资讯：{{newsCount}}篇 → {{newsReplacements}}处替换 ✅</li><li>产品介绍：{{productCount}}篇 → {{productReplacements}}处替换 ✅</li></ul></div><div style="background: #cce5ff; padding: 12px; border-radius: 8px;"><p><strong>📈 后续建议：</strong></p><ul style="margin: 8px 0; padding-left: 20px; font-size: 13px;"><li>检查关键文章的显示效果</li><li>更新网站Sitemap</li><li>通知内容团队更新完成</li></ul></div>',
            'invalidCommand' => '抱歉，我没有理解您的替换指令。<br><br><strong>支持的格式：</strong><br>• 把\'原文本\'替换成\'新文本\'<br>• 将"AI"改为"人工智能"<br>• AI → 人工智能<br>• 替换\'ML\'为\'机器学习\'',
            'commandHelp' => '<strong>📝 支持的替换指令格式：</strong><br><br>• <code>把\'原文本\'替换成\'新文本\'</code><br>• <code>将"AI"改为"人工智能"</code><br>• <code>AI → 人工智能</code><br>• <code>ML -> 机器学习</code><br>• <code>替换\'iot\'为\'物联网\'</code><br><br>💡 <strong>快捷替换：</strong><br>• 直接输入 <code>ai</code> 替换为 <code>人工智能</code><br>• 直接输入 <code>ml</code> 替换为 <code>机器学习</code><br>• 直接输入 <code>iot</code> 替换为 <code>物联网</code>'
        ];

        return $templates[$templateName] ?? '';
    }

} 