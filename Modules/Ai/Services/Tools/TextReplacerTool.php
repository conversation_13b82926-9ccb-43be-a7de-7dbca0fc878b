<?php

namespace Modules\Ai\Services\Tools;

use Modules\Ai\Services\AI\ParameterExtraction\TextParameterExtractor;
use Modules\Ai\Services\AI\IntentRecognition\TextIntentRecognition;
use Modules\Ai\Services\FileManager\FileManagerService;

class TextReplacerTool extends BaseTool
{

    private TextIntentRecognition $intentService;

    private TextParameterExtractor $parameterExtractor;

    private FileManagerService $fileManager;

    public function __construct(
        TextIntentRecognition $intentService,
        TextParameterExtractor $parameterExtractor,
        FileManagerService $fileManager
    ) {
        $this->intentService = $intentService;
        $this->parameterExtractor = $parameterExtractor;
        $this->fileManager = $fileManager;
        $this->initializeMetadata();
    }

    private const SUPPORTED_EXTENSIONS = [
        'txt', 'md', 'json', 'xml', 'html', 'htm', 'css', 'js',
        'php', 'py', 'java', 'rb', 'c', 'cpp', 'h', 'cs',
        'yml', 'yaml', 'ini', 'conf', 'cfg'
    ];

    private function initializeMetadata(): void
    {
        $this->metadata = [
            'name' => 'text_replacer',
            'description' => '批量替换文本内容的工具，支持单个文件、多个文件或整个文件夹的文本替换',
        ];

        $this->capabilities = [
            'preview' => true,
            'batch_processing' => true,
            'folder_processing' => true,
            'interactive' => true
        ];
    }

    /**
     * 执行文本替换的核心方法
     */
    public function execute(array $parameters, array $context = []): array
    {
        try {
            $sourceText = $parameters['sourceText'] ?? '';
            $targetText = $parameters['targetText'] ?? '';
            $files = $parameters['files'] ?? [];
            $isPreview = $parameters['preview'] ?? false;

            // 参数验证
            if (empty($sourceText) || empty($targetText)) {
                return $this->createErrorResponse('替换文本参数不完整');
            }

            if (empty($files)) {
                return [
                    'content' => '请先上传文件或选择文件夹',
                    'quickActions' => [
                        ['id' => 'upload_file', 'text' => '上传文件'],
                        ['id' => 'select_folder', 'text' => '选择文件夹']
                    ],
                    'toolData' => ['params' => compact('sourceText', 'targetText'), 'files' => []]
                ];
            }

            // 根据模式执行预览或替换
            if ($isPreview) {
                $previewResult = $this->generatePreview($files, $sourceText, $targetText);
                return [
                    'content' => $this->formatPreviewResult($previewResult),
                    'quickActions' => [
                        ['id' => 'cancel', 'text' => '取消'],
                        ['id' => 'execute', 'text' => '执行替换']
                    ],
                    'toolData' => [
                        'params' => compact('sourceText', 'targetText'),
                        'files' => $files,
                        'preview' => $previewResult
                    ]
                ];
            } else {
                $result = $this->executeReplacement($files, $sourceText, $targetText);
                
                // 添加下载按钮
                $downloadActions = [];
                if ($result['status'] === 'success' && !empty($result['data']['files'])) {
                    foreach ($result['data']['files'] as $file) {
                        $downloadActions[] = [
                            'id' => 'download_file',
                            'text' => '下载 ' . $file['name'],
                            'data' => ['path' => $file['path']]
                        ];
                    }
                }

                return [
                    'content' => $this->formatExecutionResult($result),
                    'quickActions' => $downloadActions,
                    'toolData' => [
                        'params' => compact('sourceText', 'targetText'),
                        'files' => $files,
                        'result' => $result
                    ]
                ];
            }

        } catch (\Exception $e) {
            $this->logError('执行替换失败', [
                'error' => $e->getMessage(),
                'parameters' => $parameters
            ]);
            return $this->createErrorResponse($e->getMessage());
        }
    }

    private function isRestartCommand(string $message): bool
    {
        $message = strtolower($message);
        $restartKeywords = ['替换功能', '重新开始', '重新替换', '重新操作'];

        foreach ($restartKeywords as $keyword) {
            if (str_contains($message, strtolower($keyword))) {
                return true;
            }
        }
        return false;
    }

    public function handleMessage(string $message, array $context = []): array
    {
        try {
            // 处理重启命令
            if ($this->isRestartCommand($message) || $message == '') {
                return $this->handleInitialActivation();
            }

            // 获取会话数据
            $session = $context['session'] ?? [];
            if (empty($session)) {
                return $this->createErrorResponse('会话数据缺失');
            }

            // 提取当前状态
            $activeToolInfo = $session['data']['activeTool'] ?? null;
            $data = $activeToolInfo['data'] ?? [];
            $params = $data['params'] ?? [];
            $files = $data['files'] ?? [];

            // 识别意图并尝试提取参数
            $intent = $this->identifyIntent($message);
            if ($intent === 'specify_replacement' || ($intent === 'unknown' && !$this->hasRequiredParams($params))) {
                $extractedParams = $this->extractReplaceParams($message, $session['history'] ?? []);
                if ($extractedParams) {
                    $params = $extractedParams;
                }
            }

            // 处理所有意图
            return $this->processIntent($intent, $message, $params, $files);

        } catch (\Exception $e) {
            $this->logError('处理消息失败', [
                'error' => $e->getMessage(),
                'message' => $message,
                'context' => $context
            ]);

            return [
                'content' => '处理您的请求时发生错误，请稍后重试',
                'releaseControl' => true
            ];
        }
    }

    private function handleInitialActivation(array $params = [], array $files = []): array
    {
        return [
            'content' => "好的，请问你要替换什么？替换前请先上传文件或选择文件夹" ,
            'quickActions' => [
                ['id' => 'upload_file', 'text' => '上传文件'],
                ['id' => 'select_folder', 'text' => '选择文件夹']
            ],
            'toolData' => compact('params', 'files'),
            'resetSession' => true
        ];
    }

    private function processIntent(string $intent, string $message, array $params, array $files): array
    {
        // 如果有文件但参数不完整，优先提示用户指定替换内容
        if (!empty($files) && !$this->hasRequiredParams($params)) {
            return [
                'content' => "请告诉我要替换什么内容，例如：\n将\"旧文本\"替换为\"新文本\"",
                'toolData' => compact('params', 'files')
            ];
        }

        switch ($intent) {
            case 'exit_tool':
                return [
                    'content' => '文本替换工具已退出',
                    'releaseControl' => true
                ];

            case 'specify_replacement':
                return $this->handleReplacementSpecification($params, $files);

            case 'upload_file':
                return $this->handleFileUpload($params, $files);

            case 'select_folder':
                return $this->handleFolderSelection($params, $files);

            case 'generate_preview':
                if ($this->hasRequiredParams($params)) {
                    return $this->execute([
                        'sourceText' => $params['sourceText'],
                        'targetText' => $params['targetText'],
                        'files' => $files,
                        'preview' => true
                    ], []);
                }
                return [
                    'content' => '请先指定要替换的文本内容',
                    'toolData' => compact('params', 'files')
                ];

            case 'execute_replacement':
                if ($this->hasRequiredParams($params)) {
                    return $this->execute([
                        'sourceText' => $params['sourceText'],
                        'targetText' => $params['targetText'],
                        'files' => $files,
                        'preview' => false
                    ], []);
                }
                return [
                    'content' => '请先指定要替换的文本内容',
                    'toolData' => compact('params', 'files')
                ];

            default:
                return $this->handleUnknownIntent($params, $files);
        }
    }

    private function handleReplacementSpecification(array $params, array $files): array
    {
        if (!$this->hasRequiredParams($params)) {
            return [
                'content' => '请明确指定要替换的文本和替换后的文本，例如：\n将"旧文本"替换为"新文本"',
                'toolData' => compact('params', 'files')
            ];
        }

        return [
            'content' => "将把\"{$params['sourceText']}\"替换为\"{$params['targetText']}\"" .
                        (empty($files) ? "\n请上传需要处理的文件或选择文件夹" : "\n是否生成预览？"),
            'quickActions' => empty($files) ? [
                ['id' => 'upload_file', 'text' => '上传文件'],
                ['id' => 'select_folder', 'text' => '选择文件夹']
            ] : [
                ['id' => 'preview', 'text' => '生成预览'],
                ['id' => 'execute', 'text' => '直接执行']
            ],
            'toolData' => compact('params', 'files')
        ];
    }

    private function handleFileUpload(array $params, array $files): array
    {
        if (!empty($files)) {
            return [
                'content' => '文件已上传，请明确指定要替换的文本和替换后的文本，例如：\n将"旧文本"替换为"新文本"',
                'toolData' => compact('params', 'files')
            ];
        }

        return [
            'content' => '请上传需要处理的文件',
            'upload' => true,
            'toolData' => compact('params', 'files')
        ];
    }

    private function handleFolderSelection(array $params, array $files): array
    {
        return [
            'content' => '请选择要处理的文件夹',
            'folderSelect' => true,
            'toolData' => compact('params', 'files')
        ];
    }


    private function handleUnknownIntent(array $params, array $files): array
    {
        if ($this->hasRequiredParams($params)) {
            return $this->handleReplacementSpecification($params, $files);
        }

        return [
            'content' => '请告诉我您要替换什么内容，例如：\n将"旧文本"替换为"新文本"',
            'toolData' => compact('params', 'files')
        ];
    }

    private function hasRequiredParams(array $params): bool
    {
        return isset($params['sourceText']) && !empty($params['sourceText']) &&
               isset($params['targetText']) && !empty($params['targetText']);
    }

    private function formatPreviewResult(array $result): string
    {
        if ($result['status'] !== 'success') {
            return '预览生成失败：' . ($result['message'] ?? '未知错误');
        }

        $data = $result['data'];
        $output = "预览生成成功！\n\n";
        $output .= "总计找到 {$data['total_matches']} 处匹配\n";
        $output .= "涉及 " . count($data['files']) . " 个文件\n\n";

        foreach ($data['categories'] as $category) {
            $output .= "{$category['name']}: {$category['count']} 个文件，{$category['textCount']} 处匹配\n";
        }

        return $output;
    }

    private function formatExecutionResult(array $result): string
    {
        if ($result['status'] !== 'success') {
            return '替换执行失败：' . ($result['message'] ?? '未知错误');
        }

        $data = $result['data'];
        return "替换执行成功！\n" .
               "总计替换了 {$data['total_replaced']} 处文本\n" .
               "处理了 " . count($data['files']) . " 个文件";
    }

    private function identifyIntent(string $message): string
    {
        try {
            $result = $this->intentService->recognizeIntent($message, [
                'exit_tool' => ['退出', '取消', '结束', '关闭'],
                'specify_replacement' => ['替换', '修改', '改为', '换成'],
                'upload_file' => ['上传', '文件'],
                'select_folder' => ['文件夹', '目录'],
                'generate_preview' => ['预览', '查看'],
                'execute_replacement' => ['执行', '确认', '开始']
            ]);
            return $result['intent'] ?? 'unknown';
        } catch (\Exception $e) {
            $this->logError('意图识别失败', ['error' => $e->getMessage()]);
            return 'unknown';
        }
    }

    private function extractReplaceParams(string $message,array $history): ?array
    {
        $params = $this->parameterExtractor->extractParameters(
            $message,
            'text_replacer',
            ['sourceText', 'targetText'],
            ['history' => $history] // 需要添加获取历史记录的方法
        );
        // 如果必需参数都存在才返回
        if (isset($params['sourceText']) && isset($params['targetText'])) {
            return $params;
        }
        return null;
    }

    /**
     * 检查文件类型是否支持
     */
    private function isFileTypeSupported(string $filename): bool
    {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($extension, self::SUPPORTED_EXTENSIONS);
    }


    private function generatePreview(array $files, string $sourceText, string $targetText): array
    {
        $previewData = $this->fileManager->generatePreview($files, $sourceText);
        return $this->createResponse('success', '预览生成成功', $previewData);
    }

    private function executeReplacement(array $files, string $sourceText, string $targetText): array
    {
        $resultData = $this->fileManager->executeReplacement($files, $sourceText, $targetText);
        return $this->createResponse('success', '替换执行成功', $resultData);
    }


}
