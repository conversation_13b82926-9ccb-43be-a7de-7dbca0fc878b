<?php

declare(strict_types=1);

namespace Modules\Ai\Services\AI\IntentRecognition;

use Modules\Ai\Services\LLM\LLMService;
use Illuminate\Support\Facades\Log;

/**
 * 意图识别基类
 */
abstract class BaseIntentRecognition implements IntentRecognitionContract
{
    protected LLMService $llm;

    public function __construct(LLMService $llm)
    {
        $this->llm = $llm;
    }

    /**
     * 识别用户输入的意图
     *
     * @param string $message 用户输入的消息
     * @param array $intentTypes 意图类型映射
     * @param array $context 上下文信息
     * @return array 返回识别结果
     */
    public function recognizeIntent(string $message, array $intentTypes, array $context = []): array
    {
        $prompt = $this->buildIntentPrompt($message, $intentTypes, $context);
        try {
            $response = $this->llm->chat([
                [
                    'role' => 'system',
                    'content' => $this->getSystemPrompt()
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ]);
            return $this->parseIntentResponse($response);
        } catch (\Exception $e) {
            Log::error($this->getToolName() . '意图识别失败', [
                'error' => $e->getMessage(),
                'message' => $message
            ]);
            
            return $this->getDefaultIntentResult('处理出错: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取系统提示词
     * 
     * @return string 系统提示词
     */
    protected function getSystemPrompt(): string
    {
        return '你是一个工具意图识别助手，需要识别用户输入中的意图。';
    }
    
    /**
     * 获取工具名称
     * 
     * @return string 工具名称
     */
    abstract protected function getToolName(): string;

    /**
     * 构建意图识别的提示词
     *
     * @param string $message 用户输入的消息
     * @param array $intentTypes 意图类型映射
     * @param array $context 上下文信息
     * @return string 返回构建好的提示词
     */
    abstract protected function buildIntentPrompt(string $message, array $intentTypes, array $context): string;

    /**
     * 解析意图识别响应
     *
     * @param string $response LLM返回的响应内容
     * @return array 返回结构化的意图识别结果
     */
    abstract protected function parseIntentResponse(string $response): array;

    /**
     * 获取默认的意图识别结果
     *
     * @param string $reason 原因说明
     * @return array 默认结果
     */
    protected function getDefaultIntentResult(string $reason): array
    {
        return [
            'intent' => 'unknown',
            'confidence' => 0,
            'parameters' => [],
            'reasoning' => $reason
        ];
    }
}
