<?php

namespace Modules\Ai\Services\AI\IntentRecognition;

use Modules\Ai\Services\LLM\LLMService;
use Modules\Ai\Services\Tools\ToolRegistry;
use Illuminate\Support\Facades\Log;

/**
 * 未知工具意图识别器
 * 用于处理无法直接匹配到具体工具时的意图识别
 */
class UnknownToolIntentRecognizer extends BaseIntentRecognition
{
    private ToolRegistry $toolRegistry;
    
    /**
     * 预定义的通用意图类型
     */
    private const GENERAL_INTENTS = [
        'tool_query' => [
            'keywords' => ['怎么用', '有什么工具', '能做什么', '帮助', '使用说明'],
            'description' => '查询工具使用方法或可用工具列表'
        ],
        'tool_suggestion' => [
            'keywords' => ['推荐', '建议', '应该用什么', '该用哪个'],
            'description' => '请求推荐合适的工具'
        ],
        'task_description' => [
            'keywords' => ['我想', '我需要', '帮我', '能否', '可以'],
            'description' => '描述具体任务需求'
        ],
        'error_report' => [
            'keywords' => ['出错', '错误', '失败', '有问题', '不工作'],
            'description' => '报告错误或问题'
        ],
        'general_chat' => [
            'keywords' => ['你好', '谢谢', '再见'],
            'description' => '一般性对话'
        ]
    ];

    public function __construct(LLMService $llm, ToolRegistry $toolRegistry)
    {
        parent::__construct($llm);
        $this->toolRegistry = $toolRegistry;
    }

    protected function getToolName(): string
    {
        return 'unknown_tool_recognizer';
    }

    protected function buildIntentPrompt(string $message, array $intentTypes, array $context): string
    {
        $tools = $this->toolRegistry->getToolDescriptions();
        $toolDescriptions = collect($tools)
            ->map(fn($tool) => "- {$tool['name']}: {$tool['description']}")
            ->implode("\n");

        return <<<EOT
分析用户输入，判断用户可能想要执行的操作或需要的帮助。

可用工具列表：
{$toolDescriptions}

用户输入：{$message}

请分析并返回以下JSON格式结果：
{
    "intent_type": "tool_query|tool_suggestion|task_description|error_report|general_chat",
    "confidence": 0.1-1.0,
    "suggested_tools": ["可能适合的工具名称列表"],
    "user_need": "用户实际需求的简要描述",
    "recommended_action": "建议的下一步操作"
}
EOT;
    }

    protected function parseIntentResponse(string $response): array
    {
        try {
            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \RuntimeException('Invalid JSON response');
            }

            return [
                'intent' => $result['intent_type'] ?? 'unknown',
                'confidence' => $result['confidence'] ?? 0.5,
                'parameters' => [
                    'suggested_tools' => $result['suggested_tools'] ?? [],
                    'user_need' => $result['user_need'] ?? '',
                    'recommended_action' => $result['recommended_action'] ?? ''
                ]
            ];
        } catch (\Exception $e) {
            Log::error('解析未知工具意图响应失败', [
                'error' => $e->getMessage(),
                'response' => $response
            ]);
            return $this->getDefaultResult();
        }
    }

    /**
     * 在调用 LLM 之前先尝试使用规则匹配
     */
    public function recognizeIntent(string $message, array $intentTypes, array $context = []): array
    {
        // 1. 首先尝试规则匹配
        $ruleBasedResult = $this->matchByRules($message);
        if ($ruleBasedResult['confidence'] > 0.8) {
            return $ruleBasedResult;
        }

        // 2. 如果规则匹配置信度不高，使用 LLM
        return parent::recognizeIntent($message, $intentTypes, $context);
    }

    /**
     * 基于预定义规则进行意图匹配
     */
    private function matchByRules(string $message): array
    {
        $lowerMessage = strtolower($message);
        
        // 检查每个通用意图的关键词
        foreach (self::GENERAL_INTENTS as $intentType => $info) {
            foreach ($info['keywords'] as $keyword) {
                if (str_contains($lowerMessage, $keyword)) {
                    return [
                        'intent' => $intentType,
                        'confidence' => 0.9,
                        'parameters' => [
                            'user_need' => $info['description'],
                            'matched_keyword' => $keyword,
                            'suggested_tools' => $this->suggestToolsByIntent($intentType, $message)
                        ]
                    ];
                }
            }
        }

        return [
            'intent' => 'unknown',
            'confidence' => 0.3,
            'parameters' => []
        ];
    }

    /**
     * 根据意图类型推荐可能的工具
     */
    private function suggestToolsByIntent(string $intentType, string $message): array
    {
        $tools = $this->toolRegistry->getToolDescriptions();
        
        // 根据不同意图类型返回合适的工具建议
        switch ($intentType) {
            case 'tool_query':
                // 返回所有工具
                return collect($tools)->pluck('name')->toArray();
                
            case 'task_description':
                // 根据消息内容关键词匹配可能的工具
                return $this->matchToolsByKeywords($message, $tools);
                
            default:
                // 返回最常用的几个工具
                return array_slice(collect($tools)->pluck('name')->toArray(), 0, 3);
        }
    }

    /**
     * 根据关键词匹配可能的工具
     */
    private function matchToolsByKeywords(string $message, array $tools): array
    {
        $matches = [];
        $lowerMessage = strtolower($message);
        
        foreach ($tools as $tool) {
            $relevanceScore = 0;
            
            // 检查工具名称
            if (str_contains($lowerMessage, strtolower($tool['name']))) {
                $relevanceScore += 2;
            }
            
            // 检查工具描述中的关键词
            $keywords = explode(' ', strtolower($tool['description']));
            foreach ($keywords as $keyword) {
                if (strlen($keyword) > 3 && str_contains($lowerMessage, $keyword)) {
                    $relevanceScore += 1;
                }
            }
            
            if ($relevanceScore > 0) {
                $matches[$tool['name']] = $relevanceScore;
            }
        }
        
        // 按相关度排序并返回前3个
        arsort($matches);
        return array_slice(array_keys($matches), 0, 3);
    }

    private function getDefaultResult(): array
    {
        return [
            'intent' => 'unknown',
            'confidence' => 0.3,
            'parameters' => [
                'suggested_tools' => [],
                'user_need' => '未能识别具体需求',
                'recommended_action' => '请更具体地描述您的需求，或查看可用工具列表'
            ]
        ];
    }
}