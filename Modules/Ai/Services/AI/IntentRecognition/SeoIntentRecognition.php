<?php

declare(strict_types=1);

namespace Modules\Ai\Services\AI\IntentRecognition;

use Modules\Ai\Services\LLM\LLMService;

/**
 * SEO分析工具意图识别
 */
class SeoIntentRecognition extends BaseIntentRecognition
{
    /**
     * 获取工具名称
     * 
     * @return string 工具名称
     */
    protected function getToolName(): string
    {
        return 'SEO分析';
    }
    
    /**
     * 获取SEO分析工具的特定提示词
     *
     * @param string $toolType 工具类型
     * @param array $parameterNames 参数名称
     * @return string 工具特定提示词
     */
    protected function getToolSpecificPrompt(string $toolType, array $parameterNames): string
    {
        return "特别注意提取：
        - 「title」(标题)：通常以'标题是'、'标题：'等形式出现
        - 「content」(内容)：通常以'内容是'、'内容：'等形式出现
        - 「keywords」(关键词)：可能是逗号分隔的列表，如'关键词：SEO,优化,网站'
        - 「description」(描述)：通常以'描述是'、'描述：'等形式出现
        - 「url」(网址)：如果提到网址，需要完整提取，包括http://或https://
        - 「locale」(语言)：如果提到语言设置，如'中文'、'英文'等
        - 「article_id」(文章ID)：通常以'文章id是'、'编号是'等形式出现，需要提取数字部分";
    }
    
    /**
     * 构建SEO意图识别专用的提示词
     *
     * @param string $message 用户输入的消息
     * @param array $intentTypes 意图类型映射
     * @param array $context 上下文信息
     * @return string 返回构建好的提示词
     */
    protected function buildIntentPrompt(string $message, array $intentTypes, array $context): string
    {
        // 构建意图类型描述
        $intentDescriptions = [];
        foreach ($intentTypes as $intent => $keywords) {
            $keywordsStr = implode('、', $keywords);
            $intentDescriptions[] = "- {$intent}: 包含关键词（{$keywordsStr}）";
        }
        $intentTypesStr = implode("\n", $intentDescriptions);

        return <<<EOT
请分析以下用户输入的意图，这是在SEO分析工具的上下文中。你需要：
1. 准确理解用户想要进行的SEO分析操作
2. 如果提到具体的内容和标题，需要注意这些可能是分析对象
3. 给出较高的置信度(0.7-1.0)当意图明确时

可能的意图类型包括：
{$intentTypesStr}
- unknown: 未知意图（当无法明确判断时）

意图判断规则（按优先级排序）：
1. 如果用户输入包含文章ID或编号，优先识别为 analyze_by_id
2. 如果用户输入包含完整URL或网址，优先识别为 analyze_by_url
3. 如果用户输入包含"标题："、"标题是"或明确的标题内容，识别为 submit_content
4. 如果用户输入包含"内容："、"内容是"或明确的内容文本，识别为 submit_content
5. 如果用户提到退出、返回等词，识别为 exit_tool
6. 如果用户提到批量、多个等词，识别为 batch_analyze
7. 如果用户提到分析、检查、评估等，识别为 analyze_content
8. 如果不能明确判断意图，返回 unknown，置信度设为 0

特别说明：
- 当输入包含文章ID或URL时，这表示用户想分析特定文章，应将置信度设为 0.9-1.0
- 当输入包含标题或内容的明确标识时，这表示用户在提交内容，应将置信度设为 0.9-1.0

用户输入: {$message}

请返回JSON格式：
{
    "intent": "意图类型",
    "confidence": 置信度(0-1),
    "parameters": {
        "title": "标题(如果有)",
        "content": "内容(如果有)",
        "keywords": "关键词(如果有)",
        "description": "描述(如果有)",
        "url": "网址(如果有)",
        "article_id": "文章ID(如果有)"
    },
    "reasoning": "推理过程说明"
}
EOT;
    }

      /**
     * 解析意图识别响应
     *
     * @param string $response LLM返回的响应内容
     * @return array 返回结构化的意图识别结果
     */
    protected function parseIntentResponse(string $response): array
    {
        try {
            if (preg_match('/\{[\s\S]*\}/m', $response, $matches)) {
                $jsonStr = $matches[0];
                $jsonStr = preg_replace('/[\x00-\x1F\x7F]/', '', $jsonStr);
                $result = json_decode($jsonStr, true);
                
                if (json_last_error() === JSON_ERROR_NONE) {
                    // SEO特定的结果处理
                    if ($result['intent'] === 'submit_content') {
                        $result['parameters'] = array_merge([
                            'title' => '',
                            'content' => '',
                            'keywords' => [],
                            'description' => '',
                        ], $result['parameters'] ?? []);
                        
                        if (!empty($result['parameters']['title']) && 
                            !empty($result['parameters']['content'])) {
                            $result['confidence'] = max($result['confidence'] ?? 0, 0.9);
                        }
                    } elseif ($result['intent'] === 'analyze_by_id') {
                        if (!empty($result['parameters']['article_id'])) {
                            $result['confidence'] = max($result['confidence'] ?? 0, 0.9);
                        }
                    } elseif ($result['intent'] === 'analyze_by_url') {
                        if (!empty($result['parameters']['url'])) {
                            $result['confidence'] = max($result['confidence'] ?? 0, 0.9);
                        }
                    }
                    
                    return $result;
                }
            }
            
            return $this->getDefaultIntentResult('响应格式不正确');
            
        } catch (\Exception $e) {
            return $this->getDefaultIntentResult('解析失败: ' . $e->getMessage());
        }
    }



    
    /**
     * 参数后处理 - 添加SEO专用处理
     *
     * @param array|null $params 提取的参数
     * @param string $toolType 工具类型
     * @return array|null 处理后的参数
     */
    protected function postProcessParameters(?array $params, string $toolType): ?array
    {
        // 如果成功提取了参数，进行SEO专用处理
        if ($params && $toolType === 'seo_analysis') {
            // 处理关键词，确保是数组格式
            if (isset($params['keywords']) && !is_array($params['keywords'])) {
                if (is_string($params['keywords'])) {
                    $params['keywords'] = array_map('trim', explode(',', $params['keywords']));
                } else {
                    $params['keywords'] = [$params['keywords']];
                }
            }
        }
        
        return $params;
    }
}
