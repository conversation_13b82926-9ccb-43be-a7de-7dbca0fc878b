<?php

declare(strict_types=1);

namespace Modules\Ai\Services\AI\IntentRecognition;

use Modules\Ai\Services\LLM\LLMService;

/**
 * 文章内容替换工具意图识别
 */
class ArticleIntentRecognition extends BaseIntentRecognition
{
    protected function getToolName(): string
    {
        return '文章内容替换';
    }

    protected function getToolSpecificPrompt(string $toolType, array $parameterNames): string
    {
        return "特别注意提取：
        - 「sourceText」(原文本)：需要被替换的文本，通常以'将X替换为'、'把X改成'等形式出现
        - 「targetText」(目标文本)：替换后的文本，通常在'替换为Y'、'改成Y'等形式出现
        - 「moduleType」(模块类型)：可能提到的具体模块，如'新闻'、'产品'、'页面'、'博客'等
        - 「articleIds」(文章ID)：如果提到具体文章ID，通常以'文章id是'、'编号是'等形式出现";
    }

    protected function buildIntentPrompt(string $message, array $intentTypes, array $context): string
    {
        // 构建意图类型描述
        $intentDescriptions = [];
        foreach ($intentTypes as $intent => $keywords) {
            $keywordsStr = implode('、', $keywords);
            $intentDescriptions[] = "- {$intent}: 包含关键词（{$keywordsStr}）";
        }
        $intentTypesStr = implode("\n", $intentDescriptions);

        return <<<EOT
请分析以下用户输入的意图，这是在文章内容替换工具的上下文中。你需要：
1. 准确理解用户想要进行的文本替换操作
2. 识别用户想要替换的文本和替换后的文本
3. 给出较高的置信度(0.7-1.0)当意图明确时

可能的意图类型包括：
{$intentTypesStr}
- unknown: 未知意图（当无法明确判断时）

意图判断规则（按优先级排序）：
1. 如果用户输入包含"全部"、"所有"等词，优先识别为 replace_all
2. 如果用户输入包含具体文章ID或提到"选择"、"指定"等词，优先识别为 select_specific
3. 如果用户提到"取消"、"退出"等词，识别为 cancel
4. 如果用户提到"预览"、"查看"等词，识别为 preview
5. 如果不能明确判断意图，返回 unknown，置信度设为 0

特别说明：
- 当输入明确包含替换文本对（原文本和目标文本）时，置信度应设为 0.9-1.0
- 当输入包含具体模块名称时（如新闻、产品等），这些信息应被提取到 parameters 中

用户输入: {$message}

请返回JSON格式：
{
    "intent": "意图类型",
    "confidence": 置信度(0-1),
    "parameters": {
        "sourceText": "要替换的原文本",
        "targetText": "替换后的新文本",
        "moduleType": "模块类型(如果有)",
        "articleIds": ["文章ID列表(如果有)"]
    },
    "reasoning": "推理过程说明"
}
EOT;
    }

    protected function parseIntentResponse(string $response): array
    {
        try {
            if (preg_match('/\{[\s\S]*\}/m', $response, $matches)) {
                $jsonStr = $matches[0];
                $jsonStr = preg_replace('/[\x00-\x1F\x7F]/', '', $jsonStr);
                $result = json_decode($jsonStr, true);
                
                if (json_last_error() === JSON_ERROR_NONE) {
                    // 文章替换特定的结果处理
                    if (in_array($result['intent'], ['replace_all', 'select_specific', 'preview'])) {
                        $result['parameters'] = array_merge([
                            'sourceText' => '',
                            'targetText' => '',
                            'moduleType' => 'all',
                            'articleIds' => [],
                        ], $result['parameters'] ?? []);
                        
                        // 当有明确的替换文本对时，提高置信度
                        if (!empty($result['parameters']['sourceText']) && 
                            !empty($result['parameters']['targetText'])) {
                            $result['confidence'] = max($result['confidence'] ?? 0, 0.9);
                        }
                    }
                    
                    return $result;
                }
            }
            
            return $this->getDefaultIntentResult('响应格式不正确');
            
        } catch (\Exception $e) {
            return $this->getDefaultIntentResult('解析失败: ' . $e->getMessage());
        }
    }

    protected function postProcessParameters(?array $params, string $toolType): ?array
    {
        if ($params && $toolType === 'article_content_replacer') {
            // 确保 articleIds 是数组
            if (isset($params['articleIds']) && !is_array($params['articleIds'])) {
                if (is_string($params['articleIds']) || is_numeric($params['articleIds'])) {
                    $params['articleIds'] = [$params['articleIds']];
                } else {
                    $params['articleIds'] = [];
                }
            }

            // 标准化模块类型
            if (isset($params['moduleType'])) {
                $moduleMap = [
                    '新闻' => 'news',
                    '产品' => 'products',
                    '页面' => 'pages',
                    '博客' => 'blogs'
                ];
                $params['moduleType'] = $moduleMap[$params['moduleType']] ?? 'all';
            }
        }
        
        return $params;
    }
}
