<?php

declare(strict_types=1);

namespace Modules\Ai\Services\AI\IntentRecognition;

use Modules\Ai\Services\LLM\LLMService;

/**
 * 文本替换工具意图识别
 */
class TextIntentRecognition extends BaseIntentRecognition
{
    /**
     * 获取工具名称
     * 
     * @return string 工具名称
     */
    protected function getToolName(): string
    {
        return '文本替换';
    }
    
    /**
     * 获取文本替换工具的特定提示词
     *
     * @param string $toolType 工具类型
     * @param array $parameterNames 参数名称
     * @return string 工具特定提示词
     */
    protected function getToolSpecificPrompt(string $toolType, array $parameterNames): string
    {
        return "特别注意提取「sourceText」(要替换的文本)和「targetText」(替换后的文本)，通常会以「将X替换为Y」「把X改成Y」等格式出现。需要精确识别边界，避免包含多余的标点符号或空格。";
    }
    
    /**
     * 构建意图识别的提示词 - 文本替换专用版本
     *
     * @param string $message 用户输入的消息
     * @param array $intentTypes 意图类型映射
     * @param array $context 上下文信息
     * @return string 返回构建好的提示词
     */
    protected function buildIntentPrompt(string $message, array $intentTypes, array $context): string
    {
        // 构建意图类型描述
        $intentDescriptions = [];
        foreach ($intentTypes as $intent => $keywords) {
            $keywordsStr = implode('、', $keywords);
            $intentDescriptions[] = "- {$intent}: 包含关键词（{$keywordsStr}）";
        }
        $intentTypesStr = implode("\n", $intentDescriptions);

        return <<<EOT
请分析以下用户输入的意图。你需要：
1. 准确理解用户想要执行的操作
2. 如果提到具体的替换文本，需要提取出来
3. 给出较高的置信度(0.7-1.0)当意图明确时
4. 特别注意文件相关的操作意图

可能的意图类型包括：
{$intentTypesStr}
- unknown: 未知意图（当无法明确判断时）

意图判断规则：
1. 如果用户提到任何与文件上传、导入、选择相关的词，应该识别为 upload_file
2. 如果用户提到替换、修改等操作，并明确指出源文本和目标文本，应该识别为 specify_replacement
3. 如果用户提到预览、查看等词，应该识别为 generate_preview
4. 如果用户提到确认、执行等词，应该识别为 execute_replacement
5. 如果用户提到退出、返回等词，应该识别为 exit_tool
6. 如果不能明确判断意图，返回 unknown，置信度设为 0

用户输入: {$message}

请返回JSON格式：
{
    "intent": "意图类型",
    "confidence": 置信度(0-1),
    "parameters": {
        "sourceText": "要替换的文本(如果有)",
        "targetText": "替换后的文本(如果有)"
    },
    "reasoning": "推理过程说明"
}
EOT;
    }

    /**
     * 解析意图识别响应
     *
     * @param string $response LLM返回的响应内容
     * @return array 返回结构化的意图识别结果
     */
    protected function parseIntentResponse(string $response): array
    {
        try {
            // 提取JSON字符串
            if (preg_match('/\{[\s\S]*\}/m', $response, $matches)) {
                $jsonStr = $matches[0];
                // 清理可能的特殊字符
                $jsonStr = preg_replace('/[\x00-\x1F\x7F]/', '', $jsonStr);
                $result = json_decode($jsonStr, true);
                
                if (json_last_error() === JSON_ERROR_NONE) {
                    return [
                        'intent' => $result['intent'] ?? 'unknown',
                        'confidence' => $result['confidence'] ?? 0,
                        'parameters' => $result['parameters'] ?? [],
                        'reasoning' => $result['reasoning'] ?? ''
                    ];
                }
            }
            
            // 如果提取失败或JSON解析失败，返回unknown
            return [
                'intent' => 'unknown',
                'confidence' => 0,
                'parameters' => [],
                'reasoning' => '响应格式不正确'
            ];
            
        } catch (\Exception $e) {
            return [
                'intent' => 'unknown',
                'confidence' => 0,
                'parameters' => [],
                'reasoning' => '解析失败: ' . $e->getMessage()
            ];
        }
    }

}
