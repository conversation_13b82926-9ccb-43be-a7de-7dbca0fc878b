<?php
/*
 * @Author: jay <EMAIL>
 * @Date: 2025-04-10 19:01:14
 * @LastEditors: jay <EMAIL>
 * @LastEditTime: 2025-04-11 10:08:13
 * @FilePath: /bwms/Modules/Ai/Services/AI/IntentRecognition/IntentRecognitionContract.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

declare(strict_types=1);

namespace Modules\Ai\Services\AI\IntentRecognition;

interface IntentRecognitionContract
{
    /**
     * 识别用户输入的意图
     *
     * @param string $message 用户输入的消息
     * @param array $intentTypes 意图类型映射，格式: ['intent_name' => ['keyword1', 'keyword2']]
     * @param array $context 上下文信息
     * @return array 返回识别结果，包含intent, confidence, parameters等信息
     */
    public function recognizeIntent(string $message, array $intentTypes, array $context = []): array;
  
}