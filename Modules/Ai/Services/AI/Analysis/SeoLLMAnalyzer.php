<?php

declare(strict_types=1);

namespace Modules\Ai\Services\AI\Analysis;

use Modules\Ai\Services\LLM\LLMService;
use Illuminate\Support\Facades\Log;

class SeoLLMAnalyzer
{
    private LLMService $llmService;

    public function __construct(LLMService $llmService)
    {
        $this->llmService = $llmService;
    }

    public function analyze(array $data): array
    {
        try {
            $prompt = $this->preparePrompt($data);
            $response = $this->llmService->chat([
                [
                    'role' => 'system',
                    'content' => 'You are a professional SEO expert. Analyze the content and provide detailed SEO suggestions.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ]);
            
            return $this->parseResponse($response);
        } catch (\Exception $e) {
            Log::error('LLM analysis failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            
            return $this->getEmptyAnalysisResult();
        }
    }


   protected function preparePrompt(array $data): string
   {
     $lang = $data['lang'] ?? 'zh_CN';
 
     $locale = $data['locale'] ?? 'zh_CN';
 
     return "您是一个专业的SEO分析工具。请分析以下内容并提供SEO评分和优化建议。
 请严格按照指定的JSON格式返回结果。
 
 内容标题: {$data['title']}
 目标关键词: " . implode(', ', $data['keywords'] ?? []) . "
 描述: {$data['description']}
 内容正文:
 {$data['content']}
 
 请提供以下分析:
 1. 总体SEO评分(0-100)
 2. 内容质量评分(0-100)
 3. 关键词优化评分(0-100)
 4. 技术SEO评分(0-100)
 5. 具体分析和改进建议
 6. 具体的SEO建议和标题建议
 
 请严格按照JSON格式生成内容，确保符合以下要求：
 1. 使用双引号包裹所有键和字符串值
 2. 不使用单引号、注释或JavaScript风格的尾部逗号
 3. 包含明确的层级结构
 4. 确保所有键和值都正确格式化
 
 返回示例：
 {
   'overall_score': 数字,
   'content_score': 数字,
   'keyword_score': 数字,
   'technical_score': 数字,
   'readability': {
     'score': 数字,
     'level': '容易/中等/困难',
     'description': '文本'
   },
   'content_depth': {
     'score': 数字,
     'word_count': 数字,
     'assessment': '文本'
   },
   'originality': {
     'score': 数字,
     'assessment': '文本'
   },
   'keyword_analysis': {
     'primary_keyword': {
       'presence': '好/中/差',
       'density': 数字,
       'assessment': '文本'
     },
     'secondary_keywords': [
       {
         'keyword': '文本',
         'presence': '好/中/差',
         'suggestions': '文本'
       }
     ]
   },
   'suggestions': {
     'title': ['建议1', '建议2'],
     'description': ['建议1', '建议2'],
     'content': ['建议1', '建议2'],
     'keywords': ['建议1', '建议2'],
     'technical': ['建议1', '建议2']
   },
   'seo_suggestions': {
     'title': ['标题1', '标题1'],
     'description': ['描述1', '描述2'],
     'seo_title': ['SEO标题1', 'SEO标题1'],
     'seo_description': ['SEO描述1', 'SEO描述2'],
     'keywords':  ['SEO关键词1', 'SEO关键词2'],
   }
 }
 
 不要包含注释和多余说明，直接返回有效JSON数组,使用紧凑JSON格式（无换行空格），禁用Unicode转义符, JSON中seo_suggestions的语言需要是{$lang},其他的文本语言需要使用{$locale},seo_keywords需要数组，seo_title和seo_description需要包含优化后的标题和描述。
 。修复任何可能的JSON错误。";
   }

  /**
     * 解析LLM响应
     *
     * @param string $response
     * @return array
     */
    protected function parseResponse(string $response): array
    {
        // 预处理JSON内容
        $content = $this->preprocessJsonContent($response);

        try {
            // 增加JSON解析深度和错误检测
            $data = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

            return [
                'overall_score' => $data['overall_score'] ?? 0,
                'content_score' => $data['content_score'] ?? 0,
                'keyword_score' => $data['keyword_score'] ?? 0,
                'technical_score' => $data['technical_score'] ?? 0,
                'readability' => $data['readability'] ?? [],
                'content_depth' => $data['content_depth'] ?? [],
                'originality' => $data['originality'] ?? [],
                'keyword_analysis' => $data['keyword_analysis'] ?? [],
                'suggestions' => [
                    'title' => $data['suggestions']['title'] ?? [],
                    'description' => $data['suggestions']['description'] ?? [],
                    'content' => $data['suggestions']['content'] ?? [],
                    'keywords' => $data['suggestions']['keywords'] ?? [],
                    'technical' => $data['suggestions']['technical'] ?? []
                ],
                'seo_suggestions' => $data['seo_suggestions'] ?? [],
            ];
        } catch (\JsonException $e) {
            Log::error('Failed to parse LLM response', [
                'error' => $e->getMessage(),
                'content' => $content,
                'json_error' => json_last_error_msg()
            ]);
            
            // 返回空结果以避免中断流程
            return [
                'overall_score' => 0,
                'content_score' => 0,
                'keyword_score' => 0,
                'technical_score' => 0,
                'readability' => [],
                'content_depth' => [],
                'originality' => [],
                'keyword_analysis' => [],
                'suggestions' => [],
                'seo_suggestions' => [],
            ];
        }
    }

       /**
     * 预处理JSON内容
     */
    protected function preprocessJsonContent(string $content): string
    {
        // 移除可能的BOM头
        $content = preg_replace('/\x{EF}\x{BB}\x{BF}/', '', $content);
        $content = preg_replace('/```json/', '', $content);
        $content = preg_replace('/```/', '', $content);

        // 处理转义问题
        $content = stripslashes($content);

        // 替换中文标点为英文标点

        // 修复可能的JSON截断
        if (substr($content, -1) !== '}' && strpos($content, '}') !== false) {
            $content = substr($content, 0, strrpos($content, '}') + 1);
        }

        return trim($content);
    }

    private function getEmptyAnalysisResult(): array
    {
        return [
            'overall_score' => 0,
            'content_score' => 0,
            'keyword_score' => 0,
            'technical_score' => 0,
            'readability' => [],
            'content_depth' => [],
            'originality' => [],
            'keyword_analysis' => [],
            'suggestions' => [],
            'seo_suggestions' => [],
        ];
    }
}