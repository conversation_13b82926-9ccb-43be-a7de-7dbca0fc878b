<?php

declare(strict_types=1);

namespace Modules\Ai\Services\AI\ParameterExtraction;

use Modules\Ai\Services\LLM\LLMService;

/**
 * 文本替换工具参数提取器
 */
class TextParameterExtractor extends BaseParameterExtractor
{
    /**
     * 获取工具名称
     */
    protected function getToolName(): string
    {
        return '文本替换';
    }
    
    /**
     * 初始化工具参数配置
     */
    protected function initToolParameters(): void
    {
        $this->toolParameters = [
            'text_replacer' => [
                'sourceText' => ['required' => true, 'description' => '要替换的原文本'],
                'targetText' => ['required' => true, 'description' => '替换后的新文本'],
                'caseSensitive' => ['required' => false, 'description' => '是否区分大小写', 'default' => false]
            ]
        ];
    }
    
    /**
     * 解析LLM响应
     *
     * @param string $response LLM响应
     * @return array 解析后的参数数组
     */
    protected function parseResponse(string $response): array
    {
        // 清理响应内容
        $response = trim($response, '"'); // 移除首尾的引号
        $response = stripslashes($response); // 处理转义字符
        
        // 提取JSON
        if (preg_match('/\{[\s\S]*\}/m', $response, $matches)) {
            $jsonStr = $matches[0];
            $jsonStr = preg_replace('/[\x00-\x1F\x7F]/', '', $jsonStr);
            $result = json_decode($jsonStr, true);
            
            // 文本替换特定的处理逻辑
            if (is_array($result)) {
                // 确保sourceText和targetText是字符串类型
                if (isset($result['sourceText']) && !is_string($result['sourceText'])) {
                    $result['sourceText'] = (string)$result['sourceText'];
                }
                
                if (isset($result['targetText']) && !is_string($result['targetText'])) {
                    $result['targetText'] = (string)$result['targetText'];
                }
                
                // 确保caseSensitive是布尔类型
                if (isset($result['caseSensitive'])) {
                    if (is_string($result['caseSensitive'])) {
                        $value = strtolower($result['caseSensitive']);
                        $result['caseSensitive'] = in_array($value, ['true', '1', 'yes', 'y']);
                    } else {
                        $result['caseSensitive'] = (bool)$result['caseSensitive'];
                    }
                }
            }
            
            return $result ?: [];
        }
        
        return [];
    }
    
    /**
     * 获取特定工具的特殊说明
     */
    protected function getSpecialInstructions(string $toolType): string
    {
        if ($toolType === 'text_replacer') {
            return "请特别注意：
1. sourceText 通常出现在「将」「把」等词后面
2. targetText 通常出现在「替换为」「改成」等词后面
3. 需要精确提取文本边界，避免包含多余的标点符号或空格";
        }
        
        return parent::getSpecialInstructions($toolType);
    }
    
    /**
     * 获取示例输出
     */
    protected function getExampleOutput(string $toolType): string
    {
        if ($toolType === 'text_replacer') {
            return '{
    "sourceText": "hello",
    "targetText": "hi",
    "caseSensitive": false
}';
        }
        
        return parent::getExampleOutput($toolType);
    }
}