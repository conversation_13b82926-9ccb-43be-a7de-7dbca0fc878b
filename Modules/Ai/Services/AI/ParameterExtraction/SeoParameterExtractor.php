<?php

declare(strict_types=1);

namespace Modules\Ai\Services\AI\ParameterExtraction;

use Modules\Ai\Services\LLM\LLMService;

/**
 * SEO分析工具参数提取器
 */
class SeoParameterExtractor extends BaseParameterExtractor
{
    /**
     * 获取工具名称
     */
    protected function getToolName(): string
    {
        return 'SEO分析';
    }
    
    /**
     * 初始化工具参数配置
     */
    protected function initToolParameters(): void
    {
        $this->toolParameters = [
            'seo_analysis' => [
                'title' => ['required' => true, 'description' => '页面标题'],
                'content' => ['required' => true, 'description' => '页面内容'],
                'keywords' => ['required' => false, 'description' => '关键词列表（逗号分隔）'],
                'description' => ['required' => false, 'description' => '页面描述'],
                'url' => ['required' => false, 'description' => '页面URL'],
                'locale' => ['required' => false, 'description' => '语言设置', 'default' => 'zh_CN'],
                'article_id' => ['required' => false, 'description' => '文章ID']
            ]
        ];
    }
    
    /**
     * 解析LLM响应
     *
     * @param string $response LLM响应
     * @return array 解析后的参数数组
     */
    protected function parseResponse(string $response): array
    {
        // 清理响应内容，移除三引号
        $response = trim($response, '"');  // 移除普通引号
        $response = trim($response, '"""'); // 移除三引号
        $response = stripslashes($response); // 处理转义字符
        
        // 提取JSON
        if (preg_match('/\{[\s\S]*\}/m', $response, $matches)) {
            $jsonStr = $matches[0];
            $jsonStr = preg_replace('/[\x00-\x1F\x7F]/', '', $jsonStr);
            $result = json_decode($jsonStr, true);
            
            // SEO分析特定的处理逻辑
            if (is_array($result)) {
                // 处理标题字段
                if (isset($result['title'])) {
                    $result['title'] = $result['title'] === null ? '' : (string)$result['title'];
                }
                
                // 处理内容字段
                if (isset($result['content'])) {
                    $result['content'] = $result['content'] === null ? '' : (string)$result['content'];
                }
                
                // 处理关键词字段
                if (isset($result['keywords'])) {
                    if ($result['keywords'] === null) {
                        $result['keywords'] = [];
                    } elseif (is_string($result['keywords'])) {
                        if (strpos($result['keywords'], ',') !== false) {
                            $result['keywords'] = array_map('trim', explode(',', $result['keywords']));
                        } else {
                            $result['keywords'] = [$result['keywords']];
                        }
                    } elseif (!is_array($result['keywords'])) {
                        $result['keywords'] = [$result['keywords']];
                    }
                }
                
                // 处理描述字段
                if (isset($result['description'])) {
                    $result['description'] = $result['description'] === null ? '' : (string)$result['description'];
                }
                
                // 处理URL字段
                if (isset($result['url'])) {
                    $result['url'] = $result['url'] === null ? '' : (string)$result['url'];
                    // 验证并清理URL
                    if ($result['url'] !== '') {
                        if (!filter_var($result['url'], FILTER_VALIDATE_URL)) {
                            // 尝试从文本中提取URL
                            if (preg_match('/https?:\/\/[^\s]+/', $result['url'], $matches)) {
                                $result['url'] = $matches[0];
                            }
                        }
                    }
                }
                
                // 处理文章ID字段
                if (isset($result['article_id'])) {
                    if ($result['article_id'] === null) {
                        $result['article_id'] = '';
                    } else {
                        // 确保article_id是字符串类型，并且只包含数字
                        $articleId = (string)$result['article_id'];
                        if (preg_match('/(\d+)/', $articleId, $matches)) {
                            $result['article_id'] = $matches[1];
                        } else {
                            $result['article_id'] = '';
                        }
                    }
                }
                
                // 处理语言设置字段
                if (isset($result['locale'])) {
                    $result['locale'] = $result['locale'] === null ? 'zh_CN' : (string)$result['locale'];
                }
                
                return $result;
            }
        }
        
        return [];
    }
    
    /**
     * 获取特定工具的特殊说明
     */
    protected function getSpecialInstructions(string $toolType): string
    {
        if ($toolType === 'seo_analysis') {
            return "请特别注意：
1. title 通常出现在「标题是」「标题：」等形式之后
2. content 通常出现在「内容是」「内容：」等形式之后
3. keywords 是逗号分隔的列表，需要完整提取
4. 如果用户指定了语言，需在locale中标明
5. article_id 通常出现在「文章id」「编号」等形式之后，需要提取数字部分
6. url 需要完整提取，包括http://或https://前缀";
        }
        
        return parent::getSpecialInstructions($toolType);
    }
    
    /**
     * 获取示例输出
     */
    protected function getExampleOutput(string $toolType): string
    {
        if ($toolType === 'seo_analysis') {
            return '{
    "title": "如何提高网站SEO排名",
    "content": "提高网站SEO排名需要考虑多个因素...",
    "keywords": "SEO,网站优化,搜索引擎",
    "description": "本文介绍了提高网站SEO排名的有效方法",
    "url": "https://example.com/seo-tips",
    "article_id": "12345",
    "locale": "zh_CN"
}';
        }
        
        return parent::getExampleOutput($toolType);
    }
    
    /**
     * 参数后处理
     */
    protected function postProcessParameters(array $params, string $toolType): ?array
    {
        $params = parent::postProcessParameters($params, $toolType);
        
        if ($params && $toolType === 'seo_analysis') {
            // 这部分可以被移除，因为我们已经在parseResponse方法中处理了关键词格式
            // 但为了保持向后兼容，可以保留
        }
        
        return $params;
    }
}
