<?php

declare(strict_types=1);

namespace Modules\Ai\Services\AI\ParameterExtraction;

use Illuminate\Support\Facades\Log;
use Modules\Ai\Services\LLM\LLMService;

/**
 * 基础参数提取器
 * 
 * 提供通用功能，子类可以通过重写特定方法来定制功能
 */
abstract class BaseParameterExtractor implements ParameterExtractorContract
{
    /**
     * 工具参数配置
     *
     * @var array
     */
    protected array $toolParameters = [];

    /**
     * LLM服务
     *
     * @var LLMService
     */
    protected LLMService $llm;
    
    public function __construct(LLMService $llm)
    {
        $this->llm = $llm;
        $this->initToolParameters();
    }
    
    /**
     * 初始化工具参数配置
     * 子类应重写此方法来定义特定工具的参数
     */
    abstract protected function initToolParameters(): void;
    
    /**
     * 获取工具名称
     */
    abstract protected function getToolName(): string;

    /**
     * 从用户输入中提取参数
     *
     * @param string $userInput 用户输入的消息
     * @param string $toolType 工具类型
     * @param array $parameterNames 要提取的参数名称
     * @param array $context 上下文信息
     * @return array|null 提取的参数，如果没有提取到则返回null
     */
    public function extractParameters(
        string $userInput,
        string $toolType,
        array $parameterNames,
        array $context = []
    ): ?array {
        // 获取工具配置
        $toolConfig = $this->getToolConfig($toolType, $parameterNames);
        if (empty($toolConfig)) {
            return null;
        }
        // 获取对话历史
        // $historyText = $this->formatHistory($context);
        $historyText = '';
        // 构建提示词
        $prompt = $this->buildPrompt($userInput, $toolType, $toolConfig, $historyText);
        try {
            // 调用LLM服务
            $response = $this->llm->chat([
                [
                    'role' => 'system',
                    'content' => $this->getSystemPrompt($toolType)
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ]);

            // 解析响应
            $params = $this->parseResponse($response);
            // 验证参数
            $result = $this->validateParameters($params, $toolConfig);
            // 后处理参数
            return $this->postProcessParameters($result, $toolType);
        } catch (\Exception $e) {
            Log::error($this->getToolName() . '参数提取失败：' . $e->getMessage(), [
                'user_input' => $userInput,
                'tool_type' => $toolType
            ]);
            return null;
        }
    }
    
    /**
     * 获取特定工具的配置
     *
     * @param string $toolType 工具类型
     * @param array $parameterNames 参数名称列表
     * @return array 工具配置
     */
    protected function getToolConfig(string $toolType, array $parameterNames): array
    {
        $baseConfig = $this->toolParameters[$toolType] ?? [];
        
        // 如果基础配置为空但提供了参数名称，创建简单配置
        if (empty($baseConfig) && !empty($parameterNames)) {
            $config = [];
            foreach ($parameterNames as $param) {
                $config[$param] = [
                    'required' => true,
                    'description' => $param
                ];
            }
            return $config;
        }
        
        return $baseConfig;
    }
    
    /**
     * 获取系统提示词
     *
     * @param string $toolType 工具类型
     * @return string 系统提示词
     */
    protected function getSystemPrompt(string $toolType): string
    {
        return "你是一个参数提取助手，专门负责从用户输入中提取{$toolType}所需的参数。";
    }
    
    /**
     * 格式化对话历史
     *
     * @param array $context 上下文信息
     * @return string 格式化后的对话历史
     */
    protected function formatHistory(array $context): string
    {
        $history = $context['history'] ?? [];
        $relevantHistory = array_slice($history, -5);
        return collect($relevantHistory)
            ->map(fn($msg) => "{$msg['role']}: {$msg['content']}")
            ->implode("\n");
    }
    
    /**
     * 构建参数提取的提示词
     *
     * @param string $userInput 用户输入
     * @param string $toolType 工具类型
     * @param array $toolConfig 工具参数配置
     * @param string $historyText 对话历史文本
     * @return string 构建好的提示词
     */
    protected function buildPrompt(string $userInput, string $toolType, array $toolConfig, string $historyText): string
    {
        $paramDescriptions = $this->formatParamDescriptions($toolConfig);
        $exampleOutput = $this->getExampleOutput($toolType);
        $specialInstructions = $this->getSpecialInstructions($toolType);
        
        return <<<EOT
从用户输入"{$userInput}"中提取参数，直接返回JSON格式数据。

参数说明：
{$paramDescriptions}

{$specialInstructions}

格式要求：
{$exampleOutput}

规则：
1. 直接返回JSON，不要包含```json等标记
2. 不要添加任何额外说明文字
3. 未识别的参数使用null

对话历史（仅供参考）：
{$historyText}
EOT;
    }
    
    /**
     * 获取特定工具的特殊说明
     * 
     * @param string $toolType 工具类型
     * @return string 特殊说明
     */
    protected function getSpecialInstructions(string $toolType): string
    {
        return '';
    }
    
    /**
     * 获取示例输出
     * 
     * @param string $toolType 工具类型
     * @return string 示例输出
     */
    protected function getExampleOutput(string $toolType): string
    {
        // 默认实现，子类可重写
        return '{"param1":"value1","param2":"value2"}';
    }
    
    /**
     * 格式化参数描述
     *
     * @param array $toolConfig 工具参数配置
     * @return string 格式化后的参数描述文本
     */
    protected function formatParamDescriptions(array $toolConfig): string
    {
        $descriptions = [];
        foreach ($toolConfig as $param => $config) {
            $required = $config['required'] ? '(必需)' : '(可选)';
            $default = isset($config['default']) ? "，默认值：{$config['default']}" : '';
            $descriptions[] = "- {$param}: {$config['description']}{$required}{$default}";
        }
        return implode("\n", $descriptions);
    }
    
    /**
     * 解析LLM响应
     *
     * @param string $response LLM响应
     * @return array 解析后的参数数组
     */
    abstract protected function parseResponse(string $response): array;
    
    /**
     * 验证和处理提取的参数
     *
     * @param array $params 提取的原始参数
     * @param array $toolConfig 工具参数配置
     * @return array 验证后的参数数组
     */
    protected function validateParameters(array $params, array $toolConfig): array
    {
        $validated = [];
        foreach ($toolConfig as $param => $config) {
            if (!isset($params[$param]) || (is_string($params[$param]) && trim($params[$param]) === '')) {
                $validated[$param] = $config['required'] ? null : ($config['default'] ?? null);
                continue;
            }
            $validated[$param] = $params[$param];
        }
        return $validated;
    }
    
    /**
     * 参数后处理
     * 子类可重写此方法以提供特定的处理逻辑
     *
     * @param array $params 验证后的参数
     * @param string $toolType 工具类型
     * @return array|null 处理后的参数
     */
    protected function postProcessParameters(array $params, string $toolType): ?array
    {
        // 检查是否有任何有效参数
        $hasValidParams = false;
        foreach ($params as $value) {
            if ($value !== null) {
                $hasValidParams = true;
                break;
            }
        }
        
        return $hasValidParams ? $params : null;
    }
}