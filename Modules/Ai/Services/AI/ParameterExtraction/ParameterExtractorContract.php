<?php

declare(strict_types=1);

namespace Modules\Ai\Services\AI\ParameterExtraction;

interface ParameterExtractorContract
{
    /**
     * 从用户输入中提取参数
     *
     * @param string $userInput 用户输入的消息
     * @param string $toolType 工具类型
     * @param array $parameterNames 要提取的参数名称
     * @param array $context 上下文信息
     * @return array|null 提取的参数，如果没有提取到则返回null
     */
    public function extractParameters(
        string $userInput,
        string $toolType,
        array $parameterNames,
        array $context = []
    ): ?array;
}