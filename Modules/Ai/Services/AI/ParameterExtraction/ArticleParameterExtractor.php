<?php

declare(strict_types=1);

namespace Modules\Ai\Services\AI\ParameterExtraction;

use Modules\Ai\Services\LLM\LLMService;

/**
 * 文章内容替换工具参数提取器
 */
class ArticleParameterExtractor extends BaseParameterExtractor
{
    /**
     * 获取工具名称
     */
    protected function getToolName(): string
    {
        return '文章内容替换';
    }

    /**
     * 初始化工具参数配置
     */
    protected function initToolParameters(): void
    {
        $this->toolParameters = [
            'article_content_replacer' => [
                'sourceText' => ['required' => true, 'description' => '要替换的原文本'],
                'targetText' => ['required' => true, 'description' => '替换后的新文本'],
                'moduleType' => ['required' => false, 'description' => '模块类型', 'default' => 'all'],
                'articleIds' => ['required' => false, 'description' => '要处理的文章ID列表']
            ]
        ];
    }

    /**
     * 解析LLM响应
     *
     * @param string $response LLM响应
     * @return array 解析后的参数数组
     */
    protected function parseResponse(string $response): array
    {
        // 清理响应内容
        $response = trim($response, '"');  // 移除普通引号
        $response = trim($response, '"""'); // 移除三引号
        $response = stripslashes($response); // 处理转义字符
        
        // 提取JSON
        if (preg_match('/\{[\s\S]*\}/m', $response, $matches)) {
            $jsonStr = $matches[0];
            $jsonStr = preg_replace('/[\x00-\x1F\x7F]/', '', $jsonStr);
            $result = json_decode($jsonStr, true);
            
            // 文章替换特定的处理逻辑
            if (is_array($result)) {
                // 处理原文本字段
                if (isset($result['sourceText'])) {
                    $result['sourceText'] = $result['sourceText'] === null ? '' : (string)$result['sourceText'];
                }
                
                // 处理目标文本字段
                if (isset($result['targetText'])) {
                    $result['targetText'] = $result['targetText'] === null ? '' : (string)$result['targetText'];
                }
                
                // 处理模块类型字段
                if (isset($result['moduleType'])) {
                    $result['moduleType'] = $result['moduleType'] === null ? 'all' : (string)$result['moduleType'];
                    // 标准化模块类型
                    $moduleMap = [
                        '新闻' => 'news',
                        '产品' => 'products',
                        '页面' => 'pages',
                        '博客' => 'blogs',
                        'news' => 'news',
                        'products' => 'products',
                        'pages' => 'pages',
                        'blogs' => 'blogs'
                    ];
                    $result['moduleType'] = $moduleMap[strtolower($result['moduleType'])] ?? 'all';
                }
                
                // 处理文章ID列表字段
                if (isset($result['articleIds'])) {
                    if ($result['articleIds'] === null) {
                        $result['articleIds'] = [];
                    } elseif (is_string($result['articleIds'])) {
                        // 如果是逗号分隔的字符串，转换为数组
                        if (strpos($result['articleIds'], ',') !== false) {
                            $result['articleIds'] = array_map('trim', explode(',', $result['articleIds']));
                        } else {
                            $result['articleIds'] = [$result['articleIds']];
                        }
                        // 确保所有ID都是有效的数字
                        $result['articleIds'] = array_filter($result['articleIds'], function($id) {
                            return preg_match('/^\d+$/', trim($id));
                        });
                    } elseif (!is_array($result['articleIds'])) {
                        $result['articleIds'] = [(string)$result['articleIds']];
                    }
                    // 确保所有ID都是字符串类型
                    $result['articleIds'] = array_map('strval', $result['articleIds']);
                }
                
                return $result;
            }
        }
        
        return [];
    }

    /**
     * 获取特定工具的特殊说明
     */
    protected function getSpecialInstructions(string $toolType): string
    {
        if ($toolType === 'article_content_replacer') {
            return "请特别注意：
1. sourceText 通常出现在「将」「把」等词后面，表示要被替换的原文本
2. targetText 通常出现在「替换为」「改成」等词后面，表示替换后的新文本
3. moduleType 可能以中文形式出现，如「新闻」「产品」「页面」「博客」等
4. articleIds 通常出现在「文章id」「编号」等形式之后，可能是单个ID或逗号分隔的多个ID
5. 需要准确识别替换文本的边界，避免包含多余的标点符号或空格";
        }
        
        return parent::getSpecialInstructions($toolType);
    }

    /**
     * 获取示例输出
     */
    protected function getExampleOutput(string $toolType): string
    {
        if ($toolType === 'article_content_replacer') {
            return '{
    "sourceText": "原始文本",
    "targetText": "替换后的文本",
    "moduleType": "news",
    "articleIds": ["123", "456"]
}';
        }
        
        return parent::getExampleOutput($toolType);
    }

    /**
     * 参数后处理
     */
    protected function postProcessParameters(array $params, string $toolType): ?array
    {
        $params = parent::postProcessParameters($params, $toolType);
        
        if ($params && $toolType === 'article_content_replacer') {
            // 确保必要字段存在
            $params['sourceText'] = $params['sourceText'] ?? '';
            $params['targetText'] = $params['targetText'] ?? '';
            $params['moduleType'] = $params['moduleType'] ?? 'all';
            $params['articleIds'] = $params['articleIds'] ?? [];

            // 清理文本字段的首尾空格
            $params['sourceText'] = trim($params['sourceText']);
            $params['targetText'] = trim($params['targetText']);

            // 确保articleIds是数组
            if (!is_array($params['articleIds'])) {
                $params['articleIds'] = [$params['articleIds']];
            }
        }
        
        return $params;
    }
}
