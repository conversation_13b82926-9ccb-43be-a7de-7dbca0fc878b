<?php

namespace Modules\Ai\Services\AI;

use Modules\Ai\Services\Tools\ToolRegistry;
use Modules\Ai\Services\Chat\ChatSessionService;
use Modules\Ai\Config\ChatActions;
use Illuminate\Support\Facades\Log;
use Modules\Ai\Services\AI\IntentRecognition\UnknownToolIntentRecognizer;
use Modules\Ai\Services\LLM\LLMResponseGenerator;
use Modules\Ai\Services\Tools\ToolInterface;

class AIAgent
{
    private ChatSessionService $sessionService;
    private UnknownToolIntentRecognizer $unknownToolRecognizer;
    private LLMResponseGenerator $responseGenerator;

    public function __construct(
        ChatSessionService $sessionService,
        UnknownToolIntentRecognizer $unknownToolIntentRecognizer,
        LLMResponseGenerator $responseGenerator
    ) {
        $this->sessionService = $sessionService;
        $this->unknownToolRecognizer = $unknownToolIntentRecognizer;
        $this->responseGenerator = $responseGenerator;
    }

    /**
     * 处理用户消息
     *
     * @param string $sessionId 会话ID
     * @param string $message 用户消息
     * @param array $params 额外参数
     * @return array 响应结果
     */
    public function chat(string $sessionId, string $message, array $params = []): array
    {
        try {
            // 获取或创建会话
            $session = $this->sessionService->getOrCreateSession($sessionId);
            // 处理初始化聊天
            if ($this->isInitChatRequest($message, $params)) {
                return $this->handleInitChat($session);
            }

            // 添加用户消息到会话
            $session = $this->sessionService->addMessage($session, 'user', $message);


            // 检查是否有激活的工具
            $activeTool = $this->sessionService->getActiveTool($session);
            if ($activeTool) {
                // 检查是否是退出工具的请求
                if ($this->isExitToolRequest($message)) {
                    return $this->handleToolExit($session);
                }
                
                // 将消息委托给激活的工具处理（包括快捷操作）
                return $this->delegateMessageToTool($message, $session, $params);
            }

            // 检查是否是工具选择指令或快捷操作
            $toolSelection = $this->checkToolSelectionMessage($message);
            if ($toolSelection) {
                return $this->activateTool($toolSelection, $session, $params);
            }

            // 检查是否是快捷操作（通过action参数）
            if (isset($params['action'])) {
                $toolName = $this->mapActionToTool($params['action']);
                if ($toolName) {
                    return $this->activateTool($toolName, $session, $params);
                }
            }

            // 尝试分析用户意图，看是否需要激活某个工具
            $intent = $this->unknownToolRecognizer->recognizeIntent($message, [], ['session' => $session]);
            if ($intent['confidence'] > 0.7 && isset($intent['parameters']['suggested_tools'])) {
                $suggestedTools = $intent['parameters']['suggested_tools'];
                if (!empty($suggestedTools)) {
                    $toolName = $suggestedTools[0];
                    return $this->activateTool($toolName, $session, $params, $intent['parameters'] ?? []);
                }
            }

            // 使用常规对话处理
            $response = $this->generateLLMResponse($message, $session);
            
            // 保存AI回复
            if (isset($response['content'])) {
                $this->sessionService->addMessage($session, 'assistant', $response['content']);
            }

            // 添加默认工具建议
            $response['quickActions'] = $this->getDefaultQuickActions();
            $response['sessionId'] = $session['id'];

            return $response;
        } catch (\Exception $e) {
            Log::error("Chat error: " . $e->getMessage(), [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'content' => '處理消息時發生錯誤，請稍後再試',
                'sessionId' => $sessionId,
                'quickActions' => $this->getDefaultQuickActions()
            ];
        }
    }

    /**
     * 检查是否是初始化聊天请求
     */
    private function isInitChatRequest(string $message, array $params): bool
    {
        return empty($message) && isset($params['action']) && $params['action'] === ChatActions::INIT_CHAT;
    }



    /**
     * 将action映射到工具名称
     */
    private function mapActionToTool(string $action): ?string
    {
        return ChatActions::getToolName($action);
    }

    /**
     * 处理初始化聊天
     * 
     * 会清除所有之前的会话状态：
     * - 消息历史
     * - 激活的工具
     * - 工具参数
     * 
     * 保留基本信息：
     * - session ID
     * - user ID
     */
    private function handleInitChat(array $session): array
    {
        // 重置session，清除所有之前的状态（消息历史、激活工具、工具参数等）
        $session = $this->sessionService->resetSession($session['id']);
        
        $welcomeMessage = "👋 您好！我是您的智慧助手，我可以幫助您：\n\n" .
                         "📝 **文章替換** - 批量處理文章內容\n\n" .
                         "請選擇您需要的功能，或直接向我提問！";

        // 保存欢迎消息到会话
        $this->sessionService->addMessage($session, 'assistant', $welcomeMessage);

        return [
            'content' => $welcomeMessage,
            'quickActions' => $this->getDefaultQuickActions(),
            'sessionId' => $session['id']
        ];
    }

    /**
     * 检查消息是否为工具选择
     */
    private function checkToolSelectionMessage(string $message): ?string
    {
        $toolSelectionPatterns = [
            'text_replacer' => '/^替换功能$|^文本替换$|^使用替换功能$/i',
            'seo_analysis' => '/^SEO分析$|^SEO检查$|^使用SEO分析$/i',
            'article_content_replacer' => '/^文章替换$|^文章内容替换$|^文章替换功能$|^使用文章替换$/i'
        ];

        foreach ($toolSelectionPatterns as $toolName => $pattern) {
            if (preg_match($pattern, $message)) {
                return $toolName;
            }
        }

        return null;
    }

    /**
     * 激活指定的工具
     */
    private function activateTool(string $toolName, array $session, array $params = [], array $intentParams = []): array
    {
        $tool = ToolRegistry::getTool($toolName);
        if (!$tool) {
            return $this->createErrorResponse("抱歉，{$toolName}工具不可用");
        }

        // 合并工具初始数据
        $toolData = array_merge($params, $intentParams);
        
        // 激活工具
        $session = $this->sessionService->activateTool($session, $toolName, $toolData);

        // 构建简化的工具上下文
        $context = [
            'session' => $session
        ];

        // 让工具处理初始化
        return $tool->handleMessage('', $context);
    }

    /**
     * 将消息委托给工具处理
     */
    private function delegateMessageToTool(string $message, array $session, array $params): array
    {
        $toolName = $this->sessionService->getActiveTool($session);
        $tool = ToolRegistry::getTool($toolName);
        
        if (!$tool) {
            $session = $this->sessionService->clearActiveTool($session);
            return $this->createErrorResponse("工具不可用，已退出工具模式");
        }

        // 更新工具数据
        if (!empty($params)) {
            $session = $this->sessionService->updateToolData($session, $params);
        }

        // 构建简化的工具上下文
        $context = [
            'session' => $session
        ];

        try {
            $response = $tool->handleMessage($message, $context);
            
            // 处理工具数据，将toolData保存到session中
            if (isset($response['toolData']) && is_array($response['toolData'])) {
                $session = $this->sessionService->updateToolData($session, $response['toolData']);
            }
            
            // 处理工具响应
            if (isset($response['releaseControl']) && $response['releaseControl']) {
                $this->sessionService->clearActiveTool($session);
            }

            if (isset($response['resetSession']) && $response['resetSession']) {
                $this->sessionService->resetSession($session['id']);
            }

            // 保存AI回复
            if (isset($response['content'])) {
                $this->sessionService->addMessage($session, 'assistant', $response['content']);
            }

            return $response;
        } catch (\Exception $e) {
            Log::error('Tool execution error', [
                'tool' => $toolName,
                'error' => $e->getMessage()
            ]);
            
            $this->sessionService->clearActiveTool($session);
            return $this->createErrorResponse("工具執行出錯，已退出工具模式：" . $e->getMessage());
        }
    }

    /**
     * 检查是否是退出工具的请求
     */
    private function isExitToolRequest(string $message): bool
    {
        $exitPatterns = [
            '/^退出$/i',
            '/^取消$/i',
            '/^结束$/i',
            '/^返回$/i'
        ];

        foreach ($exitPatterns as $pattern) {
            if (preg_match($pattern, $message)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 处理工具退出
     */
    private function handleToolExit(array $session): array
    {
        $this->sessionService->clearActiveTool($session);
        $exitMessage = "已退出工具模式。您可以繼續使用其他功能或直接向我提問。";
        $this->sessionService->addMessage($session, 'assistant', $exitMessage);
        
        return [
            'content' => $exitMessage,
            'quickActions' => $this->getDefaultQuickActions(),
            'sessionId' => $session['id']
        ];
    }

    /**
     * 使用LLM生成回复
     */
    private function generateLLMResponse(string $message, array $session): array
    {
        $result = $this->responseGenerator->generate($message, $session);
        
        if (!$result['success']) {
            return $this->createErrorResponse($result['error']);
        }

        return ['content' => $result['content']];
    }

    /**
     * 创建错误响应
     */
    private function createErrorResponse(string $errorMessage): array
    {
        return [
            'content' => $errorMessage,
            'quickActions' => $this->getDefaultQuickActions()
        ];
    }

    /**
     * 获取默认快捷操作按钮
     */
    public function getDefaultQuickActions(): array
    {
        return [
            [
                'id' => 'article_content_replacer', 
                'text' => '📄 文章替換',
                'action' => ChatActions::SELECT_ARTICLE_REPLACER
            ]
        ];
    }
}
