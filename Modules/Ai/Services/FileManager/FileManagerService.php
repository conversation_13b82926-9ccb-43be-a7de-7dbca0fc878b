<?php

namespace Modules\Ai\Services\FileManager;

use Illuminate\Support\Facades\Storage;
use Symfony\Component\Finder\Finder;

class FileManagerService
{
    private array $supportedExtensions;
    private array $excludedDirectories;

    public function __construct(array $supportedExtensions = [], array $excludedDirectories = [])
    {
        $this->supportedExtensions = $supportedExtensions;
        $this->excludedDirectories = $excludedDirectories ?: ['.git', 'vendor', 'node_modules'];
    }

    /**
     * 读取文件内容
     */
    private function getFileContent(string $path, string $extension): string
    {
        $extension = strtolower($extension);
        // 二进制文件处理
        if (in_array($extension, ['pdf', 'doc', 'docx'])) {
            try {
                if ($extension === 'pdf') {
                    $parser = new \Smalot\PdfParser\Parser();
                    $pdf = $parser->parseFile(storage_path('app/' . $path));
                    return $pdf->getText();
                } else if (in_array($extension, ['doc', 'docx'])) {
                    // $phpWord = \PhpOffice\PhpWord\IOFactory::load(storage_path('app/' . $path));
                    // $content = '';
                    // foreach ($phpWord->getSections() as $section) {
                    //     foreach ($section->getElements() as $element) {
                    //         if (method_exists($element, 'getText')) {
                    //             $content .= $element->getText() . "\n";
                    //         }
                    //     }
                    // }
                    // return $content;
                }
            } catch (\Exception $e) {
                return '[文件内容无法读取]';
            }
        }

        // 文本文件直接读取
        return Storage::disk('local')->get($path);
    }

    /**
     * 读取单个文件
     */
    public function readFile(string $path): ?array
    {
        $storagePath = $path;
        if (!Storage::disk('local')->exists($storagePath)) {
            return null;
        }

        $extension = pathinfo($storagePath, PATHINFO_EXTENSION);

        return [
            'path' => $storagePath,
            'name' => basename($storagePath),
            'content' => $this->getFileContent($storagePath, $extension),
            'extension' => $extension,
            'size' => Storage::disk('local')->size($storagePath),
            'lastModified' => Storage::disk('local')->lastModified($storagePath)
        ];
    }

    /**
     * 读取多个文件
     */
    public function readFiles(array $paths): array
    {
        return array_filter(array_map([$this, 'readFile'], $paths));
    }

    /**
     * 读取目录下的所有文件
     */
    public function readDirectory(string $directory, ?string $pattern = null, bool $recursive = true): array
    {
        $finder = new Finder();
        $finder->files()->in($directory);

        if ($pattern) {
            $finder->name($pattern);
        }

        if (!$recursive) {
            $finder->depth(0);
        }

        foreach ($this->excludedDirectories as $excludedDir) {
            $finder->exclude($excludedDir);
        }

        $files = [];
        foreach ($finder as $file) {
            $relativePath = $file->getRelativePathname();
            if ($this->isFileTypeSupported($relativePath)) {
                $files[] = [
                    'path' => $relativePath,
                    'name' => $file->getFilename(),
                    'content' => $file->getContents(),
                    'extension' => $file->getExtension(),
                    'size' => $file->getSize(),
                    'lastModified' => $file->getMTime()
                ];
            }
        }

        return $files;
    }

    /**
     * 保存文件
     */
    public function saveFile(string $path, string $content): bool
    {
        try {
            Storage::put($path, $content);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 检查文件类型是否支持
     */
    private function isFileTypeSupported(string $filename): bool
    {
        if (empty($this->supportedExtensions)) {
            return true;
        }

        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($extension, $this->supportedExtensions);
    }

    /**
     * 获取文件分类
     */
    public function getFileCategory(string $extension): string
    {
        $categories = [
            'text' => ['txt', 'md', 'log'],
            'code' => ['php', 'js', 'py', 'java', 'cpp', 'h', 'cs'],
            'web' => ['html', 'htm', 'css', 'xml', 'json'],
            'config' => ['yml', 'yaml', 'ini', 'conf', 'cfg'],
            'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'odt', 'ods', 'odp'],
        ];

        foreach ($categories as $category => $extensions) {
            if (in_array(strtolower($extension), $extensions)) {
                return $category;
            }
        }

        return 'other';
    }

        /**
     * 转换文件路径为存储路径
     */
    private function convertToStoragePath(string $path): string
    {
         // 使用正则表达式匹配包含 files 的路径部分
         if (preg_match('/ai\/.*/', $path, $matches)) {
            return $matches[0];
        }
        return $path;
    }



    /**
     * 生成替换预览
     */
    public function generatePreview(array $paths, string $sourceText): array
    {
        $totalMatches = 0;
        $fileResults = [];
        $categories = [];
        // 先读取所有文件
        $files = [];
        foreach ($paths as $path) {
            if (is_dir($path)) {
                // 如果是目录，读取目录下的所有文件
                $files = array_merge($files, $this->readDirectory($path));
            } else {
                // 如果是单个文件，直接读取
                $path = $this->convertToStoragePath($path);
                $file = $this->readFile($path);
                if ($file) {
                    $files[] = $file;
                }
            }
        }
        // 处理文件内容并统计
        foreach ($files as $file) {
            $matches = substr_count($file['content'], $sourceText);
            if ($matches > 0) {
                $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                $category = $this->getFileCategory($extension);

                if (!isset($categories[$category])) {
                    $categories[$category] = [
                        'name' => $category,
                        'count' => 0,
                        'textCount' => 0
                    ];
                }

                $categories[$category]['count']++;
                $categories[$category]['textCount'] += $matches;
                $totalMatches += $matches;

                $fileResults[] = [
                    'name' => $file['name'],
                    'matches' => $matches
                ];
            }
        }

        return [
            'total_matches' => $totalMatches,
            'files' => $fileResults,
            'categories' => array_values($categories)
        ];
    }

    /**
     * 执行文本替换
     */
    public function executeReplacement(array $paths, string $sourceText, string $targetText): array
    {
        $totalReplaced = 0;
        $processedFiles = [];
        $files = [];

        // 处理文件路径并读取文件
        foreach ($paths as $path) {
            if (is_dir($path)) {
                // 如果是目录，读取目录下的所有文件
                $files = array_merge($files, $this->readDirectory($path));
            } else {
                $path = $this->convertToStoragePath($path);
                // 如果是单个文件，直接读取
                $file = $this->readFile($path);
                if ($file) {
                    $files[] = $file;
                }
            }
        }

        // 执行替换操作
        foreach ($files as $file) {
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);

            // 检查文件类型是否支持文本替换
            if (!in_array($this->getFileCategory($extension), ['text', 'code', 'web', 'config', 'document'])) {
                continue;
            }

            $newContent = str_replace($sourceText, $targetText, $file['content'], $count);
            if ($count > 0) {
                $outputPath = $this->saveModifiedFile($file['name'], $newContent);
                $processedFiles[] = [
                    'name' => $file['name'],
                    'path' => $outputPath,
                    'replacements' => $count
                ];
                $totalReplaced += $count;
            }
        }

        return [
            'total_replaced' => $totalReplaced,
            'files' => $processedFiles
        ];
    }

    /**
     * 保存修改后的文件
     */
    private function saveModifiedFile(string $originalName, string $content): string
    {
        $timestamp = date('YmdHis');
        $outputPath = 'replacements/' . $timestamp . '_' . $originalName;
        Storage::put($outputPath, $content);
        return $outputPath;
    }
}
