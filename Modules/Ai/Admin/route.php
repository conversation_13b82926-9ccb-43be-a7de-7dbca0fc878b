<?php
/*
 * @Author: jay <EMAIL>
 * @Date: 2025-04-10 19:01:24
 * @LastEditors: jay <EMAIL>
 
 * @LastEditTime: 2025-04-15 14:27:18
 * @FilePath: /bwms/Modules/Ai/Admin/route.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

use Illuminate\Support\Facades\Route;
use Modules\Ai\Admin\Controllers\ContentAssistantController;
use Modules\Ai\Admin\Controllers\SettingController;
use Modules\Ai\Admin\Controllers\PromptTemplateController;
use Modules\Ai\Admin\Controllers\ChatController;

Route::prefix('ai')->group(function () {
    Route::post('/generate', [ContentAssistantController::class, 'generateContent']);

    Route::get('setting/create', [SettingController::class, 'create'])->name('bingo-admin.ai.create');
    Route::get('setting/{id}/edit', [SettingController::class, 'edit'])->name('bingo-admin.ai.edit');
    Route::apiResource('setting', SettingController::class)->names('bingo-admin.ai');

    Route::post('/prompt-templates/{templateId}/generate', [PromptTemplateController::class, 'generateContent']);

    Route::apiResource('prompt-templates', PromptTemplateController::class);

    // 添加以下路由
    Route::post('/chat', [ChatController::class, 'chat']);
    Route::get('/chat/config', [ChatController::class, 'getChatConfig']);
    Route::get('/downloadFile', [ChatController::class, 'downloadFile'])->withoutMiddleware(['auth.iam']);
});
