<?php

namespace Modules\Ai\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GenerateContentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'primary_focus' => 'required|string|max:255',
            'priority_keywords' => 'required|string|max:255',
            'lang' => 'required|string|max:50',
            'short_details' => 'nullable|boolean',
            'article_details' => 'nullable|boolean',
            'article_title' => 'nullable|boolean',
            'content_length' => 'nullable|integer|min:1',
            'short_details_length' => 'nullable|integer|max:100',
            'title_length' => 'nullable|integer|max:100',
            'editorial_tone' => 'nullable|string|max:50',
            'creativity_level' => 'nullable|numeric|min:0|max:1',
        ];
    }

    public function messages(): array
    {
        return [
            'primary_focus.required' => T('Ai::validation.primary_focus.required'),
            'primary_focus.string' => T('Ai::validation.primary_focus.string'),
            'primary_focus.max' => T('Ai::validation.primary_focus.max'),
            'priority_keywords.required' => T('Ai::validation.priority_keywords.required'),
            'priority_keywords.string' => T('Ai::validation.priority_keywords.string'),
            'priority_keywords.max' => T('Ai::validation.priority_keywords.max'),
            'lang.required' => T('Ai::validation.lang.required'),
            'lang.string' => T('Ai::validation.lang.string'),
            'lang.max' => T('Ai::validation.lang.max'),
            'short_details.boolean' => T('Ai::validation.short_details.boolean'),
            'article_details.boolean' => T('Ai::validation.article_details.boolean'),
            'article_title.boolean' => T('Ai::validation.article_title.boolean'),
            'content_length.integer' => T('Ai::validation.content_length.integer'),
            'content_length.min' => T('Ai::validation.content_length.min'),
            'short_details_length.integer' => T('Ai::validation.short_details_length.integer'),
            'short_details_length.max' => T('Ai::validation.short_details_length.max'),
            'title_length.integer' => T('Ai::validation.title_length.integer'),
            'title_length.max' => T('Ai::validation.title_length.max'),
            'editorial_tone.string' => T('Ai::validation.editorial_tone.string'),
            'editorial_tone.max' => T('Ai::validation.editorial_tone.max'),
            'creativity_level.numeric' => T('Ai::validation.creativity_level.numeric'),
            'creativity_level.min' => T('Ai::validation.creativity_level.min'),
            'creativity_level.max' => T('Ai::validation.creativity_level.max'),
        ];
    }
}
