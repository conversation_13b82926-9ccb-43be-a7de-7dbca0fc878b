<?php

namespace Modules\Ai\Admin\Controllers;

use Bingo\Base\AdminController;
use Modules\Ai\Services\AI\AIAgent;
use Modules\Ai\Services\Chat\ChatSessionService;
use Modules\Ai\Config\ChatActions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChatController extends AdminController
{
    private ChatSessionService $sessionService;

    public function __construct(ChatSessionService $sessionService)
    {
        $this->sessionService = $sessionService;
    }

    /**
     * 处理聊天消息 - 唯一的聊天接口
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function chat(Request $request)
    {
        $validated = $request->validate([
            'messages' => 'required|array|min:1',
            'messages.*.role' => 'required|string|in:user,bot',
            'messages.*.content' => 'nullable|string',
            'messages.*.timestamp' => 'nullable|integer',
            'sessionId' => 'nullable|string',
            'action' => 'nullable|string',
            'payload' => 'nullable|array',
            'context' => 'nullable|array',
        ]);
  // 获取AIAgent实例，统一处理所有消息
        $aiAgent = app(AIAgent::class);
        // 生成统一的session_id格式：chat_{用户id}
        $userId = Auth::id();
        $sessionId = "chat_{$userId}";

        // 获取最新的用户消息
        $latestMessage = end($validated['messages']);
        if (!$latestMessage || $latestMessage['role'] !== 'user') {
            return response()->json([
                'content' => '请输入有效的消息',
                'sessionId' => $sessionId,
                'quickActions' => $aiAgent->getDefaultQuickActions()
            ]);
        }
        
        // 确保消息内容是字符串类型，防止null值
        $messageContent = (string)($latestMessage['content'] ?? '');
        
        
        // 构建简化的请求参数
        $params = [
            'action' => $validated['action'] ?? ChatActions::TEXT_MESSAGE,
            'payload' => $validated['payload'] ?? []
        ];

        // 处理聊天请求
        $response = $aiAgent->chat($sessionId, $messageContent, $params);

        // 确保返回会话ID
        $response['sessionId'] = $sessionId;

        return response()->json($response);
    }

    /**
     * 获取默认快捷操作
     */
    private function getDefaultQuickActions(): array
    {
        return [
            [
                'id' => 'article_replacer',
                'text' => '📄 文章替换',
                'action' => ChatActions::SELECT_ARTICLE_REPLACER
            ]
        ];
    }
}
