<?php

namespace Modules\Ai\Admin\Controllers;

use Bingo\Base\BingoController;
use Modules\Ai\Services\PromptTemplateService;
use Illuminate\Http\Request;
use Exception;

class PromptTemplateController extends BingoController
{
    protected PromptTemplateService $service;

    public function __construct(PromptTemplateService $service)
    {
        $this->service = $service;
    }

    /**
     * 獲取所有模板
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        return $this->service->getAllTemplates();
    }

    /**
     * 獲取指定ID的模板
     * @param Request $request
     * @param int $id
     * @return mixed
     */
    public function show(Request $request, int $id): mixed
    {
        return $this->service->getTemplateById($id);
    }

    /**
     * 創建新模板
     * @param Request $request
     * @return mixed
     */
    public function store(Request $request): mixed
    {
        $validatedData = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'goal' => 'required|string',
            'participants' => 'required|string',
            'setting' => 'nullable|string',
            'relationships' => 'nullable|string',
            'fields' => 'nullable|array',
            'instructions' => 'required|string',
            'guidelines' => 'nullable|string',
            'lang' => 'required|string',
            'style_tone' => 'nullable|string',
            'ext' => 'nullable|array',
            'status' => 'required|boolean',
            'creator_id' => 'required|integer',
        ]);

        return $this->service->createTemplate($validatedData);
    }

    /**
     * 更新模板
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function update(Request $request, int $id): array
    {
        $validatedData = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'goal' => 'required|string',
            'participants' => 'required|string',
            'setting' => 'nullable|string',
            'relationships' => 'nullable|string',
            'fields' => 'nullable|array',
            'instructions' => 'required|string',
            'guidelines' => 'nullable|string',
            'lang' => 'required|string',
            'style_tone' => 'nullable|string',
            'ext' => 'nullable|array',
            'status' => 'required|boolean',
            'creator_id' => 'required|integer',
        ]);

        return $this->service->updateTemplate($id, $validatedData);
    }

    /**
     * 刪除模板
     * @param int $id
     * @return string[]
     */
    public function destroy(int $id): array
    {
        $this->service->deleteTemplate($id);
        return ['message' => 'Prompt template deleted successfully.'];
    }

    /**
     * 生成內容
     * @param Request $request
     * @param int $templateId
     * @return string
     * @throws Exception
     */
    public function generateContent(Request $request, int $templateId): string
    {
        $validatedData = $request->validate([
            'fields' => 'required|array',
        ]);

        return $this->service->generateContent($templateId, $validatedData['fields']);
    }
}
