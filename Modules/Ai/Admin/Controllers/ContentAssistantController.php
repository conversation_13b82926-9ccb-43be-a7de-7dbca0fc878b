<?php

namespace Modules\Ai\Admin\Controllers;

use Bingo\Base\AdminController;
use Exception;
use Modules\Ai\Admin\Requests\GenerateContentRequest;
use Modules\Message\Services\MessageService;

/**
 * AI 内容生成助手
 */
class ContentAssistantController extends AdminController
{
    protected MessageService $aiAssistantService;

    public function __construct(MessageService $aiAssistantService)
    {
        $this->aiAssistantService = $aiAssistantService;
    }

    /**
     * 生成内容
     * @param GenerateContentRequest $request
     * @return array|array[]
     */
    public function generateContent(GenerateContentRequest $request): array
    {
        $validated = $request->validated();


        if ($validated['lang'] == "-1") {
            return ['language' => ['Please select a language']];
        }

        if (! ($validated['short_details'] || $validated['article_details'] || $validated['article_title'])) {
            return ['errors' => ['request_type' => ['Please select any of the above to generate response']]];
        }

        try {
            $parameters = [
                'content_length' => $validated['content_length'],
                'short_details_length' => $validated['short_details_length'],
                'title_length' => $validated['title_length'],
                'primary_focus' => $validated['primary_focus'],
                'priority_keywords' => $validated['priority_keywords'],
                'lang' => $validated['lang'],
                'editorial_tone' => $validated['editorial_tone'],
                'creativity_level' => $validated['creativity_level'],
                'article_details' => $validated['article_details'],
                'short_details' => $validated['short_details'],
                'article_title' => $validated['article_title'],
                'max_token' => $validated['content_length']
            ];
            $response = $this->aiAssistantService->generateContent($parameters);

            return [
                'success' => true,
                'article_details' => $response['article_details'] ?? null,
                'short_details' => $response['short_details'] ?? null,
                'article_title' => $response['article_title'] ?? null,
                'message' => 'Content generation successful'
            ];
        } catch (Exception $e) {

            return [
                'success' => false,
                'message' => 'Unable to generate content'
            ];
        }
    }

}
