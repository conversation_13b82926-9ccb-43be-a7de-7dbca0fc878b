<?php

namespace Modules\Ai\Admin\Controllers;

use Bingo\Amis\Components\Form;
use Bingo\Amis\Components\Grid;
use Bingo\Amis\Renderers\Divider;
use Bingo\Amis\Renderers\Form\Group;
use Bingo\Amis\Renderers\Form\InputText;
use Bingo\Amis\Renderers\Form\Radios;
use Bingo\Amis\Renderers\Form\Select;
use Bingo\Amis\Renderers\Status;
use Bingo\Base\AdminController;
use Modules\Ai\Models\AiSetting;

/**
 * AI 设置
 */
class SettingController extends AdminController
{
    public function grid(): Grid
    {
        return Grid::make(AiSetting::query(), 'bingo-admin.ai', function (Grid $grid) {
            $grid->disableMultilingualLanguage();
            $grid->usePage()->title(T("Ai::list_title"));
            $grid->column("id", "ID");
            $grid->column("engine_name", T("Ai::list.engine"));
            $grid->column("default_model", T("Ai::list.default_model"));
            $grid->column('is_default', T('Ai::list.is_default'))->width(40)->useTableColumn(Status::make());
            $grid->column('status', T('Status'))->width(100)->useTableColumn(Status::make());
            $grid->column("created_at", T("Created At"));
            $grid->column("updated_at", T("Updated At"));
            $grid->dialogForm('lg');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->wrapWithPanel(false)->className('mb-3 bg-search p-2 pt-3');
                $filter->like('engine', T("Ai::list.engine"))->useFormItem()->clearable(true)->size("sm");
            });

            $grid->actions(function (Grid\Actions $actions) use ($grid) {
                $actions->rowAction();
            });
        });
    }

    protected function form(): Form
    {
        return Form::make(AiSetting::query(), 'bingo-admin.ai', function (Form $form) {
            $form->customLayout([
                $form->item('engine', T('Ai::list.engine'))->useFormItem(
                    Select::make()->name('engine')
                        ->options(getSelectOption(Message::toArray()))
                )->required(true)->size('lg'),
                InputText::make()->name('api_key')->label(T("Ai::list.api_key"))->size('lg'),
                Divider::make(),
                InputText::make()->name('default_model')->label(T("Ai::list.default_model"))->size('lg'),
                Divider::make(),
                Group::make()->body([
                    $form->item('is_default', T('Ai::list.is_default'))->useFormItem(Radios::make()->options([
                        ['value' => 1, 'label' => T('Yes')],
                        ['value' => 0, 'label' => T('No')],
                    ])->value(0)),
                    $form->item('status', T('Status'))->useFormItem(Radios::make()->options([
                        ['value' => 1, 'label' => T('Yes')],
                        ['value' => 0, 'label' => T('No')],
                    ])->value(1)),
                ]),
            ]);
        });

    }
}
