<?php

return [
    'primary_focus.required' => 'The primary focus is required.',
    'primary_focus.string' => 'The primary focus must be a string.',
    'primary_focus.max' => 'The primary focus may not be greater than 255 characters.',
    'priority_keywords.required' => 'The priority keywords are required.',
    'priority_keywords.string' => 'The priority keywords must be a string.',
    'priority_keywords.max' => 'The priority keywords may not be greater than 255 characters.',
    'lang.required' => 'The language is required.',
    'lang.string' => 'The language must be a string.',
    'lang.max' => 'The language may not be greater than 50 characters.',
    'short_details.boolean' => 'The short details field must be true or false.',
    'article_details.boolean' => 'The article details field must be true or false.',
    'article_title.boolean' => 'The article title field must be true or false.',
    'content_length.integer' => 'The content length must be an integer.',
    'content_length.min' => 'The content length must be at least 1.',
    'short_details_length.integer' => 'The short details length must be an integer.',
    'short_details_length.max' => 'The short details length may not be greater than 100 characters.',
    'title_length.integer' => 'The title length must be an integer.',
    'title_length.max' => 'The title length may not be greater than 100 characters.',
    'editorial_tone.string' => 'The editorial tone must be a string.',
    'editorial_tone.max' => 'The editorial tone may not be greater than 50 characters.',
    'creativity_level.numeric' => 'The creativity level must be a number.',
    'creativity_level.min' => 'The creativity level must be at least 0.',
    'creativity_level.max' => 'The creativity level may not be greater than 1.',
];
