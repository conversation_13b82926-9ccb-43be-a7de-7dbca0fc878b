<?php
namespace Modules\Ai\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Iam\Models\IamUsers;

class ChatSession extends Model
{
    protected $table = 'ai_chat_sessions';

    protected $fillable = [
        'user_id', 
        'title',
        'active_tool',
        'tool_state',
        'tool_data',
        'context',
        'last_activity_at',
        'session_id' // 保留session_id字段
    ];

    protected $casts = [
        'tool_data' => 'json',
        'context' => 'json',
        'last_activity_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(IamUsers::class, 'user_id', 'id');
    }

    public function messages(): HasMany
    {
        return $this->hasMany(Message::class, 'session_id');
    }

    public function files()
    {
        return $this->hasMany(File::class, 'session_id');
    }
    
    /**
     * 判断会话是否有活动工具
     *
     * @return bool
     */
    public function hasActiveTool(): bool
    {
        return !empty($this->active_tool) && $this->tool_state === 'active';
    }
    
    /**
     * 获取工具状态
     *
     * @return string
     */
    public function getToolState(): string
    {
        return $this->tool_state ?? 'none';
    }
    
    /**
     * 设置会话的活动工具
     *
     * @param string $toolName 工具名称
     * @param array $initialData 初始数据
     * @return self
     */
    public function setActiveTool(string $toolName, array $initialData = []): self
    {
        $this->update([
            'active_tool' => $toolName,
            'tool_state' => 'active',
            'tool_data' => $initialData,
            'last_activity_at' => now(),
        ]);
        
        return $this;
    }
    
    /**
     * 清除活动工具
     *
     * @return self
     */
    public function clearActiveTool(): self
    {
        $this->update([
            'active_tool' => null,
            'tool_state' => 'none',
            'tool_data' => null,
            'last_activity_at' => now(),
        ]);
        
        return $this;
    }
    
    /**
     * 更新会话活动时间
     *
     * @return self
     */
    public function updateActivity(): self
    {
        $this->update(['last_activity_at' => now()]);
        return $this;
    }
    
    /**
     * 设置上下文数据
     * 
     * @param array $data 上下文数据
     * @return self
     */
    public function setContext(array $data): self
    {
        $this->update(['context' => $data]);
        return $this;
    }
    
    /**
     * 获取上下文数据
     * 
     * @return array
     */
    public function getContext(): array
    {
        return $this->context ?? [];
    }

    /**
     * 获取会话的最后一条消息
     * 
     * @return ChatMessage|null
     */
    public function getLastMessage()
    {
        return $this->messages()->latest()->first();
    }

    /**
     * 获取会话的标题
     * 
     * @return string
     */
    public function getSessionTitle()
    {
        if (!empty($this->title)) {
            return $this->title;
        }

        // 如果没有标题，尝试从第一条用户消息生成
        $firstUserMessage = $this->messages()
            ->where('role', 'user')
            ->orderBy('created_at')
            ->first();

        if ($firstUserMessage) {
            $content = $firstUserMessage->content;
            // 截取前30个字符作为标题
            $title = mb_substr($content, 0, 30);
            if (mb_strlen($content) > 30) {
                $title .= '...';
            }
            return $title;
        }

        return '新对话 #' . $this->id; // 修改为使用id而不是user_session_index
    }
}
