<?php

namespace Modules\Ai\Models;

use Bingo\Base\BingoModel as Model;
use Mo<PERSON>les\Ai\Enums\AiEngine;

class AiSetting extends Model
{
    protected $table = 'ai_setting';

    protected $fillable = [
        'id', 'engine', 'api_key', 'default_model', 'is_default', 'status', 'creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

    protected $casts = [
        'engine' => 'integer'
    ];


    public $appends = ['engine_name'];

    public function getEngineNameAttribute(): string
    {
        return AiEngine::fromValue($this->engine)->description();
    }

}
