<?php

namespace Modules\Ai\Models;

use Illuminate\Database\Eloquent\Model;

class File extends Model
{
    protected $table = 'ai_files';
    
    protected $fillable = [
        'session_id',
        'name',
        'path',
        'mime_type',
        'size',
        'file_id',
        'metadata',
    ];
    
    protected $casts = [
        'metadata' => 'array',
        'size' => 'integer',
    ];
    
    public function session()
    {
        return $this->belongsTo(ChatSession::class);
    }
} 