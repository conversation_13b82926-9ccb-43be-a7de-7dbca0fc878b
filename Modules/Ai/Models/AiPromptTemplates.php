<?php

namespace Modules\Ai\Models;

use Bingo\Base\BingoModel as Model;

class AiPromptTemplates extends Model
{
    protected $table = 'ai_prompt_templates';

    protected $fillable = [
        'id', 'title', 'content', 'goal', 'participants', 'setting', 'relationships', 'fields', 'instructions', 'guidelines', 'style_tone', 'ext', 'status', 'lang', 'creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

    protected $casts = [
        'fields' => 'json',
        'ext' => 'json',
        'status' => 'boolean',
    ];
}
