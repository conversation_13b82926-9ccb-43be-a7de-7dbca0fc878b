<?php

namespace Modules\Ai\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ToolCall extends Model
{
    protected $table = 'ai_tool_calls';

    protected $fillable = ['message_id', 'tool_name', 'params', 'result', 'status'];

    protected $casts = [
        'params' => 'array',
        'result' => 'array',
    ];

    public function message(): BelongsTo
    {
        return $this->belongsTo(Message::class);
    }
}
