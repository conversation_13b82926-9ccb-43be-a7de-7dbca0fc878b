<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddToolFieldsToChatSessionsTable extends Migration
{
    /**
     * 运行迁移
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ai_chat_sessions', function (Blueprint $table) {
            // 添加工具状态相关字段
            $table->string('active_tool')->nullable()->after('title')->comment('当前活动工具名称');
            $table->string('tool_state')->default('none')->after('active_tool')->comment('工具状态：none/active/completed/error');
            $table->json('tool_data')->nullable()->after('tool_state')->comment('工具数据（JSON格式）');
            $table->json('context')->nullable()->after('tool_data')->comment('会话上下文数据（JSON格式）');
            $table->timestamp('last_activity_at')->nullable()->after('context')->comment('最后活动时间');
        });
    }

    /**
     * 回滚迁移
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ai_chat_sessions', function (Blueprint $table) {
            $table->dropColumn('active_tool');
            $table->dropColumn('tool_state');
            $table->dropColumn('tool_data');
            $table->dropColumn('context');
            $table->dropColumn('last_activity_at');
        });
    }
} 