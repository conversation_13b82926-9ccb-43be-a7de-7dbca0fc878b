-- Table structure for table `bingo_ai_setting`

CREATE TABLE `bingo_ai_setting` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键，自动增长的唯一标识符',
  `engine` tinyint DEFAULT NULL COMMENT '引擎名称：1 openai ',
  `api_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'api 密钥',
  `default_model` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '默认模型',
  `is_default` tinyint NOT NULL DEFAULT '0' COMMENT '默认AI引擎，1=是，0=否',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '是否启用，0=关闭，1=开启',
  `creator_id` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者ID',
  `created_at` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted_at` int unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI 设置';

-- Dumping data for table `bingo_ai_setting`

INSERT INTO `bingo_ai_setting` (`id`, `engine`, `api_key`, `default_model`, `is_default`, `status`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES 
('1', '1', '***************************************************', 'gpt-4o', '1', '1', '1', '1717121184', '1717300517', '0');


