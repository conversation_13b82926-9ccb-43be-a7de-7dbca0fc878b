<?php

namespace Modules\Ai\Providers;

use Bingo\Providers\BingoModuleServiceProvider;
use Modules\Iam\Enums\MenuType;
use Modules\Ai\Services\LLM\LLMService;  // 修改这行
use Modules\Ai\Services\AI\AIAgent;
use Modules\Ai\Services\Tools\ToolRegistry;
use Modules\Ai\Services\Tools\TextReplacerTool;
use Modules\Ai\Services\Tools\SeoAnalysisTool;
use Modules\Ai\Services\Chat\ChatSessionService;
use Modules\Ai\Services\AI\IntentRecognition\TextIntentRecognition;
use Modules\Ai\Services\AI\IntentRecognition\SeoIntentRecognition;
use Modules\Ai\Services\AI\IntentRecognition\ArticleIntentRecognition;
use Modules\Ai\Services\AI\ParameterExtraction\SeoParameterExtractor;
use Modules\Ai\Services\AI\ParameterExtraction\TextParameterExtractor;
use Modules\Ai\Services\AI\ParameterExtraction\ArticleParameterExtractor;
use Modules\Ai\Services\AI\IntentRecognition\UnknownToolIntentRecognizer;
use Modules\Ai\Services\LLM\LLMResponseGenerator;
use Modules\Ai\Services\FileManager\FileManagerService;
use Modules\Ai\Services\AI\Analysis\SeoLLMAnalyzer;
use Modules\Ai\Services\Tools\ArticleContentReplacerTool;


class AiServiceProvider extends BingoModuleServiceProvider
{


    public function boot(): void
    {
        // 1. 加载配置
        $this->mergeConfigFrom(
            __DIR__ . '/../config/services.php',
            'services'
        );
        $path = dirname(__DIR__, 2).DIRECTORY_SEPARATOR.'Ai'.DIRECTORY_SEPARATOR.'Lang';
        $this->loadTranslationsFrom($path, 'Ai');
        $this->registerNavigation();

       // 注册LLM服务
       $this->app->singleton(LLMService::class, function ($app) {
           return new LLMService(config('services.ai'));
       });

           // 注册LLM响应生成器
        $this->app->singleton(LLMResponseGenerator::class, function ($app) {
            return new LLMResponseGenerator(
                $app->make(LLMService::class),
                $app->make('log')
            );
        });


       // 注册session处理
        $this->app->singleton(ChatSessionService::class, function ($app) {
            return new ChatSessionService();
        });


       // 注册AI代理
       $this->app->singleton(AIAgent::class, function ($app) {
           return new AIAgent(
               $app->make(ChatSessionService::class),
               $app->make(UnknownToolIntentRecognizer::class),
               $app->make(LLMResponseGenerator::class)
           );
       });

       $this->app->singleton(SeoLLMAnalyzer::class, function ($app) {
        return new SeoLLMAnalyzer(
            $app->make(LLMService::class)
        );
    });

       // 注册意图识别服务

        $this->registerModulePermissions();

        // 注册AI工具
        $this->registerAiTools();

    }

    /**
     * 注册工具
     *
     * @return void
     */
    private function registerAiTools(): void
    {
        // 注册文本意图识别服务
        $this->app->singleton(TextIntentRecognition::class, function ($app) {
            return new TextIntentRecognition(
                $app->make(LLMService::class)
            );
        });

        // 注册SEO意图识别服务
        $this->app->singleton(SeoIntentRecognition::class, function ($app) {
            return new SeoIntentRecognition(
                $app->make(LLMService::class)
            );
        });

        // 注册文章替换意图识别服务
        $this->app->singleton(ArticleIntentRecognition::class, function ($app) {
            return new ArticleIntentRecognition(
                $app->make(LLMService::class)
            );
        });
        
        // 注册文本参数提取器
        $this->app->singleton(TextParameterExtractor::class, function ($app) {
            return new TextParameterExtractor(
                $app->make(LLMService::class)
            );
        });
        
        // 注册SEO参数提取器
        $this->app->singleton(SeoParameterExtractor::class, function ($app) {
            return new SeoParameterExtractor(
                $app->make(LLMService::class)
            );
        });

        // 注册文章替换提取器
        $this->app->singleton(ArticleParameterExtractor::class, function ($app) {
            return new ArticleParameterExtractor(
                $app->make(LLMService::class)
            );
        });
        
        // 注册文本替换工具        
        ToolRegistry::register('text_replacer', new TextReplacerTool(
            app(TextIntentRecognition::class),
            app(TextParameterExtractor::class),
            app(FileManagerService::class)
        ));
        
        // 注册SEO分析工具
        ToolRegistry::register('seo_analysis', new SeoAnalysisTool(
            app(SeoIntentRecognition::class),
            app(SeoParameterExtractor::class),
            app(SeoLLMAnalyzer::class)
        ));

        // 注册文章内容替换工具
        ToolRegistry::register('article_content_replacer', new ArticleContentReplacerTool(
            app(ArticleIntentRecognition::class),
            app(ArticleParameterExtractor::class),
            app(FileManagerService::class)
        ));
    }



    /**
     * route path
     *
     * @return string
     */
    public function moduleName(): string
    {
        return 'Ai';
    }

    protected function navigation(): array
    {
        return [
        ];
    }

    /**
     * 注册配置
     * @return array
     */
    public function registerSettings(): array
    {
        return [
        ];
    }

    public function registerPermissions(): array
    {
        $admin = [
            'ai' => [
                'permission_name' => T("Ai::permission.ai_settings"),
                'route' => '/ai',
                'parent_id' => 0,
                'permission_mark' => 'ai',
                'component' => '/admin/layout/index.vue',
                'type' => MenuType::Top->value(),
                'sort' => 1,
                'children' => [
                    [
                        'permission_name' => T("Ai::permission.ai_setting"),
                        'route' => 'setting',
                        'parent_id' => 'ai',
                        'permission_mark' => 'setting',
                        'component' => '/admin/views/AMisPage.vue',
                        'type' => MenuType::Menu->value(),
                        'sort' => 1,
                        'actions' => [
                            [
                                'permission_name' => T("List"),
                                'route' => '',
                                'permission_mark' => 'setting@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 1,
                                'parent_id' => 'setting'
                            ],
                            [
                                'permission_name' => T("Read"),
                                'route' => '',
                                'permission_mark' => 'setting@show',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 2,
                                'parent_id' => 'setting'
                            ],
                            [
                                'permission_name' => T("Create"),
                                'route' => '',
                                'permission_mark' => 'setting@store',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 3,
                                'parent_id' => 'setting'
                            ],
                            [
                                'permission_name' => T("Update"),
                                'route' => '',
                                'permission_mark' => 'setting@update',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 4,
                                'parent_id' => 'setting'
                            ],
                            [
                                'permission_name' => T("Delete"),
                                'route' => '',
                                'permission_mark' => 'setting@destroy',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 5,
                                'parent_id' => 'setting'
                            ],
                        ],
                    ],
                ],
            ],
        ];
        $frontend = [];
        return array_merge(["admin" => $admin], ["frontend" => $frontend]);
    }

}
