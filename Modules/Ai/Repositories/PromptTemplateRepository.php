<?php

namespace Modules\Ai\Repositories;

use Modules\Ai\Models\AiPromptTemplates;

class PromptTemplateRepository
{
    public function getAll()
    {
        return AiPromptTemplates::all();
    }

    public function findById($id)
    {
        return AiPromptTemplates::findOrFail($id);
    }

    public function create(array $data)
    {
        return AiPromptTemplates::create($data);
    }

    public function update($id, array $data)
    {
        $template = $this->findById($id);
        $template->update($data);
        return $template;
    }

    public function delete($id)
    {
        $template = $this->findById($id);
        return $template->delete();
    }
}
