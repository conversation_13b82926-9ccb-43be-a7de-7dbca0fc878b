# 聊天功能参数传递流程分析文档

## 概述

本文档详细分析聊天功能从前端到后端的完整参数传递流程，帮助理解数据在各个层级间的传递和转换。

## 1. 前端发送请求

### 1.1 前端请求结构 (chatBot.vue)

```typescript
// 发送消息的基本结构
const requestData = {
  messages: [
    {
      role: 'user',
      content: messageContent,  // 用户输入的文本内容
      timestamp: Date.now()
    }
  ],
  sessionId: sessionId.value,     // 会话ID
  action: action || 'text_message', // 操作类型
  payload: payload || {},         // 载荷数据
  context: chatContext.value      // 上下文数据
}
```

### 1.2 快捷回复请求

当用户点击快捷回复按钮时：

```typescript
// handleQuickReply 方法构建的 payload
const payload = {
  reply_id: reply.id,           // 回复按钮ID，如 "ai_replace"
  reply_action: reply.action,   // 回复动作，如 "QUICK_REPLACE"
  reply_text: reply.text,       // 回复文本，如 "🔄 AI → 人工智能"
  reply_data: reply.data || {}  // 回复数据，如 { sourceText: "AI", targetText: "人工智能" }
}
```

### 1.3 实际发送示例

```json
{
  "messages": [{"role": "user", "content": "", "timestamp": 1752150628967}],
  "sessionId": "chat_1752150628967_71dstgmln",
  "action": "quick_reply",
  "payload": {
    "reply_id": "ai_replace",
    "reply_action": "QUICK_REPLACE",
    "reply_text": "🔄 AI → 人工智能",
    "reply_data": {
      "sourceText": "AI",
      "targetText": "人工智能"
    }
  },
  "context": {}
}
```

## 2. ChatController 处理请求

### 2.1 请求验证 (ChatController.php)

```php
$validated = $request->validate([
    'messages' => 'required|array|min:1',
    'messages.*.role' => 'required|string|in:user,bot',
    'messages.*.content' => 'nullable|string',
    'messages.*.timestamp' => 'nullable|integer',
    'sessionId' => 'nullable|string',
    'action' => 'nullable|string',
    'payload' => 'nullable|array',
    'context' => 'nullable|array',
]);
```

### 2.2 参数提取和重构

```php
// 提取用户消息内容
$latestMessage = end($validated['messages']);
$messageContent = $latestMessage['content'] ?? '';

// 构建传递给 AIAgent 的参数
$params = [
    'files' => !empty($latestMessage['fileUrl']) ? [$latestMessage['fileUrl']] : [],
    'userId' => Auth::id(),
    'action' => $validated['action'] ?? 'text_message',
    'payload' => $validated['payload'] ?? [],
    'context' => $validated['context'] ?? [],
];

// 调用 AIAgent
$response = $aiAgent->chat($sessionId, $messageContent, $params);
```

## 3. AIAgent 处理流程

### 3.1 chat 方法签名

```php
public function chat(string $sessionId, string $message, array $params = []): array
```

### 3.2 参数传递到工具

有两个主要路径：

#### 路径1: 快捷操作激活工具 (handleQuickActionRequest)

```php
// 检测快捷操作
if ($this->isQuickActionRequest($params)) {
    return $this->handleQuickActionRequest($session, $params);
}

// handleQuickActionRequest 调用 activateTool
return $this->activateTool($toolName, $session, $params);
```

#### 路径2: 委托消息给活动工具 (delegateMessageToTool)

```php
if ($activeToolInfo && $activeToolInfo['state'] === ChatSessionService::TOOL_STATE_ACTIVE) {
    return $this->delegateMessageToTool($message, $session, $activeToolInfo, $params);
}
```

### 3.3 activateTool 方法

```php
private function activateTool(string $toolName, array &$session, array $params = [], array $intentParams = []): array
{
    $tool = ToolRegistry::getTool($toolName);
    
    // 合并参数
    $toolParams = array_merge($params, $intentParams);
    
    // 设置工具为活动状态
    $this->sessionService->setActiveTool($session, $toolName, ['params' => $toolParams]);

    // 调用工具的 handleMessage 方法
    return $tool->handleMessage('', [
        'session' => $session,
        'params' => $toolParams      // 关键：参数在这里传递
    ]);
}
```

### 3.4 delegateMessageToTool 方法

```php
private function delegateMessageToTool(string $message, array &$session, array $activeToolInfo, array $params): array
{
    // 更新工具参数
    $activeToolInfo = $this->updateToolParams($activeToolInfo, $params);
    
    // 调用工具处理
    $response = $tool->handleMessage($message, [
        'session' => $session,
        'params' => $activeToolInfo['data']['params']  // 关键：从工具数据中获取参数
    ]);
}
```

## 4. 工具接收参数

### 4.1 工具 handleMessage 方法签名

```php
public function handleMessage(string $message, array $context = []): array
```

### 4.2 context 参数结构

根据调用路径不同，context 结构如下：

#### 工具激活时 (activateTool 调用)

```php
$context = [
    'session' => $session,      // 会话数据
    'params' => $toolParams     // 包含 action, payload, context, userId, files 等
]
```

#### 消息委托时 (delegateMessageToTool 调用)

```php
$context = [
    'session' => $session,                           // 会话数据
    'params' => $activeToolInfo['data']['params']    // 从工具状态中获取的参数
]
```

### 4.3 实际参数访问

在工具的 handleMessage 方法中：

```php
public function handleMessage(string $message, array $context = []): array
{
    // 正确的参数访问方式
    $params = $context['params'] ?? [];
    
    // 访问 payload 数据
    $payload = $params['payload'] ?? [];
    $replyAction = $payload['reply_action'] ?? null;
    $replyData = $payload['reply_data'] ?? [];
    
    // 访问具体的替换数据
    $sourceText = $replyData['sourceText'] ?? null;
    $targetText = $replyData['targetText'] ?? null;
}
```

## 5. 问题分析

### 5.1 当前问题

在 ArticleContentReplacerTool 中，代码尝试从错误的路径获取参数：

```php
// 错误的访问方式
$contextParams = $context['params'] ?? [];
if (isset($contextParams['payload']['reply_action']) && $contextParams['payload']['reply_action'] === 'QUICK_REPLACE') {
    $replyData = $contextParams['payload']['reply_data'] ?? [];
}
```

### 5.2 正确的访问方式

```php
// 正确的访问方式
$params = $context['params'] ?? [];
$payload = $params['payload'] ?? [];

if (isset($payload['reply_action']) && $payload['reply_action'] === 'QUICK_REPLACE') {
    $replyData = $payload['reply_data'] ?? [];
    $sourceText = $replyData['sourceText'] ?? null;
    $targetText = $replyData['targetText'] ?? null;
}
```

## 6. 完整的数据流图

```
Frontend (chatBot.vue)
    ↓ HTTP POST /ai/chat
    ↓ payload: { reply_id, reply_action, reply_text, reply_data }
    
ChatController.php
    ↓ $params = ['action' => 'quick_reply', 'payload' => $payload, ...]
    ↓ $aiAgent->chat($sessionId, $message, $params)
    
AIAgent.php
    ↓ activateTool($toolName, $session, $params)
    ↓ $tool->handleMessage('', ['session' => $session, 'params' => $params])
    
ArticleContentReplacerTool.php
    ↓ $context['params']['payload']['reply_data']['sourceText']
    ↓ $context['params']['payload']['reply_data']['targetText']
```

## 7. 修复方案

### 7.1 修复工具中的参数访问

```php
public function handleMessage(string $message, array $context = []): array
{
    $params = $context['params'] ?? [];
    $payload = $params['payload'] ?? [];
    
    // 检查快捷替换请求
    if (isset($payload['reply_action']) && $payload['reply_action'] === 'QUICK_REPLACE') {
        $replyData = $payload['reply_data'] ?? [];
        if (isset($replyData['sourceText']) && isset($replyData['targetText'])) {
            return $this->handleQuickReplaceWithSearch(
                $replyData['sourceText'], 
                $replyData['targetText']
            );
        }
    }
}
```

## 8. 测试验证

使用 `dd($context)` 在工具的 handleMessage 方法开头，可以查看完整的 context 结构，验证参数传递是否正确。

## 9. 建议

1. **统一参数访问模式**：在所有工具中使用相同的参数访问方式
2. **添加调试日志**：在关键节点添加日志，便于追踪参数传递
3. **类型定义**：为参数结构添加明确的类型定义
4. **文档维护**：保持此文档与代码同步更新