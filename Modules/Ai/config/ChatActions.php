<?php

namespace Modules\Ai\Config;

/**
 * 聊天机器人统一Action定义
 * 
 * 这个类定义了前后端通用的action常量，确保前后端action名称一致
 */
class ChatActions
{
    // ==================== 基础操作 ====================
    
    /** 初始化聊天 */
    public const INIT_CHAT = 'init_chat';
    
    /** 文本消息 */
    public const TEXT_MESSAGE = 'text_message';
    
    /** 快捷回复 */
    public const QUICK_REPLY = 'quick_reply';
    
    // ==================== 工具激活操作 ====================
    
    /** 激活文本替换工具 */
    public const SELECT_TEXT_REPLACER = 'text_replacer';
    
    /** 激活SEO分析工具 */
    public const SELECT_SEO_ANALYZER = 'seo_analyzer';
    
    /** 激活文章替换工具 */
    public const SELECT_ARTICLE_REPLACER = 'article_replacer';
    
    // ==================== 文章替换工具专用操作 ====================
    
    /** 快速替换 */
    public const QUICK_REPLACE = 'QUICK_REPLACE';
    
    /** 选择文章 */
    public const SELECT_ARTICLE = 'SELECT_ARTICLE';
    
    /** 选择全部文章 */
    public const SELECT_ALL_ARTICLES = 'SELECT_ALL_ARTICLES';
    
    /** 预览结果 */
    public const PREVIEW_RESULTS = 'PREVIEW_RESULTS';
    
    /** 确认执行 */
    public const CONFIRM_EXECUTE = 'CONFIRM_EXECUTE';
    
    /** 执行替换 */
    public const EXECUTE_REPLACEMENT = 'EXECUTE_REPLACEMENT';
    
    /** 选择模块 */
    public const SELECT_MODULE = 'SELECT_MODULE';
    
    /** 取消操作 */
    public const CANCEL_OPERATION = 'CANCEL_OPERATION';
    
    /** 新的替换操作 */
    public const NEW_REPLACEMENT = 'NEW_REPLACEMENT';
    
    /** 返回主菜单 */
    public const BACK_TO_MAIN = 'BACK_TO_MAIN';
    
    /** 重试搜索 */
    public const RETRY_SEARCH = 'RETRY_SEARCH';
    
    /** 查看详情 */
    public const VIEW_DETAILS = 'VIEW_DETAILS';
    
    /** 批量替换所有文章 */
    public const BATCH_REPLACE_ALL = 'BATCH_REPLACE_ALL';
    
    /** 选择单独文章进行替换 */
    public const SELECT_INDIVIDUAL_ARTICLES = 'SELECT_INDIVIDUAL_ARTICLES';
    
    /** 返回搜索步骤 */
    public const BACK_TO_SEARCH = 'BACK_TO_SEARCH';
    
    /** 显示更多文章 */
    public const SHOW_MORE_ARTICLES = 'SHOW_MORE_ARTICLES';
    
    /** 搜索特定文章 */
    public const SEARCH_SPECIFIC_ARTICLE = 'SEARCH_SPECIFIC_ARTICLE';
    
    /** 返回文章列表 */
    public const BACK_TO_ARTICLE_LIST = 'BACK_TO_ARTICLE_LIST';
    
    /** 执行搜索 */
    public const EXECUTE_SEARCH = 'EXECUTE_SEARCH';
    
    /** 取消搜索 */
    public const CANCEL_SEARCH = 'CANCEL_SEARCH';
    
    // ==================== 通用工具操作 ====================
    
    /** 显示帮助 */
    public const SHOW_HELP = 'SHOW_HELP';
    
    /** 显示示例 */
    public const SHOW_EXAMPLES = 'SHOW_EXAMPLES';
    
    /** 退出工具 */
    public const EXIT_TOOL = 'EXIT_TOOL';
    
    /** 重试操作 */
    public const RETRY = 'RETRY';
    
    // ==================== 工具映射 ====================
    
    /**
     * action到工具名称的映射
     */
    public const TOOL_MAPPING = [
        self::SELECT_TEXT_REPLACER => 'text_replacer',
        self::SELECT_SEO_ANALYZER => 'seo_analysis', 
        self::SELECT_ARTICLE_REPLACER => 'article_content_replacer'
    ];
    
    /**
     * 快捷操作action列表
     */
    public const QUICK_ACTIONS = [
        self::SELECT_TEXT_REPLACER,
        self::SELECT_SEO_ANALYZER,
        self::SELECT_ARTICLE_REPLACER
    ];
    
    // ==================== 辅助方法 ====================
    
    /**
     * 检查是否是快捷操作
     */
    public static function isQuickAction(string $action): bool
    {
        return in_array($action, self::QUICK_ACTIONS);
    }
    
    /**
     * 获取action对应的工具名称
     */
    public static function getToolName(string $action): ?string
    {
        return self::TOOL_MAPPING[$action] ?? null;
    }
    
    /**
     * 获取所有工具激活action
     */
    public static function getToolActivationActions(): array
    {
        return [
            self::SELECT_TEXT_REPLACER,
            self::SELECT_SEO_ANALYZER,
            self::SELECT_ARTICLE_REPLACER
        ];
    }
    
    /**
     * 获取文章替换工具的所有action
     */
    public static function getArticleReplacerActions(): array
    {
        return [
            self::QUICK_REPLACE,
            self::SELECT_ARTICLE,
            self::SELECT_ALL_ARTICLES,
            self::PREVIEW_RESULTS,
            self::CONFIRM_EXECUTE,
            self::EXECUTE_REPLACEMENT,
            self::SELECT_MODULE,
            self::CANCEL_OPERATION,
            self::NEW_REPLACEMENT,
            self::BACK_TO_MAIN,
            self::RETRY_SEARCH,
            self::VIEW_DETAILS,
            self::SHOW_HELP,
            self::SHOW_EXAMPLES,
            self::EXIT_TOOL,
            self::RETRY
        ];
    }
    
    /**
     * 生成前端JavaScript常量定义
     */
    public static function generateJavaScriptConstants(): string
    {
        $constants = [];
        
        $reflection = new \ReflectionClass(self::class);
        $classConstants = $reflection->getConstants();
        
        foreach ($classConstants as $name => $value) {
            if (is_string($value) && !str_starts_with($name, 'TOOL_MAPPING') && !str_starts_with($name, 'QUICK_ACTIONS')) {
                $constants[] = "  {$name}: '{$value}'";
            }
        }
        
        return "export const ChatActions = {\n" . implode(",\n", $constants) . "\n}";
    }
} 