<?php

namespace Modules\Appointment\Models;

use Bingo\Base\BingoModel as Model;


class AppointmentEmailTemplate extends Model
{
    protected $table = 'appointment_email_templates';

    protected $fillable = [
        'id', 'name', 'code', 'subject', 'content', 'attachments', 'status', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

    protected $casts = [
        'attachments' => 'array',
    ];

    /**
     * 预约模板变量
     */
    /**
     * 预约邮件模板可用变量列表
     * 
     * 这些变量可以在邮件模板中使用，系统会自动替换为实际值
     * 
     * @var array
     */
    public const APPOINTMENT_TEMPLATE_VARIABLES = [
        '{customer_name}' => '客户姓名',
        '{service_name}' => '服务名称',
        '{appointment_date}' => '预约日期',
        '{appointment_time}' => '预约时间',
        '{end_time}' => '结束时间',
        '{location}' => '服务地点',
        '{price}' => '服务价格',
        '{final_price}' => '最终价格（含折扣）',
        '{discount_amount}' => '折扣金额',
        '{discount_name}' => '折扣名称',
        '{staff_name}' => '服务人员姓名',
        '{customer_email}' => '客户邮箱',
        '{customer_phone}' => '客户电话',
        '{appointment_notes}' => '预约备注',
        '{appointment_status}' => '预约状态',
        '{payment_status}' => '支付状态',
        '{old_appointment_date}' => '原预约日期（仅改期时有效）',
        '{old_appointment_time}' => '原预约时间（仅改期时有效）',
        '{company_name}' => '公司名称',
        '{company_address}' => '公司地址',
        '{company_phone}' => '公司电话',
        '{current_year}' => '当前年份',
        '{appointment_id}' => '预约编号',
        '{service_duration}' => '服务时长（分钟）',
    ];

    /**
     * 设置预约邮件模板变量
     * 
     * @param AppointmentRecords $appointment 预约记录对象
     * @return array 填充后的变量数组
     */
    public static function setAppointmentTemplateVariables(AppointmentRecords $appointment): array
    {
        // 创建变量副本，避免修改类常量
        $variables = self::APPOINTMENT_TEMPLATE_VARIABLES;
        
        // 基本预约信息
        $variables['{customer_name}'] = $appointment->customer?->name ?? '';
        $variables['{service_name}'] = $appointment->service?->name ?? '';
        $variables['{appointment_date}'] = date('Y-m-d', strtotime($appointment->appointment_date));
        $variables['{appointment_time}'] = date('H:i', strtotime($appointment->appointment_date));
        $variables['{end_time}'] = date('H:i', strtotime($appointment->end_time));
        $variables['{location}'] = $appointment->location ?? '';
        $variables['{price}'] = number_format((float)$appointment->original_price, 2);
        $variables['{final_price}'] = number_format((float)$appointment->final_price, 2);
        $variables['{discount_amount}'] = number_format((float)$appointment->discount_amount, 2);
        $variables['{discount_name}'] = $appointment->discount?->name ?? '';
        $variables['{staff_name}'] = $appointment->service?->staff?->name ?? '';
        $variables['{customer_email}'] = $appointment->customer?->email ?? '';
        $variables['{customer_phone}'] = $appointment->customer?->phone ?? '';
        $variables['{appointment_notes}'] = $appointment->remark ?? '';
        $variables['{appointment_status}'] = $appointment->getStatusText();
        $variables['{payment_status}'] = $appointment->getPaymentStatusText();
        $variables['{appointment_id}'] = (string)$appointment->id;
        $variables['{service_duration}'] = (string)($appointment->getDuration() ? floor($appointment->getDuration() / 60) : 0);
        
        // 公司相关信息
        $variables['{company_name}'] = config('app.name', '');
        $variables['{company_address}'] = config('app.address', '');
        $variables['{company_phone}'] = config('app.phone', '');
        $variables['{current_year}'] = date('Y');
        
        // 改期相关（需要上下文才能填充）
        $variables['{old_appointment_date}'] = $variables['{appointment_date}'];
        $variables['{old_appointment_time}'] = $variables['{appointment_time}'];
        
        return $variables;
    }

}
