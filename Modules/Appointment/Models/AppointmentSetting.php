<?php

namespace Modules\Appointment\Models;

use Bingo\Base\BingoModel as Model;


class AppointmentSetting extends Model
{
    protected $table = 'appointment_settings';

    protected $fillable = [
        'id', 'key', 'value', 'description', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

    /**
     * 预约限制类型
     */
    // 预约限制键名
    public const KEY_APPOINTMENT_LIMIT = 'appointment_limit';
    // 无限制
    public const VALUE_APPOINTMENT_LIMIT_NO_LIMIT = 'no_limit';
    // 仅会员可预约
    public const VALUE_APPOINTMENT_LIMIT_MEMBER_ONLY = 'member_only';

    /**
     * 非会员预约记录
     */
    // 非会员预约记录键名
    public const KEY_NON_MEMBER_RECORD = 'non_member_record';
    // 保存非会员预约记录
    public const VALUE_NON_MEMBER_RECORD_SAVE = 1;
    // 不保存非会员预约记录
    public const VALUE_NON_MEMBER_RECORD_NOT_SAVE = 0;

    /**
     * 时段粒度(分钟)
     */
    // 时段粒度键名
    public const KEY_TIME_SLOT_INTERVAL = 'time_slot_interval';
    // 5分钟
    public const VALUE_TIME_SLOT_INTERVAL_5 = 5;
    // 10分钟
    public const VALUE_TIME_SLOT_INTERVAL_10 = 10;
    // 15分钟
    public const VALUE_TIME_SLOT_INTERVAL_15 = 15;
    // 30分钟
    public const VALUE_TIME_SLOT_INTERVAL_30 = 30;
    // 60分钟
    public const VALUE_TIME_SLOT_INTERVAL_60 = 60;

    /**
     * 跨时段预约设置
     */
    // 支持跨时段预约键名
    public const KEY_CROSS_TIME_SLOT = 'cross_time_slot';
    // 支持跨时段预约
    public const VALUE_CROSS_TIME_SLOT_YES = 1;
    // 不支持跨时段预约
    public const VALUE_CROSS_TIME_SLOT_NO = 0;

    /**
     * 提前预定时间(小时)
     */
    public const KEY_ADVANCE_BOOKING_TIME = 'advance_booking_time';

    /**
     * 预约天数限制
     */
    public const KEY_MAX_BOOKING_DAYS = 'max_booking_days';

    /**
     * 默认预约状态
     */
    // 默认预约状态键名
    public const KEY_DEFAULT_APPOINTMENT_STATUS = 'default_appointment_status';
    // 待处理状态
    public const VALUE_APPOINTMENT_STATUS_PENDING = 0; // 状态：0-待确认，1-已确认，2-已改期，3-已完成，4-已取消，5-已拒绝，6-未到

    /**
     * 默认支付状态
     */
    // 默认支付状态键名
    public const KEY_DEFAULT_PAYMENT_STATUS = 'default_payment_status';
    // 待支付状态
    public const VALUE_PAYMENT_STATUS_PENDING = 0; //支付状态：0-待支付，1-已支付，2-支付失败

    /**
     * 允许超时预约
     */
    // 允许超时预约键名
    public const KEY_ALLOW_OVERTIME_BOOKING = 'allow_overtime_booking';
    // 允许超时预约
    public const VALUE_ALLOW_OVERTIME_BOOKING_YES = 1;
    // 不允许超时预约
    public const VALUE_ALLOW_OVERTIME_BOOKING_NO = 0;

    /**
     * 允许管理员在非工作时间预约
     */
    // 允许管理员在非工作时间预约键名
    public const KEY_ALLOW_ADMIN_OVERTIME_BOOKING = 'allow_admin_overtime_booking';
    // 允许管理员在非工作时间预约
    public const VALUE_ALLOW_ADMIN_OVERTIME_BOOKING_YES = 1;
    // 不允许管理员在非工作时间预约
    public const VALUE_ALLOW_ADMIN_OVERTIME_BOOKING_NO = 0;


    /** 
     * 预约通知
     */
    // 预约改期通知键名
    public const KEY_APPOINTMENT_RESCHEDULE_NOTIFICATION = 'appointment_reschedule_notification';
    // 预约取消通知键名
    public const KEY_APPOINTMENT_CANCEL_NOTIFICATION = 'appointment_cancel_notification';
    // 预约确认通知键名
    public const KEY_APPOINTMENT_CONFIRM_NOTIFICATION = 'appointment_confirm_notification';

    /**
     * 假期设置
     */
    // 假期设置键名
    public const KEY_HOLIDAY_SETTINGS = 'holiday_settings';

    /**
     * 获取系统设置值
     *
     * @param string $key 设置键
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function getSetting(string $key, $default = null)
    {
        $setting = self::where('key', $key)->first();
        return $setting ? $setting->value : $default;
    }
}
