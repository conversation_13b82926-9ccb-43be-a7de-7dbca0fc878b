<?php

namespace Modules\Appointment\Models;

use Bingo\Base\BingoModel as Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


class AppointmentRecords extends Model
{
    protected $table = 'appointment_records';

    protected $fillable = [
        'id',
        'customer_id',
        'service_id',
        'location',
        'appointment_date',
        'end_time',
        'status',
        'discount_id',
        'original_price',
        'discount_amount',
        'final_price',
        'payment_method',
        'payment_status',
        'payment_time',
        'remark',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];
   

    /**
     * 获取预约持续时间（秒）
     * 
     * @return int|null 持续时间（秒），如果时间无效则返回null
     */
    public function getDuration(): ?int
    {
        // 检查预约时间和结束时间是否存在
        if (empty($this->appointment_date) || empty($this->end_time)) {
            return null;
        }
        
        try {
            // 解析预约开始时间
            $startDateTime = new \DateTime($this->appointment_date);
            
            // 解析结束时间
            $endDateTime = new \DateTime($this->end_time);
            
            // 计算时间差（秒）
            $interval = $endDateTime->getTimestamp() - $startDateTime->getTimestamp();
            
            // 转换为秒并返回
            return (int)$interval;
        } catch (\Exception $e) {
            // 日期解析错误时返回null
            return null;
        }
    }

    /**
     * 预约状态常量
     */
    // 待确认
    public const STATUS_PENDING = 0;
    // 已确认
    public const STATUS_CONFIRMED = 1;
    // 已改期
    public const STATUS_RESCHEDULED = 2;
    // 已完成
    public const STATUS_COMPLETED = 3;
    // 已取消
    public const STATUS_CANCELLED = 4;
    // 已拒绝
    public const STATUS_REJECTED = 5;
    // 未到
    public const STATUS_NO_SHOW = 6;

    /**
     * 预约状态映射
     *
     * @var array
     */
    public static array $statusMap = [
        self::STATUS_PENDING => '待确认',
        self::STATUS_CONFIRMED => '已确认',
        self::STATUS_RESCHEDULED => '已改期',
        self::STATUS_COMPLETED => '已完成',
        self::STATUS_CANCELLED => '已取消',
        self::STATUS_REJECTED => '已拒绝',
        self::STATUS_NO_SHOW => '未到',
    ];

    /** 
     * 获取预约状态文本
     *
     * @return string
     */
    public function getStatusText(): string
    {
        return self::$statusMap[$this->status] ?? '未知状态';
    }
    
    /**
     * 获取预约状态文本
     *
     * @param int $status 状态
     * @return string
     */
    public static function getStaticStatusText(int $status): string
    {
        return self::$statusMap[$status] ?? '未知状态';
    }

    /**
     * 检查状态是否有效
     *
     * @param int $status 状态
     * @return bool
     */
    public static function isValidStatus(int $status): bool
    {
        return in_array($status, array_keys(self::$statusMap));
    }

    /**
     * 支付状态常量
     */
    // 待支付
    public const PAYMENT_STATUS_PENDING = 0;
    // 已支付
    public const PAYMENT_STATUS_PAID = 1;
    // 支付失败
    public const PAYMENT_STATUS_FAILED = 2;

    /**
     * 支付状态映射
     *
     * @var array
     */
    public static array $paymentStatusMap = [
        self::PAYMENT_STATUS_PENDING => '待支付',
        self::PAYMENT_STATUS_PAID => '已支付',
        self::PAYMENT_STATUS_FAILED => '支付失败',
    ];  

    /**
     * 获取支付状态文本
     *
     * @return string
     */
    public function getPaymentStatusText(): string
    {
        return self::$paymentStatusMap[$this->payment_status] ?? '未知支付状态';
    }
    
    /**
     * 获取关联的服务
     *
     * @return BelongsTo
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(AppointmentService::class, 'service_id');
    }

    /**
     * 获取关联的客户
     *
     * @return BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(AppointmentCustomer::class, 'customer_id');
    }

    /**
     * 获取关联的折扣
     *
     * @return BelongsTo
     */
    public function discount(): BelongsTo
    {
        return $this->belongsTo(AppointmentDiscount::class, 'discount_id');
    }

    /**
     * 设置默认预约状态
     */
    public function setDefaultStatus(): void
    {
        if (empty($this->status)) {
            $this->status = AppointmentSetting::getSetting(AppointmentSetting::KEY_DEFAULT_APPOINTMENT_STATUS, AppointmentRecords::STATUS_PENDING);
        }
    }

    /**
     * 设置默认支付状态
     */
    public function setDefaultPaymentStatus(): void
    {
        if (empty($this->payment_status)) {
            $this->payment_status = AppointmentSetting::getSetting(AppointmentSetting::KEY_DEFAULT_PAYMENT_STATUS, AppointmentRecords::PAYMENT_STATUS_PENDING);
        }
    }
}
