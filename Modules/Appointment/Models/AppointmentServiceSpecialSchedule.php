<?php

namespace Modules\Appointment\Models;

use Bingo\Base\BingoModel as Model;


class AppointmentServiceSpecialSchedule extends Model
{
    protected $table = 'appointment_service_special_schedules';

    protected $fillable = [
        'id', 'service_id', 'start_time', 'end_time', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

    /**
     * 设置开始时间
     * 
     * @param string|mixed $value 开始时间
     * @return void
     */
    public function setStartTimeAttribute($value): void
    {
        // 由于start_time是datetime类型，直接存储字符串格式的日期时间
        // 如果传入的不是字符串，尝试转换为字符串格式
        if (!is_string($value) && !is_null($value)) {
            $value = date('Y-m-d H:i:s', $value);
        }
        $this->attributes['start_time'] = $value;
    }

    /**
     * 设置结束时间
     * 
     * @param string|mixed $value 结束时间
     * @return void
     */
    public function setEndTimeAttribute($value): void
    {
        // 由于end_time是datetime类型，直接存储字符串格式的日期时间
        // 如果传入的不是字符串，尝试转换为字符串格式
        if (!is_string($value) && !is_null($value)) {
            $value = date('Y-m-d H:i:s', $value);
        }
        $this->attributes['end_time'] = $value;
    }

    /**
     * 获取开始时间
     * 
     * @param string $value 开始时间
     * @return string
     */
    public function getStartTimeAttribute($value): string
    {
        // 由于数据库中已经是datetime类型，直接返回即可
        return $value ?: '';
    }

    /**
     * 获取结束时间
     * 
     * @param string $value 结束时间
     * @return string
     */
    public function getEndTimeAttribute($value): string
    {
        // 由于数据库中已经是datetime类型，直接返回即可
        return $value ?: '';
    }
}
