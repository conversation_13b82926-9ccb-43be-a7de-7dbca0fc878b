<?php

namespace Modules\Appointment\Models;

use Bingo\Base\BingoModel as Model;
use Modules\Iam\Models\IamUsers;
use Modules\Iam\Models\IamUserDepartments;

class AppointmentServiceStaff extends Model
{
    protected $table = 'appointment_service_staff';

    protected $fillable = [
        'id', 'service_id', 'staff_id','creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

    // 定义追加到模型数组中的访问器
    protected $appends = [
        'email',
        'phone', 
        'name',
        'photo',
        'gender',
        'birthdate',
        'department_id',
        'department_name',
        'department_description'
    ];

    // 定义在序列化为JSON时应该隐藏的属性
    protected $hidden = [
        'user',
        'userDepartments'
    ];

    /**
     * 获取用户邮箱
     */
    public function getEmailAttribute()
    {
        return $this->user?->email;
    }

    /**
     * 获取用户手机号
     */
    public function getPhoneAttribute()
    {
        return $this->user?->phone;
    }

    /**
     * 获取用户姓名
     */
    public function getNameAttribute()
    {
        return $this->user?->name;
    }

    /**
     * 获取用户头像
     */
    public function getPhotoAttribute()
    {
        return $this->user?->photo;
    }

    /**
     * 获取用户性别
     */
    public function getGenderAttribute()
    {
        return $this->user?->gender;
    }

    /**
     * 获取用户生日
     */
    public function getBirthdateAttribute()
    {
        return $this->user?->birthdate;
    }

    /**
     * 获取用户部门名称
     */
    public function getDepartmentNameAttribute()
    {
        // 获取用户的主部门名称，并预加载department关系
        $mainDepartment = $this->userDepartments()
            ->with('department')
            ->where('is_main_department', 1)
            ->first();
            
        if ($mainDepartment && $mainDepartment->department) {
            return $mainDepartment->department->name;
        }
        
        // 如果没有主部门，获取第一个部门名称，并预加载department关系
        $firstDepartment = $this->userDepartments()
            ->with('department')
            ->first();
        
        if ($firstDepartment && $firstDepartment->department) {
            return $firstDepartment->department->name;
        }
        
        return '';
    }

    /**
     * 获取用户部门ID
     * 
     * @return int|null 部门ID
     */
    public function getDepartmentIdAttribute()
    {
        // 获取用户的主部门ID
        $mainDepartment = $this->userDepartments()
            ->where('is_main_department', 1)
            ->first();
            
        if ($mainDepartment) {
            return $mainDepartment->department_id;
        }
        
        // 如果没有主部门，获取第一个部门ID
        $firstDepartment = $this->userDepartments()
            ->first();
        
        if ($firstDepartment) {
            return $firstDepartment->department_id;
        }
        
        return null;
    }

    /**
     * 获取用户部门描述
     * 
     * @return string 部门描述
     */
    public function getDepartmentDescriptionAttribute()
    {
        // 获取用户的主部门，并预加载department关系
        $mainDepartment = $this->userDepartments()
            ->with('department')
            ->where('is_main_department', 1)
            ->first();
            
        if ($mainDepartment && $mainDepartment->department) {
            return $mainDepartment->department->description ?? '';
        }
        
        // 如果没有主部门，获取第一个部门描述
        $firstDepartment = $this->userDepartments()
            ->with('department')
            ->first();
        
        if ($firstDepartment && $firstDepartment->department) {
            return $firstDepartment->department->description ?? '';
        }
        
        return '';
    }

    public function service()
    {
        return $this->belongsTo(AppointmentService::class, 'service_id');
    }

    public function user()
    {
        return $this->belongsTo(IamUsers::class, 'staff_id');
    }
    
    /**
     * 获取员工的所有部门关联
     */
    public function userDepartments()
    {
        return $this->hasMany(IamUserDepartments::class, 'user_id', 'staff_id');
    }
}
