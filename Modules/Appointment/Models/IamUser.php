<?php

namespace Modules\Appointment\Models;

use Bingo\Base\BingoModel as Model;
use Modules\Iam\Models\IamUserDepartments;
use Modules\Iam\Models\IamUserPositions;
use Modules\Iam\Models\IamUserPreferences;

/**
 * IAM用户模型
 */
class IamUser extends Model
{
    /**
     * 指定数据表名
     */
    protected $table = 'iam_users';

    //默认密码
    public const DEFAULT_PASSWORD = '123456';

    /**
     * 可填充的字段
     */
    protected $fillable = [
        'id',
        'email',
        'phone',
        'status',
        'name',
        'photo',
        'password',
        'gender',
        'birthdate',
        'user_type',
        'application_id',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 可见的字段（限制查询时只返回这些字段）
     */
    protected $visible = [
        'id',
        'email',
        'phone',
        'status',
        'name',
        'photo',
        'gender',
        'birthdate',
        'user_type',
        'application_id',
        'creator_id',
        'created_at',
        'updated_at',
        'department_id',
        'department_name',
        'department_description',
        'position_id',
        'position_name',
        'position_description',
        'description',
    ];

    /**
     * 追加到模型数组的访问器
     */
    protected $appends = [
        'department_id',
        'department_name',
        'department_description',
        'position_id',
        'position_name',
        'position_description',
        'description',
    ];

    // 定义在序列化为JSON时应该隐藏的属性
    protected $hidden = [
        'department',
        'position',
    ];

    /**
     * 获取关联的预约客户信息
     */
    public function appointments()
    {
        return $this->hasMany(AppointmentRecords::class, 'customer_id');
    }

    /**
     * 获取用户部门名称
     */
    public function getDepartmentNameAttribute()
    {
        // 获取用户的主部门名称，并预加载department关系
        $department = $this->department()
            ->with('department')
            ->where('application_id', $this->application_id)
            ->first();
            
        if ($department && $department->department) {
            return $department->department->name;
        }
       
        return '';
    }

    /**
     * 获取用户部门ID
     * 
     * @return int|null 部门ID
     */
    public function getDepartmentIdAttribute()
    {
        // 获取用户的主部门ID
        $department = $this->department()
            ->where('application_id', $this->application_id)
            ->first();
            
        if ($department) {
            return $department->department_id;
        }
        
        return null;
    }

    /**
     * 获取用户部门描述
     * 
     * @return string 部门描述
     */
    public function getDepartmentDescriptionAttribute()
    {
        // 获取用户的主部门，并预加载department关系
        $department = $this->department()
            ->with('department')
            ->where('application_id', $this->application_id)
            ->first();
            
        if ($department && $department->department) {
            return $department->department->description ?? '';
        }
        
        return '';
    }

    /**
     * 获取员工的所有部门关联
     */
    public function department()
    {
        return $this->hasOne(IamUserDepartments::class, 'user_id');
    }

    /**
     * 获取员工的所有职位关联
     */
    public function position()  
    {
        return $this->hasOne(IamUserPositions::class, 'user_id');
    }   

    /**
     * 获取用户职位ID
     * 
     * @return int|null 职位ID
     */
    public function getPositionIdAttribute()
    {
        // 获取用户的主职位ID
        $position = $this->position()
            ->first();
            
        if ($position) {
            return $position->position_id;
        }
        
        return null;
    }

    /**
     * 获取用户职位名称
     * 
     * @return string 职位名称
     */
    public function getPositionNameAttribute()
    {
        // 获取用户的主职位，并预加载position关系
        $position = $this->position()
            ->with('position')
            ->first();
            
        if ($position && $position->position) {
            return $position->position->position_name ?? '';
        }
        
        return '';
    }

    /**
     * 获取用户职位描述
     * 
     * @return string 职位描述
     */
    public function getPositionDescriptionAttribute()
    {
        // 获取用户的主职位，并预加载position关系
        $position = $this->position()
            ->with('position')
            ->first();
            
        if ($position && $position->position) {
            return $position->position->description ?? '';
        }
        
        return '';
    }

    /**
     * 应用类型常量
     */
    const APPLICATION_FRONTEND = 1; // 前端应用
    const APPLICATION_BACKEND = 2;  // 后台应用
    
    /**
     * 获取应用类型名称
     * 
     * @param int $applicationType 应用类型ID
     * @return string 应用类型名称
     */
    public static function getApplicationTypeName($applicationType)
    {
        $types = [
            self::APPLICATION_FRONTEND => '前端',
            self::APPLICATION_BACKEND => '后台',
        ];
        
        return $types[$applicationType] ?? '';
    }
    
    /**
     * 判断用户是否属于前端应用
     * 
     * @return bool
     */
    public function isFrontendUser()
    {
        return $this->application_id === self::APPLICATION_FRONTEND;
    }
    
    /**
     * 判断用户是否属于后台应用
     * 
     * @return bool
     */
    public function isBackendUser()
    {
        return $this->application_id === self::APPLICATION_BACKEND;
    }

    /**
     * 用户类型常量
     */
    const USER_TYPE_CUSTOMER = 8; // 客户类型
    const USER_TYPE_EMPLOYEE = 9; // 员工类型
    
    /**
     * 获取用户类型名称
     * 
     * @param int $userType 用户类型ID
     * @return string 用户类型名称
     */
    public static function getUserTypeName($userType)
    {
        $types = [
            self::USER_TYPE_CUSTOMER => '客户',
            self::USER_TYPE_EMPLOYEE => '员工',
        ];
        
        return $types[$userType] ?? '';
    }
    
    /**
     * 判断用户是否为客户
     * 
     * @return bool
     */
    public function isCustomer()
    {
        return $this->user_type === self::USER_TYPE_CUSTOMER;
    }
    
    /**
     * 判断用户是否为员工
     * 
     * @return bool
     */
    public function isEmployee()
    {
        return $this->user_type === self::USER_TYPE_EMPLOYEE;
    }
    /**
     * 用户状态常量
     */
    const STATUS_ACTIVATED = 'Activated';    // 已激活
    const STATUS_SUSPENDED = 'Suspended';    // 已暂停
    const STATUS_DEACTIVATED = 'Deactivated'; // 已停用
    const STATUS_RESIGNED = 'Resigned';      // 已离职
    const STATUS_ARCHIVED = 'Archived';      // 已归档
    
    /**
     * 获取用户状态名称
     * 
     * @param string $status 状态值
     * @return string 状态名称
     */
    public static function getStatusName($status)
    {
        $statusMap = [
            self::STATUS_ACTIVATED => '已激活',
            self::STATUS_SUSPENDED => '已暂停',
            self::STATUS_DEACTIVATED => '已停用',
            self::STATUS_RESIGNED => '已离职',
            self::STATUS_ARCHIVED => '已归档',
        ];
        
        return $statusMap[$status] ?? '';
    }
    
    /**
     * 判断用户是否已激活
     * 
     * @return bool
     */
    public function isActivated()
    {
        return $this->status === self::STATUS_ACTIVATED;
    }
    
    /**
     * 判断用户是否已暂停
     * 
     * @return bool
     */
    public function isSuspended()
    {
        return $this->status === self::STATUS_SUSPENDED;
    }
    
    /**
     * 判断用户是否已停用
     * 
     * @return bool
     */
    public function isDeactivated()
    {
        return $this->status === self::STATUS_DEACTIVATED;
    }
    
    /**
     * 判断用户是否已离职
     * 
     * @return bool
     */
    public function isResigned()
    {
        return $this->status === self::STATUS_RESIGNED;
    }
    
    /**
     * 判断用户是否已归档
     * 
     * @return bool
     */
    public function isArchived()
    {
        return $this->status === self::STATUS_ARCHIVED;
    }
    
    /**
     * 根据id判断是否时员工
     * 
     * @param int $id 用户ID
     * @return bool
     */
    public static function isEmployeeInIds(array $ids)
    {
        return self::whereIn('id', $ids)
        ->where('application_id', self::APPLICATION_BACKEND)
        ->where('status', self::STATUS_ACTIVATED)
        ->where('user_type', self::USER_TYPE_EMPLOYEE)->exists();
    }

    public const NAMESPACE_APPOINTMENT = 'appointment';
    public const GROUP_STAFF = 'staff';
    public const GROUP_CUSTOMER = 'customer';

    /**
     * 获取用户偏好
     */
    public function preferences()
    {
        return $this->hasMany(IamUserPreferences::class, 'user_id');
    }

    /**
     * 获取用户偏好
     * 
     * @param string $key 偏好键
     * @return mixed 偏好值
     */
    protected function getIamUserPreference($key, $applicationId, $namespace, $group)
    {
        return $this->preferences()
        ->where('application_id', $applicationId)
        ->where('namespace', $namespace)
        ->where('group', $group)
        ->where('item', $key)->first();
    }

    /**
     * 设置员工偏好
     */
    public function setStaffPreference($key, $value, $creatorId)
    {
        $this->setIamUserPreference($key, $value, self::APPLICATION_BACKEND, self::NAMESPACE_APPOINTMENT, self::GROUP_STAFF, $creatorId);
    }

    /**
     * 设置客户偏好
     */
    public function setCustomerPreference($key, $value, $creatorId)
    {
        $this->setIamUserPreference($key, $value, self::APPLICATION_FRONTEND, self::NAMESPACE_APPOINTMENT, self::GROUP_CUSTOMER, $creatorId);
    }
    
    /**
     * 获取客户偏好
     */
    public function getCustomerPreference($key)
    {
        return $this->getIamUserPreference($key, self::APPLICATION_FRONTEND, self::NAMESPACE_APPOINTMENT, self::GROUP_CUSTOMER);
    }

    /**
     * 获取员工偏好
     */
    public function getStaffPreference($key)
    {
        return $this->getIamUserPreference($key, self::APPLICATION_BACKEND, self::NAMESPACE_APPOINTMENT, self::GROUP_STAFF);
    }

    /**
     * 设置用户偏好
     * 
     * @param string $key 偏好键
     * @param mixed $value 偏好值
     * @param int $creatorId 创建者ID
     */
    protected function setIamUserPreference($key, $value, $applicationId, $namespace, $group, $creatorId)
    {
        // 查找是否已存在该偏好设置
        $preference = $this->preferences()
            ->where('item', $key)
            ->where('application_id', $applicationId)
            ->where('namespace', $namespace)
            ->where('group', $group)
            ->first();
            
        if ($preference) {
            // 如果存在则更新
            $preference->update([
                'value' => $value,
                'creator_id' => $creatorId,
            ]);
        } else {
            // 不存在则创建
            $this->preferences()->create([
                'item' => $key,
                'value' => $value,
                'application_id' => $applicationId,
                'namespace' => $namespace,
                'group' => $group,
                'user_id' => $this->id,
                'creator_id' => $creatorId,
            ]);
        }
    }

    /**
     * 性别常量
     */
    const GENDER_UNKNOWN = 0;  // 未知
    const GENDER_MALE = 1;     // 男性
    const GENDER_FEMALE = 2;   // 女性

    /**
     * 获取性别名称
     * 
     * @param int $gender 性别值
     * @return string 性别名称
     */
    public static function getGenderName($gender)
    {
        $genderMap = [
            self::GENDER_UNKNOWN => '未知',
            self::GENDER_MALE => '男性',
            self::GENDER_FEMALE => '女性',
        ];
        
        return $genderMap[$gender] ?? '未知';
    }

    /**
     * 获取性别值对应的数据库枚举值
     * 
     * @param int $gender 性别值
     * @return string 数据库中的性别枚举值
     */
    public static function getGenderEnum($gender)
    {
        $genderMap = [
            self::GENDER_UNKNOWN => 'U',
            self::GENDER_MALE => 'M',
            self::GENDER_FEMALE => 'F',
        ];
        
        return $genderMap[$gender] ?? 'U';
    }

    /**
     * 设置性别值的修改器
     * 
     * @param mixed $value
     * @return void
     */
    public function setGenderAttribute($value)
    {
        $this->attributes['gender'] = self::getGenderEnum($value);
    }

    /**
     * 获取性别值的访问器
     * 
     * @param string $value
     * @return int
     */
    public function getGenderAttribute($value)
    {
        $genderMap = [
            'U' => self::GENDER_UNKNOWN,
            'M' => self::GENDER_MALE,
            'F' => self::GENDER_FEMALE,
        ];
        
        return $genderMap[$value] ?? self::GENDER_UNKNOWN;
    }

    /**
     * 获取描述
     */
    public function getDescriptionAttribute()   
    {
        if ($this->isCustomer()) {
            return $this->getCustomerPreference('description')->value ?? '';
        }
        
        return $this->getStaffPreference('description')->value ?? '';
    }
}
