<?php

namespace Modules\Appointment\Models;

/**
 * 预约客户模型
 * 继承自IamUser模型，专门用于处理预约客户相关的功能
 */
class AppointmentCustomer extends IamUser
{
    /**
     * 构造函数：设置默认值
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        
        // 设置默认的应用类型为前端应用
        $this->attributes['application_id'] = self::APPLICATION_FRONTEND;
        // 设置默认的用户类型为客户
        $this->attributes['user_type'] = self::USER_TYPE_CUSTOMER;
    }

    /**
     * 追加到模型数组的访问器
     */
    protected $appends = [
        'appointment_count',
        'latest_appointment_time',
        'completed_appointments',
        'cancelled_appointments',
        'pending_appointments',
        'has_ongoing_appointment',
        'formatted_latest_appointment',
        'description'
    ];

    /**
     * 限定查询范围：只查询客户类型的用户
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOnlyCustomers($query)
    {
        return $query->where('application_id', self::APPLICATION_FRONTEND);
    }

    /**
     * 获取该客户的所有预约记录
     */
    public function appointmentRecords()
    {
        return $this->hasMany(AppointmentRecords::class, 'customer_id');
    }

    /**
     * 获取该客户的最近一次预约记录
     */
    public function latestAppointment()
    {
        return $this->hasOne(AppointmentRecords::class, 'customer_id')
                    ->latest('created_at');
    }

    /**
     * 判断客户是否有进行中的预约
     *
     * @return bool
     */
    public function hasOngoingAppointment()
    {
        return $this->appointmentRecords()
                    ->whereIn('status', ['pending', 'confirmed'])
                    ->exists();
    }

    /**
     * 获取客户的预约统计信息
     *
     * @return array
     */
    public function getAppointmentStats()
    {
        return [
            'total' => $this->appointmentRecords()->count(),
            'completed' => $this->appointmentRecords()->where('status', 'completed')->count(),
            'cancelled' => $this->appointmentRecords()->where('status', 'cancelled')->count(),
            'pending' => $this->appointmentRecords()->whereIn('status', ['pending', 'confirmed'])->count()
        ];
    }

    /**
     * 重写父类的boot方法，添加创建和更新时的默认行为
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            // 如果没有设置密码，使用默认密码
            if (empty($model->password)) {
                $model->password = password_hash(self::DEFAULT_PASSWORD, PASSWORD_DEFAULT);
            }
            // 如果没有设置状态，默认设置为激活状态
            if (empty($model->status)) {
                $model->status = self::STATUS_ACTIVATED;
            }
        });
    }

    /**
     * 可填充的字段
     */
    protected $fillable = [
        'id',
        'name',
        'phone',
        'email',
        'gender',
        'birthdate',
        'photo',
        'user_type',
        'application_id',
        'status',
        'creator_id',
        'created_at',
        'updated_at',
    ];

    /**
     * 可见的字段（限制查询时只返回这些字段）
     */
    protected $visible = [
        'id',
        'name',
        'phone',
        'email',
        'gender',
        'birthdate',
        'photo',
        'status',
        'creator_id',
        'created_at',
        'updated_at',
        'appointment_count',
        'latest_appointment_time',
        'description',
    ];

    /**
     * 获取预约总数
     */
    public function getAppointmentCountAttribute()
    {
        return $this->appointmentRecords()->count();
    }

    /**
     * 获取最近预约时间
     */
    public function getLatestAppointmentTimeAttribute()
    {
        $latest = $this->latestAppointment;
        return $latest ? $latest->created_at->format('Y-m-d H:i:s') : null;
    }

    public function service()
    {
        return $this->hasOne(AppointmentService::class, 'service_id');
    }
} 