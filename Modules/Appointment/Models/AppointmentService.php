<?php

namespace Modules\Appointment\Models;

use Bingo\Base\BingoModel as Model;


class AppointmentService extends Model
{
    protected $table = 'appointment_services';

    /**
     * 可填充字段
     * 
     * @var array
     */
    protected $fillable = [
        'id',
        'category_id',
        'name',
        'price',
        'duration',
        'is_show_price',
        'is_show_duration',
        'is_repeat_service',
        'is_repeat_service_pay_type',
        'is_repeat_service_category',
        'is_repeat_service_count',
        'person_count',
        'description',
        'pay_types',
        'advance_booking_time',
        'only_member_visible',
        'status',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 自动类型转换
     * 
     * @var array
     */
    protected $casts = [
        'pay_types' => 'array', // 将pay_types字段在存储时自动转为JSON，获取时自动转为数组
    ];

    /**
     * 追加到模型数组的访问器
     *
     * @var array
     */
    protected $appends = ['category_name'];

    /**
     * 获取分类名称
     *
     * @return string|null
     */
    protected function getCategoryNameAttribute()
    {
        return AppointmentServiceCategory::where('id', $this->category_id)->value('name');
    }

    /**
     * 服务状态常量
     */
    public const STATUS_CLOSED = 0; // 关闭状态
    public const STATUS_OPEN = 1;   // 开放状态

    /**
     * 获取服务状态文本
     *
     * @return string
     */
    public function getStatusText(): string
    {
        return match($this->status) {
            self::STATUS_CLOSED => '关闭',
            self::STATUS_OPEN => '开放',
            default => '未知状态',
        };
    }

    /**
     * 判断服务是否开放
     *
     * @return bool
     */
    public function isOpen(): bool
    {
        return $this->status === self::STATUS_OPEN;
    }

    /**
     * 判断服务是否关闭
     *
     * @return bool
     */
    public function isClosed(): bool
    {
        return $this->status === self::STATUS_CLOSED;
    }

    public function staffs()
    {
        return $this->hasMany(AppointmentServiceStaff::class, 'service_id');
    }

    public function category()
    {
        return $this->belongsTo(AppointmentServiceCategory::class, 'category_id');
    }

    /**
     * 重复服务收费类型常量
     */
    public const REPEAT_PAY_TYPE_PER_TIME = 1;    // 按次收费
    public const REPEAT_PAY_TYPE_PER_CYCLE = 2;   // 按重复周期收费

    /**
     * 重复服务类型常量
     */
    public const REPEAT_CATEGORY_DAILY = 1;       // 按天重复
    public const REPEAT_CATEGORY_WEEKLY = 2;      // 按周重复
    public const REPEAT_CATEGORY_MONTHLY = 3;     // 按月重复

    /**
     * 设置默认持续时间
     * 
     * @return void
     */
    public function setDefaultDuration(): void
    {
        if (empty($this->duration)) {
            // 获取系统配置的时段粒度作为默认值（单位：分钟）
            $defaultDuration = AppointmentSetting::getSetting(AppointmentSetting::KEY_TIME_SLOT_INTERVAL, AppointmentSetting::VALUE_TIME_SLOT_INTERVAL_30);
            $this->duration = (int)$defaultDuration * 60;
        }
    }

    /**
     * 获取重复服务收费类型文本
     *
     * @return string
     */
    public function getRepeatPayTypeText(): string
    {
        return match($this->is_repeat_service_pay_type) {
            self::REPEAT_PAY_TYPE_PER_TIME => '按次收费',
            self::REPEAT_PAY_TYPE_PER_CYCLE => '按重复周期收费',
            default => '未知收费类型',
        };
    }

    /**
     * 获取重复服务类型文本
     *
     * @return string
     */
    public function getRepeatCategoryText(): string
    {
        return match($this->is_repeat_service_category) {
            self::REPEAT_CATEGORY_DAILY => '按天重复',
            self::REPEAT_CATEGORY_WEEKLY => '按周重复',
            self::REPEAT_CATEGORY_MONTHLY => '按月重复',
            default => '未知重复类型',
        };
    }

    public function schedules()
    {
        return $this->hasMany(AppointmentServiceSchedule::class, 'service_id');
    }

    public function specialSchedules()  
    {
        return $this->hasMany(AppointmentServiceSpecialSchedule::class, 'service_id');
    }

    public function extraServices() 
    {
        return $this->hasMany(AppointmentExtraServiceRelations::class, 'service_id');
    }

    
    
}
