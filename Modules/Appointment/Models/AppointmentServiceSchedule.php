<?php

namespace Modules\Appointment\Models;

use Bingo\Base\BingoModel as Model;


class AppointmentServiceSchedule extends Model
{
    protected $table = 'appointment_service_schedules';

    protected $fillable = [
        'id',
        'service_id',
        'day_of_week',
        'start_time',
        'end_time',
        'rest_time_list',
        'is_rest_day',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    

    /**
     * 预处理批量插入数据
     *
     * @param array $data 要插入的数据数组
     * @return array 处理后的数据
     */
    public static function prepareForInsert(array $data): array
    {
        // 处理单条记录
        if (isset($data['rest_time_list']) && is_array($data['rest_time_list'])) {
            $data['rest_time_list'] = json_encode($data['rest_time_list']);
        }

        // 处理多条记录的情况
        foreach ($data as $key => $item) {
            if (is_array($item) && isset($item['rest_time_list']) && is_array($item['rest_time_list'])) {
                $data[$key]['rest_time_list'] = json_encode($item['rest_time_list']);
            }
        }

        return $data;
    }

    /**
     * 重写insert方法，在插入前预处理数据
     *
     * @param array $data 要插入的数据
     * @return bool|int 插入是否成功
     */
    public static function insert(array $data)
    {
        $data = static::prepareForInsert($data);
        return parent::insert($data);
    }

    /**
     * 批量插入数据
     *
     * @param array $records 要批量插入的数据数组
     * @param int $chunkSize 每批次插入的数据量
     * @return bool 插入是否成功
     */
    public static function insertBatch(array $records, int $chunkSize = 100): bool
    {
        if (empty($records)) {
            return false;
        }

        // 预处理数据
        foreach ($records as $key => $record) {
            $records[$key] = static::prepareForInsert($record);
        }

        // 分批插入数据以优化性能
        return self::getQuery()->insert($records);
    }

    /**
     * 设置休息时间列表
     *
     * @param array|string $value 休息时间列表
     * @return void
     */
    public function setRestTimeListAttribute($value): void
    {
        // 如果传入的是数组，则转换为JSON字符串存储
        if (is_array($value)) {
            $this->attributes['rest_time_list'] = json_encode($value);
        } else {
            $this->attributes['rest_time_list'] = $value;
        }
    }

    /**
     * 获取休息时间列表
     *
     * @param string|null $value 休息时间列表JSON字符串
     * @return array
     */
    public function getRestTimeListAttribute($value): array
    {
        // 如果是空值，返回空数组
        if (empty($value)) {
            return [];
        }

        // 将JSON字符串转换为数组返回
        return is_string($value) ? json_decode($value, true) : $value;
    }

    /**
     * 获取开始时间
     *
     * @param string $value 开始时间（time类型）
     * @return string
     */
    public function getStartTimeAttribute($value): string
    {
        // 直接返回time类型的值，无需转换
        return $value ?: '';
    }

    /**
     * 获取结束时间
     *
     * @param string $value 结束时间（time类型）
     * @return string
     */
    public function getEndTimeAttribute($value): string
    {
        // 直接返回time类型的值，无需转换
        return $value ?: '';
    }

    /**
     * 休息日状态常量
     */
    public const IS_REST_DAY = 1;  // 是休息日
    public const NOT_REST_DAY = 0; // 不是休息日

    /**
     * 获取休息日状态列表
     *
     * @return array<int, string> 休息日状态列表
     */
    public static function getRestDayStatusList(): array
    {
        return [
            self::IS_REST_DAY => '是',
            self::NOT_REST_DAY => '否',
        ];
    }

    /**
     * 判断是否为休息日
     *
     * @return bool
     */
    public function isRestDay(): bool
    {
        return $this->is_rest_day === self::IS_REST_DAY;
    }

    /**
     * 设置开始时间
     *
     * @param string|int $value 开始时间值
     * @return void
     */
    public function setStartTimeAttribute($value): void
    {
        // 如果是时间戳，转换为时间格式
        if (is_numeric($value)) {
            $this->attributes['start_time'] = date('H:i:s', $value);
        } else {
            $this->attributes['start_time'] = $value;
        }
    }

    /**
     * 设置结束时间
     *
     * @param string|int $value 结束时间值
     * @return void
     */
    public function setEndTimeAttribute($value): void
    {
        // 如果是时间戳，转换为时间格式
        if (is_numeric($value)) {
            $this->attributes['end_time'] = date('H:i:s', $value);
        } else {
            $this->attributes['end_time'] = $value;
        }
    }
}
