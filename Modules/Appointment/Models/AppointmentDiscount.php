<?php

namespace Modules\Appointment\Models;

use Bingo\Base\BingoModel as Model;


class AppointmentDiscount extends Model
{
    protected $table = 'appointment_discounts';

    protected $fillable = [
        'id', 'name', 'type', 'value', 'start_date', 'end_date', 'status', 'creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

    /**
     * 折扣类型常量
     */
    // 固定金额折扣
    public const TYPE_FIXED = 'fixed';
    // 百分比折扣
    public const TYPE_PERCENTAGE = 'percentage';

    /**
     * 折扣类型映射
     *
     * @var array
     */
    public static array $typeMap = [
        self::TYPE_FIXED => '固定金额',
        self::TYPE_PERCENTAGE => '百分比',
    ];

    /**
     * 折扣状态常量
     */
    // 未启用
    public const STATUS_DISABLED = 0;
    // 已启用
    public const STATUS_ENABLED = 1;
    // 已过期
    public const STATUS_EXPIRED = 2;

    /**
     * 折扣状态映射
     *
     * @var array
     */
    public static array $statusMap = [
        self::STATUS_DISABLED => '未启用',
        self::STATUS_ENABLED => '已启用',
        self::STATUS_EXPIRED => '已过期',
    ];
}
