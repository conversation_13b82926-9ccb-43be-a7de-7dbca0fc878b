<?php

namespace Modules\Appointment\Models;

use Bingo\Base\BingoModel as Model;


class AppointmentExtraServiceRelations extends Model
{
    protected $table = 'appointment_extra_service_relations';

    protected $fillable = [
        'id', 'service_id', 'extra_service_id', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];


    /**
     * 关联服务
     */
    public function service()
    {
        return $this->belongsTo(AppointmentService::class, 'service_id');
    }

    /**
     * 关联额外服务
     */
    public function extraService()  
    {
        return $this->belongsTo(AppointmentExtraService::class, 'extra_service_id');
    }
    
}
