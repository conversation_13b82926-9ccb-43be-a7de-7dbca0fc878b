<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 员工列表请求验证
 */
class ListStaffRequest extends FormRequest
{
    /**
     * 验证规则
     */
    public function rules(): array
    {
        return [
            'page' => ['nullable', 'integer', 'min:1'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'keyword' => ['nullable', 'string', 'max:50'],
            'status' => ['nullable', 'integer', 'in:0,1'],
            'location_id' => ['nullable', 'integer', 'exists:locations,id'],
            'sort_field' => ['nullable', 'string', 'in:id,created_at,updated_at'],
            'sort_order' => ['nullable', 'string', 'in:asc,desc'],
        ];
    }

    /**
     * 验证消息
     */
    public function messages(): array
    {
        return [
            'page.integer' => T('Appointment::validation.ListStaff.Page.Integer'),
            'page.min' => T('Appointment::validation.ListStaff.Page.Min'),
            'per_page.integer' => T('Appointment::validation.ListStaff.PerPage.Integer'),
            'per_page.min' => T('Appointment::validation.ListStaff.PerPage.Min'),
            'per_page.max' => T('Appointment::validation.ListStaff.PerPage.Max'),
            'keyword.string' => T('Appointment::validation.ListStaff.Keyword.String'),
            'keyword.max' => T('Appointment::validation.ListStaff.Keyword.Max'),
            'status.integer' => T('Appointment::validation.ListStaff.Status.Integer'),
            'status.in' => T('Appointment::validation.ListStaff.Status.In'),
            'location_id.integer' => T('Appointment::validation.ListStaff.LocationId.Integer'),
            'location_id.exists' => T('Appointment::validation.ListStaff.LocationId.Exists'),
            'sort_field.string' => T('Appointment::validation.ListStaff.SortField.String'),
            'sort_field.in' => T('Appointment::validation.ListStaff.SortField.In'),
            'sort_order.string' => T('Appointment::validation.ListStaff.SortOrder.String'),
            'sort_order.in' => T('Appointment::validation.ListStaff.SortOrder.In'),
        ];
    }

    /**
     * 授权验证
     */
    public function authorize(): bool
    {
        return true;
    }
} 