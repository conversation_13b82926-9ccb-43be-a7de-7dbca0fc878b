<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Appointment\Enums\ErrorCode;

/**
 * 导入员工请求
 */
class ImportStaffRequest extends FormRequest
{
    /**
     * 获取适用于请求的验证规则
     *
     * @return array
     */
    public function rules()
    {
        return [
            'file' => 'required|file|mimes:xlsx,xls,csv|max:10240',
        ];
    }

    /**
     * 确定用户是否有权提出此请求
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * 获取已定义验证规则的错误消息
     *
     * @return array
     */
    public function messages()
    {
        return [
            'file.required' => T('Appointment::validation.import_staff.file.required'),
            'file.file' => T('Appointment::validation.import_staff.file.file'),
            'file.mimes' => T('Appointment::validation.import_staff.file.mimes'),
            'file.max' => T('Appointment::validation.import_staff.file.max'),
        ];
    }
} 