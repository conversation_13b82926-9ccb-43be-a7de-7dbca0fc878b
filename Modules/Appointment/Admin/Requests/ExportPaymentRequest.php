<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 导出付款请求验证类
 */
class ExportPaymentRequest extends FormRequest
{
    /**
     * 判断用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取验证规则
     *
     * @return array<string, array<int, string>>
     */
    public function rules(): array
    {
        return [
            'appointment_no' => ['nullable', 'string', 'max:50'],
            'customer_name' => ['nullable', 'string', 'max:50'],
            'payment_status' => ['nullable', 'integer'],
            'start_date' => ['nullable', 'date'],
            'end_date' => ['nullable', 'date', 'after_or_equal:start_date'],
        ];
    }

    /**
     * 获取验证错误消息
     */
    public function messages(): array
    {
        return [
            'appointment_no.max' => T('Appointment::validation.ExportPayment.AppointmentNo.Max'),
            'customer_name.max' => T('Appointment::validation.ExportPayment.CustomerName.Max'),
            'payment_status.integer' => T('Appointment::validation.ExportPayment.PaymentStatus.Integer'),
            'start_date.date' => T('Appointment::validation.ExportPayment.StartDate.Date'),
            'end_date.date' => T('Appointment::validation.ExportPayment.EndDate.Date'),
            'end_date.after_or_equal' => T('Appointment::validation.ExportPayment.EndDate.AfterOrEqual'),
        ];
    }
} 