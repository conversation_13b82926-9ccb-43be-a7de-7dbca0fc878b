<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * 预约列表请求验证
 */
class ListAppointmentRequest extends FormRequest
{
    /**
     * 确定用户是否有权发出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'page' => ['nullable', 'integer', 'min:1'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
            'keyword' => ['nullable', 'string', 'max:100'],
            'customer_id' => ['nullable', 'integer'],
            'status' => ['nullable', 'string'],
            'sort_field' => ['nullable', 'string', Rule::in(['id', 'appointment_date', 'created_at', 'updated_at'])],
            'sort_order' => ['nullable', 'string', Rule::in(['asc', 'desc'])],
            'start_date' => ['nullable', 'date'],
            'end_date' => ['nullable', 'date'],
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     */
    public function messages(): array
    {
        return [
            'page.integer' => T('Appointment::validation.ListAppointment.Page.Integer'), 
            'page.min' => T('Appointment::validation.ListAppointment.Page.Min'),
            'limit.integer' => T('Appointment::validation.ListAppointment.Limit.Integer'),
            'limit.min' => T('Appointment::validation.ListAppointment.Limit.Min'),
            'limit.max' => T('Appointment::validation.ListAppointment.Limit.Max'),
            'keyword.string' => T('Appointment::validation.ListAppointment.Keyword.String'),
            'keyword.max' => T('Appointment::validation.ListAppointment.Keyword.Max'),
            'customer_id.integer' => T('Appointment::validation.ListAppointment.CustomerId.Integer'),
            'status.string' => T('Appointment::validation.ListAppointment.Status.String'),
            'sort_field.string' => T('Appointment::validation.ListAppointment.SortField.String'),              
            'sort_field.in' => T('Appointment::validation.ListAppointment.SortField.In'),
            'sort_order.string' => T('Appointment::validation.ListAppointment.SortOrder.String'),  
            'sort_order.in' => T('Appointment::validation.ListAppointment.SortOrder.In'),
            'start_date.date' => T('Appointment::validation.ListAppointment.StartDate.Date'),
            'end_date.date' => T('Appointment::validation.ListAppointment.EndDate.Date'),
        ];
    }
} 