<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 服务列表请求验证
 */
class ListServiceRequest extends FormRequest
{
    /**
     * 获取验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'keyword' => ['nullable', 'string', 'max:50'],
            'name' => ['nullable', 'string', 'max:50'],
            'category_id' => ['nullable', 'integer', 'exists:appointment_service_categories,id'],
            'status' => ['nullable', 'boolean'],
            'is_visible' => ['nullable', 'boolean'],
            'page' => ['nullable', 'integer', 'min:1'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.max' => T('Appointment::validation.ListService.Name.Max'),
            'category_id.integer' => T('Appointment::validation.ListService.CategoryId.Integer'),
            'category_id.exists' => T('Appointment::validation.ListService.CategoryId.Exists'),
            'status.boolean' => T('Appointment::validation.ListService.Status.Boolean'),
            'is_visible.boolean' => T('Appointment::validation.ListService.IsVisible.Boolean'),
            'page.integer' => T('Appointment::validation.ListService.Page.Integer'),
            'page.min' => T('Appointment::validation.ListService.Page.Min'),
            'limit.integer' => T('Appointment::validation.ListService.Limit.Integer'),
            'limit.min' => T('Appointment::validation.ListService.Limit.Min'),
            'limit.max' => T('Appointment::validation.ListService.Limit.Max'),
            'keyword.string' => T('Appointment::validation.ListService.Keyword.String'),
            'keyword.max' => T('Appointment::validation.ListService.Keyword.Max'),
        ];
    }
} 