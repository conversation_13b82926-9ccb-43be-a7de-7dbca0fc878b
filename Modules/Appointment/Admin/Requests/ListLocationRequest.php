<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 地点列表请求验证
 */
class ListLocationRequest extends FormRequest
{
    /**
     * 获取验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'nullable', 'string', 'max:50'],
            'page' => ['sometimes', 'nullable', 'integer', 'min:1'],
            'limit' => ['sometimes', 'nullable', 'integer', 'min:1', 'max:100'],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.max' => T('Appointment::validation.ListLocation.Name.Max'),
            'page.integer' => T('Appointment::validation.ListLocation.Page.Integer'),
            'page.min' => T('Appointment::validation.ListLocation.Page.Min'),
            'limit.integer' => T('Appointment::validation.ListLocation.Limit.Integer'),
            'limit.min' => T('Appointment::validation.ListLocation.Limit.Min'),
            'limit.max' => T('Appointment::validation.ListLocation.Limit.Max'),
        ];
    }
} 