<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 导入预约请求验证
 */
class ImportAppointmentRequest extends FormRequest
{
    /**
     * 确定用户是否有权发出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'file' => ['required', 'file', 'mimes:xlsx,xls,csv', 'max:10240'],
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     */
    public function messages(): array
    {
        return [
            'file.required' => T('Appointment::validation.ImportAppointment.File.Required'),
            'file.file' => T('Appointment::validation.ImportAppointment.File.File'),
            'file.mimes' => T('Appointment::validation.ImportAppointment.File.Mimes'),
            'file.max' => T('Appointment::validation.ImportAppointment.File.Max'),
        ];
    }
} 