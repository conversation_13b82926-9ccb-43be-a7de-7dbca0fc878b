<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 服务分类列表请求验证
 */
class ListServiceCategoryRequest extends FormRequest
{
    /**
     * 获取验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => ['nullable', 'string', 'max:50'],
            'status' => ['nullable', 'boolean'],
            'page' => ['nullable', 'integer', 'min:1'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.max' => T('Appointment::validation.ListServiceCategory.Name.Max'),
            'status.boolean' => T('Appointment::validation.ListServiceCategory.Status.Boolean'),
            'page.integer' => T('Appointment::validation.ListServiceCategory.Page.Integer'),
            'page.min' => T('Appointment::validation.ListServiceCategory.Page.Min'),
            'limit.integer' => T('Appointment::validation.ListServiceCategory.Limit.Integer'),
            'limit.min' => T('Appointment::validation.ListServiceCategory.Limit.Min'),
            'limit.max' => T('Appointment::validation.ListServiceCategory.Limit.Max'),
        ];
    }
} 