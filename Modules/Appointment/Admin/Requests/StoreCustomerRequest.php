<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 保存客户请求验证
 */
class StoreCustomerRequest extends FormRequest
{
    /**
     * 判断用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取验证规则
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:50',
            'email' => 'required|email|max:100',
            'phone' => 'nullable|string|max:20',
            'gender' => 'nullable|integer|in:0,1,2', // 0:未知 1:男 2:女
            'birthdate' => 'nullable|date',
            'description' => 'nullable|string|max:500',
            'photo' => 'nullable|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'email.required' => T('Appointment::validation.StoreCustomer.Email.Required'),
            'email.email' => T('Appointment::validation.StoreCustomer.Email.Email'),
            'phone.required' => T('Appointment::validation.StoreCustomer.Phone.Required'),
            'phone.string' => T('Appointment::validation.StoreCustomer.Phone.String'),
            'phone.max' => T('Appointment::validation.StoreCustomer.Phone.Max'),
            'gender.required' => T('Appointment::validation.StoreCustomer.Gender.Required'),
            'gender.integer' => T('Appointment::validation.StoreCustomer.Gender.Integer'),
            'gender.in' => T('Appointment::validation.StoreCustomer.Gender.In'),
            'birthdate.date' => T('Appointment::validation.StoreCustomer.Birthdate.Date'),
            'description.string' => T('Appointment::validation.StoreCustomer.Description.String'),
            'description.max' => T('Appointment::validation.StoreCustomer.Description.Max'),
            'name.required' => T('Appointment::validation.StoreCustomer.Name.Required'),
            'name.string' => T('Appointment::validation.StoreCustomer.Name.String'),
            'name.max' => T('Appointment::validation.StoreCustomer.Name.Max'),
            'photo.string' => T('Appointment::validation.StoreCustomer.Photo.String'),
            'photo.max' => T('Appointment::validation.StoreCustomer.Photo.Max'),
        ];
    }
} 