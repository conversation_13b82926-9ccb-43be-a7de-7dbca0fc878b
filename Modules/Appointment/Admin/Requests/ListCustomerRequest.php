<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 客户列表请求验证
 */
class ListCustomerRequest extends FormRequest
{
    /**
     * 判断用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取验证规则
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
            'keyword' => 'nullable|string|max:50',
        ];
    }

    public function messages(): array
    {
        return [
            'limit.max' => T('Appointment::validation.ListCustomer.Limit.Max'),
            'page.min' => T('Appointment::validation.ListCustomer.Page.Min'),
            'keyword.max' => T('Appointment::validation.ListCustomer.Keyword.Max'),
            'limit.min' => T('Appointment::validation.ListCustomer.Limit.Min'),
            'page.integer' => T('Appointment::validation.ListCustomer.Page.Integer'),
            'limit.integer' => T('Appointment::validation.ListCustomer.Limit.Integer'),
        ];
    }
} 