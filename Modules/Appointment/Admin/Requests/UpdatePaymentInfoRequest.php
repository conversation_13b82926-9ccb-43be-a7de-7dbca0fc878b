<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 更新预约付款信息请求
 */
class UpdatePaymentInfoRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'payment_method' => 'nullable|string|max:50', // 支付方式
            'final_price' => 'nullable|numeric|min:0', // 支付金额
            'payment_status' => 'nullable|integer|in:0,1,2', // 支付状态：0未支付，1已支付，2支付失败
            'discount_id' => 'nullable|numeric|min:0', // 折扣ID
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'payment_method.string' => T('Appointment::validation.update_payment_info.payment_method.string'),
            'payment_method.max' => T('Appointment::validation.update_payment_info.payment_method.max'),
            'final_price.numeric' => T('Appointment::validation.update_payment_info.final_price.numeric'),
            'final_price.min' => T('Appointment::validation.update_payment_info.final_price.min'),  
            'payment_status.integer' => T('Appointment::validation.update_payment_info.payment_status.integer'),  
            'payment_status.in' => T('Appointment::validation.update_payment_info.payment_status.in'),  
            'discount_id.numeric' => T('Appointment::validation.update_payment_info.discount_id.numeric'),  
            'discount_id.min' => T('Appointment::validation.update_payment_info.discount_id.min'),  
        ];
    }
} 