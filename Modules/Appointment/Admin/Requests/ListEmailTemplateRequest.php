<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 邮件模板列表请求验证
 */
class ListEmailTemplateRequest extends FormRequest
{
    /**
     * 确定用户是否有权提出此请求
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'keyword' => 'nullable|string|max:50',
            'name' => 'nullable|string|max:100',
            'code' => 'nullable|string|max:50',
            'status' => 'nullable|integer|in:0,1',
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
            'sort_field' => 'nullable|string|in:id,name,code,sort,created_at,updated_at',
            'sort_order' => 'nullable|string|in:asc,desc',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.string' => T('Appointment::validation.list_email_template.name.string'),
            'name.max' => T('Appointment::validation.list_email_template.name.max'),
            'code.string' => T('Appointment::validation.list_email_template.code.string'),
            'code.max' => T('Appointment::validation.list_email_template.code.max'),
            'status.integer' => T('Appointment::validation.list_email_template.status.integer'),
            'status.in' => T('Appointment::validation.list_email_template.status.in'),
            'page.integer' => T('Appointment::validation.list_email_template.page.integer'),
            'page.min' => T('Appointment::validation.list_email_template.page.min'),
            'limit.integer' => T('Appointment::validation.list_email_template.limit.integer'),
            'limit.min' => T('Appointment::validation.list_email_template.limit.min'),
            'limit.max' => T('Appointment::validation.list_email_template.limit.max'),
            'sort_field.string' => T('Appointment::validation.list_email_template.sort_field.string'),
            'sort_field.in' => T('Appointment::validation.list_email_template.sort_field.in'),
            'sort_order.string' => T('Appointment::validation.list_email_template.sort_order.string'),
            'sort_order.in' => T('Appointment::validation.list_email_template.sort_order.in'),
            'keyword.string' => T('Appointment::validation.list_email_template.keyword.string'),
            'keyword.max' => T('Appointment::validation.list_email_template.keyword.max'),
        ];
    }
}
