<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 保存服务分类请求验证
 */
class StoreServiceCategoryRequest extends FormRequest
{
    /**
     * 获取验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:50'],
            'description' => ['nullable', 'string', 'max:500'],
            'sort' => ['nullable', 'integer', 'min:0'],
            'status' => ['required', 'boolean'],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => T('Appointment::validation.StoreServiceCategory.Name.Required'),
            'name.max' => T('Appointment::validation.StoreServiceCategory.Name.Max'),
            'description.max' => T('Appointment::validation.StoreServiceCategory.Description.Max'),
            'sort.integer' => T('Appointment::validation.StoreServiceCategory.Sort.Integer'),
            'sort.min' => T('Appointment::validation.StoreServiceCategory.Sort.Min'),
            'status.required' => T('Appointment::validation.StoreServiceCategory.Status.Required'),
            'status.boolean' => T('Appointment::validation.StoreServiceCategory.Status.Boolean'),
        ];
    }
} 