<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 导入客户数据请求验证
 */
class ImportCustomerRequest extends FormRequest
{
    /**
     * 判断用户是否有权限进行此请求
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取验证规则
     * @return array
     */
    public function rules(): array
    {
        $maxSize = config('appointment.import.max_file_size', 5120); // 默认5MB
        $allowedTypes = config('appointment.import.allowed_types', ['xlsx', 'xls', 'csv']);
        
        return [
            'file' => [
                'required',
                'file',
                'mimes:' . implode(',', $allowedTypes),
                'max:' . $maxSize, // 单位：KB
            ],
        ];
    }

    /**
     * 获取验证错误的自定义消息
     * @return array
     */
    public function messages(): array
    {
        $maxSize = config('appointment.import.max_file_size', 5120);
        $allowedTypes = config('appointment.import.allowed_types', ['xlsx', 'xls', 'csv']);
        
        return [
            'file.required' => T('Appointment::validation.ImportCustomer.File.Required'),
            'file.file' => T('Appointment::validation.ImportCustomer.File.File'),
            'file.mimes' => T('Appointment::validation.ImportCustomer.File.Mimes').' '.implode(', ', $allowedTypes),
            'file.max' => T('Appointment::validation.ImportCustomer.File.Max').' '.($maxSize / 1024).'MB',
        ];
    }
} 