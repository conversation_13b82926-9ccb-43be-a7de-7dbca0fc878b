<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 保存服务特殊排班请求验证
 */
class SaveServiceSpecialSchedulesRequest extends FormRequest
{
    /**
     * 获取验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'date' => ['required', 'date_format:Y-m-d'],
            'schedules' => ['required', 'array'],
            'schedules.*.start_time' => ['required', 'date_format:H:i'],
            'schedules.*.end_time' => ['required', 'date_format:H:i', 'after:schedules.*.start_time'],
            'schedules.*.is_rest_day' => ['nullable', 'boolean'],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'date.required' => T('Appointment::validation.SaveServiceSpecialSchedules.Date.Required'),
            'date.date_format' => T('Appointment::validation.SaveServiceSpecialSchedules.Date.DateFormat'),
            'schedules.required' => T('Appointment::validation.SaveServiceSpecialSchedules.Schedules.Required'),
            'schedules.array' => T('Appointment::validation.SaveServiceSpecialSchedules.Schedules.Array'),
            'schedules.*.start_time.required' => T('Appointment::validation.SaveServiceSpecialSchedules.Schedules.StartTime.Required'),
            'schedules.*.start_time.date_format' => T('Appointment::validation.SaveServiceSpecialSchedules.Schedules.StartTime.DateFormat'),
            'schedules.*.end_time.required' => T('Appointment::validation.SaveServiceSpecialSchedules.Schedules.EndTime.Required'),
            'schedules.*.end_time.date_format' => T('Appointment::validation.SaveServiceSpecialSchedules.Schedules.EndTime.DateFormat'),
            'schedules.*.end_time.after' => T('Appointment::validation.SaveServiceSpecialSchedules.Schedules.EndTime.After'),
            'schedules.*.is_rest_day.boolean' => T('Appointment::validation.SaveServiceSpecialSchedules.Schedules.IsRestDay.Boolean'),
        ];
    }
} 