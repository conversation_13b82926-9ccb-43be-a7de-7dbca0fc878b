<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 更新地点请求验证
 */
class UpdateLocationRequest extends FormRequest
{
    /**
     * 获取验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'nullable', 'string', 'max:50'],
            'address' => ['sometimes', 'nullable', 'string', 'max:255'], 
            'description' => ['sometimes', 'nullable', 'string', 'max:500'],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.max' => T('Appointment::validation.UpdateLocation.Name.Max'),    
            'address.max' => T('Appointment::validation.UpdateLocation.Address.Max'),   
            'description.max' => T('Appointment::validation.UpdateLocation.Description.Max'),   
        ];
    }
} 