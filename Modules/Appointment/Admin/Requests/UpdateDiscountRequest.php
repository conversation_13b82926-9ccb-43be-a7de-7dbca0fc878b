<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Appointment\Models\AppointmentDiscount;

/**
 * 更新折扣请求验证
 */
class UpdateDiscountRequest extends FormRequest
{
    /**
     * 确定用户是否有权发出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:100'],
            'type' => ['required', 'string', 'in:' . implode(',', array_keys(AppointmentDiscount::$typeMap))],
            'value' => ['required', 'numeric', 'min:0', 'decimal:0,2'],
            'start_date' => ['nullable', 'date'],
            'end_date' => ['nullable', 'date', 'after_or_equal:start_date'],
        ];
    }

    /**
     * 自定义验证消息
     */
    public function messages(): array
    {
        return [
            'name.required' => T('Appointment::validation.UpdateDiscount.Name.Required'),
            'name.string' => T('Appointment::validation.UpdateDiscount.Name.String'),
            'name.max' => T('Appointment::validation.UpdateDiscount.Name.Max'),
            'name.unique' => T('Appointment::validation.UpdateDiscount.Name.Unique'),
            'code.max' => T('Appointment::validation.UpdateDiscount.Code.Max'),
            'code.unique' => T('Appointment::validation.UpdateDiscount.Code.Unique'),
            'type.required' => T('Appointment::validation.UpdateDiscount.Type.Required'),
            'type.string' => T('Appointment::validation.UpdateDiscount.Type.String'),
            'type.in' => T('Appointment::validation.UpdateDiscount.Type.In'),
            'value.required' => T('Appointment::validation.UpdateDiscount.Value.Required'),
            'value.numeric' => T('Appointment::validation.UpdateDiscount.Value.Numeric'),
            'value.min' => T('Appointment::validation.UpdateDiscount.Value.Min'),
            'value.decimal' => T('Appointment::validation.UpdateDiscount.Value.Decimal'),
            'start_date.date' => T('Appointment::validation.UpdateDiscount.StartDate.Date'),
            'end_date.date' => T('Appointment::validation.UpdateDiscount.EndDate.Date'),
            'end_date.after_or_equal' => T('Appointment::validation.UpdateDiscount.EndDate.AfterOrEqual'),
        ];
    }
} 