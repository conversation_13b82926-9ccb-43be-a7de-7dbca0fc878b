<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * 更新员工请求验证
 */
class UpdateStaffRequest extends FormRequest
{
    /**
     * 验证规则
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:50'],
            'email' => ['required', 'email'],
            'phone' => ['nullable', 'string', 'max:20'],
            'position_id' => ['nullable', 'integer'],
            'department_id' => ['nullable', 'integer'],
            'location' => ['nullable', 'string', 'max:50'],
            'description' => ['nullable', 'string', 'max:255'],
            'photo' => ['nullable', 'string', 'max:255'],
        ];
    }

    /**
     * 验证消息
     */
    public function messages(): array
    {
        return [
            'name.required' => T('Appointment::validation.UpdateStaff.Name.Required'),
            'name.string' => T('Appointment::validation.UpdateStaff.Name.String'),
            'name.max' => T('Appointment::validation.UpdateStaff.Name.Max'),
            'email.required' => T('Appointment::validation.UpdateStaff.Email.Required'),
            'email.email' => T('Appointment::validation.UpdateStaff.Email.Email'),
            'phone.string' => T('Appointment::validation.UpdateStaff.Phone.String'),
            'phone.max' => T('Appointment::validation.UpdateStaff.Phone.Max'),
            'position_id.integer' => T('Appointment::validation.UpdateStaff.PositionId.Integer'),
            'department_id.integer' => T('Appointment::validation.UpdateStaff.DepartmentId.Integer'),
            'location.string' => T('Appointment::validation.UpdateStaff.Location.String'),
            'location.max' => T('Appointment::validation.UpdateStaff.Location.Max'),
            'description.string' => T('Appointment::validation.UpdateStaff.Description.String'),
            'description.max' => T('Appointment::validation.UpdateStaff.Description.Max'),
            'photo.string' => T('Appointment::validation.UpdateStaff.Photo.String'),
            'photo.max' => T('Appointment::validation.UpdateStaff.Photo.Max'),
        ];
    }

    /**
     * 授权验证
     */
    public function authorize(): bool
    {
        return true;
    }
} 