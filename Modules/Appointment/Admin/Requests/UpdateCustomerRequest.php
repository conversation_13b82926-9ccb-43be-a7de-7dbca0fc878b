<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 更新客户请求验证
 */
class UpdateCustomerRequest extends FormRequest
{
    /**
     * 获取验证规则
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'nullable|string|max:50',
            'email' => 'nullable|email|max:100',
            'phone' => 'nullable|string|max:20',
            'gender' => 'nullable|integer|in:0,1,2',
            'birthdate' => 'nullable|date',
            'description' => 'nullable|string|max:500',
            'photo' => 'nullable|string|max:255',
        ];
    }

    /**
     * 获取验证错误消息
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.max' => T('Appointment::validation.UpdateCustomer.Name.Max'),
            'email.email' => T('Appointment::validation.UpdateCustomer.Email.Email'),
            'email.max' => T('Appointment::validation.UpdateCustomer.Email.Max'),
            'phone.max' => T('Appointment::validation.UpdateCustomer.Phone.Max'),
            'gender.in' => T('Appointment::validation.UpdateCustomer.Gender.In'),
            'birthdate.date' => T('Appointment::validation.UpdateCustomer.Birthdate.Date'),
            'description.max' => T('Appointment::validation.UpdateCustomer.Description.Max'),
            'photo.string' => T('Appointment::validation.UpdateCustomer.Photo.String'),
            'photo.max' => T('Appointment::validation.UpdateCustomer.Photo.Max'),
        ];
    }
}