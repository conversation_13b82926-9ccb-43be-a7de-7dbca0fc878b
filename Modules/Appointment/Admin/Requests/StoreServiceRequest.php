<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Appointment\Models\IamUser;
/**
 * 创建服务请求验证
 */
class StoreServiceRequest extends FormRequest
{
    /**
     * 获取验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'category_id' => ['required', 'integer', 'exists:appointment_service_categories,id'],
            'name' => ['required', 'string', 'max:100'],
            'price' => ['required', 'numeric', 'min:0'],
            'duration' => ['required', 'integer', 'min:1'],
            'is_show_price' => ['nullable', 'boolean'], // 是否显示价格
            'is_show_duration' => ['nullable', 'boolean'], // 是否显示时长
            'is_repeat_service' => ['nullable', 'boolean'], // 是否重复服务
            'is_repeat_service_pay_type' => ['nullable', 'integer', 'required_if:is_repeat_service,true'], // 重复收费方式
            'is_repeat_service_category' => ['nullable', 'integer', 'required_if:is_repeat_service,true'], // 重复类型
            'is_repeat_service_count' => ['nullable', 'integer', 'min:1', 'required_if:is_repeat_service,true'], // 固定次数
            'person_count' => ['required', 'integer', 'min:1'], // 服务人数
            'description' => ['nullable', 'string'],
            'pay_types' => ['nullable', 'array'], // 支付方式
            'advance_booking_time' => ['nullable', 'integer', 'min:0'], // 提前预约最小时间
            'only_member_visible' => ['nullable', 'boolean'], // 仅会员可见
            'status' => ['required', 'boolean'],
            'extra_services' => ['nullable', 'array'], // 额外服务
            'extra_services.*.name' => ['required', 'string', 'max:100'], // 额外服务名称
            'extra_services.*.category_name' => ['nullable', 'string', 'max:100'], // 额外服务分类名称
            'extra_services.*.price' => ['required', 'numeric', 'min:0'], // 额外服务价格
            'extra_services.*.duration' => ['required', 'integer', 'min:1'], // 额外服务时长
            'extra_services.*.max_quantity' => ['nullable', 'integer', 'min:1'], // 额外服务最大可选数量
            'extra_services.*.min_quantity' => ['nullable', 'integer', 'min:1'], // 额外服务最小可选数量
            'schedules' => ['nullable', 'array'], // 服务时间安排
            'schedules.*.day_of_week' => ['required', 'integer', 'min:1', 'max:7'], // 星期几
            'schedules.*.start_time' => ['required', 'string', 'min:0'], // 开始时间
            'schedules.*.end_time' => ['required', 'string', 'min:0'], // 结束时间
            'schedules.*.rest_time_list' => ['nullable', 'array'], // 休息时段列表
            'schedules.*.rest_time_list.*.start_time' => ['nullable', 'string'], // 休息时段开始时间
            'schedules.*.rest_time_list.*.end_time' => ['nullable', 'string'], // 休息时段结束时间
            'schedules.*.is_rest_day' => ['nullable', 'boolean'], // 是否休息日
            'special_schedules' => ['nullable', 'array'], // 特殊时间安排
            'special_schedules.*.start_time' => ['required', 'string'], // 特殊时间开始时间
            'special_schedules.*.end_time' => ['required', 'string'], // 特殊时间结束时间
            'staff_ids.*' => [
                'required', 
                'integer',
            ],
            'staff_ids' => [
                'required',
                'array',
                'min:1',
                function ($attribute, $value, $fail) {
                    // 批量验证员工ID是否存在且符合条件
                    if (!is_array($value) || empty($value)) {
                        return;
                    }
                    
                    if (!IamUser::isEmployeeInIds($value)) {
                        $fail('以下员工ID不存在或已被禁用: ' . implode(', ', $value));
                    }
                }
            ],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'category_id.required' => T('Appointment::validation.StoreService.CategoryId.Required'),
            'category_id.integer' => T('Appointment::validation.StoreService.CategoryId.Integer'),
            'category_id.exists' => T('Appointment::validation.StoreService.CategoryId.Exists'),
            'name.required' => T('Appointment::validation.StoreService.Name.Required'),
            'name.string' => T('Appointment::validation.StoreService.Name.String'),
            'name.max' => T('Appointment::validation.StoreService.Name.Max'),
            'price.required' => T('Appointment::validation.StoreService.Price.Required'),
            'price.numeric' => T('Appointment::validation.StoreService.Price.Numeric'),
            'price.min' => T('Appointment::validation.StoreService.Price.Min'),
            'duration.required' => T('Appointment::validation.StoreService.Duration.Required'),
            'duration.integer' => T('Appointment::validation.StoreService.Duration.Integer'),
            'duration.min' => T('Appointment::validation.StoreService.Duration.Min'),
            'is_repeat_service_pay_type.required_if' => T('Appointment::validation.StoreService.IsRepeatServicePayType.RequiredIf'),
            'is_repeat_service_category.required_if' => T('Appointment::validation.StoreService.IsRepeatServiceCategory.RequiredIf'),
            'is_repeat_service_count.required_if' => T('Appointment::validation.StoreService.IsRepeatServiceCount.RequiredIf'),
            'is_repeat_service_count.min' => T('Appointment::validation.StoreService.IsRepeatServiceCount.Min'),
            'person_count.required' => T('Appointment::validation.StoreService.PersonCount.Required'),
            'person_count.integer' => T('Appointment::validation.StoreService.PersonCount.Integer'),
            'person_count.min' => T('Appointment::validation.StoreService.PersonCount.Min'),
            'pay_types.required' => T('Appointment::validation.StoreService.PayTypes.Required'),
            'pay_types.array' => T('Appointment::validation.StoreService.PayTypes.Array'),
            'advance_booking_time.integer' => T('Appointment::validation.StoreService.AdvanceBookingTime.Integer'),
            'advance_booking_time.min' => T('Appointment::validation.StoreService.AdvanceBookingTime.Min'),
            'status.required' => T('Appointment::validation.StoreService.Status.Required'),
            'status.boolean' => T('Appointment::validation.StoreService.Status.Boolean'),
            'extra_services.*.name.required' => T('Appointment::validation.StoreService.ExtraServices.Name.Required'),
            'extra_services.*.name.string' => T('Appointment::validation.StoreService.ExtraServices.Name.String'),
            'extra_services.*.name.max' => T('Appointment::validation.StoreService.ExtraServices.Name.Max'),
            'extra_services.*.category_name.string' => T('Appointment::validation.StoreService.ExtraServices.CategoryName.String'),
            'extra_services.*.category_name.max' => T('Appointment::validation.StoreService.ExtraServices.CategoryName.Max'),
            'schedules.*.day_of_week.required' => T('Appointment::validation.StoreService.Schedules.DayOfWeek.Required'),
            'schedules.*.day_of_week.integer' => T('Appointment::validation.StoreService.Schedules.DayOfWeek.Integer'),
            'schedules.*.day_of_week.min' => T('Appointment::validation.StoreService.Schedules.DayOfWeek.Min'),
            'schedules.*.day_of_week.max' => T('Appointment::validation.StoreService.Schedules.DayOfWeek.Max'),
            'schedules.*.start_time.required' => T('Appointment::validation.StoreService.Schedules.StartTime.Required'),
            'schedules.*.start_time.string' => T('Appointment::validation.StoreService.Schedules.StartTime.String'),
            'schedules.*.start_time.min' => T('Appointment::validation.StoreService.Schedules.StartTime.Min'),
            'schedules.*.end_time.required' => T('Appointment::validation.StoreService.Schedules.EndTime.Required'),
            'schedules.*.end_time.string' => T('Appointment::validation.StoreService.Schedules.EndTime.String'), 
            'schedules.*.end_time.min' => T('Appointment::validation.StoreService.Schedules.EndTime.Min'),  
            'schedules.*.rest_time_list.array' => T('Appointment::validation.StoreService.Schedules.RestTimeList.Array'),
            'schedules.*.rest_time_list.*.start_time.string' => T('Appointment::validation.StoreService.Schedules.RestTimeList.StartTime.String'),
            'schedules.*.rest_time_list.*.start_time.min' => T('Appointment::validation.StoreService.Schedules.RestTimeList.StartTime.Min'),
            'schedules.*.rest_time_list.*.end_time.string' => T('Appointment::validation.StoreService.Schedules.RestTimeList.EndTime.String'),
            'schedules.*.rest_time_list.*.end_time.min' => T('Appointment::validation.StoreService.Schedules.RestTimeList.EndTime.Min'),
            'schedules.*.is_rest_day.boolean' => T('Appointment::validation.StoreService.Schedules.IsRestDay.Boolean'),
            'special_schedules.*.start_time.required' => T('Appointment::validation.StoreService.SpecialSchedules.StartTime.Required'),
            'special_schedules.*.start_time.string' => T('Appointment::validation.StoreService.SpecialSchedules.StartTime.String'),
            'special_schedules.*.start_time.min' => T('Appointment::validation.StoreService.SpecialSchedules.StartTime.Min'),
            'special_schedules.*.end_time.required' => T('Appointment::validation.StoreService.SpecialSchedules.EndTime.Required'),
            'special_schedules.*.end_time.string' => T('Appointment::validation.StoreService.SpecialSchedules.EndTime.String'),
            'special_schedules.*.end_time.min' => T('Appointment::validation.StoreService.SpecialSchedules.EndTime.Min'),
            'staff_ids.required' => T('Appointment::validation.StoreService.StaffIds.Required'),
            'staff_ids.array' => T('Appointment::validation.StoreService.StaffIds.Array'),
            'staff_ids.min' => T('Appointment::validation.StoreService.StaffIds.Min'),
            'staff_ids.*.exists' => T('Appointment::validation.StoreService.StaffIds.Exists'),
        ];
    }

    
} 