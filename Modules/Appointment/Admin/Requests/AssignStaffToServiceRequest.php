<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 分配员工到服务请求验证
 */
class AssignStaffToServiceRequest extends FormRequest
{
    /**
     * 获取验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'staff_id' => ['required', 'integer', 'exists:appointment_staff,id'],
            'is_manager' => ['nullable', 'boolean'],
            'quantity' => ['nullable', 'integer', 'min:1'],
            'ratio' => ['nullable', 'numeric', 'min:0'],
            'ratio_type' => ['nullable', 'string', 'in:fixed,percentage'],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'staff_id.required' => T('Appointment::validation.Staff.Id.Required'),
            'staff_id.integer' => T('Appointment::validation.Staff.Id.Integer'),
            'staff_id.exists' => T('Appointment::validation.Staff.Id.Exists'),
            'is_manager.boolean' => T('Appointment::validation.Staff.IsManager.Boolean'),
            'quantity.integer' => T('Appointment::validation.Staff.Quantity.Integer'),
            'quantity.min' => T('Appointment::validation.Staff.Quantity.Min'),
            'ratio.numeric' => T('Appointment::validation.Staff.Ratio.Numeric'),
            'ratio.min' => T('Appointment::validation.Staff.Ratio.Min'),
            'ratio_type.in' => T('Appointment::validation.Staff.RatioType.In'),
        ];
    }
} 