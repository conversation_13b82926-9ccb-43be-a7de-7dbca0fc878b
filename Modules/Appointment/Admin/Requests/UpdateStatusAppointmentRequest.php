<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * 更新预约状态请求验证
 */
class UpdateStatusAppointmentRequest extends FormRequest
{
    /**
     * 确定用户是否有权发出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'status' => ['required', 'string'],
            'reason' => ['nullable', 'string', 'max:500'],
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     */
    public function messages(): array
    {
        return [
            'status.required' => T('Appointment::validation.UpdateStatusAppointment.Status.Required'),
            'status.string' => T('Appointment::validation.UpdateStatusAppointment.Status.String'),
            'reason.string' => T('Appointment::validation.UpdateStatusAppointment.Reason.String'),
            'reason.max' => T('Appointment::validation.UpdateStatusAppointment.Reason.Max'),
        ];
    }
} 