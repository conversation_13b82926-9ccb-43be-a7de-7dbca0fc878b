<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 更新服务分类请求验证
 */
class UpdateServiceCategoryRequest extends FormRequest
{
    /**
     * 获取验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => ['sometimes','nullable', 'string', 'max:50'],
            'description' => ['sometimes','nullable', 'string', 'max:500'],
            'sort' => ['sometimes','nullable', 'integer', 'min:0'],
            'status' => ['sometimes','nullable', 'boolean'],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.max' => T('Appointment::validation.UpdateServiceCategory.Name.Max'),
            'description.max' => T('Appointment::validation.UpdateServiceCategory.Description.Max'),
            'sort.integer' => T('Appointment::validation.UpdateServiceCategory.Sort.Integer'),
            'sort.min' => T('Appointment::validation.UpdateServiceCategory.Sort.Min'),
            'status.boolean' => T('Appointment::validation.UpdateServiceCategory.Status.Boolean'),
        ];
    }
} 