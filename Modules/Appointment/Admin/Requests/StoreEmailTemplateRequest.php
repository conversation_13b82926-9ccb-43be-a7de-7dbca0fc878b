<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 保存邮件模板请求验证
 */
class StoreEmailTemplateRequest extends FormRequest
{
    /**
     * 确定用户是否有权提出此请求
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100',
            'code' => 'required|string|max:50',
            'subject' => 'required|string|max:200',
            'content' => 'required|string',
            'status' => 'nullable|boolean',
            'attachments' => 'nullable|array', // 附件,示例：['https://example.com/image.jpg', '/path/to/image.jpg']
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => T('Appointment::validation.store_email_template.name.required'),
            'name.string' => T('Appointment::validation.store_email_template.name.string'),
            'name.max' => T('Appointment::validation.store_email_template.name.max'),
            'code.required' => T('Appointment::validation.store_email_template.code.required'),
            'code.string' => T('Appointment::validation.store_email_template.code.string'),
            'code.max' => T('Appointment::validation.store_email_template.code.max'),
            'subject.required' => T('Appointment::validation.store_email_template.subject.required'),
            'subject.string' => T('Appointment::validation.store_email_template.subject.string'),
            'subject.max' => T('Appointment::validation.store_email_template.subject.max'),
            'content.required' => T('Appointment::validation.store_email_template.content.required'),
            'content.string' => T('Appointment::validation.store_email_template.content.string'),
            'status.boolean' => T('Appointment::validation.store_email_template.status.boolean'),
            'attachments.array' => T('Appointment::validation.store_email_template.attachments.array'),
        ];
    }
} 