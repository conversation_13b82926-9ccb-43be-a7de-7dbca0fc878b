<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use Modules\Appointment\Models\AppointmentSetting;
use Modules\Members\Domain\Repositories\MemberRepository;

/**
 * 更新预约请求验证
 */
class UpdateAppointmentRequest extends FormRequest
{
    /**
     * 确定用户是否有权发出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'location' => ['required', 'string'],
            'service_id' => ['required', 'integer'],
            'appointment_date' => ['required', 'date', 'after_or_equal:today'],
            'discount_id' => ['nullable', 'integer'],
            'remark' => ['nullable', 'string', 'max:500'],
            'customer_id' => ['required', 'integer', function ($attribute, $value, $fail) {
                // 获取预约限制设置
                $appointmentLimit = AppointmentSetting::getSetting(AppointmentSetting::KEY_APPOINTMENT_LIMIT, AppointmentSetting::VALUE_APPOINTMENT_LIMIT_NO_LIMIT);
                
                // 如果设置为仅会员可预约，则验证客户是否为会员
                if ($appointmentLimit === AppointmentSetting::VALUE_APPOINTMENT_LIMIT_MEMBER_ONLY) {
                    $customer = app(MemberRepository::class)->isMember($value);
                    if (!$customer) {
                        $fail(T('Appointment::validation.StoreAppointment.CustomerId.MemberOnly'));
                    }
                }
            }],
            'status' => ['required', 'integer'],
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     */
    public function messages(): array
    {
        return [
            'location.required' => T('Appointment::validation.StoreAppointment.Location.Required'),
            'location.string' => T('Appointment::validation.StoreAppointment.Location.String'),
            'service_id.required' => T('Appointment::validation.StoreAppointment.ServiceId.Required'),
            'service_id.integer' => T('Appointment::validation.StoreAppointment.ServiceId.Integer'),
            'appointment_date.required' => T('Appointment::validation.StoreAppointment.AppointmentDate.Required'),
            'appointment_date.datetime' => T('Appointment::validation.StoreAppointment.AppointmentDate.DateTime'),
            'appointment_date.after_or_equal' => T('Appointment::validation.StoreAppointment.AppointmentDate.AfterOrEqual'),
            'discount_id.integer' => T('Appointment::validation.StoreAppointment.DiscountId.Integer'),
            'remark.string' => T('Appointment::validation.StoreAppointment.Remark.String'),
            'remark.max' => T('Appointment::validation.StoreAppointment.Remark.Max'),
            'customer_id.required' => T('Appointment::validation.StoreAppointment.CustomerId.Required'),
            'customer_id.integer' => T('Appointment::validation.StoreAppointment.CustomerId.Integer'),
            'status.required' => T('Appointment::validation.StoreAppointment.Status.Required'),
            'status.integer' => T('Appointment::validation.StoreAppointment.Status.Integer'),
        ];
    }
} 