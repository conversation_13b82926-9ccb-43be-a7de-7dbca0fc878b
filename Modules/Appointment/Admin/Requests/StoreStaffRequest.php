<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 创建员工请求验证
 */
class StoreStaffRequest extends FormRequest
{
    /**
     * 验证规则
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:50'],
            'email' => ['required', 'email'],
            'phone' => ['nullable', 'string', 'max:20'],
            'position_id' => ['nullable', 'integer'],
            'department_id' => ['nullable', 'integer'],
            'location' => ['nullable', 'string', 'max:50'],
            'description' => ['nullable', 'string', 'max:255'],
            'photo' => ['nullable', 'string', 'max:255'],
        ];
    }

    /**
     * 验证消息
     */
    public function messages(): array
    {
        return [
            'name.required' => T('Appointment::validation.StoreStaff.Name.Required'),
            'name.string' => T('Appointment::validation.StoreStaff.Name.String'),
            'name.max' => T('Appointment::validation.StoreStaff.Name.Max'),
            'email.required' => T('Appointment::validation.StoreStaff.Email.Required'),
            'email.email' => T('Appointment::validation.StoreStaff.Email.Email'),
            'phone.string' => T('Appointment::validation.StoreStaff.Phone.String'),
            'phone.max' => T('Appointment::validation.StoreStaff.Phone.Max'),
            'position_id.integer' => T('Appointment::validation.StoreStaff.PositionId.Integer'),
            'department_id.integer' => T('Appointment::validation.StoreStaff.DepartmentId.Integer'),
            'location.string' => T('Appointment::validation.StoreStaff.Location.String'),
            'location.max' => T('Appointment::validation.StoreStaff.Location.Max'),
            'description.string' => T('Appointment::validation.StoreStaff.Description.String'),
            'description.max' => T('Appointment::validation.StoreStaff.Description.Max'),
            'photo.string' => T('Appointment::validation.StoreStaff.Photo.String'),
            'photo.max' => T('Appointment::validation.StoreStaff.Photo.Max'),
        ];
    }

    /**
     * 授权验证
     */
    public function authorize(): bool
    {
        return true;
    }
} 