<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 折扣列表请求验证
 */
class ListDiscountRequest extends FormRequest
{
    /**
     * 确定用户是否有权发出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'page' => ['nullable', 'integer', 'min:1'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
            'keyword' => ['nullable', 'string'],
            'status' => ['nullable', 'integer'],
            'type' => ['nullable', 'string'],
            'sort_field' => ['nullable', 'string', 'in:id,name,code,type,value,start_date,end_date,status,created_at'],
            'sort_order' => ['nullable', 'string', 'in:asc,desc'],
        ];
    }

    /**
     * 准备验证数据
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'page' => $this->page ?? 1,
            'limit' => $this->limit ?? 15,
            'sort_field' => $this->sort_field ?? 'id',
            'sort_order' => $this->sort_order ?? 'desc',
        ]);
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            'page.integer' => T('Appointment::validation.list_discount.page.integer'),
            'page.min' => T('Appointment::validation.list_discount.page.min'),
            'limit.integer' => T('Appointment::validation.list_discount.limit.integer'),
            'limit.min' => T('Appointment::validation.list_discount.limit.min'),
            'limit.max' => T('Appointment::validation.list_discount.limit.max'),
            'status.integer' => T('Appointment::validation.list_discount.status.integer'),
            'sort_field.in' => T('Appointment::validation.list_discount.sort_field.in'),
            'sort_order.in' => T('Appointment::validation.list_discount.sort_order.in'),
        ];
    }
} 