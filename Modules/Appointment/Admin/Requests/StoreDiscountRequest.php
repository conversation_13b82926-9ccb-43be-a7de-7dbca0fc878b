<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Appointment\Models\AppointmentDiscount;

/**
 * 保存折扣请求验证
 */
class StoreDiscountRequest extends FormRequest
{
    /**
     * 确定用户是否有权发出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:100', 'unique:appointment_discounts,name,deleted_at,0'],
            'type' => ['required', 'string', 'in:' . implode(',', array_keys(AppointmentDiscount::$typeMap))],
            'value' => ['required', 'numeric', 'min:0', 'decimal:0,2'],
            'start_date' => ['nullable', 'date'],
            'end_date' => ['nullable', 'date', 'after_or_equal:start_date'],
        ];
    }

    /**
     * 自定义验证消息
     */
    public function messages(): array
    {
        return [
            'name.required' => T('Appointment::validation.StoreDiscount.Name.Required'),
            'name.string' => T('Appointment::validation.StoreDiscount.Name.String'),
            'name.max' => T('Appointment::validation.StoreDiscount.Name.Max'),
            'name.unique' => T('Appointment::validation.StoreDiscount.Name.Unique'),
            'code.max' => T('Appointment::validation.StoreDiscount.Code.Max'),
            'code.unique' => T('Appointment::validation.StoreDiscount.Code.Unique'),
            'type.required' => T('Appointment::validation.StoreDiscount.Type.Required'),
            'type.string' => T('Appointment::validation.StoreDiscount.Type.String'),
            'type.in' => T('Appointment::validation.StoreDiscount.Type.In'),
            'value.required' => T('Appointment::validation.StoreDiscount.Value.Required'),
            'value.numeric' => T('Appointment::validation.StoreDiscount.Value.Numeric'),
            'value.min' => T('Appointment::validation.StoreDiscount.Value.Min'),
            'value.decimal' => T('Appointment::validation.StoreDiscount.Value.Decimal'),
            'start_date.date' => T('Appointment::validation.StoreDiscount.StartDate.Date'),
            'end_date.date' => T('Appointment::validation.StoreDiscount.EndDate.Date'),
            'end_date.after_or_equal' => T('Appointment::validation.StoreDiscount.EndDate.AfterOrEqual'),
        ];
    }
} 