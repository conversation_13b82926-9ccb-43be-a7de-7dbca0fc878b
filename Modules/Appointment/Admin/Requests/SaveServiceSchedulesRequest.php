<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 保存服务排班请求验证
 */
class SaveServiceSchedulesRequest extends FormRequest
{
    /**
     * 获取验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'schedules' => ['required', 'array'],
            'schedules.*.day_of_week' => ['required', 'integer', 'min:1', 'max:7'],
            'schedules.*.start_time' => ['required', 'date_format:H:i'],
            'schedules.*.end_time' => ['required', 'date_format:H:i', 'after:schedules.*.start_time'],
            'schedules.*.is_rest_day' => ['nullable', 'boolean'],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'schedules.required' => T('Appointment::validation.SaveServiceSchedules.Schedules.Required'),
            'schedules.array' => T('Appointment::validation.SaveServiceSchedules.Schedules.Array'),
            'schedules.*.day_of_week.required' => T('Appointment::validation.SaveServiceSchedules.Schedules.DayOfWeek.Required'),
            'schedules.*.day_of_week.integer' => T('Appointment::validation.SaveServiceSchedules.Schedules.DayOfWeek.Integer'),
            'schedules.*.day_of_week.min' => T('Appointment::validation.SaveServiceSchedules.Schedules.DayOfWeek.Min'),
            'schedules.*.day_of_week.max' => T('Appointment::validation.SaveServiceSchedules.Schedules.DayOfWeek.Max'),
            'schedules.*.start_time.required' => T('Appointment::validation.SaveServiceSchedules.Schedules.StartTime.Required'),
            'schedules.*.start_time.date_format' => T('Appointment::validation.SaveServiceSchedules.Schedules.StartTime.DateFormat'),
            'schedules.*.end_time.required' => T('Appointment::validation.SaveServiceSchedules.Schedules.EndTime.Required'),
            'schedules.*.end_time.date_format' => T('Appointment::validation.SaveServiceSchedules.Schedules.EndTime.DateFormat'),
            'schedules.*.end_time.after' => T('Appointment::validation.SaveServiceSchedules.Schedules.EndTime.After'),
            'schedules.*.is_rest_day.boolean' => T('Appointment::validation.SaveServiceSchedules.Schedules.IsRestDay.Boolean'),
        ];
    }
} 