<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 通用设置请求
 */
class SaveSettingRequest extends FormRequest
{
    /**
     * 确定用户是否有权提交此请求
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array
     */
    public function rules()
    {
        return [
            // 通用设置相关字段验证规则
            'appointment_limit' => 'sometimes|string|in:no_limit,member_only', // 预约限制
            'non_member_record' => 'sometimes|boolean', // 非会员记录
            'time_slot_interval' => 'sometimes|integer|in:5,10,15,30,60', // 时段粒度
            'cross_time_slot' => 'sometimes|boolean', // 跨时段预约
            'advance_booking_time' => 'sometimes|integer|min:1|max:168', // 提前预约时间
            'max_booking_days' => 'sometimes|integer|min:1|max:365', // 最大预约天数
            'default_appointment_status' => 'sometimes|integer|in:0,1,2,3,4,5,6', // 默认预约状态，0-待确认，1-已确认，2-已改期，3-已完成，4-已取消，5-已拒绝，6-未到
            'default_payment_status' => 'sometimes|integer|in:0,1,2', // 默认支付状态，0-待支付，1-已支付，2-支付失败
            'allow_overtime_booking' => 'sometimes|boolean', // 允许超时预约
            'allow_admin_overtime_booking' => 'sometimes|boolean', // 允许管理员在非工作时间预约

            // 通知设置相关字段验证规则
            'appointment_reschedule_notification' => 'sometimes|nullable|string|in:email,sms', // 预约改期通知
            'appointment_cancel_notification' => 'sometimes|nullable|string|in:email,sms', // 预约取消通知
            'appointment_confirm_notification' => 'sometimes|nullable|string|in:email,sms', // 预约确认通知

            //假期设置相关字段验证规则
            'holiday_settings' => 'sometimes|nullable|array', // 假期设置
            'holiday_settings.*.start' => 'required|date', // 开始日期
            'holiday_settings.*.end' => 'required|date', // 结束日期
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     *
     * @return array
     */
    public function messages()
    {
        return [
            // 通用设置相关字段错误信息
            'appointment_limit.string' => T('Appointment::validation.SaveGeneralSetting.AppointmentLimit.String'),
            'appointment_limit.in' => T('Appointment::validation.SaveGeneralSetting.AppointmentLimit.In'),
            'non_member_record.boolean' => T('Appointment::validation.SaveGeneralSetting.NonMemberRecord.Boolean'),
            'time_slot_interval.integer' => T('Appointment::validation.SaveGeneralSetting.TimeSlotInterval.Integer'),
            'time_slot_interval.in' => T('Appointment::validation.SaveGeneralSetting.TimeSlotInterval.In'), 
            'cross_time_slot.boolean' => T('Appointment::validation.SaveGeneralSetting.CrossTimeSlot.Boolean'), 
            'advance_booking_time.integer' => T('Appointment::validation.SaveGeneralSetting.AdvanceBookingTime.Integer'),
            'max_booking_days.integer' => T('Appointment::validation.SaveGeneralSetting.MaxBookingDays.Integer'),
            'default_appointment_status.integer' => T('Appointment::validation.SaveGeneralSetting.DefaultAppointmentStatus.Integer'),
            'default_appointment_status.in' => T('Appointment::validation.SaveGeneralSetting.DefaultAppointmentStatus.In'),
            'default_payment_status.integer' => T('Appointment::validation.SaveGeneralSetting.DefaultPaymentStatus.Integer'),
            'default_payment_status.in' => T('Appointment::validation.SaveGeneralSetting.DefaultPaymentStatus.In'),
            'allow_overtime_booking.boolean' => T('Appointment::validation.SaveGeneralSetting.AllowOvertimeBooking.Boolean'),
            'allow_admin_overtime_booking.boolean' => T('Appointment::validation.SaveGeneralSetting.AllowAdminOvertimeBooking.Boolean'),
            // 通知设置相关字段错误信息
            'appointment_reschedule_notification.string' => T('Appointment::validation.SaveNotificationSetting.AppointmentRescheduleNotification.String'),
            'appointment_cancel_notification.string' => T('Appointment::validation.SaveNotificationSetting.AppointmentCancelNotification.String'),
            'appointment_confirm_notification.string' => T('Appointment::validation.SaveNotificationSetting.AppointmentConfirmNotification.String'),
            //假期设置相关字段错误信息
            'holiday_settings.array' => T('Appointment::validation.SaveHolidaySetting.HolidaySettings.Array'),
            'holiday_settings.*.start.required' => T('Appointment::validation.SaveHolidaySetting.HolidaySettings.Start.Required'),
            'holiday_settings.*.end.required' => T('Appointment::validation.SaveHolidaySetting.HolidaySettings.End.Required'),
        ];
    }
} 