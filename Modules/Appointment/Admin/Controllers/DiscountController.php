<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Controllers;

use Bingo\Exceptions\BizException;
use Modules\Appointment\Admin\Requests\ListDiscountRequest;
use Modules\Appointment\Admin\Requests\StoreDiscountRequest;
use Modules\Appointment\Admin\Requests\UpdateDiscountRequest;
use Modules\Appointment\Services\DiscountService;
use App\Http\Controllers\Controller;
use Modules\Appointment\Models\AppointmentDiscount;
use Illuminate\Http\Request;

/**
 * 折扣管理控制器
 */
class DiscountController extends Controller
{
    /**
     * 构造函数
     *
     * @param DiscountService $discountService 折扣服务
     */
    public function __construct(private DiscountService $discountService)
    {
    }

    /**
     * 显示折扣列表
     *
     * @param ListDiscountRequest $request 请求
     * @return array
     */
    public function index(ListDiscountRequest $request): array
    {
        $filters = $request->validated();
        $discounts = $this->discountService->getDiscountList($filters);
        return [
            'total' => $discounts->total(),
            'items' => $discounts->items(),
        ];
    }

    /**
     * 显示折扣类型列表
     *
     * @return array
     */
    public function types(): array
    {
        $typeMap = AppointmentDiscount::$typeMap;
        $result = [];

        foreach ($typeMap as $value => $label) {
            $result[] = [
                'value' => $value,
                'label' => $label
            ];
        }

        return $result;
    }

    /**
     * 显示折扣状态列表
     *
     * @return array
     */
    public function statuses(): array
    {
        $statusMap = AppointmentDiscount::$statusMap;
        $result = [];

        foreach ($statusMap as $value => $label) {
            $result[] = [
                'value' => $value,
                'label' => $label
            ];
        }

        return $result;
    }

    /**
     * 保存新折扣
     *
     * @param StoreDiscountRequest $request 请求
     * @return array
     */
    public function store(StoreDiscountRequest $request): array
    {
        $data = $request->validated();
        return $this->discountService->createDiscount($data)->toArray();
    }

    /**
     * 显示折扣详情
     *
     * @param int $id 折扣ID
     * @return array
     */
    public function show($id): array
    {
        return $this->discountService->getDiscountDetail((int) $id)->toArray();
    }

    /**
     * 更新折扣信息
     *
     * @param UpdateDiscountRequest $request 请求
     * @param int $id 折扣ID
     * @return array
     */
    public function update(UpdateDiscountRequest $request, $id): array
    {
        $data = $request->validated();
        return $this->discountService->updateDiscount((int) $id, $data)->toArray();
    }

    /**
     * 删除折扣
     *
     * @param int $id 折扣ID
     * @return array
     */
    public function destroy($id): array
    {
        return [
            'success' => $this->discountService->deleteDiscount((int) $id),
        ];
    }
}