<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Appointment\Services\DepartmentService;

/**
 * 部门管理控制器
 */
class DepartmentController extends Controller
{
    public function __construct(
        private readonly DepartmentService $service
    ) {}

    /**
     * 显示部门列表
     * 
     * @return array
     */
    public function index(Request $request):array
    {
        return $this->service->list($request->all());
    }
} 