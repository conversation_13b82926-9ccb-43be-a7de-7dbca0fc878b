<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Appointment\Services\PositionService;

/**
 * 职位管理控制器
 */
class PositionController extends Controller
{
    public function __construct(
        private readonly PositionService $service
    ) {}

    /**
     * 显示职位列表
     * 
     * @return array
     */
    public function index(Request $request):array
    {
        return $this->service->list($request->all());
    }
} 