<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Appointment\Admin\Requests\ListStaffRequest;
use Modules\Appointment\Admin\Requests\StoreStaffRequest;
use Modules\Appointment\Admin\Requests\UpdateStaffRequest;
use Modules\Appointment\Services\StaffService;
use Bingo\Exceptions\BizException;
use Modules\Appointment\Enums\ErrorCode;
use Modules\Appointment\Admin\Requests\DeleteStaffRequest;
use Modules\Appointment\Admin\Requests\ImportStaffRequest;
use Modules\Appointment\Admin\Requests\ExportStaffRequest;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

/**
 * 员工管理控制器
 */
class StaffController extends Controller
{
    /**
     * @var StaffService
     */
    protected $staffService;

    /**
     * 构造函数
     */
    public function __construct(StaffService $staffService)
    {
        $this->staffService = $staffService;
    }

    /**
     * 显示员工列表
     * @param ListStaffRequest $request
     * @return array
     */
    public function index(ListStaffRequest $request)
    {
        $filters = $request->validated();
        $staffs = $this->staffService->list($filters);
        return [
            'total' => $staffs->total(),
            'items' => $staffs->items(),
        ];
    }

    /**
     * 保存新员工
     * @param StoreStaffRequest $request
     * @return array
     */
    public function store(StoreStaffRequest $request): array
    {
        $data = $request->validated();
        $staff = $this->staffService->store($data);
        return $staff->toArray();
    }

    /**
     * 显示员工详情
     */
    public function show(int $id): array
    {
       return $this->staffService->show($id)->toArray();
    }

    /**
     * 更新员工信息
     * @param UpdateStaffRequest $request
     * @param int $id 员工ID
     * @return array
     */
    public function update(UpdateStaffRequest $request, int $id): array
    {
        $data = $request->validated();
        return $this->staffService->update($id, $data)->toArray();
    }

    /**
     * 删除员工
     * @param int $id 员工ID
     * @return array
     */
    public function destroyBatch(DeleteStaffRequest $request): array
    {
        $data = $request->validated();
        return [
            'success' => $this->staffService->destroyBatch($data['staff_ids']),
        ];
    }

    /**
     * 导出员工数据
     * 
     * @param ExportStaffRequest $request
     * @return BinaryFileResponse
     * @throws BizException
     */
    public function export(Request $request): BinaryFileResponse
    {
        $page = (int)($request->input('page', 1));
        $limit = (int)($request->input('limit', 20));

        return $this->staffService->export(['page' => $page, 'limit' => $limit]);
    }

    /**
     * 导入员工数据
     * 
     * @param ImportStaffRequest $request
     * @return array
     * @throws BizException
     */
    public function import(ImportStaffRequest $request): array
    {
        try {
            if (!$request->hasFile('file')) {
                throw BizException::throws(ErrorCode::INVALID_PARAMS, '请选择要导入的文件');
            }

            $file = $request->file('file');
            $result = $this->staffService->import($file);

            return [
                'message' => T('Appointment::messages.staff.imported'),
                'data' => $result,
            ];
        } catch (\Exception $e) {
            throw BizException::throws(ErrorCode::STAFF_UPDATE_FAILED, '导入员工数据失败：' . $e->getMessage());
        }
    }

    /**
     * 下载员工导入模板
     * 
     * @return BinaryFileResponse
     */
    public function exportTemplate(): BinaryFileResponse
    {
        return $this->staffService->exportTemplate();
    }
}
