<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Controllers;

use Bingo\Exceptions\BizException;
use Carbon\Carbon;
use Modules\Appointment\Admin\Requests\ListAppointmentRequest;
use Modules\Appointment\Admin\Requests\StoreAppointmentRequest;
use Modules\Appointment\Admin\Requests\UpdateAppointmentRequest;
use Modules\Appointment\Admin\Requests\ImportAppointmentRequest;
use Modules\Appointment\Services\AppointmentService;
use App\Http\Controllers\Controller;
use Modules\Appointment\Models\AppointmentRecords;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Illuminate\Http\Request;
use Modules\Appointment\Admin\Requests\UpdatePaymentInfoRequest;

/**
 * 预约管理控制器
 */
class AppointmentController extends Controller
{
    /**
     * 构造函数
     *
     * @param AppointmentService $appointmentService 预约服务
     */
    public function __construct(private readonly AppointmentService $appointmentService) {}

    /**
     * 显示预约列表
     *
     * @param ListAppointmentRequest $request 请求
     * @return array
     */
    public function index(ListAppointmentRequest $request): array
    {
        $filters = $request->validated();
        $appointments = $this->appointmentService->getAppointmentList($filters);
        return [
            'total' => $appointments->total(),
            'items' => $appointments->items(),
        ];
    }

    /**
     * 显示预约状态列表
     *
     * @return array
     */
    public function statuses(): array
    {
        $statusMap = AppointmentRecords::$statusMap;
        $result = [];

        foreach ($statusMap as $value => $label) {
            $result[] = [
                'value' => $value,
                'label' => $label
            ];
        }

        return $result;
    }

    /**
     * 显示我的预约列表
     *
     * @param ListAppointmentRequest $request 请求
     * @return array
     */
    public function my(ListAppointmentRequest $request): array
    {
        $filters = $request->validated();
        $filters['customer_id'] = auth()->guard()->user()->id ?? 0;
        $appointments = $this->appointmentService->getAppointmentList($filters);
        return [
            'total' => $appointments->total(),
            'items' => $appointments->items(),
        ];
    }

    /**
     * 保存新预约
     *
     * @param StoreAppointmentRequest $request 请求
     * @return array
     */
    public function store(StoreAppointmentRequest $request): array
    {
        $data = $request->validated();
        return $this->appointmentService->createAppointment($data)->toArray();
    }

    /**
     * 显示预约详情
     *
     * @param int $id 预约ID
     * @return array
     */
    public function show($id): array
    {
        return $this->appointmentService->getAppointmentDetail((int) $id)->toArray();
    }

    /**
     * 更新预约信息
     *
     * @param UpdateAppointmentRequest $request 请求
     * @param int $id 预约ID
     * @return array
     */
    public function update(UpdateAppointmentRequest $request, $id): array
    {
        $data = $request->validated();
        return  $this->appointmentService->updateAppointment((int) $id, $data)->toArray();
    }

    /**
     * 删除预约
     *
     * @param int $id 预约ID
     * @return array
     */
    public function destroy($id): array
    {
        return [
            'success' => $this->appointmentService->deleteAppointment((int) $id),
        ];
    }

    /**
     * 批量删除预约
     *
     * @param Request $request 请求
     * @return array
     */
    public function destroyBatch(Request $request): array
    {
        $ids = $request->input('ids');
        return [
            'success' => $this->appointmentService->deleteAppointments($ids),
        ];
    }

    /**
     * 导出预约
     *
     * @param Request $request 请求
     * @return BinaryFileResponse 文件下载响应
     * @throws BizException
     */
    public function export(Request $request): BinaryFileResponse
    {
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 15);
        return $this->appointmentService->exportAppointment(['page' => $page, 'limit' => $limit]);
    }

    /**
     * 导入预约
     *
     * @param ImportAppointmentRequest $request 请求
     * @return array
     * @throws BizException
     */
    public function import(ImportAppointmentRequest $request): array
    {
        $file = $request->file('file');
        $creatorId = auth()->guard()->user()->id ?? 0;
        return $this->appointmentService->importAppointment($file, $creatorId);
    }

    /**
     * 导出预约导入模板
     *
     * @return BinaryFileResponse 文件下载响应
     */
    public function exportTemplate(): BinaryFileResponse
    {
        return $this->appointmentService->exportTemplate();
    }

    /**
     * 更新预约付款信息
     *
     * @param UpdatePaymentInfoRequest $request 请求
     * @param int $id 预约ID
     * @return array
     */
    public function updatePaymentInfo(UpdatePaymentInfoRequest $request, $id): array
    {
        $data = $request->validated();
        return $this->appointmentService->updateAppointmentPayment((int) $id, $data);
    }

    /**
     * 更新预约状态
     *
     * @param int $id 预约ID
     * @param Request $request 请求
     * @return array
     * @throws BizException
     */
    public function updateStatus($id, Request $request): array
    {
        $status = $request->input('status');
        $this->appointmentService->updateAppointmentStatus((int) $id, $status);
        return ['message' => '预约状态更新成功'];
    }

    /**
     * 预约仪表盘
     *
     * @param Request $request
     * @return array
     */
    public function dashboard(Request $request): array
    {
        // 使用Carbon类处理日期，默认为今天
        $startDate = $request->input('start_date', Carbon::today()->toDateString());
        $endDate = $request->input('end_date', Carbon::today()->toDateString());
        return $this->appointmentService->dashboard(['start_date' => $startDate, 'end_date' => $endDate]);
    }
}
