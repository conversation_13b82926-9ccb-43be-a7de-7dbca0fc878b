<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Appointment\Services\CustomerService;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Modules\Appointment\Admin\Requests\ListCustomerRequest;
use Modules\Appointment\Admin\Requests\StoreCustomerRequest;
use Modules\Appointment\Admin\Requests\ImportCustomerRequest;
use Modules\Appointment\Admin\Requests\UpdateCustomerRequest;

/**
 * 客户管理控制器
 */
class CustomerController extends Controller
{
    /**
     * @var CustomerService
     */
    private CustomerService $service;

    /**
     * 构造函数
     */
    public function __construct(CustomerService $service)
    {
        $this->service = $service;
    }

    /**
     * 显示客户列表
     * @param ListCustomerRequest $request
     * @return array
     */
    public function index(ListCustomerRequest $request): array
    {
        $paginate = $this->service->list($request->validated());
        return [
            'items' => $paginate->items(),
            'total' => $paginate->total(),
        ];
    }

    /**
     * 保存新客户
     * @param StoreCustomerRequest $request
     * @return array
     */
    public function store(StoreCustomerRequest $request): array
    {
        $customer = $this->service->create($request->validated());
        return $customer->toArray();
    }

    /**
     * 显示客户详情
     * @param int $id
     * @return array
     */
    public function show(int $id): array
    {
        return $this->service->show($id)->toArray();
    }

    /**
     * 更新客户信息
     * @param UpdateCustomerRequest $request
     * @param int $id
     * @return array
     */
    public function update(UpdateCustomerRequest $request, int $id): array
    {
        return $this->service->update($id, $request->validated());
    }

    /**
     * 导出客户数据
     * @return array
     */
    public function export(Request $request): BinaryFileResponse
    {
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 1000);
        return $this->service->export($page, $limit);
    }

    /**
     * 导入客户数据
     * @param ImportCustomerRequest $request
     * @return array
     */
    public function import(ImportCustomerRequest $request): array
    {
        // 获取验证后的文件
        $file = $request->validated()['file'];
            
        // 调用service层处理导入逻辑
        $result = $this->service->import($file);

        return $result;
    }

    /**
     * 导出客户导入模板
     * @return array
     */
    public function exportTemplate(): BinaryFileResponse
    {
        return $this->service->exportTemplate();
    }

    /**
     * 显示客户预约历史
     * @param int $id
     * @param ListCustomerRequest $request
     * @return array
     */
    public function appointments(int $id, ListCustomerRequest $request): array
    {
        $result = $this->service->appointments($id, $request->validated());
        return [
            'items' => $result->items(),
            'total' => $result->total(),
        ];
    }

    /**
     * 批量删除客户
     * @param Request $request
     * @return array
     */
    public function destroyBatch(Request $request): array
    {
        $ids = $request->input('ids', []);
        $result = $this->service->destroyBatch($ids);
        return $result;
    }
} 