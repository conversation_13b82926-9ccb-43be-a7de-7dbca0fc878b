<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Controllers;

use Illuminate\Routing\Controller;
use Modules\Appointment\Admin\Requests\ListEmailTemplateRequest;
use Modules\Appointment\Admin\Requests\StoreEmailTemplateRequest;
use Modules\Appointment\Services\EmailTemplateService;
use Modules\Approval\Services\EmailService;

/**
 * 邮件模板控制器
 */
final class EmailTemplateController extends Controller
{
    /**
     * 构造函数
     *
     * @param EmailTemplateService $emailTemplateService 邮件模板服务
     */
    public function __construct(
        private readonly EmailTemplateService $emailTemplateService,
        private readonly EmailService $emailService
    ) {
    }

    /**
     * 获取邮件模板列表
     *
     * @param ListEmailTemplateRequest $request
     * @return array
     */
    public function index(ListEmailTemplateRequest $request): array
    {
        $templates = $this->emailTemplateService->getEmailTemplates($request->validated());

        return [
            'total' => $templates->total(),
            'items' => $templates->items(),
        ];
    }

    /**
     * 获取邮件模板详情
     *
     * @param int $id
     * @return array
     */
    public function show(int $id): array
    {
        $template = $this->emailTemplateService->getEmailTemplateById($id);
        return $template->toArray();
    }

    /**
     * 保存邮件模板
     *
     * @param StoreEmailTemplateRequest $request
     * @return array
     */
    public function store(StoreEmailTemplateRequest $request): array
    {
        $data = $request->validated();
        
        $template = $this->emailTemplateService->saveEmailTemplate($data);

        return $template->toArray();
    }

    /**
     * 删除邮件模板
     *
     * @param int $id
     * @return array
     */
    public function destroy(int $id): array
    {
        $success = $this->emailTemplateService->deleteEmailTemplate($id);
        return [
            'success' => $success,
        ];
    }

    /**
     * 开关邮件模板
     *
     * @param int $id
     * @return array
     */
    public function switch(int $id): array  
    {
        $success = $this->emailTemplateService->switchEmailTemplate($id);
        return [
            'success' => $success,
        ];
    }
} 