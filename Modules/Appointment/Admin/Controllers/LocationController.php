<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Controllers;

use App\Http\Controllers\Controller;
use Modules\Appointment\Admin\Requests\ListLocationRequest;
use Modules\Appointment\Admin\Requests\StoreLocationRequest;
use Modules\Appointment\Admin\Requests\UpdateLocationRequest;
use Modules\Appointment\Services\LocationService;

/**
 * 地点管理控制器
 */
class LocationController extends Controller
{
    /**
     * @var LocationService
     */
    private LocationService $locationService;

    /**
     * 构造函数
     */
    public function __construct(LocationService $locationService)
    {
        $this->locationService = $locationService;
    }

    /**
     * 显示地点列表
     * 
     * @param ListLocationRequest $request
     * @return array
     */
    public function index(ListLocationRequest $request): array
    {
        $filters = $request->validated();
        $locations = $this->locationService->getList($filters);
        return [
            "items" => $locations->items(),
            "total" => $locations->total(),
        ];
    }

    /**
     * 保存新地点
     * 
     * @param StoreLocationRequest $request
     * @return array
     */
    public function store(StoreLocationRequest $request): array
    {
        $location = $this->locationService->create($request->validated());

        return $location->toArray();    
    }

    /**
     * 更新地点信息
     * 
     * @param UpdateLocationRequest $request
     * @param int $id
     * @return array
     */
    public function update(int $id,UpdateLocationRequest $request): array
    {
        $location = $this->locationService->update($id, $request->validated());

        return $location->toArray();
    }
}
