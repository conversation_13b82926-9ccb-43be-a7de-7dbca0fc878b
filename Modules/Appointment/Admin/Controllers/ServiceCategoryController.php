<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Appointment\Admin\Requests\ListServiceCategoryRequest;
use Modules\Appointment\Admin\Requests\StoreServiceCategoryRequest;
use Modules\Appointment\Admin\Requests\UpdateServiceCategoryRequest;
use Modules\Appointment\Services\ServiceCategoryService;

/**
 * 服务分类管理控制器
 */
class ServiceCategoryController extends Controller
{
    /**
     * @var ServiceCategoryService
     */
    private ServiceCategoryService $serviceCategoryService;

    /**
     * 构造函数
     */
    public function __construct(ServiceCategoryService $serviceCategoryService)
    {
        $this->serviceCategoryService = $serviceCategoryService;
    }

    /**
     * 获取服务分类列表
     *
     * @param ListServiceCategoryRequest $request
     * @return array
     */
    public function index(ListServiceCategoryRequest $request): array
    {
        $filters = $request->validated();
        $categories = $this->serviceCategoryService->getList($filters);
        
        return [
            "items" => $categories->items(),
            "total" => $categories->total(),
        ];
    }

    /**
     * 创建服务分类
     *
     * @param StoreServiceCategoryRequest $request
     * @return array
     */
    public function store(StoreServiceCategoryRequest $request): array
    {
        $data = $request->validated();
        $category = $this->serviceCategoryService->create($data);
        
        return $category->toArray();
    }

    /**
     * 更新服务分类
     *
     * @param UpdateServiceCategoryRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateServiceCategoryRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();
        $category = $this->serviceCategoryService->update($id, $data);
        
        if (!$category) {
            return response()->json([
                'code' => 404,
                'message' => '服务分类不存在',
            ], 404);
        }
        
        return response()->json([
            'code' => 0,
            'message' => '更新成功',
            'data' => $category,
        ]);
    }

    /**
     * 删除服务分类
     *
     * @param int $id
     * @return bool
     */
    public function destroy(int $id): bool
    {
        return $this->serviceCategoryService->delete($id);
        
        
    }
} 