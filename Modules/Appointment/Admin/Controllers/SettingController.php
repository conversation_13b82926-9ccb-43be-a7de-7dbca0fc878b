<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Appointment\Services\AppointmentSettingService;
use Modules\Appointment\Admin\Requests\SaveSettingRequest;

/**
 * 系统设置控制器
 */
class SettingController extends Controller
{

    protected $service;

    public function __construct(AppointmentSettingService $service)
    {
        $this->service = $service;
    }

    /**
     * 显示设置页面
     */
    public function index()
    {
        $list =  $this->service->list();
        // 将设置列表转换为以key为索引的集合
        $settings = $list->keyBy('key');
        
        // 返回设置数据
        return $settings;
    }

    /**
     * 保存设置
     */
    public function batchSave(SaveSettingRequest $request)
    {
        return $this->service->batchSave($request->all());
    }

} 