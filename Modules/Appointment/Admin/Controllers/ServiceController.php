<?php

declare(strict_types=1);

namespace Modules\Appointment\Admin\Controllers;

use App\Http\Controllers\Controller;
use Modules\Appointment\Services\ServiceService;
use Modules\Appointment\Admin\Requests\ListServiceRequest;
use Modules\Appointment\Admin\Requests\StoreServiceRequest;
use Modules\Appointment\Admin\Requests\UpdateServiceRequest;


/**
 * 服务管理控制器
 */
class ServiceController extends Controller
{
    /**
     * @var ServiceService
     */
    private ServiceService $serviceService;

    /**
     * 构造函数
     */
    public function __construct(ServiceService $serviceService)
    {
        $this->serviceService = $serviceService;
    }

    /**
     * 显示服务列表
     *
     * @param ListServiceRequest $request
     * @return array
     */
    public function index(ListServiceRequest $request): array
    {
        $filters = $request->validated();
        $services = $this->serviceService->getList($filters);

        return [
            'items' => $services->items(),
            'total' => $services->total(),
        ];
    }

    /**
     * 保存新服务
     *
     * @param StoreServiceRequest $request
     * @return array
     */
    public function store(StoreServiceRequest $request): array
    {
        $data = $request->validated();
        $service = $this->serviceService->create($data);

        return $service->toArray();
    }

    /**
     * 显示服务详情
     *
     * @param int $id
     * @return array
     */
    public function show(int $id): array
    {
        $service = $this->serviceService->find($id);
        return $service->toArray();
    }

    /**
     * 更新服务信息
     *
     * @param UpdateServiceRequest $request
     * @param int $id
     * @return array
     */
    public function update(UpdateServiceRequest $request, int $id): array
    {
        $data = $request->validated();
        $service = $this->serviceService->update($id, $data);

        return $service->toArray();
    }

    /**
     * 删除服务
     *
     * @param int $id
     * @return array
     */
    public function destroy(int $id): array
    {
        return ['success' => $this->serviceService->delete($id)];
    }

    /**
     * 开放服务
     *
     * @param int $id
     * @return array
     */
    public function open(int $id): array
    {
        $service = $this->serviceService->openService($id);

        return [
            'message' => '服务已开放',
            'service' => $service->toArray()
        ];
    }

    /**
     * 关闭服务
     *
     * @param int $id
     * @return array
     */
    public function close(int $id): array
    {
        $service = $this->serviceService->closeService($id);

        return [
            'message' => '服务已关闭',
            'service' => $service->toArray()
        ];
    }

    /**
     * 全部开放
     *
     * @return array
     */
    public function openAll(): array
    {
        $count = $this->serviceService->openAllServices();

        return [
            'message' => "已成功开放{$count}个服务",
            'count' => $count
        ];
    }

    /**
     * 全部关闭
     *
     * @return array
     */
    public function closeAll(): array
    {
        $count = $this->serviceService->closeAllServices();

        return [
            'message' => "已成功关闭{$count}个服务",
            'count' => $count
        ];
    }
}
