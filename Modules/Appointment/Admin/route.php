<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Modules\Appointment\Admin\Controllers\StaffController;
use Modules\Appointment\Admin\Controllers\ServiceController;
use Modules\Appointment\Admin\Controllers\SettingController;
use Modules\Appointment\Admin\Controllers\CustomerController;
use Modules\Appointment\Admin\Controllers\DiscountController;
use Modules\Appointment\Admin\Controllers\PositionController;
use Modules\Appointment\Admin\Controllers\DepartmentController;
use Modules\Appointment\Admin\Controllers\AppointmentController;
use Modules\Appointment\Admin\Controllers\EmailTemplateController;
use Modules\Appointment\Admin\Controllers\ServiceCategoryController;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| 模块的管理后台路由定义
|
*/

Route::prefix('reservation')->group(function () {
    // 预约系统仪表盘
    Route::get('dashboard', [AppointmentController::class, 'dashboard'])->name('appointment.dashboard');

    // 预约管理
    Route::prefix('appointments')->group(function () {
        // 导出预约
        Route::get('/export', [AppointmentController::class, 'export'])->name('appointment.export');
        // 导入预约
        Route::post('/import', [AppointmentController::class, 'import'])->name('appointment.import');
        // 导出预约导入模板
        Route::get('/export-template', [AppointmentController::class, 'exportTemplate'])->name('appointment.export.template');
        // 预约列表
        Route::get('/', [AppointmentController::class, 'index'])->name('appointment.index');
        // 预约状态列表
        Route::get('/statuses', [AppointmentController::class, 'statuses'])->name('appointment.statuses');
        // 我的预约
        Route::get('/my', [AppointmentController::class, 'my'])->name('appointment.my');
        // 添加预约
        Route::post('/', [AppointmentController::class, 'store'])->name('appointment.store');
        // 预约详情
        Route::get('/{id}', [AppointmentController::class, 'show'])->name('appointment.show');
        // 更新预约
        Route::put('/{id}', [AppointmentController::class, 'update'])->name('appointment.update');
        // 删除预约
        Route::delete('/{id}', [AppointmentController::class, 'destroy'])->name('appointment.destroy');
        // 批量删除预约
        Route::delete('/', [AppointmentController::class, 'destroyBatch'])->name('appointment.destroy.batch');
        // 更新预约付款信息
        Route::put('/{id}/payment', [AppointmentController::class, 'updatePaymentInfo'])->name('appointments.payment.update');
        // 更新预约状态
        Route::put('/{id}/status', [AppointmentController::class, 'updateStatus'])->name('appointments.status.update');
    });

    // 客户管理
    Route::prefix('customers')->group(function () {
        // 导出客户
        Route::get('/export', [CustomerController::class, 'export'])->name('customer.export');
        // 导入客户
        Route::post('/import', [CustomerController::class, 'import'])->name('customer.import');
        // 下载客户导入模板
        Route::get('/export-template', [CustomerController::class, 'exportTemplate'])->name('customer.export.template');
        // 客户列表
        Route::get('/', [CustomerController::class, 'index'])->name('customer.index');
        // 保存客户
        Route::post('/', [CustomerController::class, 'store'])->name('customer.store');
        // 客户详情
        Route::get('/{id}', [CustomerController::class, 'show'])->name('customer.show');
        // 更新客户
        Route::put('/{id}', [CustomerController::class, 'update'])->name('customer.update');
        // 客户历史预约
        Route::get('/{id}/appointments', [CustomerController::class, 'appointments'])->name('customer.appointments');
        // 批量删除客户
        Route::delete('/', [CustomerController::class, 'destroyBatch'])->name('customer.destroy.batch');
    });

    // 服务分类管理
    Route::prefix('service-categories')->group(function () {
        // 服务分类列表
        Route::get('/', [ServiceCategoryController::class, 'index'])->name('service.category.index');
        // 保存服务分类
        Route::post('/', [ServiceCategoryController::class, 'store'])->name('service.category.store');
        // 更新服务分类
        Route::put('/{id}', [ServiceCategoryController::class, 'update'])->name('service.category.update');
        // 删除服务分类
        Route::delete('/{id}', [ServiceCategoryController::class, 'destroy'])->name('service.category.destroy');
    });

    // 服务管理
    Route::prefix('services')->group(function () {
        // 服务列表
        Route::get('/', [ServiceController::class, 'index'])->name('service.index');
        // 保存服务
        Route::post('/', [ServiceController::class, 'store'])->name('service.store');
        // 服务详情
        Route::get('/{id}', [ServiceController::class, 'show'])->name('service.show');
        // 更新服务
        Route::put('/{id}', [ServiceController::class, 'update'])->name('service.update');
        // 删除服务
        Route::delete('/{id}', [ServiceController::class, 'destroy'])->name('service.destroy');
        // 开放服务
        Route::put('/{id}/open', [ServiceController::class, 'open'])->name('service.open');
        // 关闭服务
        Route::put('/{id}/close', [ServiceController::class, 'close'])->name('service.close');
        // 全部开放
        Route::post('/open-all', [ServiceController::class, 'openAll'])->name('service.open-all');
        // 全部关闭
        Route::post('/close-all', [ServiceController::class, 'closeAll'])->name('service.close-all');
    });

    // 员工管理
    Route::prefix('staff')->group(function () {
        // 导出员工
        Route::get('/export', [StaffController::class, 'export'])->name('staff.export');
        // 导入员工
        Route::post('/import', [StaffController::class, 'import'])->name('staff.import');
        // 下载员工导入模板
        Route::get('/export-template', [StaffController::class, 'exportTemplate'])->name('staff.export.template');
        // 员工列表
        Route::get('/', [StaffController::class, 'index'])->name('staff.index');
        // 保存员工
        Route::post('/', [StaffController::class, 'store'])->name('staff.store');
        // 更新员工
        Route::put('/{id}', [StaffController::class, 'update'])->name('staff.update');
        // 批量删除员工
        Route::delete('/', [StaffController::class, 'destroyBatch'])->name('staff.destroy');
        // 员工详情
        Route::get('/{id}', [StaffController::class, 'show'])->name('staff.show');
    });

    // // 部门管理
    Route::prefix('departments')->group(function () {
        // 部门列表
        Route::get('/', [DepartmentController::class, 'index'])->name('department.index');
    });

    // 职位管理
    Route::prefix('positions')->group(function () {
        // 职位列表
        Route::get('/', [PositionController::class, 'index'])->name('position.index');
    });

    // 折扣管理
    Route::prefix('discounts')->group(function () {
        // 折扣列表
        Route::get('/', [DiscountController::class, 'index'])->name('discount.index');
        // 折扣状态列表
        Route::get('/statuses', [DiscountController::class, 'statuses'])->name('discount.statuses');
        // 折扣类型列表
        Route::get('/types', [DiscountController::class, 'types'])->name('discount.types');
        // 折扣详情
        Route::get('/{id}', [DiscountController::class, 'show'])->name('discount.show');
        // 保存折扣
        Route::post('/', [DiscountController::class, 'store'])->name('discount.store');
        // 更新折扣
        Route::put('/{id}', [DiscountController::class, 'update'])->name('discount.update');
        // 删除折扣
        Route::delete('/{id}', [DiscountController::class, 'destroy'])->name('discount.destroy');
    });

    // // 假期设置
    // Route::prefix('holidays')->group(function () {
    //     // 假期列表
    //     Route::get('/', [HolidayController::class, 'index'])->name('holiday.index');
    //     // 创建假期
    //     Route::get('/create', [HolidayController::class, 'create'])->name('holiday.create');
    //     // 保存假期
    //     Route::post('/', [HolidayController::class, 'store'])->name('holiday.store');
    //     // 编辑假期
    //     Route::get('/{id}/edit', [HolidayController::class, 'edit'])->name('holiday.edit');
    //     // 更新假期
    //     Route::put('/{id}', [HolidayController::class, 'update'])->name('holiday.update');
    //     // 删除假期
    //     Route::delete('/{id}', [HolidayController::class, 'destroy'])->name('holiday.destroy');
    // });

    // 系统设置
    Route::prefix('settings')->group(function () {
        // 系统设置列表
        Route::get('/', [SettingController::class, 'index'])->name('setting.index');
        // 保存系统设置
        Route::post('/', [SettingController::class, 'batchSave'])->name('setting.save.batchSave');
    });

    // 邮件模板
    Route::prefix('email-templates')->group(function () {
        // 邮件模板列表
        Route::get('/', [EmailTemplateController::class, 'index'])->name('email.template.index');
        // 邮件模板详情
        Route::get('/{id}', [EmailTemplateController::class, 'show'])->name('email.template.show');
        // 保存邮件模板
        Route::post('/', [EmailTemplateController::class, 'store'])->name('email.template.store');
        // 删除邮件模板
        Route::delete('/{id}', [EmailTemplateController::class, 'destroy'])->name('email.template.destroy');    
        // 开关邮件模板
        Route::post('/{id}/switch', [EmailTemplateController::class, 'switch'])->name('email.template.switch');
    });
});
