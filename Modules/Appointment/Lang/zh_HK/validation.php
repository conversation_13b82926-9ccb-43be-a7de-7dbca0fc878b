<?php

return [
    // Store Customer Request
    'StoreCustomer' => [
        'Name' => [
            'Required' => '姓名不能為空',
            'String' => '姓名必須是字符串',
            'Max' => '姓名長度不能超過50個字符'
        ],
        'Email' => [
            'Required' => '郵箱不能為空',
            'Email' => '請輸入有效的郵箱地址'
        ],
        'Phone' => [
            'Required' => '手機號不能為空',
            'String' => '手機號必須是字符串',
            'Max' => '手機號長度不能超過20個字符'
        ],
        'Gender' => [
            'Required' => '性別不能為空',
            'Integer' => '性別必須是整數',
            'In' => '性別必須是1（男）或2（女）'
        ],
        'Birthday' => [
            'Date' => '生日必須是有效的日期'
        ],
        'Description' => [
            'String' => '描述必須是字符串',
            'Max' => '描述長度不能超過500個字符'
        ],
        'Photo' => [
            'String' => '照片必須是字符串',
            'Max' => '照片URL長度不能超過255個字符'
        ]
    ],
    
    // List Appointment Request
    'ListAppointment' => [
        'Page' => [
            'Integer' => '頁碼必須是整數',
            'Min' => '頁碼必須大於等於1'
        ],
        'Limit' => [
            'Integer' => '每頁數量必須是整數',
            'Min' => '每頁數量必須大於等於1',
            'Max' => '每頁數量不能超過100'
        ],
        'Keyword' => [
            'String' => '關鍵詞必須是字符串',
            'Max' => '關鍵詞長度不能超過100個字符'
        ],
        'CustomerId' => [
            'Integer' => '客戶ID必須是整數'
        ],
        'Status' => [
            'String' => '狀態必須是字符串'
        ],
        'SortField' => [
            'String' => '排序字段必須是字符串',
            'In' => '排序字段必須是id、appointment_date、created_at或updated_at之一'
        ],
        'SortOrder' => [
            'String' => '排序方式必須是字符串',
            'In' => '排序方式必須是asc或desc'
        ]
    ],
    
    // Store Staff Request
    'StoreStaff' => [
        'Name' => [
            'Required' => '姓名不能為空',
            'String' => '姓名必須是字符串',
            'Max' => '姓名長度不能超過50個字符'
        ],
        'Email' => [
            'Required' => '郵箱不能為空',
            'Email' => '請輸入有效的郵箱地址'
        ],
        'Phone' => [
            'String' => '手機號必須是字符串',
            'Max' => '手機號長度不能超過20個字符'
        ],
        'PositionId' => [
            'Integer' => '職位ID必須是整數'
        ],
        'DepartmentId' => [
            'Integer' => '部門ID必須是整數'
        ],
        'Location' => [
            'String' => '地點必須是字符串',
            'Max' => '地點長度不能超過50個字符'
        ],
        'Description' => [
            'String' => '描述必須是字符串',
            'Max' => '描述長度不能超過255個字符'
        ]
    ],
    
    // Update Appointment Request
    'UpdateAppointment' => [
        'Location' => [
            'Required' => '地點不能為空',
            'String' => '地點必須是字符串'
        ],
        'ServiceId' => [
            'Required' => '服務ID不能為空',
            'Integer' => '服務ID必須是整數'
        ],
        'AppointmentDate' => [
            'Required' => '預約日期不能為空',
            'DateTime' => '預約日期必須是有效的日期和時間',
            'AfterOrEqual' => '預約日期必須是今天或將來的日期'
        ],
        'DiscountId' => [
            'Integer' => '折扣ID必須是整數'
        ],
        'Remark' => [
            'String' => '備註必須是字符串',
            'Max' => '備註長度不能超過500個字符'
        ],
        'CustomerId' => [
            'Required' => '客戶ID不能為空',
            'Integer' => '客戶ID必須是整數'
        ],
        'Status' => [
            'Required' => '狀態不能為空',
            'Integer' => '狀態必須是整數'
        ]
    ],
    
    // Store Service Request
    'StoreService' => [
        'CategoryId' => [
            'Required' => '分類ID不能為空',
            'Integer' => '分類ID必須是整數',
            'Exists' => '所選分類不存在'
        ],
        'Name' => [
            'Required' => '服務名稱不能為空',
            'String' => '服務名稱必須是字符串',
            'Max' => '服務名稱長度不能超過100個字符'
        ],
        'Price' => [
            'Required' => '價格不能為空',
            'Numeric' => '價格必須是數字',
            'Min' => '價格必須大於等於0'
        ],
        'Duration' => [
            'Required' => '時長不能為空',
            'Integer' => '時長必須是整數',
            'Min' => '時長必須大於等於1'
        ],
        'IsRepeatServicePayType' => [
            'RequiredIf' => '當啟用重複服務時，必須設置重複服務付款類型'
        ],
        'IsRepeatServiceCategory' => [
            'RequiredIf' => '當啟用重複服務時，必須設置重複服務分類'
        ],
        'IsRepeatServiceCount' => [
            'RequiredIf' => '當啟用重複服務時，必須設置重複服務次數',
            'Min' => '重複服務次數必須大於等於1'
        ],
        'PersonCount' => [
            'Required' => '服務人數不能為空',
            'Integer' => '服務人數必須是整數',
            'Min' => '服務人數必須大於等於1'
        ],
        'PayTypes' => [
            'Required' => '支付方式不能為空',
            'Array' => '支付方式必須是數組'
        ],
        'AdvanceBookingTime' => [
            'Integer' => '提前預約時間必須是整數',
            'Min' => '提前預約時間必須大於等於0'
        ],
        'Status' => [
            'Required' => '狀態不能為空',
            'Boolean' => '狀態必須是布爾值'
        ],
        'ExtraServices' => [
            'Name' => [
                'Required' => '額外服務名稱不能為空',
                'String' => '額外服務名稱必須是字符串',
                'Max' => '額外服務名稱長度不能超過100個字符'
            ],
            'CategoryName' => [
                'String' => '額外服務分類名稱必須是字符串',
                'Max' => '額外服務分類名稱長度不能超過100個字符'
            ]
        ],
        'Schedules' => [
            'DayOfWeek' => [
                'Required' => '星期幾不能為空',
                'Integer' => '星期幾必須是整數',
                'Min' => '星期幾必須大於等於1',
                'Max' => '星期幾不能超過7'
            ],
            'StartTime' => [
                'Required' => '開始時間不能為空',
                'String' => '開始時間必須是字符串',
                'Min' => '開始時間必須有效'
            ],
            'EndTime' => [
                'Required' => '結束時間不能為空',
                'String' => '結束時間必須是字符串',
                'Min' => '結束時間必須有效'
            ],
            'RestTimeList' => [
                'Array' => '休息時間列表必須是數組',
                'StartTime' => [
                    'String' => '休息時間開始時間必須是字符串',
                    'Min' => '休息時間開始時間必須有效'
                ],
                'EndTime' => [
                    'String' => '休息時間結束時間必須是字符串',
                    'Min' => '休息時間結束時間必須有效'
                ]
            ],
            'IsRestDay' => [
                'Boolean' => '是否休息日必須是布爾值'
            ]
        ],
        'SpecialSchedules' => [
            'StartTime' => [
                'Required' => '特殊時間安排開始時間不能為空',
                'String' => '特殊時間安排開始時間必須是字符串',
                'Min' => '特殊時間安排開始時間必須有效'
            ],
            'EndTime' => [
                'Required' => '特殊時間安排結束時間不能為空',
                'String' => '特殊時間安排結束時間必須是字符串',
                'Min' => '特殊時間安排結束時間必須有效'
            ]
        ],
        'StaffIds' => [
            'Required' => '員工ID不能為空',
            'Array' => '員工ID必須是數組',
            'Min' => '至少需要分配一名員工',
            'Exists' => '一個或多個所選員工不存在'
        ]
    ],
    
    // Import Staff Request
    'import_staff' => [
        'file' => [
            'required' => '請選擇要導入的文件',
            'file' => '上傳的必須是文件',
            'mimes' => '文件必須是 xlsx, xls 或 csv 格式',
            'max' => '文件大小不能超過 10MB',
        ],
    ],
    
    // Export Customer (kept for backward compatibility)
    'export_customer' => [
        'id' => 'ID',
        'name' => '姓名',
        'email' => '郵箱',
        'phone' => '手機號',
        'gender' => '性別',
        'birthdate' => '出生日期',
        'appointment_count' => '預約次數',
        'last_appointment_at' => '最近預約時間',
        'created_at' => '創建時間',
    ],
    
    // Import Customer (kept for backward compatibility)
    'import_customer' => [
        'name' => '姓名',
        'email' => '郵箱',
        'phone' => '手機號',
        'gender' => '性別',
        'birthdate' => '出生日期',
        'description' => '描述',
    ],
    
    // Update Service Request
    'UpdateService' => [
        'CategoryId' => [
            'Required' => '分類ID不能為空',
            'Integer' => '分類ID必須是整數',
            'Exists' => '所選分類不存在'
        ],
        'Name' => [
            'Required' => '服務名稱不能為空',
            'String' => '服務名稱必須是字符串',
            'Max' => '服務名稱長度不能超過100個字符'
        ],
        'Price' => [
            'Required' => '價格不能為空',
            'Numeric' => '價格必須是數字',
            'Min' => '價格必須大於等於0'
        ],
        'Duration' => [
            'Required' => '時長不能為空',
            'Integer' => '時長必須是整數',
            'Min' => '時長必須大於等於1'
        ],
        'IsRepeatServicePayType' => [
            'RequiredIf' => '當啟用重複服務時，必須設置重複服務付款類型'
        ],
        'IsRepeatServiceCategory' => [
            'RequiredIf' => '當啟用重複服務時，必須設置重複服務分類'
        ],
        'IsRepeatServiceCount' => [
            'RequiredIf' => '當啟用重複服務時，必須設置重複服務次數',
            'Min' => '重複服務次數必須大於等於1'
        ],
        'PersonCount' => [
            'Required' => '服務人數不能為空',
            'Integer' => '服務人數必須是整數',
            'Min' => '服務人數必須大於等於1'
        ],
        'PayTypes' => [
            'Required' => '支付方式不能為空',
            'Array' => '支付方式必須是數組'
        ],
        'AdvanceBookingTime' => [
            'Integer' => '提前預約時間必須是整數',
            'Min' => '提前預約時間必須大於等於0'
        ],
        'Status' => [
            'Required' => '狀態不能為空',
            'Boolean' => '狀態必須是布爾值'
        ],
        'ExtraServices' => [
            'Name' => [
                'Required' => '額外服務名稱不能為空',
                'String' => '額外服務名稱必須是字符串',
                'Max' => '額外服務名稱長度不能超過100個字符'
            ],
            'CategoryName' => [
                'String' => '額外服務分類名稱必須是字符串',
                'Max' => '額外服務分類名稱長度不能超過100個字符'
            ]
        ],
        'Schedules' => [
            'DayOfWeek' => [
                'Required' => '星期幾不能為空',
                'Integer' => '星期幾必須是整數',
                'Min' => '星期幾必須大於等於1',
                'Max' => '星期幾不能超過7'
            ],
            'StartTime' => [
                'Required' => '開始時間不能為空',
                'String' => '開始時間必須是字符串',
                'Min' => '開始時間必須有效'
            ],
            'EndTime' => [
                'Required' => '結束時間不能為空',
                'String' => '結束時間必須是字符串',
                'Min' => '結束時間必須有效'
            ],
            'RestTimeList' => [
                'Array' => '休息時間列表必須是數組',
                'StartTime' => [
                    'String' => '休息時間開始時間必須是字符串',
                    'Min' => '休息時間開始時間必須有效'
                ],
                'EndTime' => [
                    'String' => '休息時間結束時間必須是字符串',
                    'Min' => '休息時間結束時間必須有效'
                ]
            ],
            'IsRestDay' => [
                'Boolean' => '是否休息日必須是布爾值'
            ]
        ],
        'SpecialSchedules' => [
            'StartTime' => [
                'Required' => '特殊時間安排開始時間不能為空',
                'String' => '特殊時間安排開始時間必須是字符串',
                'Min' => '特殊時間安排開始時間必須有效'
            ],
            'EndTime' => [
                'Required' => '特殊時間安排結束時間不能為空',
                'String' => '特殊時間安排結束時間必須是字符串',
                'Min' => '特殊時間安排結束時間必須有效'
            ]
        ],
        'StaffIds' => [
            'Required' => '員工ID不能為空',
            'Array' => '員工ID必須是數組',
            'Min' => '至少需要分配一名員工',
            'Exists' => '一個或多個所選員工不存在'
        ]
    ],
    
    // Update Discount Request
    'UpdateDiscount' => [
        'Name' => [
            'Required' => '折扣名稱不能為空',
            'String' => '折扣名稱必須是字符串',
            'Max' => '折扣名稱長度不能超過100個字符',
            'Unique' => '該折扣名稱已被使用'
        ],
        'Code' => [
            'Max' => '折扣代碼長度超出限制',
            'Unique' => '該折扣代碼已被使用'
        ],
        'Type' => [
            'Required' => '折扣類型不能為空',
            'String' => '折扣類型必須是字符串',
            'In' => '選擇的折扣類型無效'
        ],
        'Value' => [
            'Required' => '折扣值不能為空',
            'Numeric' => '折扣值必須是數字',
            'Min' => '折扣值必須大於等於0',
            'Decimal' => '折扣值最多可有2位小數'
        ],
        'StartDate' => [
            'Date' => '開始日期必須是有效的日期'
        ],
        'EndDate' => [
            'Date' => '結束日期必須是有效的日期',
            'AfterOrEqual' => '結束日期必須大於或等於開始日期'
        ]
    ],
    
    // Update Service Category Request
    'UpdateServiceCategory' => [
        'Name' => [
            'String' => '服務分類名稱必須是字符串',
            'Max' => '服務分類名稱長度不能超過50個字符'
        ],
        'Description' => [
            'String' => '描述必須是字符串',
            'Max' => '描述長度不能超過500個字符'
        ],
        'Sort' => [
            'Integer' => '排序必須是整數',
            'Min' => '排序必須大於等於0'
        ],
        'Status' => [
            'Boolean' => '狀態必須是布爾值'
        ]
    ],
    
    // Update Payment Info Request
    'update_payment_info' => [
        'payment_method' => [
            'required' => '支付方式不能為空',
            'string' => '支付方式必須是字符串',
            'max' => '支付方式長度不能超過50個字符'
        ],
        'final_price' => [
            'required' => '支付金額不能為空',
            'numeric' => '支付金額必須是數字',
            'min' => '支付金額必須大於等於0'
        ],
        'payment_status' => [
            'required' => '支付狀態不能為空',
            'integer' => '支付狀態必須是整數',
            'in' => '支付狀態必須是0（未支付）、1（已支付）或2（已退款）'
        ],
        'payment_time' => [
            'date' => '支付時間必須是有效的日期'
        ],
        'transaction_id' => [
            'string' => '交易編號必須是字符串',
            'max' => '交易編號長度不能超過100個字符'
        ],
        'payment_remark' => [
            'string' => '支付備註必須是字符串',
            'max' => '支付備註長度不能超過255個字符'
        ],
        'discount_id' => [
            'numeric' => '折扣ID必須是數字',
            'min' => '折扣ID必須大於等於0'
        ]
    ],
    
    // Update Staff Request
    'UpdateStaff' => [
        'Name' => [
            'Required' => '姓名不能為空',
            'String' => '姓名必須是字符串',
            'Max' => '姓名長度不能超過50個字符'
        ],
        'Email' => [
            'Required' => '郵箱不能為空',
            'Email' => '請輸入有效的郵箱地址'
        ],
        'Phone' => [
            'String' => '手機號必須是字符串',
            'Max' => '手機號長度不能超過20個字符'
        ],
        'PositionId' => [
            'Integer' => '職位ID必須是整數'
        ],
        'DepartmentId' => [
            'Integer' => '部門ID必須是整數'
        ],
        'Location' => [
            'String' => '地點必須是字符串',
            'Max' => '地點長度不能超過50個字符'
        ],
        'Description' => [
            'String' => '描述必須是字符串',
            'Max' => '描述長度不能超過255個字符'
        ]
    ],
    
    // Update Location Request
    'UpdateLocation' => [
        'Name' => [
            'String' => '地點名稱必須是字符串',
            'Max' => '地點名稱長度不能超過50個字符'
        ],
        'Address' => [
            'String' => '地址必須是字符串',
            'Max' => '地址長度不能超過255個字符'
        ],
        'Description' => [
            'String' => '描述必須是字符串',
            'Max' => '描述長度不能超過500個字符'
        ]
    ],
    
    // Update Status Appointment Request
    'UpdateStatusAppointment' => [
        'Status' => [
            'Required' => '預約狀態不能為空',
            'String' => '預約狀態必須是字符串',
        ],
        'Reason' => [
            'String' => '原因必須是字符串',
            'Max' => '原因不能超過500個字符',
        ],
    ],
    
    // Save Service Schedules Request
    'SaveServiceSchedules' => [
        'Schedules' => [
            'Required' => '服務排班不能為空',
            'Array' => '服務排班必須是數組格式',
            'DayOfWeek' => [
                'Required' => '星期幾不能為空',
                'Integer' => '星期幾必須是整數',
                'Min' => '星期幾最小值為1',
                'Max' => '星期幾最大值為7',
            ],
            'StartTime' => [
                'Required' => '開始時間不能為空',
                'DateFormat' => '開始時間必須是HH:mm格式',
            ],
            'EndTime' => [
                'Required' => '結束時間不能為空',
                'DateFormat' => '結束時間必須是HH:mm格式',
                'After' => '結束時間必須晚於開始時間',
            ],
            'IsRestDay' => [
                'Boolean' => '休息日標識必須是布爾值',
            ],
        ],
    ],
    'SaveServiceSpecialSchedules' => [
        'Date' => [
            'Required' => '日期不能為空',
            'DateFormat' => '日期必須是YYYY-MM-DD格式',
        ],
        'Schedules' => [
            'Required' => '特殊排班不能為空',
            'Array' => '特殊排班必須是數組格式',
            'StartTime' => [
                'Required' => '開始時間不能為空',
                'DateFormat' => '開始時間必須是HH:mm格式',
            ],
            'EndTime' => [
                'Required' => '結束時間不能為空',
                'DateFormat' => '結束時間必須是HH:mm格式',
                'After' => '結束時間必須晚於開始時間',
            ],
            'IsRestDay' => [
                'Boolean' => '休息日標識必須是布爾值',
            ],
        ],
    ],
    'SaveServicePaymentMethods' => [
        'PaymentMethods' => [
            'Required' => '支付方式不能為空',
            'Array' => '支付方式必須是數組格式',
            '*' => [
                'Required' => '每個支付方式不能為空',
                'String' => '每個支付方式必須是字符串',
                'In' => '無效的支付方式，必須是：現金、微信、支付寶、銀行卡其中之一',
            ],
        ],
    ],
    'ListStaff' => [
        'Page' => [
            'Integer' => '頁碼必須是整數',
            'Min' => '頁碼必須大於等於1',
        ],
        'PerPage' => [
            'Integer' => '每頁數量必須是整數',
            'Min' => '每頁數量必須大於等於1',
            'Max' => '每頁數量不能超過100',
        ],
        'Keyword' => [
            'String' => '關鍵詞必須是字符串',
            'Max' => '關鍵詞長度不能超過50個字符',
        ],
        'Status' => [
            'Integer' => '狀態必須是整數',
            'In' => '狀態必須是0（禁用）或1（啟用）',
        ],
        'LocationId' => [
            'Integer' => '地點ID必須是整數',
            'Exists' => '所選地點不存在',
        ],
        'SortField' => [
            'String' => '排序字段必須是字符串',
            'In' => '排序字段必須是id、created_at或updated_at之一',
        ],
        'SortOrder' => [
            'String' => '排序方式必須是字符串',
            'In' => '排序方式必須是asc（升序）或desc（降序）',
        ],
    ],
    'ListService' => [
        'Keyword' => [
            'String' => '關鍵詞必須是字符串',
            'Max' => '關鍵詞長度不能超過50個字符',
        ],
        'Name' => [
            'Max' => '服務名稱長度不能超過50個字符',
        ],
        'CategoryId' => [
            'Integer' => '分類ID必須是整數',
            'Exists' => '所選分類不存在',
        ],
        'Status' => [
            'Boolean' => '狀態必須是布爾值',
        ],
        'IsVisible' => [
            'Boolean' => '可見性必須是布爾值',
        ],
        'Page' => [
            'Integer' => '頁碼必須是整數',
            'Min' => '頁碼必須大於等於1',
        ],
        'Limit' => [
            'Integer' => '每頁數量必須是整數',
            'Min' => '每頁數量必須大於等於1',
            'Max' => '每頁數量不能超過100',
        ],
    ],
    'ListServiceCategory' => [
        'Name' => [
            'Max' => '分類名稱長度不能超過50個字符',
        ],
        'Status' => [
            'Boolean' => '狀態必須是布爾值',
        ],
        'Page' => [
            'Integer' => '頁碼必須是整數',
            'Min' => '頁碼必須大於等於1',
        ],
        'Limit' => [
            'Integer' => '每頁數量必須是整數',
            'Min' => '每頁數量必須大於等於1',
            'Max' => '每頁數量不能超過100',
        ],
    ],
    'ListLocation' => [
        'Name' => [
            'Max' => '地點名稱長度不能超過50個字符',
        ],
        'Page' => [
            'Integer' => '頁碼必須是整數',
            'Min' => '頁碼必須大於等於1',
        ],
        'Limit' => [
            'Integer' => '每頁數量必須是整數',
            'Min' => '每頁數量必須大於等於1',
            'Max' => '每頁數量不能超過100',
        ],
    ],
    // Error messages from ErrorCode
    'error' => [
        'unknown_error' => '未知錯誤',
        'invalid_params' => '無效的參數',
        'unauthorized' => '未授權',
        'forbidden' => '禁止訪問',
        'not_found' => '資源不存在',
        'method_not_allowed' => '方法不允許',
        'validation_error' => '驗證錯誤',
        'service_unavailable' => '服務不可用',
        'setting_not_found' => '設置項不存在',

        // Business errors
        'business_error' => '業務錯誤',
        'data_not_found' => '數據不存在',
        'data_already_exists' => '數據已存在',
        'data_validation_failed' => '數據驗證失敗',
        'operation_failed' => '操作失敗',
        'status_invalid' => '狀態無效',
        'permission_denied' => '權限不足',

        // Appointment errors
        'appointment_not_found' => '預約不存在',
        'appointment_already_exists' => '預約已存在',
        'appointment_status_invalid' => '預約狀態無效',
        'appointment_time_conflict' => '預約時間衝突',
        'appointment_capacity_exceeded' => '預約人數已滿',
        'appointment_cancel_failed' => '取消預約失敗',
        'appointment_update_failed' => '更新預約失敗',
        'appointment_export_failed' => '導出預約失敗',
        'appointment_import_failed' => '導入預約失敗',
        'appointment_cannot_delete' => '無法刪除預約',
        'appointment_cannot_update' => '無法更新預約',
        'appointment_expired' => '預約已過期',
        'appointment_already_paid' => '預約已支付',
        'payment_update_failed' => '更新支付信息失敗',

        // Service errors
        'service_not_found' => '服務不存在',
        'service_unavailable_time' => '服務時間不可用',
        'service_staff_not_available' => '服務人員不可用',
        'service_location_not_available' => '服務地點不可用',
        'service_has_appointments' => '服務存在預約',
        'service_delete_failed' => '刪除服務失敗',

        // Customer errors
        'customer_not_found' => '客戶不存在',
        'customer_blacklisted' => '客戶已被列入黑名單',
        'customer_appointment_limit_exceeded' => '客戶預約次數已達上限',
        'customer_already_exists' => '客戶已存在',
        'customer_email_exists' => '客戶郵箱已存在',
        'customer_phone_exists' => '客戶手機號已存在',
        'customer_has_appointments' => '客戶存在預約',
        'customer_export_failed' => '導出客戶數據失敗',
        'customer_delete_failed' => '刪除客戶失敗',
        'customer_delete_failed_batch' => '刪除客戶失敗',
        'customer_import_failed' => '導入客戶數據失敗',

        // Save errors
        'save_extra_service_failed' => '保存額外服務失敗',
        'save_special_schedule_failed' => '保存特殊排班失敗',
        'save_staff_failed' => '保存員工失敗',
        'save_schedule_failed' => '保存排班失敗',

        // Discount errors
        'discount_not_found' => '折扣不存在',
        'discount_name_exists' => '折扣名稱已存在',
        'discount_in_use' => '折扣正在使用中',
        'discount_create_failed' => '創建折扣失敗',
        'discount_update_failed' => '更新折扣失敗',
        'invalid_discount_type' => '無效的折扣類型',
        'invalid_discount_status' => '無效的折扣狀態',
        'invalid_date_range' => '無效的日期範圍',

        // Staff errors
        'staff_not_found' => '員工不存在',
        'staff_already_exists' => '員工已存在',
        'staff_delete_failed' => '刪除員工失敗',
        'staff_update_failed' => '更新員工失敗',
        'email_already_exists' => '郵箱已存在',
        'phone_already_exists' => '手機號已存在',
        'position_not_found' => '職位不存在',
        'department_not_found' => '部門不存在',
        'staff_export_failed' => '導出員工數據失敗',
        'staff_import_failed' => '導入員工數據失敗',
        'staff_import_failed_message' => '導入員工數據失敗，成功：:success_count，失敗：:fail_count',

        // Email template errors
        'email_template_variable_is_not_valid' => '郵件模板變量無效',
        'email_template_variable_is_not_valid_message' => '郵件模板變量：:variable 無效，有效變量：:valid_variables',
    ],
    'import_template' => [
        'customer_email_required' => '客戶郵箱不能為空',
        'customer_email_email' => '請輸入有效的郵箱地址',
        'customer_phone_required' => '客戶手機號不能為空',
        'customer_phone_string' => '手機號必須是字符串',
        'customer_phone_max' => '手機號長度不能超過20個字符',
        'customer_name_required' => '客戶姓名不能為空',
        'customer_name_string' => '姓名必須是字符串',
        'customer_name_max' => '姓名長度不能超過50個字符',
        'customer_gender_string' => '性別必須是字符串',
        'customer_gender_max' => '性別長度不能超過10個字符',
        'service_name_required' => '服務名稱不能為空',
        'service_name_string' => '服務名稱必須是字符串',
        'service_name_max' => '服務名稱長度不能超過100個字符',
        'location_required' => '地點不能為空',
        'location_string' => '地點必須是字符串',
        'location_max' => '地點長度不能超過50個字符',
        'appointment_date_required' => '預約日期不能為空',
        'appointment_date_date' => '預約日期必須是有效的日期',
        'status_string' => '狀態必須是字符串',
        'status_max' => '狀態長度不能超過20個字符',
        'discount_name_string' => '折扣名稱必須是字符串',
        'discount_name_max' => '折扣名稱長度不能超過100個字符',
        'payment_method_string' => '支付方式必須是字符串',
        'payment_method_max' => '支付方式長度不能超過50個字符',
        'payment_status_string' => '支付狀態必須是字符串',
        'payment_status_max' => '支付狀態長度不能超過20個字符',
        'payment_time_date' => '支付時間必須是有效的日期',
        'remark_string' => '備註必須是字符串',
        'remark_max' => '備註長度不能超過500個字符',
    ],
    'SaveGeneralSetting' => [
        'AppointmentLimit' => [
            'String' => '預約限制必須是字符串',
            'In' => '預約限制必須是no_limit或member_only',
        ],
        'NonMemberRecord' => [
            'Boolean' => '非會員預約記錄必須是布爾值',
        ],
        'TimeSlotInterval' => [
            'Integer' => '時段粒度必須是整數',
            'In' => '時段粒度必須是5,10,15,30,60分鐘',
        ],
        'CrossTimeSlot' => [
            'Boolean' => '支持跨時段預約必須是布爾值',
        ],
        'CrossTimeSlotReview' => [
            'Boolean' => '跨時段需要審核必須是布爾值',
        ],
        'AdvanceBookingTime' => [
            'Integer' => '提前預約時間必須是整數',
        ],
        'MaxBookingDays' => [
            'Integer' => '預約天數限制必須是整數',
        ],
        'DefaultAppointmentStatus' => [
            'Integer' => '預約狀態必須是整數',
            'In' => '預約狀態必須是0,1,2,3,4,5,6',
        ],
        'DefaultPaymentStatus' => [
            'Integer' => '支付狀態必須是整數',
            'In' => '支付狀態必須是0,1,2',
        ],
        'AllowOvertimeBooking' => [
            'Boolean' => '允許超時預約必須是布爾值',
        ],
        'AllowAdminOvertimeBooking' => [
            'Boolean' => '允許管理員在非工作時間預約必須是布爾值',
        ],  
        'CustomerId' => [
            'MemberOnly' => '客戶必須是會員',
        ],
        'AppointmentRescheduleNotification' => [
            'String' => '預約改期通知必須是字符串',
        ],
        'AppointmentCancelNotification' => [
            'String' => '預約取消通知必須是字符串',
        ],
        'AppointmentConfirmNotification' => [
            'String' => '預約確認通知必須是字符串',
        ],  
        'HolidaySettings' => [
            'Array' => '假期設置必須是數組',
        ],
        'HolidaySettings.Start' => [
            'Required' => '假期開始日期不能為空',
        ],  
        'HolidaySettings.End' => [
            'Required' => '假期結束日期不能為空',
        ],
    ],
    
    // 電子郵件模板驗證
    'store_email_template' => [
        'name' => [
            'required' => '模板名稱不能為空',
            'string' => '模板名稱必須是字符串',
            'max' => '模板名稱長度不能超過100個字符'
        ],
        'code' => [
            'required' => '模板代碼不能為空',
            'string' => '模板代碼必須是字符串',
            'max' => '模板代碼長度不能超過50個字符'
        ],
        'subject' => [
            'required' => '郵件主題不能為空',
            'string' => '郵件主題必須是字符串',
            'max' => '郵件主題長度不能超過200個字符'
        ],
        'content' => [
            'required' => '郵件內容不能為空',
            'string' => '郵件內容必須是字符串'
        ],
        'status' => [
            'boolean' => '狀態必須是布爾值'
        ],
        'to' => [
            'required' => '郵件收件人不能為空',
            'string' => '郵件收件人必須是字符串',
            'max' => '郵件收件人長度不能超過100個字符'
        ],
        'attachments' => [
            'array' => '附件必須是數組'
        ]
    ],
    
    'update_email_template' => [
        'name' => [
            'required' => '模板名稱不能為空',
            'string' => '模板名稱必須是字符串',
            'max' => '模板名稱長度不能超過100個字符'
        ],
        'code' => [
            'required' => '模板代碼不能為空',
            'string' => '模板代碼必須是字符串',
            'max' => '模板代碼長度不能超過50個字符'
        ],
        'subject' => [
            'required' => '郵件主題不能為空',
            'string' => '郵件主題必須是字符串',
            'max' => '郵件主題長度不能超過200個字符'
        ],
        'content' => [
            'required' => '郵件內容不能為空',
            'string' => '郵件內容必須是字符串'
        ]
    ],
    
    'list_email_template' => [
        'keyword' => [
            'string' => '關鍵詞必須是字符串',
            'max' => '關鍵詞長度不能超過50個字符'
        ],
        'name' => [
            'string' => '模板名稱必須是字符串',
            'max' => '模板名稱長度不能超過100個字符'
        ],
        'code' => [
            'string' => '模板代碼必須是字符串',
            'max' => '模板代碼長度不能超過50個字符'
        ],
        'status' => [
            'integer' => '狀態必須是整數',
            'in' => '狀態必須是0（禁用）或1（啟用）'
        ],
        'page' => [
            'integer' => '頁碼必須是整數',
            'min' => '頁碼必須大於等於1'
        ],
        'limit' => [
            'integer' => '每頁數量必須是整數',
            'min' => '每頁數量必須大於等於1',
            'max' => '每頁數量不能超過100'
        ],
        'sort_field' => [
            'string' => '排序字段必須是字符串',
            'in' => '排序字段必須是id、name、code、sort、created_at或updated_at之一'
        ],
        'sort_order' => [
            'string' => '排序方式必須是字符串',
            'in' => '排序方式必須是asc（升序）或desc（降序）'
        ]
    ],
    
    // Store Appointment Request
    'StoreAppointment' => [
        'Location' => [
            'Required' => '預約地點不能為空',
            'String' => '預約地點必須是字符串'
        ],
        'ServiceId' => [
            'Required' => '服務ID不能為空',
            'Integer' => '服務ID必須是整數'
        ],
        'AppointmentDate' => [
            'Required' => '預約日期不能為空',
            'DateTime' => '預約日期必須是有效的日期和時間',
            'AfterOrEqual' => '預約日期必須是今天或將來的日期'
        ],
        'DiscountId' => [
            'Integer' => '折扣ID必須是整數'
        ],
        'Remark' => [
            'String' => '備註必須是字符串',
            'Max' => '備註長度不能超過500個字符'
        ],
        'CustomerId' => [
            'Required' => '客戶ID不能為空',
            'Integer' => '客戶ID必須是整數',
            'MemberOnly' => '只有會員才能預約'
        ],
        'Status' => [
            'Required' => '狀態不能為空',
            'Integer' => '狀態必須是整數'
        ]
    ],
];
