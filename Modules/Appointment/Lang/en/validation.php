<?php

return [
    // Store Customer Request
    'StoreCustomer' => [
        'Name' => [
            'Required' => 'Name is required',
            'String' => 'Name must be a string',
            'Max' => 'Name cannot exceed 50 characters'
        ],
        'Email' => [
            'Required' => 'Email is required',
            'Email' => 'Please enter a valid email address'
        ],
        'Phone' => [
            'Required' => 'Phone is required',
            'String' => 'Phone must be a string',
            'Max' => 'Phone cannot exceed 20 characters'
        ],
        'Gender' => [
            'Required' => 'Gender is required',
            'Integer' => 'Gender must be an integer',
            'In' => 'Gender must be either 1 (Male) or 2 (Female)'
        ],
        'Birthday' => [
            'Date' => 'Birthday must be a valid date'
        ],
        'Description' => [
            'String' => 'Description must be a string',
            'Max' => 'Description cannot exceed 500 characters'
        ],
        'Photo' => [
            'String' => 'Photo must be a string',
            'Max' => 'Photo URL cannot exceed 255 characters'
        ]
    ],
    
    // List Appointment Request
    'ListAppointment' => [
        'Page' => [
            'Integer' => 'Page must be an integer',
            'Min' => 'Page must be at least 1'
        ],
        'Limit' => [
            'Integer' => 'Limit must be an integer',
            'Min' => 'Limit must be at least 1',
            'Max' => 'Limit cannot exceed 100'
        ],
        'Keyword' => [
            'String' => 'Keyword must be a string',
            'Max' => 'Keyword cannot exceed 100 characters'
        ],
        'CustomerId' => [
            'Integer' => 'Customer ID must be an integer'
        ],
        'Status' => [
            'String' => 'Status must be a string'
        ],
        'SortField' => [
            'String' => 'Sort field must be a string',
            'In' => 'Sort field must be one of: id, appointment_date, created_at, updated_at'
        ],
        'SortOrder' => [
            'String' => 'Sort order must be a string',
            'In' => 'Sort order must be either asc or desc'
        ]
    ],
    
    // Store Staff Request
    'StoreStaff' => [
        'Name' => [
            'Required' => 'Name is required',
            'String' => 'Name must be a string',
            'Max' => 'Name cannot exceed 50 characters'
        ],
        'Email' => [
            'Required' => 'Email is required',
            'Email' => 'Please enter a valid email address'
        ],
        'Phone' => [
            'String' => 'Phone must be a string',
            'Max' => 'Phone cannot exceed 20 characters'
        ],
        'PositionId' => [
            'Integer' => 'Position ID must be an integer'
        ],
        'DepartmentId' => [
            'Integer' => 'Department ID must be an integer'
        ],
        'Location' => [
            'String' => 'Location must be a string',
            'Max' => 'Location cannot exceed 50 characters'
        ],
        'Description' => [
            'String' => 'Description must be a string',
            'Max' => 'Description cannot exceed 255 characters'
        ]
    ],
    
    // Store Appointment Request
    'StoreAppointment' => [
        'Location' => [
            'Required' => 'Location cannot be empty',
            'String' => 'Location must be a string'
        ],
        'ServiceId' => [
            'Required' => 'Service ID cannot be empty',
            'Integer' => 'Service ID must be an integer'
        ],
        'AppointmentDate' => [
            'Required' => 'Appointment date cannot be empty',
            'DateTime' => 'Appointment date must be a valid date and time',
            'AfterOrEqual' => 'Appointment date must be today or a future date'
        ],
        'DiscountId' => [
            'Integer' => 'Discount ID must be an integer'
        ],
        'Remark' => [
            'String' => 'Remark must be a string',
            'Max' => 'Remark length cannot exceed 500 characters'
        ],
        'CustomerId' => [
            'Required' => 'Customer ID cannot be empty',
            'Integer' => 'Customer ID must be an integer',
            'MemberOnly' => 'Only members can make appointments'
        ],
        'Status' => [
            'Required' => 'Status cannot be empty',
            'Integer' => 'Status must be an integer'
        ]
    ],
    
    // Store Service Request
    'StoreService' => [
        'CategoryId' => [
            'Required' => 'Category ID is required',
            'Integer' => 'Category ID must be an integer',
            'Exists' => 'The selected category does not exist'
        ],
        'Name' => [
            'Required' => 'Service name is required',
            'String' => 'Service name must be a string',
            'Max' => 'Service name cannot exceed 100 characters'
        ],
        'Price' => [
            'Required' => 'Price is required',
            'Numeric' => 'Price must be a number',
            'Min' => 'Price must be at least 0'
        ],
        'Duration' => [
            'Required' => 'Duration is required',
            'Integer' => 'Duration must be an integer',
            'Min' => 'Duration must be at least 1'
        ],
        'IsRepeatServicePayType' => [
            'RequiredIf' => 'Repeat service payment type is required when repeat service is enabled'
        ],
        'IsRepeatServiceCategory' => [
            'RequiredIf' => 'Repeat service category is required when repeat service is enabled'
        ],
        'IsRepeatServiceCount' => [
            'RequiredIf' => 'Repeat service count is required when repeat service is enabled',
            'Min' => 'Repeat service count must be at least 1'
        ],
        'PersonCount' => [
            'Required' => 'Person count is required',
            'Integer' => 'Person count must be an integer',
            'Min' => 'Person count must be at least 1'
        ],
        'PayTypes' => [
            'Required' => 'Payment types are required',
            'Array' => 'Payment types must be an array'
        ],
        'AdvanceBookingTime' => [
            'Integer' => 'Advance booking time must be an integer',
            'Min' => 'Advance booking time must be at least 0'
        ],
        'Status' => [
            'Required' => 'Status is required',
            'Boolean' => 'Status must be true or false'
        ],
        'ExtraServices' => [
            'Name' => [
                'Required' => 'Extra service name is required',
                'String' => 'Extra service name must be a string',
                'Max' => 'Extra service name cannot exceed 100 characters'
            ],
            'CategoryName' => [
                'String' => 'Extra service category name must be a string',
                'Max' => 'Extra service category name cannot exceed 100 characters'
            ]
        ],
        'Schedules' => [
            'DayOfWeek' => [
                'Required' => 'Day of week is required',
                'Integer' => 'Day of week must be an integer',
                'Min' => 'Day of week must be at least 1',
                'Max' => 'Day of week cannot exceed 7'
            ],
            'StartTime' => [
                'Required' => 'Start time is required',
                'String' => 'Start time must be a string',
                'Min' => 'Start time must be valid'
            ],
            'EndTime' => [
                'Required' => 'End time is required',
                'String' => 'End time must be a string',
                'Min' => 'End time must be valid'
            ],
            'RestTimeList' => [
                'Array' => 'Rest time list must be an array',
                'StartTime' => [
                    'String' => 'Rest time start time must be a string',
                    'Min' => 'Rest time start time must be valid'
                ],
                'EndTime' => [
                    'String' => 'Rest time end time must be a string',
                    'Min' => 'Rest time end time must be valid'
                ]
            ],
            'IsRestDay' => [
                'Boolean' => 'Is rest day must be true or false'
            ]
        ],
        'SpecialSchedules' => [
            'StartTime' => [
                'Required' => 'Special schedule start time is required',
                'String' => 'Special schedule start time must be a string',
                'Min' => 'Special schedule start time must be valid'
            ],
            'EndTime' => [
                'Required' => 'Special schedule end time is required',
                'String' => 'Special schedule end time must be a string',
                'Min' => 'Special schedule end time must be valid'
            ]
        ],
        'StaffIds' => [
            'Required' => 'Staff IDs are required',
            'Array' => 'Staff IDs must be an array',
            'Min' => 'At least one staff member must be assigned',
            'Exists' => 'One or more selected staff members do not exist'
        ]
    ],
    
    // Export Customer (kept for backward compatibility)
    'export_customer' => [
        'id' => 'ID',
        'name' => 'Name',
        'email' => 'Email',
        'phone' => 'Phone',
        'gender' => 'Gender',
        'birthdate' => 'Birth Date',
        'appointment_count' => 'Appointment Count',
        'last_appointment_at' => 'Last Appointment',
        'created_at' => 'Created At',
    ],
    
    // Import Customer (kept for backward compatibility)
    'import_customer' => [
        'name' => 'Name',
        'email' => 'Email',
        'phone' => 'Phone',
        'gender' => 'Gender',
        'birthdate' => 'Birth Date',
        'description' => 'Description',
    ],
    
    // Import Staff Request
    'import_staff' => [
        'file' => [
            'required' => 'Please select a file to import',
            'file' => 'The upload must be a file',
            'mimes' => 'The file must be in xlsx, xls, or csv format',
            'max' => 'The file size cannot exceed 10MB',
        ],
    ],
    
    // Update Service Request
    'UpdateService' => [
        'CategoryId' => [
            'Required' => 'Category ID is required',
            'Integer' => 'Category ID must be an integer',
            'Exists' => 'Selected category does not exist'
        ],
        'Name' => [
            'Required' => 'Service name is required',
            'String' => 'Service name must be a string',
            'Max' => 'Service name cannot exceed 100 characters'
        ],
        'Price' => [
            'Required' => 'Price is required',
            'Numeric' => 'Price must be a number',
            'Min' => 'Price must be greater than or equal to 0'
        ],
        'Duration' => [
            'Required' => 'Duration is required',
            'Integer' => 'Duration must be an integer',
            'Min' => 'Duration must be greater than or equal to 1'
        ],
        'IsRepeatServicePayType' => [
            'RequiredIf' => 'Repeat service payment type is required when repeat service is enabled'
        ],
        'IsRepeatServiceCategory' => [
            'RequiredIf' => 'Repeat service category is required when repeat service is enabled'
        ],
        'IsRepeatServiceCount' => [
            'RequiredIf' => 'Repeat service count is required when repeat service is enabled',
            'Min' => 'Repeat service count must be greater than or equal to 1'
        ],
        'PersonCount' => [
            'Required' => 'Person count is required',
            'Integer' => 'Person count must be an integer',
            'Min' => 'Person count must be greater than or equal to 1'
        ],
        'PayTypes' => [
            'Required' => 'Payment types are required',
            'Array' => 'Payment types must be an array'
        ],
        'AdvanceBookingTime' => [
            'Integer' => 'Advance booking time must be an integer',
            'Min' => 'Advance booking time must be greater than or equal to 0'
        ],
        'Status' => [
            'Required' => 'Status is required',
            'Boolean' => 'Status must be a boolean value'
        ],
        'ExtraServices' => [
            'Name' => [
                'Required' => 'Extra service name is required',
                'String' => 'Extra service name must be a string',
                'Max' => 'Extra service name cannot exceed 100 characters'
            ],
            'CategoryName' => [
                'String' => 'Extra service category name must be a string',
                'Max' => 'Extra service category name cannot exceed 100 characters'
            ]
        ],
        'Schedules' => [
            'DayOfWeek' => [
                'Required' => 'Day of week is required',
                'Integer' => 'Day of week must be an integer',
                'Min' => 'Day of week must be greater than or equal to 1',
                'Max' => 'Day of week cannot exceed 7'
            ],
            'StartTime' => [
                'Required' => 'Start time is required',
                'String' => 'Start time must be a string',
                'Min' => 'Start time must be valid'
            ],
            'EndTime' => [
                'Required' => 'End time is required',
                'String' => 'End time must be a string',
                'Min' => 'End time must be valid'
            ],
            'RestTimeList' => [
                'Array' => 'Rest time list must be an array',
                'StartTime' => [
                    'String' => 'Rest time start time must be a string',
                    'Min' => 'Rest time start time must be valid'
                ],
                'EndTime' => [
                    'String' => 'Rest time end time must be a string',
                    'Min' => 'Rest time end time must be valid'
                ]
            ],
            'IsRestDay' => [
                'Boolean' => 'Is rest day must be a boolean value'
            ]
        ],
        'SpecialSchedules' => [
            'StartTime' => [
                'Required' => 'Special schedule start time is required',
                'String' => 'Special schedule start time must be a string',
                'Min' => 'Special schedule start time must be valid'
            ],
            'EndTime' => [
                'Required' => 'Special schedule end time is required',
                'String' => 'Special schedule end time must be a string',
                'Min' => 'Special schedule end time must be valid'
            ]
        ],
        'StaffIds' => [
            'Required' => 'Staff IDs are required',
            'Array' => 'Staff IDs must be an array',
            'Min' => 'At least one staff member must be assigned',
            'Exists' => 'One or more selected staff members do not exist'
        ]
    ],
    
    // Update Discount Request
    'UpdateDiscount' => [
        'Name' => [
            'Required' => 'Discount name is required',
            'String' => 'Discount name must be a string',
            'Max' => 'Discount name cannot exceed 100 characters',
            'Unique' => 'This discount name has already been taken'
        ],
        'Code' => [
            'Max' => 'Discount code exceeds maximum length',
            'Unique' => 'This discount code has already been taken'
        ],
        'Type' => [
            'Required' => 'Discount type is required',
            'String' => 'Discount type must be a string',
            'In' => 'Selected discount type is invalid'
        ],
        'Value' => [
            'Required' => 'Discount value is required',
            'Numeric' => 'Discount value must be a number',
            'Min' => 'Discount value must be greater than or equal to 0',
            'Decimal' => 'Discount value cannot have more than 2 decimal places'
        ],
        'StartDate' => [
            'Date' => 'Start date must be a valid date'
        ],
        'EndDate' => [
            'Date' => 'End date must be a valid date',
            'AfterOrEqual' => 'End date must be after or equal to start date'
        ]
    ],

    // Update Service Category Request
    'UpdateServiceCategory' => [
        'Name' => [
            'String' => 'Service category name must be a string',
            'Max' => 'Service category name cannot exceed 50 characters'
        ],
        'Description' => [
            'String' => 'Description must be a string',
            'Max' => 'Description cannot exceed 500 characters'
        ],
        'Sort' => [
            'Integer' => 'Sort must be an integer',
            'Min' => 'Sort must be greater than or equal to 0'
        ],
        'Status' => [
            'Boolean' => 'Status must be a boolean value'
        ]
    ],
    
    // Update Payment Info Request
    'update_payment_info' => [
        'payment_method' => [
            'required' => 'Payment method is required',
            'string' => 'Payment method must be a string',
            'max' => 'Payment method cannot exceed 50 characters'
        ],
        'final_price' => [
            'required' => 'Payment amount is required',
            'numeric' => 'Payment amount must be a number',
            'min' => 'Payment amount must be greater than or equal to 0'
        ],
        'payment_status' => [
            'required' => 'Payment status is required',
            'integer' => 'Payment status must be an integer',
            'in' => 'Payment status must be 0 (Unpaid), 1 (Paid), or 2 (Refunded)'
        ],
        'payment_time' => [
            'date' => 'Payment time must be a valid date'
        ],
        'transaction_id' => [
            'string' => 'Transaction ID must be a string',
            'max' => 'Transaction ID cannot exceed 100 characters'
        ],
        'payment_remark' => [
            'string' => 'Payment remark must be a string',
            'max' => 'Payment remark cannot exceed 255 characters'
        ],
        'discount_id' => [
            'numeric' => 'Discount ID must be a number',
            'min' => 'Discount ID must be greater than or equal to 0'
        ]
    ],

    // Update Staff Request
    'UpdateStaff' => [
        'Name' => [
            'Required' => 'Name is required',
            'String' => 'Name must be a string',
            'Max' => 'Name cannot exceed 50 characters'
        ],
        'Email' => [
            'Required' => 'Email is required',
            'Email' => 'Please enter a valid email address'
        ],
        'Phone' => [
            'String' => 'Phone must be a string',
            'Max' => 'Phone cannot exceed 20 characters'
        ],
        'PositionId' => [
            'Integer' => 'Position ID must be an integer'
        ],
        'DepartmentId' => [
            'Integer' => 'Department ID must be an integer'
        ],
        'Location' => [
            'String' => 'Location must be a string',
            'Max' => 'Location cannot exceed 50 characters'
        ],
        'Description' => [
            'String' => 'Description must be a string',
            'Max' => 'Description cannot exceed 255 characters'
        ]
    ],

    // Update Location Request
    'UpdateLocation' => [
        'Name' => [
            'String' => 'Location name must be a string',
            'Max' => 'Location name cannot exceed 50 characters'
        ],
        'Address' => [
            'String' => 'Address must be a string',
            'Max' => 'Address cannot exceed 255 characters'
        ],
        'Description' => [
            'String' => 'Description must be a string',
            'Max' => 'Description cannot exceed 500 characters'
        ]
    ],

    'UpdateStatusAppointment' => [
        'Status' => [
            'Required' => 'The appointment status is required.',
            'String' => 'The appointment status must be a string.',
        ],
        'Reason' => [
            'String' => 'The reason must be a string.',
            'Max' => 'The reason cannot exceed 500 characters.',
        ],
    ],
    'SaveServiceSchedules' => [
        'Schedules' => [
            'Required' => 'The service schedules are required.',
            'Array' => 'The service schedules must be an array.',
            'DayOfWeek' => [
                'Required' => 'The day of week is required.',
                'Integer' => 'The day of week must be an integer.',
                'Min' => 'The day of week must be at least 1.',
                'Max' => 'The day of week cannot exceed 7.',
            ],
            'StartTime' => [
                'Required' => 'The start time is required.',
                'DateFormat' => 'The start time must be in HH:mm format.',
            ],
            'EndTime' => [
                'Required' => 'The end time is required.',
                'DateFormat' => 'The end time must be in HH:mm format.',
                'After' => 'The end time must be after the start time.',
            ],
            'IsRestDay' => [
                'Boolean' => 'The rest day indicator must be a boolean value.',
            ],
        ],
    ],
    'SaveServiceSpecialSchedules' => [
        'Date' => [
            'Required' => 'The date is required.',
            'DateFormat' => 'The date must be in YYYY-MM-DD format.',
        ],
        'Schedules' => [
            'Required' => 'The special schedules are required.',
            'Array' => 'The special schedules must be an array.',
            'StartTime' => [
                'Required' => 'The start time is required.',
                'DateFormat' => 'The start time must be in HH:mm format.',
            ],
            'EndTime' => [
                'Required' => 'The end time is required.',
                'DateFormat' => 'The end time must be in HH:mm format.',
                'After' => 'The end time must be after the start time.',
            ],
            'IsRestDay' => [
                'Boolean' => 'The rest day indicator must be a boolean value.',
            ],
        ],
    ],
    'SaveServicePaymentMethods' => [
        'PaymentMethods' => [
            'Required' => 'The payment methods are required.',
            'Array' => 'The payment methods must be an array.',
            '*' => [    
                'Required' => 'Each payment method is required.',
                'String' => 'Each payment method must be a string.',
                'In' => 'Invalid payment method. Must be one of: cash, wechat, alipay, bank_card.',
            ],
        ],
    ],
    'ListStaff' => [
        'Page' => [
            'Integer' => 'Page must be an integer.',
            'Min' => 'Page must be at least 1.',
        ],
        'PerPage' => [
            'Integer' => 'Items per page must be an integer.',
            'Min' => 'Items per page must be at least 1.',
            'Max' => 'Items per page cannot exceed 100.',
        ],
        'Keyword' => [
            'String' => 'Keyword must be a string.',
            'Max' => 'Keyword cannot exceed 50 characters.',
        ],
        'Status' => [
            'Integer' => 'Status must be an integer.',
            'In' => 'Status must be either 0 (inactive) or 1 (active).',
        ],
        'LocationId' => [
            'Integer' => 'Location ID must be an integer.',
            'Exists' => 'The selected location does not exist.',
        ],
        'SortField' => [
            'String' => 'Sort field must be a string.',
            'In' => 'Sort field must be one of: id, created_at, updated_at.',
        ],
        'SortOrder' => [
            'String' => 'Sort order must be a string.',
            'In' => 'Sort order must be either asc or desc.',
        ],
    ],
    'ListService' => [
        'Keyword' => [
            'String' => 'Keyword must be a string.',
            'Max' => 'Keyword cannot exceed 50 characters.',
        ],
        'Name' => [
            'Max' => 'Service name cannot exceed 50 characters.',
        ],
        'CategoryId' => [
            'Integer' => 'Category ID must be an integer.',
            'Exists' => 'The selected category does not exist.',
        ],
        'Status' => [
            'Boolean' => 'Status must be a boolean value.',
        ],
        'IsVisible' => [
            'Boolean' => 'Visibility must be a boolean value.',
        ],
        'Page' => [
            'Integer' => 'Page must be an integer.',
            'Min' => 'Page must be at least 1.',
        ],
        'Limit' => [
            'Integer' => 'Items per page must be an integer.',
            'Min' => 'Items per page must be at least 1.',
            'Max' => 'Items per page cannot exceed 100.',
        ],
    ],
    'ListServiceCategory' => [
        'Name' => [
            'Max' => 'Category name cannot exceed 50 characters.',
        ],
        'Status' => [
            'Boolean' => 'Status must be a boolean value.',
        ],
        'Page' => [
            'Integer' => 'Page must be an integer.',
            'Min' => 'Page must be at least 1.',
        ],
        'Limit' => [
            'Integer' => 'Items per page must be an integer.',
            'Min' => 'Items per page must be at least 1.',
            'Max' => 'Items per page cannot exceed 100.',
        ],
    ],
    'ListLocation' => [
        'Name' => [
            'Max' => 'Location name cannot exceed 50 characters.',
        ],
        'Page' => [
            'Integer' => 'Page must be an integer.',
            'Min' => 'Page must be at least 1.',
        ],
        'Limit' => [
            'Integer' => 'Items per page must be an integer.',
            'Min' => 'Items per page must be at least 1.',
            'Max' => 'Items per page cannot exceed 100.',
        ],
    ],

    // Error messages from ErrorCode
    'error' => [
        'unknown_error' => 'Unknown error',
        'invalid_params' => 'Invalid parameters',
        'unauthorized' => 'Unauthorized',
        'forbidden' => 'Access forbidden',
        'not_found' => 'Resource not found',
        'method_not_allowed' => 'Method not allowed',
        'validation_error' => 'Validation error',
        'service_unavailable' => 'Service unavailable',
        'setting_not_found' => 'Setting not found',
        // Business errors
        'business_error' => 'Business error',
        'data_not_found' => 'Data not found',
        'data_already_exists' => 'Data already exists',
        'data_validation_failed' => 'Data validation failed',
        'operation_failed' => 'Operation failed',
        'status_invalid' => 'Invalid status',
        'permission_denied' => 'Permission denied',

        // Appointment errors
        'appointment_not_found' => 'Appointment not found',
        'appointment_already_exists' => 'Appointment already exists',
        'appointment_status_invalid' => 'Invalid appointment status',
        'appointment_time_conflict' => 'Appointment time conflict',
        'appointment_capacity_exceeded' => 'Appointment capacity exceeded',
        'appointment_cancel_failed' => 'Failed to cancel appointment',
        'appointment_update_failed' => 'Failed to update appointment',
        'appointment_export_failed' => 'Failed to export appointment',
        'appointment_import_failed' => 'Failed to import appointment',
        'appointment_cannot_delete' => 'Cannot delete appointment',
        'appointment_cannot_update' => 'Cannot update appointment',
        'appointment_expired' => 'Appointment expired',
        'appointment_already_paid' => 'Appointment already paid',
        'payment_update_failed' => 'Failed to update payment',


        // Service errors
        'service_not_found' => 'Service not found',
        'service_unavailable_time' => 'Service unavailable at this time',
        'service_staff_not_available' => 'Service staff not available',
        'service_location_not_available' => 'Service location not available',
        'service_has_appointments' => 'Service has existing appointments',
        'service_delete_failed' => 'Failed to delete service',

        // Customer errors
        'customer_not_found' => 'Customer not found',
        'customer_blacklisted' => 'Customer is blacklisted',
        'customer_appointment_limit_exceeded' => 'Customer appointment limit exceeded',
        'customer_already_exists' => 'Customer already exists',
        'customer_email_exists' => 'Customer email already exists',
        'customer_phone_exists' => 'Customer phone already exists',
        'customer_has_appointments' => 'Customer has existing appointments',
        'customer_export_failed' => 'Failed to export customer data',
        'customer_delete_failed' => 'Failed to delete customer',
        'customer_delete_failed_batch' => 'Failed to delete customers',
        'customer_import_failed' => 'Failed to import customer data',

        // Save errors
        'save_extra_service_failed' => 'Failed to save extra service',
        'save_special_schedule_failed' => 'Failed to save special schedule',
        'save_staff_failed' => 'Failed to save staff',
        'save_schedule_failed' => 'Failed to save schedule',

        // Discount errors
        'discount_not_found' => 'Discount not found',
        'discount_name_exists' => 'Discount name already exists',
        'discount_in_use' => 'Discount is in use',
        'discount_create_failed' => 'Failed to create discount',
        'discount_update_failed' => 'Failed to update discount',
        'invalid_discount_type' => 'Invalid discount type',
        'invalid_discount_status' => 'Invalid discount status',
        'invalid_date_range' => 'Invalid date range',

        // Staff errors
        'staff_not_found' => 'Staff not found',
        'staff_already_exists' => 'Staff already exists',
        'staff_delete_failed' => 'Failed to delete staff',
        'staff_update_failed' => 'Failed to update staff',
        'email_already_exists' => 'Email already exists',
        'phone_already_exists' => 'Phone already exists',
        'position_not_found' => 'Position not found',
        'department_not_found' => 'Department not found',
        'staff_export_failed' => 'Failed to export staff data',
        'staff_import_failed' => 'Failed to import staff data',
        'staff_import_failed_message' => 'Import staff data failed, success: :success_count, fail: :fail_count',

        // Email template errors
        'email_template_variable_is_not_valid' => 'Email template variable is not valid',
        'email_template_variable_is_not_valid_message' => 'Email template variable :variable is not valid, valid variables: :valid_variables',
    ],

    'import_template' => [
        'customer_email_required' => 'Customer email is required',
        'customer_email_email' => 'Please enter a valid email address',
        'customer_phone_required' => 'Customer phone number is required',
        'customer_phone_string' => 'Phone number must be a string',
        'customer_phone_max' => 'Phone number cannot exceed 20 characters',
        'customer_name_required' => 'Customer name is required',
        'customer_name_string' => 'Name must be a string',
        'customer_name_max' => 'Name cannot exceed 50 characters',
        'customer_gender_string' => 'Gender must be a string',
        'customer_gender_max' => 'Gender cannot exceed 10 characters',
        'service_name_required' => 'Service name is required',
        'service_name_string' => 'Service name must be a string',
        'service_name_max' => 'Service name cannot exceed 100 characters',
        'location_required' => 'Location is required',
        'location_string' => 'Location must be a string',
        'location_max' => 'Location cannot exceed 50 characters',
        'appointment_date_required' => 'Appointment date is required',
        'appointment_date_date' => 'Appointment date must be a valid date',
        'status_string' => 'Status must be a string',
        'status_max' => 'Status cannot exceed 20 characters',
        'discount_name_string' => 'Discount name must be a string',
        'discount_name_max' => 'Discount name cannot exceed 100 characters',
        'payment_method_string' => 'Payment method must be a string',
        'payment_method_max' => 'Payment method cannot exceed 50 characters',
        'payment_status_string' => 'Payment status must be a string',
        'payment_status_max' => 'Payment status cannot exceed 20 characters',
        'payment_time_date' => 'Payment time must be a valid date',
        'remark_string' => 'Remark must be a string',
        'remark_max' => 'Remark cannot exceed 500 characters',
    ],
    'SaveGeneralSetting' => [
        'AppointmentLimit' => [
            'String' => 'Appointment limit must be a string',
            'In' => 'Appointment limit must be no_limit or member_only',
        ],
        'NonMemberRecord' => [
            'Boolean' => 'Non-member record must be a boolean value',
        ],
        'TimeSlotInterval' => [
            'Integer' => 'Time slot interval must be an integer',
            'In' => 'Time slot interval must be 5,10,15,30,60 minutes',
        ],
        'CrossTimeSlot' => [
            'Boolean' => 'Cross time slot must be a boolean value',
        ],
        'AllowAdminOvertimeBooking' => [
            'Boolean' => 'Allow admin overtime booking must be a boolean value',
        ],
        'AllowOvertimeBooking' => [
            'Boolean' => 'Allow overtime booking must be a boolean value',
        ],
        'MaxBookingDays' => [
            'Integer' => 'Max booking days must be an integer',
        ],
        'DefaultAppointmentStatus' => [
            'Integer' => 'Default appointment status must be an integer',
            'In' => 'Default appointment status must be 0,1,2,3,4,5,6',
        ],
        'DefaultPaymentStatus' => [
            'Integer' => 'Default payment status must be an integer',
            'In' => 'Default payment status must be 0,1,2',
        ],
        'AllowAdminOvertimeBooking' => [
            'Boolean' => 'Allow admin overtime booking must be a boolean value',
        ],
        'AllowOvertimeBooking' => [
            'Boolean' => 'Allow overtime booking must be a boolean value',
        ],
        'AdvanceBookingTime' => [
            'Integer' => 'Advance booking time must be an integer',
        ],
        'CustomerId' => [
            'MemberOnly' => 'Customer must be a member',
        ],
        'AppointmentRescheduleNotification' => [
            'String' => 'Appointment reschedule notification must be a string',
        ],
        'AppointmentCancelNotification' => [
            'String' => 'Appointment cancel notification must be a string',
        ],
        'AppointmentConfirmNotification' => [
            'String' => 'Appointment confirm notification must be a string',
        ],
        'HolidaySettings' => [
            'Array' => 'Holiday settings must be an array',
        ],
        'HolidaySettings.Start' => [
            'Required' => 'Holiday start date is required',
        ],
        'HolidaySettings.End' => [
            'Required' => 'Holiday end date is required',
        ],
    ],
    
    // Email Template validation
    'store_email_template' => [
        'name' => [
            'required' => 'Template name is required',
            'string' => 'Template name must be a string',
            'max' => 'Template name cannot exceed 100 characters'
        ],
        'code' => [
            'required' => 'Template code is required',
            'string' => 'Template code must be a string',
            'max' => 'Template code cannot exceed 50 characters'
        ],
        'subject' => [
            'required' => 'Email subject is required',
            'string' => 'Email subject must be a string',
            'max' => 'Email subject cannot exceed 200 characters'
        ],
        'content' => [
            'required' => 'Email content is required',
            'string' => 'Email content must be a string'
        ],
        'status' => [
            'boolean' => 'Status must be a boolean value'
        ],
        'to' => [
            'required' => 'Email to is required',
            'string' => 'Email to must be a string',
            'max' => 'Email to cannot exceed 100 characters'
        ],
        'attachments' => [
            'array' => 'Attachments must be an array'
        ]
    ],
    
    'update_email_template' => [
        'name' => [
            'required' => 'Template name is required',
            'string' => 'Template name must be a string',
            'max' => 'Template name cannot exceed 100 characters'
        ],
        'code' => [
            'required' => 'Template code is required',
            'string' => 'Template code must be a string',
            'max' => 'Template code cannot exceed 50 characters'
        ],
        'subject' => [
            'required' => 'Email subject is required',
            'string' => 'Email subject must be a string',
            'max' => 'Email subject cannot exceed 200 characters'
        ],
        'content' => [
            'required' => 'Email content is required',
            'string' => 'Email content must be a string'
        ]
    ],
    
    'list_email_template' => [
        'keyword' => [
            'string' => 'Keyword must be a string',
            'max' => 'Keyword cannot exceed 50 characters'
        ],
        'name' => [
            'string' => 'Template name must be a string',
            'max' => 'Template name cannot exceed 100 characters'
        ],
        'code' => [
            'string' => 'Template code must be a string',
            'max' => 'Template code cannot exceed 50 characters'
        ],
        'status' => [
            'integer' => 'Status must be an integer',
            'in' => 'Status must be either 0 (inactive) or 1 (active)'
        ],
        'page' => [
            'integer' => 'Page must be an integer',
            'min' => 'Page must be at least 1'
        ],
        'limit' => [
            'integer' => 'Items per page must be an integer',
            'min' => 'Items per page must be at least 1',
            'max' => 'Items per page cannot exceed 100'
        ],
        'sort_field' => [
            'string' => 'Sort field must be a string',
            'in' => 'Sort field must be one of: id, name, code, sort, created_at, updated_at'
        ],
        'sort_order' => [
            'string' => 'Sort order must be a string',
            'in' => 'Sort order must be either asc or desc'
        ]
    ],          
];
