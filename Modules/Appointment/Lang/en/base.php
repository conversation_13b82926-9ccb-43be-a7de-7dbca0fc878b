<?php

return [
    "nav" => [
        "admin_setting" => [
            "appointment_title" => "Appointment Management",
            "appointments" => "Appointment Services",
            "customers" => "Customer Management",
            "services" => "Service Management",
            "staff" => "Staff Management",
            "discounts" => "Discount Management",
            "settings" => "Setting Management",
            "dashboard" => "Dashboard",
            "calendar" => "Appointment Calendar",
            "payments" => "Payment Management",
            "email_templates" => "Email Templates",
        ]
    ],
    'permission' => [
        "admin_setting" => [
            "title" => "Appointment Management",
            "appointments" => "Appointments Management",
            "customers" => "Customers Management",
            "services" => "Services Management",
            "staff" => "Staff Management",
            "discounts" => "Discounts Management",
            "settings" => "Settings Management",
            "calendar" => "Appointment Calendar",
            "payments" => "Payment Management",
            "email_templates" => "Email Templates",
            "dashboard" => "Dashboard",
            "action" => [
                "list" => "List",
                "detail" => "Detail",
                "create" => "Create",
                "update" => "Update",
                "delete" => "Delete",
                "save" => "Save",
                "cancel" => "Cancel",
                "reset" => "Reset",
                "search" => "Search",
                "export" => "Export",
                "import" => "Import",
                "print" => "Print",
            ]
        ]
    ],
    'import_template' => [
        'customer_name' => 'John Doe',
        'customer_gender' => 'Male',
        'customer_birthday' => '1990-01-01',
        'customer_description' => 'VIP customer with special requirements',
        'service_name' => 'Premium Haircut',
        'location' => 'Main Street Branch',
        'status' => 'Confirmed',
        'discount_name' => 'New Customer 10% Off',
        'payment_method' => 'Credit Card',
        'payment_status' => 'Paid',
        'payment_time' => '2023-12-01 13:45:00',
        'remark' => 'Customer prefers quiet environment',
        'customer_name2' => 'Jane Smith',
        'customer_gender2' => 'Female',
        'customer_description2' => 'Regular customer since 2020',
        'service_name2' => 'Spa Treatment',
        'location2' => 'Downtown Wellness Center',
        'status2' => 'Pending',
        'discount_name2' => 'Loyalty Program 15% Off',
        'payment_method2' => 'Cash',
        'payment_status2' => 'Pending',
        'remark2' => 'Allergic to certain oils, check notes',
    ],
    'import_customer' => [
        'name1' => 'John Smith',
        'email1' => '<EMAIL>',
        'phone1' => '1234567890',
        'gender1' => 'Male',
        'birthdate1' => '1990-01-01 12:00:00',
        'description1' => 'Regular member',

        'name2' => 'Mary Johnson',
        'email2' => '<EMAIL>',
        'phone2' => '1234567891',
        'gender2' => 'Female',
        'birthdate2' => '1992-02-02 12:00:00',
        'description2' => 'VIP customer',
    ],
    'import_staff' => [
        'name1' => 'John Smith',
        'email1' => '<EMAIL>',
        'phone1' => '1234567890',
        'position_name1' => 'Manager',
        'department_name1' => 'Sales',

        'name2' => 'Mary Johnson',
        'email2' => '<EMAIL>',
        'phone2' => '1234567891',
        'position_name2' => 'Manager',
        'department_name2' => 'Sales',
    ],
];
