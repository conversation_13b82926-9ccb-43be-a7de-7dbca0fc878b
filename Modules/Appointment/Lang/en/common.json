{"CustomerList": {"create": "Add Customer", "import_text": "Import", "export_text": "Export", "keyword": "Keyword", "keywordPlaceholder": "Please input", "search": "Search", "reset": "Reset", "customer": "Customer", "phone": "Phone Number", "latestAppointment": "Latest Appointment", "appointmentCount": "Appointment Count", "registrationDate": "Registration Date", "operations": "Operations", "view": "View", "edit": "Edit", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this customer?", "tip": "Tip", "confirm": "Confirm", "cancel": "Cancel", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "getListFailed": "Failed to get customer list", "import": {"title": "Import Customers", "uploadTip": "Drop file here or click to upload", "fileTypeTip": "Only xlsx/xls files can be uploaded. Please download the template first", "downloadTemplate": "Download Import Template", "importSuccess": "Import successful", "importFailed": "Import failed", "downloadFailed": "Failed to download template"}, "export": {"exportSuccess": "Export successful", "exportFailed": "Export failed"}}, "CustomerDetail": {"tabs": {"info": "Customer Info", "history": "Appointment History"}, "profile": {"email": "Email", "phone": "Phone", "birthdate": "Birthday", "appointmentCount": "Appointment Count", "gender": "Gender", "description": "Notes", "genderOptions": {"male": "Male", "female": "Female", "unknown": "Unknown"}}, "history": {"empty": "No appointment history", "pagination": {"prev": "Previous", "next": "Next"}, "appointment": {"serviceType": "Service Type", "staff": "Staff", "location": "Location"}}, "error": {"getCustomerFailed": "Failed to get customer information", "getHistoryFailed": "Failed to get appointment history"}}, "CustomerCreate": {"avatar": {"title": "Avatar Settings", "upload": "Click to Upload Avatar", "change": "Change Avatar", "uploadError": {"formatError": "Only image files can be uploaded!", "sizeError": "Image size cannot exceed 2MB!", "uploadSuccess": "Upload successful", "uploadFailed": "Upload failed"}}, "form": {"title": "Customer Information", "name": {"label": "Customer Name", "placeholder": "Please enter customer name", "required": "Please enter customer name", "length": "Length should be between 2 and 50 characters"}, "email": {"label": "Customer <PERSON><PERSON>", "placeholder": "Please enter customer email", "required": "Please enter customer email", "format": "Please enter a valid email address"}, "phone": {"label": "Customer Phone", "placeholder": "Please enter customer phone", "format": "Please enter a valid phone number"}, "gender": {"label": "Gender", "placeholder": "Please select gender", "options": {"male": "Male", "female": "Female", "unknown": "Unknown"}}, "birthdate": {"label": "Birthday", "placeholder": "Select date"}, "description": {"label": "Description", "placeholder": "Please enter description"}}, "buttons": {"cancel": "Cancel", "saveAndAdd": "Save and Add", "saveChanges": "Save Changes"}, "messages": {"formError": "Please complete the form", "updateSuccess": "Customer information updated successfully", "updateFailed": "Failed to update customer", "createSuccess": "Customer created successfully", "createFailed": "Failed to create customer", "getFailed": "Failed to get customer information"}}, "Dashboard": {"filter": {"timeRange": {"placeholder": "Custom", "today": "Today", "week": "This Week", "month": "This Month", "last_month": "Last Month", "quarter": "This Quarter", "year": "This Year", "custom": "Custom"}, "datePicker": {"start": "Start Date", "end": "End Date"}}, "cards": {"appointments": "Appointments", "pendingAppointments": "Pending Appointments", "revenue": "Revenue", "newCustomers": "New Customers"}, "appointmentList": {"title": "Appointment List", "columns": {"appointmentTime": "Appointment Time", "customerName": "Customer Name", "staff": "Staff", "service": "Service", "duration": "Duration", "createTime": "Create Time"}}, "statusOverview": {"title": "Appointment Status Overview", "status": {"confirmed": "Confirmed", "cancelled": "Cancelled", "pending": "Pending", "rescheduled": "Rescheduled", "rejected": "Rejected", "completed": "Completed", "noShow": "No Show"}}}, "PanelList": {"buttons": {"import": "Import", "export": "Export", "create": "Add Appointment"}, "search": {"placeholder": "Please input", "selectPlaceholder": "Please select", "search": "Search", "reset": "Reset", "delete": "Delete"}, "tabs": {"my": "My Appointments", "all": "All Appointments"}, "table": {"location": "Location", "appointmentTime": "Appointment Time", "appointmentDate": "Appointment Date", "customer": "Customer", "staffName": "Staff Name", "service": "Service", "status": "Status", "duration": "Duration", "createTime": "Create Time", "operations": "Operations", "discount": "Apply Discount", "discountCode": "Discount Code", "applyDiscount": "Apply Discount", "discountCodePlaceholder": "Please enter discount code"}, "dialog": {"status": {"title": "Change Appointment Status", "label": "Status", "placeholder": "Please select status"}, "import": {"title": "Import Appointments", "uploadTip": "Drop file here or click to upload", "fileTypeTip": "Only xlsx/xls files can be uploaded. Please download the template first", "downloadTemplate": "Download Import Template"}}, "messages": {"deleteConfirm": "Are you sure you want to delete this appointment?", "deleteTitle": "Delete Confirmation", "batchDeleteConfirm": "Are you sure you want to delete {count} selected appointments?", "batchDeleteTitle": "Delete Appointments", "selectDelete": "Please select appointments to delete", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "importSuccess": "Import successful", "importFailed": "Import failed", "downloadFailed": "Failed to download template", "exportSuccess": "Export successful", "exportFailed": "Export failed", "statusSuccess": "Status updated successfully", "statusFailed": "Failed to update status", "getStatusFailed": "Failed to get status options", "getListFailed": "Failed to get appointment list", "getDetailFailed": "Failed to get appointment detail", "selectFile": "Please select a file to upload"}, "confirm": "Confirm", "cancel": "Cancel"}, "Common": {"duration": {"hour": "hour", "minute": "minute", "second": "second"}, "buttons": {"confirm": "Confirm", "cancel": "Cancel"}}, "ServiceList": {"button_texts": {"createCategory": "Create Category", "createService": "Add Service"}, "search": {"placeholder": "Please input", "search": "Search", "reset": "Reset"}, "table": {"serviceName": "Service Name", "category": "Category", "price": "Price", "duration": "Duration", "enabled": "Enabled", "operations": "Operations"}, "category": {"dialog": {"title": "Create Category", "name": "Category Name", "namePlaceholder": "Please enter category name", "nameRequired": "Please enter category name", "nameLength": "Length should be between 2 and 50 characters", "description": "Description", "descriptionPlaceholder": "Please enter category description"}}, "buttons": {"cancel": "Cancel", "submit": "Submit"}, "messages": {"deleteConfirm": "Are you sure you want to delete this service?", "deleteTitle": "Delete", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "getListFailed": "Failed to get data", "createCategorySuccess": "Category created successfully", "createCategoryFailed": "Failed to create category", "formError": "Please complete the form", "enableAll": "Enable All", "disableAll": "Disable All", "enableSuccess": "All services enabled", "disableSuccess": "All services disabled", "enableFailed": "Enable failed", "disableFailed": "Disable failed"}}, "ServiceCreate": {"tabs": {"basic": "Basic Info", "staff": "Staff Arrangement", "time": "Time Schedule", "settings": "Settings"}, "basic": {"message": {"basicUp": "Please complete the basic information", "basicStaff": "Please select at least one employee responsible for this service"}, "name": {"label": "Service Name", "placeholder": "Please enter", "required": "Please enter service name"}, "category": {"label": "Service Category", "placeholder": "Please select", "required": "Please select service category"}, "price": {"label": "Service Price (HKD)", "required": "Please enter service price"}, "duration": {"label": "Service Duration", "placeholder": "Please select", "required": "Please select service duration", "options": {"30": "30 minutes", "40": "40 minutes", "60": "1 hour"}}, "display": {"label": "Client Booking Interface", "hidePrice": "<PERSON><PERSON> Price", "hideDuration": "Hide Duration"}, "repeat": {"label": "Repeat Booking", "type": {"label": "Repeat Type", "placeholder": "Please select", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly"}, "mode": {"label": "Repeat Mode", "placeholder": "Please select", "byTimes": "By Times", "byCycle": "By Cycle"}, "fixedTimes": {"label": "Fixed Times", "placeholder": "Please enter"}}, "staffLimit": {"label": "Service Staff Limit", "placeholder": "Please select", "required": "Please select service staff limit", "options": {"1": "one", "2": "two", "3": "three", "4": "four", "5": "five"}}, "description": {"label": "Description", "placeholder": "Please enter"}}, "staff": {"title": "Add Staff for This Service", "placeholder": "Please select staff", "required": "Please select at least one staff member for this service"}, "time": {"title": "Configure Specific Schedule", "weekday": "Week {day}", "weekdayOptions": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "timeRange": {"start": "Start Time", "end": "End Time"}, "holiday": "Add Rest Day", "break": {"label": "Add Break Time", "start": "Start Time", "end": "End Time"}, "special": {"title": "Special Time", "date": "Select Date", "start": "Start Time", "end": "End Time"}}, "settings": {"extraService": {"title": "Configure Extra Service Time", "addButton": "Add Extra Service", "dialog": {"title": "Add Extra Service", "name": "Extra Service Name", "category": "Category", "minQuantity": "Minimum Quantity", "maxQuantity": "Maximum Quantity", "price": "Price (HKD)", "duration": "Duration", "placeholder": {"name": "Please enter", "category": "Please enter category", "duration": "10 minutes"}}, "table": {"name": "Service Name", "category": "Category", "duration": "Duration", "price": "Price", "minQuantity": "Min Quantity", "maxQuantity": "Max Quantity"}}, "memberVisible": "Member Only Visible"}, "buttons": {"prev": "Previous", "next": "Next", "cancel": "Cancel", "submit": "Submit", "add": "Add"}, "messages": {"formError": "Please complete the form", "createSuccess": "Service created successfully", "createFailed": "Failed to create service", "updateSuccess": "Service updated successfully", "updateFailed": "Failed to update service", "getDetailFailed": "Failed to get service details", "getCategoryFailed": "Failed to get category list", "getStaffFailed": "Failed to get staff list"}}, "StaffList": {"buttons": {"import": "Import", "export": "Export", "create": "Add Staff"}, "search": {"placeholder": "Please input", "search": "Search", "reset": "Reset"}, "table": {"name": "Staff Name", "position": "Position", "department": "Department", "email": "E-mail", "phone": "Phone", "operations": "Operations"}, "import": {"title": "Import Staff", "uploadTip": "Drop file here or click to upload", "fileTypeTip": "Only xlsx/xls files can be uploaded. Please download the template first", "downloadTemplate": "Download Import Template", "templateFileName": "Staff Import Template.xlsx", "importSuccess": "Import successful", "importFailed": "Import failed", "downloadFailed": "Failed to download template", "selectFile": "Please select a file to upload"}, "export": {"fileName": "Staff Records", "exportSuccess": "Export successful", "exportFailed": "Export failed"}, "messages": {"deleteConfirm": "Are you sure you want to delete the staff {name}?", "deleteTitle": "Delete", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "deleteCancel": "Delete operation cancelled", "getListFailed": "Failed to get staff list"}}, "StaffAdd": {"title": "Staff Information", "avatar": {"title": "Avatar Settings", "upload": "Click to Upload Avatar", "change": "Change Avatar", "uploadError": {"formatError": "Only image files can be uploaded!", "sizeError": "Image size cannot exceed 10MB!", "uploadSuccess": "Upload successful", "uploadFailed": "Upload failed"}}, "form": {"name": {"label": "Staff Name", "placeholder": "Please enter", "required": "Please enter staff name", "length": "Length should be between 1 and 50 characters"}, "email": {"label": "Staff Email", "placeholder": "Please enter", "required": "Please enter staff email", "format": "Please enter a valid email address"}, "phone": {"label": "Staff Phone", "placeholder": "Please enter", "format": "Please enter a valid phone number"}, "department": {"label": "Department", "placeholder": "Please select"}, "position": {"label": "Position", "placeholder": "Please select"}, "remark": {"label": "Description", "placeholder": "Please enter description", "length": "Length cannot exceed 255 characters"}}, "buttons": {"cancel": "Cancel", "saveAndAdd": "Save and Add", "submit": "Submit"}, "messages": {"formError": "Please complete the form", "updateSuccess": "Staff information updated successfully", "updateFailed": "Failed to update staff", "createSuccess": "Staff created successfully", "createFailed": "Failed to create staff", "getFailed": "Failed to get staff information", "getDepartmentFailed": "Failed to get department list", "getPositionFailed": "Failed to get position list"}}, "Settings": {"tabs": {"basic": "Basic Settings", "holiday": "Holiday Settings", "notification": "Notification Settings"}, "basic": {"bookingLimit": {"title": "Booking Limitation", "memberOnly": "No limit, members only", "all": "Everyone can book", "none": "No booking allowed"}, "saveGuestHistory": {"title": "Save booking history for non-members", "yes": "Yes", "no": "No"}, "timeSlot": {"title": "Time Slot Interval [<PERSON><PERSON><PERSON>]", "5min": "5 minutes", "15min": "15 minutes", "30min": "30 minutes", "customTimeSlot": "Use service duration as time slot interval"}, "advanceBooking": {"title": "Advance Booking [Default]", "value": "Please enter", "minute": "Minutes", "hour": "Hours", "day": "Days"}, "bookingDays": {"title": "Maximum booking days", "placeholder": "Please enter"}, "defaultStatus": {"title": "Default Appointment Status", "pending": "Pending", "confirmed": "Confirmed", "rescheduled": "Rescheduled", "completed": "Completed", "cancelled": "Cancelled", "rejected": "Rejected", "noShow": "No Show"}, "defaultPaymentStatus": {"title": "Default Payment Success Status", "pending": "Pending Payment", "paid": "Paid", "failed": "Payment Failed"}, "allowAdmin": {"title": "Allow admin to book outside working hours"}}, "holiday": {"title": "Holiday Time", "startDate": "Start Date", "endDate": "End Date", "noDate": "Please select holiday date range"}, "notification": {"reschedule": {"title": "Appointment Reschedule Notification", "placeholder": "Please select notification method"}, "cancel": {"title": "Appointment Cancellation Notification", "placeholder": "Please select notification method"}, "confirm": {"title": "Appointment Confirmation Notification", "placeholder": "Please select notification method"}}, "buttons": {"cancel": "Cancel", "save": "Save"}, "messages": {"cancelChanges": "Changes cancelled", "saveSuccess": "Saved successfully", "saveFailed": "Save failed, please try again", "getSettingsFailed": "Failed to get settings", "getTemplatesFailed": "Failed to get email template list"}}, "Calendar": {"views": {"year": "Year", "month": "Month", "week": "Week", "day": "Day", "today": "Today", "monday": "Mon", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed", "thursday": "<PERSON>hu", "friday": "<PERSON><PERSON>", "saturday": "Sat", "sunday": "Sun"}, "detail": {"title": "{year}/{month}/{day}", "appointment": {"time": "{start} - {end}", "customer": "Customer", "staff": "Staff", "location": "Location", "service": "Service", "status": {"pending": "Pending", "confirmed": "Confirmed", "completed": "Completed", "cancelled": "Cancelled", "rescheduled": "Rescheduled", "rejected": "Rejected", "noShow": "No Show"}}, "empty": "No appointments today", "addButton": "Add Appointment"}, "status": {"pending": "Pending", "confirmed": "Confirmed", "completed": "Completed", "cancelled": "Cancelled", "rescheduled": "Rescheduled", "rejected": "Rejected", "noShow": "No Show"}}, "PaymentList": {"buttons_texts": {"import": "Import", "export": "Export"}, "buttons": {"cancel": "Cancel", "confirm": "Confirm", "submit": "Save"}, "search": {"placeholder": "Please enter keywords", "search": "Search", "reset": "Reset"}, "table": {"appointmentTime": "Appointment Time", "customer": "Customer", "staffName": "Staff Name", "serviceName": "Service Name", "paymentMethod": "Payment Method", "amount": "Amount", "paymentStatus": "Payment Status", "operations": "Operations"}, "dialog": {"import": {"title": "Import Payment Records", "uploadTip": "Drag files here or click to upload", "fileTypeTip": "Support .xlsx, .xls files", "downloadTemplate": "Download Template"}}, "messages": {"deleteConfirm": "Are you sure to delete this payment record?", "deleteTitle": "Delete Confirmation", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "exportSuccess": "Export successful", "exportFailed": "Export failed", "importSuccess": "Import successful", "importFailed": "Import failed", "getListFailed": "Failed to get payment list", "downloadFailed": "Download failed", "selectFile": "Please select a file"}, "detail": {"title": "Payment Details", "customerInfo": "Customer Information", "paymentMethod": "Payment Method", "paymentStatus": "Payment Status", "paymentDetails": "Payment Details", "servicePrice": "Service Price", "discount": "Discount", "total": "Total", "status": {"pending": "Pending", "paid": "Paid", "failed": "Failed"}}, "edit": {"title": "Edit Payment Details", "method": "Payment Method", "amount": "Payment Amount", "status": "Payment Status", "discount": "Apply Discount", "discountAmount": "Discount Amount", "saveSuccess": "Save successful", "saveFailed": "Save failed"}}, "EmailList": {"buttons": {"create": "Create Template"}, "search": {"placeholder": "Please input", "search": "Search", "reset": "Reset"}, "table": {"name": "Template Name", "creator": "Creator", "createDate": "Create Date", "lastEdit": "Last Edit", "enabled": "Enabled", "operations": "Operations"}, "messages": {"deleteConfirm": "Are you sure to delete template \"{name}\"?", "deleteTitle": "Delete", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "getListFailed": "Failed to get data", "enableSuccess": "Enabled successfully", "disableSuccess": "Disabled successfully", "statusChangeFailed": "Status update failed"}}, "EmailCreate": {"form": {"name": {"label": "Template Name", "placeholder": "Please enter", "required": "Please enter template name", "length": "Length should be between 2 and 50 characters"}, "subject": {"label": "Subject", "placeholder": "Please enter", "required": "Please enter email subject"}, "code": {"label": "Code", "placeholder": "Please enter", "required": "Please enter email code"}, "content": {"label": "Content", "required": "Please enter email content", "contentWarning": "Please modify the default template content", "defaultContent": {"1": "Dear customer_full_name,", "2": "We would like to notify you that your appointment at company_name has been rescheduled. Here are the updated details of the appointment:", "3": "New appointment date and time: new_appointment_date_time_client", "4": "Service: service_name", "5": "Staff: staff_name", "6": "Location: location_name", "7": "If you have any questions or need further assistance, please contact us:", "8": "Email: staff_email", "9": "Phone: staff_phone", "10": "We apologize for any inconvenience and thank you for your understanding.", "11": "Best regards,", "12": "Company Name: company_name"}}, "attachment": {"label": "Attachments", "addButton": "Add Attachment", "tip": "Support Excel files and images, single file should not exceed 10MB", "uploadError": {"typeError": "Only Excel files or images can be uploaded!", "sizeError": "File size cannot exceed 10MB!", "uploadFailed": "Image upload failed"}}}, "buttons": {"cancel": "Cancel", "save": "Save", "submit": "Submit"}, "messages": {"saveSuccess": "Save successful", "saveFailed": "Save failed", "getDetailFailed": "Failed to get template data", "defaultContentWarning": "Please modify the default template content before saving"}}, "router": {"appointment": "Appointment Management", "dashboard": "Dashboard", "calendar": "Calendar", "appointments": "Appointments", "panel_create": "Create Appointment", "edit": "Edit", "payments": "Payment Management", "customers": "Customer Management", "customer_create": "Add Customer", "customer_edit": "Edit Customer", "customer_detail": "Customer Details", "services": "Service Management", "service_create": "Add Service", "service_edit": "Edit Service", "staff": "Staff Management", "staff_create": "Add Staff", "staff_edit": "Edit Staff", "settings": "Settings", "email_templates": "Email Templates", "email_template_create": "Create <PERSON><PERSON>", "email_template_edit": "Edit <PERSON><PERSON>late"}}