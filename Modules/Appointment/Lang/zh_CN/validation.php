<?php

return [
    // Store Customer Request
    'StoreCustomer' => [
        'Name' => [
            'Required' => '姓名不能为空',
            'String' => '姓名必须是字符串',
            'Max' => '姓名长度不能超过50个字符'
        ],
        'Email' => [
            'Required' => '邮箱不能为空',
            'Email' => '请输入有效的邮箱地址'
        ],
        'Phone' => [
            'Required' => '手机号不能为空',
            'String' => '手机号必须是字符串',
            'Max' => '手机号长度不能超过20个字符'
        ],
        'Gender' => [
            'Required' => '性别不能为空',
            'Integer' => '性别必须是整数',
            'In' => '性别必须是1（男）或2（女）'
        ],
        'Birthday' => [
            'Date' => '生日必须是有效的日期'
        ],
        'Description' => [
            'String' => '描述必须是字符串',
            'Max' => '描述长度不能超过500个字符'
        ],
        'Photo' => [
            'String' => '照片必须是字符串',
            'Max' => '照片URL长度不能超过255个字符'
        ]
    ],
    
    // List Appointment Request
    'ListAppointment' => [
        'Page' => [
            'Integer' => '页码必须是整数',
            'Min' => '页码必须大于等于1'
        ],
        'Limit' => [
            'Integer' => '每页数量必须是整数',
            'Min' => '每页数量必须大于等于1',
            'Max' => '每页数量不能超过100'
        ],
        'Keyword' => [
            'String' => '关键词必须是字符串',
            'Max' => '关键词长度不能超过100个字符'
        ],
        'CustomerId' => [
            'Integer' => '客户ID必须是整数'
        ],
        'Status' => [
            'String' => '状态必须是字符串'
        ],
        'SortField' => [
            'String' => '排序字段必须是字符串',
            'In' => '排序字段必须是id、appointment_date、created_at或updated_at之一'
        ],
        'SortOrder' => [
            'String' => '排序方式必须是字符串',
            'In' => '排序方式必须是asc或desc'
        ]
    ],
    
    // Store Staff Request
    'StoreStaff' => [
        'Name' => [
            'Required' => '姓名不能为空',
            'String' => '姓名必须是字符串',
            'Max' => '姓名长度不能超过50个字符'
        ],
        'Email' => [
            'Required' => '邮箱不能为空',
            'Email' => '请输入有效的邮箱地址'
        ],
        'Phone' => [
            'String' => '手机号必须是字符串',
            'Max' => '手机号长度不能超过20个字符'
        ],
        'PositionId' => [
            'Integer' => '职位ID必须是整数'
        ],
        'DepartmentId' => [
            'Integer' => '部门ID必须是整数'
        ],
        'Location' => [
            'String' => '地点必须是字符串',
            'Max' => '地点长度不能超过50个字符'
        ],
        'Description' => [
            'String' => '描述必须是字符串',
            'Max' => '描述长度不能超过255个字符'
        ]
    ],
    
    // Store Appointment Request
    'StoreAppointment' => [
        'Location' => [
            'Required' => '预约地点不能为空',
            'String' => '预约地点必须是字符串'
        ],
        'ServiceId' => [
            'Required' => '服务ID不能为空',
            'Integer' => '服务ID必须是整数'
        ],
        'AppointmentDate' => [
            'Required' => '预约日期不能为空',
            'DateTime' => '预约日期必须是有效的日期和时间',
            'AfterOrEqual' => '预约日期必须是今天或将来的日期'
        ],
        'DiscountId' => [
            'Integer' => '折扣ID必须是整数'
        ],
        'Remark' => [
            'String' => '备注必须是字符串',
            'Max' => '备注长度不能超过500个字符'
        ],
        'CustomerId' => [
            'Required' => '客户ID不能为空',
            'Integer' => '客户ID必须是整数',
            'MemberOnly' => '只有会员才能预约'
        ],
        'Status' => [
            'Required' => '状态不能为空',
            'Integer' => '状态必须是整数'
        ]
    ],
    
    // Store Service Request
    'StoreService' => [
        'CategoryId' => [
            'Required' => '分类ID不能为空',
            'Integer' => '分类ID必须是整数',
            'Exists' => '所选分类不存在'
        ],
        'Name' => [
            'Required' => '服务名称不能为空',
            'String' => '服务名称必须是字符串',
            'Max' => '服务名称长度不能超过100个字符'
        ],
        'Price' => [
            'Required' => '价格不能为空',
            'Numeric' => '价格必须是数字',
            'Min' => '价格必须大于等于0'
        ],
        'Duration' => [
            'Required' => '时长不能为空',
            'Integer' => '时长必须是整数',
            'Min' => '时长必须大于等于1'
        ],
        'IsRepeatServicePayType' => [
            'RequiredIf' => '当启用重复服务时，必须设置重复服务付款类型'
        ],
        'IsRepeatServiceCategory' => [
            'RequiredIf' => '当启用重复服务时，必须设置重复服务分类'
        ],
        'IsRepeatServiceCount' => [
            'RequiredIf' => '当启用重复服务时，必须设置重复服务次数',
            'Min' => '重复服务次数必须大于等于1'
        ],
        'PersonCount' => [
            'Required' => '服务人数不能为空',
            'Integer' => '服务人数必须是整数',
            'Min' => '服务人数必须大于等于1'
        ],
        'PayTypes' => [
            'Required' => '支付方式不能为空',
            'Array' => '支付方式必须是数组'
        ],
        'AdvanceBookingTime' => [
            'Integer' => '提前预约时间必须是整数',
            'Min' => '提前预约时间必须大于等于0'
        ],
        'Status' => [
            'Required' => '状态不能为空',
            'Boolean' => '状态必须是布尔值'
        ],
        'ExtraServices' => [
            'Name' => [
                'Required' => '额外服务名称不能为空',
                'String' => '额外服务名称必须是字符串',
                'Max' => '额外服务名称长度不能超过100个字符'
            ],
            'CategoryName' => [
                'String' => '额外服务分类名称必须是字符串',
                'Max' => '额外服务分类名称长度不能超过100个字符'
            ]
        ],
        'Schedules' => [
            'DayOfWeek' => [
                'Required' => '星期几不能为空',
                'Integer' => '星期几必须是整数',
                'Min' => '星期几必须大于等于1',
                'Max' => '星期几不能超过7'
            ],
            'StartTime' => [
                'Required' => '开始时间不能为空',
                'String' => '开始时间必须是字符串',
                'Min' => '开始时间必须有效'
            ],
            'EndTime' => [
                'Required' => '结束时间不能为空',
                'String' => '结束时间必须是字符串',
                'Min' => '结束时间必须有效'
            ],
            'RestTimeList' => [
                'Array' => '休息时间列表必须是数组',
                'StartTime' => [
                    'String' => '休息时间开始时间必须是字符串',
                    'Min' => '休息时间开始时间必须有效'
                ],
                'EndTime' => [
                    'String' => '休息时间结束时间必须是字符串',
                    'Min' => '休息时间结束时间必须有效'
                ]
            ],
            'IsRestDay' => [
                'Boolean' => '是否休息日必须是布尔值'
            ]
        ],
        'SpecialSchedules' => [
            'StartTime' => [
                'Required' => '特殊时间安排开始时间不能为空',
                'String' => '特殊时间安排开始时间必须是字符串',
                'Min' => '特殊时间安排开始时间必须有效'
            ],
            'EndTime' => [
                'Required' => '特殊时间安排结束时间不能为空',
                'String' => '特殊时间安排结束时间必须是字符串',
                'Min' => '特殊时间安排结束时间必须有效'
            ]
        ],
        'StaffIds' => [
            'Required' => '员工ID不能为空',
            'Array' => '员工ID必须是数组',
            'Min' => '至少需要分配一名员工',
            'Exists' => '一个或多个所选员工不存在'
        ]
    ],
    
    // Update Service Request
    'UpdateService' => [
        'CategoryId' => [
            'Required' => '分类ID不能为空',
            'Integer' => '分类ID必须是整数',
            'Exists' => '所选分类不存在'
        ],
        'Name' => [
            'Required' => '服务名称不能为空',
            'String' => '服务名称必须是字符串',
            'Max' => '服务名称长度不能超过100个字符'
        ],
        'Price' => [
            'Required' => '价格不能为空',
            'Numeric' => '价格必须是数字',
            'Min' => '价格必须大于等于0'
        ],
        'Duration' => [
            'Required' => '时长不能为空',
            'Integer' => '时长必须是整数',
            'Min' => '时长必须大于等于1'
        ],
        'IsRepeatServicePayType' => [
            'RequiredIf' => '当启用重复服务时，必须设置重复服务付款类型'
        ],
        'IsRepeatServiceCategory' => [
            'RequiredIf' => '当启用重复服务时，必须设置重复服务分类'
        ],
        'IsRepeatServiceCount' => [
            'RequiredIf' => '当启用重复服务时，必须设置重复服务次数',
            'Min' => '重复服务次数必须大于等于1'
        ],
        'PersonCount' => [
            'Required' => '服务人数不能为空',
            'Integer' => '服务人数必须是整数',
            'Min' => '服务人数必须大于等于1'
        ],
        'PayTypes' => [
            'Required' => '支付方式不能为空',
            'Array' => '支付方式必须是数组'
        ],
        'AdvanceBookingTime' => [
            'Integer' => '提前预约时间必须是整数',
            'Min' => '提前预约时间必须大于等于0'
        ],
        'Status' => [
            'Required' => '状态不能为空',
            'Boolean' => '状态必须是布尔值'
        ],
        'ExtraServices' => [
            'Name' => [
                'Required' => '额外服务名称不能为空',
                'String' => '额外服务名称必须是字符串',
                'Max' => '额外服务名称长度不能超过100个字符'
            ],
            'CategoryName' => [
                'String' => '额外服务分类名称必须是字符串',
                'Max' => '额外服务分类名称长度不能超过100个字符'
            ]
        ],
        'Schedules' => [
            'DayOfWeek' => [
                'Required' => '星期几不能为空',
                'Integer' => '星期几必须是整数',
                'Min' => '星期几必须大于等于1',
                'Max' => '星期几不能超过7'
            ],
            'StartTime' => [
                'Required' => '开始时间不能为空',
                'String' => '开始时间必须是字符串',
                'Min' => '开始时间必须有效'
            ],
            'EndTime' => [
                'Required' => '结束时间不能为空',
                'String' => '结束时间必须是字符串',
                'Min' => '结束时间必须有效'
            ],
            'RestTimeList' => [
                'Array' => '休息时间列表必须是数组',
                'StartTime' => [
                    'String' => '休息时间开始时间必须是字符串',
                    'Min' => '休息时间开始时间必须有效'
                ],
                'EndTime' => [
                    'String' => '休息时间结束时间必须是字符串',
                    'Min' => '休息时间结束时间必须有效'
                ]
            ],
            'IsRestDay' => [
                'Boolean' => '是否休息日必须是布尔值'
            ]
        ],
        'SpecialSchedules' => [
            'StartTime' => [
                'Required' => '特殊时间安排开始时间不能为空',
                'String' => '特殊时间安排开始时间必须是字符串',
                'Min' => '特殊时间安排开始时间必须有效'
            ],
            'EndTime' => [
                'Required' => '特殊时间安排结束时间不能为空',
                'String' => '特殊时间安排结束时间必须是字符串',
                'Min' => '特殊时间安排结束时间必须有效'
            ]
        ],
        'StaffIds' => [
            'Required' => '员工ID不能为空',
            'Array' => '员工ID必须是数组',
            'Min' => '至少需要分配一名员工',
            'Exists' => '一个或多个所选员工不存在'
        ]
    ],
    
    // Update Discount Request
    'UpdateDiscount' => [
        'Name' => [
            'Required' => '折扣名称不能为空',
            'String' => '折扣名称必须是字符串',
            'Max' => '折扣名称长度不能超过100个字符',
            'Unique' => '该折扣名称已被使用'
        ],
        'Code' => [
            'Max' => '折扣代码长度超出限制',
            'Unique' => '该折扣代码已被使用'
        ],
        'Type' => [
            'Required' => '折扣类型不能为空',
            'String' => '折扣类型必须是字符串',
            'In' => '选择的折扣类型无效'
        ],
        'Value' => [
            'Required' => '折扣值不能为空',
            'Numeric' => '折扣值必须是数字',
            'Min' => '折扣值必须大于等于0',
            'Decimal' => '折扣值最多可有2位小数'
        ],
        'StartDate' => [
            'Date' => '开始日期必须是有效的日期'
        ],
        'EndDate' => [
            'Date' => '结束日期必须是有效的日期',
            'AfterOrEqual' => '结束日期必须大于或等于开始日期'
        ]
    ],
    
    // Import Staff (kept for backward compatibility)
    'import_staff' => [
        'file' => [
            'required' => '请选择要导入的文件',
            'file' => '上传的必须是文件',
            'mimes' => '文件必须是 xlsx, xls 或 csv 格式',
            'max' => '文件大小不能超过 10MB',
        ],
    ],
    
    // Export Staff (kept for backward compatibility)
    'export_staff' => [
        'page' => [
            'integer' => '页码必须是整数',
            'min' => '页码必须大于等于 1',
        ],
        'limit' => [
            'integer' => '每页数量必须是整数',
            'min' => '每页数量必须大于等于 1',
            'max' => '每页数量不能超过 1000',
        ],
        'name' => [
            'string' => '名称必须是字符串',
            'max' => '名称长度不能超过 50 个字符',
        ],
        'email' => [
            'string' => '邮箱必须是字符串',
            'max' => '邮箱长度不能超过 100 个字符',
        ],
        'phone' => [
            'string' => '手机号必须是字符串',
            'max' => '手机号长度不能超过 20 个字符',
        ],
        'position_id' => [
            'integer' => '职位ID必须是整数',
            'exists' => '所选职位不存在',
        ],
        'department_id' => [
            'integer' => '部门ID必须是整数',
            'exists' => '所选部门不存在',
        ],
    ],
    
    // Export Customer (kept for backward compatibility)
    'export_customer' => [
        'id' => 'ID',
        'name' => '姓名',
        'email' => '邮箱',
        'phone' => '手机号',
        'gender' => '性别',
        'birthdate' => '出生日期',
        'appointment_count' => '预约次数',
        'last_appointment_at' => '最近预约时间',
        'created_at' => '创建时间',
    ],
    
    // Import Customer (kept for backward compatibility)
    'import_customer' => [
        'name' => '姓名',
        'email' => '邮箱',
        'phone' => '手机号',
        'gender' => '性别',
        'birthdate' => '出生日期',
        'description' => '描述',
    ],
    
    // Update Service Category Request
    'UpdateServiceCategory' => [
        'Name' => [
            'String' => '服务分类名称必须是字符串',
            'Max' => '服务分类名称长度不能超过50个字符'
        ],
        'Description' => [
            'String' => '描述必须是字符串',
            'Max' => '描述长度不能超过500个字符'
        ],
        'Sort' => [
            'Integer' => '排序必须是整数',
            'Min' => '排序必须大于等于0'
        ],
        'Status' => [
            'Boolean' => '状态必须是布尔值'
        ]
    ],
    
    // Update Payment Info Request
    'update_payment_info' => [
        'payment_method' => [
            'required' => '支付方式不能为空',
            'string' => '支付方式必须是字符串',
            'max' => '支付方式长度不能超过50个字符'
        ],
        'final_price' => [
            'required' => '支付金额不能为空',
            'numeric' => '支付金额必须是数字',
            'min' => '支付金额必须大于等于0'
        ],
        'payment_status' => [
            'required' => '支付状态不能为空',
            'integer' => '支付状态必须是整数',
            'in' => '支付状态必须是0（未支付）、1（已支付）或2（已退款）'
        ],
        'payment_time' => [
            'date' => '支付时间必须是有效的日期'
        ],
        'transaction_id' => [
            'string' => '交易编号必须是字符串',
            'max' => '交易编号长度不能超过100个字符'
        ],
        'payment_remark' => [
            'string' => '支付备注必须是字符串',
            'max' => '支付备注长度不能超过255个字符'
        ],
        'discount_id' => [
            'numeric' => '折扣ID必须是数字',
            'min' => '折扣ID必须大于等于0'
        ]
    ],
    
    // Update Staff Request
    'UpdateStaff' => [
        'Name' => [
            'Required' => '姓名不能为空',
            'String' => '姓名必须是字符串',
            'Max' => '姓名长度不能超过50个字符'
        ],
        'Email' => [
            'Required' => '邮箱不能为空',
            'Email' => '请输入有效的邮箱地址'
        ],
        'Phone' => [
            'String' => '手机号必须是字符串',
            'Max' => '手机号长度不能超过20个字符'
        ],
        'PositionId' => [
            'Integer' => '职位ID必须是整数'
        ],
        'DepartmentId' => [
            'Integer' => '部门ID必须是整数'
        ],
        'Location' => [
            'String' => '地点必须是字符串',
            'Max' => '地点长度不能超过50个字符'
        ],
        'Description' => [
            'String' => '描述必须是字符串',
            'Max' => '描述长度不能超过255个字符'
        ]
    ],
    
    // Update Location Request
    'UpdateLocation' => [
        'Name' => [
            'String' => '地点名称必须是字符串',
            'Max' => '地点名称长度不能超过50个字符'
        ],
        'Address' => [
            'String' => '地址必须是字符串',
            'Max' => '地址长度不能超过255个字符'
        ],
        'Description' => [
            'String' => '描述必须是字符串',
            'Max' => '描述长度不能超过500个字符'
        ]
    ],
    
    // Update Status Appointment Request
    'UpdateStatusAppointment' => [
        'Status' => [
            'Required' => '预约状态不能为空',
            'String' => '预约状态必须是字符串',
        ],
        'Reason' => [
            'String' => '原因必须是字符串',
            'Max' => '原因不能超过500个字符',
        ],
    ],
    
    // Save Service Schedules Request
    'SaveServiceSchedules' => [
        'Schedules' => [
            'Required' => '服务排班不能为空',
            'Array' => '服务排班必须是数组格式',
            'DayOfWeek' => [
                'Required' => '星期几不能为空',
                'Integer' => '星期几必须是整数',
                'Min' => '星期几最小值为1',
                'Max' => '星期几最大值为7',
            ],
            'StartTime' => [
                'Required' => '开始时间不能为空',
                'DateFormat' => '开始时间必须是HH:mm格式',
            ],
            'EndTime' => [
                'Required' => '结束时间不能为空',
                'DateFormat' => '结束时间必须是HH:mm格式',
                'After' => '结束时间必须晚于开始时间',
            ],
            'IsRestDay' => [
                'Boolean' => '休息日标识必须是布尔值',
            ],
        ],
    ],
    'SaveServiceSpecialSchedules' => [
        'Date' => [
            'Required' => '日期不能为空',
            'DateFormat' => '日期必须是YYYY-MM-DD格式',
        ],
        'Schedules' => [
            'Required' => '特殊排班不能为空',
            'Array' => '特殊排班必须是数组格式',
            'StartTime' => [
                'Required' => '开始时间不能为空',
                'DateFormat' => '开始时间必须是HH:mm格式',
            ],
            'EndTime' => [
                'Required' => '结束时间不能为空',
                'DateFormat' => '结束时间必须是HH:mm格式',
                'After' => '结束时间必须晚于开始时间',
            ],
            'IsRestDay' => [
                'Boolean' => '休息日标识必须是布尔值',
            ],
        ],
    ],
    'SaveServicePaymentMethods' => [
        'PaymentMethods' => [
            'Required' => '支付方式不能为空',
            'Array' => '支付方式必须是数组格式',
            '*' => [
                'Required' => '每个支付方式不能为空',
                'String' => '每个支付方式必须是字符串',
                'In' => '无效的支付方式，必须是：现金、微信、支付宝、银行卡其中之一',
            ],
        ],
    ],
    'ListStaff' => [
        'Page' => [
            'Integer' => '页码必须是整数',
            'Min' => '页码必须大于等于1',
        ],
        'PerPage' => [
            'Integer' => '每页数量必须是整数',
            'Min' => '每页数量必须大于等于1',
            'Max' => '每页数量不能超过100',
        ],
        'Keyword' => [
            'String' => '关键词必须是字符串',
            'Max' => '关键词长度不能超过50个字符',
        ],
        'Status' => [
            'Integer' => '状态必须是整数',
            'In' => '状态必须是0（禁用）或1（启用）',
        ],
        'LocationId' => [
            'Integer' => '地点ID必须是整数',
            'Exists' => '所选地点不存在',
        ],
        'SortField' => [
            'String' => '排序字段必须是字符串',
            'In' => '排序字段必须是id、created_at或updated_at之一',
        ],
        'SortOrder' => [
            'String' => '排序方式必须是字符串',
            'In' => '排序方式必须是asc（升序）或desc（降序）',
        ],
    ],
    'ListService' => [
        'Keyword' => [
            'String' => '关键词必须是字符串',
            'Max' => '关键词长度不能超过50个字符',
        ],
        'Name' => [
            'Max' => '服务名称长度不能超过50个字符',
        ],
        'CategoryId' => [
            'Integer' => '分类ID必须是整数',
            'Exists' => '所选分类不存在',
        ],
        'Status' => [
            'Boolean' => '状态必须是布尔值',
        ],
        'IsVisible' => [
            'Boolean' => '可见性必须是布尔值',
        ],
        'Page' => [
            'Integer' => '页码必须是整数',
            'Min' => '页码必须大于等于1',
        ],
        'Limit' => [
            'Integer' => '每页数量必须是整数',
            'Min' => '每页数量必须大于等于1',
            'Max' => '每页数量不能超过100',
        ],
    ],
    'ListServiceCategory' => [
        'Name' => [
            'Max' => '分类名称长度不能超过50个字符',
        ],
        'Status' => [
            'Boolean' => '状态必须是布尔值',
        ],
        'Page' => [
            'Integer' => '页码必须是整数',
            'Min' => '页码必须大于等于1',
        ],
        'Limit' => [
            'Integer' => '每页数量必须是整数',
            'Min' => '每页数量必须大于等于1',
            'Max' => '每页数量不能超过100',
        ],
    ],
    'ListLocation' => [
        'Name' => [
            'Max' => '地点名称长度不能超过50个字符',
        ],
        'Page' => [
            'Integer' => '页码必须是整数',
            'Min' => '页码必须大于等于1',
        ],
        'Limit' => [
            'Integer' => '每页数量必须是整数',
            'Min' => '每页数量必须大于等于1',
            'Max' => '每页数量不能超过100',
        ],
    ],
    
    // Error messages from ErrorCode
    'error' => [
        'unknown_error' => '未知错误',
        'invalid_params' => '无效的参数',
        'unauthorized' => '未授权',
        'forbidden' => '禁止访问',
        'not_found' => '资源不存在',
        'method_not_allowed' => '方法不允许',
        'validation_error' => '验证错误',
        'service_unavailable' => '服务不可用',
        'setting_not_found' => '设置项不存在',

        // Business errors
        'business_error' => '业务错误',
        'data_not_found' => '数据不存在',
        'data_already_exists' => '数据已存在',
        'data_validation_failed' => '数据验证失败',
        'operation_failed' => '操作失败',
        'status_invalid' => '状态无效',
        'permission_denied' => '权限不足',

        // Appointment errors
        'appointment_not_found' => '预约不存在',
        'appointment_already_exists' => '预约已存在',
        'appointment_status_invalid' => '预约状态无效',
        'appointment_time_conflict' => '预约时间冲突',
        'appointment_capacity_exceeded' => '预约人数已满',
        'appointment_cancel_failed' => '取消预约失败',
        'appointment_update_failed' => '更新预约失败',
        'appointment_export_failed' => '导出预约失败',
        'appointment_import_failed' => '导入预约失败',
        'appointment_cannot_delete' => '无法删除预约',
        'appointment_cannot_update' => '无法更新预约',
        'appointment_expired' => '预约已过期',
        'appointment_already_paid' => '预约已支付',
        'payment_update_failed' => '更新支付信息失败',

        // Service errors
        'service_not_found' => '服务不存在',
        'service_unavailable_time' => '服务时间不可用',
        'service_staff_not_available' => '服务人员不可用',
        'service_location_not_available' => '服务地点不可用',
        'service_has_appointments' => '服务存在预约',
        'service_delete_failed' => '删除服务失败',

        // Customer errors
        'customer_not_found' => '客户不存在',
        'customer_blacklisted' => '客户已被列入黑名单',
        'customer_appointment_limit_exceeded' => '客户预约次数已达上限',
        'customer_already_exists' => '客户已存在',
        'customer_email_exists' => '客户邮箱已存在',
        'customer_phone_exists' => '客户手机号已存在',
        'customer_has_appointments' => '客户存在预约',
        'customer_export_failed' => '导出客户数据失败',
        'customer_delete_failed' => '删除客户失败',
        'customer_delete_failed_batch' => '删除客户失败',
        'customer_import_failed' => '导入客户数据失败',

        // Save errors
        'save_extra_service_failed' => '保存额外服务失败',
        'save_special_schedule_failed' => '保存特殊排班失败',
        'save_staff_failed' => '保存员工失败',
        'save_schedule_failed' => '保存排班失败',

        // Discount errors
        'discount_not_found' => '折扣不存在',
        'discount_name_exists' => '折扣名称已存在',
        'discount_in_use' => '折扣正在使用中',
        'discount_create_failed' => '创建折扣失败',
        'discount_update_failed' => '更新折扣失败',
        'invalid_discount_type' => '无效的折扣类型',
        'invalid_discount_status' => '无效的折扣状态',
        'invalid_date_range' => '无效的日期范围',

        // Staff errors
        'staff_not_found' => '员工不存在',
        'staff_already_exists' => '员工已存在',
        'staff_delete_failed' => '删除员工失败',
        'staff_update_failed' => '更新员工失败',
        'staff_create_failed' => '创建员工失败',
        'email_already_exists' => '邮箱已存在',
        'phone_already_exists' => '手机号已存在',
        'position_not_found' => '职位不存在',
        'department_not_found' => '部门不存在',
        'staff_export_failed' => '导出员工数据失败',
        'staff_import_failed' => '导入员工数据失败',
        'staff_import_failed_message' => '导入员工数据失败，成功：:success_count，失败：:fail_count',

        // Email template errors
        'email_template_variable_is_not_valid' => '邮件模板变量无效',
        'email_template_variable_is_not_valid_message' => '邮件模板变量：:variable 无效，有效变量：:valid_variables',
    ],
    'import_template' => [
        'customer_email_required' => '客户邮箱不能为空',
        'customer_email_email' => '请输入有效的邮箱地址',
        'customer_phone_required' => '客户手机号不能为空',
        'customer_phone_string' => '手机号必须是字符串',
        'customer_phone_max' => '手机号长度不能超过20个字符',
        'customer_name_required' => '客户姓名不能为空',
        'customer_name_string' => '姓名必须是字符串',
        'customer_name_max' => '姓名长度不能超过50个字符',
        'customer_gender_string' => '性别必须是字符串',
        'customer_gender_max' => '性别长度不能超过10个字符',
        'service_name_required' => '服务名称不能为空',
        'service_name_string' => '服务名称必须是字符串',
        'service_name_max' => '服务名称长度不能超过100个字符',
        'location_required' => '地点不能为空',
        'location_string' => '地点必须是字符串',
        'location_max' => '地点长度不能超过50个字符',
        'appointment_date_required' => '预约日期不能为空',
        'appointment_date_date' => '预约日期必须是有效的日期',
        'status_string' => '状态必须是字符串',
        'status_max' => '状态长度不能超过20个字符',
        'discount_name_string' => '折扣名称必须是字符串',
        'discount_name_max' => '折扣名称长度不能超过100个字符',
        'payment_method_string' => '支付方式必须是字符串',
        'payment_method_max' => '支付方式长度不能超过50个字符',
        'payment_status_string' => '支付状态必须是字符串',
        'payment_status_max' => '支付状态长度不能超过20个字符',
        'payment_time_date' => '支付时间必须是有效的日期',
        'remark_string' => '备注必须是字符串',
        'remark_max' => '备注长度不能超过500个字符',
    ],
    'SaveGeneralSetting' => [
        'AppointmentLimit' => [
            'String' => '预约限制必须是字符串',
            'In' => '预约限制必须是no_limit或member_only',
        ],
        'NonMemberRecord' => [
            'Boolean' => '非会员预约记录必须是布尔值',
        ],
        'TimeSlotInterval' => [
            'Integer' => '时段粒度必须是整数',
            'In' => '时段粒度必须是5,10,15,30,60分钟',
        ],
        'CrossTimeSlot' => [
            'Boolean' => '支持跨时段预约必须是布尔值',
        ],
        'CrossTimeSlotReview' => [
            'Boolean' => '跨时段需要审核必须是布尔值',
        ],
        'AdvanceBookingTime' => [
            'Integer' => '提前预约时间必须是整数',
        ],
        'MaxBookingDays' => [
            'Integer' => '预约天数限制必须是整数',
        ],
        'DefaultAppointmentStatus' => [
            'Integer' => '预约状态必须是整数',
            'In' => '预约状态必须是0,1,2,3,4,5,6',
        ],
        'DefaultPaymentStatus' => [
            'Integer' => '支付状态必须是整数',
            'In' => '支付状态必须是0,1,2',
        ],
        'AllowOvertimeBooking' => [
            'Boolean' => '允许超时预约必须是布尔值',
        ],
        'AllowAdminOvertimeBooking' => [
            'Boolean' => '允许管理员在非工作时间预约必须是布尔值',
        ],
        'CustomerId' => [
            'MemberOnly' => '客户必须是会员',
        ],
        'AppointmentRescheduleNotification' => [
            'String' => '预约改期通知必须是字符串',
        ],
        'AppointmentCancelNotification' => [
            'String' => '预约取消通知必须是字符串',
        ],
        'AppointmentConfirmNotification' => [
            'String' => '预约确认通知必须是字符串',
        ],
        'HolidaySettings' => [
            'Array' => '假期设置必须是数组',
        ],
        'HolidaySettings.Start' => [
            'Required' => '假期开始日期不能为空',
        ],
        'HolidaySettings.End' => [
            'Required' => '假期结束日期不能为空',
        ],
    ],
    'store_email_template' => [
        'name' => [
            'required' => '模板名称不能为空',
            'string' => '模板名称必须是字符串',
            'max' => '模板名称长度不能超过100个字符'
        ],
        'code' => [
            'required' => '模板代码不能为空',
            'string' => '模板代码必须是字符串',
            'max' => '模板代码长度不能超过50个字符'
        ],
        'subject' => [
            'required' => '邮件主题不能为空',
            'string' => '邮件主题必须是字符串',
            'max' => '邮件主题长度不能超过200个字符'
        ],
        'content' => [
            'required' => '邮件内容不能为空',
            'string' => '邮件内容必须是字符串'
        ],
        'status' => [
            'boolean' => '状态必须是布尔值'
        ],
        'to' => [
            'required' => '邮件收件人不能为空',
            'string' => '邮件收件人必须是字符串',
            'max' => '邮件收件人长度不能超过100个字符'
        ],
        'attachments' => [
            'array' => '附件必须是数组'
        ]
    ],
    'update_email_template' => [
        'name' => [
            'required' => '模板名称不能为空',
            'string' => '模板名称必须是字符串',
            'max' => '模板名称长度不能超过100个字符'
        ],
        'code' => [
            'required' => '模板代码不能为空',
            'string' => '模板代码必须是字符串',
            'max' => '模板代码长度不能超过50个字符'
        ],
        'subject' => [
            'required' => '邮件主题不能为空',
            'string' => '邮件主题必须是字符串',
            'max' => '邮件主题长度不能超过200个字符'
        ],
        'content' => [
            'required' => '邮件内容不能为空',
            'string' => '邮件内容必须是字符串'
        ]
    ],
    'list_email_template' => [
        'keyword' => [
            'string' => '关键词必须是字符串',
            'max' => '关键词长度不能超过50个字符'
        ],
        'name' => [
            'string' => '模板名称必须是字符串',
            'max' => '模板名称长度不能超过100个字符'
        ],
        'code' => [
            'string' => '模板代码必须是字符串',
            'max' => '模板代码长度不能超过50个字符'
        ],
        'status' => [
            'integer' => '状态必须是整数',
            'in' => '状态必须是0（禁用）或1（启用）'
        ],
        'page' => [
            'integer' => '页码必须是整数',
            'min' => '页码必须大于等于1'
        ],
        'limit' => [
            'integer' => '每页数量必须是整数',
            'min' => '每页数量必须大于等于1',
            'max' => '每页数量不能超过100'
        ],
        'sort_field' => [
            'string' => '排序字段必须是字符串',
            'in' => '排序字段必须是id、name、code、sort、created_at或updated_at之一'
        ],
        'sort_order' => [
            'string' => '排序方式必须是字符串',
            'in' => '排序方式必须是asc（升序）或desc（降序）'
        ]
    ],
];
