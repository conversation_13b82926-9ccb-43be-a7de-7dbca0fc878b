<?php

declare(strict_types=1);

namespace Modules\Appointment\Enums;

use Bingo\Enums\Traits\EnumEnhance;

/**
 * 预约模块错误码
 */
enum ErrorCode: int
{
    use EnumEnhance;

    /**
     * 通用错误码 (10000-10999)
     */
    case UNKNOWN_ERROR = 10000;
    case INVALID_PARAMS = 10001;
    case UNAUTHORIZED = 10002;
    case FORBIDDEN = 10003;
    case NOT_FOUND = 10004;
    case METHOD_NOT_ALLOWED = 10005;
    case VALIDATION_ERROR = 10006;
    case SERVICE_UNAVAILABLE = 10007;
    case SETTING_NOT_FOUND = 10008;

    /**
     * 预约相关错误码 (11000-11999)
     */
    case APPOINTMENT_NOT_FOUND = 11000;
    case APPOINTMENT_ALREADY_EXISTS = 11001;
    case APPOINTMENT_STATUS_INVALID = 11002;
    case APPOINTMENT_TIME_CONFLICT = 11003;
    case APPOINTMENT_CAPACITY_EXCEEDED = 11004;
    case APPOINTMENT_CANCEL_FAILED = 11005;
    case APPOINTMENT_UPDATE_FAILED = 11006;
    case APPOINTMENT_EXPORT_FAILED = 11007;
    case APPOINTMENT_IMPORT_FAILED = 11008;
    case APPOINTMENT_CANNOT_DELETE = 11009;
    case APPOINTMENT_CANNOT_UPDATE = 11010;
    case PAYMENT_UPDATE_FAILED = 11011;
    case INVALID_STATUS = 11012;
    case APPOINTMENT_EXPIRED = 11013;
    case APPOINTMENT_ALREADY_PAID = 11014;


    /**
     * 服务相关错误码 (12000-12999)
     */
    case SERVICE_NOT_FOUND = 12000;
    case SERVICE_UNAVAILABLE_TIME = 12001;
    case SERVICE_STAFF_NOT_AVAILABLE = 12002;
    case SERVICE_LOCATION_NOT_AVAILABLE = 12003;
    case SERVICE_HAS_APPOINTMENTS = 12004;
    case SERVICE_DELETE_FAILED = 12005;

    /**
     * 客户相关错误码 (13000-13099)
     */
    case CUSTOMER_NOT_FOUND = 13000;
    case CUSTOMER_BLACKLISTED = 13001;
    case CUSTOMER_APPOINTMENT_LIMIT_EXCEEDED = 13002;
    case CUSTOMER_ALREADY_EXISTS = 13003;
    case CUSTOMER_EMAIL_EXISTS = 13004;
    case CUSTOMER_PHONE_EXISTS = 13005;
    case CUSTOMER_HAS_APPOINTMENTS = 13006;
    case CUSTOMER_EXPORT_FAILED = 13007;
    case CUSTOMER_DELETE_FAILED = 13008;
    case CUSTOMER_IMPORT_FAILED = 13009;

    /**
     * 数据保存相关错误码 (13100-13199)
     */
    case SAVE_EXTRA_SERVICE_FAILED = 13100;
    case SAVE_SPECIAL_SCHEDULE_FAILED = 13101;
    case SAVE_STAFF_FAILED = 13102;
    case SAVE_SCHEDULE_FAILED = 13103;

    /**
     * 折扣相关错误码 (14000-14999)
     */
    case DISCOUNT_NOT_FOUND = 14000;
    case DISCOUNT_NAME_EXISTS = 14001;      
    case DISCOUNT_IN_USE = 14002;
    case DISCOUNT_CREATE_FAILED = 14003;
    case DISCOUNT_UPDATE_FAILED = 14004;
    case INVALID_DISCOUNT_TYPE = 14005;
    case INVALID_DISCOUNT_STATUS = 14006;
    case INVALID_DATE_RANGE = 14007;

    /**
     * 员工相关错误码 (15000-15999)
     */
    case STAFF_NOT_FOUND = 15000;
    case STAFF_ALREADY_EXISTS = 15001;
    case STAFF_DELETE_FAILED = 15002;
    case STAFF_UPDATE_FAILED = 15003;
    case EMAIL_ALREADY_EXISTS = 15004;
    case PHONE_ALREADY_EXISTS = 15005;
    case POSITION_NOT_FOUND = 15006;
    case DEPARTMENT_NOT_FOUND = 15007;
    case STAFF_EXPORT_FAILED = 15008;
    case STAFF_IMPORT_FAILED = 15009;
    case STAFF_CREATE_FAILED = 15010;

    /**
     * 邮件模板相关错误码 (16000-16999)
     */
    case EMAIL_TEMPLATE_NOT_FOUND = 16000;
    case EMAIL_TEMPLATE_ALREADY_EXISTS = 16001;
    case EMAIL_TEMPLATE_DELETE_FAILED = 16002;
    case EMAIL_TEMPLATE_UPDATE_FAILED = 16003;
    case EMAIL_TEMPLATE_VARIABLE_IS_NOT_VALID = 16004;
    case EMAIL_TEMPLATE_VARIABLE_IS_NOT_VALID_MESSAGE = 16005;
    

    /**
     * 获取错误信息
     */
    public function message(): string
    {
        return match ($this) {
            // 通用错误码
            self::UNKNOWN_ERROR => T("Appointment::validation.error.unknown_error"),
            self::INVALID_PARAMS => T("Appointment::validation.error.invalid_params"),
            self::UNAUTHORIZED => T("Appointment::validation.error.unauthorized"),
            self::FORBIDDEN => T("Appointment::validation.error.forbidden"),
            self::NOT_FOUND => T("Appointment::validation.error.not_found"),
            self::METHOD_NOT_ALLOWED => T("Appointment::validation.error.method_not_allowed"),
            self::VALIDATION_ERROR => T("Appointment::validation.error.validation_error"),
            self::SERVICE_UNAVAILABLE => T("Appointment::validation.error.service_unavailable"),

            // 预约相关错误码
            self::APPOINTMENT_NOT_FOUND => T("Appointment::validation.error.appointment_not_found"),
            self::APPOINTMENT_ALREADY_EXISTS => T("Appointment::validation.error.appointment_already_exists"),
            self::APPOINTMENT_STATUS_INVALID => T("Appointment::validation.error.appointment_status_invalid"),
            self::APPOINTMENT_TIME_CONFLICT => T("Appointment::validation.error.appointment_time_conflict"),
            self::APPOINTMENT_CAPACITY_EXCEEDED => T("Appointment::validation.error.appointment_capacity_exceeded"),
            self::APPOINTMENT_CANCEL_FAILED => T("Appointment::validation.error.appointment_cancel_failed"),
            self::APPOINTMENT_UPDATE_FAILED => T("Appointment::validation.error.appointment_update_failed"),
            self::APPOINTMENT_EXPORT_FAILED => T("Appointment::validation.error.appointment_export_failed"),
            self::APPOINTMENT_IMPORT_FAILED => T("Appointment::validation.error.appointment_import_failed"),
            self::APPOINTMENT_CANNOT_DELETE => T("Appointment::validation.error.appointment_cannot_delete"),
            self::APPOINTMENT_CANNOT_UPDATE => T("Appointment::validation.error.appointment_cannot_update"),
            self::PAYMENT_UPDATE_FAILED => T("Appointment::validation.error.payment_update_failed"),
            self::INVALID_STATUS => T("Appointment::validation.error.invalid_status"),
            self::APPOINTMENT_EXPIRED => T("Appointment::validation.error.appointment_expired"),
            self::APPOINTMENT_ALREADY_PAID => T("Appointment::validation.error.appointment_already_paid"),


            // 服务相关错误码
            self::SERVICE_NOT_FOUND => T("Appointment::validation.error.service_not_found"),
            self::SERVICE_UNAVAILABLE_TIME => T("Appointment::validation.error.service_unavailable_time"),
            self::SERVICE_STAFF_NOT_AVAILABLE => T("Appointment::validation.error.service_staff_not_available"),
            self::SERVICE_LOCATION_NOT_AVAILABLE => T("Appointment::validation.error.service_location_not_available"),
            self::SERVICE_HAS_APPOINTMENTS => T("Appointment::validation.error.service_has_appointments"),
            self::SERVICE_DELETE_FAILED => T("Appointment::validation.error.service_delete_failed"),

            // 客户相关错误码
            self::CUSTOMER_NOT_FOUND => T("Appointment::validation.error.customer_not_found"),
            self::CUSTOMER_BLACKLISTED => T("Appointment::validation.error.customer_blacklisted"),
            self::CUSTOMER_APPOINTMENT_LIMIT_EXCEEDED => T("Appointment::validation.error.customer_appointment_limit_exceeded"),
            self::CUSTOMER_ALREADY_EXISTS => T("Appointment::validation.error.customer_already_exists"),
            self::CUSTOMER_EMAIL_EXISTS => T("Appointment::validation.error.customer_email_exists"),    
            self::CUSTOMER_PHONE_EXISTS => T("Appointment::validation.error.customer_phone_exists"),
            self::CUSTOMER_HAS_APPOINTMENTS => T("Appointment::validation.error.customer_has_appointments"),
            self::CUSTOMER_EXPORT_FAILED => T("Appointment::validation.error.customer_export_failed"),
            self::CUSTOMER_DELETE_FAILED => T("Appointment::validation.error.customer_delete_failed"),
            self::CUSTOMER_IMPORT_FAILED => T("Appointment::validation.error.customer_import_failed"),

            // 数据保存相关错误码
            self::SAVE_EXTRA_SERVICE_FAILED => T("Appointment::validation.error.save_extra_service_failed"),
            self::SAVE_SPECIAL_SCHEDULE_FAILED => T("Appointment::validation.error.save_special_schedule_failed"),
            self::SAVE_STAFF_FAILED => T("Appointment::validation.error.save_staff_failed"),
            self::SAVE_SCHEDULE_FAILED => T("Appointment::validation.error.save_schedule_failed"),

            // 折扣相关错误码
            self::DISCOUNT_NOT_FOUND => T("Appointment::validation.error.discount_not_found"),
            self::DISCOUNT_NAME_EXISTS => T("Appointment::validation.error.discount_name_exists"),
            self::DISCOUNT_IN_USE => T("Appointment::validation.error.discount_in_use"),
            self::DISCOUNT_CREATE_FAILED => T("Appointment::validation.error.discount_create_failed"),
            self::DISCOUNT_UPDATE_FAILED => T("Appointment::validation.error.discount_update_failed"),
            self::INVALID_DISCOUNT_TYPE => T("Appointment::validation.error.invalid_discount_type"),
            self::INVALID_DISCOUNT_STATUS => T("Appointment::validation.error.invalid_discount_status"),
            self::INVALID_DATE_RANGE => T("Appointment::validation.error.invalid_date_range"),

            // 员工相关错误码
            self::STAFF_NOT_FOUND => T("Appointment::validation.error.staff_not_found"),
            self::STAFF_ALREADY_EXISTS => T("Appointment::validation.error.staff_already_exists"),
            self::STAFF_DELETE_FAILED => T("Appointment::validation.error.staff_delete_failed"),
            self::STAFF_UPDATE_FAILED => T("Appointment::validation.error.staff_update_failed"),
            self::EMAIL_ALREADY_EXISTS => T("Appointment::validation.error.email_already_exists"),
            self::PHONE_ALREADY_EXISTS => T("Appointment::validation.error.phone_already_exists"),
            self::POSITION_NOT_FOUND => T("Appointment::validation.error.position_not_found"),
            self::DEPARTMENT_NOT_FOUND => T("Appointment::validation.error.department_not_found"),
            self::STAFF_EXPORT_FAILED => T("Appointment::validation.error.staff_export_failed"),
            self::STAFF_IMPORT_FAILED => T("Appointment::validation.error.staff_import_failed"),
            self::STAFF_CREATE_FAILED => T("Appointment::validation.error.staff_create_failed"),

            // 邮件模板相关错误码
            self::EMAIL_TEMPLATE_NOT_FOUND => T("Appointment::validation.error.email_template_not_found"),
            self::EMAIL_TEMPLATE_ALREADY_EXISTS => T("Appointment::validation.error.email_template_already_exists"),
            self::EMAIL_TEMPLATE_DELETE_FAILED => T("Appointment::validation.error.email_template_delete_failed"),
            self::EMAIL_TEMPLATE_UPDATE_FAILED => T("Appointment::validation.error.email_template_update_failed"),
            self::EMAIL_TEMPLATE_VARIABLE_IS_NOT_VALID => T("Appointment::validation.error.email_template_variable_is_not_valid"),
            self::EMAIL_TEMPLATE_VARIABLE_IS_NOT_VALID_MESSAGE => T("Appointment::validation.error.email_template_variable_is_not_valid_message"),
        };
    }
}
