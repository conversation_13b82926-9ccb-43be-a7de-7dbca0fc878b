<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('appointment_service_schedules', function (Blueprint $table) {
            $table->comment('服务排期表');
            $table->id()->autoIncrement()->comment('ID');
            $table->unsignedBigInteger('service_id')->comment('服务ID');
            $table->tinyInteger('day_of_week')->comment('星期几：1-7代表周一至周日');
            $table->time('start_time')->default('00:00:00')->comment('开始时间');
            $table->time('end_time')->default('00:00:00')->comment('结束时间');
            $table->text('rest_time_list')->nullable()->comment('休息时段列表');
            $table->boolean('is_rest_day')->default(0)->comment('是否休息日：1-是，0-否');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
            $table->index('service_id', 'service_schedules_service_id_index');
            $table->unique(['service_id', 'day_of_week', 'deleted_at'], 'service_schedules_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_service_schedules');
    }
};
