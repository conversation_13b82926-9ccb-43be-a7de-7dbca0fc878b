<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建预约系统邮件模板表
 */
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        Schema::create('appointment_email_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('模板名称');
            $table->string('code', 50)->comment('模板代码');
            $table->string('subject', 255)->comment('邮件主题');
            $table->longText('content')->comment('邮件内容');
            $table->text('attachments')->nullable()->comment('附件');
            $table->boolean('status')->default(true)->comment('状态：1-启用，0-禁用'); 
            $table->string('creator_name', 100)->default('')->comment('创建人名称');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            $table->unique(['code', 'deleted_at'], 'appointment_email_templates_code_unique');
            $table->unique(['name', 'deleted_at'], 'appointment_email_templates_name_unique');

            $table->comment('预约系统邮件模板表');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_email_templates');
    }
}; 