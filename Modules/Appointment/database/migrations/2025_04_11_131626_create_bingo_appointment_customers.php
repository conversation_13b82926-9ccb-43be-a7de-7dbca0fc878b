<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('appointment_customers', function (Blueprint $table) {
            $table->comment('客户表');
            $table->id()->autoIncrement()->comment('ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->integer('appointment_count')->default(0)->comment('预约次数');
            $table->unsignedInteger('last_appointment_at')->default(0)->comment('最近预约时间');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
            $table->unique(['user_id', 'deleted_at'], 'customers_user_id_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_customers');
    }
};
