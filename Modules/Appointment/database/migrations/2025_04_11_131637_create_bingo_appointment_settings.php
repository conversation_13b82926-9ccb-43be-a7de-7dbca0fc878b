<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('appointment_settings', function (Blueprint $table) {
            $table->comment('预约设置表');
            $table->id()->autoIncrement()->comment('ID');
            $table->string('key', 100)->comment('设置键');
            $table->text('value')->nullable()->comment('设置值');
            $table->text('description')->nullable()->comment('描述');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');

            $table->unique(['key', 'deleted_at'], 'appointment_settings_key_unique');
        });

        // 初始化系统设置数据
        $this->initSettings();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_settings');
    }

    /**
     * 初始化系统设置数据
     *
     * @return void
     */
    private function initSettings(): void
    {
        $time = time();

        // 基本设置
        $settings = [
            [
                'key' => 'appointment_limit',
                'value' => 'member_only',
                'description' => '预约限制：no_limit-无限制，member_only-仅会员可预约',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            [
                'key' => 'non_member_record',
                'value' => '1',
                'description' => '非会员预约记录：1-保存，0-不保存',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            [
                'key' => 'time_slot_interval',
                'value' => '30',
                'description' => '时段粒度(分钟)：5，10，15，30，60',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            [
                'key' => 'cross_time_slot',
                'value' => '1',
                'description' => '支持跨时段预约：1-是，0-否',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            [
                'key' => 'advance_booking_time',
                'value' => '24',
                'description' => '提前预定时间(小时)',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            [
                'key' => 'max_booking_days',
                'value' => '365',
                'description' => '预约天数限制',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            [
                'key' => 'default_appointment_status',
                'value' => '0',
                'description' => '默认预约状态',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            [
                'key' => 'default_payment_status',
                'value' => '0',
                'description' => '默认支付状态',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            [
                'key' => 'allow_overtime_booking',
                'value' => '0',
                'description' => '允许超时预约：1-是，0-否',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            [
                'key' => 'allow_admin_overtime_booking',
                'value' => '0',
                'description' => '允许管理员在非工作时间预约：1-是，0-否',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            
            // 通知设置
            [
                'key' => 'appointment_reschedule_notification',
                'value' => '',
                'description' => '预约改期通知 - email,sms',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            [
                'key' => 'appointment_cancel_notification',
                'value' => '',
                'description' => '预约取消通知 - email,sms',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            [
                'key' => 'appointment_confirm_notification',
                'value' => '',
                'description' => '预约确认通知 - email,sms',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
            
            // 假期设置
            [
                'key' => 'holiday_settings',
                'value' => '',
                'description' => '假期设置',
                'creator_id' => 0,
                'created_at' => $time,
                'updated_at' => $time,
                'deleted_at' => 0,
            ],
        ];

        // 插入数据
        DB::table('appointment_settings')->insert($settings);
    }
};
