<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('appointment_discounts', function (Blueprint $table) {
            $table->comment('折扣表');
            $table->id()->autoIncrement()->comment('ID');
            $table->string('name', 100)->comment('折扣名称');
            $table->string('type', 20)->comment('折扣类型：percentage-百分比，fixed-固定金额');
            $table->decimal('value', 10, 2)->comment('折扣值');
            $table->dateTime('start_date')->nullable()->comment('开始日期');
            $table->dateTime('end_date')->nullable()->comment('结束日期');
            $table->boolean('status')->default(1)->comment('状态：1-启用，0-禁用');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
            $table->unique(['name', 'deleted_at'], 'discounts_name_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_discounts');
    }
};
