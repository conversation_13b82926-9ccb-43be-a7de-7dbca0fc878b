<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('appointment_records', function (Blueprint $table) {
            $table->comment('预约记录表');
            $table->id()->autoIncrement()->comment('ID');
            $table->unsignedBigInteger('customer_id')->comment('客户ID');
            $table->unsignedBigInteger('service_id')->comment('服务ID');
            $table->string('location', 255)->comment('预约地点');
            $table->dateTime('appointment_date')->comment('预约开始时间');
            $table->dateTime('end_time')->comment('预约结束时间');
            $table->tinyInteger('status')->default(0)->comment('状态：0-待确认，1-已确认，2-已改期，3-已完成，4-已取消，5-已拒绝，6-未到');
            $table->unsignedBigInteger('discount_id')->nullable()->comment('折扣ID');
            $table->decimal('original_price', 10, 2)->default(0.00)->comment('原价');
            $table->decimal('discount_amount', 10, 2)->nullable()->comment('折扣金额');
            $table->decimal('final_price', 10, 2)->default(0.00)->comment('最终价格');
            $table->string('payment_method', 50)->nullable()->comment('支付方式');
            $table->tinyInteger('payment_status')->default(0)->comment('支付状态：0-待支付，1-已支付，2-支付失败');
            $table->dateTime('payment_time')->nullable()->comment('支付时间');
            $table->string('remark', 500)->nullable()->comment('备注');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
            $table->index('customer_id', 'records_customer_id_index');
            $table->index('service_id', 'records_service_id_index');
            $table->index('appointment_date', 'records_appointment_date_index');
            $table->index('end_time', 'records_end_time_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_records');
    }
};
