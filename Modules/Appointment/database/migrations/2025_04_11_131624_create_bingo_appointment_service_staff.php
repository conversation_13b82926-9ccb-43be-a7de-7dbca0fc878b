<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('appointment_service_staff', function (Blueprint $table) {
            $table->comment('服务员工表');
            $table->id()->autoIncrement()->comment('ID');
            $table->unsignedBigInteger('service_id')->comment('服务ID');
            $table->unsignedBigInteger('staff_id')->comment('员工ID');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
            $table->unique(['service_id', 'staff_id'], 'service_staff_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_service_staff');
    }
};
