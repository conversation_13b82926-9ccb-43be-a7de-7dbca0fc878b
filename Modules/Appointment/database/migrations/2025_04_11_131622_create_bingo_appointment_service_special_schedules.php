<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('appointment_service_special_schedules', function (Blueprint $table) {
            $table->comment('服务特殊排期表');
            $table->id()->autoIncrement()->comment('ID');
            $table->unsignedBigInteger('service_id')->comment('服务ID');
            $table->dateTime('start_time')->default(now())->comment('开始时间');
            $table->dateTime('end_time')->default(now())->comment('结束时间');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
            $table->index('service_id', 'service_special_schedules_service_id_index');
            $table->unique(['service_id', 'start_time', 'end_time', 'deleted_at'], 'service_special_schedules_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_service_special_schedules');
    }
};
