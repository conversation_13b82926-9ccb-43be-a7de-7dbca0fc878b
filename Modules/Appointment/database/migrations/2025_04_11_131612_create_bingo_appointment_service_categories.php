<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('appointment_service_categories', function (Blueprint $table) {
            $table->comment('服务分类表');
            $table->id()->autoIncrement()->comment('ID');
            $table->string('name', 100)->comment('分类名称');
            $table->text('description')->nullable()->comment('分类描述');
            $table->integer('sort')->default(0)->comment('排序');
            $table->boolean('status')->default(1)->comment('状态：1-启用，0-禁用');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
            $table->unique(['name', 'deleted_at'], 'name_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_service_categories');
    }
};
