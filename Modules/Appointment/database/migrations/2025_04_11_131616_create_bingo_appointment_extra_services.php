<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('appointment_extra_services', function (Blueprint $table) {
            $table->comment('额外服务表');
            $table->id()->autoIncrement()->comment('ID');
            $table->string('name', 100)->comment('额外服务名称');
            $table->string('category_name', 100)->comment('分类名称');
            $table->decimal('price', 10, 2)->default(0.00)->comment('额外服务价格');
            $table->integer('duration')->default(0)->comment('额外服务时长(分钟)');
            $table->integer('max_quantity')->default(1)->comment('最大可选数量');
            $table->integer('min_quantity')->default(1)->comment('最小可选数量');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
            $table->unique(['name', 'deleted_at'], 'extra_services_name_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_extra_services');
    }
};
