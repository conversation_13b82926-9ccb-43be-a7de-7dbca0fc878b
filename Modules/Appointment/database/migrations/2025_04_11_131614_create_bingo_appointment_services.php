<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('appointment_services', function (Blueprint $table) {
            $table->comment('服务表');
            $table->id()->autoIncrement()->comment('ID');
            $table->unsignedBigInteger('category_id')->comment('分类ID');
            $table->string('name', 100)->comment('服务名称');
            $table->decimal('price', 10, 2)->default(0.00)->comment('服务价格');
            $table->integer('duration')->default(1800)->comment('服务时长(秒) 默认30分钟');
            $table->boolean('is_show_price')->default(1)->comment('客户端预訂面板不顯示價格');
            $table->boolean('is_show_duration')->default(1)->comment('客户端预訂面板不顯示時長');
            $table->boolean('is_repeat_service')->default(1)->comment('重複fuwu');
            $table->boolean('is_repeat_service_pay_type')->default(1)->comment('重複收費方式 1-按次，2-按重复周期');
            $table->boolean('is_repeat_service_category')->default(1)->comment('重複類型 1-按天，2-按周，3-按月');
            $table->integer('is_repeat_service_count')->default(1)->comment('固定次數');
            $table->integer('person_count')->default(1)->comment('服務人數');
            $table->text('description')->nullable()->comment('服务描述');
            $table->string('pay_types', 255)->default('')->comment('支付方式');
            $table->integer('advance_booking_time')->default(0)->comment('提前预约最小时间');
            $table->boolean('only_member_visible')->default(0)->comment('仅会员可见 1-是，0-否');
            $table->boolean('status')->default(1)->comment('状态：1-启用，0-禁用');
            $table->unsignedInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
            $table->index('category_id', 'services_category_id_index');
            $table->unique(['name', 'deleted_at'], 'services_name_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_services');
    }
};
