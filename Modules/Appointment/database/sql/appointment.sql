-- 预约系统数据库表结构

-- 服务分类表
DROP TABLE IF EXISTS `bwms`.`bingo_appointment_service_categories`;
CREATE TABLE `bwms`.`bingo_appointment_service_categories` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text DEFAULT NULL COMMENT '分类描述',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_unique` (`name`, `deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务分类表';

-- 服务表
DROP TABLE IF EXISTS `bwms`.`bingo_appointment_services`;
CREATE TABLE `bwms`.`bingo_appointment_services` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_id` bigint(20) UNSIGNED NOT NULL COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '服务名称',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '服务价格',
  `duration` int(11) NOT NULL DEFAULT 1800 COMMENT '服务时长(秒) 默认30分钟',
  `is_show_price` tinyint(1) NOT NULL DEFAULT 1 COMMENT '客户端预訂面板不顯示價格',
  `is_show_duration` tinyint(1) NOT NULL DEFAULT 1 COMMENT '客户端预訂面板不顯示時長',
  `is_repeat_service` tinyint(1) NOT NULL DEFAULT 1 COMMENT '重複fuwu',
  `is_repeat_service_pay_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '重複收費方式 1-按次，2-按重复周期',
  `is_repeat_service_category` tinyint(1) NOT NULL DEFAULT 1 COMMENT '重複類型 1-按天，2-按周，3-按月',
  `is_repeat_service_count` int(11) NOT NULL DEFAULT 1 COMMENT '固定次數',
  `person_count` int(11) NOT NULL DEFAULT 1 COMMENT '服務人數',
  `description` text DEFAULT NULL COMMENT '服务描述',
  `pay_types` varchar(255) NOT NULL DEFAULT '' COMMENT '支付方式',
  `advance_booking_time` int(11) NOT NULL DEFAULT 0 COMMENT '提前预约最小时间',
  `only_member_visible` tinyint(1) NOT NULL DEFAULT 0 COMMENT '仅会员可见 1-是，0-否',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `services_category_id_index` (`category_id`),
  UNIQUE KEY `services_name_unique` (`name`, `deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务表';

-- 额外服务表
DROP TABLE IF EXISTS `bwms`.`bingo_appointment_extra_services`;
CREATE TABLE `bwms`.`bingo_appointment_extra_services` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '额外服务名称',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '额外服务价格',
  `duration` int(11) NOT NULL DEFAULT 0 COMMENT '额外服务时长(分钟)',
  `max_quantity` int(11) NOT NULL DEFAULT 1 COMMENT '最大可选数量',
  `min_quantity` int(11) NOT NULL DEFAULT 1 COMMENT '最小可选数量',
  `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `extra_services_name_unique` (`name`, `deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='额外服务表';

-- 额外服务关联表
DROP TABLE IF EXISTS `bwms`.`bingo_appointment_extra_service_relations`;
CREATE TABLE `bwms`.`bingo_appointment_extra_service_relations` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `service_id` bigint(20) UNSIGNED NOT NULL COMMENT '关联的主服务ID',
  `extra_service_id` bigint(20) UNSIGNED NOT NULL COMMENT '关联的额外服务ID',
  `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `extra_service_relations_unique` (`service_id`, `extra_service_id`, `deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='额外服务关联表';

-- 服务时间安排表
DROP TABLE IF EXISTS `bwms`.`bingo_appointment_service_schedules`;
CREATE TABLE `bwms`.`bingo_appointment_service_schedules` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `service_id` bigint(20) UNSIGNED NOT NULL COMMENT '服务ID',
  `day_of_week` tinyint(1) NOT NULL COMMENT '星期几：1-7代表周一至周日',
  `start_time` time NOT NULL DEFAULT '00:00:00' COMMENT '开始时间',
  `end_time` time NOT NULL DEFAULT '00:00:00' COMMENT '结束时间',
  `rest_time_list` text DEFAULT NULL COMMENT '休息时段列表',
  `is_rest_day` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否休息日：1-是，0-否',
  `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `service_schedules_service_id_index` (`service_id`),
  UNIQUE KEY `service_schedules_unique` (`service_id`, `day_of_week`,`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务时间安排表';

-- 服务特殊时间安排表
DROP TABLE IF EXISTS `bwms`.`bingo_appointment_service_special_schedules`;
CREATE TABLE `bwms`.`bingo_appointment_service_special_schedules` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `service_id` bigint(20) UNSIGNED NOT NULL COMMENT '服务ID',
  `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
  `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `service_special_schedules_service_id_index` (`service_id`),
  UNIQUE KEY `service_special_schedules_unique` (`service_id`, `start_time`, `end_time`, `deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务特殊时间安排表';

-- 服务人员安排表
DROP TABLE IF EXISTS `bwms`.`bingo_appointment_service_staff`;
CREATE TABLE `bwms`.`bingo_appointment_service_staff` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `service_id` bigint(20) UNSIGNED NOT NULL COMMENT '服务ID',
  `staff_id` bigint(20) UNSIGNED NOT NULL COMMENT '员工ID',
  `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `service_staff_unique` (`service_id`, `staff_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务人员安排表';

-- 客户表
DROP TABLE IF EXISTS `bwms`.`bingo_appointment_customers`;
CREATE TABLE `bwms`.`bingo_appointment_customers` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `appointment_count` int(11) NOT NULL DEFAULT 0 COMMENT '预约次数',
  `last_appointment_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '最近预约时间',
  `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `customers_user_id_unique` (`user_id`,`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户表';

-- 折扣表
DROP TABLE IF EXISTS `bwms`.`bingo_appointment_discounts`;
CREATE TABLE `bwms`.`bingo_appointment_discounts` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '折扣名称',
  `type` varchar(20) NOT NULL COMMENT '折扣类型：percentage-百分比，fixed-固定金额',
  `value` decimal(10,2) NOT NULL COMMENT '折扣值',
  `start_date` datetime DEFAULT NULL COMMENT '开始日期',
  `end_date` datetime DEFAULT NULL COMMENT '结束日期',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `discounts_name_unique` (`name`,`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='折扣表';

-- 预约表
DROP TABLE IF EXISTS `bwms`.`bingo_appointment_records`;
CREATE TABLE `bwms`.`bingo_appointment_records` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_id` bigint(20) UNSIGNED NOT NULL COMMENT '客户ID',
  `service_id` bigint(20) UNSIGNED NOT NULL COMMENT '服务ID',
  `location` varchar(255) NOT NULL COMMENT '预约地点',
  `appointment_date` datetime NOT NULL COMMENT '预约开始时间',
  `end_time` datetime NOT NULL COMMENT '预约结束时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0-待确认，1-已确认，2-已改期，3-已完成，4-已取消，5-已拒绝，6-未到',
  `discount_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '折扣ID',
  `original_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '原价',
  `discount_amount` decimal(10,2) DEFAULT NULL COMMENT '折扣金额',
  `final_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '最终价格',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式',
  `payment_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付状态：0-待支付，1-已支付，2-支付失败',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `records_customer_id_index` (`customer_id`),
  KEY `records_service_id_index` (`service_id`),
  KEY `records_appointment_date_index` (`appointment_date`),
  KEY `records_end_time_index` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预约记录表';

-- 系统设置表
DROP TABLE IF EXISTS `bwms`.`bingo_appointment_settings`;
CREATE TABLE `bwms`.`bingo_appointment_settings` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `key` varchar(100) NOT NULL COMMENT '设置键',
  `value` text DEFAULT NULL COMMENT '设置值',
  `description` text DEFAULT NULL COMMENT '描述',
  `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `appointment_settings_key_unique` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 邮件模板
DROP TABLE IF EXISTS `bwms`.`bingo_appointment_email_templates`;
CREATE TABLE `bwms`.`bingo_appointment_email_templates` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `code` varchar(50) NOT NULL COMMENT '模板代码',
  `subject` varchar(255) NOT NULL COMMENT '邮件主题',
  `content` text NOT NULL COMMENT '邮件内容',
  `attachments` text DEFAULT NULL COMMENT '附件',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `creator_name` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人名称',
  `creator_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `appointment_email_templates_name_unique` (`name`, `deleted_at`),
  UNIQUE KEY `appointment_email_templates_code_unique` (`code`, `deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件模板表';

-- 初始化系统设置数据
INSERT INTO `bwms`.`bingo_appointment_settings` (`key`, `value`, `description`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
('appointment_limit', 'member_only', '预约限制：no_limit-无限制，member_only-仅会员可预约', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
('non_member_record', '1', '非会员预约记录：1-保存，0-不保存', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
('time_slot_interval', '30', '时段粒度(分钟)：5，10，15，30，60', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
('cross_time_slot', '1', '支持跨时段预约：1-是，0-否', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
('advance_booking_time', '24', '提前预定时间(小时)', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
('max_booking_days', '365', '预约天数限制', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
('default_appointment_status', '0', '默认预约状态', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
('default_payment_status', '0', '默认支付状态', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
('allow_overtime_booking', '0', '允许超时预约：1-是，0-否', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
('allow_admin_overtime_booking', '0', '允许管理员在非工作时间预约：1-是，0-否', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0);


-- 初始化通知设置数据
INSERT INTO `bwms`.`bingo_appointment_settings` (`key`, `value`, `description`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
('appointment_reschedule_notification', '', '预约改期通知', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
('appointment_cancel_notification', '', '预约取消通知', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
('appointment_confirm_notification', '', '预约确认通知', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0);

-- 初始化假期设置数据
INSERT INTO `bwms`.`bingo_appointment_settings` (`key`, `value`, `description`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
('holiday_settings', '', '假期设置', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0);



