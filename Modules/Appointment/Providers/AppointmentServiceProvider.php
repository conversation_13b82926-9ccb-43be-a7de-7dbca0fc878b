<?php

namespace Modules\Appointment\Providers;

use Bingo\Providers\BingoModuleServiceProvider;
use Modules\Members\Enums\MenuType;

class AppointmentServiceProvider extends BingoModuleServiceProvider
{
    public function boot(): void
    {
        $path = dirname(__DIR__, 2).DIRECTORY_SEPARATOR.'Appointment'.DIRECTORY_SEPARATOR.'Lang';
        $this->loadTranslationsFrom($path, 'Appointment');
        $this->registerNavigation();
        $this->registerModulePermissions();
    }

    /**
     * route path
     *
     * @return string
     */
    public function moduleName(): string
    {
        return 'Appointment';
    }

    protected function navigation(): array
    {
        return [
            [
                "key" => "appointment",
                "parent" => "application",
                "nav_name" => T("Appointment::nav.admin_setting.appointment_title"),
                "path" => "/appointment",
                "icon" => "Calendar",
                "order" => 10,
                "children" => [
                    [
                        "key" => "dashboard",
                        "parent" => "appointment",
                        "nav_name" => T("Appointment::nav.admin_setting.dashboard"),
                        "path" => "/appointment/dashboard",
                        "icon" => "DataBoard",
                        "order" => 1,
                    ],
                    [
                        "key" => "appointments",
                        "parent" => "appointment",
                        "nav_name" => T("Appointment::nav.admin_setting.appointments"),
                        "path" => "/appointment/appointments",
                        "icon" => "Calendar",
                        "order" => 2,
                    ],
                    [
                        "key" => "customers",
                        "parent" => "appointment",
                        "nav_name" => T("Appointment::nav.admin_setting.customers"),
                        "path" => "/appointment/customers",
                        "icon" => "User",
                        "order" => 3,
                    ],
                    [
                        "key" => "services",
                        "parent" => "appointment",
                        "nav_name" => T("Appointment::nav.admin_setting.services"),
                        "path" => "/appointment/services",
                        "icon" => "Service",
                        "order" => 4,
                    ],
                    [
                        "key" => "staff",
                        "parent" => "appointment",
                        "nav_name" => T("Appointment::nav.admin_setting.staff"),
                        "path" => "/appointment/staff",
                        "icon" => "User",
                        "order" => 5,
                    ],
                    [
                        "key" => "settings",
                        "parent" => "appointment",
                        "nav_name" => T("Appointment::nav.admin_setting.settings"),
                        "path" => "/appointment/settings",
                        "icon" => "Setting",
                        "order" => 6,
                    ],
                    [
                        "key" => "calendar",
                        "parent" => "appointment",
                        "nav_name" => T("Appointment::nav.admin_setting.calendar"),
                        "path" => "/appointment/calendar",
                        "icon" => "Calendar",
                        "order" => 7,
                    ],
                    [
                        "key" => "payments",
                        "parent" => "appointment",
                        "nav_name" => T("Appointment::nav.admin_setting.payments"),
                        "path" => "/appointment/payments",
                        "icon" => "Money",
                        "order" => 8,
                    ],
                    [
                        "key" => "email_templates",
                        "parent" => "appointment",
                        "nav_name" => T("Appointment::nav.admin_setting.email_templates"),
                        "path" => "/appointment/email-templates",
                        "icon" => "ChatDotRound",
                        "order" => 9,
                    ]
                ]
            ]
        ];
    }

    /**
     * 注册配置
     * @return array
     */
    public function registerSettings(): array
    {
        return [
        ];
    }

    public function registerPermissions(): array
    {
        $admin = [
            // 预约管理顶级权限
            [
                'permission_name' => T("Appointment::permission.admin_setting.title"),
                'route' => '/appointment',
                'parent_id' => '0',
                'permission_mark' => 'appointment',
                'component' => '/admin/layout/index.vue',
                'type' => MenuType::Top->value(),
                'children' => [
                    // 预约权限
                    [
                        'permission_name' => T("Appointment::permission.admin_setting.appointments"),
                        'route' => '/appointment/appointments',
                        'parent_id' => 'appointment',
                        'permission_mark' => 'appointment_appointments',
                        'component' => '/admin/views/appointment/appointments/index.vue',
                        'type' => MenuType::Menu->value(),
                        'actions' => [
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.list"),
                                'route' => '/appointment/appointments',
                                'permission_mark' => 'appointment_appointments@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_appointments'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.detail"),
                                'route' => '/appointment/appointments/{id}',
                                'permission_mark' => 'appointment_appointments@show',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_appointments'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.create"),
                                'route' => '/appointment/appointments',
                                'permission_mark' => 'appointment_appointments@store',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_appointments'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.update"),
                                'route' => '/appointment/appointments/{id}',
                                'permission_mark' => 'appointment_appointments@update',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_appointments'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.delete"),
                                'route' => '/appointment/appointments/{id}',
                                'permission_mark' => 'appointment_appointments@destroy',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_appointments'
                            ],
                        ]
                    ],
                    // 客户权限
                    [
                        'permission_name' => T("Appointment::permission.admin_setting.customers"),
                        'route' => '/appointment/customers',
                        'parent_id' => 'appointment',
                        'permission_mark' => 'appointment_customers',
                        'component' => '/admin/views/appointment/customers/index.vue',
                        'type' => MenuType::Menu->value(),
                        'actions' => [
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.list"),
                                'route' => '/appointment/customers',
                                'permission_mark' => 'appointment_customers@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_customers'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.detail"),
                                'route' => '/appointment/customers/{id}',
                                'permission_mark' => 'appointment_customers@show',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_customers'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.create"),
                                'route' => '/appointment/customers',
                                'permission_mark' => 'appointment_customers@store',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_customers'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.update"),
                                'route' => '/appointment/customers/{id}',
                                'permission_mark' => 'appointment_customers@update',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_customers'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.delete"),
                                'route' => '/appointment/customers/{id}',
                                'permission_mark' => 'appointment_customers@destroy',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_customers'
                            ],
                        ]
                    ],
                    // 服务权限
                    [
                        'permission_name' => T("Appointment::permission.admin_setting.services"),
                        'route' => '/appointment/services',
                        'parent_id' => 'appointment',
                        'permission_mark' => 'appointment_services',
                        'component' => '/admin/views/appointment/services/index.vue',
                        'type' => MenuType::Menu->value(),
                        'actions' => [
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.list"),
                                'route' => '/appointment/services',
                                'permission_mark' => 'appointment_services@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_services'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.detail"),
                                'route' => '/appointment/services/{id}',
                                'permission_mark' => 'appointment_services@show',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_services'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.create"),
                                'route' => '/appointment/services',
                                'permission_mark' => 'appointment_services@store',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_services'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.update"),
                                'route' => '/appointment/services/{id}',
                                'permission_mark' => 'appointment_services@update',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_services'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.delete"),
                                'route' => '/appointment/services/{id}',
                                'permission_mark' => 'appointment_services@destroy',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_services'
                            ],
                        ]
                    ],
                    // 员工权限
                    [
                        'permission_name' => T("Appointment::permission.admin_setting.staff"),
                        'route' => '/appointment/staff',
                        'parent_id' => 'appointment',
                        'permission_mark' => 'appointment_staff',
                        'component' => '/admin/views/appointment/staff/index.vue',
                        'type' => MenuType::Menu->value(),
                        'actions' => [
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.list"),
                                'route' => '/appointment/staff',
                                'permission_mark' => 'appointment_staff@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_staff'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.detail"),
                                'route' => '/appointment/staff/{id}',
                                'permission_mark' => 'appointment_staff@show',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_staff'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.create"),
                                'route' => '/appointment/staff',
                                'permission_mark' => 'appointment_staff@store',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_staff'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.update"),
                                'route' => '/appointment/staff/{id}',
                                'permission_mark' => 'appointment_staff@update',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_staff'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.delete"),
                                'route' => '/appointment/staff/{id}',
                                'permission_mark' => 'appointment_staff@destroy',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_staff'
                            ],
                        ]
                    ],
                    // 折扣权限
                    [
                        'permission_name' => T("Appointment::permission.admin_setting.discounts"),
                        'route' => '/appointment/discounts',
                        'parent_id' => 'appointment',
                        'permission_mark' => 'appointment_discounts',
                        'component' => '/admin/views/appointment/discounts/index.vue',
                        'type' => MenuType::Menu->value(),
                        'actions' => [
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.list"),
                                'route' => '/appointment/discounts',
                                'permission_mark' => 'appointment_discounts@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_discounts'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.detail"),
                                'route' => '/appointment/discounts/{id}',
                                'permission_mark' => 'appointment_discounts@show',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_discounts'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.create"),
                                'route' => '/appointment/discounts',
                                'permission_mark' => 'appointment_discounts@store',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_discounts'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.update"),
                                'route' => '/appointment/discounts/{id}',
                                'permission_mark' => 'appointment_discounts@update',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_discounts'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.delete"),
                                'route' => '/appointment/discounts/{id}',
                                'permission_mark' => 'appointment_discounts@destroy',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_discounts'
                            ],
                        ]
                    ],
                    // 设置权限
                    [
                        'permission_name' => T("Appointment::permission.admin_setting.settings"),
                        'route' => '/appointment/settings',
                        'parent_id' => 'appointment',
                        'permission_mark' => 'appointment_settings',
                        'component' => '/admin/views/appointment/settings/index.vue',
                        'type' => MenuType::Menu->value(),
                        'actions' => [
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.list"),    
                                'route' => '/appointment/settings',
                                'permission_mark' => 'appointment_settings@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_settings'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.update"),
                                'route' => '/appointment/settings',
                                'permission_mark' => 'appointment_settings@update',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_settings'
                            ]
                        ]
                    ],
                    // 邮件模板权限
                    [
                        'permission_name' => T("Appointment::permission.admin_setting.email_templates"),
                        'route' => '/appointment/email-templates',
                        'parent_id' => 'appointment',
                        'permission_mark' => 'appointment_email_templates', 
                        'component' => '/admin/views/appointment/email-templates/index.vue',
                        'type' => MenuType::Menu->value(),
                        'actions' => [
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.list"),
                                'route' => '/appointment/email-templates',
                                'permission_mark' => 'appointment_email_templates@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_email_templates'
                            ],  
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.detail"),
                                'route' => '/appointment/email-templates/{id}',
                                'permission_mark' => 'appointment_email_templates@show',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_email_templates'
                            ],
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.save"),
                                'route' => '/appointment/email-templates',
                                'permission_mark' => 'appointment_email_templates@store',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_email_templates'
                            ],  
                            [
                                'permission_name' => T("Appointment::permission.admin_setting.action.delete"),
                                'route' => '/appointment/email-templates/{id}',
                                'permission_mark' => 'appointment_email_templates@destroy',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'parent_id' => 'appointment_email_templates'
                            ],  
                        ]
                    ],
                ]
            ]
        ];
        $frontend = [];
        return array_merge(["admin" => $admin], ["frontend" => $frontend]);
    }
}
