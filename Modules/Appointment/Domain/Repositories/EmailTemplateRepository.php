<?php

declare(strict_types=1); // 启用严格类型模式

namespace Modules\Appointment\Domain\Repositories; // 定义命名空间

use Illuminate\Contracts\Pagination\LengthAwarePaginator; // 引入分页接口
use Modules\Appointment\Models\AppointmentEmailTemplate; // 引入邮件模板模型

/**
 * 邮件模板仓储实现
 */
class EmailTemplateRepository
{
    /**
     * 获取邮件模板列表
     *
     * @param array $filters 查询条件
     * @return LengthAwarePaginator
     */
    public function getEmailTemplates(array $filters): LengthAwarePaginator
    {
        // 初始化查询构建器
        $query = AppointmentEmailTemplate::query();

        // 按关键字筛选
        if (isset($filters['keyword'])) {
            $query->where(function ($query) use ($filters) {
                $query->where('name', 'like', "%{$filters['keyword']}%")
                    ->orWhere('code', 'like', "%{$filters['keyword']}%");
            });
        }

        // 按名称搜索
        if (isset($filters['name']) && $filters['name']) {
            $query->where('name', 'like', "%{$filters['name']}%"); // 使用like进行模糊匹配
        }

        // 按代码搜索
        if (isset($filters['code']) && $filters['code']) {
            $query->where('code', 'like', "%{$filters['code']}%"); // 使用like进行模糊匹配
        }

        // 按状态筛选
        if (isset($filters['status']) && $filters['status'] !== '') {
            $query->where('status', $filters['status']); // 精确匹配状态
        }

        // 排序
        $query->orderBy($filters['sort_field'] ?? 'id', $filters['sort_order'] ?? 'desc'); // 默认按sort字段升序排序

        $page = (int)($filters['page'] ?? 1); // 获取当前页码，默认为1
        $limit = (int)($filters['limit'] ?? 15); // 获取每页显示数量，默认为15

        return $query->paginate($limit, ['*'], 'page', $page); // 返回分页结果
    }

    /**
     * 获取邮件模板详情
     *
     * @param int $id 邮件模板ID
     * @return AppointmentEmailTemplate|null
     */
    public function getEmailTemplateById(int $id): ?AppointmentEmailTemplate
    {
        return AppointmentEmailTemplate::find($id); // 根据ID查找邮件模板
    }

    /**
     * 获取邮件模板详情
     *
     * @param string $code 邮件模板代码
     * @return AppointmentEmailTemplate|null
     */
    public function getEmailTemplateByCode(string $code): ?AppointmentEmailTemplate
    {
        return AppointmentEmailTemplate::where('code', $code)->first();
    }

    /**
     * 获取邮件模板详情
     *
     * @param string $name 邮件模板名称
     * @return AppointmentEmailTemplate|null
     */
    public function getEmailTemplateByName(string $name): ?AppointmentEmailTemplate
    {
        return AppointmentEmailTemplate::where('name', $name)->first();
    }

    /**
     * 删除邮件模板
     *
     * @param int $id 邮件模板ID
     * @return bool
     */
    public function deleteEmailTemplate(int $id): bool
    {
        $template = $this->getEmailTemplateById($id);
        if (!$template) {
            return false;
        }

        return $template->delete();
    }
    
    /**
     * 更新邮件模板状态
     *
     * @param int $id 邮件模板ID
     * @param int $status 状态值
     * @return bool
     */
    public function updateEmailTemplateStatus(int $id, int $status): bool
    {
        $template = $this->getEmailTemplateById($id);
        if (!$template) {
            return false;
        }

        $template->status = $status;
        return $template->save();
    }
} 