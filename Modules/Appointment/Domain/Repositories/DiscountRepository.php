<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Repositories;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Models\AppointmentDiscount;
use Modules\Appointment\Models\AppointmentRecords;

/**
 * 折扣仓储实现
 */
final class DiscountRepository
{
    /**
     * 获取折扣列表
     *
     * @param array $filters 过滤条件
     * @return LengthAwarePaginator
     */
    public function list(array $filters): LengthAwarePaginator
    {
        $page = $filters['page'] ?? 1;
        $limit = $filters['limit'] ?? 15;
        $sortField = $filters['sort_field'] ?? 'id';
        $sortOrder = $filters['sort_order'] ?? 'desc';

        $query = AppointmentDiscount::query();

        // 关键词搜索
        if (!empty($filters['keyword'])) {
            $keyword = $filters['keyword'];
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%");
            });
        }

        // 按状态筛选
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // 按类型筛选
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        // 排序
        $query->orderBy($sortField, $sortOrder);

        return $query->paginate($limit, ['*'], 'page', $page);
    }

    /**
     * 根据ID查找折扣
     *
     * @param int $id 折扣ID
     * @return ?AppointmentDiscount
     */
    public function findById(int $id): ?AppointmentDiscount
    {
        return AppointmentDiscount::find($id);
    }

    /**
     * 根据折扣名称查找折扣
     *
     * @param string $name 折扣名称
     * @return ?AppointmentDiscount
     */
    public function findByName(string $name): ?AppointmentDiscount
    {
        return AppointmentDiscount::where('name', $name)->first();
    }

    /**
     * 创建折扣
     *
     * @param array $data 折扣数据
     * @return AppointmentDiscount
     */
    public function create(array $data): AppointmentDiscount
    {
        return AppointmentDiscount::create($data);
    }

    /**
     * 更新折扣
     *
     * @param int $id 折扣ID
     * @param array $data 更新数据
     * @return bool
     */
    public function update(int $id, array $data): bool
    {
        $discount = $this->findById($id);
        
        if ($discount) {
            return $discount->update($data);
        }
        
        return false;
    }

    /**
     * 删除折扣
     *
     * @param int $id 折扣ID
     * @return bool
     */
    public function delete(int $id): bool
    {
        $discount = $this->findById($id);
        
        if ($discount) {
            return $discount->delete();
        }
        
        return false;
    }

    /**
     * 检查折扣是否正在使用中
     *
     * @param int $id 折扣ID
     * @return bool
     */
    public function isDiscountInUse(int $id): bool
    {
        return AppointmentRecords::where('discount_id', $id)->exists();
    }
} 