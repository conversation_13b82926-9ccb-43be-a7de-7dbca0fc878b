<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Repositories;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Models\IamUser;
use Modules\Iam\Models\IamPositions;
use Modules\Iam\Models\IamDepartments;
use Modules\Iam\Models\IamUserDepartments;
use Modules\Iam\Models\IamUserPositions;

/**
 * 员工仓储类
 */
class StaffRepository
{
    /**
     * IAM用户模型
     *
     * @var IamUser
     */
    private $staff;


    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->staff = new IamUser();
    }

    /**
     * 获取员工列表
     *
     * @param array $filters 过滤条件
     * @return LengthAwarePaginator
     */
    public function list(array $filters): LengthAwarePaginator
    {
        // 提取分页参数
        $page = (int)($filters['page'] ?? 1);
        $limit = (int)($filters['limit'] ?? 15);

        // 构建查询
        $query = IamUser::query()->with(['department', 'position'])
            ->where('application_id', IamUser::APPLICATION_BACKEND) // 只查询后台员工
            ->where('user_type', IamUser::USER_TYPE_EMPLOYEE); // 只查询员工

        // 关键字搜索
        if (isset($filters['keyword'])) {
            $query->where('name', 'like', "%{$filters['keyword']}%");
        }

        // 排序处理
        if (!empty($filters['sort_field']) && !empty($filters['sort_order'])) {
            $query->orderBy($filters['sort_field'], $filters['sort_order']);
        } else {
            $query->orderBy('id', 'desc');
        }

        // 返回分页结果
        return $query->paginate($limit, ['*'], 'page', $page);
    }

    /**
     * 获取员工详情
     *
     * @param int $id 员工ID
     * @return IamUser
     */
    public function getStaffDetail($id): IamUser
    {
        return $this->staff->find($id);
    }

    /**
     * 根据邮箱查找员工
     *
     * @param string $email 邮箱
     * @return IamUser|null
     */
    public function findByEmail(string $email): ?IamUser
    {
        return IamUser::where('email', $email)
            ->where('application_id', IamUser::APPLICATION_BACKEND) // 只查询后台员工
            ->where('user_type', IamUser::USER_TYPE_EMPLOYEE) // 只查询员工
            ->first();
    }

    /**
     * 根据手机号查找员工
     *
     * @param string $phone 手机号
     * @return IamUser|null
     */
    public function findByPhone(string $phone): ?IamUser
    {
        return IamUser::where('phone', $phone)
            ->where('application_id', IamUser::APPLICATION_BACKEND) // 只查询后台员工
            ->where('user_type', IamUser::USER_TYPE_EMPLOYEE) // 只查询员工
            ->first();
    }

    /**
     * 根据ID查找职位
     *
     * @param int $positionId 职位ID
     * @return mixed
     */
    public function findPosition(int $positionId)
    {
        // 假设职位模型为Position
        return IamPositions::where('id', $positionId)->first();
    }

    /**
     * 根据ID查找部门
     *
     * @param int $departmentId 部门ID
     * @return mixed
     */
    public function findDepartment(int $departmentId)
    {
        // 假设部门模型为Department
        return IamDepartments::where('id', $departmentId)->first();
    }

    /**
     * 更新员工信息
     *
     * @param int $id 员工ID
     * @param array $data 更新数据
     * @return bool
     */
    public function update(int $id, array $data): bool
    {
        $staff = $this->staff->find($id);
        if (!$staff) {
            return false;
        }

        return $staff->update($data);
    }

    /**
     * 根据邮箱查找员工（排除指定ID）
     *
     * @param string $email 邮箱
     * @param int $excludeId 排除的员工ID
     * @return IamUser|null
     */
    public function findByEmailExcept(string $email, int $excludeId): ?IamUser
    {
        return IamUser::where('email', $email)
            ->where('id', '!=', $excludeId)
            ->where('application_id', IamUser::APPLICATION_BACKEND)
            ->where('user_type', IamUser::USER_TYPE_EMPLOYEE)
            ->first();
    }

    /**
     * 根据手机号查找员工（排除指定ID）
     *
     * @param string $phone 手机号
     * @param int $excludeId 排除的员工ID
     * @return IamUser|null
     */
    public function findByPhoneExcept(string $phone, int $excludeId): ?IamUser
    {
        return IamUser::where('phone', $phone)
            ->where('id', '!=', $excludeId)
            ->where('application_id', IamUser::APPLICATION_BACKEND)
            ->where('user_type', IamUser::USER_TYPE_EMPLOYEE)
            ->first();
    }

    /**
     * 更新员工与部门的关联
     *
     * @param int $userId 用户ID
     * @param int $departmentId 部门ID
     * @param int $creatorId 创建者ID
     * @return bool
     */
    public function updateUserDepartment(int $userId, int $departmentId, ?int $creatorId): bool
    {
        // 查找是否已有部门关联
        $existingDept = IamUserDepartments::where('user_id', $userId)
            ->where('department_id', $departmentId)
            ->where('application_id', IamUser::APPLICATION_BACKEND)
            ->first();

        // 如果存在则更新，否则创建新关联
        if ($existingDept) {
            return $existingDept->update([
                'department_id' => $departmentId,
            ]) > 0;
        } else {
            // 创建新的部门关联
            return (bool)\Modules\Iam\Models\IamUserDepartments::create([
                'user_id' => $userId,
                'department_id' => $departmentId,
                'application_id' => IamUser::APPLICATION_BACKEND,
                'creator_id' => $creatorId,
                'joined_at' => time(),
            ]);
        }
    }

    /**
     * 更新员工与职位的关联
     *
     * @param int $userId 用户ID
     * @param int $positionId 职位ID
     * @param int $creatorId 创建者ID
     * @return bool
     */
    public function updateUserPosition(int $userId, int $positionId, ?int $creatorId): bool
    {
        // 查找是否已有职位关联
        $existingPosition = IamUserPositions::where('user_id', $userId)
            ->where('position_id', $positionId)
            ->first();

        // 如果存在则更新，否则创建新关联
        if ($existingPosition) {
            return $existingPosition->update([
                'position_id' => $positionId,
            ]) > 0;
        } else {
            // 创建新的职位关联
            return (bool)\Modules\Iam\Models\IamUserPositions::create([
                'user_id' => $userId,
                'position_id' => $positionId,
                'creator_id' => $creatorId,
                'joined_at' => time(),
            ]);
        }
    }

    /**
     * 删除员工基本信息
     *
     * @param int $id 员工ID
     * @return bool
     */
    public function deleteBatch(array $userIds): bool
    {
        return $this->staff->whereIn('id', $userIds)->delete() > 0;
    }

    /**
     * 删除员工与部门的关联关系
     *
     * @param int $userId 用户ID
     * @return bool
     */
    public function deleteBatchUserDepartmentRelation(array $userIds): bool
    {
        return IamUserDepartments::whereIn('user_id', $userIds)->delete() > 0;
    }

    /**
     * 删除员工与职位的关联关系
     *
     * @param int $userId 用户ID
     * @return bool
     */
    public function deleteBatchUserPositionRelation(array $userIds): bool
    {
        return IamUserPositions::whereIn('user_id', $userIds)->delete() > 0;
    }

    /**
     * 根据职位名称查找职位
     *
     * @param string $positionName 职位名称 
     * @return IamPositions|null
     */
    public function findPositionByName(string $positionName): ?IamPositions
    {
        return IamPositions::where('position_name', $positionName)->first();
    }

    /**
     * 根据部门名称查找部门
     *
     * @param string $departmentName 部门名称
     * @return IamDepartments|null
     */
    public function findDepartmentByName(string $departmentName): ?IamDepartments
    {
        return IamDepartments::where('name', $departmentName)->first();
    }

    /**
     * 创建员工
     *
     * @param array $data 员工数据
     * @return IamUser
     */
    public function create(array $data): IamUser
    {
        $data['password'] = password_hash(IamUser::DEFAULT_PASSWORD, PASSWORD_DEFAULT);
        $data['application_id'] = IamUser::APPLICATION_BACKEND;
        $data['user_type'] = IamUser::USER_TYPE_EMPLOYEE;
        return $this->staff->create($data);
    }
}
