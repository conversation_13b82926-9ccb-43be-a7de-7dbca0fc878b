<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Repositories;

use Modules\Iam\Models\IamPositions;

/**
 * 职位仓储类
 */
class PositionRepository
{
    /**
     * 获取职位列表
     * 
     * @param array $params 查询参数
     * @return array 职位列表数据
     */
    public function list(array $params = []): array
    {
        $query = IamPositions::query();
        
        // 排序
        $sortField = $params['sort_field'] ?? 'id';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->orderBy($sortField, $sortOrder);
        
        // 分页
        $page = (int)($params['page'] ?? 1);
        $limit = (int)($params['limit'] ?? 15);
        
        $total = $query->count();
        $items = $query->select('id', 'position_code', 'position_name', 'description', 'created_at', 'updated_at')
            ->offset(($page - 1) * $limit)
            ->limit($limit)
            ->get();
        
        return [
            'items' => $items,
            'total' => $total,
        ];
    }
} 