<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Repositories;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Models\AppointmentLocation;

/**
 * 地点仓储实现
 */
class LocationRepository
{
    /**
     * 获取地点列表
     *
     * @param array<string, mixed> $filters 过滤条件
     * @return LengthAwarePaginator
     */
    public function list(array $filters): LengthAwarePaginator
    {
        $query = AppointmentLocation::query();

        // 检查name参数是否存在且不为空
        if (!empty($filters['name'])) {
            $query->where('name', 'like', '%' . trim($filters['name']) . '%');
        }

        // 设置默认分页大小
        $limit = $filters['limit'] ?? 15;
        
        // 验证分页大小范围
        if ($limit < 1 || $limit > 100) {
            $limit = 15;
        }

        // 设置默认页码
        $page = $filters['page'] ?? 1;

        $paginate = $query->orderByDesc('id')->paginate($limit, ['*'], 'page', $page);
        return $paginate;
    }

    /**
     * 创建地点
     *
     * @param array<string, mixed> $data
     * @return AppointmentLocation
     */
    public function create(array $data): AppointmentLocation  
    {
        return AppointmentLocation::create($data);
    }

    /**
     * 更新地点
     *
     * @param int $id
     * @param array<string, mixed> $data
     * @return AppointmentLocation
     */
    public function update(int $id, array $data): AppointmentLocation
    {
        $location = $this->find($id);
        if ($location) {
            $location->update($data);
        }
        return $location;
    }

    /**
     * 根据ID获取地点
     *
     * @param int $id
     * @return AppointmentLocation|null
     */
    public function find(int $id): ?AppointmentLocation
    {
        return AppointmentLocation::find($id);
    }
} 