<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Repositories;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Models\AppointmentRecords;
use Modules\Appointment\Models\AppointmentDiscount;
use Carbon\Carbon;
use Modules\Appointment\Models\AppointmentService;

/**
 * 预约仓储实现
 */
final class AppointmentRepository
{
    /**
     * 获取预约列表
     */
    public function list(array $filters): LengthAwarePaginator
    {
        $page = $filters['page'] ?? 1;
        $limit = $filters['limit'] ?? 15;
        $sortField = $filters['sort_field'] ?? 'id';
        $sortOrder = $filters['sort_order'] ?? 'desc';
        $query = AppointmentRecords::query()
            ->with([
                'customer:id,name,phone,email,gender,status,birthdate', // 只选择客户的基本信息字段
                'service', // 加载服务信息
                'service.staffs', // 加载员工信息  
            ]);

            //追加折扣名称和折扣类型
            $query->with([
                'discount' => function ($query) {
                    $query->select('id', 'name', 'type');
                }
            ]);

        // 关键词搜索
        if (!empty($filters['keyword'])) {
            $keyword = $filters['keyword'];
            $query->whereHas('service', function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%");
            });
        }

        // 按状态筛选
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // 按客户ID筛选
        if (!empty($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }

        // 按日期范围筛选
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            // 确保日期时间格式严谨，如果没有时间部分则补充
            $startDate = $this->ensureDateTime($filters['start_date'], '00:00:00');
            $endDate = $this->ensureDateTime($filters['end_date'], '23:59:59');

            $query->whereBetween('appointment_date', [$startDate, $endDate]);
        } elseif (!empty($filters['start_date'])) {
            // 只有开始日期时
            $startDate = $this->ensureDateTime($filters['start_date'], '00:00:00');
            $query->where('appointment_date', '>=', $startDate);
        } elseif (!empty($filters['end_date'])) {
            // 只有结束日期时
            $endDate = $this->ensureDateTime($filters['end_date'], '23:59:59');
            $query->where('appointment_date', '<=', $endDate);
        }

        // 排序
        $query->orderBy($sortField, $sortOrder);

        // 获取分页结果
        $results = $query->paginate($limit, ['*'], 'page', $page);
        
        // 为每个预约记录添加持续时间字段
        $results->getCollection()->transform(function ($appointment) {
            // 计算并添加持续时间字段
            $appointment->duration = $appointment->getDuration();
            $appointment->status_text = $appointment->getStatusText();
            $appointment->payment_status_text = $appointment->getPaymentStatusText();
            return $appointment;
        });

        return $results;
    }

    /**
     * 确保日期时间格式严谨
     *
     * @param string $date 日期字符串
     * @param string $time 时间字符串
     * @return string 严谨的日期时间字符串  
     */
    /**
     * 确保日期时间格式严谨
     * 
     * @param string $date 日期或日期时间字符串
     * @param string $time 默认时间字符串
     * @return string 标准化的日期时间字符串
     */
    public function ensureDateTime(string $date, string $time): string
    {
        // 检查输入的日期是否已经包含时间部分
        if (strpos($date, ':') !== false) {
            // 已经是日期时间格式，直接解析
            return Carbon::parse($date)->format('Y-m-d H:i:s');
        }
        
        // 只有日期部分，添加时间
        $dateTime = $date . ' ' . $time;
        return Carbon::parse($dateTime)->format('Y-m-d H:i:s');
    }

    /**
     * 获取预约详情
     */
    public function findById(int $id): ?AppointmentRecords
    {
        return AppointmentRecords::find($id);
    }

    /**
     * 创建预约记录
     *
     * @param array $data 预约数据
     * @return AppointmentRecords 创建的预约记录
     */
    public function create(array $data): AppointmentRecords
    {
        $appointment = new AppointmentRecords($data);

        // 设置默认预约状态,支付状态
        $appointment->setDefaultStatus();
        $appointment->setDefaultPaymentStatus();
        
        $appointment->save();
        return $appointment;
    }

    /**
     * 检查预约时间是否冲突
     *
     * @param string $appointmentDate 预约日期时间
     * @param int|null $serviceId 服务ID
     * @param int $customerId 客户ID
     * @param int|null $excludeId 要排除的预约ID（用于更新操作）
     * @return bool 是否存在冲突
     */
    public function checkTimeConflict(string $appointmentDate, int $serviceId, int $customerId, ?int $excludeId = null): bool
    {
        // 将预约日期转换为Carbon实例，方便进行时间计算
        $date = Carbon::parse($appointmentDate);

        // 获取服务持续时间（分钟）
        $service = AppointmentService::find($serviceId);
        if ($service && $service->duration > 0) {
            $duration = $service->duration;
        } else {
            return false;
        }

        // 计算预约结束时间
        $endDate = $date->copy()->addSeconds($duration);

        // 构建查询
        $query = AppointmentRecords::query()
            ->where(function ($q) use ($date, $endDate) {
                // 检查是否有预约时间与新预约时间重叠
                // 情况1：已有预约的开始时间在新预约时间段内
                $q->where(function ($subQ) use ($date, $endDate) {
                    $subQ->where('appointment_date', '>=', $date)
                        ->where('appointment_date', '<', $endDate);
                });

                // 情况2：已有预约的结束时间在新预约时间段内
                $q->orWhere(function ($subQ) use ($date, $endDate) {
                    $subQ->where('end_time', '>', $date)
                        ->where('appointment_date', '<', $date);
                });

                // 情况3：新预约时间段完全包含已有预约时间段
                $q->orWhere(function ($subQ) use ($date, $endDate) {
                    $subQ->where('appointment_date', '<', $date)
                        ->where('end_time', '>', $endDate);
                });
            });

        $query->where('customer_id', $customerId);
        
        // 排除指定ID的预约（用于更新操作）
        if ($excludeId !== null) {
            $query->where('id', '!=', $excludeId);
        }

        // 检查是否存在满足条件的记录
        return $query->exists();
    }

    /**
     * 获取折扣信息
     *
     * @param int $discountId 折扣ID
     * @return ?AppointmentDiscount 折扣信息
     */
    public function getDiscount(int $discountId): ?AppointmentDiscount
    {
        return AppointmentDiscount::find($discountId);
    }

    /**
     * 获取服务信息
     *
     * @param int $serviceId 服务ID
     * @return ?AppointmentService 服务信息
     */
    public function getService(int $serviceId): ?AppointmentService
    {
        return AppointmentService::find($serviceId);
    }

    /**
     * 删除预约记录
     *
     * @param int $id 预约ID
     * @return bool 删除是否成功
     */
    public function delete(int $id): bool
    {
        $appointment = $this->findById($id);

        if (!empty($appointment)) {
            return $appointment->delete();
        }

        return false;
    }

    /**
     * 更新预约记录
     *
     * @param int $id 预约ID
     * @param array $data 更新数据
     * @return bool 更新是否成功
     */
    public function update(int $id, array $data): bool
    {
        $appointment = $this->findById($id);
        
        if ($appointment) {
            return $appointment->update($data);
        }
        
        return false;
    }

    
    /**
     * 根据服务名称查找服务
     *
     * @param string $name 服务名称
     * @return ?AppointmentService 服务信息
     */
    public function findServiceByName(string $name): ?AppointmentService
    {
        return AppointmentService::where('name', $name)->first();
    }

    
    /**
     * 根据折扣名称查询折扣
     *
     * @param string $name 折扣名称
     * @return ?AppointmentDiscount 折扣信息
     */
    public function findDiscountByName(string $name): ?AppointmentDiscount
    {
        return AppointmentDiscount::where('name', $name)->first();
    }

    /**
     * 更新预约状态
     *
     * @param AppointmentRecords $appointment 预约记录
     * @param int $status 状态
     * @return bool 更新是否成功
     */ 
    public function updateStatus(AppointmentRecords $appointment, int $status): bool
    {
        $appointment->status = $status;
        return $appointment->save();
    }

    /**
     * 批量查询预约记录
     *
     * @param array $ids 预约ID数组
     * @return \Illuminate\Database\Eloquent\Collection 预约记录集合
     */
    public function findByIds(array $ids): \Illuminate\Database\Eloquent\Collection
    {
        return AppointmentRecords::whereIn('id', $ids)->get();
    }

    /**
     * 批量删除预约记录
     *
     * @param array $ids 预约ID数组
     * @return bool 删除是否成功
     */
    public function deleteMany(array $ids): bool
    {
        return AppointmentRecords::whereIn('id', $ids)->delete() > 0;
    }

    /**
     * 获取今日预定列表
     *
     * @return \Illuminate\Database\Eloquent\Collection 今日预定列表
     */
    public function getTodayAppointments(): \Illuminate\Database\Eloquent\Collection
    {
        return AppointmentRecords::where('appointment_date', '>=', Carbon::today())
            ->where('appointment_date', '<=', Carbon::today()->endOfDay())
            ->get();
    }
}
