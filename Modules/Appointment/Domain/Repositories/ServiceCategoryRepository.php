<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Repositories;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Models\AppointmentServiceCategory;

/**
 * 服务分类仓储实现
 */
class ServiceCategoryRepository
{
    /**
     * 获取服务分类列表
     *
     * @param array<string, mixed> $filters 过滤条件
     * @return LengthAwarePaginator
     */
    public function list(array $filters): LengthAwarePaginator
    {
        $query = AppointmentServiceCategory::query();

        // 按名称筛选
        if (isset($filters['name'])) {
            $query->where('name', 'like', "%{$filters['name']}%");
        }

        // 按状态筛选
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // 获取分页参数
        $page = $filters['page'] ?? 1;
        $limit = $filters['limit'] ?? 15;

        // 默认按排序字段和ID倒序排列
        return $query->orderBy('sort')->orderByDesc('id')->paginate($limit, ['*'], 'page', $page);
    }

    /**
     * 创建服务分类
     *
     * @param array<string, mixed> $data
     * @return AppointmentServiceCategory
     */
    public function create(array $data): AppointmentServiceCategory
    {
        return AppointmentServiceCategory::create($data);
    }

    /**
     * 更新服务分类
     *
     * @param int $id
     * @param array<string, mixed> $data
     * @return AppointmentServiceCategory
     */
    public function update(int $id, array $data): AppointmentServiceCategory
    {
        $category = $this->find($id);
        if ($category) {
            $category->update($data);
        }
        return $category;
    }

    /**
     * 删除服务分类
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $category = $this->find($id);
        if ($category) {
            return $category->delete();
        }
        return false;
    }

    /**
     * 根据ID获取服务分类
     *
     * @param int $id
     * @return AppointmentServiceCategory|null
     */
    public function find(int $id): ?AppointmentServiceCategory
    {
        return AppointmentServiceCategory::find($id);
    }

    /**
     * 获取所有服务分类（用于下拉列表）
     *
     * @return array<int, array<string, mixed>>
     */
    public function getAllForSelect(): array
    {
        return AppointmentServiceCategory::where('status', true)
            ->orderBy('sort')
            ->orderByDesc('id')
            ->get(['id', 'name'])
            ->toArray();
    }
} 