<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Repositories;

use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Models\AppointmentService;
use Modules\Appointment\Models\AppointmentServiceStaff;
use Modules\Appointment\Models\AppointmentServiceSchedule;
use Modules\Appointment\Models\AppointmentServiceSpecialSchedule;
use Modules\Appointment\Models\AppointmentExtraService;
use Modules\Appointment\Models\AppointmentExtraServiceRelations;
use Bingo\Exceptions\BizException;
use Modules\Appointment\Enums\ErrorCode;

/**
 * 服务仓储类
 */
class ServiceRepository
{
    /**
     * 获取服务列表
     *
     * @param array<string, mixed> $filters
     * @return LengthAwarePaginator
     */
    public function list(array $filters): LengthAwarePaginator
    {
        $query = AppointmentService::query();

        // 按关键字筛选
        if (isset($filters['keyword'])) {
            $query->where(function ($query) use ($filters) {
                $query->where('name', 'like', "%{$filters['keyword']}%")
                    ->orWhere('description', 'like', "%{$filters['keyword']}%");
            });
        }

        // 按名称筛选
        if (isset($filters['name'])) {
            $query->where('name', 'like', "%{$filters['name']}%");
        }

        // 按分类筛选
        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        // 按状态筛选
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // 按可见性筛选
        if (isset($filters['is_visible'])) {
            $query->where('is_visible', $filters['is_visible']);
        }

        // 获取分页参数
        $page = $filters['page'] ?? 1;
        $limit = $filters['limit'] ?? 15;

        // 默认按ID倒序排列
        $result = $query->orderByDesc('id')->paginate($limit, ['*'], 'page', $page);

        return $result;
    }

    /**
     * 保存服务
     *
     * @param array<string, mixed> $data
     * @return AppointmentService
     */
    public function create(array $data): AppointmentService
    {
        // 过滤掉null值的字段
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });

        $service = new AppointmentService($data);

        // 设置默认持续时间
        $service->setDefaultDuration();

        $service->save();
        return $service;
    }

    /**
     * 更新服务
     *
     * @param int $id
     * @param array<string, mixed> $data
     * @return AppointmentService
     */
    public function update(int $id, array $data): AppointmentService
    {
        $service = $this->find($id);
        if (!empty($service)) {
            $service->update($data);
        }
        return $service;
    }

    /**
     * 删除服务
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $service = $this->find($id);
        if (!empty($service)) {
            return $service->delete();
        }
        return false;
    }

    /**
     * 根据ID获取服务
     *
     * @param int $id
     * @return AppointmentService
     */
    public function find(int $id): AppointmentService
    {
        return AppointmentService::findOrFail($id);
    }


    /**
     * 批量分配员工到服务
     *
     * @param int $serviceId 服务ID
     * @param array<int> $staffIds 员工ID数组
     * @param int $creatorId 创建者ID
     * @return void
     * @throws Exception
     */
    public function batchAssignStaff(int $serviceId, array $staffIds, int $creatorId = 0): void
    {
        // 准备批量插入数据
        $staffData = [];
        foreach ($staffIds as $staffId) {
            $staffData[] = [
                'service_id' => $serviceId,
                'staff_id' => $staffId,
                'creator_id' => $creatorId,
            ];
        }

        // 如果没有员工数据，直接返回
        if (empty($staffData)) {
            return;
        }

        // 批量插入员工关联数据
        $result = AppointmentServiceStaff::insert($staffData);
        if (!$result) {
            throw new Exception('保存员工信息失败');
        }
    }

    /**
     * 保存服务排班
     *
     * @param int $id
     * @param array<string, mixed> $schedules
     * @param int $creatorId
     * @return void
     * @throws BizException
     */
    public function saveSchedules(int $id, array $schedules, int $creatorId): void
    {
        // 批量创建新排班
        $scheduleDataList = [];
        foreach ($schedules as $schedule) {
            $scheduleDataList[] = [
                'service_id' => $id,
                'day_of_week' => $schedule['day_of_week'],
                'start_time' => $schedule['start_time'],
                'end_time' => $schedule['end_time'],
                'is_rest_day' => $schedule['is_rest_day'] ? 1 : 0,
                'creator_id' => $creatorId,
                'rest_time_list' => $schedule['rest_time_list'] ?? [],
            ];
        }

        $result = AppointmentServiceSchedule::insertBatch($scheduleDataList);
        if (!$result) {
            BizException::throws(ErrorCode::SAVE_SCHEDULE_FAILED);
        }
    }

    /**
     * 批量保存额外服务
     *
     * @param int $serviceId
     * @param array<string, mixed> $extraServices
     * @param int $creatorId
     * @return void
     * @throws BizException
     */
    public function saveExtraServices(int $serviceId, array $extraServices, int $creatorId): void
    {
        // 收集所有额外服务的名称和分类名称，用于检查已存在的服务
        $serviceNames = array_column($extraServices, 'name');

        // 查找已存在的额外服务
        $existingServices = AppointmentExtraService::query()
            ->whereIn('name', $serviceNames)
            ->get()
            ->keyBy(function ($service) {
                return $service->name;
            });

        // 准备批量插入数据
        $newServices = [];
        $relations = [];

        foreach ($extraServices as $extraService) {
            $key = $extraService['name'] ?? '';

            // 如果服务已存在，直接使用现有ID
            if (isset($existingServices[$key])) {
                $extraServiceId = $existingServices[$key]->id;
            } else {
                // 准备新服务数据
                $newServices[] = [
                    'name' => $extraService['name'] ?? '',
                    'category_name' => $extraService['category_name'] ?? '',
                    'price' => $extraService['price'] ?? 0,
                    'duration' => $extraService['duration'] ?? 0,
                    'max_quantity' => $extraService['max_quantity'] ?? 1,
                    'min_quantity' => $extraService['min_quantity'] ?? 1,
                    'creator_id' => $creatorId,
                ];
            }
        }

        // 批量插入新服务
        if (!empty($newServices)) {
            $insertResult = AppointmentExtraService::insert($newServices);
            if (!$insertResult) {
                BizException::throws(ErrorCode::SAVE_EXTRA_SERVICE_FAILED, ErrorCode::SAVE_EXTRA_SERVICE_FAILED->message());
            }

            // 重新获取所有服务（包括新插入的）
            $allServices = AppointmentExtraService::query()
                ->whereIn('name', $serviceNames)
                ->get()
                ->keyBy(function ($service) {
                    return $service->name;
                });
        } else {
            $allServices = $existingServices;
        }

        // 准备关联数据
        foreach ($extraServices as $extraService) {
            $key = $extraService['name'] ?? '';
            if (isset($allServices[$key])) {
                $relations[] = [
                    'service_id' => $serviceId,
                    'extra_service_id' => $allServices[$key]->id,
                    'creator_id' => $creatorId,
                ];
            }
        }

        // 批量处理关联数据（差异化更新）
        if (!empty($relations)) {
            // 获取当前已存在的关联记录
            $existingRelations = AppointmentExtraServiceRelations::where('service_id', $serviceId)
                ->pluck('extra_service_id')
                ->toArray();

            // 获取新的额外服务ID列表
            $newExtraServiceIds = array_column($relations, 'extra_service_id');

            // 找出需要删除的关联（存在于数据库但不在新参数中）
            $idsToDelete = array_diff($existingRelations, $newExtraServiceIds);
            if (!empty($idsToDelete)) {
                AppointmentExtraServiceRelations::where('service_id', $serviceId)
                    ->whereIn('extra_service_id', $idsToDelete)
                    ->delete();
            }

            // 找出需要新增的关联（不存在于数据库但在新参数中）
            $existingRelationsMap = array_flip($existingRelations);
            $relationsToInsert = array_filter($relations, function($relation) use ($existingRelationsMap) {
                return !isset($existingRelationsMap[$relation['extra_service_id']]);
            });

            // 批量插入新的关联
            if (!empty($relationsToInsert)) {
                $relationResult = AppointmentExtraServiceRelations::insert($relationsToInsert);
                if (!$relationResult) {
                    BizException::throws(ErrorCode::SAVE_EXTRA_SERVICE_FAILED, ErrorCode::SAVE_EXTRA_SERVICE_FAILED->message());
                }
            }
        } else {
            // 如果没有新的关联数据，则删除所有现有关联
            AppointmentExtraServiceRelations::where('service_id', $serviceId)->delete();
        }
    }

    /**
     * 批量保存服务特殊排班
     *
     * @param int $serviceId
     * @param array<string, mixed> $special_schedules
     * @param int $creatorId
     * @return void
     * @throws BizException
     */
    public function saveSpecialSchedules(int $serviceId, array $special_schedules, int $creatorId): void
    {
        // 批量创建新特殊排班
        $specialScheduleDataList = collect($special_schedules)->map(function ($special_schedule) use ($serviceId, $creatorId) {
            return [
                'service_id' => $serviceId,
                'start_time' => $special_schedule['start_time'],
                'end_time' => $special_schedule['end_time'],
                'creator_id' => $creatorId,
            ];
        })->toArray();

        $result = AppointmentServiceSpecialSchedule::insert($specialScheduleDataList);
        if (!$result) {
            BizException::throws(ErrorCode::SAVE_SPECIAL_SCHEDULE_FAILED,ErrorCode::SAVE_SPECIAL_SCHEDULE_FAILED->message());
        }
    }

    /**
     * 批量更新服务状态
     *
     * @param int $status 状态值（0:关闭, 1:开放）
     * @return int 更新的记录数
     */
    public function updateBatchStatus(int $status): int
    {
        // 确保状态值只能是0或1
        if (!in_array($status, [AppointmentService::STATUS_CLOSED, AppointmentService::STATUS_OPEN])) {
            return 0;
        }

        // 更新所有服务的状态
        return AppointmentService::query()->update(['status' => $status]);
    }

    /**
     * 同步额外服务（差异化更新）
     *
     * @param int $serviceId 服务ID
     * @param array<string, mixed> $extraServices 额外服务数据
     * @param int $updaterId 更新者ID
     * @return void
     * @throws BizException
     */
    public function syncExtraServices(int $serviceId, array $extraServices, int $updaterId): void
    {
        // 收集所有额外服务的名称
        $extraServiceNames = array_map(function($extraService) {
            return $extraService['name'] ?? '';
        }, $extraServices);

        // 先从数据库中查找所有可能存在的额外服务
        $existingExtraServices = AppointmentExtraService::query()
            ->whereIn('name', $extraServiceNames)
            ->get()
            ->keyBy('name');

        // 获取当前的所有额外服务关联
        $currentRelations = AppointmentExtraServiceRelations::where('service_id', $serviceId)
            ->pluck('extra_service_id')
            ->toArray();

        // 准备新数据
        $newRelations = [];
        $extraServiceIds = [];

        // 处理传入的每个额外服务
        foreach ($extraServices as $extraService) {
            $name = $extraService['name'] ?? '';
            if (empty($name)) continue;

            // 检查额外服务是否存在
            if (isset($existingExtraServices[$name])) {
                // 服务存在，获取ID
                $extraServiceId = $existingExtraServices[$name]->id;

                // 更新现有额外服务记录
                $updateData = array_filter([
                    'category_name' => $extraService['category_name'] ?? null,
                    'price' => $extraService['price'] ?? null,
                    'duration' => $extraService['duration'] ?? null,
                    'max_quantity' => $extraService['max_quantity'] ?? null,
                    'min_quantity' => $extraService['min_quantity'] ?? null,
                    'creator_id' => $updaterId,
                ], function ($value, $key) use ($existingExtraServices, $name) {
                    // 确保值不为空且与现有值不同
                    return $value !== null && $value !== $existingExtraServices[$name]->{$key};
                }, ARRAY_FILTER_USE_BOTH);

                // 如果有需要更新的字段
                if (!empty($updateData)) {
                    $existingExtraServices[$name]->update($updateData);
                }
            } else {
                // 额外服务不存在，创建新的
                $newExtraService = [
                    'name' => $name,
                    'category_name' => $extraService['category_name'] ?? '',
                    'price' => $extraService['price'] ?? 0,
                    'duration' => $extraService['duration'] ?? 0,
                    'max_quantity' => $extraService['max_quantity'] ?? 1,
                    'min_quantity' => $extraService['min_quantity'] ?? 1,
                    'creator_id' => $updaterId,
                ];

                // 插入新的额外服务并获取ID
                $extraServiceId = AppointmentExtraService::insertGetId($newExtraService);
                if (!$extraServiceId) {
                    BizException::throws(ErrorCode::SAVE_EXTRA_SERVICE_FAILED, ErrorCode::SAVE_EXTRA_SERVICE_FAILED->message());
                }
            }

            // 记录额外服务ID，用于后续关联
            $extraServiceIds[] = $extraServiceId;

            // 如果该额外服务尚未与服务关联，添加到新关联列表
            if (!in_array($extraServiceId, $currentRelations)) {
                $newRelations[] = [
                    'service_id' => $serviceId,
                    'extra_service_id' => $extraServiceId,
                    'creator_id' => $updaterId,
                ];
            }
        }

        // 删除不再需要的关联
        if (!empty($currentRelations)) {
            $idsToRemove = array_diff($currentRelations, $extraServiceIds);
            if (!empty($idsToRemove)) {
                AppointmentExtraServiceRelations::where('service_id', $serviceId)
                    ->whereIn('extra_service_id', $idsToRemove)
                    ->delete();
            }
        }

        // 批量插入新的关联记录
        if (!empty($newRelations)) {
            $result = AppointmentExtraServiceRelations::insert($newRelations);
            if (!$result) {
                BizException::throws(ErrorCode::SAVE_EXTRA_SERVICE_FAILED, ErrorCode::SAVE_EXTRA_SERVICE_FAILED->message());
            }
        }
    }

    /**
     * 同步特殊排班（差异化更新）
     *
     * @param int $serviceId 服务ID
     * @param array<string, mixed> $specialSchedules 特殊排班数据
     * @param int $updaterId 更新者ID
     * @return void
     * @throws BizException
     */
    public function syncSpecialSchedules(int $serviceId, array $specialSchedules, int $updaterId): void
    {
        // 获取当前的所有特殊排班
        $currentSchedules = AppointmentServiceSpecialSchedule::where('service_id', $serviceId)->get();

        // 将现有记录转换为以 start_time 和 end_time 组合为键的集合
        $currentScheduleMap = $currentSchedules->mapWithKeys(function ($schedule) {
            $key = $schedule->start_time . '_' . $schedule->end_time;
            return [$key => $schedule];
        });

        // 准备新数据和需要保留的时间段
        $newSchedules = [];
        $keepTimeSlots = [];

        // 处理传入的每个特殊排班
        foreach ($specialSchedules as $schedule) {
            // 确保时间格式为Y-m-d H:i:s
            $startTime = date('Y-m-d H:i:s', is_numeric($schedule['start_time']) ? $schedule['start_time'] : strtotime($schedule['start_time']));
            $endTime = date('Y-m-d H:i:s', is_numeric($schedule['end_time']) ? $schedule['end_time'] : strtotime($schedule['end_time']));
            $timeKey = $startTime . '_' . $endTime;
            $keepTimeSlots[] = $timeKey;

            // 检查是否存在相同时间段的记录
            if ($currentScheduleMap->has($timeKey)) {
                // 记录已存在，不需要更新因为时间段是唯一标识
                continue;
            }

            // 新增记录
            $newSchedules[] = [
                'service_id' => $serviceId,
                'start_time' => $schedule['start_time'],
                'end_time' => $schedule['end_time'],
                'creator_id' => $updaterId,
            ];
        }

        // 删除不在新数据中的记录
        $currentSchedules->each(function ($schedule) use ($keepTimeSlots) {
            $timeKey = $schedule->start_time . '_' . $schedule->end_time;
            if (!in_array($timeKey, $keepTimeSlots)) {
                $schedule->delete();
            }
        });

        // 批量插入新记录
        if (!empty($newSchedules)) {
            $result = AppointmentServiceSpecialSchedule::insert($newSchedules);
            if (!$result) {
                BizException::throws(ErrorCode::SAVE_SPECIAL_SCHEDULE_FAILED, ErrorCode::SAVE_SPECIAL_SCHEDULE_FAILED->message());
            }
        }
    }

    /**
     * 同步服务排班（差异化更新）
     *
     * @param int $serviceId 服务ID
     * @param array<string, mixed> $schedules 排班数据
     * @param int $updaterId 更新者ID
     * @return void
     * @throws BizException
     */
    public function syncSchedules(int $serviceId, array $schedules, int $updaterId): void
    {
        // 获取当前的所有排班
        $currentSchedules = AppointmentServiceSchedule::where('service_id', $serviceId)
            ->get()
            ->keyBy('day_of_week');

        // 准备更新和插入的数据
        $upsertData = [];

        // 处理传入的每个排班
        foreach ($schedules as $schedule) {
            $dayOfWeek = $schedule['day_of_week'];

            // 检查是否存在该天的排班
            if (isset($currentSchedules[$dayOfWeek])) {
                // 更新现有记录
                $existingSchedule = $currentSchedules[$dayOfWeek];

                // 只更新提供的字段，保持数据完整性
                $updateData = array_filter([
                    'start_time' => $schedule['start_time'] ?? null,
                    'end_time' => $schedule['end_time'] ?? null,
                    'is_rest_day' => isset($schedule['is_rest_day']) ? ($schedule['is_rest_day'] ? AppointmentServiceSchedule::IS_REST_DAY : AppointmentServiceSchedule::NOT_REST_DAY) : null,
                    'rest_time_list' => $schedule['rest_time_list'] ?? null,
                    'creator_id' => $updaterId,
                ], function ($value) {
                    return $value !== null;
                });

                // 执行更新
                $existingSchedule->update($updateData);
            } else {
                // 新增记录
                $upsertData[] = [
                    'service_id' => $serviceId,
                    'day_of_week' => $dayOfWeek,
                    'start_time' => $schedule['start_time'],
                    'end_time' => $schedule['end_time'],
                    'is_rest_day' => isset($schedule['is_rest_day']) ? ($schedule['is_rest_day'] ? AppointmentServiceSchedule::IS_REST_DAY : AppointmentServiceSchedule::NOT_REST_DAY) : AppointmentServiceSchedule::NOT_REST_DAY,
                    'rest_time_list' => $schedule['rest_time_list'] ?? [],
                    'creator_id' => $updaterId,
                ];
            }
        }

        // 批量插入新记录（只有当确实有新记录时才执行插入操作）
        if (!empty($upsertData)) {
            $result = AppointmentServiceSchedule::insert($upsertData);
            if (!$result) {
                BizException::throws(ErrorCode::SAVE_SCHEDULE_FAILED, ErrorCode::SAVE_SCHEDULE_FAILED->message());
            }
        }
    }

    /**
     * 同步服务员工分配（差异化更新）
     *
     * @param int $serviceId 服务ID
     * @param array<int> $staffIds 员工ID数组
     * @param int $updaterId 更新者ID
     * @return void
     * @throws BizException
     */
    public function syncStaffAssignments(int $serviceId, array $staffIds, int $updaterId): void
    {
        // 获取当前分配的员工ID列表
        $currentStaffIds = AppointmentServiceStaff::where('service_id', $serviceId)
            ->pluck('staff_id')
            ->toArray();

        // 找出需要新增的员工ID
        $staffIdsToAdd = array_diff($staffIds, $currentStaffIds);

        // 找出需要删除的员工ID
        $staffIdsToRemove = array_diff($currentStaffIds, $staffIds);

        // 删除不再需要的员工分配
        if (!empty($staffIdsToRemove)) {
            AppointmentServiceStaff::where('service_id', $serviceId)
                ->whereIn('staff_id', $staffIdsToRemove)
                ->delete();
        }

        // 添加新的员工分配
        if (!empty($staffIdsToAdd)) {
            $staffData = [];
            foreach ($staffIdsToAdd as $staffId) {
                $staffData[] = [
                    'service_id' => $serviceId,
                    'staff_id' => $staffId,
                    'creator_id' => $updaterId,
                ];
            }

            $result = AppointmentServiceStaff::insert($staffData);
            if (!$result && !empty($staffData)) {
                BizException::throws(ErrorCode::SAVE_STAFF_FAILED,ErrorCode::SAVE_STAFF_FAILED->message());
            }
        }
    }

    /**
     * @param int $serviceId
     * @param mixed $extraService
     * @param int $creatorId
     * @param array $inserts
     * @return array
     */
    public function getInserts(int $serviceId, mixed $extraService, int $creatorId, array $inserts): array
    {
        $inserts[] = [
            'name' => $extraService['name'] ?? '',
            'category_name' => $extraService['category_name'] ?? '',
            'price' => $extraService['price'] ?? 0,
            'duration' => $extraService['duration'] ?? 0,
            'max_quantity' => $extraService['max_quantity'] ?? 1,
            'min_quantity' => $extraService['min_quantity'] ?? 1,
            'creator_id' => $creatorId,
        ];
        return $inserts;
    }
}
