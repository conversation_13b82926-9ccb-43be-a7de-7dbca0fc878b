<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Repositories;

use Illuminate\Support\Facades\DB;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Models\AppointmentCustomer;
use Modules\Appointment\Models\AppointmentRecords;

/**
 * 客户仓储实现
 */
class CustomerRepository
{
    /**
     * @var AppointmentCustomer
     */
    private AppointmentCustomer $customer;

    /**
     * 构造函数
     */
    public function __construct(AppointmentCustomer $customer)
    {
        $this->customer = $customer;
    }

    /**
     * 获取客户列表
     * @param array $criteria 查询条件
     * @return LengthAwarePaginator
     */
    public function list(array $criteria): LengthAwarePaginator
    {
        $query = $this->customer->newQuery()->onlyCustomers();

        // 关键词搜索
        if (!empty($criteria['keyword'])) {
            $query->where(function ($q) use ($criteria) {
                $q->where('name', 'like', "%{$criteria['keyword']}%")
                    ->orWhere('phone', 'like', "%{$criteria['keyword']}%")
                    ->orWhere('email', 'like', "%{$criteria['keyword']}%");
            });
        }

        // 排序处理
        $sortField = $criteria['sort_field'] ?? 'created_at';
        $sortOrder = $criteria['sort_order'] ?? 'desc';
        
        // 使用排序
        $query->orderBy($sortField, $sortOrder);

        // 分页处理
        $perPage = (int)($criteria['limit'] ?? 15);
        $page = (int)($criteria['page'] ?? 1);

        // 限制每页最大条数
        if ($perPage > 100) {
            $perPage = 100;
        }
     

        $result = $query->paginate($perPage, ['*'], 'page', $page);


        // 加载最近一次预约记录
        $result->getCollection()->each(function ($customer) {
            // 加载最近一次预约记录
            $customer->load('latestAppointment');
            
            // 获取预约总数
            $appointmentCount = $customer->appointmentRecords()->count();
            
            // 添加预约总数到客户数据中
            $customer->appointment_count = $appointmentCount;
            
            // 添加最近预约时间（如果有）
            if ($customer->latestAppointment) {
                $customer->latest_appointment_time = $customer->latestAppointment->created_at;
            } else {
                $customer->latest_appointment_time = null;
            }
        });

        return $result;
    }

    /**
     * 创建客户
     * @param array $data 客户数据
     * @return AppointmentCustomer
     */
    public function create(array $data): AppointmentCustomer
    {
        return $this->customer->create($data);
    }

    /**
     * 更新客户
     * @param int $id 客户ID
     * @param array $data 客户数据
     * @return AppointmentCustomer
     */
    public function update(AppointmentCustomer $customer, array $data): AppointmentCustomer
    {
        $customer->update($data);
        $customer->refresh();
        return $customer;
    }

    /**
     * 获取客户详情
     * @param int $id 客户ID
     * @return AppointmentCustomer|null
     */
    public function find(int $id): ?AppointmentCustomer
    {
        return $this->customer->find($id);
    }

    /**
     * 获取客户预约历史
     * @param int $id 客户ID
     * @param array $criteria 查询条件
     * @return LengthAwarePaginator
     */
    public function getAppointments(int $id, array $criteria): LengthAwarePaginator
    {
        $customer = $this->customer->findOrFail($id);
        $query = $customer->appointmentRecords();

        // 日期范围
        if (!empty($criteria['start_date'])) {
            $query->where('appointment_date', '>=', $criteria['start_date']);
        }
        if (!empty($criteria['end_date'])) {
            $query->where('appointment_date', '<=', $criteria['end_date']);
        }

        $perPage = (int)($criteria['limit'] ?? 15);
        $page = (int)($criteria['page'] ?? 1);
        $result = $query->paginate($perPage, ['*'], 'page', $page);

        // 加载关联数据：服务名称和员工姓名
        $result->getCollection()->each(function ($appointment) {
            $serviceName = $appointment->service()->value('name');
            $appointment->service_name = $serviceName;
            
            // 获取关联的员工名字，使用name属性访问器
            $staffs = $appointment->service()->first()?->staffs;
            $staffNames = $staffs ? $staffs->pluck('name')->filter()->implode(', ') : '';
            $appointment->staff_names = $staffNames;
        });

        return $result;
    }

    /**
     * 根据邮箱查询客户
     * @param string $email 邮箱
     * @return AppointmentCustomer|null
     */
    public function findByEmail(string $email): ?AppointmentCustomer
    {
        return $this->customer->where('email', $email)
            ->onlyCustomers()
            ->first();
    }

    /**
     * 根据手机号查询客户
     * @param string $phone 手机号
     * @return AppointmentCustomer|null
     */
    public function findByPhone(string $phone): ?AppointmentCustomer
    {
        return $this->customer->where('phone', $phone)
            ->onlyCustomers()
            ->first();
    }

    /**
     * 根据ID数组批量查询客户
     * @param array $ids 客户ID数组
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function findByIds(array $ids): \Illuminate\Database\Eloquent\Collection
    {
        return $this->customer->whereIn('id', $ids)
            ->onlyCustomers()
            ->get();
    }
} 