<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Business;

use Modules\Appointment\Domain\Repositories\StaffRepository;
use Bingo\Exceptions\BizException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Modules\Appointment\Models\IamUser;
use Modules\Appointment\Enums\ErrorCode;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Modules\Appointment\Domain\Exports\StaffExport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Http\UploadedFile;
use Modules\Appointment\Domain\Imports\StaffImport;
use Illuminate\Support\Facades\Log;
use Modules\Appointment\Domain\Exports\StaffImportTemplate;

/**
 * 员工业务逻辑类
 */
class StaffBusiness
{
    /**
     * @var StaffRepository
     */
    private StaffRepository $staffRepository;

    /**
     * 构造函数
     */
    public function __construct(StaffRepository $staffRepository)
    {
        $this->staffRepository = $staffRepository;
    }

    /**
     * 获取员工列表
     *
     * @param array $filters 过滤条件
     * @return LengthAwarePaginator
     */
    public function list(array $filters): LengthAwarePaginator
    {
        return $this->staffRepository->list($filters);
    }

    /**
     * 创建员工
     *
     * @param array $data 员工数据
     * @return IamUser    
     */
    public function store(array $data): IamUser
    {
        //检查职位是否存在
        if (isset($data['position_id'])) {
            $position = $this->staffRepository->findPosition($data['position_id']);
            if (!$position) {
                throw BizException::throws(ErrorCode::POSITION_NOT_FOUND, ErrorCode::POSITION_NOT_FOUND->message());
            }
        }

        //检查部门是否存在
        if (isset($data['department_id'])) {
            $department = $this->staffRepository->findDepartment($data['department_id']);
            if (!$department) {
                throw BizException::throws(ErrorCode::DEPARTMENT_NOT_FOUND, ErrorCode::DEPARTMENT_NOT_FOUND->message());
            }
        }

        // 检查邮箱是否已存在
        $existingUserByEmail = $this->staffRepository->findByEmail($data['email']);
        if ($existingUserByEmail) {
            throw BizException::throws(ErrorCode::EMAIL_ALREADY_EXISTS, ErrorCode::EMAIL_ALREADY_EXISTS->message());
        }

        // 检查手机号是否已存在
        if (isset($data['phone'])) {
            $existingUserByPhone = $this->staffRepository->findByPhone($data['phone']);
            if ($existingUserByPhone) {
                throw BizException::throws(ErrorCode::PHONE_ALREADY_EXISTS, ErrorCode::PHONE_ALREADY_EXISTS->message());
            }
        }

        // 获取当前用户ID
        $creatorId = Auth::guard()->user()->id ?? 0;

        // 创建员工基础数据
        $staffData = [
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'] ?? null,
            'photo' => $data['photo'] ?? null,
            'creator_id' => $creatorId,
        ];
       
        // 使用数据库事务确保数据一致性
        try {
            DB::beginTransaction();

            // 创建员工信息 - 使用模型直接创建
            $staff = $this->staffRepository->create($staffData);

            // 添加描述
            if (isset($data['description'])) {
                $staff->setStaffPreference('description', $data['description'], $creatorId);
            }

            // 添加部门关联
            if (isset($data['department_id'])) {
                $departmentUpdated = $this->staffRepository->updateUserDepartment(
                    $staff->id,
                    $data['department_id'],
                    $creatorId
                );

                if (!$departmentUpdated) {
                    throw BizException::throws(ErrorCode::STAFF_CREATE_FAILED, '关联部门失败');
                }
            }

            // 添加职位关联
            if (isset($data['position_id'])) {
                $positionUpdated = $this->staffRepository->updateUserPosition(
                    $staff->id,
                    $data['position_id'],
                    $creatorId
                );

                if (!$positionUpdated) {
                    throw BizException::throws(ErrorCode::STAFF_CREATE_FAILED, '关联职位失败');
                }
            }

            DB::commit();
            
            // 返回创建后的员工信息（包含关联数据）
            return $this->staffRepository->getStaffDetail($staff->id);
        } catch (\Exception $e) {
            DB::rollBack();

            // 如果是业务异常则直接抛出
            if ($e instanceof BizException) {
                throw $e;
            }

            // 其他异常转为业务异常
            throw BizException::throws(
                ErrorCode::STAFF_CREATE_FAILED,
                ErrorCode::STAFF_CREATE_FAILED->message() . ': ' . $e->getMessage()
            );
        }
    }

    /**
     * 获取员工详情
     *
     * @param int $id 员工ID
     * @return IamUser
     */
    public function show(int $id): IamUser
    {
        $staff = $this->staffRepository->getStaffDetail($id);
        if (!$staff) {
            throw BizException::throws(ErrorCode::STAFF_NOT_FOUND, ErrorCode::STAFF_NOT_FOUND->message());
        }

        return $staff;
    }

    /**
     * 更新员工信息
     *
     * @param int $id 员工ID
     * @param array $data 更新数据
     * @return IamUser
     */
    public function update(int $id, array $data): IamUser
    {
        // 检查员工是否存在
        $staff = $this->staffRepository->getStaffDetail($id);
        if (!$staff) {
            throw BizException::throws(ErrorCode::STAFF_NOT_FOUND, ErrorCode::STAFF_NOT_FOUND->message());
        }

        // 检查职位是否存在
        if (isset($data['position_id'])) {
            $position = $this->staffRepository->findPosition($data['position_id']);
            if (!$position) {
                throw BizException::throws(ErrorCode::POSITION_NOT_FOUND, ErrorCode::POSITION_NOT_FOUND->message());
            }
        }

        // 检查部门是否存在
        if (isset($data['department_id'])) {
            $department = $this->staffRepository->findDepartment($data['department_id']);
            if (!$department) {
                throw BizException::throws(ErrorCode::DEPARTMENT_NOT_FOUND, ErrorCode::DEPARTMENT_NOT_FOUND->message());
            }
        }

        // 检查邮箱是否已被其他用户使用
        if (isset($data['email']) && $data['email'] !== $staff->email) {
            $existingUserByEmail = $this->staffRepository->findByEmailExcept($data['email'], $id);
            if ($existingUserByEmail) {
                throw BizException::throws(ErrorCode::EMAIL_ALREADY_EXISTS, ErrorCode::EMAIL_ALREADY_EXISTS->message());
            }
        }

        // 检查手机号是否已被其他用户使用
        if (isset($data['phone']) && $data['phone'] !== $staff->phone) {
            $existingUserByPhone = $this->staffRepository->findByPhoneExcept($data['phone'], $id);
            if ($existingUserByPhone) {
                throw BizException::throws(ErrorCode::PHONE_ALREADY_EXISTS, ErrorCode::PHONE_ALREADY_EXISTS->message());
            }
        }

        // 获取当前用户ID
        $creatorId = Auth::guard()->user()->id ?? 0;

        // 使用数据库事务确保数据一致性
        try {
            DB::beginTransaction();

            // 准备更新用户基本信息
            $updateData = collect($data)
                ->only(['name', 'email', 'phone', 'photo'])
                ->filter(function ($value) {
                    return !is_null($value);
                })
                // 过滤掉未发生变化的属性
                ->filter(function ($value, $key) use ($staff) {
                    // 只保留与原值不同的属性
                    return !isset($staff->$key) || $staff->$key !== $value;
                })
                ->toArray();

            if (!empty($updateData)) {
                // 更新用户基本信息
                $result = $this->staffRepository->update($id, $updateData);
                if (!$result) {
                    throw BizException::throws(ErrorCode::STAFF_UPDATE_FAILED, ErrorCode::STAFF_UPDATE_FAILED->message());
                }
            }

            // 更新描述
            if (isset($data['description']) && $data['description'] !== $staff->description) {
                $staff->setStaffPreference('description', $data['description'], $creatorId);
            }

            // 更新部门关联
            if (isset($data['department_id']) && $data['department_id'] !== $staff->department_id) {
                $departmentUpdated = $this->staffRepository->updateUserDepartment(
                    $id,
                    $data['department_id'],
                    $creatorId
                );

                if (!$departmentUpdated) {
                    throw BizException::throws(ErrorCode::STAFF_UPDATE_FAILED, ErrorCode::STAFF_UPDATE_FAILED->message());
                }
            }

            // 更新职位关联
            if (isset($data['position_id']) && $data['position_id'] !== $staff->position_id) {
                $positionUpdated = $this->staffRepository->updateUserPosition(
                    $id,
                    $data['position_id'],
                    $creatorId
                );

                if (!$positionUpdated) {
                    throw BizException::throws(ErrorCode::STAFF_UPDATE_FAILED, ErrorCode::STAFF_UPDATE_FAILED->message());
                }
            }

            DB::commit();

            // 返回更新后的员工信息
            return $this->staffRepository->getStaffDetail($id);
        } catch (\Exception $e) {
            DB::rollBack();

            // 如果是业务异常则直接抛出
            if ($e instanceof BizException) {
                throw $e;
            }

            // 其他异常转为业务异常
            throw BizException::throws(
                ErrorCode::STAFF_UPDATE_FAILED,
                ErrorCode::STAFF_UPDATE_FAILED->message() . ': ' . $e->getMessage()
            );
        }
    }

    /**
     * 删除员工
     *
     * @param array $ids 员工ID数组
     * @return bool 删除是否成功
     */
    public function destroyBatch(array $ids): bool
    {
        // 使用数据库事务确保数据一致性
        try {
            DB::beginTransaction();

            // 删除员工与部门的关联关系
            $departmentDeleted = $this->staffRepository->deleteBatchUserDepartmentRelation($ids);
            if (!$departmentDeleted) {
                throw BizException::throws(ErrorCode::STAFF_DELETE_FAILED, ErrorCode::STAFF_DELETE_FAILED->message());
            }

            // 删除员工与职位的关联关系
            $positionDeleted = $this->staffRepository->deleteBatchUserPositionRelation($ids);
            if (!$positionDeleted) {
                throw BizException::throws(ErrorCode::STAFF_DELETE_FAILED, ErrorCode::STAFF_DELETE_FAILED->message());
            }

            // 删除员工基本信息
            $staffDeleted = $this->staffRepository->deleteBatch($ids);
            if (!$staffDeleted) {
                throw BizException::throws(ErrorCode::STAFF_DELETE_FAILED, ErrorCode::STAFF_DELETE_FAILED->message());
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();

            // 如果是业务异常则直接抛出
            if ($e instanceof BizException) {
                throw $e;
            }

            // 其他异常转为业务异常
            BizException::throws(
                ErrorCode::STAFF_DELETE_FAILED,
                ErrorCode::STAFF_DELETE_FAILED->message() . ': ' . $e->getMessage()
            );
        }
    }

    /**
     * 导出员工数据
     *
     * @param array $filters 过滤条件
     * @return BinaryFileResponse
     */
    public function export(array $filters = []): BinaryFileResponse
    {
        try {
            $filename = 'staff_' . date('YmdHis') . '.xlsx';

            return Excel::download(new StaffExport($filters), $filename);
        } catch (\Exception $e) {
            Log::error('导出员工失败: ' . $e->getMessage());
            BizException::throws(ErrorCode::STAFF_EXPORT_FAILED, ErrorCode::STAFF_EXPORT_FAILED->message());
        }
    }

    /**
     * 导入员工数据
     *
     * @param UploadedFile $file 导入文件
     * @return array
     */
    public function import(UploadedFile $file): array
    {
         // 获取当前用户ID作为创建者
         $creatorId = Auth::guard()->user()->id ?? 0;

         $importHandler = new StaffImport($creatorId, $this->staffRepository);

         // 使用Laravel Excel导入
         Excel::import($importHandler, $file);

         // 获取导入结果数据
         $rowCount = $importHandler->getRowCount();
         $successCount = $importHandler->getSuccessCount();
         $failCount = $importHandler->getFailCount();

         // 如果有导入失败的记录，抛出业务异常
         if ($failCount > 0) {
             $errorMessage = T("Appointment::validation.error.staff_import_failed_message",[
                 'success_count' => strval($successCount),
                 'fail_count' => strval($failCount),
             ]);
            
             BizException::throws(
                 ErrorCode::STAFF_IMPORT_FAILED,
                 $errorMessage
             );
         }
         
         // 全部成功时返回导入结果
         return [
             'success' => true,
             'message' => "导入成功，共导入{$successCount}条数据",
             'total' => $rowCount,
             'success_count' => $successCount,
             'fail_count' => 0,
             'errors' => [],
         ];
    }

    /**
     * 下载员工导入模板
     *
     * @return BinaryFileResponse
     */
    public function exportTemplate(): BinaryFileResponse
    {
        $filename = 'staff_import_template_' . date('YmdHis') . '.xlsx';
        return Excel::download(new StaffImportTemplate(), $filename);
    }
}
