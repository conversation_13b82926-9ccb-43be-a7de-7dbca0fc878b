<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Business;

use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Bingo\Exceptions\BizException;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Appointment\Enums\ErrorCode;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Models\AppointmentRecords;
use Modules\Appointment\Models\AppointmentSetting;
use Modules\Appointment\Models\AppointmentDiscount;
use Modules\Appointment\Services\EmailTemplateService;
use Modules\Appointment\Models\AppointmentEmailTemplate;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Modules\Appointment\Domain\Exports\AppointmentExport;
use Modules\Appointment\Domain\Imports\AppointmentImport;
use Modules\Appointment\Services\AppointmentSettingService;
use Modules\Appointment\Domain\Exports\AppointmentTemplateExport;
use Modules\Appointment\Domain\Repositories\AppointmentRepository;

/**
 * 预约业务实现
 */
final class AppointmentBusiness
{

    /**
     * 构造函数
     *
     * @param AppointmentRepository $appointmentRepository 预约仓储
     */
    public function __construct(
        private AppointmentRepository $appointmentRepository,
        private EmailTemplateService $emailTemplateService,
        private AppointmentSettingService $appointmentSettingService
    ) {}

    /**
     * 获取预约列表
     *
     * @param array $filters 过滤条件
     * @return LengthAwarePaginator 分页结果
     */
    public function getAppointmentList(array $filters): LengthAwarePaginator
    {
        return $this->appointmentRepository->list($filters);
    }

    /**
     * 获取预约详情
     *
     * @param int $id 预约ID
     * @return ?AppointmentRecords 预约记录
     */
    public function getAppointmentDetail(int $id): ?AppointmentRecords
    {
        $appointmentRecord = $this->appointmentRepository->findById($id);

        if (!$appointmentRecord) {
            throw BizException::throws(ErrorCode::APPOINTMENT_NOT_FOUND, ErrorCode::APPOINTMENT_NOT_FOUND->message());
        }

        return $appointmentRecord;
    }

    /**
     * 创建预约
     *
     * @param array $data 预约数据
     * @return AppointmentRecords 创建的预约记录
     * @throws BizException 业务异常
     */
    public function createAppointment(array $data): AppointmentRecords
    {
        // 检查客户是否存在
        if (!isset($data['customer_id'])) {
            BizException::throws(ErrorCode::INVALID_PARAMS, ErrorCode::INVALID_PARAMS->message());
        }

        // 检查服务是否存在并获取服务信息
        if (!isset($data['service_id'])) {
            BizException::throws(ErrorCode::INVALID_PARAMS, ErrorCode::INVALID_PARAMS->message());
        }

        $service = $this->appointmentRepository->getService($data['service_id']);
        if (!$service) {
            BizException::throws(ErrorCode::SERVICE_NOT_FOUND, ErrorCode::SERVICE_NOT_FOUND->message());
        }

        // 检查预约时间是否冲突
        $this->checkAppointmentTimeConflict($data);

        // 设置原价
        $data['original_price'] = $service->price;

        // 计算价格（如果有折扣）
        if (isset($data['discount_id'])) {
            $this->calculatePrice($data);
        } else {
            // 无折扣时，最终价格等于原价
            $data['discount_amount'] = '0.00';
            $data['final_price'] = $data['original_price'];
        }

        // 设置预约结束时间
        $appointmentDate = Carbon::parse($data['appointment_date']);
        $data['end_time'] = $appointmentDate->copy()->addSeconds($service->duration);

        // 默认设置预约状态为待确认
        $data['status'] = $data['status'] ?? AppointmentRecords::STATUS_PENDING;

        // 默认设置支付状态为待支付
        $data['payment_status'] = $data['payment_status'] ?? AppointmentRecords::PAYMENT_STATUS_PENDING;

        // 创建预约记录
        $appointment = $this->appointmentRepository->create($data);

        return $appointment;
    }

    /**
     * 检查预约时间是否冲突
     *
     * @param array $data 预约数据
     * @return void
     * @throws BizException 如果预约时间冲突
     */
    private function checkAppointmentTimeConflict(array $data): void
    {
        // 时间冲突检查逻辑
        $hasConflict = $this->appointmentRepository->checkTimeConflict(
            $data['appointment_date'],
            $data['service_id'],
            $data['customer_id']
        );

        if ($hasConflict) {
            throw BizException::throws(ErrorCode::APPOINTMENT_TIME_CONFLICT);
        }
    }

    /**
     * 计算价格
     *
     * @param array $data 预约数据
     * @return void
     */
    public function calculatePrice(array &$data): void
    {
        // 确保原价存在
        if (!isset($data['original_price'])) {
            return;
        }

        // 获取折扣信息
        // 根据折扣ID获取折扣信息
        $discount = null;
        if (isset($data['discount_id'])) {
            $discount = $this->appointmentRepository->getDiscount($data['discount_id']);
            $data['discount_name'] = $discount->name ?? null;
        } elseif (isset($data['discount_name'])) {
            // 如果提供了折扣名称，则通过名称查找折扣
            $discount = $this->appointmentRepository->findDiscountByName($data['discount_name']);
            $data['discount_id'] = $discount->id ?? null;
        }

        if ($discount) {
            // 计算折扣金额
            $discountAmount = 0;

            // 根据折扣类型计算
            if ($discount->type === AppointmentDiscount::TYPE_PERCENTAGE) {
                // 百分比折扣 - 使用bc函数确保精确计算
                $percentValue = bcdiv($discount->value, '100', 4); // 将百分比转换为小数，保留4位小数
                $discountAmount = bcmul($data['original_price'], $percentValue, 2); // 计算折扣金额，保留2位小数
            } elseif ($discount->type === AppointmentDiscount::TYPE_FIXED) {
                // 固定金额折扣
                $discountAmount = $discount->value;
            }

            // 更新折扣金额和最终价格
            $data['discount_amount'] = $discountAmount;
            $data['final_price'] = bcsub($data['original_price'], $discountAmount, 2); // 使用bc函数计算最终价格

            // 确保最终价格不小于0
            if (bccomp($data['final_price'], '0', 2) < 0) {
                $data['final_price'] = '0.00';
            }
        } else {
            // 无折扣
            $data['discount_amount'] = '0.00';
            $data['final_price'] = $data['original_price'];
        }
    }


    /**
     * 删除预约
     *
     * @param int $id 预约ID
     * @return bool 删除是否成功
     * @throws BizException 如果预约不存在或无法删除
     */
    public function deleteAppointment(int $id): bool
    {
        // 检查预约是否存在
        $appointment = $this->appointmentRepository->findById($id);

        if (!$appointment) {
            throw BizException::throws(ErrorCode::APPOINTMENT_NOT_FOUND);
        }

        // 检查预约是否可以删除（已完成的预约不允许删除）
        if ($appointment->status === AppointmentRecords::STATUS_COMPLETED) {
            BizException::throws(ErrorCode::APPOINTMENT_CANNOT_DELETE, ErrorCode::APPOINTMENT_CANNOT_DELETE->message());
        }

        //已经付款的预约不允许删除
        if ($appointment->payment_status === AppointmentRecords::PAYMENT_STATUS_PAID) {
            BizException::throws(ErrorCode::APPOINTMENT_CANNOT_DELETE, ErrorCode::APPOINTMENT_CANNOT_DELETE->message());
        }

        // 不允许删除正在进行中的预约
        if (
            $appointment->status === AppointmentRecords::STATUS_CONFIRMED &&
            Carbon::now()->between(
                Carbon::parse($appointment->appointment_date),
                Carbon::parse($appointment->end_time)
            )
        ) {
            BizException::throws(ErrorCode::APPOINTMENT_CANNOT_DELETE, ErrorCode::APPOINTMENT_CANNOT_DELETE->message());
        }

        // 删除预约
        return $this->appointmentRepository->delete($id);
    }

    /**
     * 批量删除预约
     *
     * @param array $ids 预约ID列表
     * @return bool 删除结果
     */
    public function deleteAppointments(array $ids): bool
    {
        if (empty($ids)) {
            return true;
        }

        // 批量获取预约记录
        $appointments = $this->appointmentRepository->findByIds($ids);

        if ($appointments->isEmpty()) {
            throw BizException::throws(ErrorCode::APPOINTMENT_NOT_FOUND, ErrorCode::APPOINTMENT_NOT_FOUND->message());
        }

        $now = Carbon::now();
        $invalidAppointments = $appointments->filter(function ($appointment) use ($now) {
            // 检查是否为已完成的预约
            if ($appointment->status === AppointmentRecords::STATUS_COMPLETED) {
                return true;
            }

            // 检查是否已付款
            if ($appointment->payment_status === AppointmentRecords::PAYMENT_STATUS_PAID) {
                return true;
            }

            // 检查是否正在进行中
            if (
                $appointment->status === AppointmentRecords::STATUS_CONFIRMED &&
                $now->between(
                    Carbon::parse($appointment->appointment_date),
                    Carbon::parse($appointment->end_time)
                )
            ) {
                return true;
            }

            return false;
        });

        if ($invalidAppointments->isNotEmpty()) {
            throw BizException::throws(ErrorCode::APPOINTMENT_CANNOT_DELETE, ErrorCode::APPOINTMENT_CANNOT_DELETE->message());
        }

        // 执行批量删除
        return $this->appointmentRepository->deleteMany($ids);
    }

    /**
     * 更新预约信息
     *
     * @param int $id 预约ID
     * @param array $data 更新的数据
     * @return AppointmentRecords 更新后的预约记录
     * @throws BizException 如果预约不存在或无法更新
     */
    public function updateAppointment(int $id, array $data): AppointmentRecords
    {
        // 检查预约是否存在
        $appointment = $this->appointmentRepository->findById($id);

        if (!$appointment) {
            throw BizException::throws(ErrorCode::APPOINTMENT_NOT_FOUND);
        }

        // 检查预约状态是否允许更新
        if ($appointment->status === AppointmentRecords::STATUS_COMPLETED) {
            throw BizException::throws(ErrorCode::APPOINTMENT_CANNOT_UPDATE, ErrorCode::APPOINTMENT_CANNOT_UPDATE->message());
        }

        //已经付款的预约不允许更新
        if ($appointment->payment_status === AppointmentRecords::PAYMENT_STATUS_PAID) {
            throw BizException::throws(ErrorCode::APPOINTMENT_CANNOT_UPDATE, ErrorCode::APPOINTMENT_CANNOT_UPDATE->message());
        }

        // 如果更新了服务，需要更新原始价格和结束时间
        if (isset($data['service_id']) && $data['service_id'] != $appointment->service_id) {
            // 获取新服务的信息
            $service = $this->appointmentRepository->getService($data['service_id']);
            if (!$service) {
                throw BizException::throws(ErrorCode::SERVICE_NOT_FOUND, ErrorCode::SERVICE_NOT_FOUND->message());
            }

            // 更新原始价格
            $data['original_price'] = $service->price;

            // 更新结束时间
            if (isset($data['appointment_date'])) {
                $appointmentDate = Carbon::parse($data['appointment_date']);
            } else {
                $appointmentDate = Carbon::parse($appointment->appointment_date);
            }
            $data['end_time'] = $appointmentDate->copy()->addSeconds($service->duration);
        }

        // 如果更新了预约时间,需要检查时间冲突
        if (isset($data['appointment_date']) || isset($data['end_time'])) {
            // 使用checkTimeConflict方法检查时间冲突
            $appointmentDate = $data['appointment_date'] ?? $appointment->appointment_date;

            // 获取服务ID - 如果有更新则使用新的，否则使用原来的
            $serviceId = $data['service_id'] ?? $appointment->service_id;

            // 获取客户ID - 如果有更新则使用新的，否则使用原来的
            $customerId = $data['customer_id'] ?? $appointment->customer_id;

            // 检查新的预约时间是否有冲突，排除当前预约ID
            $hasConflict = $this->appointmentRepository->checkTimeConflict(
                $appointmentDate,
                $serviceId,
                $customerId,
                $id // 排除当前预约ID
            );

            if ($hasConflict) {
                throw BizException::throws(ErrorCode::APPOINTMENT_TIME_CONFLICT, ErrorCode::APPOINTMENT_TIME_CONFLICT->message());
            }
        }

        // 处理折扣和价格计算
        // 只有在服务变更或折扣变更时才需要重新计算价格
        $needRecalculatePrice = false;

        // 检查服务是否变更
        if (isset($data['service_id']) && $data['service_id'] != $appointment->service_id) {
            $needRecalculatePrice = true;
        }

        // 检查折扣是否变更
        if (isset($data['discount_id']) && $data['discount_id'] != $appointment->discount_id) {
            $needRecalculatePrice = true;
        }

        // 如果需要重新计算价格
        if ($needRecalculatePrice) {
            // 如果没有明确设置discount_id，则使用原折扣ID
            if (!isset($data['discount_id'])) {
                $data['discount_id'] = $appointment->discount_id;
            }

            // 获取用于计算的服务价格
            if (isset($data['service_id']) && $data['service_id'] != $appointment->service_id) {
                // 如果更新了服务，获取新服务的价格
                $service = $this->appointmentRepository->getService($data['service_id']);
                if (!$service) {
                    throw BizException::throws(ErrorCode::SERVICE_NOT_FOUND, ErrorCode::SERVICE_NOT_FOUND->message());
                }
                $data['original_price'] = $service->price;
            } elseif (!isset($data['original_price'])) {
                // 如果没有更新服务且没有直接设置原始价格，则使用当前服务的价格
                $data['original_price'] = $appointment->original_price;
            }

            // 计算折扣后的最终价格
            if (isset($data['original_price'])) {
                if (!empty($data['discount_id'])) {
                    // 有折扣ID，计算折扣价格
                    $this->calculatePrice($data);
                } else {
                    // 无折扣时，最终价格等于原价
                    $data['discount_amount'] = '0.00';
                    $data['final_price'] = $data['original_price'];
                }
            }
        }

        // 更新预约信息
        $this->appointmentRepository->update($id, $data);

        return $this->appointmentRepository->findById($id);
    }

    /**
     * 导出预约数据
     *
     * @param array $filters 过滤条件
     * @return BinaryFileResponse 文件下载响应
     * @throws BizException 业务异常
     */
    public function exportAppointment(array $filters): BinaryFileResponse
    {
        try {
            // 确定导出格式
            $format = $filters['format'] ?? 'xlsx';

            // 生成文件名
            $fileName = 'appointments_export_' . date('YmdHis') . '.' . $format;

            // 使用Excel导出
            return Excel::download(
                new AppointmentExport($filters),
                $fileName
            );
        } catch (\Exception $e) {
            BizException::throws(ErrorCode::APPOINTMENT_EXPORT_FAILED, '导出预约数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 导入预约数据
     *
     * @param UploadedFile $file 上传的文件
     * @param int $creatorId 创建者ID
     * @return array 导入结果
     * @throws BizException
     */
    public function importAppointment(UploadedFile $file, int $creatorId): array
    {
        try {
            // 导入Excel文件
            Excel::import(new AppointmentImport($creatorId), $file);

            return [
                'success' => true,
                'message' => '导入成功',
            ];
        } catch (\Exception $e) {
            BizException::throws(ErrorCode::APPOINTMENT_IMPORT_FAILED, '导入预约数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出预约导入模板
     *
     * @return BinaryFileResponse
     * @throws BizException
     */
    public function exportTemplate(): BinaryFileResponse
    {
        try {
            $fileName = 'appointment_import_template.xlsx';
            return Excel::download(new AppointmentTemplateExport(), $fileName);
        } catch (\Exception $e) {
            BizException::throws(ErrorCode::APPOINTMENT_EXPORT_FAILED, '导出模板失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新预约状态
     *
     * @param int $id 预约ID
     * @param int $status 状态
     * @return bool
     * @throws BizException
     */
    public function updateAppointmentStatus(int $id, int $status): bool
    {

        // 检查预约是否存在
        $appointment = $this->appointmentRepository->findById($id);
        if (!$appointment) {
            throw BizException::throws(ErrorCode::APPOINTMENT_NOT_FOUND, ErrorCode::APPOINTMENT_NOT_FOUND->message());
        }

        // 检查预约状态是否有效
        if (!AppointmentRecords::isValidStatus($status)) {
            throw BizException::throws(ErrorCode::INVALID_STATUS, ErrorCode::INVALID_STATUS->message());
        }



        $result = $this->appointmentRepository->updateStatus($appointment, $status);

        if ($result) {
            // 发送邮件通知
            $this->sendEmailNotification($appointment, $status);
        }

        return $result;
    }


    /** 
     * 发送邮件通知
     *
     * @param AppointmentRecords $appointment 预约记录
     * @param int $status 状态
     */
    public function sendEmailNotification(AppointmentRecords $appointment, int $status): void
    {
        // 准备邮件模板变量
        $templateVariables = AppointmentEmailTemplate::setAppointmentTemplateVariables($appointment);

        $notificationTemplateCode = null;
        switch ($status) {
            case AppointmentRecords::STATUS_CONFIRMED:
                $notificationTemplateCode = $this->appointmentSettingService->get(AppointmentSetting::KEY_APPOINTMENT_CONFIRM_NOTIFICATION);
                break;
            case AppointmentRecords::STATUS_CANCELLED:
                $notificationTemplateCode = $this->appointmentSettingService->get(AppointmentSetting::KEY_APPOINTMENT_CANCEL_NOTIFICATION);
                break;
            case AppointmentRecords::STATUS_RESCHEDULED:
                $notificationTemplateCode = $this->appointmentSettingService->get(AppointmentSetting::KEY_APPOINTMENT_RESCHEDULE_NOTIFICATION);
                break;
        }

        if (!$notificationTemplateCode) {
            return;
        }

        // 发送邮件
        switch ($status) {
            case AppointmentRecords::STATUS_CONFIRMED:
                // 发送预约确认邮件
                $this->emailTemplateService->sendEmailByTemplate(
                    $appointment->customer_email,
                    $notificationTemplateCode,
                    $templateVariables
                );
                break;
            case AppointmentRecords::STATUS_CANCELLED:
                // 发送预约取消邮件
                $this->emailTemplateService->sendEmailByTemplate(
                    $appointment->customer_email,
                    $notificationTemplateCode,
                    $templateVariables
                );
                break;
            case AppointmentRecords::STATUS_RESCHEDULED:
                // 发送预约改期邮件
                $this->emailTemplateService->sendEmailByTemplate(
                    $appointment->customer_email,
                    $notificationTemplateCode,
                    $templateVariables
                );
                break;
        }
    }
    /**
     * 预约仪表盘
     *
     * @param array $filters 过滤条件
     * @return array
     */
    public function dashboard(array $filters): array
    {
        // 构建基础查询
        $baseQuery = AppointmentRecords::query();

        // 处理日期时间过滤条件，使用ensureDateTime函数确保日期格式严谨
        if (!empty($filters['start_date'])) {
            $startDate = $this->appointmentRepository->ensureDateTime($filters['start_date'], '00:00:00');
            $baseQuery->where('created_at', '>=', $startDate);
        }
        if (!empty($filters['end_date'])) {
            $endDate = $this->appointmentRepository->ensureDateTime($filters['end_date'], '23:59:59');
            $baseQuery->where('created_at', '<=', $endDate);
        }

        // 统计总收入 - 已支付的预约
        $totalRevenue = (clone $baseQuery)
            ->where('payment_status', AppointmentRecords::PAYMENT_STATUS_PAID)
            ->when(!empty($filters['start_date']), function ($query) use ($filters) {
                $startDate = $this->appointmentRepository->ensureDateTime($filters['start_date'], '00:00:00');
                return $query->where('payment_time', '>=', $startDate);
            })
            ->when(!empty($filters['end_date']), function ($query) use ($filters) {
                $endDate = $this->appointmentRepository->ensureDateTime($filters['end_date'], '23:59:59');
                return $query->where('payment_time', '<=', $endDate);
            })
            ->sum('final_price');

        // 统计新客户 - 根据过滤条件统计首次预约的客户数量
        $newCustomerQuery = AppointmentRecords::whereIn('customer_id', function ($query) use ($filters) {
            $query->select('customer_id')
                ->from('appointment_records')
                ->groupBy('customer_id');

            if (!empty($filters['start_date'])) {
                $startDate = $this->appointmentRepository->ensureDateTime($filters['start_date'], '00:00:00');
                $query->havingRaw('MIN(created_at) >= ?', [$startDate]);
            } else {
                $query->havingRaw('MIN(created_at) >= ?', [Carbon::today()]);
            }

            if (!empty($filters['end_date'])) {
                $endDate = $this->appointmentRepository->ensureDateTime($filters['end_date'], '23:59:59');
                $query->havingRaw('MIN(created_at) <= ?', [$endDate]);
            }
        });
        $newCustomerCount = $newCustomerQuery->distinct('customer_id')->count('customer_id');

        // 统计各种预约状态的数量
        // 使用一个SQL查询统计各种预约状态的数量，提高性能
        // 统计各种预约状态的数量，确保应用日期过滤条件
        $statusCountsQuery = (clone $baseQuery)
            ->selectRaw('status, COUNT(*) as count')
            ->when(!empty($filters['start_date']), function ($query) use ($filters) {
                $startDate = $this->appointmentRepository->ensureDateTime($filters['start_date'], '00:00:00');
                return $query->where('created_at', '>=', $startDate);
            })
            ->when(!empty($filters['end_date']), function ($query) use ($filters) {
                $endDate = $this->appointmentRepository->ensureDateTime($filters['end_date'], '23:59:59');
                return $query->where('created_at', '<=', $endDate);
            })
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // 初始化所有状态的计数
        $statusCounts = [
            'confirmed' => $statusCountsQuery[AppointmentRecords::STATUS_CONFIRMED] ?? 0,
            'cancelled' => $statusCountsQuery[AppointmentRecords::STATUS_CANCELLED] ?? 0,
            'pending' => $statusCountsQuery[AppointmentRecords::STATUS_PENDING] ?? 0,
            'rescheduled' => $statusCountsQuery[AppointmentRecords::STATUS_RESCHEDULED] ?? 0,
            'rejected' => $statusCountsQuery[AppointmentRecords::STATUS_REJECTED] ?? 0,
            'completed' => $statusCountsQuery[AppointmentRecords::STATUS_COMPLETED] ?? 0,
            'no_show' => $statusCountsQuery[AppointmentRecords::STATUS_NO_SHOW] ?? 0,
        ];

        // 获取预约状态概览数据
        $statusOverview = [
            [
                'name' => '预约确认',
                'count' => $statusCounts['confirmed'],
                'status' => AppointmentRecords::STATUS_CONFIRMED,
                'status_text' => AppointmentRecords::getStaticStatusText(AppointmentRecords::STATUS_CONFIRMED),
                'color' => '#007bff'
            ],
            [
                'name' => '预约取消',
                'count' => $statusCounts['cancelled'],
                'status' => AppointmentRecords::STATUS_CANCELLED,
                'status_text' => AppointmentRecords::getStaticStatusText(AppointmentRecords::STATUS_CANCELLED),
                'color' => '#dc3545'
            ],
            [
                'name' => '预约待确认',
                'count' => $statusCounts['pending'],
                'status' => AppointmentRecords::STATUS_PENDING,
                'status_text' => AppointmentRecords::getStaticStatusText(AppointmentRecords::STATUS_PENDING),
                'color' => '#ffc107'
            ],
            [
                'name' => '预约改期',
                'count' => $statusCounts['rescheduled'],
                'status' => AppointmentRecords::STATUS_RESCHEDULED,
                'status_text' => AppointmentRecords::getStaticStatusText(AppointmentRecords::STATUS_RESCHEDULED),
                'color' => '#28a745'
            ],
            [
                'name' => '预约拒绝',
                'count' => $statusCounts['rejected'],
                'status' => AppointmentRecords::STATUS_REJECTED,
                'status_text' => AppointmentRecords::getStaticStatusText(AppointmentRecords::STATUS_REJECTED),
                'color' => '#dc3545'
            ],
            [
                'name' => '预约完成',
                'count' => $statusCounts['completed'],
                'status' => AppointmentRecords::STATUS_COMPLETED,
                'status_text' => AppointmentRecords::getStaticStatusText(AppointmentRecords::STATUS_COMPLETED),
                'color' => '#28a745'
            ],
            [
                'name' => 'no-show',
                'count' => $statusCounts['no_show'],
                'status' => AppointmentRecords::STATUS_NO_SHOW,
                'status_text' => AppointmentRecords::getStaticStatusText(AppointmentRecords::STATUS_NO_SHOW),
                'color' => '#dc3545'
            ],
        ];

        // 总数等于所有状态的数量之和
        $totalAppointmentCount = array_sum($statusCounts);

        // 统计待确认预定数量
        $pendingAppointmentCount = $statusCounts['pending'];

        // 添加时间范围信息到返回数据中
        $dateRange = [
            'start_date' => $filters['start_date'],
            'end_date' => $filters['end_date'],
        ];

        return [
            'totalAppointmentCount' => $totalAppointmentCount,
            'pendingAppointmentCount' => $pendingAppointmentCount,
            'totalRevenue' => $totalRevenue,
            'newCustomerCount' => $newCustomerCount,
            'statusOverview' => $statusOverview,
            'dateRange' => $dateRange,
        ];
    }
}
