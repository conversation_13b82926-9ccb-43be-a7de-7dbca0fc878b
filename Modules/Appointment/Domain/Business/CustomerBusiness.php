<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Business;

use Illuminate\Http\UploadedFile;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Appointment\Enums\ErrorCode;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Models\AppointmentCustomer;
use Modules\Appointment\Domain\Exports\CustomerExport;
use Modules\Appointment\Domain\Imports\CustomerImport;
use Modules\Appointment\Domain\Exports\CustomerImportTemplate;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Modules\Appointment\Domain\Repositories\CustomerRepository;


/**
 * 客户业务逻辑
 */
class CustomerBusiness
{
    /**
     * @var CustomerRepository
     */
    private CustomerRepository $repository;


    /**
     * 构造函数
     */
    public function __construct(CustomerRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * 获取客户列表
     * @param array $criteria 查询条件
     * @return LengthAwarePaginator
     */
    public function list(array $criteria): LengthAwarePaginator
    {
        return $this->repository->list($criteria);
    }

    /**
     * 创建客户
     * @param array $data 客户数据
     * @return AppointmentCustomer
     * @throws \Bingo\Exceptions\BizException
     */
    public function create(array $data): AppointmentCustomer
    {
        // 获取当前用户ID作为创建者
        $creatorId = Auth::guard()->user()->id ?? 0;

        return DB::transaction(function () use ($data, $creatorId) {
            $existingCustomer = $this->repository->findByEmail($data['email']);
            if ($existingCustomer) {
                BizException::throws(ErrorCode::CUSTOMER_EMAIL_EXISTS, ErrorCode::CUSTOMER_EMAIL_EXISTS->message());
            }

            if (isset($data['phone'])) {
                $existingUser = $this->repository->findByPhone($data['phone']);
                if ($existingUser) {
                    BizException::throws(ErrorCode::CUSTOMER_PHONE_EXISTS, ErrorCode::CUSTOMER_PHONE_EXISTS->message());
                }
            }

            // 创建新的客户记录
            $customer = $this->repository->create($data);

            if (isset($data['description'])) {
                $customer->setCustomerPreference('description', $data['description'], $creatorId);
            }

            return $customer;
        });
    }

    /**
     * 更新客户
     * @param int $id 客户ID
     * @param array $data 客户数据
     * @return AppointmentCustomer
     */
    public function update(int $id, array $data): AppointmentCustomer
    {
        $creatorId = Auth::guard()->user()->id ?? 0;
        return DB::transaction(function () use ($id, $data, $creatorId) {
            // 查找客户
            $customer = $this->repository->find($id);
            if (!$customer) {
                BizException::throws(ErrorCode::CUSTOMER_NOT_FOUND, ErrorCode::CUSTOMER_NOT_FOUND->message());
            }

            // 检查邮箱是否已被其他用户使用
            if (isset($data['email']) && $data['email'] !== $customer->email) {
                $existingUser = $this->repository->findByEmail($data['email']);
                if ($existingUser) {
                    BizException::throws(ErrorCode::CUSTOMER_EMAIL_EXISTS, ErrorCode::CUSTOMER_EMAIL_EXISTS->message());
                }
            }

            // 检查手机号是否已被其他用户使用
            if (isset($data['phone']) && $data['phone'] !== $customer->phone) {
                $existingUser = $this->repository->findByPhone($data['phone']);
                if ($existingUser) {
                    BizException::throws(ErrorCode::CUSTOMER_PHONE_EXISTS, ErrorCode::CUSTOMER_PHONE_EXISTS->message());
                }
            }

            // 检查更新数据是否有实际变化，只更新发生变化的字段
            $changedData = array_filter([
                'name' => $data['name'] ?? null,
                'email' => $data['email'] ?? null,
                'phone' => $data['phone'] ?? null,
                'birthdate' => $data['birthdate'] ?? null,
                'photo' => $data['photo'] ?? null,
            ], function ($value, $key) use ($customer) {
                return $customer->$key !== $value && $value !== null;
            }, ARRAY_FILTER_USE_BOTH);

            if (!empty($changedData)) {
                $customer = $this->repository->update($customer, $changedData);
            }

            // 更新客户描述信息（存储在偏好设置中）
            if (isset($data['description'])) {
                $customer->setCustomerPreference('description', $data['description'], $creatorId);
            }

            return $customer;
        });
    }

    /**
     * 删除客户
     * @param int $id 客户ID
     * @return bool
     */
    /**
     * 删除客户
     * 
     * @param int $id 客户ID
     * @return bool
     * @throws BizException
     */
    public function delete(int $id): bool
    {
        // 查找客户是否存在
        $customer = $this->repository->find($id);
        if (!$customer) {
            BizException::throws(ErrorCode::CUSTOMER_NOT_FOUND, ErrorCode::CUSTOMER_NOT_FOUND->message());
        }

        //检查客户是否存在预约
        $hasAppointments = $customer->appointments()->exists();
        if ($hasAppointments) {
            BizException::throws(ErrorCode::CUSTOMER_HAS_APPOINTMENTS, ErrorCode::CUSTOMER_HAS_APPOINTMENTS->message());
        }

        // 开启事务处理
        return DB::transaction(function () use ($customer) {
            // 删除客户偏好设置
            $customer->iamUser->preferences()->delete();

            // 删除客户资料
            $customer->delete();

            return true;
        });
    }

    /**
     * 获取客户详情
     * @param int $id 客户ID
     * @return AppointmentCustomer|null
     */
    public function find(int $id): ?AppointmentCustomer
    {
        return $this->repository->find($id);
    }

    /**
     * 获取客户预约历史
     * @param int $id 客户ID
     * @param array $criteria 查询条件
     * @return LengthAwarePaginator
     */
    public function getAppointments(int $id, array $criteria): LengthAwarePaginator
    {
        return $this->repository->getAppointments($id, $criteria);
    }

    /**
     * 导出客户数据
     * @param array $criteria 查询条件
     * @return BinaryFileResponse
     * @throws \Exception
     */
    public function export(array $criteria): BinaryFileResponse
    {
        try {
            // 生成文件名（包含时间戳防止重复）
            $filename = 'customer_' . date('YmdHis') . '.xlsx';

            // 创建Excel导出对象并返回下载响应
            return Excel::download(new CustomerExport($criteria), $filename);
        } catch (\Exception $e) {
            // 记录导出失败的日志
            Log::error('客户数据导出失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'criteria' => $criteria ?? []
            ]);

            // 抛出业务异常
            BizException::throws(ErrorCode::CUSTOMER_EXPORT_FAILED, ErrorCode::CUSTOMER_EXPORT_FAILED->message());
        }
    }

    /**
     * 导出客户导入模板
     * @return BinaryFileResponse
     */
    public function exportTemplate(): BinaryFileResponse
    {
        return Excel::download(new CustomerImportTemplate(), 'customer_import_template.xlsx');
    }

    /**
     * 导入客户数据
     * @param UploadedFile $file 上传的文件
     * @return array 导入结果统计
     * @throws \Exception
     */
    public function import(UploadedFile $file): array
    {
        try {
            // 获取当前用户ID作为创建者
            $creatorId = Auth::guard()->user()->id ?? 0;

            // 导入客户数据
            Excel::import(new CustomerImport($creatorId), $file);

            return [
                'success' => true,
                'message' => '导入成功'
            ];
        } catch (\Exception $e) {
            BizException::throws(ErrorCode::CUSTOMER_IMPORT_FAILED, ErrorCode::CUSTOMER_IMPORT_FAILED->message());
        }
    }

    /**
     * 创建或更新客户
     * @param array $data 客户数据
     * @return AppointmentCustomer
     */
    /**
     * 创建或更新客户信息
     * 
     * @param array $data 客户数据
     * @param int $creatorId 创建者ID
     * @return AppointmentCustomer
     * @throws BizException
     */
    public function createOrUpdate(array $data, int $creatorId): AppointmentCustomer
    {
        // 验证必填字段
        if (empty($data['email'])) {
            BizException::throws(ErrorCode::INVALID_PARAMS, '邮箱不能为空');
        }

        // 查找是否存在相同邮箱的客户
        $existingCustomer = $this->repository->findByEmail($data['email']);
        
        if ($existingCustomer) {
            // 如果存在，则更新客户信息
            return $this->update($existingCustomer->id, array_merge($data, [
                'creator_id' => $creatorId,
            ]));
        }

        // 如果不存在，则创建新客户
        return $this->create(array_merge($data, [
            'creator_id' => $creatorId,
        ]));
    }

    /**
     * 批量删除客户
     * 
     * @param array $ids 客户ID列表
     * @return array 删除结果统计
     * @throws BizException
     */
    public function destroyBatch(array $ids): array
    {
        if (empty($ids)) {
            BizException::throws(ErrorCode::INVALID_PARAMS, '客户ID列表不能为空');
        }
        
        try {
            return DB::transaction(function () use ($ids) {
                // 获取所有存在的客户记录
                $customers = $this->repository->findByIds($ids);
                $existingIds = $customers->pluck('id')->toArray();
                
                // 检查是否所有ID都存在
                $notFoundIds = array_diff($ids, $existingIds);
                if (!empty($notFoundIds)) {
                    BizException::throws(ErrorCode::CUSTOMER_NOT_FOUND, ErrorCode::CUSTOMER_NOT_FOUND->message());
                }
                
                // 检查客户是否存在预约
                $customersWithAppointments = $customers->filter(function ($customer) {
                    return $customer->appointments()->exists();
                });
                
                if ($customersWithAppointments->isNotEmpty()) {
                    BizException::throws(ErrorCode::CUSTOMER_HAS_APPOINTMENTS,ErrorCode::CUSTOMER_HAS_APPOINTMENTS->message());
                }
                
                // 删除客户偏好设置和客户记录
                foreach ($customers as $customer) {
                    $customer->preferences()->delete();
                    $customer->delete();
                }
                
                // 记录成功日志
                Log::info('批量删除客户成功', [
                    'customer_ids' => $ids,
                    'deleted_count' => count($ids)
                ]);
                
                return [
                    'success' => true,
                    'message' => '批量删除成功',
                    'deleted_count' => count($ids)
                ];
            });
        } catch (\Exception $e) {
            if (!($e instanceof BizException)) {
                Log::error('批量删除客户失败', [
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'customer_ids' => $ids
                ]);
                
                BizException::throws(ErrorCode::CUSTOMER_DELETE_FAILED, ErrorCode::CUSTOMER_DELETE_FAILED->message());
            }
            
            throw $e;
        }
    }
}
