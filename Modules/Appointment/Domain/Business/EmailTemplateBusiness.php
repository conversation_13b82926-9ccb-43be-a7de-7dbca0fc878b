<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Business;

use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\Auth;
use Modules\Appointment\Enums\ErrorCode;
use Modules\Appointment\Models\AppointmentEmailTemplate;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Modules\Appointment\Domain\Repositories\EmailTemplateRepository;

/**
 * 邮件模板业务类
 */
final class EmailTemplateBusiness
{
    /**
     * 构造函数
     *
     * @param EmailTemplateRepository $emailTemplateRepository 邮件模板仓储
     */
    public function __construct(
        private readonly EmailTemplateRepository $emailTemplateRepository
    ) {
    }

    /**
     * 获取邮件模板列表
     *
     * @param array $filters 查询条件
     * @return LengthAwarePaginator
     */
    public function getEmailTemplates(array $filters): LengthAwarePaginator
    {
        return $this->emailTemplateRepository->getEmailTemplates($filters);
    }

    /**
     * 获取邮件模板详情
     *
     * @param int $id 邮件模板ID
     * @return AppointmentEmailTemplate|null
     */
    public function getEmailTemplateById(int $id): ?AppointmentEmailTemplate
    {
        $emailTemplate = $this->emailTemplateRepository->getEmailTemplateById($id);

        if (!$emailTemplate) {
            BizException::throws(ErrorCode::EMAIL_TEMPLATE_NOT_FOUND,ErrorCode::EMAIL_TEMPLATE_NOT_FOUND->message());
        }

        return $emailTemplate;
    }

    /**
     * 创建或更新邮件模板
     *
     * @param array $data 邮件模板数据
     * @return AppointmentEmailTemplate
     */
    public function saveEmailTemplate(array $data): AppointmentEmailTemplate
    {
        // 检查subject和content中的变量是否都在预设变量列表中
        $content = $data['subject'] . ' ' . $data['content'];
        
        // 使用正则表达式匹配所有 {xxx} 格式的变量
        preg_match_all('/{([^}]+)}/', $content, $matches);
        
        if (!empty($matches[0])) {
            $foundVariables = $matches[0];
            $validVariables = array_keys(AppointmentEmailTemplate::APPOINTMENT_TEMPLATE_VARIABLES);
            
            // 检查每个找到的变量是否在有效变量列表中
            foreach ($foundVariables as $variable) {
                if (!in_array($variable, $validVariables)) {
                    throw BizException::throws(
                        ErrorCode::EMAIL_TEMPLATE_VARIABLE_IS_NOT_VALID, 
                        ErrorCode::EMAIL_TEMPLATE_VARIABLE_IS_NOT_VALID_MESSAGE->message(),
                        ['variable' => $variable, 'validVariables' => implode(',', $validVariables)]
                    );
                }
            }
        }

        // 根据名称或code或id判断是否存在
        if (isset($data['id']) && $data['id'] > 0) {
            $emailTemplate = $this->emailTemplateRepository->getEmailTemplateById($data['id']);
        } else if (!empty($data['name'])) {
            $emailTemplate = $this->emailTemplateRepository->getEmailTemplateByName($data['name']);
        } else if(!empty($data['code'])) {
            $emailTemplate = $this->emailTemplateRepository->getEmailTemplateByCode($data['code']);
        }

        if (!$emailTemplate) {
            $emailTemplate = new AppointmentEmailTemplate();
            $emailTemplate->creator_id = Auth::guard()->user()->id ?? 0;
            $emailTemplate->creator_name = Auth::guard()->user()->name ?? '';
        }

        //按需更新字段，代码要优雅不要冗余
        // 获取模型的可填充字段
        $fillableFields = $emailTemplate->getFillable();
        
        // 过滤数据，只保留可填充字段中存在的数据
        $filteredData = array_filter(
            $data,
            function ($key) use ($fillableFields, $emailTemplate, $data) {
                // 值发生了变更
                return in_array($key, $fillableFields) && $data[$key] !== $emailTemplate[$key];
            },
            ARRAY_FILTER_USE_KEY
        );
        
        // 如果没有需要更新的字段，返回原始数据
        if (empty($filteredData)) {
            return $emailTemplate;
        }
        
        // 更新数据前，将过滤后的数据赋值给$data
        $data = $filteredData;
        $emailTemplate->fill($data);
        $emailTemplate->save();

        return $emailTemplate;
    }

    /**
     * 删除邮件模板
     *
     * @param int $id 邮件模板ID
     * @return bool
     */
    public function deleteEmailTemplate(int $id): bool
    {
        return $this->emailTemplateRepository->deleteEmailTemplate($id) > 0;
    }
    
    /**
     * 根据代码获取邮件模板
     *
     * @param string $code 模板代码
     * @return AppointmentEmailTemplate|null
     */
    public function getEmailTemplateByCode(string $code): ?AppointmentEmailTemplate
    {
        return $this->emailTemplateRepository->getEmailTemplateByCode($code);
    }
    
    /**
     * 开关邮件模板
     *
     * @param int $id 邮件模板ID
     * @return bool
     */
    public function switchEmailTemplate(int $id): bool
    {
        $emailTemplate = $this->getEmailTemplateById($id);
        $emailTemplate->status = !$emailTemplate->status;
        return $emailTemplate->save();
    }
}
