<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Business;

use Bingo\Exceptions\BizException;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Domain\Repositories\DiscountRepository;
use Modules\Appointment\Enums\ErrorCode;
use Modules\Appointment\Models\AppointmentDiscount;

/**
 * 折扣业务逻辑类
 */
final class DiscountBusiness
{
    /**
     * 构造函数
     *
     * @param DiscountRepository $discountRepository 折扣仓储
     */
    public function __construct(private DiscountRepository $discountRepository)
    {
    }

    /**
     * 获取折扣列表
     *
     * @param array $filters 过滤条件
     * @return LengthAwarePaginator 分页结果
     */
    public function getDiscountList(array $filters): LengthAwarePaginator
    {
        return $this->discountRepository->list($filters);
    }

    /**
     * 创建折扣
     *
     * @param array $data 折扣数据
     * @return AppointmentDiscount 创建的折扣对象
     * @throws BizException 业务异常
     */
    public function createDiscount(array $data): AppointmentDiscount
    {
        // 检查折扣代码是否已存在
        $existingDiscount = $this->discountRepository->findByName($data['name']);
        if ($existingDiscount) {
            throw BizException::throws(ErrorCode::DISCOUNT_NAME_EXISTS,ErrorCode::DISCOUNT_NAME_EXISTS->message());
        }

        // 检查折扣类型是否有效
        if (!isset(AppointmentDiscount::$typeMap[$data['type']])) {
            throw BizException::throws(ErrorCode::INVALID_DISCOUNT_TYPE,ErrorCode::INVALID_DISCOUNT_TYPE->message());
        }

        // 检查日期范围
        $startDate = Carbon::parse($data['start_date']);
        $endDate = Carbon::parse($data['end_date']);
        
        if ($endDate->isBefore($startDate)) {
            throw BizException::throws(ErrorCode::INVALID_DATE_RANGE,ErrorCode::INVALID_DATE_RANGE->message());
        }

        // 创建折扣
        return $this->discountRepository->create($data);
    }

    /**
     * 获取折扣详情
     *
     * @param int $id 折扣ID
     * @return AppointmentDiscount 折扣对象
     * @throws BizException 业务异常
     */
    public function getDiscountDetail(int $id): AppointmentDiscount
    {
        $discount = $this->discountRepository->findById($id);
        
        if (!$discount) {
            throw BizException::throws(ErrorCode::DISCOUNT_NOT_FOUND,ErrorCode::DISCOUNT_NOT_FOUND->message());
        }
        
        return $discount;
    }

    /**
     * 更新折扣
     *
     * @param int $id 折扣ID
     * @param array $data 更新数据
     * @return AppointmentDiscount 更新后的折扣对象
     * @throws BizException 业务异常
     */
    public function updateDiscount(int $id, array $data): AppointmentDiscount
    {
        // 检查折扣是否存在
        $discount = $this->discountRepository->findById($id);
        
        if (!$discount) {
            throw BizException::throws(ErrorCode::DISCOUNT_NOT_FOUND,ErrorCode::DISCOUNT_NOT_FOUND->message());
        }
        
        // 检查折扣名称是否已存在(排除当前折扣名称)
        if (isset($data['name']) && $data['name'] !== $discount->name) {
            $existingDiscount = $this->discountRepository->findByName($data['name']);
            if ($existingDiscount && $existingDiscount->id != $id) {
                throw BizException::throws(ErrorCode::DISCOUNT_NAME_EXISTS,ErrorCode::DISCOUNT_NAME_EXISTS->message());
            }
        }
        
        // 检查日期范围
        if (isset($data['start_date']) && isset($data['end_date'])) {
            $startDate = Carbon::parse($data['start_date']);
            $endDate = Carbon::parse($data['end_date']);
            
            if ($endDate->isBefore($startDate)) {
                throw BizException::throws(ErrorCode::INVALID_DATE_RANGE,ErrorCode::INVALID_DATE_RANGE->message());
            }
        } elseif (isset($data['start_date'])) {
            $startDate = Carbon::parse($data['start_date']);
            $endDate = Carbon::parse($discount->end_date);
            
            if ($endDate->isBefore($startDate)) {
                throw BizException::throws(ErrorCode::INVALID_DATE_RANGE,ErrorCode::INVALID_DATE_RANGE->message());
            }
        } elseif (isset($data['end_date'])) {
            $startDate = Carbon::parse($discount->start_date);
            $endDate = Carbon::parse($data['end_date']);
            
            if ($endDate->isBefore($startDate)) {
                throw BizException::throws(ErrorCode::INVALID_DATE_RANGE,ErrorCode::INVALID_DATE_RANGE->message());
            }
        }
        
        // 更新折扣
        if (!$this->discountRepository->update($id, $data)) {
            throw BizException::throws(ErrorCode::DISCOUNT_UPDATE_FAILED,ErrorCode::DISCOUNT_UPDATE_FAILED->message());
        }
        
        return $this->discountRepository->findById($id);
    }

    /**
     * 删除折扣
     *
     * @param int $id 折扣ID
     * @return bool 删除是否成功
     * @throws BizException 业务异常
     */
    public function deleteDiscount(int $id): bool
    {
        // 检查折扣是否存在
        $discount = $this->discountRepository->findById($id);
        
        if (!$discount) {
            throw BizException::throws(ErrorCode::DISCOUNT_NOT_FOUND,ErrorCode::DISCOUNT_NOT_FOUND->message());
        }
        
        // 检查折扣是否正在使用中（与预约关联）
        if ($this->discountRepository->isDiscountInUse($id)) {
            throw BizException::throws(ErrorCode::DISCOUNT_IN_USE,ErrorCode::DISCOUNT_IN_USE->message());
        }
        
        // 删除折扣
        return $this->discountRepository->delete($id);
    }
} 