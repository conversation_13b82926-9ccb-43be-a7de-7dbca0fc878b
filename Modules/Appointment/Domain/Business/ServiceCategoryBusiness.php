<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Business;

use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Modules\Appointment\Domain\Repositories\ServiceCategoryRepository;
use Modules\Appointment\Models\AppointmentServiceCategory;

/**
 * 服务分类业务逻辑
 */
class ServiceCategoryBusiness
{
    /**
     * @var ServiceCategoryRepository
     */
    private ServiceCategoryRepository $serviceCategoryRepository;

    /**
     * 构造函数
     */
    public function __construct(ServiceCategoryRepository $serviceCategoryRepository)
    {
        $this->serviceCategoryRepository = $serviceCategoryRepository;
    }

    /**
     * 获取服务分类列表
     *
     * @param array<string, mixed> $filters
     * @return LengthAwarePaginator
     */
    public function getList(array $filters): LengthAwarePaginator
    {
        return $this->serviceCategoryRepository->list($filters);
    }

    /**
     * 创建服务分类
     *
     * @param array<string, mixed> $data
     * @return AppointmentServiceCategory  
     */
    public function create(array $data): AppointmentServiceCategory
    {
        // 设置创建者ID
        $data['creator_id'] = Auth::guard()->id() ?? 0;
        
        // 如果没有设置排序，默认为0
        if (!isset($data['sort'])) {
            $data['sort'] = 0;
        }
        
        return $this->serviceCategoryRepository->create($data);
    }

    /**
     * 更新服务分类
     *
     * @param int $id
     * @param array<string, mixed> $data
     * @return AppointmentServiceCategory
     */
    public function update(int $id, array $data): AppointmentServiceCategory
    {
        return $this->serviceCategoryRepository->update($id, $data);
    }

    /**
     * 删除服务分类
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        // 检查分类下是否有服务，如果有则不允许删除
        $category = $this->find($id);
        if ($category && $category->services()->count() > 0) {
            return false;
        }
        
        return $this->serviceCategoryRepository->delete($id);
    }

    /**
     * 根据ID获取服务分类
     *
     * @param int $id
     * @return AppointmentServiceCategory|null
     */
    public function find(int $id): ?AppointmentServiceCategory
    {
        return $this->serviceCategoryRepository->find($id);
    }

    /**
     * 获取所有服务分类（用于下拉列表）
     *
     * @return array<int, array<string, mixed>>
     */
    public function getAllForSelect(): array
    {
        return $this->serviceCategoryRepository->getAllForSelect();
    }
} 