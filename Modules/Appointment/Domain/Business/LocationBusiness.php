<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Business;

use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Modules\Appointment\Domain\Repositories\LocationRepository;
use Modules\Appointment\Models\AppointmentLocation;

/**
 * 地点业务逻辑
 */
class LocationBusiness
{
    /**
     * @var LocationRepository
     */
    private LocationRepository $locationRepository;

    /**
     * 构造函数
     */
    public function __construct(LocationRepository $locationRepository)
    {
        $this->locationRepository = $locationRepository;
    }

    /**
     * 获取地点列表
     *
     * @param array<string, mixed> $filters
     * @return LengthAwarePaginator
     */
    public function getList(array $filters): LengthAwarePaginator
    {
        $filters['limit'] = $filters['limit'] ?? 15;
        $filters['page'] = $filters['page'] ?? 1;
        return $this->locationRepository->list($filters);
    }

    /**
     * 创建地点
     *
     * @param array<string, mixed> $data
     * @return AppointmentLocation
     */
    public function create(array $data): AppointmentLocation
    {
        // 设置创建者ID
        $data['creator_id'] = Auth::guard()->user()->id ?? 0;
        return $this->locationRepository->create($data);
    }

    /**
     * 更新地点
     *
     * @param int $id
     * @param array<string, mixed> $data
     * @return AppointmentLocation
     */
    public function update(int $id, array $data): AppointmentLocation
    {
        return $this->locationRepository->update($id, $data);
    }

    /**
     * 根据ID获取地点
     *
     * @param int $id
     * @return AppointmentLocation|null
     */
    public function find(int $id): ?AppointmentLocation
    {
        return $this->locationRepository->find($id);
    }
} 