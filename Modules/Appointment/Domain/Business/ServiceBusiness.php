<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Business;

use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Modules\Appointment\Domain\Repositories\ServiceRepository;
use Modules\Appointment\Models\AppointmentExtraService;
use Modules\Appointment\Models\AppointmentRecords;
use Modules\Appointment\Models\AppointmentService;
use Illuminate\Support\Facades\DB;
use Bingo\Exceptions\BizException;
use Modules\Appointment\Enums\ErrorCode;
use Illuminate\Support\Facades\Log;
use Modules\Appointment\Models\AppointmentServiceSchedule;
use Modules\Appointment\Models\AppointmentServiceSpecialSchedule;
use Modules\Appointment\Models\AppointmentServiceStaff;
use Modules\Appointment\Models\AppointmentExtraServiceRelations;

/**
 * 服务业务逻辑类
 */
class ServiceBusiness
{
    /**
     * @var ServiceRepository
     */
    private ServiceRepository $serviceRepository;

    /**
     * 构造函数
     */
    public function __construct(ServiceRepository $serviceRepository)
    {
        $this->serviceRepository = $serviceRepository;
    }

    /**
     * 获取服务列表
     *
     * @param array<string, mixed> $filters
     * @return LengthAwarePaginator
     */
    public function getList(array $filters): LengthAwarePaginator
    {
        return $this->serviceRepository->list($filters);
    }

    /**
     * 创建服务
     *
     * @param array<string, mixed> $data
     * @return AppointmentService
     * @throws Exception
     */
    public function create(array $data): AppointmentService
    {
        try {
            // 需要事务
            DB::beginTransaction();

            // 设置创建者ID
            $data['creator_id'] = Auth::guard()->id() ?? 0;

            // 1. 保存服务基础信息
            $service = $this->serviceRepository->create($data);

            // 2. 判断是否有额外服务，有则保存额外服务
            if (!empty($data['extra_services']) && is_array($data['extra_services'])) {
                // 使用仓储方法批量保存额外服务
                $this->serviceRepository->saveExtraServices($service->id, $data['extra_services'], $data['creator_id']);
            }

            // 3. 判断是否有特殊时间，有则设置特殊时间
            if (!empty($data['special_schedules']) && is_array($data['special_schedules'])) {
                // 处理特殊时间保存逻辑
                $this->serviceRepository->saveSpecialSchedules($service->id, $data['special_schedules'], $data['creator_id']);
            }

            // 4. 保存服务排班
            if (!empty($data['schedules']) && is_array($data['schedules'])) {
                // 处理排班保存逻辑
                $this->serviceRepository->saveSchedules($service->id, $data['schedules'], $data['creator_id']);
            }

            // 5. 保存员工信息
            if (!empty($data['staff_ids']) && is_array($data['staff_ids'])) {
                // 处理员工信息保存逻辑
                $this->serviceRepository->batchAssignStaff($service->id, $data['staff_ids'], $data['creator_id']);
            }

            DB::commit();

            return $service;
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception('创建服务失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新服务
     *
     * @param int $id
     * @param array<string, mixed> $data
     * @return AppointmentService
     * @throws Exception
     */
    public function update(int $id, array $data): AppointmentService
    {
        try {
            // 需要事务
            DB::beginTransaction();

            // 设置更新者ID
            $data['updater_id'] = Auth::guard()->id() ?? 0;

            // 1. 更新服务基础信息
            $service = $this->serviceRepository->update($id, $data);

            // 2. 处理额外服务
            if (isset($data['extra_services'])) {
                // 使用差异化更新策略处理额外服务
                $this->serviceRepository->syncExtraServices($id, $data['extra_services'], $data['updater_id']);
            }

            // 3. 处理特殊时间安排
            if (isset($data['special_schedules'])) {
                // 使用差异化更新策略处理特殊时间安排
                $this->serviceRepository->syncSpecialSchedules($id, $data['special_schedules'], $data['updater_id']);
            }

            // 4. 处理服务排班
            if (isset($data['schedules'])) {
                // 使用差异化更新策略处理服务排班
                $this->serviceRepository->syncSchedules($id, $data['schedules'], $data['updater_id']);
            }

            // 5. 处理员工分配
            if (isset($data['staff_ids'])) {
                // 使用差异化更新策略处理员工分配
                $this->serviceRepository->syncStaffAssignments($id, $data['staff_ids'], $data['updater_id']);
            }

            DB::commit();

            return $service;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 删除服务
     *
     * @param int $id
     * @return bool
     * @throws BizException
     */
    public function delete(int $id): bool
    {
        try {
            // 先获取服务信息
            $service = $this->serviceRepository->find($id);
            if (!$service) {
                BizException::throws(ErrorCode::SERVICE_NOT_FOUND);
            }

            // 检查服务是否存在关联的预约记录
            $appointmentCount = AppointmentRecords::where('service_id', $id)->count();
            if ($appointmentCount > 0) {
                BizException::throws(ErrorCode::SERVICE_HAS_APPOINTMENTS,ErrorCode::SERVICE_HAS_APPOINTMENTS->message());
            }

            // 使用事务保证数据一致性
            return DB::transaction(function () use ($id) {
                // 1. 删除服务相关的额外服务关联
                AppointmentExtraServiceRelations::where('service_id', $id)->delete();

                // 2. 删除服务相关的排班设置
                AppointmentServiceSchedule::where('service_id', $id)->delete();

                // 3. 删除服务相关的特殊排班
                AppointmentServiceSpecialSchedule::where('service_id', $id)->delete();

                // 4. 删除服务相关的员工分配
                AppointmentServiceStaff::where('service_id', $id)->delete();

                // 5. 最后删除服务本身
                return $this->serviceRepository->delete($id);
            });
        } catch (Exception $e) {
            // 如果是业务异常，则直接抛出
            if ($e instanceof BizException) {
                throw $e;
            }

            // 其他异常记录日志并抛出通用异常
            Log::error('删除服务失败', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw BizException::throws(ErrorCode::SERVICE_DELETE_FAILED,ErrorCode::SERVICE_DELETE_FAILED->message());
        }
    }

    /**
     * 根据ID获取服务
     *
     * @param int $id
     * @return AppointmentService
     */
    public function find(int $id): AppointmentService
    {
        $service = $this->serviceRepository->find($id);
        if (empty($service)) {
            BizException::throws(ErrorCode::SERVICE_NOT_FOUND,ErrorCode::SERVICE_NOT_FOUND->message());
        }

        // 获取服务关联的员工ID列表
        $staffIds = $service->staffs()->get()->pluck('staff_id')->toArray();
        $service->staff_ids = $staffIds;

        // 获取服务关联的排班
        $schedules = $service->schedules()->select('day_of_week', 'start_time', 'end_time', 'rest_time_list', 'is_rest_day')->get();
        $service->schedules = $schedules;

        // 获取服务关联的特殊排班
        $specialSchedules = $service->specialSchedules()->select('start_time', 'end_time')->get();
        $service->specialSchedules = $specialSchedules;

        // 获取服务关联的额外服务
        $extraServices = $service->extraServices()->with('extraService')->get()->map(function ($item) {
            return [
                'name' => $item->extraService->name,
                'category_name' => $item->extraService->category_name,
                'price' => $item->extraService->price,
                'duration' => $item->extraService->duration,
                'max_quantity' => $item->extraService->max_quantity,
                'min_quantity' => $item->extraService->min_quantity,
            ];
        });
        $service->extraServices = $extraServices;

        return $service;
    }

    /**
     * 开放服务
     *
     * @param int $id
     * @return AppointmentService
     */
    public function openService(int $id): AppointmentService
    {
        // 更新服务状态为开放(1)
        return $this->serviceRepository->update($id, [
            'status' => AppointmentService::STATUS_OPEN, // 开放状态
        ]);
    }

    /**
     * 关闭服务
     *
     * @param int $id
     * @return AppointmentService
     */
    public function closeService(int $id): AppointmentService
    {
        // 更新服务状态为关闭(0)
        return $this->serviceRepository->update($id, [
            'status' => AppointmentService::STATUS_CLOSED, // 关闭状态
        ]);
    }

    /**
     * 全部开放服务
     *
     * @return int 成功开放的服务数量
     */
    public function openAllServices(): int
    {
        // 通过仓储层批量更新状态
        return $this->serviceRepository->updateBatchStatus(AppointmentService::STATUS_OPEN); // 开放状态
    }

    /**
     * 全部关闭服务
     *
     * @return int 成功关闭的服务数量
     */
    public function closeAllServices(): int
    {
        // 通过仓储层批量更新状态
        return $this->serviceRepository->updateBatchStatus(AppointmentService::STATUS_CLOSED); // 关闭状态
    }
}
