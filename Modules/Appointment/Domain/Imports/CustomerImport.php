<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Imports;

use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Modules\Appointment\Domain\Business\CustomerBusiness;

/**
 * 客户数据导入类
 */
class CustomerImport implements 
    ToCollection, 
    WithBatchInserts,
    WithChunkReading
{
    use Importable;

    /**
     * @var int 创建者ID
     */
    protected int $creatorId;

    /**
     * @var array 导入结果统计
     */
    protected array $stats = [
        'total' => 0,
        'success' => 0,
        'failed' => 0,
        'errors' => [],
    ];

    /**
     * @var Collection|null 处理后的行数据
     */
    protected ?Collection $processedRows = null;

    /**
     * @var array 列名映射关系
     */
    protected array $columnMaps = [
        'name' => ['姓名', 'name', '名字', '客户姓名', '客戶姓名', '姓名'],
        'email' => ['邮箱', 'email', '电子邮件', '邮件', 'mail', '電子郵件', '郵件'],
        'phone' => ['手机号', 'phone', '电话', '联系电话', '手机', '手機號', '電話', '聯繫電話', '手機'],
        'gender' => ['性别', 'gender', 'sex', '性別'],
        'birthdate' => ['出生日期', 'birthday', 'birth', '生日', '出生年月', '出生日期', '生日'],
        'description' => ['描述', 'description', '备注', 'desc', '说明', '描述', '備註', '說明'],
        'photo' => ['头像', 'photo', '头像地址', '头像url', '头像链接', '頭像', '頭像地址', '頭像url', '頭像鏈接']
    ];

    /**
     * @var array 必填字段
     */
    protected array $requiredFields = ['name', 'email'];

    /**
     * @var array 字段验证规则
     */
    protected array $validationRules = [
        'name' => ['required',],
        'email' => ['required', 'email'],
        'phone' => ['nullable',],
        'gender' => ['nullable'],
        'birthdate' => ['nullable',],
        'description' => ['nullable',],
        'photo' => ['nullable',],
    ];

    /**
     * 构造函数
     *
     * @param int $creatorId 创建者ID
     */
    public function __construct(int $creatorId)
    {
        $this->creatorId = $creatorId;
    }

    /**
     * 获取标准列名
     * @param string $columnName 原始列名
     * @return string|null
     */
    protected function getStandardColumnName(string $columnName): ?string
    {
        $columnName = mb_strtolower(trim($columnName));
        foreach ($this->columnMaps as $standard => $aliases) {
            if (in_array($columnName, $aliases)) {
                return $standard;
            }
        }
        return null;
    }

    /**
     * 处理性别值
     * @param mixed $value
     * @return int|null
     */
    protected function processGenderValue(mixed $value): ?int
    {
        if (empty($value)) {
            return null;
        }

        $value = mb_strtolower(trim((string)$value));
        if (in_array($value, ['男', 'male', '1', 'm'])) {
            return 1;
        }
        if (in_array($value, ['女', 'female', '2', 'f'])) {
            return 2;
        }
        return null;
    }

    /**
     * 处理日期值
     * @param mixed $value
     * @return string|null
     */
    protected function processDateValue(mixed $value): ?string
    {
        if (empty($value)) {
            return null;
        }

        try {
            return Carbon::parse($value)->format('Y-m-d');
        } catch (\Exception $e) {
            Log::warning('日期格式转换失败', [
                'value' => $value,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 处理手机号值
     * @param mixed $value
     * @return string|null
     */
    protected function processPhoneValue(mixed $value): ?string
    {
        if (empty($value)) {
            return null;
        }

        // 移除所有非数字字符
        $value = preg_replace('/[^0-9]/', '', (string)$value);
        
        // 验证手机号长度
        if (strlen($value) < 11) {
            return null;
        }

        // 返回字符串类型的手机号
        return (string)$value;
    }

    /**
     * 验证必填字段
     * @param array $data
     * @return bool
     */
    protected function validateRequiredFields(array $data): bool
    {
        foreach ($this->requiredFields as $field) {
            if (empty($data[$field])) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证数据
     * @param array $data
     * @return array 错误信息数组
     */
    protected function validateData(array $data): array
    {
        $errors = [];
        
        foreach ($this->validationRules as $field => $rules) {
            // 检查必填字段
            if (in_array('required', $rules) && empty($data[$field])) {
                $errors[] = "{$field}不能为空";
                continue;
            }

            // 如果字段为空且不是必填，则跳过其他验证
            if (!isset($data[$field]) || $data[$field] === null) {
                continue;
            }

            // 验证字段类型和格式
            foreach ($rules as $rule) {
                if ($rule === 'required') {
                    continue;
                }

                if (is_string($rule)) {
                    if (strpos($rule, 'max:') === 0) {
                        $max = (int)substr($rule, 4);
                        if (strlen((string)$data[$field]) > $max) {
                            $errors[] = "{$field}长度不能超过{$max}个字符";
                        }
                    } elseif ($rule === 'email') {
                        if (!filter_var($data[$field], FILTER_VALIDATE_EMAIL)) {
                            $errors[] = "{$field}格式不正确";
                        }
                    } elseif ($rule === 'date') {
                        try {
                            Carbon::parse($data[$field]);
                        } catch (\Exception $e) {
                            $errors[] = "{$field}日期格式不正确";
                        }
                    } elseif ($rule === 'string') {
                        if (!is_string($data[$field])) {
                            $errors[] = "{$field}必须是字符串";
                        }
                    } elseif (strpos($rule, 'in:') === 0) {
                        $validValues = explode(',', substr($rule, 3));
                        if (!in_array($data[$field], $validValues)) {
                            $errors[] = "{$field}的值不在允许范围内";
                        }
                    }
                }
            }
        }

        return $errors;
    }

    /**
     * 处理导入的数据集合
     * @param Collection $rows
     * @return void
     */
    public function collection(Collection $rows): void
    {
        // 获取业务逻辑实例
        $business = app()->make(CustomerBusiness::class);
        
        // 初始化统计信息
        $this->stats['total'] = $rows->count() - 1; // 减去表头行
        $this->processedRows = new Collection();

        // 获取表头行并映射列名
        $headerRow = $rows[0];
        $headers = [];
        
        foreach ($headerRow as $index => $columnName) {
            if (!empty($columnName)) {
                $standardName = $this->getStandardColumnName((string)$columnName);
                if ($standardName) {
                    $headers[$index] = $standardName;
                }
            }
        }
        
        // 处理数据行（跳过表头行）
        for ($i = 1; $i < $rows->count(); $i++) {
            $row = $rows[$i];
            
            try {
                // 跳过空行
                if (empty($row) || empty(array_filter((array)$row))) {
                    continue;
                }
                
                // 映射数据
                $mappedData = [];
                foreach ($headers as $index => $fieldName) {
                    $mappedData[$fieldName] = $row[$index] ?? null;
                }
                
                // 处理映射后的数据
                $data = [
                    'name' => trim($mappedData['name'] ?? ''),
                    'email' => strtolower(trim($mappedData['email'] ?? '')),
                    'phone' => $this->processPhoneValue($mappedData['phone'] ?? null),
                    'gender' => $this->processGenderValue($mappedData['gender'] ?? null),
                    'birthdate' => $this->processDateValue($mappedData['birthdate'] ?? null),
                    'description' => trim($mappedData['description'] ?? ''),
                    'photo' => trim($mappedData['photo'] ?? ''),
                    'creator_id' => $this->creatorId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
                
                // 验证数据
                $errors = $this->validateData($data);
                if (!empty($errors)) {
                    $this->stats['failed']++;
                    $this->stats['errors'][] = [
                        'row' => $i + 1, // Excel行号
                        'errors' => $errors
                    ];
                    continue;
                }
                
                // 创建客户
                \DB::transaction(function () use ($business, $data) {
                    $customer = $business->create($data);
                    $this->processedRows->push($customer);
                    $this->stats['success']++;
                });
                
            } catch (\Exception $e) {
                $this->stats['failed']++;
                $this->stats['errors'][] = [
                    'row' => $i + 1,
                    'message' => $e->getMessage()
                ];
                
                Log::warning('客户数据导入行处理失败', [
                    'row' => $row,
                    'index' => $i + 1,
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                throw $e;
            }
        }
        
        // 记录最终导入结果
        Log::info('客户数据导入完成', [
            'total' => $this->stats['total'],
            'success' => $this->stats['success'],
            'failed' => $this->stats['failed'],
            'error_count' => count($this->stats['errors'])
        ]);
    }

    /**
     * 获取标题行
     * @param array $firstRow
     * @return array
     */
    protected function getHeaders(array $firstRow): array
    {
        $headers = [];
        foreach ($firstRow as $key => $value) {
            if (empty($key) && !empty($value)) {
                // 处理没有列名的情况，使用值作为描述
                $standardName = $this->getStandardColumnNameByValue($value);
                if ($standardName) {
                    $headers[''] = $standardName;
                }
            } else {
                $standardName = $this->getStandardColumnName($key);
                if ($standardName) {
                    $headers[$key] = $standardName;
                }
            }
        }
        return $headers;
    }

    /**
     * 根据值获取标准列名
     * @param string $value
     * @return string|null
     */
    protected function getStandardColumnNameByValue(string $value): ?string
    {
        $value = mb_strtolower(trim($value));
        if (str_contains($value, '普通会员') || str_contains($value, '普通')) {
            return 'description';
        }
        if (str_contains($value, 'vip') || str_contains($value, 'VIP')) {
            return 'description';
        }
        return null;
    }

    /**
     * 将行数据与标题行对应
     * @param array $row
     * @param array $headers
     * @return array
     */
    protected function mapRowWithHeaders(array $row, array $headers): array
    {
        $mappedRow = [];
        foreach ($headers as $originalKey => $standardKey) {
            $mappedRow[$standardKey] = $row[$originalKey] ?? null;
        }
        return $mappedRow;
    }

    /**
     * 检查是否为空行
     * @param mixed $row
     * @return bool
     */
    protected function isEmptyRow(mixed $row): bool
    {
        if (!is_array($row) && !($row instanceof \ArrayAccess)) {
            return true;
        }

        return empty(array_filter((array)$row));
    }

    /**
     * 处理行数据
     * @param mixed $row
     * @return array
     * @throws \InvalidArgumentException
     */
    protected function processRow(mixed $row): array
    {
        if (!is_array($row) && !($row instanceof \ArrayAccess)) {
            throw new \InvalidArgumentException('无效的行数据格式');
        }

        $data = [
            'name' => trim($row['name'] ?? ''),
            'email' => strtolower(trim($row['email'] ?? '')),
            'phone' => $this->processPhoneValue($row['phone'] ?? null),
            'gender' => $this->processGenderValue($row['gender'] ?? null),
            'birthdate' => $this->processDateValue($row['birthdate'] ?? null),
            'description' => trim($row['description'] ?? ''),
            'photo' => trim($row['photo'] ?? ''),
            'created_at' => now(),
            'updated_at' => now(),
        ];

        // 过滤空值
        return array_filter($data, function ($value) {
            return $value !== null && $value !== '';
        });
    }

    /**
     * 设置批量插入大小
     * @return int
     */
    public function batchSize(): int
    {
        return 100;
    }

    /**
     * 设置分块读取大小
     * @return int
     */
    public function chunkSize(): int
    {
        return 100;
    }

    /**
     * 获取处理后的数据
     * @return Collection|null
     */
    public function getProcessedRows(): ?Collection
    {
        return $this->processedRows;
    }

    /**
     * 获取导入结果统计
     * @return array
     */
    public function getStats(): array
    {
        return $this->stats;
    }
} 