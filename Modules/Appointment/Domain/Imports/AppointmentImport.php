<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Imports;

use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Modules\Appointment\Models\AppointmentRecords;
use Bingo\Exceptions\BizException;
use Modules\Appointment\Enums\ErrorCode;
use Modules\Appointment\Domain\Repositories\AppointmentRepository;
use Carbon\Carbon;
use Modules\Appointment\Domain\Business\CustomerBusiness;
use Modules\Appointment\Domain\Business\AppointmentBusiness;
use Illuminate\Support\Facades\DB;

/**
 * 预约导入类
 */
class AppointmentImport implements ToModel, WithHeadingRow, WithValidation
{
    /**
     * @var AppointmentRepository
     */
    protected AppointmentRepository $appointmentRepository;
    protected CustomerBusiness $customerBusiness;
    protected AppointmentBusiness $appointmentBusiness;

    /**
     * 构造函数
     * 
     * @param int $creatorId 创建者ID
     */
    public function __construct(
        private readonly int $creatorId
    ) {
        $this->appointmentRepository = app(AppointmentRepository::class);
        $this->customerBusiness = app(CustomerBusiness::class);
        $this->appointmentBusiness = app(AppointmentBusiness::class);
    }

    /**
     * 导入数据到模型
     * 
     * @param array $row 行数据
     * @return AppointmentRecords|null
     */
    public function model(array $row): ?AppointmentRecords
    {
        try {
            DB::beginTransaction();
            
            // 获取gender
            $gender = $this->mapGenderFromText($row['customer_gender'] ?? '');
            $birthday = $this->convertExcelDate($row['customer_birthday']);

            // 获取客户
            $customer = $this->customerBusiness->createOrUpdate([
                'email' => $row['customer_email'] ?? '',
                'phone' => strval($row['customer_phone']) ?? '',
                'name' => $row['customer_name'] ?? null,
                'gender' => $gender,
                'birthday' => $birthday,
                'description' => $row['customer_description'] ?? null,
            ], $this->creatorId);

            // 根据服务名称查找服务ID
            $serviceName = $row['service_name'] ?? '';
            $service = $this->appointmentRepository->findServiceByName($serviceName);       
            if (!$service) {
                throw new \Exception("服务 '{$serviceName}' 不存在");
            }

            // 获取状态
            $statusText = $row['status'] ?? '';
            $status = $this->mapStatusFromText($statusText);

            // 获取支付状态
            $paymentStatusText = $row['payment_status'] ?? '';
            $paymentStatus = $this->mapPaymentStatusFromText($paymentStatusText);

            // 处理Excel日期格式
            $appointmentDate = $this->convertExcelDate($row['appointment_date']);
            $paymentTime = $this->convertExcelDate($row['payment_time']);
            
            // 计算预约结束时间
            $endTime = $appointmentDate->copy()->addSeconds($service->duration);

            // 计算价格
            $priceData = [
                'original_price' => $service->price,
                'discount_name' => $row['discount_name'] ?? '',
            ];
            $this->appointmentBusiness->calculatePrice($priceData);

            DB::commit();

            // 创建预约记录
            return new AppointmentRecords([
                'customer_id' => $customer->id,
                'service_id' => $service->id,
                'location' => $row['location'] ?? '',
                'appointment_date' => $appointmentDate,
                'end_time' => $endTime,
                'status' => $status,
                'original_price' => $priceData['original_price'],
                'discount_id' => $priceData['discount_id'],
                'final_price' => $priceData['final_price'],
                'payment_method' => $row['payment_method'] ?? '',
                'payment_status' => $paymentStatus,
                'payment_time' => $paymentTime,
                'remark' => $row['remark'] ?? '',
                'creator_id' => $this->creatorId,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            BizException::throws(ErrorCode::APPOINTMENT_IMPORT_FAILED, '导入失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取验证规则
     * 
     * @return array
     */
    public function rules(): array
    {
        return [
            'customer_email' => ['required', 'email'],
            'customer_phone' => ['required', ],
            'customer_name' => ['required',],
            'customer_gender' => ['nullable',],
            'customer_birthday' => ['nullable'],
            'customer_description' => ['nullable'],
            'service_name' => ['required',],
            'location' => ['required',],
            'appointment_date' => ['required'],
            'status' => ['nullable',],
            'discount_name' => ['nullable',],
            'payment_method' => ['nullable',],
            'payment_status' => ['nullable',],
            'payment_time' => ['nullable',],
            'remark' => ['nullable',],
        ];
    }

    /**
     * 获取验证错误消息
     * 
     * @return array
     */
    public function customValidationMessages(): array
    {
        return [
            'customer_email.required' => T('Appointment::import_template.customer_email_required'),    
            'customer_email.email' => T('Appointment::import_template.customer_email_email'),
            'customer_phone.required' => T('Appointment::import_template.customer_phone_required'),
            'customer_phone.string' => T('Appointment::import_template.customer_phone_string'),
            'customer_phone.max' => T('Appointment::import_template.customer_phone_max'),
            'customer_name.required' => T('Appointment::import_template.customer_name_required'),
            'customer_name.string' => T('Appointment::import_template.customer_name_string'),
            'customer_name.max' => T('Appointment::import_template.customer_name_max'),
            'customer_gender.string' => T('Appointment::import_template.customer_gender_string'),
            'customer_gender.max' => T('Appointment::import_template.customer_gender_max'),
            'service_name.required' => T('Appointment::import_template.service_name_required'),
            'service_name.string' => T('Appointment::import_template.service_name_string'),
            'service_name.max' => T('Appointment::import_template.service_name_max'),
            'location.required' => T('Appointment::import_template.location_required'),
            'location.string' => T('Appointment::import_template.location_string'),
            'location.max' => T('Appointment::import_template.location_max'),
            'appointment_date.required' => T('Appointment::import_template.appointment_date_required'),
            'appointment_date.date' => T('Appointment::import_template.appointment_date_date'),
            'status.string' => T('Appointment::import_template.status_string'),
            'status.max' => T('Appointment::import_template.status_max'),
            'discount_name.string' => T('Appointment::import_template.discount_name_string'),
            'discount_name.max' => T('Appointment::import_template.discount_name_max'),
            'payment_method.string' => T('Appointment::import_template.payment_method_string'),
            'payment_method.max' => T('Appointment::import_template.payment_method_max'),
            'payment_status.string' => T('Appointment::import_template.payment_status_string'),
            'payment_status.max' => T('Appointment::import_template.payment_status_max'),
            'payment_time.date' => T('Appointment::import_template.payment_time_date'),
            'remark.string' => T('Appointment::import_template.remark_string'),
            'remark.max' => T('Appointment::import_template.remark_max'),
        ];
    }

    /**
     * 将性别文本映射为性别值
     * 
     * @param string $genderText 性别文本
     * @return int 性别值
     * @throws \InvalidArgumentException 当性别不存在时抛出异常
     */
    private function mapGenderFromText(string $genderText): int
    {
        // 定义性别映射关系
        $genderMap = [
            1=>['男','男性','male','m','1','男士'],
            2=>['女','女性','female','f','2','女士'],
            0=>['未知','unknown','0','不明'],
            0=>['保密','保密','0','隱私'],
        ];
        
        // 转换为小写以增加匹配成功率
        $normalizedGender = strtolower(trim($genderText));
        
        foreach ($genderMap as $key => $value) {
            if (in_array($normalizedGender, $value)) {
                return $key;
            }
        }
        
        // 如果没有匹配到，抛出异常
        throw new \InvalidArgumentException("无效的性别值: {$genderText}");
    }

    /**
     * 将状态文本映射为状态值
     *
     * @param string $statusText 状态文本
     * @return int 状态值
     */
    /**
     * 将状态文本映射为状态值
     *
     * @param string $statusText 状态文本
     * @return int 状态值
     * @throws \InvalidArgumentException 当状态不存在时抛出异常
     */
    private function mapStatusFromText(string $statusText): int
    {
        // 定义状态映射关系
        $statusMap = [
            AppointmentRecords::STATUS_PENDING => ['待确认', '未确认', 'pending', '待处理'],
            AppointmentRecords::STATUS_CONFIRMED => ['已确认', '确认', 'confirmed', '已接受'],
            AppointmentRecords::STATUS_COMPLETED => ['已完成', '完成', 'completed', '已结束'],
            AppointmentRecords::STATUS_CANCELLED => ['已取消', '取消', 'cancelled', 'canceled', '已关闭'],
            AppointmentRecords::STATUS_REJECTED => ['已拒绝', '拒绝', 'rejected', '已拒绝'],
            AppointmentRecords::STATUS_NO_SHOW => ['未到', '未到', 'no_show', '未到'],
            AppointmentRecords::STATUS_RESCHEDULED => ['已改期', '改期', 'rescheduled', '已改期'],
        ];
        
        // 转换为小写以增加匹配成功率
        $normalizedStatus = strtolower(trim($statusText));
        
        foreach ($statusMap as $key => $value) {
            if (in_array($normalizedStatus, $value)) {
                return $key;
            }
        }
        
        // 如果没有匹配到，抛出异常
        throw new \InvalidArgumentException("无效的预约状态: {$statusText}");
    }

    /**
     * 将支付状态文本映射为支付状态值
     *
     * @param string $statusText 支付状态文本
     * @return int 支付状态值
     * @throws \InvalidArgumentException 当支付状态不存在时抛出异常
     */
    private function mapPaymentStatusFromText(string $statusText): int
    {
        // 定义支付状态映射关系
        $paymentStatusMap = [
            AppointmentRecords::PAYMENT_STATUS_PENDING => ['待支付', '未支付', 'unpaid', '未付款'],
            AppointmentRecords::PAYMENT_STATUS_PAID => ['已支付', '已付款', 'paid', '支付完成'],
            AppointmentRecords::PAYMENT_STATUS_FAILED => ['支付失败', '失败', 'failed', '支付失败']
        ];
        
        // 转换为小写以增加匹配成功率
        $normalizedStatus = strtolower(trim($statusText));
        
        foreach ($paymentStatusMap as $key => $value) {
            if (in_array($normalizedStatus, $value)) {
                return $key;
            }
        }
        
        // 如果没有匹配到，抛出异常
        throw new \InvalidArgumentException("无效的支付状态: {$statusText}");
    }

    
    /**
     * 转换Excel日期格式为Carbon对象
     * 
     * 处理多种可能的日期格式:
     * 1. Excel数值格式日期（从1900年1月1日开始的天数）
     * 2. 字符串格式日期（如"2023-01-01"、"01/01/2023"等）
     * 3. 空值或无效值
     * 
     * @param mixed $excelDate Excel日期值
     * @return Carbon|null 返回Carbon日期对象或null
     * @throws \Exception 当日期格式无法解析时可能抛出异常
     */
    private function convertExcelDate($excelDate): ?Carbon
    {
        // 处理空值情况
        if (empty($excelDate) && $excelDate !== 0) {
            return null;
        }
        
        try {
            // 处理Excel数值格式日期
            if (is_numeric($excelDate)) {
                // 检查是否是有效的Excel日期值（通常大于等于0）
                if ($excelDate >= 0) {
                    // Excel日期是从1900年1月1日开始的天数
                    // 需要减去2因为Excel错误地认为1900是闰年
                    $unixTimestamp = ($excelDate - 25569) * 86400;
                    return Carbon::createFromTimestamp($unixTimestamp);
                }
            }
            
            // 处理字符串格式日期
            if (is_string($excelDate)) {
                $trimmedDate = trim($excelDate);
                if (!empty($trimmedDate)) {
                    // 尝试解析字符串日期
                    $date = Carbon::parse($trimmedDate);
                    
                    // 验证解析后的日期是否有效（不是默认日期且在合理范围内）
                    if ($date->year >= 1900 && $date->year <= 2100) {
                        return $date;
                    }
                }
            }
            
            // 对于其他格式，尝试直接解析
            return Carbon::parse($excelDate);
        } catch (\Exception $e) {
            // 记录错误但不中断流程
            \Log::warning("日期格式转换失败: {$excelDate}, 错误: " . $e->getMessage());
            
            // 返回null表示无法解析
            return null;
        }
    }
} 