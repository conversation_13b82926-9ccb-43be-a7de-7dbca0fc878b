<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Imports;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToModel;
use Modules\Appointment\Models\IamUser;
use Modules\Iam\Models\IamUserPositions;
use Modules\Iam\Models\IamUserDepartments;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Modules\Appointment\Domain\Repositories\StaffRepository;

/**
 * 员工数据导入类
 */
class StaffImport implements ToModel, WithHeadingRow, WithValidation
{
    /**
     * @var int 总行数
     */
    private int $rowCount = 0;

    /**
     * @var int 成功导入数
     */
    private int $successCount = 0;

    /**
     * @var int 失败数
     */
    private int $failCount = 0;

    /**
     * @var array 错误信息
     */
    private array $errors = [];

    /**
     * 构造函数
     * 
     * @param int $creatorId 创建者ID
     */
    public function __construct(
        private readonly int $creatorId,
        private readonly StaffRepository $staffRepository
    ) {
    }

    /**
     * 处理导入的数据集合
     * 
     * @param array $row 行数据
     * @return IamUser|null
     */
    public function model(array $row): ?IamUser
    {
        $this->rowCount++;
        
        try {
            DB::beginTransaction();
            
            // 检查必填字段
            if (empty($row['name']) || empty($row['email'])) {
                throw new \Exception('姓名和邮箱不能为空');
            }

            // 检查邮箱是否已存在
            $email = $row['email'];
            $existingUser = IamUser::where('email', $email)->first();
            if ($existingUser) {
                throw new \Exception("邮箱已存在: {$email}");
            }

            // 检查手机号是否已存在
            $phone = $row['phone'] ?? null;
            if (!empty($phone)) {
                $existingUserByPhone = IamUser::where('phone', $phone)->first();
                if ($existingUserByPhone) {
                    throw new \Exception("手机号已存在: {$phone}");
                }
            }

            // 创建员工基础数据
            $user = new IamUser();
            $user->name = $row['name'];
            $user->email = $email;
            $user->phone = $phone;
            $user->password = password_hash(IamUser::DEFAULT_PASSWORD, PASSWORD_DEFAULT);
            $user->creator_id = $this->creatorId;
            $user->application_id = IamUser::APPLICATION_BACKEND;
            $user->user_type = IamUser::USER_TYPE_EMPLOYEE;
            $user->save();

            // 处理职位关联
            $positionName = $row['position_name'] ?? null;
            if ($positionName) {
                // 检查职位是否存在
                $position = $this->staffRepository->findPositionByName($positionName);
                if (!$position) {
                    throw new \Exception("职位不存在: {$positionName}");
                }
                
                // 创建职位关联
                IamUserPositions::create([
                    'user_id' => $user->id,
                    'position_id' => $position->id,
                    'creator_id' => $this->creatorId,
                ]);
            }

            // 处理部门关联
            $departmentName = $row['department_name'] ?? null;
            if ($departmentName) {
                // 检查部门是否存在
                $department = $this->staffRepository->findDepartmentByName($departmentName);
                if (!$department) {
                    throw new \Exception("部门不存在: {$departmentName}");
                }
                
                // 创建部门关联
                IamUserDepartments::create([
                    'user_id' => $user->id,
                    'department_id' => $department->id,
                    'application_id' => IamUser::APPLICATION_BACKEND,
                    'creator_id' => $this->creatorId,
                ]);
            }

            DB::commit();
            $this->successCount++;
            
            return $user;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->failCount++;
            $this->errors[] = [
                'row' => $this->rowCount + 1, // 加1是因为有标题行
                'message' => $e->getMessage()
            ];

            Log::error('员工数据导入失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'row' => $this->rowCount + 1,
            ]);
            
            return null;
        }
    }

    /**
     * 定义导入验证规则
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => ['required',],
            'email' => ['required', 'email'],
            'phone' => ['nullable'],
            'position_name' => ['nullable',],
            'department_name' => ['nullable',],
        ];
    }

    /**
     * 获取验证错误消息
     * 
     * @return array
     */
    public function customValidationMessages(): array
    {
        return [
            'name.required' => '员工姓名不能为空',
            'name.string' => '员工姓名必须是字符串',
            'name.max' => '员工姓名不能超过50个字符',
            'email.required' => '邮箱不能为空',
            'email.email' => '邮箱格式不正确',
            'email.max' => '邮箱不能超过100个字符',
            'phone.string' => '手机号必须是字符串',
            'phone.max' => '手机号不能超过20个字符',
            'position_name.string' => '职位名称必须是字符串',
            'position_name.max' => '职位名称不能超过50个字符',
            'department_name.string' => '部门名称必须是字符串',
            'department_name.max' => '部门名称不能超过50个字符',
        ];
    }

    /**
     * 获取总行数
     *
     * @return int
     */
    public function getRowCount(): int
    {
        return $this->rowCount;
    }

    /**
     * 获取成功导入数
     *
     * @return int
     */
    public function getSuccessCount(): int
    {
        return $this->successCount;
    }

    /**
     * 获取失败数
     *
     * @return int
     */
    public function getFailCount(): int
    {
        return $this->failCount;
    }

    /**
     * 获取错误信息
     *
     * @return array
     */
    public function getErrors(): array
    {
        return $this->errors;
    }
} 