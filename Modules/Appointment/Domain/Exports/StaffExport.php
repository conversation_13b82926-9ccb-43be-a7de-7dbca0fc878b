<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Exports;

use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Modules\Appointment\Models\IamUser;
use Maatwebsite\Excel\Concerns\FromQuery;

/**
 * 员工数据导出类
 */
class StaffExport implements FromQuery, WithHeadings, WithMapping, WithStyles
{

    /**
     * 构造函数
     *
     * @param array $filters 过滤条件
     */
    public function __construct( private readonly array $filters = [])
    {
       
    }

    /**
     * 获取要导出的数据集合
     *
     * @return Builder
     */
    public function query()
    {
       $page = (int)($this->filters['page'] ?? 1);
       $limit = (int)($this->filters['limit'] ?? 20);
       return IamUser::query()
            ->with(['position', 'department'])
            ->where('application_id', IamUser::APPLICATION_BACKEND)
            ->where('user_type', IamUser::USER_TYPE_EMPLOYEE)
            ->forPage($page, $limit)
            ->orderBy('id', 'desc');
    }

    /**
     * 设置表头
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Email',
            'Phone',
            'Position Name',
            'Department Name',
            'Created At',
            'Updated At',
        ];
    }

    /**
     * 映射数据
     *
     * @param mixed $staff
     * @return array
     */
    public function map($staff): array
    {
        return [
            $staff->id,
            $staff->name,
            $staff->email,
            $staff->phone ?? '',
            $staff->position_name ?? '',
            $staff->department_name ?? '',
            $staff->created_at ? $staff->created_at->format('Y-m-d H:i:s') : '',
            $staff->updated_at ? $staff->updated_at->format('Y-m-d H:i:s') : '',
        ];
    }

    /**
     * 定义样式
     *
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // 设置第一行的样式
            1 => [
                'font' => ['bold' => true],
                'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER],
            ],
            // 设置所有单元格的垂直居中
            'A1:H' . ($this->query()->count() + 1) => [
                'alignment' => ['vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER],
            ],
        ];
    }
} 