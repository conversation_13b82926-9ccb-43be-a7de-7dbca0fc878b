<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Collection;

/**
 * 员工导入模板
 */
class StaffImportTemplate implements FromCollection, WithHeadings, WithStyles
{
    /**
     * 返回模板数据集合
     * 
     * @return Collection
     */
    public function collection(): Collection
    {
        // 返回一个空集合，只包含表头
        return collect([
            [
                T('Appointment::import_staff.name1'),   
                T('Appointment::import_staff.email1'),
                T('Appointment::import_staff.phone1'),
                T('Appointment::import_staff.position_name1'),
                T('Appointment::import_staff.department_name1'),
            ],
            [
                T('Appointment::import_staff.name2'),
                T('Appointment::import_staff.email2'),
                T('Appointment::import_staff.phone2'),
                T('Appointment::import_staff.position_name2'),
                T('Appointment::import_staff.department_name2'),
            ]
        ]);
    }

    /**
     * 设置表头
     * 
     * @return array
     */
    public function headings(): array
    {
        return [
            'Name',           
            'Email',
            'Phone',        
            'Position Name',
            'Department Name',
        ];
    }

    /**
     * 设置样式
     * 
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet): array
    {
        // 设置列宽
        $sheet->getColumnDimension('A')->setWidth(15);
        $sheet->getColumnDimension('B')->setWidth(15);
        $sheet->getColumnDimension('C')->setWidth(25);
        $sheet->getColumnDimension('D')->setWidth(15);
        $sheet->getColumnDimension('E')->setWidth(15);
        $sheet->getColumnDimension('F')->setWidth(15);
        $sheet->getColumnDimension('G')->setWidth(20);
        $sheet->getColumnDimension('H')->setWidth(30);
        
        // 设置表头样式
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'rgb' => 'E0E0E0',
                    ],
                ],
            ],
        ];
    }
}
