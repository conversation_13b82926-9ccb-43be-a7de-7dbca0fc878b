<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

/**
 * 客户导入模板导出类
 */
class CustomerImportTemplate implements FromCollection, WithHeadings, WithStyles
{
    /**
     * 获取导出数据集合
     */
    public function collection(): Collection
    {
        return collect([
            [
                T('Appointment::import_customer.name1'),
                T('Appointment::import_customer.email1'),
                T('Appointment::import_customer.phone1'),
                T('Appointment::import_customer.gender1'),
                T('Appointment::import_customer.birthdate1'),
                T('Appointment::import_customer.description1'),
            ],
            [
                T('Appointment::import_customer.name2'),
                T('Appointment::import_customer.email2'),
                T('Appointment::import_customer.phone2'),
                T('Appointment::import_customer.gender2'),
                T('Appointment::import_customer.birthdate2'),
                T('Appointment::import_customer.description2'),
            ]
        ]);
    }

    /**
     * 获取表头
     */
    public function headings(): array
    {
        return [
            T('Appointment::validation.import_customer.name'),
            T('Appointment::validation.import_customer.email'),
            T('Appointment::validation.import_customer.phone'),
            T('Appointment::validation.import_customer.gender'),
            T('Appointment::validation.import_customer.birthdate'),
            T('Appointment::validation.import_customer.description'),
        ];
    }

    /**
     * 设置样式
     */
    public function styles(Worksheet $sheet): array
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
} 