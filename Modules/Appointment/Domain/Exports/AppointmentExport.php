<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Exports;

use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Modules\Appointment\Models\AppointmentRecords;
use Bingo\Exceptions\BizException;
use Modules\Appointment\Enums\ErrorCode;

/**
 * 预约导出类
 */
class AppointmentExport implements FromQuery, WithHeadings, WithMapping, WithStyles
{
    /**
     * 构造函数
     *
     * @param array $filters 过滤条件
     */
    public function __construct(
        private readonly array $filters = []
    ) {}

    /**
     * 获取查询
     *
     * @return Builder
     */
    public function query()
    {
        try {
            $query = AppointmentRecords::query()
                ->with(['customer', 'service']);

            // 获取分页参数
            $page = (int)($this->filters['page'] ?? 1);
            $limit = (int)($this->filters['limit'] ?? 100);
            
            // 应用分页
            $query->forPage($page, $limit);

            // 按日期排序
            return $query->orderBy('id', 'desc');

        } catch (\Exception $e) {
            BizException::throws(ErrorCode::APPOINTMENT_EXPORT_FAILED, ErrorCode::APPOINTMENT_EXPORT_FAILED->message());
        }
    }

    /**
     * 获取表头
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            '客户名称',
            '服务名称',
            '地点',
            '预约时间',
            '结束时间',
            '状态',
            '原价',
            '优惠金额',
            '实付金额',
            '支付方式',
            '支付状态',
            '支付时间',
            '备注'
        ];
    }

    /**
     * 映射数据
     *
     * @param AppointmentRecords $row
     * @return array
     */
    public function map($row): array
    {
        return [
            $row->id,
            $row->customer ? $row->customer->name : '',
            $row->service ? $row->service->name : '',
            $row->location,
            $row->appointment_date,
            $row->end_time,
            AppointmentRecords::$statusMap[$row->status] ?? '',
            $row->original_price,
            $row->discount_amount,
            $row->final_price,
            $row->payment_method,
            AppointmentRecords::$paymentStatusMap[$row->payment_status] ?? '',
            $row->payment_time,
            $row->remark
        ];
    }

    /**
     * 设置样式
     *
     * @param Worksheet $sheet 工作表
     * @return array 样式配置
     */
    public function styles(Worksheet $sheet): array
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}
