<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Modules\Appointment\Domain\Repositories\CustomerRepository;

/**
 * 客户数据导出类
 */
class CustomerExport implements FromCollection, WithHeadings, WithStyles, WithColumnFormatting, ShouldAutoSize
{
    /**
     * @var array 查询条件
     */
    protected array $criteria;

    /**
     * 构造函数
     *
     * @param array $criteria 查询条件
     */
    public function __construct(array $criteria)
    {
        $this->criteria = $criteria;
    }

    /**
     * 获取导出数据集合
     *
     * @return Collection 导出数据
     */
    public function collection(): Collection
    {
        // 获取仓储层实例
        $repository = app()->make(CustomerRepository::class);
        
        // 获取分页数据
        $paginator = $repository->list($this->criteria);
        
        // 获取所有客户数据
        $customers = $paginator->getCollection();

        return $customers->map(function ($customer) {
            // 获取性别文字描述
            $gender = '';
            if ($customer->gender === 1) {
                $gender = '男';
            } elseif ($customer->gender === 2) {
                $gender = '女';
            }

            // 返回格式化后的数据
            return [
                'id' => $customer->id,
                'name' => $customer->name,
                'email' => $customer->email,
                'phone' => $customer->phone,
                'gender' => $gender,
                'birthdate' => $customer->birthdate,
                'appointment_count' => $customer->appointment_count ?? 0,
                'last_appointment_at' => $customer->last_appointment_at ? $customer->last_appointment_at->format('Y-m-d H:i:s') : '',
                'created_at' => $customer->created_at ? $customer->created_at->format('Y-m-d H:i:s') : '',
            ];
        });
    }

    /**
     * 获取表头
     *
     * @return array 表头
     */
    public function headings(): array
    {
        return [
            T('Appointment::validation.export_customer.id'),
            T('Appointment::validation.export_customer.name'),
            T('Appointment::validation.export_customer.email'),
            T('Appointment::validation.export_customer.phone'),
            T('Appointment::validation.export_customer.gender'),
            T('Appointment::validation.export_customer.birthdate'),
            T('Appointment::validation.export_customer.appointment_count'),
            T('Appointment::validation.export_customer.last_appointment_at'),
            T('Appointment::validation.export_customer.created_at'),
        ];
    }

    /**
     * 设置样式
     *
     * @param Worksheet $sheet 工作表
     * @return array 样式配置
     */
    public function styles(Worksheet $sheet): array
    {
        // 设置表头加粗
        $sheet->getStyle('A1:I1')->getFont()->setBold(true);
        
        // 设置单元格自动换行
        $sheet->getStyle('A:I')->getAlignment()->setWrapText(true);
        
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }

    /**
     * 设置列格式
     *
     * @return array 列格式配置
     */
    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_TEXT, // 手机号格式为文本，防止被识别为数字而丢失前导零
            'F' => NumberFormat::FORMAT_DATE_YYYYMMDD, // 出生日期格式
            'H' => NumberFormat::FORMAT_DATE_DATETIME, // 最近预约时间格式
            'I' => NumberFormat::FORMAT_DATE_DATETIME, // 创建时间格式
        ];
    }
} 