<?php

declare(strict_types=1);

namespace Modules\Appointment\Domain\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

/**
 * 预约导入模板导出类
 */
class AppointmentTemplateExport implements FromArray, WithHeadings, WithStyles
{
    /**
     * 获取数据
     * 
     * @return array
     */
    public function array(): array
    {
        // 返回空数组作为模板数据
        return [
            // 示例数据行，用于指导用户如何填写
            [
                '<EMAIL>', // 客户邮箱
                '13800138000', // 客户电话
                T('Appointment::import_template.customer_name'), // 客户姓名
                T('Appointment::import_template.customer_gender'), // 客户性别
                '1990-01-01', // 客户生日
                T('Appointment::import_template.customer_description'), // 客户描述
                T('Appointment::import_template.service_name'), // 服务名称
                T('Appointment::import_template.location'), // 预约地点
                '2023-12-01 14:00:00', // 预约时间
                T('Appointment::import_template.status'), // 状态：待确认、已确认、已完成、已取消
                T('Appointment::import_template.discount_name'), // 折扣名称
                T('Appointment::import_template.payment_method'), // 支付方式：微信支付、支付宝、现金
                T('Appointment::import_template.payment_status'), // 支付状态：待支付、已支付、已退款
                '2023-12-01 13:30:00', // 支付时间
                T('Appointment::import_template.remark'), // 备注
            ],
            // 第二个示例，展示不同情况
            [
                '<EMAIL>',
                '13900139000',
                T('Appointment::import_template.customer_name2'),
                T('Appointment::import_template.customer_gender2'),
                '1990-01-01',
                T('Appointment::import_template.customer_description2'),
                T('Appointment::import_template.service_name2'),
                T('Appointment::import_template.location2'),
                '2023-12-02 10:30:00',
                T('Appointment::import_template.status2'),
                T('Appointment::import_template.discount_name2'),
                T('Appointment::import_template.payment_method2'),
                T('Appointment::import_template.payment_status2'),
                '2023-12-01 20:15:00',
                T('Appointment::import_template.remark2'),
            ]
        ];
    }

    /**
     * 获取表头
     * 
     * @return array
     */
    public function headings(): array
    {
        return [
            'Customer Email',
            'Customer Phone',
            'Customer Name',
            'Customer Gender',
            'Customer Birthday',
            'Customer Description',
            'Service Name',
            'Location',
            'Appointment Date',
            'Status',
            'Discount Name',
            'Payment Method',
            'Payment Status',
            'Payment Time',
            'Remark',
        ];
    }

    /**
     * 设置样式
     * 
     * @param Worksheet $sheet 工作表
     * @return array
     */
    public function styles(Worksheet $sheet): array
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
} 