<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 预约模块设置
    |--------------------------------------------------------------------------
    |
    | 这里定义了关于预约模块的各种配置
    |
    */
    
    // 导出设置
    'export' => [
        // 最大导出数量限制，防止内存溢出
        'max_limit' => 10000,
        
        // 默认导出页面大小
        'default_limit' => 1000,
        
        // 导出文件类型
        'formats' => [
            'xlsx' => 'Excel',
            'csv' => 'CSV',
        ],
        
        // 导出文件命名格式
        'filename_pattern' => [
            'customer' => '客户数据_{datetime}.xlsx',
            'appointment' => '预约数据_{datetime}.xlsx',
            'payment' => '支付数据_{datetime}.xlsx',
        ],
        
        // 导出任务超时时间（秒）
        'timeout' => 300,
        
        // 是否在后台队列中处理大量数据导出
        'use_queue' => true,
        
        // 队列名称
        'queue_name' => 'exports',
    ],
    
    // 导入设置
    'import' => [
        // 允许导入的文件类型
        'allowed_types' => ['xlsx', 'xls', 'csv'],
        
        // 最大文件大小（KB）
        'max_file_size' => 5120, // 5MB
        
        // 是否在导入时验证数据
        'validate' => true,
        
        // 导入模板配置
        'templates' => [
            'customer' => [
                'filename' => '客户导入模板.xlsx',
                'required_fields' => ['姓名', '邮箱'],
                'optional_fields' => ['手机号', '性别', '出生日期', '描述'],
            ],
        ],
        
        // 导入批处理大小
        'batch_size' => 100,
        
        // 是否在后台队列中处理大量数据导入
        'use_queue' => true,
        
        // 队列名称
        'queue_name' => 'imports',
    ],
]; 