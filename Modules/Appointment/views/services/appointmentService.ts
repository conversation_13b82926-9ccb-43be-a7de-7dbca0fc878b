import http from '/admin/support/http'
import type {
  IApiResponse
} from '../types'

// 预约面板接口
export const appointmentService = {
  
  // 获取预约列表
  getAppointmentList: (params: any) => {
    // 根据type参数决定使用哪个接口
    const url = params.type === 'my' 
      ? '/reservation/appointments/my' 
      : '/reservation/appointments'
    
    // 删除type参数，避免传递给后端API
    const apiParams = { ...params }
    delete apiParams.type
    
    return http.get<IApiResponse>(url, apiParams)
  },

  // 获取预约列表
  getAppointmentLists: (params: any) => {
    return http.get<IApiResponse>(`/reservation/appointments`, params)
  },
  
  // 获取预约详情
  getAppointmentDetail: (id: number) => {
    return http.get<IApiResponse>(`/reservation/appointments/${id}`)
  },
  
  // 更新预约
  updateAppointment: (id: number, data: {
    location?: string,
    service_id?: number,
    appointment_date?: string,
    customer_id?: number,
    status?: number,
    discount_id?: number | null,
    remark?: string
  }) => {
    return http.put<IApiResponse>(`/reservation/appointments/${id}`, data)
  },
  
  // 获取服务列表
  getServiceList: (params?: any) => {
    return http.get<IApiResponse>(`/reservation/services`, params)
  },
  
  // 获取服务详情
  getServiceDetail: (id: number) => {
    return http.get<IApiResponse>(`/reservation/services/${id}`)
  },
  
  // 创建服务项目
  createService: (data: {
    category_id: string | number;
    name: string;
    price: number;
    duration: number;
    is_show_price: boolean;
    is_show_duration: boolean;
    is_repeat_service: boolean;
    is_repeat_service_pay_type: number | null;
    is_repeat_service_category: number | null;
    is_repeat_service_count: number | null;
    person_count: number;
    description?: string;
    pay_types?: string[];
    only_member_visible?: boolean;
    status: boolean;
    extra_services?: Array<{
      name: string;
      category_name: string;
      price: number;
      duration: number;
      max_quantity: number;
      min_quantity: number;
    }>;
    schedules?: Array<{
      day_of_week: number;
      start_time: string;
      end_time: string;
      rest_time_list: Array<{
        start_time: string;
        end_time: string;
      }>;
      is_rest_day: boolean;
    }>;
    special_schedules?: Array<{
      start_time: string;
      end_time: string;
    }>;
    staff_ids: string[] | number[];
  }) => {
    return http.post<IApiResponse>(`/reservation/services`, data)
  },
  
  // 更新服务项目
  updateService: (id: number, data: {
    category_id?: string | number;
    name?: string;
    price?: number;
    duration?: number;
    is_show_price?: boolean;
    is_show_duration?: boolean;
    is_repeat_service?: boolean;
    is_repeat_service_pay_type?: number | null;
    is_repeat_service_category?: number | null;
    is_repeat_service_count?: number | null;
    person_count?: number;
    description?: string;
    pay_types?: string[];
    only_member_visible?: boolean;
    status?: boolean;
    extra_services?: Array<{
      name: string;
      category_name: string;
      price: number;
      duration: number;
      max_quantity: number;
      min_quantity: number;
    }>;
    schedules?: Array<{
      day_of_week: number;
      start_time: string;
      end_time: string;
      rest_time_list: Array<{
        start_time: string;
        end_time: string;
      }>;
      is_rest_day: boolean;
    }>;
    special_schedules?: Array<{
      start_time: string;
      end_time: string;
    }>;
    staff_ids?: string[] | number[];
  }) => {
    return http.put<IApiResponse>(`/reservation/services/${id}`, data)
  },
  
  // 删除服务项目
  deleteService: (id: number) => {
    return http.delete<IApiResponse>(`/reservation/services/${id}`)
  },
  
  // 获取服务分类列表
  getServiceCategories: (params?: any) => {
    return http.get<IApiResponse>(`/reservation/service-categories`, params)
  },
  
  // 创建服务分类
  createServiceCategory: (data: any) => {
    return http.post<IApiResponse>(`/reservation/service-categories`, data)
  },
  
  // 创建预约
  createAppointment: (data: {
    location?: string,
    service_id?: number,
    appointment_date?: string,
    customer_id?: number,
    status?: number,
    discount_id?: number | null,
    remark?: string
  }) => {
    return http.post<IApiResponse>(`/reservation/appointments`, data)
  },

  // 删除预约
  deleteAppointment: (id: number) => {
    return http.delete<IApiResponse>(`/reservation/appointments/${id}`)
  },
  
  // 获取预约设置
  getSettings: () => {
    return http.get<IApiResponse>(`/reservation/settings`)
  },
  
  // 保存预约设置
  saveSettings: (data: {
    appointment_limit: string,
    non_member_record: boolean,
    time_slot_interval: number,
    cross_time_slot: boolean,
    advance_booking_time: number,
    max_booking_days: number,
    default_appointment_status: number,
    default_payment_status: number,
    allow_overtime_booking: boolean,
    allow_admin_overtime_booking: boolean,
    appointment_reschedule_notification: string,
    appointment_cancel_notification: string,
    appointment_confirm_notification: string,
    holiday_settings: Array<{
      start: string,
      end: string
    }>
  }) => {
    return http.post(`/reservation/settings`, data)
  },
  
  // 导入预约
  importAppointments: (formData: FormData) => {
    return http.post<IApiResponse>(`/reservation/appointments/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  // 获取仪表盘数据
  getDashboardData: (params: {
    start_date?: string,
    end_date?: string
  }) => {
    return http.get<IApiResponse>(`/reservation/dashboard`, params)
  },
  
  // 更新付款记录
  updatePayment: (id: number, data: any) => {
    return http.put<IApiResponse>(`/reservation/appointments/${id}/payment`, data)
  },
  
  // 获取客户列表
  getCustomerList: (params?: any) => {
    return http.get<IApiResponse>(`/reservation/customers`, params)
  },
  
  // 删除客户
  deleteCustomer: (ids: any) => {
    return http.delete<IApiResponse>(`/reservation/customers`, { ids })
  },
  
  // 导入客户
  importCustomers: (formData: FormData) => {
    return http.post<IApiResponse>(`/reservation/customers/import`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  
  // 创建客户
  createCustomer: (data: any) => {
    return http.post<IApiResponse>(`/reservation/customers`, data)
  },

  // 上传客户头像
  uploadCustomerAvatar: (formData: any) => {
    return http.post<IApiResponse>(`/media/upload`, formData)
  },
  
  // 上传服务图片
  uploadServiceImage: (formData: any) => {
    return http.post<IApiResponse>(`/media/upload`, formData)
  },
  
  // 获取客户详情
  getCustomerDetail: (id: number) => {
    return http.get<IApiResponse>(`/reservation/customers/${id}`)
  },
  
  // 获取客户预约历史
  getCustomerAppointments: (id: number, params?: any) => {
    return http.get<IApiResponse>(`/reservation/customers/${id}/appointments`, params)
  },
  
  // 更新客户信息
  updateCustomer: (id: number, data: any) => {
    return http.put<IApiResponse>(`/reservation/customers/${id}`, data)
  },
  
  // 获取员工列表
  getStaffList: (params: any) => {
    return http.get<IApiResponse>(`/reservation/staff`, params)
  },
  
  // 获取员工详情
  getStaffDetail: (id: number) => {
    return http.get<IApiResponse>(`/reservation/staff/${id}`)
  },
  
  // 创建员工
  createStaff: (data: any) => {
    return http.post<IApiResponse>('/reservation/staff', data)
  },
  
  // 更新员工
  updateStaff: (id: number, data: any) => {
    return http.put<IApiResponse>(`/reservation/staff/${id}`, data)
  },
  
  // 删除员工
  deleteStaff: (staff_ids: any) => {
    return http.delete<IApiResponse>(`/reservation/staff`, { staff_ids })
  },
  
  // 导入员工
  importStaff: (formData: FormData) => {
    return http.post<IApiResponse>('/reservation/staff/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  // 获取邮件模板列表
  getEmailTemplateList: (params?: any) => {
    return http.get<IApiResponse>(`/reservation/email-templates`, params)
  },
  
  // 获取邮件模板详情
  getEmailTemplateDetail: (id: number) => {
    return http.get<IApiResponse>(`/reservation/email-templates/${id}`)
  },
  
  // 创建邮件模板
  createEmailTemplate: (data: any) => {
    return http.post<IApiResponse>(`/reservation/email-templates`, data)
  },
  
  // 更新邮件模板
  updateEmailTemplate: (id: number, data: any) => {
    return http.put<IApiResponse>(`/reservation/email-templates/${id}`, data)
  },
  
  // 删除邮件模板
  deleteEmailTemplate: (id: number) => {
    return http.delete<IApiResponse>(`/reservation/email-templates/${id}`)
  },
  
  // 更新邮件模板状态
  updateEmailTemplateStatus: (id: number) => {
    return http.post<IApiResponse>(`/reservation/email-templates/${id}/switch`)
  },
  
  // 获取折扣列表
  getDiscountList: (params?: any) => {
    return http.get<IApiResponse>(`/reservation/discounts`, params)
  },
  
  // 获取预约状态列表
  getAppointmentStatuses: () => {
    return http.get<IApiResponse>(`/reservation/appointments/statuses`)
  },
  
  // 更新预约状态
  updateAppointmentStatus: (id: number, status: number) => {
    return http.put<IApiResponse>(`/reservation/appointments/${id}/status`, { status })
  },
  
  // 批量删除预约
  batchDeleteAppointments: (ids: number[]) => {
    return http.delete<IApiResponse>(`/reservation/appointments`, { ids })
  },
  
  // 批量开启服务
  openAllServices: () => {
    return http.post<IApiResponse>(`/reservation/services/open-all`)
  },
  
  // 批量停用服务
  closeAllServices: () => {
    return http.post<IApiResponse>(`/reservation/services/close-all`)
  },
  
  // 获取部门列表
  getDepartmentList: (params?: any) => {
    return http.get(`/reservation/departments`, params)
  },
  
  // 获取职位列表
  getPositionList: (params?: any) => {
    return http.get(`/reservation/positions`, params)
  },
  
  // 获取通知设置
  getNotificationSettings: () => {
    return http.get(`/reservation/notification-settings`)
  },

} 