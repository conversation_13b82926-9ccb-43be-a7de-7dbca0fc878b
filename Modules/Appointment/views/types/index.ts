// API 响应接口
export interface IApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应接口
export interface IPaginationResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// 预约面板接口
export interface IPanel {
  id: number
  name: string
  type: number
  description: string
  service_count: number
  status: number
  status_text: string
  created_at: string
  updated_at: string
}

// 预约记录接口
export interface IAppointment {
  id: number
  panel_id: number
  panel_name: string
  customer_id: number
  customer_name: string
  customer_phone: string
  service_id: number
  service_name: string
  staff_id: number
  staff_name: string
  appointment_time: string
  duration: number
  status: number
  status_text: string
  notes: string
  created_at: string
  updated_at: string
}

// 服务项目接口
export interface IService {
  id: number
  name: string
  panel_id: number
  panel_name: string
  duration: number
  price: number
  description: string
  status: number
  status_text: string
  created_at: string
  updated_at: string
}

// 搜索表单接口
export interface ISearchForm {
  keyword?: string
  panel_id?: number
  service_id?: number
  status?: number
  date_range?: [string, string]
}

// 预约设置接口
export interface IAppointmentSettings {
  booking_enabled: number
  advance_booking_days: number
  min_notice_hours: number
  max_appointments_per_day: number
  reminder_enabled: number
  reminder_hours: number
  cancellation_policy: number
  cancellation_hours: number
}

// 可用时间段接口
export interface ITimeSlot {
  start_time: string
  end_time: string
  is_available: boolean
  unavailable_reason?: string
}

// 付款记录接口
export interface IPayment {
  id: number
  appointment_id: number
  appointment_time: string
  customer_name: string
  customer_email: string
  customer_avatar: string
  staff_name: string
  location: string
  service_name: string
  payment_method: number
  service_price: number
  discount: number
  vat: number
  total: number
  paid: number
  due: number
  payment_status: number
  created_at: string
  updated_at: string
} 