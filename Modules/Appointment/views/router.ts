import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
    {
        path: '/appointment',
        component: () => import('/admin/layout/index.vue'), // 请确保路径正确
        meta: { title: 'Appointment.router.appointment', icon: 'calendar' },
        children: [
            {
                path: 'dashboard',
                name: 'AppointmentDashboard',
                meta: { title: 'Appointment.router.dashboard' },
                component: () => import('./ui/dashboard/Dashboard.vue'),
            },
            {
                path: 'calendar',
                name: 'AppointmentCalendar',
                meta: { title: 'Appointment.router.calendar' },
                component: () => import('./ui/dashboard/calendarPage.vue'),
            },
            {
                path: 'appointments',
                name: 'AppointmentPanel',
                meta: { title: 'Appointment.router.appointments' },
                component: () => import('./ui/panel/panelList.vue'),
            },
            {
                path: 'panel-create',
                name: 'AppointmentCreate',
                meta: { title: 'Appointment.router.panel_create' },
                component: () => import('./ui/panel/paneCreate.vue'),
            },
            {
                path: ':id/edit',
                name: 'AppointmentEdit',
                meta: { title: 'Appointment.router.edit' },
                component: () => import('./ui/panel/paneEdit.vue'),
            },
            {
                path: 'payments',
                name: 'AppointmentPayments',
                meta: { title: 'Appointment.router.payments' },
                component: () => import('./ui/payMents/payList.vue'),
            },
            {
                path: 'customers',
                name: 'AppointmentCustomers',
                meta: { title: 'Appointment.router.customers' },
                component: () => import('./ui/customers/customerList.vue'),
            },
            {
                path: 'customers/create',
                name: 'CustomerCreate',
                meta: { title: 'Appointment.router.customer_create' },
                component: () => import('./ui/customers/addCreate.vue'),
            },
            {
                path: 'customers/:id/edit',
                name: 'CustomerEdit',
                meta: { title: 'Appointment.router.customer_edit' },
                component: () => import('./ui/customers/addCreate.vue'),
            },
            {
                path: 'customers/:id/detail',
                name: 'CustomerDetail',
                meta: { title: 'Appointment.router.customer_detail' },
                component: () => import('./ui/customers/customerDetail.vue'),
            },
            {
                path: 'services',
                name: 'AppointmentServices',
                meta: { title: 'Appointment.router.services' },
                component: () => import('./ui/services/serviceList.vue'),
            },
            {
                path: 'services/create',
                name: 'ServiceCreate',
                meta: { title: 'Appointment.router.service_create' },
                component: () => import('./ui/services/serviceCreate.vue'),
            },
            {
                path: 'services/:id/edit',
                name: 'ServiceEdit',
                meta: { title: 'Appointment.router.service_edit' },
                component: () => import('./ui/services/serviceCreate.vue'),
            },
            {
                path: 'staff',
                name: 'AppointmentStaff',
                meta: { title: 'Appointment.router.staff' },
                component: () => import('./ui/staffManage/staffList.vue'),
            },
            {
                path: 'staff/create',
                name: 'StaffCreate',
                meta: { title: 'Appointment.router.staff_create' },
                component: () => import('./ui/staffManage/staffAdd.vue'),
            },
            {
                path: 'staff/:id/edit',
                name: 'StaffEdit',
                meta: { title: 'Appointment.router.staff_edit' },
                component: () => import('./ui/staffManage/staffAdd.vue'),
            },
            {
                path: 'settings',
                name: 'AppointmentSettings',
                meta: { title: 'Appointment.router.settings' },
                component: () => import('./ui/settings/setList.vue'),
            },
            {
                path: 'email-templates',
                name: 'EmailTemplates',
                meta: { title: 'Appointment.router.email_templates' },
                component: () => import('./ui/emailManage/emailList.vue'),
            },
            {
                path: 'email-templates/create',
                name: 'EmailTemplateCreate',
                meta: { title: 'Appointment.router.email_template_create' },
                component: () => import('./ui/emailManage/emailCreate.vue'),
            },
            {
                path: 'email-templates/:id/edit',
                name: 'EmailTemplateEdit',
                meta: { title: 'Appointment.router.email_template_edit' },
                component: () => import('./ui/emailManage/emailCreate.vue'),
            }
        ]
    }
]

export default router
