<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
      <div class="btn-list">
        
        <el-button class="button-no-border" @click="handleImport">
          <el-icon><img :src="$asset('Cms/Asset/UploadIcon.png')" alt="" /></el-icon>
          <span>{{ t('Appointment.PanelList.buttons.import') }}</span>
        </el-button>
        <el-button class="button-no-border" @click="handleExport">
          <el-icon><img :src="$asset('Cms/Asset/DownloadIcon.png')" alt="" /></el-icon>
          <span>{{ t('Appointment.PanelList.buttons.export') }}</span>
        </el-button>
        <el-button plain @click="handleDelete" :disabled="!hasSelected">
          <span>{{ t('Appointment.PanelList.search.delete') }}</span>
        </el-button>
        <FilterPopover v-model="showFilterDropdown">
          <template #reference>
            <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
              <el-icon>
                <img :src="$asset('Cms/Asset/FilterIcon.png')" alt="" />
              </el-icon>
              <span>{{ $t('Cms.list.filter') }}</span>
            </el-button>
          </template>
          <el-form :inline="true" :model="searchForm" label-position="top" style="width: 100%;">
            <el-form-item :label="t('Cms.list.filter')" style="width: 100%;">
              <el-input 
                v-model="searchForm.keyword"
                :placeholder="t('Appointment.PanelList.search.placeholder')"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="flex justify-center">
              <el-button class="el-button-default" @click="handleReset">
                <el-icon><Refresh /></el-icon>
                <span>{{ $t('Cms.list.refresh') }}</span>
              </el-button>
              <el-button class="button-no-border" type="primary" @click="handleSearch">
                <el-icon><Filter /></el-icon>
                <span>{{ $t('Cms.list.filter') }}</span>
              </el-button>
            </div>
          </template>
        </FilterPopover>
        <el-button type="primary" @click="handleCreate">
          <el-icon><img :src="$asset('Cms/Asset/PlusIcon.png')" alt="" /></el-icon>
          <span>{{ t('Appointment.PanelList.buttons.create') }}</span>
        </el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 标签切换 -->
        <div class="tab-container">
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane :label="t('Appointment.PanelList.tabs.my')" name="my">
              <!-- 我的预约表格 -->
              <el-table 
                v-loading="loading"
                :data="activeTab === 'my' ? myTableData : allTableData"
                style="width: 100%; height: 100%;"
                @selection-change="handleSelectionChange"
              >
                <template #empty>
                    <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
                </template>
                <el-table-column type="selection" width="55" />
                <el-table-column prop="appointment_date" :label="t('Appointment.PanelList.table.appointmentTime')" width="150">
                  <template #default="{ row }">
                    {{ formatTime(row.appointment_date) }}
                  </template>
                </el-table-column>
                <el-table-column :label="t('Appointment.PanelList.table.customer')" width="200">
                  <template #default="{ row }">
                    <div class="customer-info">
                      <img class="info-avatar" 
                        :src="row.customer?.photo || avatarUrl" 
                        @error="handleAvatarError"
                      />
                      <div class="customer-detail">
                        <div>{{ row.customer?.name }}</div>
                        <div class="customer-email">{{ row.customer?.email }}</div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="staff_name" :label="t('Appointment.PanelList.table.staffName')" width="150">
                  <template #default="{ row }">
                    <el-tooltip :content="row.service?.staffs.map(staff => staff?.name).join(', ')" placement="top">
                      <span>{{ row.service?.staffs.map(staff => staff?.name).join(', ') }}</span>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="service.name" :label="t('Appointment.PanelList.table.service')" min-width="150">
                  <template #default="{ row }">
                    {{ row.service?.name }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" :label="t('Appointment.PanelList.table.status')" width="150">
                  <template #default="{ row }">
                    <el-tag :type="getStatusType(row.status)" closable @close="handleStatusChange(row)">
                      {{ row.status_text }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="duration" :label="t('Appointment.PanelList.table.duration')" width="150">
                  <template #default="{ row }">
                    {{ formatDuration(row.duration) }}
                  </template>
                </el-table-column>
                <el-table-column prop="created_at" :label="t('Appointment.PanelList.table.createTime')" width="150">
                  <template #default="{ row }">
                    {{ formatTime(row.created_at) }}
                  </template>
                </el-table-column>
                <el-table-column :label="t('Appointment.PanelList.table.operations')" width="100" fixed="right">
                  <template #default="{ row }">
                    <div class="bwms-operate-btn-box">
                      <div class="bwms-operate-btn"
                        @click="handleEdit(row)"
                      >
                        <el-icon size="15"><img :src="$asset('Cms/Asset/EditIcon.png')" alt="" /></el-icon>
                      </div>
                      <div class="bwms-operate-btn"
                        @click="handleDeleteOne(row)"
                      >
                        <el-icon size="16"><img :src="$asset('Cms/Asset/DeleteIcon.png')" alt="" /></el-icon>
                      </div>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane :label="t('Appointment.PanelList.tabs.all')" name="all">
              <!-- 所有预约表格 -->
              <el-table 
                v-loading="loading"
                :data="activeTab === 'all' ? allTableData : myTableData"
                style="width: 100%; height: 100%;"
                @selection-change="handleSelectionChange"
              >
                <template #empty>
                    <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
                </template>
                <el-table-column type="selection" width="55" />
                <el-table-column prop="appointment_date" :label="t('Appointment.PanelList.table.appointmentTime')" width="150">
                  <template #default="{ row }">
                    {{ formatTime(row.appointment_date) }}
                  </template>
                </el-table-column>
                <el-table-column :label="t('Appointment.PanelList.table.customer')" width="200">
                  <template #default="{ row }">
                    <div class="customer-info">
                      <img class="info-avatar" 
                        :src="row.customer?.photo || avatarUrl" 
                        @error="handleAvatarError"
                      />
                      <div class="customer-detail">
                        <div>{{ row.customer?.name }}</div>
                        <div class="customer-email">{{ row.customer?.email }}</div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="staff_name" :label="t('Appointment.PanelList.table.staffName')" width="150">
                  <template #default="{ row }">
                    <el-tooltip :content="row.service?.staffs.map(staff => staff?.name).join(', ')" placement="top">
                      <span>{{ row.service?.staffs.map(staff => staff?.name).join(', ') }}</span>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="service.name" :label="t('Appointment.PanelList.table.service')" min-width="150">
                  <template #default="{ row }">
                    {{ row.service?.name }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" :label="t('Appointment.PanelList.table.status')" width="150">
                  <template #default="{ row }">
                    <el-tag :type="getStatusType(row.status)" closable @close="handleStatusChange(row)">
                      {{ row.status_text }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="duration" :label="t('Appointment.PanelList.table.duration')" width="150">
                  <template #default="{ row }">
                    {{ formatDuration(row.duration) }}
                  </template>
                </el-table-column>
                <el-table-column prop="created_at" :label="t('Appointment.PanelList.table.createTime')" width="150">
                  <template #default="{ row }">
                    {{ formatTime(row.created_at) }}
                  </template>
                </el-table-column>
                <el-table-column :label="t('Appointment.PanelList.table.operations')" width="100" fixed="right">
                  <template #default="{ row }">
                    <div class="bwms-operate-btn-box">
                      <div class="bwms-operate-btn"
                        @click="handleEdit(row)"
                      >
                        <el-icon size="15"><img :src="$asset('Cms/Asset/EditIcon.png')" alt="" /></el-icon>
                      </div>
                      <div class="bwms-operate-btn"
                        @click="handleDeleteOne(row)"
                      >
                        <el-icon size="16"><img :src="$asset('Cms/Asset/DeleteIcon.png')" alt="" /></el-icon>
                      </div>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <!-- 分页移到这里 -->
      <div class="box-footer">
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="currentPagination.pageSize"
              class="page-size-select"
              @change="handleSizeChange"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ t('Cms.pagination.total_items', { total: currentPagination.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="currentPagination.page"
              background
              layout="prev, pager, next"
              :page-size="currentPagination.pageSize"
              :total="currentPagination.total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 状态修改框 -->
    <el-dialog
      v-model="statusDialogVisible"
      :title="t('Appointment.PanelList.dialog.status.title')"
      width="400px"
      class="el-dialog-common-cls"
    >
      <el-form :model="statusForm" label-position="top">
        <el-form-item :label="t('Appointment.PanelList.dialog.status.label')">
          <el-select 
            style="width: 260px;" 
            v-model="statusForm.status" 
            :placeholder="t('Appointment.PanelList.dialog.status.placeholder')" 
            v-loading="statusLoading"
          >
            <el-option 
              v-for="item in statusOptions" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-center">
          <el-button @click="statusDialogVisible = false">{{ t('Appointment.PanelList.cancel') }}</el-button>
          <el-button 
            type="primary" 
            @click="confirmChangeStatus" 
            :loading="confirmStatusLoading"
          >
            {{ t('Appointment.PanelList.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      :title="t('Appointment.PanelList.dialog.import.title')"
      width="500px"
      class="el-dialog-common-cls"
    >
      <div class="import-container">
        <el-upload
          class="upload-demo"
          drag
          action="null"
          :auto-upload="false"
          :on-change="handleFileChange"
          :limit="1"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            {{ t('Appointment.PanelList.dialog.import.uploadTip') }}
          </div>
          <template #tip>
            <div class="el-upload__tip">
              {{ t('Appointment.PanelList.dialog.import.fileTypeTip') }}
            </div>
          </template>
        </el-upload>
        
        <div class="template-download">
          <el-button type="primary" link @click="downloadTemplate">
            {{ t('Appointment.PanelList.dialog.import.downloadTemplate') }}
          </el-button>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-center">
          <el-button @click="importDialogVisible = false">{{ t('Appointment.PanelList.cancel') }}</el-button>
          <el-button 
            type="primary" 
            @click="handleUpload"
            :loading="uploading"
            :disabled="!selectedFile"
          >
            {{ t('Appointment.PanelList.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { env, getBaseUrl } from '/admin/support/helper'
import { Plus, Search, Edit, Delete, More, Upload, Download, UploadFilled, Filter, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { useI18n } from 'vue-i18n'
import dayjs from 'dayjs'
import { appointmentService } from '../../services/appointmentService'
import type { IAppointment, ISearchForm, UploadFile } from '../../types'

// 初始化i18n
const { t } = useI18n()

// 添加筛选下拉框状态
const showFilterDropdown = ref(false)

// 定义状态选项接口
interface StatusOption {
  value: number
  label: string
}

// 定义预约状态枚举
enum AppointmentStatus {
  PENDING = 1,   // 待确认
  CONFIRMED = 2, // 已确认
  CANCELLED = 3, // 已取消
  COMPLETED = 4, // 已完成
  RESCHEDULED = 5, // 已改期
  REJECTED = 6,  // 已拒绝
  NO_SHOW = 7    // 未到场
}

// 路由
const router = useRouter()
const route = useRoute()

const baseURL = getBaseUrl()

// 加载状态
const loading = ref(false)

// 状态修改对话框
const statusDialogVisible = ref(false)
const statusForm = reactive({
  status: 1
})
const currentRow = ref<IAppointment | null>(null)
const statusLoading = ref(false)
const statusOptions = ref<StatusOption[]>([])
const confirmStatusLoading = ref(false)

// 导入对话框
const importDialogVisible = ref(false)
const importLoading = ref(false)

// 搜索表单
const searchForm = reactive<ISearchForm>({
  keyword: '',
  status: undefined,
  date_range: undefined
})

// 表格数据
const myTableData = ref<IAppointment[]>([])
const allTableData = ref<IAppointment[]>([])

// 分页信息
const myPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

const allPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 获取当前标签页的数据和分页
const currentTableData = computed(() => activeTab.value === 'my' ? myTableData.value : allTableData.value)
const currentPagination = computed(() => activeTab.value === 'my' ? myPagination : allPagination)

// 选中的行
const selectedRows = ref<IAppointment[]>([])
const hasSelected = ref(false)

// 标签页
const activeTab = ref('my')

// 添加选中文件变量
const selectedFile = ref<UploadFile>()
const uploading = ref(false)

// 获取状态标签类型
const getStatusType = (status: number) => {
  const types: Record<number, string> = {
    [AppointmentStatus.PENDING]: 'info',
    [AppointmentStatus.CONFIRMED]: 'success',
    [AppointmentStatus.CANCELLED]: 'danger',
    [AppointmentStatus.COMPLETED]: 'success',
    [AppointmentStatus.RESCHEDULED]: 'warning',
    [AppointmentStatus.REJECTED]: 'danger',
    [AppointmentStatus.NO_SHOW]: 'info'
  }
  return types[status] || ''
}

// 格式化时间
const formatTime = (datetime: string | null) => {
  if (!datetime) return '--'
  return dayjs(datetime).format('YYYY-MM-DD HH:mm')
}

// 格式化时长
const formatDuration = (seconds: number) => {
  if (!seconds) return `0 ${t('Appointment.Common.duration.second')}`
  
  // 计算小时和分钟
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return minutes > 0 
      ? `${hours} ${t('Appointment.Common.duration.hour')} ${minutes} ${t('Appointment.Common.duration.minute')}`
      : `${hours} ${t('Appointment.Common.duration.hour')}`
  }
  
  if (minutes > 0) {
    return `${minutes} ${t('Appointment.Common.duration.minute')}`
  }
  
  return `${seconds} ${t('Appointment.Common.duration.second')}`
}

// 处理标签页切换
const handleTabClick = (val: any) => {
  // 重置当前标签页的分页和搜索条件
  const { name } = val.props;
  activeTab.value = name;

  currentPagination.value.page = 1
  fetchData()
  
  // 将当前tab添加到路由查询参数中
  router.replace({
    query: {
      ...route.query,
      tab: activeTab.value
    }
  })
}

// 处理选择变化
const handleSelectionChange = (rows: IAppointment[]) => {
  selectedRows.value = rows
  hasSelected.value = rows.length > 0
}

// 查询
const handleSearch = () => {
  currentPagination.value.page = 1
  showFilterDropdown.value = false
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = undefined
  searchForm.date_range = undefined
  showFilterDropdown.value = false
  handleSearch()
}

// 创建预约
const handleCreate = () => {
  router.push({
    path: '/appointment/panel-create',
    query: { tab: activeTab.value }
  })
}

// 编辑预约
const handleEdit = (row: IAppointment) => {
  router.push({
    path: `/appointment/${row.id}/edit`,
    query: { tab: activeTab.value }
  })
}

// 删除服务
const handleDeleteOne = (row: IAppointment) => {
  ElMessageBox.confirm(
    t('Appointment.PanelList.messages.deleteConfirm'),
    t('Appointment.PanelList.messages.deleteTitle'),
    {
      confirmButtonText: t('Appointment.PanelList.confirm'),
      cancelButtonText: t('Appointment.PanelList.cancel'),
      type: 'warning'
    }
  ).then(async () => {
    try {
      await appointmentService.deleteAppointment(row.id)
      ElMessage.success(t('Appointment.PanelList.messages.deleteSuccess'))
      fetchData()
    } catch (error) {
      ElMessage.error(t('Appointment.PanelList.messages.deleteFailed'))
    }
  })
}

// 删除预约
const handleDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning(t('Appointment.PanelList.messages.selectDelete'))
    return
  }

  ElMessageBox.confirm(
    t('Appointment.PanelList.messages.batchDeleteConfirm', { count: selectedRows.value.length }),
    t('Appointment.PanelList.messages.batchDeleteTitle'),
    {
      confirmButtonText: t('Appointment.PanelList.confirm'),
      cancelButtonText: t('Appointment.PanelList.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    try {
      const ids = selectedRows.value.map(row => row.id)
      await appointmentService.batchDeleteAppointments(ids)
      ElMessage.success(t('Appointment.PanelList.messages.deleteSuccess'))
      fetchData()
    } catch (error) {
    }
  })
}

// 处理导入
const handleImport = () => {
  importDialogVisible.value = true
  selectedFile.value = undefined
}

// 下载导入模板
const downloadTemplate = () => {
  try {
    const link = document.createElement('a')
    link.href = `${baseURL}reservation/appointments/export-template`
    link.download = '预约导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    ElMessage.error(t('Appointment.PanelList.messages.downloadFailed'))
  }
}

// 文件选择改变
const handleFileChange = (uploadFile: UploadFile) => {
  selectedFile.value = uploadFile
}

// 处理上传按钮点击
const handleUpload = async () => {
  if (!selectedFile.value?.raw) {
    ElMessage.warning(t('Appointment.PanelList.messages.selectFile'))
    return
  }

  uploading.value = true
  try {
    const formData = new FormData()
    formData.append('file', selectedFile.value.raw)
    
    const { data } = await appointmentService.importAppointments(formData)
    
    if (data.code === 200) {
      ElMessage.success(t('Appointment.PanelList.messages.importSuccess'))
      importDialogVisible.value = false
      fetchData()
    } else {
      ElMessage.error(data.message || t('Appointment.PanelList.messages.importFailed'))
    }
  } catch (error) {
  } finally {
    uploading.value = false
  }
}

// 处理导出
const handleExport = async () => {
  try {
    loading.value = true
    const exportUrl = `${baseURL}reservation/appointments/export?page=${currentPagination.value.page}&limit=${currentPagination.value.pageSize}`
    const link = document.createElement('a')
    link.href = exportUrl
    link.setAttribute('download', `预约记录_${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success(t('Appointment.PanelList.messages.exportSuccess'))
  } catch (error) {
    ElMessage.error(t('Appointment.PanelList.messages.exportFailed'))
  } finally {
    loading.value = false
  }
}

// 获取状态选项
const fetchStatusOptions = async () => {
  statusLoading.value = true
  try {
    const { data } = await appointmentService.getAppointmentStatuses()
    
    if (data.code === 200) {
      statusOptions.value = data.data.map((item: any) => ({
        value: item.value,
        label: item.label
      }))
    }
  } catch (error) {
  } finally {
    statusLoading.value = false
  }
}

// 状态对话框打开时获取状态选项
const handleStatusChange = (row: IAppointment) => {
  currentRow.value = row
  statusForm.status = row.status
  statusDialogVisible.value = true
}

// 确认修改状态
const confirmChangeStatus = async () => {
  if (!currentRow.value) return
  
  try {
    confirmStatusLoading.value = true
    const { data } = await appointmentService.updateAppointmentStatus(currentRow.value.id, statusForm.status)
    if (data.code === 200) {
      ElMessage.success(t('Appointment.PanelList.messages.statusSuccess'))
      statusDialogVisible.value = false
      fetchData()
    } else {
      ElMessage.error(data.message || t('Appointment.PanelList.messages.statusFailed'))
    }
  } catch (error) {
  } finally {
    confirmStatusLoading.value = false
  }
}

// 改变每页条数
const handleSizeChange = (val: number) => {
  if (activeTab.value === 'my') {
    myPagination.pageSize = val
  } else {
    allPagination.pageSize = val
  }
  fetchData()
}

// 改变页码
const handleCurrentChange = (val: number) => {
  if (activeTab.value === 'my') {
    myPagination.page = val
  } else {
    allPagination.page = val
  }
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPagination.value.page,
      limit: currentPagination.value.pageSize,
      keyword: searchForm.keyword,
      type: activeTab.value === 'my' ? 'my' : 'all',
      start_date: searchForm.date_range?.[0],
      end_date: searchForm.date_range?.[1]
    }
    
    const { data } = await appointmentService.getAppointmentList(params)
    
    if (data.code === 200 && data.data) {
      if (activeTab.value === 'my') {
        myTableData.value = data.data.items || []
        myPagination.total = data.data.total || 0
      } else {
        allTableData.value = data.data.items || []
        allPagination.total = data.data.total || 0
      }
    } else {
      if (activeTab.value === 'my') {
        myTableData.value = []
        myPagination.total = 0
      } else {
        allTableData.value = []
        allPagination.total = 0
      }
      ElMessage.warning(data.message || t('Appointment.PanelList.messages.getListFailed'))
    }
  } catch (error) {
    if (activeTab.value === 'my') {
      myTableData.value = []
      myPagination.total = 0
    } else {
      allTableData.value = []
      allPagination.total = 0
    }
  } finally {
    loading.value = false
  }
}

// 添加头像加载错误处理函数
const handleAvatarError = (e: Event) => {
  const imgElement = e.target as HTMLImageElement
  imgElement.src = avatarUrl.value
}

// 初始化
onMounted(() => {
  // 从路由参数中获取tab，如果存在就设置
  if (route.query.tab) {
    activeTab.value = route.query.tab as string
  }
  
  fetchData()
  // 获取状态选项
  fetchStatusOptions()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .module-header {
    flex-shrink: 0;
  }
  
  .module-con {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .box {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      padding-top: 0;

      .tab-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        :deep(.el-tabs) {
          height: 100%;
          display: flex;
          overflow: hidden;

          .el-tabs__content {
            flex: 1;
            overflow: hidden;
            
            .el-tab-pane {
              height: 100%;
              overflow: hidden;
            }
          }
        }

        .el-table {
          height: 100% !important;
          overflow: auto;

          .customer-info {
            display: flex;
            align-items: center;
            .info-avatar {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              object-fit: cover;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
              background-color: #f5f7fa;
            }
            .customer-detail {
              margin-left: 10px;
              
              .customer-email {
                font-size: 12px;
                color: #999;
                margin-top: 3px;
              }
            }
          }
        }
      }
    }

    .box-footer {
      flex-shrink: 0;
    }
  }
}

.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
}

// 导入容器样式
.import-container {
  padding: 10px;
  
  .template-download {
    margin-top: 15px;
  }
}
</style>
