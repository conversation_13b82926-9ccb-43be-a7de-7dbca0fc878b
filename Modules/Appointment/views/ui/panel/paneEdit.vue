<template>
  <div class="table-page bwms-module">
    <div class="module-header">
    </div>
    <div class="module-con">
      <div class="box">
        <el-form 
          ref="appointmentFormRef"
          :model="appointmentForm"
          :rules="appointmentRules"
          v-loading="loading"
          label-position="top"
        >
          <!-- 预约地点和服务类型 -->
          <div class="section">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item 
                  :label="t('Appointment.PanelList.table.location')" 
                  prop="location"
                  required
                >
                  <el-input 
                    v-model="appointmentForm.location"
                    type="textarea"
                    :placeholder="t('Appointment.PanelList.search.placeholder')"
                    :rows="2"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item 
                  :label="t('Appointment.PanelList.table.service')" 
                  prop="service_type"
                  required
                >
                  <el-select
                    v-model="appointmentForm.service_type"
                    :placeholder="t('Appointment.PanelList.search.selectPlaceholder')"
                    style="width: 100%"
                    clearable
                    filterable
                    :loading="serviceLoading"
                  >
                    <el-option
                      v-for="service in serviceTypeOptions"
                      :key="service.id"
                      :label="service.name"
                      :value="service.id"
                    >
                      <div class="service-option">
                        <span>{{ service.name }}</span>
                      </div>
                    </el-option>
                    <template #empty>
                      <div style="text-align: center; padding: 8px 0;">
                        {{ t('Cms.list.no_data') }}
                      </div>
                    </template>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 预约日期和时间 -->
          <div class="section">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item 
                  :label="t('Appointment.PanelList.table.appointmentDate')" 
                  prop="appointment_date"
                >
                  <el-date-picker 
                    v-model="appointmentForm.appointment_date"
                    type="date"
                    :placeholder="t('Appointment.PanelList.search.selectPlaceholder')"
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item 
                  :label="t('Appointment.PanelList.table.appointmentTime')" 
                  prop="appointment_time"
                >
                  <el-time-picker 
                    v-model="appointmentForm.appointment_time"
                    :placeholder="t('Appointment.PanelList.search.selectPlaceholder')"
                    style="width: 100%"
                    format="HH:mm"
                    value-format="HH:mm"
                    :disabled-hours="disabledHours"
                    :disabled-minutes="disabledMinutes"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 客户信息和备注 -->
          <div class="section">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item 
                  :label="t('Appointment.CustomerDetail.profile.description')" 
                  prop="remark"
                >
                  <el-input
                    v-model="appointmentForm.remark"
                    type="textarea"
                    :placeholder="t('Appointment.PanelList.search.placeholder')"
                    :rows="4"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <div v-for="(item, index) in customerList" :key="item.id" class="customer-item">
                  <el-form-item 
                    :label="t('Appointment.PanelList.table.customer')"
                    prop="customers"
                  >
                    <div class="customer-select-wrapper">
                      <el-select
                        v-model="appointmentForm.customers"
                        filterable
                        :placeholder="t('Appointment.PanelList.search.selectPlaceholder')"
                        style="width: 100%"
                        :loading="item.loading"
                        clearable
                      >
                        <el-option
                          v-for="customer in customerOptions"
                          :key="customer.id"
                          :label="customer.name"
                          :value="customer.id"
                        >
                          <div class="customer-option">
                            <span>{{ customer.name }}</span>
                          </div>
                        </el-option>
                        <template #empty>
                          <div style="text-align: center; padding: 8px 0;">
                            {{ t('Cms.list.no_data') }}
                          </div>
                        </template>
                      </el-select>
                    </div>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 应用折扣 -->
          <div class="section">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="t('Appointment.PanelList.table.discount')">
                  <el-switch
                    v-model="appointmentForm.apply_discount"
                    :active-value="true"
                    :inactive-value="false"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="appointmentForm.apply_discount">
                <el-form-item 
                  :label="t('Appointment.PanelList.table.discountCode')" 
                  prop="discount_code"
                >
                  <el-select
                    v-model="appointmentForm.discount_code"
                    :placeholder="t('Appointment.PanelList.search.selectPlaceholder')"
                    style="width: 100%"
                    filterable
                    :loading="discountLoading"
                    clearable
                  >
                    <el-option
                      v-for="discount in discountOptions"
                      :key="discount.id"
                      :label="`${discount.name}`"
                      :value="discount.id"
                    />
                    <template #empty>
                      <div style="text-align: center; padding: 8px 0;">
                        {{ t('Cms.list.no_data') }}
                      </div>
                    </template>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          
        </el-form>
      </div>
    </div>
    <!-- 按钮组 -->
    <div class="flex justify-center" style="margin-top: 26px;">
      <el-button class="button-cancel" @click="handleCancel">{{ t('Appointment.PanelList.cancel') }}</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">{{ t('Appointment.PanelList.confirm') }}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type { FormInstance } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { appointmentService } from '../../services/appointmentService'

// i18n
const { t } = useI18n()

// 路由
const router = useRouter()
const route = useRoute()

// 表单引用
const appointmentFormRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)
const submitLoading = ref(false)
const customerLoading = ref(false)

// 折扣状态变量
const discountLoading = ref(false)
const discountSearchQuery = ref('')
const isDiscountSelectOpen = ref(false)

// 客户状态变量
const isCustomerSelectOpen = ref<boolean[]>([false])
const customerSearchQuery = ref('')

// 客户选项
const customerList = ref<Array<{
  id: string | number,
  name: string | null,
  loading: boolean
}>>([
  { id: 'customer-0', name: null, loading: false }
])

// 客户选项数据
const customerOptions = ref<Array<{
  id: number | string,
  name: string,
  email?: string,
  phone?: string
}>>([])

// 服务类型选项
const serviceTypeOptions = ref<Array<{
  id: number,
  name: string,
  price: string,
  duration: number,
  description: string
}>>([])

// 折扣选项
const discountOptions = ref<Array<{
  id: number,
  name: string,
  type: string,
  value: string,
}>>([])

// 添加服务类型相关的状态变量
const serviceLoading = ref(false)
const serviceSearchQuery = ref('')
const isServiceSelectOpen = ref(false)

// 添加预约ID参数以及状态字段
const appointmentId = ref<number | null>(null)

// 表单数据
const appointmentForm = reactive({
  location: '',
  service_type: null as number | null,
  appointment_date: '',
  appointment_time: '',
  customers: null as number | null,
  remark: '',
  apply_discount: false,
  discount_code: '',
  status: 0
})

// 表单验证规则
const appointmentRules = computed(() => ({
  location: [
    { required: true, message: t('Appointment.PanelList.search.placeholder'), trigger: 'blur' }
  ],
  service_type: [
    { required: true, message: t('Appointment.PanelList.search.placeholder'), trigger: 'change' }
  ],
  appointment_date: [
    { required: true, message: t('Appointment.PanelList.search.placeholder'), trigger: 'change' }
  ],
  appointment_time: [
    { required: true, message: t('Appointment.PanelList.search.placeholder'), trigger: 'change' }
  ],
  customers: [
    { required: true, message: t('Appointment.PanelList.search.placeholder'), trigger: 'change' }
  ]
}))

// 获取客户列表
const fetchCustomers = async (keyword = '') => {
  try {
    const params: any = {
      page: 1,
      limit: 100,
    }
    
    // 添加搜索关键词
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim()
    }
    
    const response = await appointmentService.getCustomerList(params)
    if (response.data.code === 200) {
      customerOptions.value = response.data.data.items.map((item: any) => ({
        id: item.id,
        name: item.name,
        email: item.email,
        phone: item.phone
      }))
    }
  } catch (error) {
    ElMessage.error(t('Appointment.CustomerList.getListFailed'))
  }
}

// 处理客户下拉框可见性变化
const handleCustomerVisibleChange = (visible: boolean, index: number) => {
  // 确保数组有足够的元素
  while (isCustomerSelectOpen.value.length <= index) {
    isCustomerSelectOpen.value.push(false)
  }
  
  if (visible) {
    // 下拉框打开时
    isCustomerSelectOpen.value[index] = true
    customerList.value[index].loading = true
    
    // 获取客户列表，如果已有数据就不重新请求
    if (customerOptions.value.length === 0) {
      fetchCustomers().finally(() => {
        customerList.value[index].loading = false
      })
    } else {
      customerList.value[index].loading = false
    }
  } else {
    // 下拉框关闭时，清除搜索关键词并标记关闭状态
    isCustomerSelectOpen.value[index] = false
    
    // 当下拉框关闭时，延迟一会儿再重新请求全量数据
    setTimeout(() => {
      if (!isCustomerSelectOpen.value[index]) {
        fetchCustomers()
      }
    }, 200)
  }
}

// 搜索客户方法
const searchCustomers = (query: string, index: number) => {
  // 确保下拉框已打开
  if (!isCustomerSelectOpen.value[index]) return
  
  // 设置当前操作的客户项的加载状态
  customerList.value[index].loading = true
  
  // 延迟执行搜索，避免频繁请求
  if (query) {
    fetchCustomers(query).finally(() => {
      customerList.value[index].loading = false
    })
  } else {
    fetchCustomers().finally(() => {
      customerList.value[index].loading = false
    })
  }
}


// 取消
const handleCancel = () => {
  const tab = route.query.tab || 'my'
  router.push({
    path: '/appointment/appointments',
    query: { tab }
  })
}

// 提交
const handleSubmit = async () => {
  if (!appointmentFormRef.value) return
  
  await appointmentFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error(t('Appointment.CustomerCreate.messages.formError'))
      return
    }
    
    submitLoading.value = true
    
    try {
      // 获取服务类型ID
      const serviceId = appointmentForm.service_type || 0
      
      // 合并日期和时间
      const appointmentDateTime = appointmentForm.appointment_date && appointmentForm.appointment_time
        ? `${appointmentForm.appointment_date} ${appointmentForm.appointment_time}:00`
        : ''
      
      // 构建提交数据
      const submitData = {
        location: appointmentForm.location,
        service_id: serviceId,
        appointment_date: appointmentDateTime,
        customer_id: appointmentForm.customers || undefined,
        status: appointmentForm.status || 0,
        discount_id: appointmentForm.apply_discount ? Number(appointmentForm.discount_code) : undefined,
        remark: appointmentForm.remark || ''
      }
      
      let response
      
      if (appointmentId.value) {
        // 更新预约
        response = await appointmentService.updateAppointment(appointmentId.value, submitData)
      } else {
        // 创建新预约
        response = await appointmentService.createAppointment(submitData)
      }
      
      if (response.data && response.data.code === 200) {
        ElMessage.success(appointmentId.value ? t('Appointment.CustomerCreate.messages.updateSuccess') : t('Appointment.CustomerCreate.messages.createSuccess'))
        
        // 跳转时保持tab状态
        const tab = route.query.tab || 'my'
        router.push({
          path: '/appointment/appointments',
          query: { tab }
        })
      } else {
        ElMessage.error(response.data?.message || (appointmentId.value ? t('Appointment.CustomerCreate.messages.updateFailed') : t('Appointment.CustomerCreate.messages.createFailed')))
      }
    } catch (error) {
    } finally {
      submitLoading.value = false
    }
  })
}

// 获取折扣列表
const fetchDiscounts = async (keyword = '') => {
  discountLoading.value = true
  try {
    const params: any = {
      page: 1,
      limit: 100,
      keyword: ''
    }
    
    // 添加搜索关键词
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim()
    }
    
    const response = await appointmentService.getDiscountList(params)
    if (response.data.code === 200) {
      discountOptions.value = response.data.data.items.map((item: any) => ({
        id: item.id,
        name: item.name,
        type: item.type,
        value: item.value
      }))
    }
  } catch (error) {
    ElMessage.error(t('Appointment.DiscountList.getListFailed'))
  } finally {
    discountLoading.value = false
  }
}

// 处理折扣下拉框可见性变化
const handleDiscountVisibleChange = (visible: boolean) => {
  if (visible) {
    // 下拉框打开时，如果已有数据就不重新请求
    isDiscountSelectOpen.value = true
    if (discountOptions.value.length === 0) {
      fetchDiscounts()
    }
  } else {
    // 下拉框关闭时，清除搜索关键词并标记关闭状态
    isDiscountSelectOpen.value = false
    discountSearchQuery.value = ''
    
    // 当下拉框关闭时，延迟一会儿再重新请求全量数据
    setTimeout(() => {
      if (!isDiscountSelectOpen.value) {
        fetchDiscounts()
      }
    }, 200)
  }
}

// 远程搜索折扣
const searchDiscounts = (query: string) => {
  if (!isDiscountSelectOpen.value) return
  
  discountSearchQuery.value = query
  // 延迟执行搜索，避免频繁请求
  if (query) {
    fetchDiscounts(query)
  }
}

// 获取服务类型列表
const fetchServices = async (keyword = '') => {
  serviceLoading.value = true
  try {
    const params: any = {
      page: 1,
      limit: 100,
    }
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim()
    }
    
    const response = await appointmentService.getServiceList(params)
    if (response.data.code === 200) {
      serviceTypeOptions.value = response.data.data.items.map((item: any) => ({
        id: item.id,
        name: item.name,
        price: item.price,
        duration: item.duration,
        description: item.description
      }))
    }
  } catch (error) {
    ElMessage.error('获取服务类型列表失败')
  } finally {
    serviceLoading.value = false
  }
}

// 处理服务类型下拉框可见性变化
const handleServiceVisibleChange = (visible: boolean) => {
  if (visible) {
    // 下拉框打开时，如果已有数据就不重新请求
    isServiceSelectOpen.value = true
    if (serviceTypeOptions.value.length === 0) {
      fetchServices()
    }
  } else {
    // 下拉框关闭时，清除搜索关键词并标记关闭状态
    isServiceSelectOpen.value = false
    serviceSearchQuery.value = ''
    
    // 当下拉框关闭时，延迟一会儿再重新请求全量数据
    setTimeout(() => {
      if (!isServiceSelectOpen.value) {
        fetchServices()
      }
    }, 200)
  }
}

// 远程搜索服务类型
const searchServices = (query: string) => {
  if (!isServiceSelectOpen.value) return
  
  serviceSearchQuery.value = query
  // 延迟执行搜索，避免频繁请求
  if (query) {
    fetchServices(query)
  }
}

// 禁用过去的日期
const disabledDate = (time: Date) => {
  // 如果是编辑模式且已有预约日期，则允许选择原日期
  if (appointmentId.value && appointmentForm.appointment_date) {
    const originalDate = new Date(appointmentForm.appointment_date)
    if (
      time.getFullYear() === originalDate.getFullYear() &&
      time.getMonth() === originalDate.getMonth() &&
      time.getDate() === originalDate.getDate()
    ) {
      return false
    }
  }
  
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return time.getTime() < today.getTime()
}

// 禁用过去的小时
const disabledHours = () => {
  const hours: number[] = []
  if (isToday()) {
    // 如果是编辑模式且是原始预约时间，则不禁用原时间
    if (appointmentId.value && isOriginalDateTime()) {
      return hours
    }
    
    const currentHour = new Date().getHours()
    for (let i = 0; i < currentHour; i++) {
      hours.push(i)
    }
  }
  return hours
}

// 禁用过去的分钟
const disabledMinutes = (selectedHour: number) => {
  const minutes: number[] = []
  if (isToday() && selectedHour === new Date().getHours()) {
    // 如果是编辑模式且是原始预约时间，则不禁用原时间
    if (appointmentId.value && isOriginalDateTime()) {
      return minutes
    }
    
    const currentMinute = new Date().getMinutes()
    for (let i = 0; i < currentMinute; i++) {
      minutes.push(i)
    }
  }
  return minutes
}

// 判断选择的是否是今天
const isToday = () => {
  if (!appointmentForm.appointment_date) return false
  const today = new Date()
  const selectedDate = new Date(appointmentForm.appointment_date)
  return (
    today.getFullYear() === selectedDate.getFullYear() &&
    today.getMonth() === selectedDate.getMonth() &&
    today.getDate() === selectedDate.getDate()
  )
}

// 判断是否是原始预约时间
const isOriginalDateTime = () => {
  if (!appointmentId.value || !appointmentForm.appointment_date || !appointmentForm.appointment_time) {
    return false
  }
  
  const originalDateTime = new Date(appointmentForm.appointment_date + ' ' + appointmentForm.appointment_time)
  const now = new Date()
  
  return originalDateTime.getTime() === now.getTime()
}

// 初始化
onMounted(async () => {
  // 获取路由参数中的ID
  const id = route.params.id
  if (id && id !== 'create') {
    appointmentId.value = parseInt(id as string)
    
    // 获取预约详情
    loading.value = true
    try {
      const response = await appointmentService.getAppointmentDetail(appointmentId.value)
      
      if (response.data && response.data.code === 200) {
        const appointmentData = response.data.data
        
        // 填充表单数据
        appointmentForm.location = appointmentData.location || ''
        appointmentForm.service_type = appointmentData.service_id
        appointmentForm.customers = appointmentData.customer_id
        appointmentForm.remark = appointmentData.remark || ''
        appointmentForm.status = appointmentData.status || 0
        
        // 处理折扣
        if (appointmentData.discount_id) {
          appointmentForm.apply_discount = true
          appointmentForm.discount_code = appointmentData.discount_id
        }
        
        // 处理日期和时间
        if (appointmentData.appointment_date) {
          const dateTime = new Date(appointmentData.appointment_date)
          appointmentForm.appointment_date = dateTime.toISOString().split('T')[0]
          
          const hours = dateTime.getHours().toString().padStart(2, '0')
          const minutes = dateTime.getMinutes().toString().padStart(2, '0')
          appointmentForm.appointment_time = `${hours}:${minutes}`
        }
      } else {
        ElMessage.warning(response.data?.message || t('Appointment.PanelList.messages.getDetailFailed'))
      }
    } catch (error) {
      ElMessage.error(t('Appointment.PanelList.messages.getDetailFailed'))
    } finally {
      loading.value = false
    }
  }
  
  // 获取折扣列表
  fetchDiscounts()
  
  // 获取客户列表
  fetchCustomers()
  
  // 获取服务类型列表
  fetchServices()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  
  .module-header {
    flex-shrink: 0;
    padding: 16px;
    
    h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
      color: #303133;
    }
  }
  
  .module-con {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
      
      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(64, 158, 255, 0.3);
        border-radius: 3px;
        
        &:hover {
          background-color: rgba(64, 158, 255, 0.5);
        }
      }

      .section {
        flex-shrink: 0;
      }

      .customer-item {
        &:last-child {
          margin-bottom: 18px;
        }
        .el-form-item {
          margin-bottom: 0;
        }
      }

      .customer-select-wrapper {
        display: flex;
        gap: 10px;
        align-items: center;
        width: 100%;
        
        .add-customer-btn,
        .remove-customer-btn {
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: transparent;
          border: none;
          .el-icon {
            font-size: 18px;
          }
        }
        .remove-customer-btn {
          color: #f43f5e;
        }
      }

      .form-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #ebeef5;
      }
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-input.is-focus .el-input__wrapper),
:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.customer-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  
  .customer-info {
    color: #909399;
    font-size: 12px;
  }
}

.service-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  
  .service-info {
    color: #909399;
    font-size: 12px;
  }
}
</style>