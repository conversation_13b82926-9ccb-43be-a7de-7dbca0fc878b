<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
      <h1></h1>
      <div class="btn-list">
        <FilterPopover v-model="showFilterDropdown">
          <template #reference>
            <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
              <el-icon>
                <img :src="$asset('Cms/Asset/FilterIcon.png')" alt="FilterIcon" />
              </el-icon>
              <span>{{ $t('Cms.list.filter') }}</span>
            </el-button>
          </template>
          <el-form :inline="true" :model="searchForm" label-position="top" style="width: 100%;">
            <el-form-item :label="t('Cms.list.filter')" style="width: 100%;">
              <el-input 
                v-model="searchForm.keyword"
                :placeholder="t('Appointment.EmailList.search.placeholder')+t('Appointment.EmailList.table.name')"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="flex justify-center">
              <el-button class="el-button-default" @click="handleReset">
                <el-icon><Refresh /></el-icon>
                <span>{{ $t('Cms.list.refresh') }}</span>
              </el-button>
              <el-button class="button-no-border" type="primary" @click="handleSearch">
                <el-icon><Filter /></el-icon>
                <span>{{ $t('Cms.list.filter') }}</span>
              </el-button>
            </div>
          </template>
        </FilterPopover>
        <el-button type="primary" @click="handleCreate">
          <el-icon size="16"><img :src="$asset('Cms/Asset/PlusIcon.png')" /></el-icon>
          <span>{{ t('Appointment.EmailList.buttons.create') }}</span>
        </el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 表格 -->
        <el-table
          v-loading="loading"
          :data="tableData"
          @selection-change="handleSelectionChange"
        >
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" :label="t('Appointment.EmailList.table.name')" min-width="180" />
          <el-table-column prop="creator_name" :label="t('Appointment.EmailList.table.creator')" width="150">
            <template #default="{ row }">
              {{ row.creator_name || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="created_at" :label="t('Appointment.EmailList.table.createDate')" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" :label="t('Appointment.EmailList.table.lastEdit')" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column :label="t('Appointment.EmailList.table.enabled')" width="100">
            <template #default="{ row }">
              <el-switch
                v-model="row.status"
                :active-value="1"
                :inactive-value="0"
                @change="(val) => handleStatusChange(row, val)"
              />
            </template>
          </el-table-column>
          <el-table-column :label="t('Appointment.EmailList.table.operations')" width="120" fixed="right">
            <template #default="{ row }">
              <div class="bwms-operate-btn-box">
                <div class="bwms-operate-btn" @click="handleEdit(row)">
                  <el-icon size="15"><img :src="$asset('Cms/Asset/EditIcon.png')" /></el-icon>
                </div>
                <div class="bwms-operate-btn" @click="handleDelete(row)">
                  <el-icon size="16"><img :src="$asset('Cms/Asset/DeleteIcon.png')" /></el-icon>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table> 
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pagination.pageSize"
              class="page-size-select"
              @change="handleSizeChange"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
              />
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: pagination.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.page"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              background
              layout="prev, pager, next"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, More, Filter, Refresh } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { appointmentService } from '../../services/appointmentService'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

// i18n
const { t } = useI18n()

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 添加筛选下拉框状态
const showFilterDropdown = ref(false)

// 定义邮件模板类型接口
interface EmailTemplate {
  id: number
  name: string
  code: string
  subject: string
  content: string
  status: number
  creator_id: number
  created_at: string
  updated_at: string
}

// 表格数据
const tableData = ref<EmailTemplate[]>([])
const loading = ref(false)
const selectedRows = ref<EmailTemplate[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 路由
const router = useRouter()

// 格式化日期时间
const formatDateTime = (datetime: string | null) => {
  if (!datetime) return '--'
  return dayjs(datetime).format('YYYY-MM-DD HH:mm')
}

// 创建模板
const handleCreate = () => {
  router.push({
    name: 'EmailTemplateCreate'
  })
}

// 编辑模板
const handleEdit = (row: EmailTemplate) => {
  router.push({
    name: 'EmailTemplateEdit',
    params: {
      id: row.id
    }
  })
}

// 删除单个模板
const handleDelete = (row: EmailTemplate) => {
  ElMessageBox.confirm(
    t('Appointment.EmailList.messages.deleteConfirm', { name: row.name }),
    t('Appointment.EmailList.messages.deleteTitle'),
    {
      confirmButtonText: t('Appointment.Common.buttons.confirm'),
      cancelButtonText: t('Appointment.Common.buttons.cancel'),
      type: 'warning'
    }
  ).then(async () => {
    try {
      await appointmentService.deleteEmailTemplate(row.id)
      ElMessage.success(t('Appointment.EmailList.messages.deleteSuccess'))
      fetchData()
    } catch (error) {
      ElMessage.error(t('Appointment.EmailList.messages.deleteFailed'))
    }
  })
}

// 处理选择变化
const handleSelectionChange = (rows: EmailTemplate[]) => {
  selectedRows.value = rows
}

// 查询
const handleSearch = () => {
  pagination.page = 1
  showFilterDropdown.value = false
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  showFilterDropdown.value = false
  handleSearch()
}

// 状态变更
const handleStatusChange = async (row: EmailTemplate, value: number) => {
  try {
    await appointmentService.updateEmailTemplateStatus(row.id)
    ElMessage.success(value === 1 
      ? t('Appointment.EmailList.messages.enableSuccess')
      : t('Appointment.EmailList.messages.disableSuccess'))
  } catch (error) {
    row.status = value === 1 ? 0 : 1 // 恢复原状态
    ElMessage.error(t('Appointment.EmailList.messages.statusChangeFailed'))
  }
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchData()
}

// 页码改变
const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const { data } = await appointmentService.getEmailTemplateList({
      page: pagination.page,
      limit: pagination.pageSize,
      name: searchForm.keyword
    })
    
    if (data.code === 200) {
      tableData.value = data.data.items
      pagination.total = data.data.total
    }
  } catch (error) {
    ElMessage.error(t('Appointment.EmailList.messages.getListFailed'))
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
 
  
  .module-con {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .box {
      padding-top: 20px;
      
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}


.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 18px;
}
</style>
