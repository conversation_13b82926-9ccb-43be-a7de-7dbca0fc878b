<template>
  <div class="table-page bwms-module">
    <div class="module-header">
    </div>

    <div class="module-con">
      <div class="scroll-bar-custom-transparent">
        <div class="box">
          <el-form 
            ref="formRef" 
            :model="formData" 
            :rules="rulesComputed"
            label-position="top"
            :disabled="formLoading"
          >
            <!-- 模板名称 -->
            <el-form-item :label="t('Appointment.EmailCreate.form.name.label')" prop="name">
              <el-input v-model="formData.name" style="width: 50%;" :placeholder="t('Appointment.EmailCreate.form.name.placeholder')"></el-input>
            </el-form-item>
            <!-- Subject 邮件主题 -->
            <el-form-item :label="t('Appointment.EmailCreate.form.subject.label')" prop="subject">
              <el-input v-model="formData.subject" style="width: 50%;" :placeholder="t('Appointment.EmailCreate.form.subject.placeholder')">
              </el-input>
            </el-form-item>

            <!-- code 邮件代码 -->
            <el-form-item :label="t('Appointment.EmailCreate.form.code.label')" prop="code">
              <el-input v-model="formData.code" style="width: 50%;" :placeholder="t('Appointment.EmailCreate.form.code.placeholder')">
              </el-input>
            </el-form-item>

            <!-- Body 邮件正文 -->
            <el-form-item class="editor-form-item" :label="t('Appointment.EmailCreate.form.content.label')" prop="content">
              <div class="editor-container">
                <Editor editType="tinyMce" v-model="formData.content" />
              </div>
            </el-form-item>

            <!-- 添加附件 -->
            <el-form-item :label="t('Appointment.EmailCreate.form.attachment.label')">
              <div class="attachments-container">
                <el-upload
                  class="file-uploader"
                  action="#"
                  :show-file-list="true"
                  :auto-upload="false"
                  :on-change="handleFileChange"
                  :before-upload="beforeFileUpload"
                  :on-remove="handleFileRemove"
                  accept=".xlsx,.xls,image/*"
                  multiple
                  :file-list="fileList"
                >
                  <el-button type="primary">
                    <el-icon><Upload /></el-icon>
                    <span>{{ t('Appointment.EmailCreate.form.attachment.addButton') }}</span>
                  </el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      {{ t('Appointment.EmailCreate.form.attachment.tip') }}
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-form-item>
            
            
          </el-form>
        </div>
        <!-- 底部按钮 -->
        <div class="form-footer">
          <el-button class="button-cancel" @click="cancelHandle">
            <span>{{ t('Appointment.EmailCreate.buttons.cancel') }}</span>
          </el-button>
          <el-button type="primary" @click="saveHandle" :loading="saveLoading">
            <span>{{ isEdit ? t('Appointment.EmailCreate.buttons.submit') : t('Appointment.EmailCreate.buttons.save') }}</span>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { 
  Check, Close, View, Upload, Plus
} from '@element-plus/icons-vue'
import { appointmentService } from '../../services/appointmentService'
import Editor from '/Modules/Editor/views/index.vue'
import { useI18n } from 'vue-i18n'

// 使用i18n
const { t, locale } = useI18n()

const route = useRoute()
const router = useRouter()
const id = route.params.id ? Number(route.params.id) : undefined
const isEdit = computed(() => !!id)

// 定义附件类型接口
interface Attachment {
  name: string
  url?: string
}

// 表单数据
const formRef = ref<FormInstance>()
const formLoading = ref(false)
const saveLoading = ref(false)
const fileList = ref<Array<{ name: string; url?: string;}>>([])

const formData = reactive({
  name: '',
  code: '',
  subject: '',
  content: defaultEmailTemplate(),
  status: 1,
  attachments: [] as Attachment[]
})

// 默认邮件模板
function defaultEmailTemplate() {
  return `<p>${t('Appointment.EmailCreate.form.content.defaultContent.1')}</p>
<p>${t('Appointment.EmailCreate.form.content.defaultContent.2')}</p>
<p>${t('Appointment.EmailCreate.form.content.defaultContent.3')}</p>
<p>${t('Appointment.EmailCreate.form.content.defaultContent.4')}</p>
<p>${t('Appointment.EmailCreate.form.content.defaultContent.5')}</p>
<p>${t('Appointment.EmailCreate.form.content.defaultContent.6')}</p>
<p>${t('Appointment.EmailCreate.form.content.defaultContent.7')}</p>
<p>${t('Appointment.EmailCreate.form.content.defaultContent.8')}</p>
<p>${t('Appointment.EmailCreate.form.content.defaultContent.9')}</p>
<p>${t('Appointment.EmailCreate.form.content.defaultContent.10')}</p>
<p>${t('Appointment.EmailCreate.form.content.defaultContent.11')}</p>
<p>${t('Appointment.EmailCreate.form.content.defaultContent.12')}</p>`
}

// 检查是否是默认模板
const isDefaultContent = (content: string) => {
  return content === defaultEmailTemplate()
}

// 使用计算属性来生成动态更新的表单规则
const rulesComputed = computed<FormRules>(() => ({
  name: [
    { required: true, message: t('Appointment.EmailCreate.form.name.required'), trigger: 'blur' },
    { min: 2, max: 50, message: t('Appointment.EmailCreate.form.name.length'), trigger: 'blur' }
  ],
  subject: [
    { required: true, message: t('Appointment.EmailCreate.form.subject.required'), trigger: 'blur' }
  ],
  code: [
    { required: true, message: t('Appointment.EmailCreate.form.code.required'), trigger: 'blur' }
  ],
  content: [
    { required: true, message: t('Appointment.EmailCreate.form.content.required'), trigger: 'blur' },
    { 
      validator: (rule: any, value: string, callback: Function) => {
        if (isDefaultContent(value)) {
          callback(new Error(t('Appointment.EmailCreate.form.content.contentWarning')))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}))

// 处理文件上传前的验证
const beforeFileUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.ms-excel' || 
                 file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  const isImage = file.type.startsWith('image/')
  const isValidType = isExcel || isImage
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error(t('Appointment.EmailCreate.form.attachment.uploadError.typeError'))
    return false
  }
  if (!isLt10M) {
    ElMessage.error(t('Appointment.EmailCreate.form.attachment.uploadError.sizeError'))
    return false
  }
  return true
}

// 处理文件变化
const handleFileChange = async (uploadFile: any) => {
  if (!beforeFileUpload(uploadFile.raw)) {
    return
  }

  const file = uploadFile.raw
    // 处理图片上传
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('dir', '/images/test')
      formData.append('mode', 'OVERWRITE')
      
      const { data } = await appointmentService.uploadCustomerAvatar(formData)
      if (data.code === 200 && data.data.file && data.data.file.url) {
        // 添加到文件列表
        fileList.value = [...fileList.value, {
          name: uploadFile.name,
          url: data.data.file.url
        }]
      }
    } catch (error) {
      ElMessage.error(t('Appointment.EmailCreate.form.attachment.uploadError.uploadFailed'))
    }
  
  
  // 更新表单附件
  updateFormAttachments()
}

// 处理文件移除
const handleFileRemove = (uploadFile: any) => {
  fileList.value = fileList.value.filter(file => file.name !== uploadFile.name)
  updateFormAttachments()
}

// 更新表单附件列表
const updateFormAttachments = () => {
  formData.attachments = fileList.value.map(file => ({
    name: file.name,
    url: file.url,
  }))
}

// 初始化数据
onMounted(async () => {
  if (id) {
    try {
      formLoading.value = true
      const response = await appointmentService.getEmailTemplateDetail(id)
      if (response.data.code === 200) {
        const templateData = response.data.data
        
        // 更新表单数据
        formData.name = templateData.name
        formData.code = templateData.code
        formData.subject = templateData.subject
        formData.content = templateData.content
        
        // 如果有附件数据，加载附件列表
        if (templateData.attachments && templateData.attachments.length) {
          formData.attachments = templateData.attachments
          fileList.value = templateData.attachments.map((item: { name: string; url?: string }) => ({
            name: item.name,
            url: item.url
          }))
        }
      }
    } catch (error) {
      ElMessage.error(t('Appointment.EmailCreate.messages.getDetailFailed'))
    } finally {
      formLoading.value = false
    }
  }
})

// 保存
const saveHandle = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      // 再次检查内容是否为默认模板
      if (isDefaultContent(formData.content)) {
        ElMessage.warning(t('Appointment.EmailCreate.messages.contentWarning'))
        return
      }

      try {
        saveLoading.value = true
        
        // 根据接口要求构造数据
        const data = {
          name: formData.name,
          subject: formData.subject,
          content: formData.content,
          code: formData.code,
          attachments: formData.attachments,
          status: formData.status
        }
        
        await appointmentService.createEmailTemplate(data)
        ElMessage.success(t('Appointment.EmailCreate.messages.saveSuccess'))
        
        // 返回列表页
        router.push({ name: 'EmailTemplates' })
      } catch (error) {
        ElMessage.error(t('Appointment.EmailCreate.messages.saveFailed'))
      } finally {
        saveLoading.value = false
      }
    }
  })
}

// 取消
const cancelHandle = () => {
  router.push({ name: 'EmailTemplates' })
}

// 当语言变化时更新默认模板内容
watch(locale, () => {
  if(!isEdit.value || isDefaultContent(formData.content)) {
    formData.content = defaultEmailTemplate();
  }
}, { immediate: true });

</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  
  .module-header {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
    
    h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
      color: #303133;
    }
  }
  
  .module-con {
    flex: 1;
    overflow: hidden;
    
    .box {
      padding-top: 20px;
      
      
    }
  }
}
.form-footer {
  display: flex;
  justify-content: center;
  margin-top: 26px;
}
.attachments-container {
  .file-uploader {
    :deep(.el-upload-list) {
      margin-top: 10px;
    }
  }
}

.editor-form-item {
  :deep(.el-form-item__error) {
    top: 101%;
  }
}

.el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}
</style>

