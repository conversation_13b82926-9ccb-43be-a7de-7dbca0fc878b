<template>
  <div class="table-page bwms-module">
    <div class="module-header">
    </div>
    
    <div class="module-con">
      <div class="scroll-bar-custom-transparent">
        <div class="box ">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-position="top"
            v-loading="loading"
          >
            <!-- 头像设置 -->
            <div class="section">
              <div class="section-title">{{ t('Appointment.StaffAdd.avatar.title') }}</div>
              <div class="avatar-section">
                <el-upload
                  class="avatar-uploader"
                  action="#"
                  :show-file-list="false"
                  :auto-upload="false"
                  :on-change="handleAvatarChange"
                  accept="image/*"
                >
                  <div class="avatar-container">
                    <template v-if="imageUrl">
                      <img :src="imageUrl" class="avatar" />
                      <div class="avatar-hover-mask">
                        <el-icon size="32"><Edit /></el-icon>
                        <span>{{ t('Appointment.StaffAdd.avatar.change') }}</span>
                      </div>
                    </template>
                    <template v-else>
                      <div class="upload-placeholder">
                        <el-icon size="32"><Plus /></el-icon>
                        <span style="text-align: center;">{{ t('Appointment.StaffAdd.avatar.upload') }}</span>
                      </div>
                    </template>
                  </div>
                </el-upload>
              </div>
            </div>

            <!-- 员工信息 -->
            <div class="section" style="padding-bottom: 20px;">
              <el-row :gutter="20">
                <el-col :span="12">
                  <!-- 员工姓名 -->
                  <el-form-item 
                    :label="t('Appointment.StaffAdd.form.name.label')" 
                    prop="name"
                    required
                  >
                    <el-input 
                      v-model="form.name"
                      :placeholder="t('Appointment.StaffAdd.form.name.placeholder')"
                      style="width: 100%"
                    />
                  </el-form-item>

                  <!-- 员工邮箱 -->
                  <el-form-item 
                    :label="t('Appointment.StaffAdd.form.email.label')" 
                    prop="email"
                    required
                  >
                    <el-input 
                      v-model="form.email"
                      :placeholder="t('Appointment.StaffAdd.form.email.placeholder')"
                      style="width: 100%"
                    />
                  </el-form-item>

                  <!-- 员工电话 -->
                  <el-form-item 
                    :label="t('Appointment.StaffAdd.form.phone.label')" 
                    prop="phone"
                  >
                    <el-input 
                      v-model="form.phone"
                      :placeholder="t('Appointment.StaffAdd.form.phone.placeholder')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <!-- 部门 -->
                  <el-form-item 
                    :label="t('Appointment.StaffAdd.form.department.label')" 
                    prop="department_id"
                  >
                    <el-select
                      v-model="form.department_id"
                      :placeholder="t('Appointment.StaffAdd.form.department.placeholder')"
                      style="width: 100%"
                      filterable
                      :loading="departmentLoading"
                      clearable
                    >
                      <el-option
                        v-for="department in departmentOptions"
                        :key="department.id"
                        :label="department.name"
                        :value="department.id"
                      />
                      <template #empty>
                        <div style="text-align: center; padding: 8px 0;">
                          {{ t('Cms.list.no_data') }}
                        </div>
                      </template>
                    </el-select>
                  </el-form-item>

                  <!-- 职位 -->
                  <el-form-item 
                    :label="t('Appointment.StaffAdd.form.position.label')" 
                    prop="position_id"
                  >
                    <el-select
                      v-model="form.position_id"
                      :placeholder="t('Appointment.StaffAdd.form.position.placeholder')"
                      style="width: 100%"
                      filterable
                      :loading="positionLoading"
                      clearable
                    >
                      <el-option
                        v-for="position in positionOptions"
                        :key="position.id"
                        :label="position.name"
                        :value="position.id"
                      />
                      <template #empty>
                        <div style="text-align: center; padding: 8px 0;">
                          {{ t('Cms.list.no_data') }}
                        </div>
                      </template>
                    </el-select>
                  </el-form-item>

                  <!-- 描述 -->
                  <el-form-item 
                    :label="t('Appointment.StaffAdd.form.remark.label')" 
                    prop="remark"
                  >
                    <el-input
                      v-model="form.remark"
                      type="textarea"
                      :placeholder="t('Appointment.StaffAdd.form.remark.placeholder')"
                      :rows="4"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            
          </el-form>
        </div>
        <!-- 按钮组 -->
        <div class="form-buttons">
          <el-button class="button-cancel" @click="handleCancel">{{ t('Appointment.StaffAdd.buttons.cancel') }}</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ isEdit ? t('Appointment.StaffAdd.buttons.submit') : t('Appointment.StaffAdd.buttons.saveAndAdd') }}
          </el-button>
        </div>
      </div>
    </div>
    
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type { FormInstance } from 'element-plus'
import { appointmentService } from '../../services/appointmentService'
import { Plus, Edit } from '@element-plus/icons-vue'

// i18n
const { t } = useI18n()

// 路由
const router = useRouter()
const route = useRoute()

// 判断是否为编辑模式
const isEdit = ref(false)
const staffId = ref<number | null>(null)

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)
const submitLoading = ref(false)

// 头像预览
const imageUrl = ref('')

// 表单数据
const form = reactive({
  name: '',
  email: '',
  phone: '',
  position_id: null,
  department_id: null,
  // location: '',
  remark: '',
  photo: ''
})

// 表单验证规则
const rules = computed(() => ({
  name: [
    { required: true, message: t('Appointment.StaffAdd.form.name.required'), trigger: 'blur' },
    { min: 1, max: 50, message: t('Appointment.StaffAdd.form.name.length'), trigger: 'blur' }
  ],
  email: [
    { required: true, message: t('Appointment.StaffAdd.form.email.required'), trigger: 'blur' },
    { type: 'email', message: t('Appointment.StaffAdd.form.email.format'), trigger: 'blur' }
  ],
  phone: [
    { pattern: /^[\d\+\-\s]{1,20}$/, message: t('Appointment.StaffAdd.form.phone.format'), trigger: 'blur' }
  ],
  // location: [
  //   { max: 50, message: '长度不能超过 50 个字符', trigger: 'blur' }
  // ],
  remark: [
    { max: 255, message: t('Appointment.StaffAdd.form.remark.length'), trigger: 'blur' }
  ]
}))

// 更新类型定义
interface UploadFileInfo {
  raw: File
  name: string
  size?: number
  type?: string
}

// 部门选项数据
const departmentOptions = ref<Array<{
  id: number,
  name: string
}>>([])

// 职位选项数据
const positionOptions = ref<Array<{
  id: number,
  name: string
}>>([])

// 部门相关状态
const departmentLoading = ref(false)
const isDepartmentSelectOpen = ref(false)
const departmentSearchQuery = ref('')

// 职位相关状态
const positionLoading = ref(false)
const isPositionSelectOpen = ref(false)
const positionSearchQuery = ref('')

// 获取部门列表
const fetchDepartments = async (keyword = '') => {
  departmentLoading.value = true
  try {
    const params: any = {
      page: 1,
      limit: 100
    }
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim()
    }
    
    const response = await appointmentService.getDepartmentList(params)
    if (response.data.code === 200) {
      departmentOptions.value = response.data.data.items.map((item: any) => ({
        id: item.id,
        name: item.name
      }))
    }
  } catch (error) {
    ElMessage.error(t('Appointment.StaffAdd.messages.getDepartmentFailed'))
  } finally {
    departmentLoading.value = false
  }
}

// 获取职位列表
const fetchPositions = async (keyword = '') => {
  positionLoading.value = true
  try {
    const params: any = {
     page: 1,
     limit: 100
    }
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim()
    }
    
    const response = await appointmentService.getPositionList(params)
    if (response.data.code === 200) {
      positionOptions.value = response.data.data.items.map((item: any) => ({
        id: item.id,
        name: item.position_name
      }))
    }
  } catch (error) {
    ElMessage.error(t('Appointment.StaffAdd.messages.getPositionFailed'))
  } finally {
    positionLoading.value = false
  }
}

// 处理部门下拉框可见性变化
const handleDepartmentVisibleChange = (visible: boolean) => {
  if (visible) {
    isDepartmentSelectOpen.value = true
    if (departmentOptions.value.length === 0) {
      fetchDepartments()
    }
  } else {
    isDepartmentSelectOpen.value = false
    departmentSearchQuery.value = ''
    
    setTimeout(() => {
      if (!isDepartmentSelectOpen.value) {
        fetchDepartments()
      }
    }, 200)
  }
}

// 处理职位下拉框可见性变化
const handlePositionVisibleChange = (visible: boolean) => {
  if (visible) {
    isPositionSelectOpen.value = true
    if (positionOptions.value.length === 0) {
      fetchPositions()
    }
  } else {
    isPositionSelectOpen.value = false
    positionSearchQuery.value = ''
    
    setTimeout(() => {
      if (!isPositionSelectOpen.value) {
        fetchPositions()
      }
    }, 200)
  }
}

// 搜索部门
const searchDepartments = (query: string) => {
  if (!isDepartmentSelectOpen.value) return
  
  departmentSearchQuery.value = query
  if (query) {
    fetchDepartments(query)
  }
}

// 搜索职位
const searchPositions = (query: string) => {
  if (!isPositionSelectOpen.value) return
  
  positionSearchQuery.value = query
  if (query) {
    fetchPositions(query)
  }
}

// 处理头像变更
const handleAvatarChange = async (uploadFile: UploadFileInfo) => {
  const file = uploadFile.raw
  const isValidFormat = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidFormat) {
    ElMessage.error(t('Appointment.StaffAdd.avatar.uploadError.formatError'))
    return false
  }
  if (!isLt10M) {
    ElMessage.error(t('Appointment.StaffAdd.avatar.uploadError.sizeError'))
    return false
  }
  const formData = new FormData()
  formData.append('file', file)
  formData.append('dir', '/images/avatar')
  formData.append('mode', 'OVERWRITE')
  try {
    const { data } = await appointmentService.uploadCustomerAvatar(formData)
    if (data.code === 200 && data.data.file && data.data.file.url) {
      const newAvatarUrl = data.data.file.url
      imageUrl.value = newAvatarUrl
      form.photo = newAvatarUrl
      ElMessage.success(t('Appointment.StaffAdd.avatar.uploadError.uploadSuccess'))
    } else {
      ElMessage.error(t('Appointment.StaffAdd.avatar.uploadError.uploadFailed'))
    }
  } catch (error) {
    ElMessage.error(t('Appointment.StaffAdd.avatar.uploadError.uploadFailed'))
  }
}

// 取消
const handleCancel = () => {
  router.push('/appointment/staff')
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error(t('Appointment.StaffAdd.messages.formError'))
      return
    }

    submitLoading.value = true
    try {
      // 构建请求数据
      const staffData = {
        name: form.name,
        email: form.email,
        phone: form.phone || undefined,
        position_id: form.position_id || undefined,
        department_id: form.department_id || undefined,
        // location: form.location || undefined,
        remark: form.remark || undefined,
        photo: form.photo || undefined
      }
      
      if (isEdit.value && staffId.value) {
        // 更新员工信息
        await appointmentService.updateStaff(staffId.value, staffData)
        ElMessage.success(t('Appointment.StaffAdd.messages.updateSuccess'))
      } else {
        // 创建新员工
        await appointmentService.createStaff(staffData)
        ElMessage.success(t('Appointment.StaffAdd.messages.createSuccess'))
      }
      
      router.push('/appointment/staff')
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || (isEdit.value ? t('Appointment.StaffAdd.messages.updateFailed') : t('Appointment.StaffAdd.messages.createFailed')))
    } finally {
      submitLoading.value = false
    }
  })
}

// 获取员工详情
const fetchStaffDetail = async (id: number) => {
  loading.value = true
  try {
    const { data } = await appointmentService.getStaffDetail(id)
    if (data.code === 200) {
      const staffData = data.data
      
      // 填充表单数据
      form.name = staffData.name
      form.email = staffData.email
      form.phone = staffData.phone
      form.position_id = staffData.position_id
      form.department_id = staffData.department_id
      // form.location = staffData.location
      form.remark = staffData.remark
      
      // 如果有头像，设置预览
      if (staffData.photo) {
        imageUrl.value = staffData.photo
        form.photo = staffData.photo
      }
    }
  } catch (error) {
    ElMessage.error(t('Appointment.StaffAdd.messages.getFailed'))
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  // 判断是否有ID参数，如果有则是编辑模式
  const id = route.params.id
  if (id && !Array.isArray(id)) {
    isEdit.value = true
    staffId.value = parseInt(id)
    fetchStaffDetail(staffId.value)
  }
  
  // 获取部门和职位列表
  fetchDepartments()
  fetchPositions()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  
  .module-header {
    flex-shrink: 0;
    padding: 16px;
    
    h1 {
      font-size: 16px;
      font-weight: 500;
      margin: 0;
      color: #000;
    }
  }
  
  .module-con {
    flex: 1;
    overflow: auto;
    background-color: #f5f7fa;
    
    .box {
      padding-top: 20px;
     
      .section {
        .section-title {
          font-weight: 500;
          color: #000;
          font-size: 16px;
          margin-bottom: 8px;
          position: relative;
        }
        
        .form-grid {
          display: grid;
          gap: 20px;
          
          &.single-column {
            grid-template-columns: 1fr;
          }
          
          .el-form-item {
            margin: 0;
          }
        }
      }

     
    }
  }
}
.form-buttons {
    display: flex;
    justify-content: center;
    margin-top: 26px;
    
    .el-button {
      &.is-loading {
        padding-left: 24px;
      }
    }
  }

.avatar-section {
  padding: 0;
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
  
  .avatar-uploader {
    .avatar-container {
      width: 140px;
      height: 140px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s;
      position: relative;
      overflow: hidden;
      
      &:hover {
        border-color: #409eff;
        
        .avatar-hover-mask {
          opacity: 1;
        }
      }
      
      .avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }

      .avatar-hover-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s;
        color: #fff;

        .el-icon {
          font-size: 24px;
          margin-bottom: 8px;
        }

        span {
          font-size: 14px;
        }
      }

      .upload-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #8c939d;
        width: 100%;
        height: 100%;

        .add-avatar-btn {
          padding: 8px 16px;
        }
      }
    }
  }
}

/* 调整文本域宽度 */
.full-width {
  :deep(.el-textarea) {
    width: 100%;
    max-width: 800px;
  }
}
</style>
