<template>
  <div class="month-calendar" v-loading="loading">
    <el-calendar v-model="selectedDate">
      <template #header="{ date }">
        <div class="calendar-header">
          <div class="header-left">
            <el-button text @click="selectPrevMonth">
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            <el-button text @click="goToday">{{ t('Appointment.Calendar.views.today') }}</el-button>
            <el-button text @click="selectNextMonth">
              <el-icon><ArrowRight /></el-icon>
            </el-button>
            <span class="current-date">{{ formatCalendarHeader(date) }}</span>
          </div>
        </div>
      </template>

      <template #date-cell="{ data }">
        <div class="date-cell" :class="{ 
          'is-selected': isSelectedDate(data.day),
          'is-today': isToday(data.day)
        }" @click="handleDateClick(data.day)">
          <div class="cell-top flex justify-between items-center">
            <span class="date-number">{{ data.day.split('-')[2] }}</span>
          </div>
          <!-- 预约事项列表 -->
          <div class="appointment-list" v-if="getAppointments(data.day).length">
            <div v-for="(apt, index) in getAppointments(data.day)" 
                 :key="index" 
                 class="appointment-item">
                 <div class="apt-title">{{ apt.title }}</div>
                 <div class="apt-time">{{ apt.time }}</div>
            </div>
          </div>
        </div>
      </template>
    </el-calendar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { appointmentService } from '../../services/appointmentService'
import { useI18n } from 'vue-i18n'

// 使用i18n
const { t, locale } = useI18n()

interface Service {
  id: number
  name: string
  duration: number
  category_name: string
}

interface Customer {
  id: number
  name: string
  phone: string
  email: string
}

interface AppointmentItem {
  id: number
  customer_id: number
  service_id: number
  appointment_date: string
  end_time: string
  status: number
  status_text: string
  customer: Customer
  service: Service
  location: string
}

interface ApiResponse {
  code: number
  message: string
  data: {
    total: number
    items: AppointmentItem[]
  }
}

// 状态定义
const selectedDate = ref(new Date())

// 修改预约数据的定义
const appointments = ref<AppointmentItem[]>([])
const loading = ref(false)
// 添加 emits 定义
const emit = defineEmits<{
  'date-click': [data: { date: string, appointments: AppointmentItem[] }]
}>()

// 添加获取预约数据的方法
const fetchAppointments = async (date: string) => {
  try {
    loading.value = true
    const startDate = dayjs(date).startOf('month').format('YYYY-MM-DD 00:00:00')
    const endDate = dayjs(date).endOf('month').format('YYYY-MM-DD 23:59:59')
    
    const { data } = await appointmentService.getAppointmentLists({
      start_date: startDate,
      end_date: endDate
    })
    
    if (data.code === 200 && data.data) {
      appointments.value = data.data.items
    }
  } catch (error) {
    appointments.value = []
  } finally {
    loading.value = false
  }
}

// 日历相关方法
const formatCalendarHeader = (date: Date) => {
  return dayjs(date).format('YYYY/MM')
}

const selectPrevMonth = async () => {
  selectedDate.value = dayjs(selectedDate.value).subtract(1, 'month').toDate()
  await fetchAppointments(dayjs(selectedDate.value).format('YYYY-MM-DD'))
}

const selectNextMonth = async () => {
  selectedDate.value = dayjs(selectedDate.value).add(1, 'month').toDate()
  await fetchAppointments(dayjs(selectedDate.value).format('YYYY-MM-DD'))
}

const goToday = async () => {
  selectedDate.value = new Date()
  await fetchAppointments(dayjs(selectedDate.value).format('YYYY-MM-DD'))
}

const isSelectedDate = (day: string) => {
  return dayjs(day).isSame(selectedDate.value, 'day')
}

const isDisabledDate = (day: string) => {
  return dayjs(day).isBefore(dayjs(), 'day')
}

const isToday = (day: string) => {
  return dayjs(day).isSame(dayjs(), 'day')
}

const getAppointments = (day: string) => {
  const dayAppointments = appointments.value.filter(apt => 
    dayjs(apt.appointment_date).format('YYYY-MM-DD') === day
  )
  return dayAppointments.map(apt => ({
    id: apt.id,
    title: `${apt.service.name}`,
    time: `${dayjs(apt.appointment_date).format('HH:mm')} - ${dayjs(apt.end_time).format('HH:mm')}`
  }))
}

// 修改日期点击处理函数
const handleDateClick = async (day: string) => {
  selectedDate.value = dayjs(day).toDate()
  const dayAppointments = appointments.value.filter(apt => 
    dayjs(apt.appointment_date).format('YYYY-MM-DD') === day
  )
  emit('date-click', { date: day, appointments: dayAppointments })
}

// 更新星期几的多语言标题
const updateWeekdayHeaders = () => {
  // 移除之前添加的样式（如果存在）
  const prevStyle = document.getElementById('month-calendar-i18n-style')
  if (prevStyle) {
    prevStyle.remove()
  }
  
  // 添加多语言支持的表头内容
  const style = document.createElement('style')
  style.id = 'month-calendar-i18n-style'
  style.textContent = `
    .month-calendar .el-calendar-table thead th:nth-child(1)::after { content: '${t('Appointment.Calendar.views.sunday')}' !important; }
    .month-calendar .el-calendar-table thead th:nth-child(2)::after { content: '${t('Appointment.Calendar.views.monday')}' !important; }
    .month-calendar .el-calendar-table thead th:nth-child(3)::after { content: '${t('Appointment.Calendar.views.tuesday')}' !important; }
    .month-calendar .el-calendar-table thead th:nth-child(4)::after { content: '${t('Appointment.Calendar.views.wednesday')}' !important; }
    .month-calendar .el-calendar-table thead th:nth-child(5)::after { content: '${t('Appointment.Calendar.views.thursday')}' !important; }
    .month-calendar .el-calendar-table thead th:nth-child(6)::after { content: '${t('Appointment.Calendar.views.friday')}' !important; }
    .month-calendar .el-calendar-table thead th:nth-child(7)::after { content: '${t('Appointment.Calendar.views.saturday')}' !important; }
  `
  document.head.appendChild(style)
}

// 在组件挂载时获取今天的预约数据
onMounted(async () => {
  const today = dayjs().format('YYYY-MM-DD')
  await fetchAppointments(today)
  
  // 初始化多语言星期标题
  updateWeekdayHeaders()
})

// 监听语言变化，更新页面
watch(locale, () => {
  // 更新星期标题
  updateWeekdayHeaders()
  
  // 强制刷新日历标题（通过重新获取数据）
  // const currentDate = dayjs(selectedDate.value).format('YYYY-MM-DD')
  // fetchAppointments(currentDate)
})
</script>

<style lang="scss" scoped>
.month-calendar {
  height: 100%;
  
  :deep(.el-calendar) {
    --el-calendar-border: none;
    height: 100%;
    display: flex;
    flex-flow: column;
    
    .el-calendar__header {
      padding: 0 0 20px 0;
      border-bottom: 1px solid #ebeef5;
    }

    .el-calendar__body {
      flex: 1;
      overflow-y: auto;
      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(64, 158, 255, 0.3);
        border-radius: 3px;
        
        &:hover {
          background-color: rgba(64, 158, 255, 0.5);
        }
      }
    }
    // 添加表格边框样式
    .el-calendar-table {
      border-collapse: collapse;
      border: 1px solid #ebeef5;
      
      // 表头单元格边框和样式
      thead th {
        text-align: left;
        padding: 12px 8px;
        border: 1px solid #ebeef5;
        color: transparent;
        font-weight: 500; 
        &:nth-child(1)::after {color: #303133; margin-left: -25px; content: ''; }
        &:nth-child(2)::after {color: #303133; margin-left: -25px; content: ''; }
        &:nth-child(3)::after {color: #303133; margin-left: -25px; content: ''; }
        &:nth-child(4)::after {color: #303133; margin-left: -25px; content: ''; }
        &:nth-child(5)::after {color: #303133; margin-left: -25px; content: ''; }
        &:nth-child(6)::after {color: #303133; margin-left: -17px; content: ''; }
        &:nth-child(7)::after {color: #303133; margin-left: -25px; content: ''; }
      }
      
      // 日期单元格边框
      td {
        border: 1px solid #ebeef5;
        padding: 0;
      }
      
      .el-calendar-day {
        padding: 8px;
        overflow: auto;
        min-height: 90px;
        
        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 4px;
          height: 4px;
        }
        
        &::-webkit-scrollbar-thumb {
          background-color: rgba(64, 158, 255, 0.3);
          border-radius: 3px;
          
          &:hover {
            background-color: rgba(64, 158, 255, 0.5);
          }
        }
        
        &::-webkit-scrollbar-track {
          background-color: rgba(0, 0, 0, 0.05);
          border-radius: 3px;
        }
      }
    }
  }
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 26px;
    .current-date {
      font-size: 26px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .date-cell {
    height: 100%;
    
    &.is-selected {
      background-color: #ecf5ff;
    }
    
    &.is-today {
      .date-number {
        color: #409EFF;
      }
    }
    
    .cell-top {
      margin-bottom: 6px;
      
      
      .date-number {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
  
  .appointment-list {
    .appointment-item {
      background-color: #ecf5ff;
      border-radius: 4px;
      padding: 4px 6px;
      margin-bottom: 4px;
      &:last-child {
        margin-bottom: 0;
      }
      
      .apt-title {
        font-size: 12px;
        font-weight: 500;
        color: #303133;
      }
      
      .apt-time {
        font-size: 11px;
        color: #409EFF;
        margin-top: 2px;
      }
    }
  }
}
</style>
