<template>
  <div class="week-calendar" v-loading="loading">
    <div class="week-header">
      <div class="header-left">
        <el-button text @click="selectPrevWeek">
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <el-button text @click="goToday">{{ t('Appointment.Calendar.views.today') }}</el-button>
        <el-button text @click="selectNextWeek">
          <el-icon><ArrowRight /></el-icon>
        </el-button>
        <span class="current-week">{{ formatWeekHeader() }}</span>
      </div>
    </div>
    
    <!-- 周视图 -->
    <div class="week-view">
      <!-- 星期表头 -->
      <div class="week-days-header">
        <div class="time-label-placeholder"></div>
        <div v-for="(day, index) in weekDays" :key="index" class="day-header" @click="handleDateClick(day)">
          <div class="day-name">{{ day.dayName }}</div>
          <div class="day-date">{{ day.date }}</div>
        </div>
      </div>
      
      <!-- 时间段 -->
      <div class="time-slots">
        <!-- 正常时间段 -->
        <div v-for="hour in timeSlots" :key="hour" class="time-slot-row">
          <div class="time-label">{{ formatHour(hour) }}</div>
          <div v-for="(day, index) in weekDays" :key="index" class="time-cell" @click="handleDateClick(day)">
            <div v-for="apt in getAppointmentsForHour(day.fullDate, hour)" 
                 :key="apt.id" 
                 class="appointment-item">
              <div class="apt-title">{{ apt.title }}</div>
              <div class="apt-time">{{ apt.time }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { appointmentService } from '../../services/appointmentService'
import { useI18n } from 'vue-i18n'

// 使用i18n
const { t, locale } = useI18n()

interface Service {
  id: number
  name: string
  duration: number
  category_name: string
}

interface Customer {
  id: number
  name: string
  phone: string
  email: string
}

interface AppointmentItem {
  id: number
  customer_id: number
  service_id: number
  appointment_date: string
  end_time: string
  status: number
  status_text: string
  customer: Customer
  service: Service
  location: string
}

interface DayInfo {
  dayName: string
  date: string
  fullDate: string
  isToday: boolean
}

// 添加 emits 定义
const emit = defineEmits<{
  'date-click': [data: { date: string, appointments: AppointmentItem[] }]
}>()

// 状态定义
const currentWeekStart = ref(dayjs().startOf('week').toDate())
const loading = ref(false)

// 获取星期几的名称
const getDayNames = () => {
  return [
    t('Appointment.Calendar.views.sunday'),
    t('Appointment.Calendar.views.monday'),
    t('Appointment.Calendar.views.tuesday'),
    t('Appointment.Calendar.views.wednesday'),
    t('Appointment.Calendar.views.thursday'),
    t('Appointment.Calendar.views.friday'),
    t('Appointment.Calendar.views.saturday')
  ]
}

// 计算当前一周的日期
const weekDays = computed<DayInfo[]>(() => {
  const days: DayInfo[] = []
  const startOfWeek = dayjs(currentWeekStart.value)
  const dayNames = getDayNames()
  
  for (let i = 0; i < 7; i++) {
    const date = startOfWeek.add(i, 'day')
    days.push({
      dayName: dayNames[date.day()],
      date: date.format('MM/DD'),
      fullDate: date.format('YYYY-MM-DD'),
      isToday: date.isSame(dayjs(), 'day')
    })
  }
  
  return days
})

// 修改预约数据的定义
const appointments = ref<AppointmentItem[]>([])

// 添加获取预约数据的方法
const fetchAppointments = async () => {
  try {
    loading.value = true
    const startDate = dayjs(currentWeekStart.value).format('YYYY-MM-DD 00:00:00')
    const endDate = dayjs(currentWeekStart.value).add(6, 'day').format('YYYY-MM-DD 23:59:59')
    
    const { data } = await appointmentService.getAppointmentLists({
      start_date: startDate,
      end_date: endDate
    })
    
    if (data.code === 200 && data.data) {
      appointments.value = data.data.items
    }
  } catch (error) {
    appointments.value = []
  } finally {
    loading.value = false
  }
}

// 周视图相关方法
const formatWeekHeader = () => {
  const start = dayjs(currentWeekStart.value)
  const end = start.add(6, 'day')
  return `${start.format('YYYY/MM/DD')} - ${end.format('MM/DD')}`
}

const selectPrevWeek = async () => {
  currentWeekStart.value = dayjs(currentWeekStart.value).subtract(1, 'week').toDate()
  await fetchAppointments()
}

const selectNextWeek = async () => {
  currentWeekStart.value = dayjs(currentWeekStart.value).add(1, 'week').toDate()
  await fetchAppointments()
}

const goToday = async () => {
  currentWeekStart.value = dayjs().startOf('week').toDate()
  await fetchAppointments()
}

// 格式化小时
const formatHour = (hour: number) => {
  return `${hour}:00`
}

// 添加时间段配置
const timeSlots = computed(() => {
  const slots: number[] = []
  // 从早上8点到晚上23点（9点）
  for (let hour = 8; hour <= 23; hour++) {
    slots.push(hour)
  }
  return slots
})

// 获取某天某小时的预约
const getAppointmentsForHour = (date: string, hour: number) => {
  return appointments.value.filter(apt => {
    const aptDate = dayjs(apt.appointment_date)
    return aptDate.format('YYYY-MM-DD') === date && aptDate.hour() === hour
  }).map(apt => ({
    id: apt.id,
    title: `${apt.service.name}`,
    time: `${dayjs(apt.appointment_date).format('HH:mm')} - ${dayjs(apt.end_time).format('HH:mm')}`
  }))
}

// 修改日期点击处理函数
const handleDateClick = (day: DayInfo) => {
  const dayAppointments = appointments.value.filter(apt => 
    dayjs(apt.appointment_date).format('YYYY-MM-DD') === day.fullDate
  )
  emit('date-click', { date: day.fullDate, appointments: dayAppointments })
}

// 在组件挂载时获取本周的预约数据
onMounted(async () => {
  await fetchAppointments()
})
</script>

<style lang="scss" scoped>
.week-calendar {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .week-header {
    padding: 0 0 20px 0;
    border-bottom: 1px solid #ebeef5;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 26px;
      .current-week {
        font-size: 26px;
        font-weight: 500;
        color: #303133;
      }
    }
  }
  
  .week-view {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .week-days-header {
      display: flex;
      border-bottom: 1px solid #ebeef5;
      
      .time-label-placeholder {
        width: 60px;
      }
      
      .day-header {
        flex: 1;
        padding: 12px 8px;
        text-align: center;
        
        .day-name {
          font-weight: 500;
          color: #303133;
        }
        
        .day-date {
          font-size: 12px;
          color: #606266;
          margin-top: 4px;
        }
      }
    }
    
    .time-slots {
      flex: 1;
      overflow-y: auto;
      
      .time-slot-row {
        display: flex;
        min-height: 60px;
        border-bottom: 1px solid #f0f2f5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .time-label {
          width: 60px;
          padding: 8px;
          color: #909399;
          font-size: 12px;
          text-align: center;
          border-right: 1px solid #f0f2f5;
        }
        
        .time-cell {
          flex: 1;
          padding: 4px;
          border-right: 1px solid #f0f2f5;
          
          &:last-child {
            border-right: none;
          }
          
          .appointment-item {
            background-color: #ecf5ff;
            border-radius: 4px;
            padding: 6px;
            margin-bottom: 4px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .apt-title {
              font-size: 12px;
              font-weight: 500;
              color: #303133;
            }
            
            .apt-time {
              font-size: 11px;
              color: #409EFF;
              margin-top: 2px;
            }
          }
        }
      }
      
      // 滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(64, 158, 255, 0.3);
        border-radius: 3px;
        
        &:hover {
          background-color: rgba(64, 158, 255, 0.5);
        }
      }
    }
  }
}

// 添加鼠标样式，表明单元格可点击
.day-header, .time-cell {
  cursor: pointer;
  
  &:hover {
    background-color: rgba(64, 158, 255, 0.1);
  }
}
</style> 