<template>
  <el-drawer
    v-model="drawerVisible"
    :title="formatDate(selectedDate)"
    :size="450"
    :with-header="true"
    @close="closeDrawer"
    direction="rtl"
    custom-class="calendar-detail-drawer"
  >
    <template #header>
      <div class="drawer-header">
        <h4 class="drawer-title">{{ formatDate(selectedDate) }}</h4>
      </div>
    </template>

    <div class="calendar-detail-container">
      <div class="appointment-content" v-loading="loading">
        <!-- 时间线展示预约 -->
        <template v-if="appointments.length > 0">
          <el-timeline>
            <el-timeline-item
              v-for="appointment in appointments"
              :key="appointment.id"
              :timestamp="`${formatTime(appointment.appointment_date)} - ${formatTime(appointment.end_time)}`"
              placement="top"
              :type="appointment.status === 3 ? 'primary' : ''"
              size="large"
            >
              <el-card 
                class="appointment-card" 
                shadow="hover" 
              >
                <template #header>
                  <div class="card-header">
                    <span class="class-type">{{ appointment.service.name }}</span>
                    <el-tag size="small" :type="getStatusType(appointment.status)">
                      {{ appointment.status_text }}
                    </el-tag>
                  </div>
                </template>
                <div class="appointment-details">
                  <div class="detail-item">
                    <el-icon><User /></el-icon>
                    <span class="item-label">{{ t('Appointment.Calendar.detail.appointment.customer') }}:</span>
                    <span class="item-value">{{ appointment.customer.name }}</span>
                  </div>
                  <div class="detail-item">
                    <el-icon><Money /></el-icon>
                    <span class="item-label">{{ t('Appointment.Calendar.detail.appointment.staff') }}:</span>
                    <span class="item-value">{{ getStaffNames(appointment.service) }}</span>
                  </div>
                  <div class="detail-item">
                    <el-icon><Location /></el-icon>
                    <span class="item-label">{{ t('Appointment.Calendar.detail.appointment.location') }}:</span>
                    <span class="item-value">{{ appointment.location }}</span>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </template>
        
        <!-- 无预约时显示 -->
        <div v-else class="no-appointment">
          <el-empty :description="t('Appointment.Calendar.detail.empty')" />
        </div>
      </div>
      <!-- 底部按钮 -->
      <div class="drawer-footer">
          <el-button type="primary" @click="handleAddAppointment">{{ t('Appointment.Calendar.detail.addButton') }}</el-button>
        </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { User, Location, Money, InfoFilled } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { appointmentService } from '../../services/appointmentService'
import { PropType } from 'vue'

// 使用i18n
const { t } = useI18n()

interface Service {
  id: number
  name: string
  price: string
  duration: number
  category_name: string
  description: string
  staffs?: Array<{name: string}>
}

interface Customer {
  id: number
  name: string
  phone: string
  email: string
  description: string
}

interface AppointmentItem {
  id: number
  customer_id: number
  service_id: number
  location: string
  appointment_date: string
  end_time: string
  status: number
  status_text: string
  original_price: string
  final_price: string
  payment_status: number
  payment_status_text: string
  remark: string
  customer: Customer
  service: Service
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  date: {
    type: [Date, String],
    default: () => new Date()
  },
  appointmentList: {
    type: Array as PropType<AppointmentItem[]>,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'add-appointment', 'view-appointment'])

// 创建内部可控制的抽屉状态
const drawerVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 选中的日期
const selectedDate = computed(() => {
  if (typeof props.date === 'string') {
    return new Date(props.date)
  }
  return props.date
})

// 选中的预约
const selectedAppointment = ref(0)

// 预约数据
const appointments = computed(() => {
  return [...props.appointmentList].sort((a, b) => {
    return dayjs(a.appointment_date).valueOf() - dayjs(b.appointment_date).valueOf()
  })
})
const loading = ref(false)

// 格式化日期
const formatDate = (date: Date) => {
  return dayjs(date).format('YYYY/MM/DD')
}

// 格式化时间
const formatTime = (dateStr: string) => {
  return dayjs(dateStr).format('HH:mm')
}

// 获取状态类型
const getStatusType = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'warning',  // 待确认
    2: 'info',     // 已确认
    3: 'success',  // 已完成
    4: 'danger',   // 已取消
  }
  return statusMap[status] || ''
}

// 获取员工名称
const getStaffNames = (service: Service) => {
  if (service.staffs && service.staffs.length > 0) {
    return service.staffs.map(staff => staff?.name).join(', ')
  }
  return '--'
}

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false
}

// 修改添加预约方法，通过事件通知父组件
const handleAddAppointment = () => {
  emit('add-appointment', selectedDate.value)
  closeDrawer()
}
</script>

<style lang="scss" scoped>
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .drawer-title {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #303133;
  }
}

.calendar-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  
  .appointment-content {
    flex: 1;
    overflow-y: auto;
    
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(64, 158, 255, 0.3);
      border-radius: 3px;
      
      &:hover {
        background-color: rgba(64, 158, 255, 0.5);
      }
    }

    // 时间线样式
    :deep(.el-timeline) {
      padding-left: 10px;
      
      .el-timeline-item {
        position: relative;
        padding-bottom: 20px;
        
        .el-timeline-item__timestamp {
          color: #606266;
          font-weight: 500;
          font-size: 14px;
          margin-bottom: 8px;
        }
        
        .el-timeline-item__node {
          background-color: #e4e7ed;
          
          &.el-timeline-item__node--primary {
            background-color: #409eff;
          }
        }
        
        .el-timeline-item__wrapper {
          padding-left: 20px;
          
          .appointment-card {
            border-radius: 8px;
            margin-bottom: 0;
            transition: all 0.3s;
            
            &.is-selected, &:hover {
              border-color: var(--el-color-primary);
              box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            }
            
            .card-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              
              .class-type {
                font-weight: 500;
                font-size: 15px;
                color: #303133;
              }
            }
            
            .appointment-details {
              .detail-item {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                
                &:last-child {
                  margin-bottom: 0;
                }
                
                .el-icon {
                  margin-right: 6px;
                  color: #909399;
                }
                
                .item-label {
                  margin-right: 6px;
                  color: #606266;
                  font-size: 14px;
                }
                
                .item-value {
                  color: #303133;
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
    }
    
    .no-appointment {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
  }
  
  .drawer-footer {
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    padding: 20px;
    border-top: 1px solid #ebeef5;
    background-color: #ffffff;
    
    .el-button {
      width: 100%;
    }
  }
}

:deep(.el-drawer__body) {
  padding: 0;
  overflow: hidden;
}

:deep(.el-drawer__header) {
  margin-bottom: 20px;
  padding: 0;
  border-bottom: 1px solid #ebeef5;
}
</style>
