<template>
  <div class="bwms-module table-page">
    <!-- 筛选区域 -->
    <div class="module-header">
      <div class="filter-group">
        <el-select 
          v-model="filterForm.timeRange" 
          :placeholder="t('Appointment.Dashboard.filter.timeRange.placeholder')" 
          class="filter-item"
          @change="handleTimeRangeChange"
        >
          <el-option :label="t('Appointment.Dashboard.filter.timeRange.today')" value="today" />
          <el-option :label="t('Appointment.Dashboard.filter.timeRange.week')" value="week" />
          <el-option :label="t('Appointment.Dashboard.filter.timeRange.month')" value="month" />
          <el-option :label="t('Appointment.Dashboard.filter.timeRange.last_month')" value="last_month" />
          <el-option :label="t('Appointment.Dashboard.filter.timeRange.quarter')" value="quarter" />
          <el-option :label="t('Appointment.Dashboard.filter.timeRange.year')" value="year" />
          <el-option :label="t('Appointment.Dashboard.filter.timeRange.custom')" value="custom" />
        </el-select>
        
        <el-date-picker
          v-model="filterForm.dateRange"
          type="daterange"
          size="large"
          range-separator="–"
          :start-placeholder="t('Appointment.Dashboard.filter.datePicker.start')"
          :end-placeholder="t('Appointment.Dashboard.filter.datePicker.end')"
          value-format="YYYY-MM-DD HH:mm:ss"
          @change="handleDateRangeChange"
        />
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="module-con">
      <div class="dashboard-layout">
        <!-- 左侧区域 -->
        <div class="dashboard-left">
          <!-- 数据卡片区域 -->
          <div class="data-cards">
            <div class="data-card">
              <div class="card-title">{{ t('Appointment.Dashboard.cards.appointments') }}</div>
              <div class="card-value">{{ dashboardData.appointments }}</div>
            </div>
            <div class="data-card">
              <div class="card-title">{{ t('Appointment.Dashboard.cards.pendingAppointments') }}</div>
              <div class="card-value">{{ dashboardData.pendingAppointments }}</div>
            </div>
            <div class="data-card">
              <div class="card-title">{{ t('Appointment.Dashboard.cards.revenue') }}</div>
              <div class="card-value">{{ dashboardData.revenue.toLocaleString() }}</div>
            </div>
            <div class="data-card">
              <div class="card-title">{{ t('Appointment.Dashboard.cards.newCustomers') }}</div>
              <div class="card-value">{{ dashboardData.newCustomers }}</div>
            </div>
          </div>
          
          <!-- 今日预约列表 -->
          <div class="today-appointments">
            <div class="section-title">{{ t('Appointment.Dashboard.appointmentList.title') }}</div>
            <el-table :data="todayAppointments" style="width: 100%" v-loading="loading">
              <template #empty>
                  <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
              </template>
              <el-table-column width="160" :label="t('Appointment.Dashboard.appointmentList.columns.appointmentTime')">
                <template #default="{ row }">
                  {{ formatDateTime(row.appointment_date) }}
                </template>
              </el-table-column>
              <el-table-column :label="t('Appointment.Dashboard.appointmentList.columns.customerName')" width="200">
                <template #default="{ row }">
                  <div class="customer-info">
                    <img class="info-avatar" 
                      :src="row.customer?.photo || avatarUrl" 
                      @error="handleAvatarError"
                    />
                    <div class="customer-detail">
                      <div class="customer-name">{{ row.customer?.name }}</div>
                      <div class="customer-email">{{ row.customer?.email }}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="t('Appointment.Dashboard.appointmentList.columns.staff')" min-width="150">
                <template #default="{ row }">
                  <div v-if="row.service?.staffs?.length">
                    <el-tooltip
                      placement="top"
                      :content="row.service.staffs.map(staff => staff?.name).join(', ')"
                    >
                    {{ row.service.staffs.map(staff => staff?.name).join(', ') }}
                    </el-tooltip>
                  </div>
                  <span v-else>--</span>
                </template>
              </el-table-column>
              <el-table-column prop="service.name" :label="t('Appointment.Dashboard.appointmentList.columns.service')" min-width="150" />
              <el-table-column :label="t('Appointment.Dashboard.appointmentList.columns.duration')" width="100">
                <template #default="{ row }">
                  {{ formatDuration(row.service?.duration) }}
                </template>
              </el-table-column>
              <el-table-column :label="t('Appointment.Dashboard.appointmentList.columns.createTime')" width="160" fixed="right">
                <template #default="{ row }">
                  {{ formatDateTime(row.created_at) }}
                </template>
              </el-table-column>
            </el-table>
            
            <!-- 分页 -->
            <div class="pagination table-pagination-style">
              <div class="pagination-left">
                <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
                <el-select
                  v-model="pagination.pageSize"
                  class="page-size-select"
                  @change="handleSizeChange"
                  size="default"
                >
                  <el-option
                    v-for="size in [10, 20, 50, 100]"
                    :key="size"
                    :label="size"
                    :value="size"
                    class="page-size-option"
                  />
                </el-select>
                <span class="total-text">{{ $t('Cms.pagination.total_items', { total: pagination.total }) }}</span>
              </div>
              <div class="pagination-right">
                <el-pagination
                  v-model:current-page="pagination.page"
                  background
                  layout="prev, pager, next"
                  :page-size="pagination.pageSize"
                  :total="pagination.total"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧区域 -->
        <div class="dashboard-right">
          <!-- 预约状态概览 -->
          <div class="status-overview">
            <div class="overview-title">{{ t('Appointment.Dashboard.statusOverview.title') }}</div>
            <div class="status-list">
              <div v-for="(item, index) in statusItems" :key="index" class="status-item">
                <div class="status-icon" :style="{ backgroundColor: item.color }">
                  <el-icon v-if="item.icon === 'check'"><Check /></el-icon>
                  <el-icon v-else-if="item.icon === 'close'"><Close /></el-icon>
                  <el-icon v-else-if="item.icon === 'clock'"><Clock /></el-icon>
                  <el-icon v-else-if="item.icon === 'refresh'"><Refresh /></el-icon>
                  <el-icon v-else-if="item.icon === 'remove'"><Remove /></el-icon>
                  <el-icon v-else-if="item.icon === 'finished'"><CircleCheck /></el-icon>
                  <el-icon v-else-if="item.icon === 'hide'"><Hide /></el-icon>
                </div>
                <div class="status-info">
                  <div class="status-name">{{ item.name }}</div>
                  <div class="status-count">{{ item.count }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Check, Close, Clock, Refresh, Remove, CircleCheck, Hide, More } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { appointmentService } from '../../services/appointmentService'
import type { IAppointment } from '../../types'

// 初始化i18n
const { t } = useI18n()

// 定义预约状态枚举
enum AppointmentStatus {
  PENDING = 1,   // 待确认
  CONFIRMED = 2, // 已确认
  CANCELLED = 3, // 已取消
  COMPLETED = 4, // 已完成
  RESCHEDULED = 5, // 已改期
  REJECTED = 6,  // 已拒绝
  NO_SHOW = 7    // 未到场
}

// 定义状态项接口
interface StatusOverviewItem {
  name: string
  count: number
  status: number
  status_text: string
  color: string
}

// 路由
const router = useRouter()

// 筛选表单
const filterForm = reactive({
  timeRange: 'today',
  dateRange: [] as string[]
})

// 仪表盘数据
const dashboardData = reactive({
  appointments: 0,
  pendingAppointments: 0,
  revenue: 0,
  newCustomers: 0
})

// 状态项列表
const statusItems = ref([{ name: '预约确认', count: 0, color: '#67C23A', icon: 'check' },
  { name: '预约取消', count: 0, color: '#F56C6C', icon: 'close' },
  { name: '预约待确认', count: 0, color: '#E6A23C', icon: 'clock' },
  { name: '预约改期', count: 0, color: '#409EFF', icon: 'refresh' },
  { name: '预约拒绝', count: 0, color: '#909399', icon: 'remove' },
  { name: '预约完成', count: 0, color: '#67C23A', icon: 'finished' },
  { name: 'no-show', count: 0, color: '#F56C6C', icon: 'hide' }
])

// 今日预约列表
const todayAppointments = ref([])
const loading = ref(false)

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 默认头像
const avatarUrl = ref('https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png')

// 添加头像加载错误处理函数
const handleAvatarError = (e: Event) => {
  const imgElement = e.target as HTMLImageElement
  imgElement.src = avatarUrl.value
}

// 格式化日期时间
const formatDateTime = (datetime: string | null) => {
  if (!datetime) return '--'
  return dayjs(datetime).format('YYYY-MM-DD HH:mm')
}

// 格式化时长
const formatDuration = (seconds: number) => {
  if (!seconds) return `0 ${t('Appointment.Common.duration.second')}`
  
  // 计算小时和分钟
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return minutes > 0 
      ? `${hours} ${t('Appointment.Common.duration.hour')} ${minutes} ${t('Appointment.Common.duration.minute')}`
      : `${hours} ${t('Appointment.Common.duration.hour')}`
  }
  
  if (minutes > 0) {
    return `${minutes} ${t('Appointment.Common.duration.minute')}`
  }
  
  return `${seconds} ${t('Appointment.Common.duration.second')}`
}

// 处理时间范围选择
const handleTimeRangeChange = (value: string) => {
  const today = dayjs()
  
  switch (value) {
    case 'today':
      filterForm.dateRange = [
        today.format('YYYY-MM-DD 00:00:00'),
        today.format('YYYY-MM-DD 23:59:59')
      ]
      break
    case 'week':
      filterForm.dateRange = [
        today.startOf('week').format('YYYY-MM-DD 00:00:00'),
        today.endOf('week').format('YYYY-MM-DD 23:59:59')
      ]
      break
    case 'month':
      filterForm.dateRange = [
        today.startOf('month').format('YYYY-MM-DD 00:00:00'),
        today.endOf('month').format('YYYY-MM-DD 23:59:59')
      ]
      break
    case 'last_month':
      filterForm.dateRange = [
        today.subtract(1, 'month').startOf('month').format('YYYY-MM-DD 00:00:00'),
        today.subtract(1, 'month').endOf('month').format('YYYY-MM-DD 23:59:59')
      ]
      break
    case 'quarter':
      // 计算当前季度的开始和结束日期
      const quarterStart = today.startOf('month').subtract(today.month() % 3, 'month')
      const quarterEnd = quarterStart.add(2, 'month').endOf('month')
      filterForm.dateRange = [
        quarterStart.format('YYYY-MM-DD 00:00:00'),
        quarterEnd.format('YYYY-MM-DD 23:59:59')
      ]
      break
    case 'year':
      filterForm.dateRange = [
        today.startOf('year').format('YYYY-MM-DD 00:00:00'),
        today.endOf('year').format('YYYY-MM-DD 23:59:59')
      ]
      break
    case 'custom':
      // 如果选择自定义，清空日期范围，等待用户选择
      filterForm.dateRange = []
      return
  }
  
  // 调用接口获取数据
  fetchDashboardData()
  fetchTodayAppointments()
}

// 处理日期范围变化
const handleDateRangeChange = (dates: string[]) => {
  if (!dates) {
    // 如果清空日期，设置为今天并切换下拉选项为"今日"
    filterForm.timeRange = 'today'
    const today = dayjs()
    filterForm.dateRange = [
      today.format('YYYY-MM-DD 00:00:00'),
      today.format('YYYY-MM-DD 23:59:59')
    ]
  } else {
    // 如果手动选择了日期范围，切换为自定义模式
    filterForm.timeRange = 'custom'
    filterForm.dateRange = dates
  }
  
  // 调用接口获取数据
  fetchDashboardData()
  fetchTodayAppointments()
}

// 改变每页条数
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchTodayAppointments()
}

// 改变页码
const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchTodayAppointments()
}

// 获取仪表盘数据
const fetchDashboardData = async () => {
  try {
    const params = {
      start_date: filterForm.dateRange?.[0],
      end_date: filterForm.dateRange?.[1]
    }
    
    const { data } = await appointmentService.getDashboardData(params)
    
    if (data.code === 200 && data.data) {
      // 更新仪表盘数据
      dashboardData.appointments = data.data.totalAppointmentCount
      dashboardData.pendingAppointments = data.data.pendingAppointmentCount
      dashboardData.revenue = data.data.totalRevenue
      dashboardData.newCustomers = data.data.newCustomerCount
      
      // 更新状态统计
      if (data.data.statusOverview && Array.isArray(data.data.statusOverview)) {
        statusItems.value = data.data.statusOverview.map((item: any) => ({
          name: item.name,
          count: item.count,
          status: item.status,
          status_text: item.status_text,
          color: item.color,
          icon: item?.icon || statusItems.value.find(i => i.name === item.name)?.icon || ''
        }))
      }
    }
  } catch (error) {
  }
}

// 获取今日预约
const fetchTodayAppointments = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      start_date: filterForm.dateRange?.[0],
      end_date: filterForm.dateRange?.[1]
    }
    
    const { data } = await appointmentService.getAppointmentLists(params)
    
    if (data.code === 200 && data.data) {
      todayAppointments.value = data.data.items || []
      pagination.total = data.data.total || 0
    } else {
      todayAppointments.value = []
      pagination.total = 0
    }
  } catch (error) {
    todayAppointments.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  // 设置默认日期范围为今天
  const today = dayjs()
  filterForm.timeRange = 'today'
  filterForm.dateRange = [
    today.format('YYYY-MM-DD 00:00:00'),
    today.format('YYYY-MM-DD 23:59:59')
  ]
  
  // 获取数据
  fetchDashboardData()
  fetchTodayAppointments()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh; // 使用视口高度
  
  .module-header {
    flex-shrink: 0; // 防止头部被压缩
    padding-top: 50px;
    justify-content: left;
    
    .filter-group {
      display: flex;
      align-items: center;
      
      .filter-item {
        margin-right: 10px;
        width: 120px;
      }
      
    }
  }
  
  .module-con {
    flex: 1; // 让内容区域占据剩余空间
    overflow: hidden; // 防止溢出
    
    .dashboard-layout {
      display: flex;
      height: 100%;
      gap: 16px;
      
      .dashboard-left {
        flex: 1;
        min-width: 0; // 防止内容溢出
        display: flex;
        flex-direction: column;
        background: #fff;
        border-radius: 4px;
        padding: 20px;
        overflow: auto;
        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        
        &::-webkit-scrollbar-thumb {
          background-color: rgba(64, 158, 255, 0.3);
          border-radius: 3px;
          
          &:hover {
            background-color: rgba(64, 158, 255, 0.5);
          }
        }
        
        .data-cards {
          display: flex;
          flex-wrap: wrap;
          margin: 0 -10px 20px;
          
          .data-card {
            flex: 1;
            min-width: 200px;
            margin: 0 10px 20px;
            background-color: #fff;
            border-radius: 4px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            
            .card-title {
              font-size: 14px;
              color: #606266;
              margin-bottom: 15px;
            }
            
            .card-value {
              font-size: 24px;
              font-weight: bold;
              color: #303133;
            }
          }
        }
        
        .today-appointments {
          flex: 1;
          display: flex;
          flex-direction: column;
          
          .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 20px;
          }
          
          .el-table {
            flex: 1;
          }
          
          .customer-info {
            display: flex;
            align-items: center;
            
            .info-avatar {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              object-fit: cover;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
              background-color: #f5f7fa;
            }
            
            .customer-detail {
              margin-left: 10px;
              
              .customer-name {
                font-size: 14px;
                color: #303133;
              }
              
              .customer-email {
                font-size: 12px;
                color: #909399;
                margin-top: 3px;
              }
            }
          }
          
          .el-dropdown-link {
            cursor: pointer;
            display: flex;
            align-items: center;
          }
          
          .pagination {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
        }
      }
      
      .dashboard-right {
        width: 300px;
        flex-shrink: 0;
        background: #fff;
        border-radius: 4px;
        padding: 20px;
        overflow: auto;
        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        
        &::-webkit-scrollbar-thumb {
          background-color: rgba(64, 158, 255, 0.3);
          border-radius: 3px;
          
          &:hover {
            background-color: rgba(64, 158, 255, 0.5);
          }
        }
        
        .status-overview {
          .overview-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 20px;
          }
          
          .status-list {
            display: flex;
            flex-direction: column;
            
            .status-item {
              display: flex;
              align-items: center;
              margin-bottom: 20px;
              
              .status-icon {
                width: 28px;
                height: 28px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 10px;
                color: #fff;
                
                .el-icon {
                  font-size: 16px;
                }
              }
              
              .status-info {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: space-between;
                
                .status-name {
                  font-size: 14px;
                  color: #606266;
                }
                
                .status-count {
                  font-size: 16px;
                  font-weight: bold;
                  color: #303133;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
