<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
    </div>
    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 头部切换区域 -->
        <div class="view-header">
          <div class="header-right">
            <el-radio-group v-model="viewType" size="default">
              <el-radio-button label="month">{{ t('Appointment.Calendar.views.month') }}</el-radio-button>
              <el-radio-button label="week">{{ t('Appointment.Calendar.views.week') }}</el-radio-button>
              <el-radio-button label="day">{{ t('Appointment.Calendar.views.day') }}</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="view-content">
          <MonthView v-if="viewType === 'month'" @date-click="handleDateClick" />
          <WeekView v-else-if="viewType === 'week'" @date-click="handleDateClick" />
          <DayView v-else @date-click="handleDateClick" />
        </div>
      </div>
    </div>
    
    <!-- 日历详情抽屉 -->
    <CalendarDetail
      v-model:visible="detailVisible"
      :date="selectedDate"
      :appointment-list="currentDayAppointments"
      @add-appointment="handleAddAppointment"
      @view-appointment="handleViewAppointment"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import MonthView from './month.vue'
import WeekView from './week.vue'
import DayView from './day.vue'
import CalendarDetail from './calendarDetail.vue'
import dayjs from 'dayjs'

// 使用i18n
const { t } = useI18n()
const router = useRouter()

// 视图类型
const viewType = ref('month')

// 选中的日期
const selectedDate = ref(new Date())

// 详情抽屉是否可见
const detailVisible = ref(false)

// 当前日期的预约列表
const currentDayAppointments = ref([])

// 处理日期点击
const handleDateClick = (data: { date: string | Date, appointments: [] }) => {
  selectedDate.value = typeof data.date === 'string' ? new Date(data.date) : data.date
  currentDayAppointments.value = data.appointments
  detailVisible.value = true
}

// 处理添加预约
const handleAddAppointment = (date: Date) => {
  router.push({
    path: '/appointment/panel-create',
  })
}

// 处理查看预约详情
const handleViewAppointment = (appointment: any) => {
  router.push({
    path: `/appointment/detail/${appointment.id}`
  })
}
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  
  .module-con {
    flex: 1;
    overflow: hidden;
    
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
      
      .view-header {
        padding: 8px 0;
        display: flex;
        justify-content: flex-end;
        position: absolute;
        right: 26px;
        
        .header-right {
          .el-radio-group {
            .el-radio-button__inner {
              padding: 8px 16px;
              border: 1px solid #dcdfe6;
              background-color: #f5f7fa;
              
              &:hover {
                color: var(--el-color-primary);
              }
            }

            .el-radio-button__original-radio:checked + .el-radio-button__inner {
              background-color: #fff;
              color: var(--el-color-primary);
              border-color: var(--el-color-primary);
            }
          }
        }
      }
      
      .view-content {
        flex: 1;
        overflow: hidden;
      }
    }
  }
}

// 添加对话框样式
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}
</style>
