<template>
  <div class="day-calendar" v-loading="loading">
    <div class="day-header">
      <div class="header-left">
        <el-button text @click="selectPrevDay">
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <el-button text @click="goToday">{{ t('Appointment.Calendar.views.today') }}</el-button>
        <el-button text @click="selectNextDay">
          <el-icon><ArrowRight /></el-icon>
        </el-button>
        <span class="current-day">{{ formatDayHeader() }}</span>
      </div>
    </div>
    
    <!-- 日视图 -->
    <div class="day-view">
      <!-- 时间段 -->
      <div class="time-slots">
        <!-- 正常时间段 -->
        <div v-for="hour in timeSlots" :key="hour" class="time-slot-row">
          <div class="time-label">{{ formatHour(hour) }}</div>
          <div class="time-cell" @click="handleDateClick">
            <div v-for="apt in getAppointmentsForHour(hour)" 
                 :key="apt.id" 
                 class="appointment-item">
              <div class="apt-title">{{ apt.title }}</div>
              <div class="apt-time">{{ apt.time }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { appointmentService } from '../../services/appointmentService'
import { useI18n } from 'vue-i18n'

// 使用i18n
const { t, locale } = useI18n()

interface Service {
  id: number
  name: string
  duration: number
  category_name: string
}

interface Customer {
  id: number
  name: string
  phone: string
  email: string
}

interface AppointmentItem {
  id: number
  customer_id: number
  service_id: number
  appointment_date: string
  end_time: string
  status: number
  status_text: string
  customer: Customer
  service: Service
  location: string
}

// 状态定义
const selectedDay = ref(new Date())
const loading = ref(false)
const appointments = ref<AppointmentItem[]>([])

// 当前日期信息
const currentDay = computed(() => {
  return dayjs(selectedDay.value)
})

// 添加时间段配置
const timeSlots = computed(() => {
  const slots: number[] = []
  // 从早上8点到晚上23点
  for (let hour = 8; hour <= 23; hour++) {
    slots.push(hour)
  }
  return slots
})

// 日视图相关方法
const formatDayHeader = () => {
  return currentDay.value.format('YYYY/MM/DD')
}

const selectPrevDay = async () => {
  selectedDay.value = currentDay.value.subtract(1, 'day').toDate()
  await fetchAppointments()
}

const selectNextDay = async () => {
  selectedDay.value = currentDay.value.add(1, 'day').toDate()
  await fetchAppointments()
}

const goToday = async () => {
  selectedDay.value = new Date()
  await fetchAppointments()
}

// 格式化小时
const formatHour = (hour: number) => {
  return `${hour}:00`
}

// 获取预约数据
const fetchAppointments = async () => {
  try {
    loading.value = true
    const date = currentDay.value.format('YYYY-MM-DD')
    const startDate = `${date} 00:00:00`
    const endDate = `${date} 23:59:59`
    
    const { data } = await appointmentService.getAppointmentLists({
      start_date: startDate,
      end_date: endDate
    })
    
    if (data.code === 200 && data.data) {
      appointments.value = data.data.items
    }
  } catch (error) {
    appointments.value = []
  } finally {
    loading.value = false
  }
}

// 获取当前日期某小时的预约
const getAppointmentsForHour = (hour: number) => {
  return appointments.value.filter(apt => {
    const aptDate = dayjs(apt.appointment_date)
    return aptDate.hour() === hour
  }).map(apt => ({
    id: apt.id,
    title: `${apt.service.name}`,
    time: `${dayjs(apt.appointment_date).format('HH:mm')} - ${dayjs(apt.end_time).format('HH:mm')}`
  }))
}

// 添加 emits 定义
const emit = defineEmits<{
  'date-click': [data: { date: string, appointments: AppointmentItem[] }]
}>()

// 日期点击处理函数
const handleDateClick = () => {
  const day = currentDay.value.format('YYYY-MM-DD')
  emit('date-click', { 
    date: day, 
    appointments: appointments.value 
  })
}

// 在组件挂载时获取当天的预约数据
onMounted(async () => {
  await fetchAppointments()
})
</script>

<style lang="scss" scoped>
.day-calendar {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .day-header {
    padding: 0 0 20px 0;
    border-bottom: 1px solid #ebeef5;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 26px;
      
      .current-day {
        font-size: 26px;
        font-weight: 500;
        color: #303133;
      }
    }
  }
  
  .day-view {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .time-slots {
      flex: 1;
      overflow-y: auto;
      
      .time-slot-row {
        display: flex;
        min-height: 60px;
        border-bottom: 1px solid #f0f2f5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .time-label {
          width: 80px;
          padding: 8px;
          color: #909399;
          font-size: 14px;
          text-align: center;
          border-right: 1px solid #f0f2f5;
        }
        
        .time-cell {
          flex: 1;
          padding: 8px;
          
          .appointment-item {
            background-color: #ecf5ff;
            border-radius: 4px;
            padding: 8px 12px;
            margin-bottom: 8px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .apt-title {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
            }
            
            .apt-time {
              font-size: 12px;
              color: #409EFF;
              margin-top: 4px;
            }
          }
        }
      }
      
      // 滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(64, 158, 255, 0.3);
        border-radius: 3px;
        
        &:hover {
          background-color: rgba(64, 158, 255, 0.5);
        }
      }
    }
  }
}

// 添加鼠标样式，表明单元格可点击
.time-cell {
  cursor: pointer;
  
  &:hover {
    background-color: rgba(64, 158, 255, 0.1);
  }
}
</style>
