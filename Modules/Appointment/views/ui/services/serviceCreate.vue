<template>
  <div class="table-page bwms-module">
    <div class="module-header">
    </div>
    
    <div class="module-con">
      <el-tabs v-model="activeTab" class="service-tabs">
        <el-tab-pane :label="t('Appointment.ServiceCreate.tabs.basic')" name="basic" />
        <el-tab-pane :label="t('Appointment.ServiceCreate.tabs.staff')" name="staff" />
        <el-tab-pane :label="t('Appointment.ServiceCreate.tabs.time')" name="time" />
        <el-tab-pane :label="t('Appointment.ServiceCreate.tabs.settings')" name="settings" />
      </el-tabs>

      <div class="box scroll-bar-custom">
        <!-- 基础信息表单 -->
        <div v-show="activeTab === 'basic'">
          <el-form 
            ref="serviceFormRef"
            :model="serviceForm"
            :rules="serviceRules"
            label-position="top"
            v-loading="loading"
          >

            <!-- 服务名称和分类 -->
            <div class="section">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item 
                    :label="t('Appointment.ServiceCreate.basic.name.label')"
                    prop="name"
                    required
                  >
                    <el-input 
                      v-model="serviceForm.name"
                      :placeholder="t('Appointment.ServiceCreate.basic.name.placeholder')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item 
                    :label="t('Appointment.ServiceCreate.basic.category.label')"
                    prop="category"
                    required
                  >
                    <el-select
                      v-model="serviceForm.category"
                      :placeholder="t('Appointment.ServiceCreate.basic.category.placeholder')"
                      filterable
                      :loading="categoryLoading"
                    >
                      <el-option
                        v-for="category in categoryOptions"
                        :key="category.value"
                        :label="category.label"
                        :value="category.value"
                      />
                      <template #empty>
                        <div style="text-align: center; padding: 8px 0;">
                          {{ t('Cms.list.no_data') }}
                        </div>
                      </template>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 服务价格和时长 -->
            <div class="section">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item 
                    :label="t('Appointment.ServiceCreate.basic.price.label')"
                    prop="price"
                    required
                  >
                    <el-input-number 
                      v-model="serviceForm.price"
                      :min="0"
                      :precision="2"
                      style="width: 100%;"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item 
                    :label="t('Appointment.ServiceCreate.basic.duration.label')"
                    prop="duration"
                    required
                  >
                    <el-select
                      v-model="serviceForm.duration"
                      :placeholder="t('Appointment.ServiceCreate.basic.duration.placeholder')"
                    >
                      <el-option :label="t('Appointment.ServiceCreate.basic.duration.options.30')" value="30" />
                      <el-option :label="t('Appointment.ServiceCreate.basic.duration.options.40')" value="40" />
                      <el-option :label="t('Appointment.ServiceCreate.basic.duration.options.60')" value="60" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 显示设置 + 重复预约设置 -->
            <div class="section">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="t('Appointment.ServiceCreate.basic.display.label')">
                    <div class="display-options">
                      <el-checkbox v-model="serviceForm.hidePrice">
                        {{ t('Appointment.ServiceCreate.basic.display.hidePrice') }}
                      </el-checkbox>
                      <el-checkbox v-model="serviceForm.hideDuration">
                        {{ t('Appointment.ServiceCreate.basic.display.hideDuration') }}
                      </el-checkbox>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label-position="left" :label="t('Appointment.ServiceCreate.basic.repeat.label')" style="margin-bottom: 0;">
                    <el-checkbox v-model="serviceForm.enableRepeat"  style="margin-top: -7px;"></el-checkbox>
                  </el-form-item>
                  <el-form-item v-if="serviceForm.enableRepeat" style="width: 100%;">
                    <div class="repeat-settings" style="width: 100%;">
                      <div class="repeat-row" style="width: 100%;">
                        <span class="repeat-label">{{ t('Appointment.ServiceCreate.basic.repeat.type.label') }}</span>
                        <el-select v-model="serviceForm.repeatType" :placeholder="t('Appointment.ServiceCreate.basic.repeat.type.placeholder')">
                          <el-option :label="t('Appointment.ServiceCreate.basic.repeat.type.daily')" value="1" />
                          <el-option :label="t('Appointment.ServiceCreate.basic.repeat.type.weekly')" value="2" />
                          <el-option :label="t('Appointment.ServiceCreate.basic.repeat.type.monthly')" value="3" />
                        </el-select>
                      </div>
                      <div class="repeat-row">
                        <span class="repeat-label">{{ t('Appointment.ServiceCreate.basic.repeat.mode.label') }}</span>
                        <el-select v-model="serviceForm.repeatMode" :placeholder="t('Appointment.ServiceCreate.basic.repeat.mode.placeholder')">
                          <el-option :label="t('Appointment.ServiceCreate.basic.repeat.mode.byTimes')" value="1" />
                          <el-option :label="t('Appointment.ServiceCreate.basic.repeat.mode.byCycle')" value="2" />
                        </el-select>
                      </div>
                      <div class="repeat-row">
                        <el-checkbox v-model="serviceForm.enableFixedTimes" class="fixed-times-checkbox">
                          {{ t('Appointment.ServiceCreate.basic.repeat.fixedTimes.label') }}
                        </el-checkbox>
                        <template v-if="serviceForm.enableFixedTimes">
                          <div class="fixed-times-inputs">
                            <el-input
                              v-model="serviceForm.repeatCount"
                              type="number"
                              :min="1"
                              :placeholder="t('Appointment.ServiceCreate.basic.repeat.fixedTimes.placeholder')"
                            />
                          </div>
                        </template>
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 服务人数限制 + 描述 -->
            <div class="section">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="t('Appointment.ServiceCreate.basic.staffLimit.label')" required>
                    <el-select v-model="serviceForm.staffLimit" :placeholder="t('Appointment.ServiceCreate.basic.staffLimit.placeholder')">
                      <el-option :label="t('Appointment.ServiceCreate.basic.staffLimit.options.1')" :value="1" />
                      <el-option :label="t('Appointment.ServiceCreate.basic.staffLimit.options.2')" :value="2" />
                      <el-option :label="t('Appointment.ServiceCreate.basic.staffLimit.options.3')" :value="3" />
                      <el-option :label="t('Appointment.ServiceCreate.basic.staffLimit.options.4')" :value="4" />
                      <el-option :label="t('Appointment.ServiceCreate.basic.staffLimit.options.5')" :value="5" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="t('Appointment.ServiceCreate.basic.description.label')">
                    <el-input
                      v-model="serviceForm.description"
                      type="textarea"
                      :rows="4"
                      :placeholder="t('Appointment.ServiceCreate.basic.description.placeholder')"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </div>

        <!-- 人员安排内容 -->
        <div v-show="activeTab === 'staff'">
          <div class="staff-section">
            <!-- 添加负责此服务员工标题 -->
            <div class="section-header">
              <span class="required-mark">*</span> {{ t('Appointment.ServiceCreate.staff.title') }}
            </div>
            
            <!-- 员工多选下拉 -->
            <div class="staff-form-row">
              <el-select 
                v-model="staffList" 
                multiple 
                :placeholder="t('Appointment.ServiceCreate.staff.placeholder')"
                filterable
                :loading="staffLoading"
                style="width: 50%;"
              >
                <el-option
                  v-for="staff in staffOptions"
                  :key="staff.value"
                  :label="staff.label"
                  :value="staff.value"
                />
                <template #empty>
                  <div style="text-align: center; padding: 8px 0;">
                    {{ t('Cms.list.no_data') }}
                  </div>
                </template>
              </el-select>
            </div>
          </div>
        </div>

        <!-- 时间安排内容 -->
        <div v-show="activeTab === 'time'">
          <el-form 
            ref="timeFormRef"
            :model="timeForm"
            label-width="160px"
            v-loading="loading"
          >
            <div class="section">
              <h3 class="section-title">{{ t('Appointment.ServiceCreate.time.title') }}</h3>
              
              <!-- 每周时间设置 -->
              <div v-for="(schedule, index) in timeForm.weeklySchedule" :key="index" class="schedule-row">
                <div class="day-label">{{ t(`Appointment.ServiceCreate.time.weekdayOptions.${getDayName(index)}`) }}</div>
                
                <div class="time-range">
                  <el-time-select
                    v-model="schedule.startTime"
                    start="00:00"
                    step="00:30"
                    end="23:30"
                    :placeholder="t('Appointment.ServiceCreate.time.timeRange.start')"
                    :disabled="schedule.isHoliday"
                  />
                  <span class="separator">-</span>
                  <el-time-select
                    v-model="schedule.endTime"
                    start="00:00"
                    step="00:30"
                    end="23:30"
                    :placeholder="t('Appointment.ServiceCreate.time.timeRange.end')"
                    :disabled="schedule.isHoliday"
                  />
                </div>

                <el-checkbox v-model="schedule.isHoliday">{{ t('Appointment.ServiceCreate.time.holiday') }}</el-checkbox>

                <!-- 休息时间段设置 -->
                <div class="break-time" v-if="!schedule.isHoliday">
                  <el-checkbox 
                    v-model="schedule.hasBreak"
                    @change="(val) => handleBreakCheckboxChange(val, index)"
                  >
                    {{ t('Appointment.ServiceCreate.time.break.label') }}
                  </el-checkbox>
                  
                  <template v-if="schedule.hasBreak">
                    <div v-for="(breakTime, breakIndex) in schedule.breakTimes" 
                         :key="breakIndex" 
                         class="break-time-range"
                    >
                      <el-time-select
                        v-model="breakTime.start"
                        start="00:00"
                        step="00:30"
                        end="23:30"
                        :placeholder="t('Appointment.ServiceCreate.time.break.start')"
                      />
                      <span class="separator">-</span>
                      <el-time-select
                        v-model="breakTime.end"
                        start="00:00"
                        step="00:30"
                        end="23:30"
                        :placeholder="t('Appointment.ServiceCreate.time.break.end')"
                      />
                      <div class="action-buttons">
                        <el-button 
                          type="text" 
                          class="delete-btn"
                          @click="handleRemoveBreak(index, breakIndex)"
                        >
                          <el-icon><Delete /></el-icon>
                        </el-button>
                        <el-button 
                          type="text" 
                          class="add-btn"
                          @click="handleAddBreakTime(index)"
                        >
                          <el-icon><Plus /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </template>
                </div>
              </div>

              <!-- 特殊时间设置 -->
              <div class="special-time">
                <div class="special-time-header">
                  <span class="title">{{ t('Appointment.ServiceCreate.time.special.title') }}</span>
                  <el-switch 
                    v-model="timeForm.enableSpecialTime"
                    @change="handleSpecialTimeChange"
                  />
                </div>

                <template v-if="timeForm.enableSpecialTime">
                  <div v-for="(specialTime, index) in timeForm.specialTimes" 
                       :key="index" 
                       class="special-time-row"
                  >
                    <el-date-picker
                      v-model="specialTime.date"
                      type="date"
                      :placeholder="t('Appointment.ServiceCreate.time.special.date')"
                      value-format="YYYY-MM-DD"
                    />
                    <el-time-select
                      v-model="specialTime.startTime"
                      start="00:00"
                      step="00:30"
                      end="23:30"
                      :placeholder="t('Appointment.ServiceCreate.time.special.start')"
                    />
                    <span class="separator">-</span>
                    <el-time-select
                      v-model="specialTime.endTime"
                      start="00:00"
                      step="00:30"
                      end="23:30"
                      :placeholder="t('Appointment.ServiceCreate.time.special.end')"
                    />
                    <div class="action-buttons">
                      <el-button 
                        type="text" 
                        class="delete-btn"
                        @click="handleRemoveSpecialTime(index)"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                      <el-button 
                        type="text" 
                        class="add-btn"
                        @click="handleAddSpecialTime"
                      >
                        <el-icon><Plus /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </el-form>
        </div>

        <!-- 设置内容 -->
        <div v-show="activeTab === 'settings'">
          <el-form 
            ref="settingsFormRef"
            :model="settingsForm"
            label-position="top"
            v-loading="loading"
          >
            <div class="section settings-section">
              <!-- 额外服务时间设置 -->
              <div class="setting-item">
                <div class="setting-label">{{ t('Appointment.ServiceCreate.settings.extraService.title') }}</div>
                <el-switch v-model="settingsForm.enableExtraService" />
              </div>
              
              <!-- 额外服务列表部分 -->
              <template v-if="settingsForm.enableExtraService">
                <div class="extra-service-container">
                  <div class="extra-service-table" v-if="settingsForm.extraServices.length > 0">
                    <!-- 表头 -->
                    <div class="table-header">
                      <div class="header-cell">{{ t('Appointment.ServiceCreate.settings.extraService.table.name') }}</div>
                      <div class="header-cell">{{ t('Appointment.ServiceCreate.settings.extraService.table.category') }}</div>
                      <div class="header-cell">{{ t('Appointment.ServiceCreate.settings.extraService.table.duration') }}</div>
                      <div class="header-cell">{{ t('Appointment.ServiceCreate.settings.extraService.table.price') }}</div>
                      <div class="header-cell">{{ t('Appointment.ServiceCreate.settings.extraService.table.minQuantity') }}</div>
                      <div class="header-cell">{{ t('Appointment.ServiceCreate.settings.extraService.table.maxQuantity') }}</div>
                    </div>
                    
                    <!-- 表格内容 -->
                    <div v-for="(service, index) in settingsForm.extraServices" :key="index" class="table-row">
                      <div class="row-cell">{{ service.name }}</div>
                      <div class="row-cell">{{ service.category_name }}</div>
                      <div class="row-cell">{{ service.duration }} {{ t('Appointment.Common.duration.minute') }}</div>
                      <div class="row-cell">{{ service.price }}</div>
                      <div class="row-cell">{{ service.min_quantity }}</div>
                      <div class="row-cell">{{ service.max_quantity }}</div>
                      <div class="row-action">
                        <el-button 
                          type="text" 
                          class="delete-btn"
                          @click="handleRemoveExtraService(index)"
                        >
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                  
                  <div class="extra-service-add" @click="openExtraServiceDialog">
                    <div class="add-button">
                      <el-icon><Plus /></el-icon>
                    </div>
                    <span class="add-text">{{ t('Appointment.ServiceCreate.settings.extraService.addButton') }}</span>
                  </div>
                </div>
              </template>
              
              <!-- 会员可见 -->
              <div class="setting-item">
                <div class="setting-label">{{ t('Appointment.ServiceCreate.settings.memberVisible') }}</div>
                <el-switch v-model="settingsForm.memberVisible" />
              </div>
            </div>
          </el-form>
        </div>

        <!-- 按钮组 -->
        <div class="form-buttons">
          <template v-if="activeTab === 'basic'">
            <el-button class="button-cancel" @click="handleCancel">{{ t('Appointment.ServiceCreate.buttons.cancel') }}</el-button>
            <el-button type="primary" @click="handleNext">{{ t('Appointment.ServiceCreate.buttons.next') }}</el-button>
          </template>
          
          <template v-else-if="activeTab === 'staff'">
            <el-button @click="handlePrev">{{ t('Appointment.ServiceCreate.buttons.prev') }}</el-button>
            <el-button type="primary" @click="handleNext">{{ t('Appointment.ServiceCreate.buttons.next') }}</el-button>
          </template>
          
          <template v-else-if="activeTab === 'time'">
            <el-button @click="handlePrev">{{ t('Appointment.ServiceCreate.buttons.prev') }}</el-button>
            <el-button type="primary" @click="handleNext">{{ t('Appointment.ServiceCreate.buttons.next') }}</el-button>
          </template>
          
          <template v-else-if="activeTab === 'settings'">
            <el-button @click="handlePrev">{{ t('Appointment.ServiceCreate.buttons.prev') }}</el-button>
            <el-button class="button-cancel" @click="handleCancel">{{ t('Appointment.ServiceCreate.buttons.cancel') }}</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              {{ t('Appointment.ServiceCreate.buttons.submit') }}
            </el-button>
          </template>
        </div>
      </div>
    </div>

    <!-- 额外服务弹窗 -->
    <el-dialog
      v-model="extraServiceDialogVisible"
      :title="t('Appointment.ServiceCreate.settings.extraService.dialog.title')"
      width="600px"
      :before-close="closeExtraServiceDialog"
      class="el-dialog-common-cls"
    >
      <el-form :model="currentExtraService" label-position="top">
        <div class="extra-service-form">
          <div class="row">
            <div class="col">
              <el-form-item :label="t('Appointment.ServiceCreate.settings.extraService.dialog.name')" required>
                <el-input v-model="currentExtraService.name" :placeholder="t('Appointment.ServiceCreate.settings.extraService.dialog.placeholder.name')"></el-input>
              </el-form-item>
            </div>
            <div class="col">
              <el-form-item :label="t('Appointment.ServiceCreate.settings.extraService.dialog.category')">
                <el-input
                  v-model="currentExtraService.category_name"
                  :placeholder="t('Appointment.ServiceCreate.settings.extraService.dialog.placeholder.category')"
                  style="width: 100%"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          
          <div class="row">
            <div class="col">
              <el-form-item :label="t('Appointment.ServiceCreate.settings.extraService.dialog.minQuantity')">
                <el-input-number v-model="currentExtraService.min_quantity" :min="0" placeholder="0"></el-input-number>
              </el-form-item>
            </div>
            <div class="col">
              <el-form-item :label="t('Appointment.ServiceCreate.settings.extraService.dialog.maxQuantity')">
                <el-input-number v-model="currentExtraService.max_quantity" :min="0" placeholder="6"></el-input-number>
              </el-form-item>
            </div>
          </div>
          
          <div class="row">
            <div class="col">
              <el-form-item :label="t('Appointment.ServiceCreate.settings.extraService.dialog.price')" required>
                <el-input-number v-model="currentExtraService.price" :min="0" :precision="2" placeholder="50"></el-input-number>
              </el-form-item>
            </div>
            <div class="col">
              <el-form-item :label="t('Appointment.ServiceCreate.settings.extraService.dialog.duration')" required>
                <el-select v-model="currentExtraService.duration" :placeholder="t('Appointment.ServiceCreate.settings.extraService.dialog.placeholder.duration')">
                  <el-option :label="`10 ${t('Appointment.Common.duration.minute')}`" value="10" />
                  <el-option :label="`15 ${t('Appointment.Common.duration.minute')}`" value="15" />
                  <el-option :label="`20 ${t('Appointment.Common.duration.minute')}`" value="20" />
                  <el-option :label="`30 ${t('Appointment.Common.duration.minute')}`" value="30" />
                  <el-option :label="`45 ${t('Appointment.Common.duration.minute')}`" value="45" />
                  <el-option :label="`60 ${t('Appointment.Common.duration.minute')}`" value="60" />
                </el-select>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeExtraServiceDialog">{{ t('Appointment.ServiceCreate.buttons.cancel') }}</el-button>
          <el-button type="primary" @click="confirmAddExtraService">{{ t('Appointment.ServiceCreate.buttons.add') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type { FormInstance, UploadFile } from 'element-plus'
import { Plus, Close, Delete, CopyDocument, Edit } from '@element-plus/icons-vue'
import { appointmentService } from '../../services/appointmentService'

// 初始化国际化
const { t } = useI18n()

// 路由
const router = useRouter()
const route = useRoute()

// 判断是否为编辑模式
const isEdit = ref(false)
const serviceId = ref<number | null>(null)

// 当前激活的标签页
const activeTab = ref('basic')

// 表单引用
const serviceFormRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)
const submitLoading = ref(false)
const categoryLoading = ref(false)
const staffLoading = ref(false)

// 分类相关状态变量
const categorySearchQuery = ref('')
const isCategorySelectOpen = ref(false)

// 员工相关状态变量
const staffSearchQuery = ref('')
const isStaffSelectOpen = ref(false)

// 分类选项接口
interface CategoryOption {
  value: string | number;
  label: string;
}

// 分类选项
const categoryOptions = ref<CategoryOption[]>([])

// 员工选项接口
interface StaffOption {
  value: string | number;
  label: string;
}

// 员工选项
const staffOptions = ref<StaffOption[]>([])

// 员工列表
const staffList = ref<string[]>([])

// 表单数据
const serviceForm = reactive({
  name: '',
  category: '',
  price: '',
  duration: '',
  hidePrice: false,
  hideDuration: false,
  enableRepeat: false,
  repeatType: '',
  repeatMode: '',
  enableFixedTimes: false,
  repeatCount: '',
  staffLimit: '',
  description: ''
})

// 表单验证规则
const serviceRules = computed(() => ({
  name: [
    { required: true, message: t('Appointment.ServiceCreate.basic.name.required'), trigger: 'blur' }
  ],
  category: [
    { required: true, message: t('Appointment.ServiceCreate.basic.category.required'), trigger: 'change' }
  ],
  price: [
    { required: true, message: t('Appointment.ServiceCreate.basic.price.required'), trigger: 'blur' }
  ],
  duration: [
    { required: true, message: t('Appointment.ServiceCreate.basic.duration.required'), trigger: 'change' }
  ],
  staffLimit: [
    { required: true, message: t('Appointment.ServiceCreate.basic.staffLimit.required'), trigger: 'change' }
  ]
}))

// 修改时间表接口
interface TimeSchedule {
  day: string
  startTime: string
  endTime: string
  isHoliday: boolean
  hasBreak: boolean
  breakTimes: Array<{
    start: string
    end: string
  }>
}

interface SpecialTime {
  date: string
  startTime: string
  endTime: string
}

// 修改时间表单数据
const timeForm = reactive({
  weeklySchedule: [
    { day: '一', startTime: '', endTime: '', isHoliday: false, hasBreak: false, breakTimes: [] },
    { day: '二', startTime: '', endTime: '', isHoliday: false, hasBreak: false, breakTimes: [] },
    { day: '三', startTime: '', endTime: '', isHoliday: false, hasBreak: false, breakTimes: [] },
    { day: '四', startTime: '', endTime: '', isHoliday: false, hasBreak: false, breakTimes: [] },
    { day: '五', startTime: '', endTime: '', isHoliday: false, hasBreak: false, breakTimes: [] },
    { day: '六', startTime: '', endTime: '', isHoliday: false, hasBreak: false, breakTimes: [] },
    { day: '日', startTime: '', endTime: '', isHoliday: false, hasBreak: false, breakTimes: [] }
  ] as TimeSchedule[],
  enableSpecialTime: false,
  specialTimes: [] as SpecialTime[]
})

// 额外服务接口
interface ExtraService {
  name: string
  category_name: string
  price: number
  duration: string
  min_quantity: number
  max_quantity: number
}

// 设置表单数据
const settingsForm = reactive({
  enableExtraService: false,
  extraServices: [] as ExtraService[],
  memberVisible: false
})

// 额外服务弹窗控制
const extraServiceDialogVisible = ref(false)

// 当前编辑的额外服务
const currentExtraService = reactive<ExtraService>({
  name: '',
  category_name: '',
  price: 50,
  duration: '10',
  min_quantity: 0,
  max_quantity: 6
})

// 取消
const handleCancel = () => {
  router.push('/appointment/services')
}

// 提交
const handleSubmit = async () => {
  if (!serviceFormRef.value) return
  
  await serviceFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error(t('Appointment.ServiceCreate.messages.formError'))
      return
    }
    
    // 检查是否选择了负责员工
    if (staffList.value.length === 0) {
      ElMessage.error(t('Appointment.ServiceCreate.staff.required'))
      activeTab.value = 'staff'
      return
    }
    
    submitLoading.value = true
    
    try {
      // 构建提交的数据
      const formData = {
        name: serviceForm.name,
        category_id: serviceForm.category, 
        price: Number(serviceForm.price),
        duration: Number(serviceForm.duration) * 60, // 转换为秒
        is_show_price: !serviceForm.hidePrice,
        is_show_duration: !serviceForm.hideDuration,
        is_repeat_service: serviceForm.enableRepeat,
        is_repeat_service_pay_type: serviceForm.repeatMode ? Number(serviceForm.repeatMode) : null,
        is_repeat_service_category: serviceForm.repeatType ? Number(serviceForm.repeatType) : null,
        is_repeat_service_count: serviceForm.enableFixedTimes ? Number(serviceForm.repeatCount) : null,
        person_count: Number(serviceForm.staffLimit),
        description: serviceForm.description || '',
        only_member_visible: settingsForm.memberVisible,
        status: true, // 默认启用
        advance_booking_time: 0, // 预约提前时间，默认为0
        
        // 添加额外服务
        extra_services: settingsForm.enableExtraService ? settingsForm.extraServices.map(service => ({
          name: service.name,
          category_name: service.category_name,
          price: service.price,
          duration: Number(service.duration) * 60, // 转换为秒
          max_quantity: service.max_quantity,
          min_quantity: service.min_quantity
        })) : [],
        
        // 添加时间设置
        schedules: timeForm.weeklySchedule
          .filter(schedule => {
            // 如果是休息日，直接返回 true
            if (schedule.isHoliday) {
              return true;
            }
            // 如果不是休息日，则需要检查时间是否都填写了
            return schedule.startTime && schedule.endTime;
          })
          .map((schedule, index) => {
            const dayOfWeek = index + 1; // 1-7 表示周一到周日
            
            return {
              day_of_week: dayOfWeek,
              start_time: schedule.startTime + ':00',
              end_time: schedule.endTime + ':00',
              rest_time_list: schedule.hasBreak ? schedule.breakTimes
                .filter(breakTime => breakTime.start && breakTime.end) // 过滤掉未填写完整的休息时间
                .map(breakTime => ({
                  start_time: breakTime.start + ':00',
                  end_time: breakTime.end + ':00'
                })) : [],
              is_rest_day: schedule.isHoliday
            };
          }),
        
        // 添加特殊时间
        special_schedules: timeForm.enableSpecialTime 
          ? timeForm.specialTimes
              .filter(specialTime => specialTime.date && specialTime.startTime && specialTime.endTime) // 过滤掉未填写完整的特殊时间
              .map(specialTime => ({
                start_time: `${specialTime.date} ${specialTime.startTime}:00`,
                end_time: `${specialTime.date} ${specialTime.endTime}:00`
              }))
          : [],
        
        // 员工列表
        staff_ids: staffList.value.map(id => Number(id))
      }
      
      if (isEdit.value && serviceId.value) {
        // 更新服务
        await appointmentService.updateService(serviceId.value, formData);
        ElMessage.success(t('Appointment.ServiceCreate.messages.updateSuccess'))
      } else {
        // 创建服务
        await appointmentService.createService(formData);
        ElMessage.success(t('Appointment.ServiceCreate.messages.createSuccess'))
      }
      
      router.push('/appointment/services')
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || t('Appointment.ServiceCreate.messages.createFailed'))
    } finally {
      submitLoading.value = false
    }
  })
}

// 添加休息时间段
const handleAddBreakTime = (dayIndex: number) => {
  const schedule = timeForm.weeklySchedule[dayIndex]
  schedule.breakTimes.push({
    start: '',
    end: ''
  })
}

// 删除休息时间段
const handleRemoveBreak = (dayIndex: number, breakIndex: number) => {
  const schedule = timeForm.weeklySchedule[dayIndex]
  schedule.breakTimes.splice(breakIndex, 1)
  if (schedule.breakTimes.length === 0) {
    schedule.hasBreak = false
  }
}

// 添加特殊时间
const handleAddSpecialTime = () => {
  timeForm.specialTimes.push({
    date: '',
    startTime: '',
    endTime: ''
  })
}

// 删除特殊时间
const handleRemoveSpecialTime = (index: number) => {
  timeForm.specialTimes.splice(index, 1)
  if (timeForm.specialTimes.length === 0) {
    timeForm.enableSpecialTime = false
  }
}

// 处理休息时间复选框变化
const handleBreakCheckboxChange = (val: boolean, dayIndex: number) => {
  const schedule = timeForm.weeklySchedule[dayIndex]
  if (val && schedule.breakTimes.length === 0) {
    // 当选中且没有休息时间段时，自动添加一个
    schedule.breakTimes.push({
      start: '',
      end: ''
    })
  }
}

// 处理特殊时间开关变化
const handleSpecialTimeChange = (val: boolean) => {
  if (val && timeForm.specialTimes.length === 0) {
    // 当开启且没有特殊时间时，自动添加一个
    timeForm.specialTimes.push({
      date: '',
      startTime: '',
      endTime: ''
    })
  }
}

// 打开额外服务弹窗
const openExtraServiceDialog = () => {
  // 重置当前编辑的额外服务
  currentExtraService.name = ''
  currentExtraService.category_name = ''
  currentExtraService.min_quantity = 0
  currentExtraService.max_quantity = 6
  currentExtraService.price = 50
  currentExtraService.duration = '10'
  
  extraServiceDialogVisible.value = true
}

// 关闭额外服务弹窗
const closeExtraServiceDialog = () => {
  extraServiceDialogVisible.value = false
}

// 确认添加额外服务
const confirmAddExtraService = () => {
  // 添加额外服务
  settingsForm.extraServices.push({
    name: currentExtraService.name,
    category_name: currentExtraService.category_name,
    min_quantity: currentExtraService.min_quantity,
    max_quantity: currentExtraService.max_quantity,
    price: currentExtraService.price,
    duration: currentExtraService.duration
  })
  
  // 关闭弹窗
  extraServiceDialogVisible.value = false
}

// 删除额外服务
const handleRemoveExtraService = (index: number) => {
  settingsForm.extraServices.splice(index, 1)
}

// 处理分类下拉框可见性变化
const handleCategoryVisibleChange = (visible: boolean) => {
  if (visible) {
    // 下拉框打开时
    isCategorySelectOpen.value = true
    
    // 获取分类列表，如果没有数据则请求
    if (categoryOptions.value.length <= 0) {
      fetchCategories()
    }
  } else {
    // 下拉框关闭时，标记关闭状态
    isCategorySelectOpen.value = false
    
    // 当下拉框关闭时，延迟一会儿再重新请求全量数据
    setTimeout(() => {
      if (!isCategorySelectOpen.value) {
        fetchCategories()
      }
    }, 200)
  }
}

// 获取分类列表
const fetchCategories = async (keyword = '') => {
  categoryLoading.value = true
  try {
    const params: any = {
      page: 1,
      limit: 100,
    }
    
    // 添加搜索关键词
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim()
    }
    
    const response = await appointmentService.getServiceCategories(params)
    if (response.data && response.data.code === 200) {
      categoryOptions.value = response.data.data.items.map((item: any) => ({
        value: item.id,
        label: item.name
      }))
    }
  } catch (error) {
  } finally {
    categoryLoading.value = false
  }
}

// 搜索服务分类
const searchCategories = (query: string) => {
  if (!isCategorySelectOpen.value) return
  
  categorySearchQuery.value = query
  // 延迟执行搜索，避免频繁请求
  if (query) {
    fetchCategories(query)
  }
}

// 处理员工下拉框可见性变化
const handleStaffVisibleChange = (visible: boolean) => {
  if (visible) {
    // 下拉框打开时
    isStaffSelectOpen.value = true
    
    // 获取员工列表，如果没有数据则请求
    if (staffOptions.value.length <= 0) {
      fetchStaff()
    }
  } else {
    // 下拉框关闭时，标记关闭状态
    isStaffSelectOpen.value = false
    
    // 当下拉框关闭时，延迟一会儿再重新请求全量数据
    setTimeout(() => {
      if (!isStaffSelectOpen.value) {
        fetchStaff()
      }
    }, 200)
  }
}

// 获取员工列表
const fetchStaff = async (keyword = '') => {
  staffLoading.value = true
  try {
    const params: any = {
      page: 1,
      limit: 100,
    }
    
    // 添加搜索关键词
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim()
    }
    
    const response = await appointmentService.getStaffList(params)
    if (response.data && response.data.code === 200) {
      staffOptions.value = response.data.data.items.map((item: any) => ({
        value: item.id,
        label: item.name
      }))
    }
  } catch (error) {
  } finally {
    staffLoading.value = false
  }
}

// 搜索员工
const searchStaff = (query: string) => {
  if (!isStaffSelectOpen.value) return
  
  staffSearchQuery.value = query
  // 延迟执行搜索，避免频繁请求
  if (query) {
    fetchStaff(query)
  }
}

// 处理下一步按钮
const handleNext = () => {
  if (activeTab.value === 'basic') {
    // 检查基础信息表单
    serviceFormRef.value?.validate((valid) => {
      if (valid) {
        activeTab.value = 'staff'
      } else {
        ElMessage.error(t('Appointment.ServiceCreate.basic.message.basicUp'))
      }
    })
  } else if (activeTab.value === 'staff') {
    // 检查是否选择了负责员工
    if (staffList.value.length === 0) {
      // ElMessage.error('请至少选择一名负责此服务的员工')
      ElMessage.error(t('Appointment.ServiceCreate.basic.message.basicStaff'))
      return
    }
    activeTab.value = 'time'
  } else if (activeTab.value === 'time') {
    activeTab.value = 'settings'
  }
}

// 处理上一步按钮
const handlePrev = () => {
  if (activeTab.value === 'staff') {
    activeTab.value = 'basic'
  } else if (activeTab.value === 'time') {
    activeTab.value = 'staff'
  } else if (activeTab.value === 'settings') {
    activeTab.value = 'time'
  }
}

// 获取服务详情
const fetchServiceDetail = async (id: number) => {
  loading.value = true
  try {
    const { data } = await appointmentService.getServiceDetail(id)
    if (data.code === 200) {
      const serviceData: any = data.data

      // 填充基础表单数据
      serviceForm.name = serviceData.name
      serviceForm.category = serviceData.category_id
      serviceForm.price = serviceData.price.toString()
      serviceForm.duration = (serviceData.duration / 60).toString() // 秒转分钟
      serviceForm.hidePrice = serviceData.is_show_price === 0 || serviceData.is_show_price === false
      serviceForm.hideDuration = serviceData.is_show_duration === 0 || serviceData.is_show_duration === false
      serviceForm.enableRepeat = serviceData.is_repeat_service === 1 || serviceData.is_repeat_service === true
      serviceForm.repeatType = serviceData.is_repeat_service_category?.toString() || ''
      serviceForm.repeatMode = serviceData.is_repeat_service_pay_type?.toString() || ''
      serviceForm.enableFixedTimes = !!serviceData.is_repeat_service_count
      serviceForm.repeatCount = serviceData.is_repeat_service_count?.toString() || ''
      serviceForm.staffLimit = serviceData.person_count.toString()
      serviceForm.description = serviceData.description || ''
      
      // 填充员工数据
      if (serviceData.staff_ids && Array.isArray(serviceData.staff_ids)) {
        staffList.value = serviceData.staff_ids
      }

      // 填充设置表单数据
      settingsForm.memberVisible = serviceData.only_member_visible === 1 || serviceData.only_member_visible === true
      
      // 填充额外服务数据
      if (serviceData.extraServices && serviceData.extraServices.length > 0) {
        settingsForm.enableExtraService = true
        settingsForm.extraServices = serviceData.extraServices.map((service: any) => ({
          name: service.name,
          category_name: service.category_name,
          price: Number(service.price),
          duration: (Number(service.duration) / 60).toString(), // 秒转分钟
          min_quantity: Number(service.min_quantity),
          max_quantity: Number(service.max_quantity)
        }))
      }

      // 填充时间安排数据
      if (serviceData.schedules && serviceData.schedules.length > 0) {
        // 初始化一个映射表，帮助将后端的day_of_week映射到我们的数组索引
        const dayMap: {[key: number]: number} = {
          1: 0, // 周一对应索引0
          2: 1,
          3: 2,
          4: 3,
          5: 4,
          6: 5,
          7: 6  // 周日对应索引6
        }

        // 更新每个工作日的设置
        serviceData.schedules.forEach((schedule: any) => {
          const index = dayMap[schedule.day_of_week]
          if (index !== undefined) {
            const weekdaySchedule = timeForm.weeklySchedule[index]
            weekdaySchedule.isHoliday = schedule.is_rest_day === 1 || schedule.is_rest_day === true
            if (!weekdaySchedule.isHoliday) {
              weekdaySchedule.startTime = schedule.start_time.substring(0, 5) // 去掉秒
              weekdaySchedule.endTime = schedule.end_time.substring(0, 5)
              
              if (schedule.rest_time_list && schedule.rest_time_list.length > 0) {
                weekdaySchedule.hasBreak = true
                weekdaySchedule.breakTimes = schedule.rest_time_list.map((restTime: any) => ({
                  start: restTime.start_time.substring(0, 5),
                  end: restTime.end_time.substring(0, 5)
                }))
              }
            }
          }
        })
      }

      // 填充特殊时间安排
      if (serviceData.specialSchedules && serviceData.specialSchedules.length > 0) {
        timeForm.enableSpecialTime = true
        timeForm.specialTimes = serviceData.specialSchedules.map((specialTime: any) => {
          // 解析日期时间字符串，假设格式为 "YYYY-MM-DD HH:MM:SS"
          const dateTimeStart = specialTime.start_time.split(' ')
          const dateTimeEnd = specialTime.end_time.split(' ')
          
          return {
            date: dateTimeStart[0], // 日期部分
            startTime: dateTimeStart[1]?.substring(0, 5) || '', // 时间部分，去掉秒
            endTime: dateTimeEnd[1]?.substring(0, 5) || ''
          }
        })
      }
    }
  } catch (error) {
  } finally {
    loading.value = false
  }
}

// 获取星期几的英文名称
const getDayName = (index: number): string => {
  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  return days[index];
};

// 初始化
onMounted(() => {
  // 获取服务分类
  fetchCategories()
  // 获取员工列表
  fetchStaff()

  // 判断是否有ID参数，如果有则是编辑模式
  const id = route.params.id
  if (id && !Array.isArray(id)) {
    isEdit.value = true
    serviceId.value = parseInt(id)
    fetchServiceDetail(serviceId.value)
  }
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  
  .module-header {
    flex-shrink: 0;
    padding: 16px;
    
    h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
      color: #303133;
    }
  }
  
  .module-con {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .service-tabs {
      background: #fff;
      border-radius: 4px;
      padding: 0 20px;
      border-bottom: 1px solid #ebeef5;
      
      :deep(.el-tabs__header) {
        margin: 0;
      }
      
      :deep(.el-tabs__nav-wrap::after) {
        display: none;
      }
      
      :deep(.el-tabs__item) {
        height: 60px;
        line-height: 60px;
        font-size: 14px;
        color: #606266;
        
        &.is-active {
          color: #002140;
          font-weight: 500;
        }
      }
    }
    
    .box {
      padding: 20px;
      border-radius: 0;

      .service-image-upload {
        .upload-area {
          width: 140px;
          height: 140px;
          border: 1px dashed #d9d9d9;
          border-radius: 4px;
          cursor: pointer;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          position: relative;
          overflow: hidden;
          
          &:hover {
            border-color: #409eff;
            
            .image-hover-mask {
              opacity: 1;
            }
          }
          
          .preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
          }
          
          .image-hover-mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s;
            color: #fff;
            
            .el-icon {
              font-size: 20px;
              margin-bottom: 8px;
            }
            
            span {
              font-size: 14px;
            }
          }
          
          .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #8c939d;
            
            .el-icon {
              font-size: 28px;
              margin-bottom: 8px;
            }
            
            span {
              font-size: 14px;
            }
          }
        }
      }

      .display-options {
        display: flex;
        gap: 24px;
      }

      .form-buttons {
        display: flex;
        justify-content: center;
        margin-top: 24px;
        padding-top: 24px;
      }

      .staff-section {
        max-width: 1000px;
        padding: 20px 0;
        
        .section-header {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 20px;
          
          .required-mark {
            color: #f56c6c;
            margin-right: 4px;
          }
        }
        
        .staff-form-row {
          margin-bottom: 20px;
        }
      }
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 20px;
  color: #303133;
}

.schedule-row {
  margin-bottom: 20px;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }

  .day-label {
    margin-bottom: 12px;
    font-weight: 500;
    color: #303133;
  }

  .time-range {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;

    .separator {
      color: #909399;
    }
  }

  .break-time {
    margin-top: 12px;

    .break-time-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
    }

    .break-time-range {
      margin-top: 12px;
      display: flex;
      align-items: center;
      gap: 12px;
      
      &:not(:last-child) {
        margin-bottom: 12px;
      }
    }
  }
}

.special-time {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;

  .special-time-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
  }

  .special-time-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.break-time-range,
.special-time-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 4px;

    .delete-btn,
    .add-btn {
      padding: 4px;
      
      .el-icon {
        font-size: 16px;
      }
    }

    .delete-btn {
      color: #909399;
      &:hover {
        color: #f56c6c;
      }
    }

    .add-btn {
      color: #909399;
      &:hover {
        color: #409eff;
      }
    }
  }
}

.settings-section {
  max-width: 800px;
  
  .setting-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 20px;
    gap: 16px;
    
    .setting-label {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .setting-item-content {
    margin-bottom: 20px;
    margin-top: -10px;
    max-width: 500px;
  }
  
  .extra-service-container {
    margin: 20px 0 30px;
    background-color: #f9fafb;
    border-radius: 8px;
    padding: 20px;
  }
  
  .extra-service-table {
    background-color: #fff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin-bottom: 16px;
    
    .table-header {
      display: flex;
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;
      
      .header-cell {
        flex: 1;
        padding: 12px 16px;
        font-size: 14px;
        font-weight: 500;
        color: #606266;
        text-align: left;
      }
    }
    
    .table-row {
      display: flex;
      border-bottom: 1px solid #ebeef5;
      position: relative;
      align-items: center;
      
      &:last-child {
        border-bottom: none;
      }
      
      .row-cell {
        flex: 1;
        padding: 16px;
        font-size: 14px;
        color: #606266;
        text-align: left;
      }
      
      .row-action {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        
        .delete-btn {
          color: #909399;
          
          &:hover {
            color: #f56c6c;
          }
          
          .el-icon {
            font-size: 16px;
          }
        }
      }
    }
  }
  
  .extra-service-add {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    
    .add-button {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #dcdfe6;
      
      .el-icon {
        font-size: 16px;
        color: #606266;
      }
      
      &:hover {
        background-color: #e6f1fc;
        border-color: #409eff;
        
        .el-icon {
          color: #409eff;
        }
      }
    }
    
    .add-text {
      font-size: 14px;
      color: #606266;
      
      &:hover {
        color: #409eff;
      }
    }
  }
}

// 优化弹窗内的表单样式
.extra-service-form {
  .row {
    display: flex;
    margin-bottom: 16px;
    gap: 20px;
    
    .col {
      flex: 1;
      
      :deep(.el-form-item__label) {
        padding-bottom: 8px;
      }
      
      :deep(.el-input),
      :deep(.el-input-number),
      :deep(.el-select) {
        width: 100%;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
