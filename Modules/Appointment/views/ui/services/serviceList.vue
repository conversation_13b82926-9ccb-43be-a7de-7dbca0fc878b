<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
      <h1></h1>
      <div class="btn-list">
        <el-button class="button-no-border" @click="handleBatchDisable">
          <span>{{ t('Appointment.ServiceList.messages.disableAll') }}</span>
        </el-button>
        <el-button class="button-no-border" @click="handleBatchEnable">
          <span>{{ t('Appointment.ServiceList.messages.enableAll') }}</span>
        </el-button>
        <FilterPopover v-model="showFilterDropdown">
          <template #reference>
            <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
              <el-icon>
                <img :src="$asset('Cms/Asset/FilterIcon.png')" alt="FilterIcon" />
              </el-icon>
              <span>{{ $t('Cms.list.filter') }}</span>
            </el-button>
          </template>
          <el-form :inline="true" :model="searchForm" label-position="top" style="width: 100%;">
            <el-form-item :label="t('Cms.list.filter')" style="width: 100%;">
              <el-input 
                v-model="searchForm.keyword"
                :placeholder="t('Appointment.ServiceList.search.placeholder')"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="flex justify-center">
              <el-button class="el-button-default" @click="handleReset">
                <el-icon><Refresh /></el-icon>
                <span>{{ $t('Cms.list.refresh') }}</span>
              </el-button>
              <el-button class="button-no-border" type="primary" @click="handleSearch">
                <el-icon><Filter /></el-icon>
                <span>{{ $t('Cms.list.filter') }}</span>
              </el-button>
            </div>
          </template>
        </FilterPopover>
        <el-button type="primary" @click="handleCreateCategory">
          <el-icon size="16"><FolderAdd /></el-icon>
          <span>{{ t('Appointment.ServiceList.button_texts.createCategory') }}</span>
        </el-button>
        <el-button type="primary" @click="handleCreateService">
          <el-icon size="16"><img :src="$asset('Cms/Asset/PlusIcon.png')" alt="PlusIcon" /></el-icon>
          <span>{{ t('Appointment.ServiceList.button_texts.createService') }}</span>
        </el-button>
        
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 表格 -->
        <el-table
          v-loading="loading"
          :data="tableData"
        >
          <el-table-column prop="name" :label="t('Appointment.ServiceList.table.serviceName')" min-width="180" />
          <el-table-column prop="category_name" :label="t('Appointment.ServiceList.table.category')" width="200" />
          <el-table-column prop="price" :label="t('Appointment.ServiceList.table.price')" width="180">
            <template #default="{ row }">
              HKD {{ row.price }}
            </template>
          </el-table-column>
          <el-table-column prop="duration" :label="t('Appointment.ServiceList.table.duration')" width="180">
            <template #default="{ row }">
              {{ formatDuration(row?.duration) }}
            </template>
          </el-table-column>
          <el-table-column prop="enabled" :label="t('Appointment.ServiceList.table.enabled')" width="180">
            <template #default="{ row }">
              <el-switch
                v-model="row.enabled"
                @change="(val) => handleStatusChange(row, val)"
              />
            </template>
          </el-table-column>
          <el-table-column :label="t('Appointment.ServiceList.table.operations')" width="120" fixed="right">
            <template #default="{ row }">
              <div class="bwms-operate-btn-box">
                <div class="bwms-operate-btn"
                @click="handleEdit(row)"
              >
                <el-icon size="15"><img :src="$asset('Cms/Asset/EditIcon.png')" alt="EditIcon" /></el-icon>
              </div>
              <div class="bwms-operate-btn"
                @click="handleDelete(row)"
              >
                <el-icon size="16"><img :src="$asset('Cms/Asset/DeleteIcon.png')" alt="DeleteIcon" /></el-icon>
              </div>
              </div>
            </template>
          </el-table-column>
        </el-table>       
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pagination.pageSize"
              class="page-size-select"
              @change="handleSizeChange"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
              />
            </el-select>
            <span class="total-text">{{ t('Cms.pagination.total_items', { total: pagination.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.page"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              background
              layout="prev, pager, next"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 服务分类创建对话框 -->
    <el-dialog
      v-model="categoryDialogVisible"
      :title="t('Appointment.ServiceList.category.dialog.title')"
      width="500px"
      :before-close="closeCategoryDialog"
      class="el-dialog-common-cls"
    >
      <el-form 
        :model="categoryForm" 
        :rules="categoryRules" 
        ref="categoryFormRef" 
        label-position="top"
      >
        <el-form-item :label="t('Appointment.ServiceList.category.dialog.name')" prop="name" required>
          <el-input 
            v-model="categoryForm.name" 
            :placeholder="t('Appointment.ServiceList.category.dialog.namePlaceholder')"
          ></el-input>
        </el-form-item>
        
        <el-form-item :label="t('Appointment.ServiceList.category.dialog.description')" prop="description">
          <el-input 
            v-model="categoryForm.description" 
            type="textarea" 
            :rows="3" 
            :placeholder="t('Appointment.ServiceList.category.dialog.descriptionPlaceholder')"
          ></el-input>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeCategoryDialog">{{ t('Appointment.ServiceList.buttons.cancel') }}</el-button>
          <el-button type="primary" @click="confirmCreateCategory" :loading="categorySubmitLoading">
            {{ t('Appointment.ServiceList.buttons.submit') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, FolderAdd, ArrowDown, Edit, Delete, Filter, Refresh } from '@element-plus/icons-vue'
import { appointmentService } from '../../services/appointmentService'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

// 添加路由实例
const router = useRouter()
// 添加国际化实例
const { t } = useI18n()

// 添加筛选下拉框状态
const showFilterDropdown = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 定义服务类型接口
interface Service {
  id: number
  name: string
  category_name: string | number // 可能是ID或名称
  category_id?: number
  price: number | string
  duration: number
  description?: string
  is_show_price?: number
  is_show_duration?: number
  status?: number
  enabled: boolean // 前端状态展示
  created_at?: string
  updated_at?: string
}

// 表格数据
const tableData = ref<Service[]>([])

const loading = ref(false)

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 分类对话框相关
const categoryDialogVisible = ref(false)
const categorySubmitLoading = ref(false)
const categoryFormRef = ref<FormInstance>()

// 分类表单数据
const categoryForm = reactive({
  name: '',
  description: ''
})

// 分类表单验证规则
const categoryRules = reactive({
  name: [
    { required: true, message: t('Appointment.ServiceList.category.dialog.nameRequired'), trigger: 'blur' },
    { min: 2, max: 50, message: t('Appointment.ServiceList.category.dialog.nameLength'), trigger: 'blur' }
  ]
})

// 查询
const handleSearch = () => {
  pagination.page = 1
  showFilterDropdown.value = false
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  showFilterDropdown.value = false
  handleSearch()
}

// 新建分类
const handleCreateCategory = () => {
  // 重置表单
  categoryForm.name = ''
  categoryForm.description = ''
  
  // 显示对话框
  categoryDialogVisible.value = true
}

// 新增服务
const handleCreateService = () => {
  router.push({
    name: 'ServiceCreate'
  })
}

// 编辑服务
const handleEdit = (row: Service) => {
  router.push({
    name: 'ServiceEdit',
    params: { id: row.id.toString() }
  })
}

// 删除服务
const handleDelete = (row: Service) => {
  ElMessageBox.confirm(
    t('Appointment.ServiceList.messages.deleteConfirm'),
    t('Appointment.ServiceList.messages.deleteTitle'),
    {
      confirmButtonText: t('Appointment.ServiceList.buttons.submit'),
      cancelButtonText: t('Appointment.ServiceList.buttons.cancel'),
      type: 'warning'
    }
  ).then(async () => {
    try {
      await appointmentService.deleteService(row.id)
      ElMessage.success(t('Appointment.ServiceList.messages.deleteSuccess'))
      fetchData()
    } catch (error: any) {
      ElMessage.error(error.response.data.message || t('Appointment.ServiceList.messages.deleteFailed'))
    }
  })
}

// 批量停用
const handleBatchDisable = async () => {
  try {
    loading.value = true
    await appointmentService.closeAllServices()
    ElMessage.success(t('Appointment.ServiceList.messages.disableSuccess'))
    fetchData()
  } catch (error) {
    ElMessage.error(t('Appointment.ServiceList.messages.disableFailed'))
  } finally {
    loading.value = false
  }
}

// 批量开启
const handleBatchEnable = async () => {
  try {
    loading.value = true
    await appointmentService.openAllServices()
    ElMessage.success(t('Appointment.ServiceList.messages.enableSuccess'))
    fetchData()
  } catch (error) {
    ElMessage.error(t('Appointment.ServiceList.messages.enableFailed'))
  } finally {
    loading.value = false
  }
}

// 状态切换
const handleStatusChange = async (row: Service, status: boolean) => {
  // try {
  //   await appointmentService.updateService(row.id, { enabled: status })
  //   ElMessage.success(status ? '已开启' : '已停用')
  // } catch (error) {
  //   row.enabled = !status // 恢复状态
  //   ElMessage.error('操作失败')
  // }
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchData()
}

// 页码改变
const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const { data } = await appointmentService.getServiceList({
      page: pagination.page,
      limit: pagination.pageSize,
      keyword: searchForm.keyword
    })
    
    if (data.code === 200) {
      // 更新为正确的数据结构
      tableData.value = data.data.items.map((item: any) => {
        return {
          id: item.id,
          name: item.name,
          category_name: item.category_name || '--', // 可能需要转换为分类名称
          price: item.price,
          duration: item.duration, // 保持原始秒数，不再转换
          description: item.description,
          enabled: item.status === 1
        }
      })
      pagination.total = data.data.total
    }
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 格式化时长
const formatDuration = (seconds: number) => {
  if (!seconds) return `0 ${t('Appointment.Common.duration.second')}`
  
  // 计算小时和分钟
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return minutes > 0 
      ? `${hours} ${t('Appointment.Common.duration.hour')} ${minutes} ${t('Appointment.Common.duration.minute')}`
      : `${hours} ${t('Appointment.Common.duration.hour')}`
  }
  
  if (minutes > 0) {
    return `${minutes} ${t('Appointment.Common.duration.minute')}`
  }
  
  return `${seconds} ${t('Appointment.Common.duration.second')}`
}

// 关闭分类创建对话框
const closeCategoryDialog = () => {
  categoryDialogVisible.value = false
}

// 确认创建分类
const confirmCreateCategory = async () => {
  if (!categoryFormRef.value) return
  
  await categoryFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error(t('Appointment.ServiceList.messages.formError'))
      return
    }
    
    categorySubmitLoading.value = true
    
    try {
      const categoryData = {
        name: categoryForm.name,
        description: categoryForm.description || '',
        sort: 1,
        status: true
      }
      
      await appointmentService.createServiceCategory(categoryData)
      
      ElMessage.success(t('Appointment.ServiceList.messages.createCategorySuccess'))
      closeCategoryDialog()
      fetchData()
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || t('Appointment.ServiceList.messages.createCategoryFailed'))
    } finally {
      categorySubmitLoading.value = false
    }
  })
}

// 初始化
onMounted(() => {
  // 注释掉实际的数据获取，使用模拟数据
  fetchData()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  
  .module-header {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
      color: #303133;
    }
  }
  
  .module-con {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
  }
}

.pagination {
  display: flex;
  justify-content: space-between;
}
</style>
