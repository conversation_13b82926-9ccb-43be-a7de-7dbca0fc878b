<template>
  <el-drawer
    v-model="drawerVisible"
    :title="t('Appointment.PaymentList.detail.title')"
    :size="680"
    :with-header="true"
    destroy-on-close
    direction="rtl"
    :loading="loading"
  >
    <template #header>
      <div class="drawer-header">
        <h4 class="drawer-title">{{ t('Appointment.PaymentList.detail.title') }}</h4>
      </div>
    </template>

    <div class="payment-detail-container">
      <div class="payment-content">
        <!-- 预约信息卡片 -->
        <div class="info-card">
          <div class="card-grid">
            <div class="grid-item">
              <div class="item-label">{{ t('Appointment.PaymentList.table.staffName') }}</div>
              <div class="item-value">{{ props.detailData.staff_name }}</div>
            </div>
            <div class="grid-item">
              <div class="item-label">{{ t('Appointment.PanelList.table.location') }}</div>
              <div class="item-value">{{ props.detailData.location }}</div>
            </div>
            <div class="grid-item">
              <div class="item-label">{{ t('Appointment.PaymentList.table.serviceName') }}</div>
              <div class="item-value">{{ props.detailData.service_name }}</div>
            </div>
            <div class="grid-item">
              <div class="item-label">{{ t('Appointment.PaymentList.table.appointmentTime') }}</div>
              <div class="item-value">{{ formatDateTime(props.detailData.appointment_time) }}</div>
            </div>
          </div>
        </div>

        <!-- 客户信息 -->
        <div class="section-title">{{ t('Appointment.PaymentList.detail.customerInfo') }}</div>
        <div class="customer-section">
          <div class="customer-info">
            <img class="info-avatar" 
              :src="props.detailData.customer_avatar || avatarUrl" 
              @error="handleAvatarError"
            />
            <div class="customer-details">
              <div class="customer-name">{{ props.detailData.customer_name }}</div>
              <div class="customer-email">{{ props.detailData.customer_email }}</div>
            </div>
          </div>
          <div class="payment-info">
            <div class="payment-row payment-methods-row">
              <div class="payment-method">
                <div class="info-label">{{ t('Appointment.PaymentList.detail.paymentMethod') }}</div>
                <div class="info-value">{{ props.detailData.payment_method }}</div>
              </div>
              <div class="payment-status">
                <div class="info-label">{{ t('Appointment.PaymentList.detail.paymentStatus') }}</div>
                <div class="info-value">
                  <el-tag :type="getPaymentStatusType(props.detailData.payment_status)" size="small">
                    {{ getPaymentStatusText(props.detailData.payment_status) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 付款详情 -->
        <div class="payment-details-section">
          <div class="section-title">{{ t('Appointment.PaymentList.detail.paymentDetails') }}</div>
          <div class="payment-item">
            <div class="item-label">{{ t('Appointment.PaymentList.detail.servicePrice') }}</div>
            <div class="item-value">${{ formatAmount(parseFloat(props.detailData.original_price)) }}</div>
          </div>
          <div class="payment-item">
            <div class="item-label">{{ t('Appointment.PaymentList.detail.discount') }}</div>
            <div class="item-value">${{ formatAmount(parseFloat(props.detailData.discount_amount)) }}</div>
          </div>
          <div class="payment-item total">
            <div class="item-label">{{ t('Appointment.PaymentList.detail.total') }}</div>
            <div class="item-value">${{ formatAmount(parseFloat(props.detailData.final_price)) }}</div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="drawer-footer">
        <el-button @click="closeDrawer">{{ t('Appointment.PaymentList.buttons.cancel') }}</el-button>
        <el-button type="primary" @click="handleEdit">{{ t('Appointment.CustomerList.edit') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { appointmentService } from '../../services/appointmentService'
import { useI18n } from 'vue-i18n'

// 使用i18n
const { t } = useI18n()

// 定义付款记录接口
interface PaymentRecord {
  id: number
  appointment_id: number
  appointment_time: string
  customer_name: string
  customer_email: string
  customer_avatar: string
  staff_name: string
  service_name: string
  payment_method: string
  payment_status: number
  payment_status_text: string
  created_at: string
  updated_at: string
  location: string
  original_price: string
  discount_amount: string
  final_price: string
  remark: string
}

const props = defineProps({
  paymentId: {
    type: [Number, String, null],
    required: false,
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  },
  detailData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'refresh', 'edit'])

// 创建内部可控制的抽屉状态
const drawerVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 付款状态枚举
enum PaymentStatus {
  PENDING = 0,    // 未支付
  PAID = 1,       // 已支付
  CANCELLED = 2,  // 支付失败
}

// 路由
const router = useRouter()

// 加载状态
const loading = ref(false)

// 在 script setup 部分添加
const avatarUrl = ref('https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png')

// 获取付款状态文本
const getPaymentStatusText = (status: number) => {
  const texts: Record<number, string> = {
    [PaymentStatus.PENDING]: t('Appointment.PaymentList.detail.status.pending'),
    [PaymentStatus.PAID]: t('Appointment.PaymentList.detail.status.paid'),
    [PaymentStatus.CANCELLED]: t('Appointment.PaymentList.detail.status.failed')
  }
  return texts[status] || ''
}

// 获取付款状态标签类型
const getPaymentStatusType = (status: number) => {
  const types: Record<number, string> = {
    [PaymentStatus.PENDING]: 'warning',
    [PaymentStatus.PAID]: 'success',
    [PaymentStatus.CANCELLED]: 'danger'
  }
  return types[status] || ''
}

// 格式化日期时间
const formatDateTime = (datetime: string | null) => {
  if (!datetime) return '--'
  return dayjs(datetime).format('YYYY-MM-DD HH:mm')
}

// 格式化金额
const formatAmount = (amount: number) => {
  return amount ? amount.toFixed(2) : '0.00'
}

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false
}

// 编辑付款
const handleEdit = () => {
  emit('edit', props.detailData)
}

// 添加头像加载错误处理函数
const handleAvatarError = (e: Event) => {
  const imgElement = e.target as HTMLImageElement
  imgElement.src = avatarUrl.value
}
</script>

<style lang="scss" scoped>
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .drawer-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
}

.payment-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  
  .payment-content {
    flex: 1;
    overflow-y: auto;
    padding-top: 0;
    margin-top: -20px;
    padding-bottom: 0;
    
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(64, 158, 255, 0.3);
      border-radius: 3px;
      
      &:hover {
        background-color: rgba(64, 158, 255, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 3px;
    }
  }
  
  .info-card {
    background-color: #f5f7fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    
    .card-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      
      .grid-item {
        .item-label {
          font-size: 14px;
          color: #606266;
          margin-bottom: 4px;
        }
        
        .item-value {
          font-size: 16px;
          color: #000;
        }
      }
    }
  }
  
  .section-title {
    font-size: 16px;
    color: #000;
    margin: 20px 0 16px;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  .customer-section {
    background-color: #f5f7fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    
    .customer-info {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      .info-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        background-color: #f5f7fa;
      }
      
      .customer-details {
        margin-left: 12px;
        
        .customer-name {
          font-size: 16px;
          color: #0000;
        }
        
        .customer-email {
          font-size: 16px;
          color: #000;
        }
      }
    }
    
    .payment-info {
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      padding-top: 12px;
      
      .payment-methods-row {
        display: flex;
        justify-content: space-between;
        
        .payment-method,
        .payment-status {
          flex: 1;
          
          .info-label {
            font-size: 16px;
            color: #000;
            margin-bottom: 8px;
          }
          
          .info-value {
            font-size: 16px;
            color: #000;
          }
        }
        
        .payment-status {
          text-align: right;
          
          .info-label {
            text-align: right;
          }
          
          .info-value {
            display: flex;
            justify-content: flex-end;
          }
        }
      }
    }
  }
  
  .payment-details-section {
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #ebeef5;
    padding: 16px;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    
    .payment-item {
      display: flex;
      justify-content: space-between;
      padding: 10px 0;
      
      &:not(:last-child) {
        border-bottom: 1px solid #ebeef5;
      }
      
      .item-label {
        font-size: 16px;
        color: #000;
      }
      
      .item-value {
        font-size: 16px;
        color: #000;
        
        &.paid {
          color: #67c23a;
        }
        
        &.due {
          color: #f56c6c;
        }
      }
    }
  }
  
  .drawer-footer {
    flex-shrink: 0;
    display: flex;
    justify-content: center;
  }
}

// 添加动画效果
:deep(.el-drawer__body) {
  padding: 0;
  overflow: hidden;
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-drawer.rtl) {
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}
</style>
