<?php

declare(strict_types=1);

namespace Modules\Appointment\Services;

use Modules\Appointment\Domain\Business\StaffBusiness;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Models\IamUser;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Illuminate\Http\UploadedFile;

/**
 * 员工服务类
 */
class StaffService
{
    /**
     * @var StaffBusiness
     */
    private StaffBusiness $staffBusiness;

    /**
     * 构造函数
     */
    public function __construct(StaffBusiness $staffBusiness)
    {
        $this->staffBusiness = $staffBusiness;
    }

    /**
     * 获取员工列表
     *
     * @param array $filters 过滤条件
     * @return LengthAwarePaginator
     */
    public function list(array $filters): LengthAwarePaginator
    {
        return $this->staffBusiness->list($filters);
    }

    /**
     * 创建员工
     *
     * @param array $data 员工数据
     * @return IamUser    
     */
    public function store(array $data): IamUser
    {
        return $this->staffBusiness->store($data);
    }

    /**
     * 获取员工详情
     *
     * @param int $id 员工ID
     * @return IamUser
     */
    public function show(int $id): IamUser
    {
        return $this->staffBusiness->show($id);
    }

    /**
     * 更新员工信息
     *
     * @param int $id 员工ID
     * @param array $data 更新数据
     * @return IamUser
     */
    public function update(int $id, array $data): IamUser
    {
        return $this->staffBusiness->update($id, $data);
    }

    /**
     * 删除员工
     *
     * @param array $ids 员工ID数组
     * @return bool
     */
    public function destroyBatch(array $ids): bool
    {
        return $this->staffBusiness->destroyBatch($ids);
    }

    /**
     * 导出员工数据
     *
     * @param array $filters 过滤条件
     * @return BinaryFileResponse
     */
    public function export(array $filters = []): BinaryFileResponse
    {
        return $this->staffBusiness->export($filters);
    }

    /**
     * 导入员工数据
     *
     * @param UploadedFile $file 导入文件
     * @return array
     */
    public function import(UploadedFile $file): array
    {
        return $this->staffBusiness->import($file);
    }

    /**
     * 获取员工导入模板
     *
     * @return BinaryFileResponse
     */
    public function exportTemplate(): BinaryFileResponse
    {
        return $this->staffBusiness->exportTemplate();
    }

    
} 