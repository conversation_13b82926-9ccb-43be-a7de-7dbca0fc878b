<?php

declare(strict_types=1);

namespace Modules\Appointment\Services;

use Bingo\Exceptions\BizException;
use Illuminate\Support\Collection;
use Modules\Appointment\Enums\ErrorCode;
use Modules\Appointment\Models\AppointmentSetting;

/**
 * 预约设置服务类
 */
class AppointmentSettingService
{
    /**
     * 获取所有预约设置
     *
     * @return Collection 设置集合
     */
    public function list(): Collection
    {
        return AppointmentSetting::all();
    }

    /**
     * 获取指定设置项
     *
     * @param string $key 设置键名
     * @param mixed $default 默认值
     * @return mixed 设置值
     */
    public function get(string $key, $default = null)
    {
        return AppointmentSetting::getSetting($key, $default);
    }

    /**
     * 保存设置项
     *
     * @param string $key 设置键名
     * @param mixed $value 设置值
     * @param string|null $description 设置描述
     * @return AppointmentSetting
     */
    public function save(string $key, $value, ?string $description = null): AppointmentSetting
    {
        $setting = AppointmentSetting::query()->where('key', $key)->first();
        if (!$setting) {
            BizException::throws(ErrorCode::SETTING_NOT_FOUND,ErrorCode::SETTING_NOT_FOUND->message());
        }
        $setting->value = $value;
        
        if ($description !== null) {
            $setting->description = $description;
        }
        $setting->save();
        return $setting;
    }

    /**
     * 批量保存设置
     *
     * @param array $settings 设置数组 [key => value]
     * @return bool 是否保存成功
     */
    public function batchSave(array $settings): bool
    {
        // 使用事务确保所有配置一次性保存
        \DB::transaction(function () use ($settings) {
            foreach ($settings as $key => $value) {
                $this->save($key, $value);
            }
        });
        
        return true;
    }
}
