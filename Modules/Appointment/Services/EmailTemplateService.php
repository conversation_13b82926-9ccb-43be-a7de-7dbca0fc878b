<?php

declare(strict_types=1);

namespace Modules\Appointment\Services;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Modules\Appointment\Domain\Business\EmailTemplateBusiness;
use Modules\Appointment\Models\AppointmentEmailTemplate;
use Modules\Approval\Services\EmailService;

/**
 * 邮件模板服务类
 */
final class EmailTemplateService
{
    /**
     * 构造函数
     *
     * @param EmailTemplateBusiness $emailTemplateBusiness 邮件模板业务类
     */
    public function __construct(
        private readonly EmailTemplateBusiness $emailTemplateBusiness,
        private readonly EmailService $emailService
    ) {
    }

    /**
     * 获取邮件模板列表
     *
     * @param array $filters 查询条件
     * @return LengthAwarePaginator
     */
    public function getEmailTemplates($filters): LengthAwarePaginator
    {
        return $this->emailTemplateBusiness->getEmailTemplates($filters);
    }

    /**
     * 获取邮件模板详情
     *
     * @param int $id 邮件模板ID
     * @return AppointmentEmailTemplate|null
     */
    public function getEmailTemplateById(int $id): ?AppointmentEmailTemplate
    {
        return $this->emailTemplateBusiness->getEmailTemplateById($id);
    }

    /**
     * 创建邮件模板
     *
     * @param array $data 邮件模板数据
     * @return AppointmentEmailTemplate
     */
    public function saveEmailTemplate(array $data): AppointmentEmailTemplate
    {
        return $this->emailTemplateBusiness->saveEmailTemplate($data);
    }

    /**
     * 删除邮件模板
     *
     * @param int $id 邮件模板ID
     * @return bool
     */
    public function deleteEmailTemplate(int $id): bool
    {
        return $this->emailTemplateBusiness->deleteEmailTemplate($id);
    }

    /**
     * 按模板发送邮件
     *
     * @param string $to 收件人邮箱 
     * @param string $templateCode 模板代码
     * @param array $data 模板数据
     * @return bool
     */
    public function sendEmailByTemplate(string $to, string $templateCode, array $data): bool
    {
        $template = $this->emailTemplateBusiness->getEmailTemplateByCode($templateCode);
        if (!$template) {
            return false;
        }

        $content = $this->replaceVariables($template->content, $data);
        $subject = $this->replaceVariables($template->subject, $data);

        // 是否有附件
        $attachments = $template->attachments;
        if (empty($attachments)) {
            return $this->emailService->sendEmail($to, $subject, $content);
        }else{
            $attachmentsFileData = [];
            $attachmentsStreamData = [];
            foreach ($attachments as $attachment) {
                // 判断是否为网络地址
                if (filter_var($attachment['url'], FILTER_VALIDATE_URL)) {
                    // 网络地址，使用流的方式
                    $attachmentsStreamData[] = [
                        'data' => file_get_contents($attachment['url']),
                        'name' => !empty($attachment['name']) ? $attachment['name'] : basename(parse_url($attachment['url'], PHP_URL_PATH)),
                        'mime' => mime_content_type($attachment['url'])
                    ];
                } else {
                    // 服务器文件地址，直接使用路径
                    $attachmentsFileData[] = [
                        'path' => $attachment['url'],
                        'name' => !empty($attachment['name']) ? $attachment['name'] : basename($attachment['url']),
                    ];
                }
            }
            return $this->emailService->sendEmailWithMixedAttachments($to, $subject, $content, $attachmentsFileData, $attachmentsStreamData);
        }
    }

    /**
     * 替换变量
     *
     * @param string $content 邮件内容
     * @param array $data 模板数据
     * @return string
     */
    private function replaceVariables(string $content, array $data): string
    {
        // 遍历所有预设变量，将内容中的变量替换为对应的值
        foreach ($data as $variable => $value) {
            // 确保变量是字符串类型
            if (!is_string($value)) {
                $value = (string)$value;
            }
            
            // 替换内容中的变量
            $content = str_replace($variable, $value, $content);
        }
        
        return $content;
    }

    /**
     * 开关邮件模板
     *
     * @param int $id 邮件模板ID
     * @return bool
     */
    public function switchEmailTemplate(int $id): bool  
    {
        return $this->emailTemplateBusiness->switchEmailTemplate($id);
    }
} 