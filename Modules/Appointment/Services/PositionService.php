<?php

declare(strict_types=1);

namespace Modules\Appointment\Services;

use Modules\Appointment\Domain\Repositories\PositionRepository;

/**
 * 职位服务类
 */
class PositionService
{
    public function __construct(
        private readonly PositionRepository $positionRepository
    ) {}

    /**
     * 获取职位列表
     * 
     * @param array $params 查询参数
     * @return array 职位列表数据
     */
    public function list(array $params = []): array
    {
       return $this->positionRepository->list($params);
    }
} 