<?php

declare(strict_types=1);

namespace Modules\Appointment\Services;

use Illuminate\Http\UploadedFile;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\Storage;
use Modules\Appointment\Enums\ErrorCode;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Models\AppointmentRecords;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Modules\Appointment\Domain\Business\AppointmentBusiness;

/**
 * 预约服务类
 */
final class AppointmentService
{
    /**
     * 构造函数
     *
     * @param AppointmentBusiness $appointmentBusiness 预约业务接口
     */
    public function __construct(
        private AppointmentBusiness $appointmentBusiness
    ) {}

    /**
     * 获取预约列表
     *
     * @param array $filters 过滤条件
     * @return LengthAwarePaginator 分页结果
     */
    public function getAppointmentList(array $filters): LengthAwarePaginator
    {
        return $this->appointmentBusiness->getAppointmentList($filters);
    }

    /**
     * 获取预约详情
     *
     * @param int $id 预约ID
     * @return AppointmentRecords 预约记录
     */
    public function getAppointmentDetail(int $id): AppointmentRecords
    {
        return $this->appointmentBusiness->getAppointmentDetail($id);
    }

    /**
     * 创建预约
     *
     * @param array $data 预约数据
     * @return AppointmentRecords 创建的预约记录
     */
    public function createAppointment(array $data): AppointmentRecords
    {
        return $this->appointmentBusiness->createAppointment($data);
    }

    /**
     * 更新预约信息
     *
     * @param int $id 预约ID
     * @param array $data 更新的数据
     * @return AppointmentRecords 更新后的预约记录
     */
    public function updateAppointment(int $id, array $data): AppointmentRecords
    {
        return $this->appointmentBusiness->updateAppointment($id, $data);
    }

    /**
     * 删除预约
     *
     * @param int $id 预约ID
     * @return bool 删除结果
     */
    public function deleteAppointment(int $id): bool
    {
        return $this->appointmentBusiness->deleteAppointment($id);
    }

    /**
     * 批量删除预约
     *
     * @param array $ids 预约ID列表
     * @return bool 删除结果
     */
    public function deleteAppointments(array $ids): bool
    {
        return $this->appointmentBusiness->deleteAppointments($ids);
    }

    /**
     * 导出预约数据
     *
     * @param array $filters 过滤条件
     * @return BinaryFileResponse 文件下载响应
     * @throws BizException
     */
    public function exportAppointment(array $filters): BinaryFileResponse
    {
        return $this->appointmentBusiness->exportAppointment($filters);
    }

    /**
     * 导入预约数据
     *
     * @param UploadedFile $file 上传的文件
     * @param int $creatorId 创建者ID
     * @return array 导入结果
     * @throws BizException
     */
    public function importAppointment(UploadedFile $file, int $creatorId): array
    {
        return $this->appointmentBusiness->importAppointment($file, $creatorId);
    }

    /**
     * 导出预约导入模板
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportTemplate(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return $this->appointmentBusiness->exportTemplate();
    }

    /**
     * 更新预约付款信息
     *
     * @param int $id 预约ID
     * @param array $paymentData 付款数据
     * @return array
     * @throws BizException
     */
    public function updateAppointmentPayment(int $id, array $paymentData): array
    {
        // 获取预约记录
        $appointment = $this->appointmentBusiness->getAppointmentDetail($id);

        if (!$appointment) {
            BizException::throws(ErrorCode::APPOINTMENT_NOT_FOUND, ErrorCode::APPOINTMENT_NOT_FOUND->message());
        }

        // 检查预约是否已支付
        if ($appointment->payment_status != AppointmentRecords::PAYMENT_STATUS_PENDING) {
            BizException::throws(ErrorCode::APPOINTMENT_ALREADY_PAID, ErrorCode::APPOINTMENT_ALREADY_PAID->message());
        }

        // 检查是否已过预约时间
        if ($appointment->end_time < time()) {
            BizException::throws(ErrorCode::APPOINTMENT_EXPIRED, ErrorCode::APPOINTMENT_EXPIRED->message());
        }

        // 更新付款信息
        try {
            // 如果支付状态改为已支付且没有设置支付时间，则自动设置为当前时间
            if ($paymentData['payment_status'] != AppointmentRecords::PAYMENT_STATUS_PENDING) {
                $appointment->payment_time = date('Y-m-d H:i:s');
            }

            // 如果设置折扣ID，则计算折扣后的价格
            if (!empty($paymentData['discount_id'])) {
                $priceData = [
                    'original_price' => $appointment->original_price,
                    'discount_id' => $paymentData['discount_id'],
                ];
                $this->appointmentBusiness->calculatePrice($priceData);
                $appointment->final_price = $priceData['final_price'];
                $appointment->discount_id = $paymentData['discount_id'];
                $appointment->discount_amount = $priceData['discount_amount'];
            }

            $appointment->payment_status = $paymentData['payment_status'];
            $appointment->payment_method = $paymentData['payment_method'];
            $appointment->save();

            return $appointment->toArray();
        } catch (\Exception $e) {
            throw $e;
            \Log::error('更新预约付款信息失败: ' . $e->getMessage(), [
                'appointment_id' => $id,
                'payment_data' => $paymentData,
                'exception' => $e
            ]);
            BizException::throws(ErrorCode::PAYMENT_UPDATE_FAILED, ErrorCode::PAYMENT_UPDATE_FAILED->message());
        }
    }

    /**
     * 更新预约状态
     *
     * @param int $id 预约ID
     * @param int $status 状态
     * @return bool
     * @throws BizException
     */
    public function updateAppointmentStatus(int $id, int $status): bool
    {
        return $this->appointmentBusiness->updateAppointmentStatus($id, $status);
    }

    /**
     * 预约仪表盘
     *
     * @param array $filters 过滤条件
     * @return array
     */
    public function dashboard(array $filters): array
    {
        return $this->appointmentBusiness->dashboard($filters);
    }
}
