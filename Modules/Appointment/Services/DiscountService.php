<?php

declare(strict_types=1);

namespace Modules\Appointment\Services;

use Bingo\Exceptions\BizException;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Domain\Business\DiscountBusiness;
use Modules\Appointment\Models\AppointmentDiscount;

/**
 * 折扣服务类
 */
class DiscountService
{
    /**
     * 构造函数
     *
     * @param DiscountBusiness $discountBusiness 折扣业务逻辑
     */
    public function __construct(private DiscountBusiness $discountBusiness)
    {
    }

    /**
     * 获取折扣列表
     *
     * @param array $filters 过滤条件
     * @return LengthAwarePaginator 分页结果
     */
    public function getDiscountList(array $filters): LengthAwarePaginator
    {
        return $this->discountBusiness->getDiscountList($filters);
    }

    /**
     * 创建折扣
     *
     * @param array $data 折扣数据
     * @return AppointmentDiscount 创建的折扣对象
     * @throws BizException 业务异常
     */
    public function createDiscount(array $data): AppointmentDiscount
    {
        // 设置创建者ID
        $data['creator_id'] = auth()->guard()->user()->id ?? 0;
        
        return $this->discountBusiness->createDiscount($data);
    }

    /**
     * 获取折扣详情
     *
     * @param int $id 折扣ID
     * @return AppointmentDiscount 折扣对象
     * @throws BizException 业务异常
     */
    public function getDiscountDetail(int $id): AppointmentDiscount
    {
        return $this->discountBusiness->getDiscountDetail($id);
    }

    /**
     * 更新折扣
     *
     * @param int $id 折扣ID
     * @param array $data 更新数据
     * @return AppointmentDiscount 更新后的折扣对象
     * @throws BizException 业务异常
     */
    public function updateDiscount(int $id, array $data): AppointmentDiscount
    {
        return $this->discountBusiness->updateDiscount($id, $data);
    }

    /**
     * 删除折扣
     *
     * @param int $id 折扣ID
     * @return bool 删除是否成功
     * @throws BizException 业务异常
     */
    public function deleteDiscount(int $id): bool
    {
        return $this->discountBusiness->deleteDiscount($id);
    }
} 