<?php

declare(strict_types=1);

namespace Modules\Appointment\Services;

use Modules\Appointment\Domain\Repositories\DepartmentRepository;

/**
 * 部门服务类
 */
class DepartmentService
{
    public function __construct(
        private readonly DepartmentRepository $departmentRepository
    ) {}

    /**
     * 获取部门列表
     * 
     * @param array $params 查询参数
     * @return array 部门列表数据
     */
    public function list(array $params = []): array
    {
       return $this->departmentRepository->list($params);
    }
}
