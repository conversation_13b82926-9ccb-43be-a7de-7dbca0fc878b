<?php

declare(strict_types=1);

namespace Modules\Appointment\Services;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Domain\Business\LocationBusiness;
use Modules\Appointment\Models\AppointmentLocation;

/**
 * 地点服务
 */
class LocationService
{
    /**
     * @var LocationBusiness
     */
    private LocationBusiness $locationBusiness;

    /**
     * 构造函数
     */
    public function __construct(LocationBusiness $locationBusiness)
    {
        $this->locationBusiness = $locationBusiness;
    }

    /**
     * 获取地点列表
     *
     * @param array<string, mixed> $filters
     * @return LengthAwarePaginator
     */
    public function getList(array $filters): LengthAwarePaginator
    {
        return $this->locationBusiness->getList($filters);
    }

    /**
     * 创建地点
     *
     * @param array<string, mixed> $data
     * @return AppointmentLocation
     */
    public function create(array $data): AppointmentLocation        
    {
        return $this->locationBusiness->create($data);
    }

    /**
     * 更新地点
     *
     * @param int $id
     * @param array<string, mixed> $data
     * @return AppointmentLocation
     */
    public function update(int $id, array $data): AppointmentLocation
    {
        return $this->locationBusiness->update($id, $data);
    }

    /**
     * 根据ID获取地点
     *
     * @param int $id
     * @return AppointmentLocation|null
     */
    public function find(int $id): ?AppointmentLocation
    {
        return $this->locationBusiness->find($id);
    }
} 