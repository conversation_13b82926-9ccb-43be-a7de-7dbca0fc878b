<?php

declare(strict_types=1);

namespace Modules\Appointment\Services;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Domain\Business\ServiceBusiness;
use Modules\Appointment\Models\AppointmentService;

/**
 * 服务管理服务类
 */
class ServiceService
{
    /**
     * @var ServiceBusiness
     */
    private ServiceBusiness $serviceBusiness;

    /**
     * 构造函数
     */
    public function __construct(ServiceBusiness $serviceBusiness)
    {
        $this->serviceBusiness = $serviceBusiness;
    }

    /**
     * 获取服务列表
     *
     * @param array<string, mixed> $filters
     * @return LengthAwarePaginator
     */
    public function getList(array $filters): LengthAwarePaginator
    {
        return $this->serviceBusiness->getList($filters);
    }

    /**
     * 创建服务
     *
     * @param array<string, mixed> $data
     * @return AppointmentService
     */
    public function create(array $data): AppointmentService
    {
        return $this->serviceBusiness->create($data);
    }

    /**
     * 更新服务
     *
     * @param int $id
     * @param array<string, mixed> $data
     * @return AppointmentService
     */
    public function update(int $id, array $data): AppointmentService
    {
        return $this->serviceBusiness->update($id, $data);
    }

    /**
     * 删除服务
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        return $this->serviceBusiness->delete($id);
    }

    /**
     * 根据ID获取服务
     *
     * @param int $id
     * @return AppointmentService
     */
    public function find(int $id): AppointmentService
    {
        return $this->serviceBusiness->find($id);
    }

   

    /**
     * 开放服务
     *
     * @param int $id
     * @return AppointmentService
     */
    public function openService(int $id): AppointmentService
    {
        return $this->serviceBusiness->openService($id);
    }

    /**
     * 关闭服务
     *
     * @param int $id
     * @return AppointmentService
     */
    public function closeService(int $id): AppointmentService
    {
        return $this->serviceBusiness->closeService($id);
    }

    /**
     * 全部开放服务
     *
     * @return int 成功开放的服务数量
     */
    public function openAllServices(): int
    {
        return $this->serviceBusiness->openAllServices();
    }

    /**
     * 全部关闭服务
     *
     * @return int 成功关闭的服务数量
     */
    public function closeAllServices(): int
    {
        return $this->serviceBusiness->closeAllServices();
    }
} 