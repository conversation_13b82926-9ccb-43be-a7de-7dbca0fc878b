<?php

declare(strict_types=1);

namespace Modules\Appointment\Services;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Appointment\Domain\Business\ServiceCategoryBusiness;
use Modules\Appointment\Models\AppointmentServiceCategory;

/**
 * 服务分类服务
 */
class ServiceCategoryService
{
    /**
     * @var ServiceCategoryBusiness
     */
    private ServiceCategoryBusiness $serviceCategoryBusiness;

    /**
     * 构造函数
     */
    public function __construct(ServiceCategoryBusiness $serviceCategoryBusiness)
    {
        $this->serviceCategoryBusiness = $serviceCategoryBusiness;
    }

    /**
     * 获取服务分类列表
     *
     * @param array<string, mixed> $filters
     * @return LengthAwarePaginator
     */
    public function getList(array $filters): LengthAwarePaginator
    {
        return $this->serviceCategoryBusiness->getList($filters);
    }

    /**
     * 创建服务分类
     *
     * @param array<string, mixed> $data
     * @return AppointmentServiceCategory  
     */
    public function create(array $data): AppointmentServiceCategory
    {
        return $this->serviceCategoryBusiness->create($data);
    }

    /**
     * 更新服务分类
     *
     * @param int $id
     * @param array<string, mixed> $data
     * @return AppointmentServiceCategory
     */
    public function update(int $id, array $data): AppointmentServiceCategory
    {
        return $this->serviceCategoryBusiness->update($id, $data);
    }

    /**
     * 删除服务分类
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        return $this->serviceCategoryBusiness->delete($id);
    }

    /**
     * 根据ID获取服务分类
     *
     * @param int $id
     * @return AppointmentServiceCategory|null
     */
    public function find(int $id): ?AppointmentServiceCategory
    {
        return $this->serviceCategoryBusiness->find($id);
    }

    /**
     * 获取所有服务分类（用于下拉列表）
     *
     * @return array<int, array<string, mixed>>
     */
    public function getAllForSelect(): array
    {
        return $this->serviceCategoryBusiness->getAllForSelect();
    }
} 