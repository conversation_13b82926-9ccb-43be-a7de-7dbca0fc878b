<?php

namespace Modules\Cms\Domain;

use Bingo\Core\Dao\ModelManageUtil;
use Bingo\Core\Dao\ModelUtil;
use Bingo\Core\Util\SerializeUtil;
use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Cache;
use Modules\Cms\Domain\Field\CmsField;
use Modules\Cms\Enums\CmsMode;
use Modules\Cms\Models\CmsModel;
use Modules\Translate\Models\Translations;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class CmsModelUtil
{
    public static function getModeTypeByMode($mode, CmsModel $cmsModel): string
    {

        return match ($mode) {
            CmsMode::LIST_DETAIL->value() => T("Cms::cmsModel.list_template") . "：$cmsModel->list_template " . T("Cms::cmsModel.detail_template") . "：$cmsModel->detail_template",
            CmsMode::PAGE->value() => T("Cms::cmsModel.page_template") . "：$cmsModel->page_template",
            CmsMode::FORM->value() => T("Cms::cmsModel.form_template") . "：$cmsModel->form_template",
            default => "",
        };
    }

    public static function clearCache(): void
    {
        Cache::forget('CmsModelAll');
    }

    public static function listModeMap(): array
    {
        $map = [];
        foreach (CmsMode::getList() as $k => $v) {
            $map[$k] = [];
        }
        foreach (CmsModelUtil::all() as $item) {
            $map[$item['mode']][] = $item['id'];
        }
        return $map;
    }

    public static function getByName($name)
    {
        foreach (self::all() as $model) {
            if ($model['name'] == $name) {
                return $model;
            }
        }
        BizException::throws(Code::FAILED, '模型不存在[name=' . $name . ']');
    }

    public static function get($modelId)
    {

        foreach (self::all() as $model) {
            if ($model['id'] == $modelId) {
                return $model;
            }
        }
        return [];
        // BizException::throws(Code::FAILED, '模型不存在[id='.$modelId.']');
    }

    public static function all()
    {
        return Cache::rememberForever('CmsModelAll', function () {
            try {
                $models = ModelUtil::all('cms_model', ['enable' => true]);
                $fields = ModelUtil::all('cms_model_field', ['enable' => true], ['*'], ['sort', 'asc']);
                ModelUtil::decodeRecordsJson($fields, ['field_data']);
                foreach ($models as $k => $model) {
                    $models[$k]['_customFields'] = [];
                    foreach ($fields as $field) {
                        if ($field['model_id'] == $model['id']) {
                            $models[$k]['_customFields'][] = $field;
                        }
                    }
                }
                foreach ($models as $k => $model) {
                    $models[$k]['_table'] = 'cms_m_' . $model['name'];
                }
                return $models;
            } catch (\Exception $e) {
                return [];
            }
        });
    }

    public static function build($model, $fields = []): void
    {
        $model = ModelUtil::insert('cms_model', $model);
        self::create($model);
        foreach ($fields as $field) {
            $field['model_id'] = $model['id'];
            if (! isset($field['sort'])) {
                $field['sort'] = ModelUtil::sortNext('cms_model_field', [
                    'model_id' => $model['id'],
                ]);
            }
            if (! isset($field['max_length'])) {
                $field['max_length'] = 100;
            }
            if (! isset($field['isRequired'])) {
                $field['isRequired'] = true;
            }
            if (! isset($field['isSearch'])) {
                $field['isSearch'] = false;
            }
            if (! isset($field['isList'])) {
                $field['isList'] = false;
            }
            if (isset($field['field_data'])) {
                $field['field_data'] = SerializeUtil::jsonEncode($field['field_data']);
            }
            ModelUtil::insert('cms_model_field', $field);
            self::addField($model, $field);
        }
        self::clearCache();
    }

    public static function create($model): void
    {
        $table = "cms_m_$model[name]";
        ModelManageUtil::migrate($table, function ($table, $schema) {
            $schema->create($table, function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->timestamps();
                $table->text('content')->nullable()->comment('内容');
                $table->index(['created_at']);
            });
        });
    }

    public static function drop($model): void
    {
        $table = "cms_m_$model[name]";
        ModelManageUtil::dropTable($table);
    }


    private static function convertField($field): string
    {
        $f = CmsField::getByNameOrFail($field['field_type']);
        return $f->convertMysqlType($field);
    }

    public static function addField($model, $field): void
    {
        $table = "cms_m_$model[name]";
        $fieldType = self::convertField($field);
        ModelManageUtil::ddlFieldAdd($table, $field['name'], $fieldType);
    }

    public static function editField($model, $oldField, $newField): void
    {
        $table = "cms_m_$model[name]";
        $columnExists = ModelManageUtil::hasTableColumn($table, $oldField['name']);
        if (! $columnExists) {
            $fieldType = self::convertField($oldField);
            ModelManageUtil::ddlFieldAdd($table, $oldField['name'], $fieldType);
            return;
        }
        $oldFieldType = self::convertField($oldField);
        $newFieldType = self::convertField($newField);
        if ($oldFieldType != $newFieldType || $oldField['name'] != $newField['name']) {
            if ($oldField['name'] == $newField['name']) {
                ModelManageUtil::ddlFieldModify($table, $newField['name'], $newFieldType);
            } else {
                ModelManageUtil::ddlFieldChange($table, $oldField['name'], $newField['name'], $newFieldType);
            }
        }
    }

    public static function deleteField($model, $field): void
    {
        $table = "cms_m_$model[name]";
        ModelManageUtil::ddlFieldDrop($table, $field['name']);
    }


    public static function decodeCustomField($model, $record)
    {
        foreach ($model['_customFields'] as $f) {
            $value = $record[$f['name']] ?? null;
            $cmsF = CmsField::getByNameOrFail($f['field_type']);
            $record[$f['name']] = $cmsF->unserializeValue($value, $record);
        }
        return $record;
    }

    /**
     * 获取导航
     * @param $navs
     * @return mixed
     */
    public static function getNavigation($navs): mixed
    {
        $useLocale = T_locale();
        $cacheKey = 'cms_navigation_' . $useLocale;
        $cacheDuration = 60; // Cache duration in minutes

        return Cache::remember($cacheKey, $cacheDuration, function () use ($navs) {
            $models = self::all();
            foreach ($models as $key => $model) {
                $order = $model['sort_order'] ?? $key;
                $nav = self::createModelNav($model, $order);
                $navs[] = $nav;
            }
            return $navs;
        });
    }

    /**
     * 创建模型导航
     * @param $model
     * @param $order
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    private static function createModelNav($model, $order): array
    {
        $lang = T_locale(); // 获取当前语言
        $model_id = $model['id'];
        if ($model_id == 1) {
            return [
                "key" => "cms_model_manager" ,
                "parent" => "content",
                "nav_name" => T("Cms::nav.cms_management"), // 获取多语言标题
                "path" => "/cms/cmsList?model_id=" . $model['id'],
                "icon" => "SetUp",
                "order" => $order,
                "children" => [
                    [
                        "key" => "cms_model_" . $model_id,
                        "parent" => "cms_model_manager",
                        "nav_name" => self::getTranslatedTitle($model, $lang),
                        "path" => "/cms/cmsList?model_id=1",
                        "icon" => ! empty($model['icon']) ? $model['icon'] : "SetUp",
                        "order" => 1,
                        "children" => match ($model['mode']) {
                            3 => self::createContactChildren($model),
                            default => self::createStandardChildren($model),
                        },
                    ],
                    // [
                    //     "key" => "cms_model_notice" ,
                    //     "parent" => "cms_model_manager",
                    //     "nav_name" => T("Cms::nav.cms_model_notice"), // 获取多语言标题
                    //     "path" => "/cms/cmsList?model_id=5",
                    //     "icon" => ! empty($model['icon']) ? $model['icon'] : "SetUp",
                    //     "order" => 2,
                    //     "children" => [
                    //         [
                    //             "key" => "cms_model_notice" . $model['id'],
                    //             "parent" => "cms_model_manager" ,
                    //             "nav_name" => T("Cms::nav.list"),
                    //             "path" => "/cms/notice/list",
                    //             "icon" => "Nav/Asset/menu_icon/list.png",
                    //             "order" => 1
                    //         ],
                    //         [
                    //             "key" => "cms_model_notice" . $model['id'],
                    //             "parent" => "cms_model_manager" ,
                    //             "nav_name" => T("Cms::nav.category"),
                    //             "path" => "/cms/notice/category",
                    //             "icon" => "Nav/Asset/menu_icon/category.png",
                    //             "order" => 2
                    //         ]
                    //     ],
                    // ],
                    [
                        "key" => "content-sync",
                        "parent" => "cms_model_manager",
                        "nav_name" => T("ContentSync::nav.title"),
                        // "path" => "/content/sync",
                        "path" => "/cms/multiSiteSync?model_id=1",
                        "icon" => "Connection",
                        "order" => 3,
                    ]
                ]
            ];
        }elseif ($model_id == 5) {
            $nav = [
                "key" => "cms_model_" . $model_id,
                "parent" => "content",
                "nav_name" => self::getTranslatedTitle($model, $lang), // 获取多语言标题
                "path" => "/cms/cmsList?model_id=" . $model['id'],
                "icon" => ! empty($model['icon']) ? $model['icon'] : "SetUp",
                "order" => $order
            ];
            $nav['children'] = match ($model['mode']) {
                3 => self::createContactChildren($model),
                default => self::createStandardChildren($model),
            };
            $nav['children'][] = [
                "key" => "content-info",
                "parent" => "cms_model_" . $model_id,
                "nav_name" => T("ContentSync::nav.info"),
                // "path" => "/content/info",
                "path" => "/cms/multiSiteSync?model_id=5",
                "icon" => "Connection",
                "order" => 4,
            ];
            return $nav;
        }
        $nav = [
            "key" => "cms_model_" . $model_id,
            "parent" => "content",
            "nav_name" => self::getTranslatedTitle($model, $lang), // 获取多语言标题
            "path" => "/cms/cmsList?model_id=" . $model['id'],
            "icon" => ! empty($model['icon']) ? $model['icon'] : "SetUp",
            "order" => $order
        ];

        $nav['children'] = match ($model['mode']) {
            3 => self::createContactChildren($model),
            default => self::createStandardChildren($model),
        };

        return $nav;
    }

    private static function getTranslatedTitle($model, $lang): string
    {
        $cacheKey = 'translated_title_' . $model['id'] . '_' . $lang;
        return Cache::remember($cacheKey, 86400, function () use ($model, $lang) {
            $translation = Translations::where('module', 'cms')
                ->where('item_id', $model['id'])
                ->where('field', 'model_title')
                ->where('lang', $lang)
                ->first();

            return $translation->translation ?? $model['title'];
        });
    }


    /** 
     * 创建标准模型导航
     * @param $model
     * @return array[]
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    private static function createStandardChildren($model): array
    {
        return [
            [
                "key" => "cms_list_" . $model['id'],
                "parent" => "cms_model_" . $model['id'],
                "nav_name" => T("Cms::nav.list"),
                "path" => "/cms/cmsList?model_id=" . $model['id'],
                "icon" => "Nav/Asset/menu_icon/list.png",
                "order" => 1
            ],
            [
                "key" => "cms_category_" . $model['id'],
                "parent" => "cms_model_" . $model['id'],
                "nav_name" => T("Cms::nav.category"),
                "path" => "/cms/categories?model_id=" . $model['id'],
                "icon" => "Nav/Asset/menu_icon/category.png",
                "order" => 2
            ]
        ];
    }

    /**
     * 创建联系模型导航
     * @param $model
     * @return array[]
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    private static function createContactChildren($model): array
    {
        return [
            [
                "key" => "cms_content_" . $model['id'],
                "parent" => "cms_model_" . $model['id'],
                "nav_name" => T("Cms::nav.contact"),
                "path" => "/cms/cmsList/?model_id=" . $model['id'],
                "icon" => "Nav/Asset/menu_icon/list.png",
                "order" => 1
            ],
            [
                "key" => "cms_category_" . $model['id'],
                "parent" => "cms_model_" . $model['id'],
                "nav_name" => T("Cms::nav.contact_log"),
                "path" => "/cms/contact/?model_id=" . $model['id'],
                "icon" => "Nav/Asset/menu_icon/category.png",
                "order" => 2
            ]
        ];
    }
}
