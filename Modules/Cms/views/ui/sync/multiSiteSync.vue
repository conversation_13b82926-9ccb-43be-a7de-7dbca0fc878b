<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">

      <div class="header-title">
        <h1>{{ $t('Cms.multiSiteSync.title') }}</h1>
        <p>{{ $t('Cms.multiSiteSync.description') }}</p>
      </div>

      <el-button class="button-no-border" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        <span>{{ $t('Cms.multiSiteSync.back') }}</span>
      </el-button>
    </div>

    <!-- 步骤指示器 -->
    <div class="step-indicator-wrapper">
      <div class="step-indicator">
        <div class="steps-container">
          <div
            v-for="(step, index) in steps"
            :key="index"
            class="step-item"
            :class="getStepClass(index + 1)"
            @click="goToStep(index + 1)"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <span class="step-title">{{ step.title }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="module-con">
      <div class="box">
        <div class="sync-content-layout">
          <!-- 左侧文章列表区域 -->
          <div class="article-list-section">
            <!-- 筛选器 -->
            <div class="piece-box filter-box">
              <div class="piece-tit">
                <div class="tit">
                  <el-icon><Filter /></el-icon>
                  {{ $t('Cms.multiSiteSync.filter.title') }}
                </div>
                <div class="selection-info">
                  <span>{{ $t('Cms.multiSiteSync.filter.selected') }}: </span>
                  <el-tag type="primary" size="large">{{ selectedArticles.size }}</el-tag>
                  <el-button type="primary" text @click="clearSelection">{{ $t('Cms.multiSiteSync.filter.clear') }}</el-button>
                </div>
              </div>

              <div class="filter-controls">
                <div class="filter-row">
                  <div class="filter-item">
                    <label>{{ $t('Cms.multiSiteSync.filter.site') }}</label>
                    <el-select v-model="filters.siteId" :placeholder="$t('Cms.multiSiteSync.filter.currentSite')" disabled @change="loadArticles">
                      <el-option
                        v-for="site in siteList"
                        :key="site.id"
                        :label="site.name"
                        :value="site.id"
                      />
                    </el-select>
                  </div>
                  <div class="filter-item">
                    <label>{{ $t('Cms.multiSiteSync.filter.model') }}</label>
                    <el-select v-model="filters.modelId" :placeholder="$t('Cms.multiSiteSync.filter.selectModel')" @change="loadArticles" clearable disabled>
                      <el-option
                        v-for="model in modelList"
                        :key="model.id"
                        :label="model.title"
                        :value="model.id"
                      />
                    </el-select>
                  </div>
                  <!-- <div class="filter-item">
                    <label>语言</label>
                    <el-select v-model="filters.language" placeholder="全部语言" @change="loadArticles">
                      <el-option label="全部语言" value="" />
                      <el-option
                        v-for="lang in languageList"
                        :key="lang.code"
                        :label="lang.name"
                        :value="lang.code"
                      />
                    </el-select>
                  </div> -->
                </div>

                <div class="search-row">
                  <el-input
                    v-model="filters.keyword"
                    :placeholder="$t('Cms.multiSiteSync.filter.searchPlaceholder')"
                    @input="debounceSearch"
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                  <el-button type="primary" @click="loadArticles" :loading="loading">
                    {{ $t('Cms.multiSiteSync.filter.search') }}
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 文章列表 -->
            <div class="piece-box table-box">
              <el-table
                ref="tableRef"
                :data="articleList"
                style="width: 100%"
                @selection-change="handleSelectionChange"
                v-loading="loading"
                :max-height="500"
                :scrollbar-always-on="false"
                table-layout="fixed"
              >
                <template #empty>
                  <el-empty :description="$t('Cms.multiSiteSync.table.noData')" />
                </template>
                <el-table-column type="selection" width="60" />
                <el-table-column prop="title" :label="$t('Cms.multiSiteSync.table.title')" min-width="250" show-overflow-tooltip>
                  <template #default="scope">
                    <div class="article-title">
                      <div class="title-text">{{ scope.row.title }}</div>
                      <div class="article-tags">
                        <el-tag size="small" type="primary">{{ scope.row.category_name }}</el-tag>
                        <el-tag size="small" type="success">{{ scope.row.lang }}</el-tag>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="site_name" :label="$t('Cms.multiSiteSync.table.site')" width="100" />
                <el-table-column prop="category_name" :label="$t('Cms.multiSiteSync.table.category')" width="100" />
                <el-table-column prop="author" :label="$t('Cms.multiSiteSync.table.author')" width="80" />
                <el-table-column prop="created_at" :label="$t('Cms.multiSiteSync.table.createTime')" width="100">
                  <template #default="scope">
                    {{ formatDate(scope.row.created_at) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- 右侧配置面板 -->
          <div class="config-panel">
            <div class="piece-box">
              <div class="piece-tit">
                <div class="tit">
                  <el-icon><Setting /></el-icon>
                  同步配置
                </div>
              </div>

              <div class="panel-content">
                <!-- 步骤1：空状态 -->
                <div v-if="currentStep === 1 && selectedArticles.size === 0" class="empty-state">
                  <el-icon class="empty-icon"><Setting /></el-icon>

                  <!-- 单站点用户提示 -->
                  <div v-if="!canSync" class="single-site-warning">
                    <el-alert
                      title="无法进行多站点同步"
                      type="warning"
                      :closable="false"
                    >
                      <p>您的账户只有 {{ userSites.length }} 个站点权限，无法进行多站点同步。</p>
                      <p>请联系管理员为您分配更多站点权限。</p>
                    </el-alert>
                  </div>

                  <!-- 多站点用户正常提示 -->
                  <div v-else>
                    <p>请先选择要同步的文章</p>
                    <div class="help-tips">
                      <h4>操作指南：</h4>
                      <ul>
                        <li>1. 在左侧表格中勾选要同步的文章</li>
                        <li>2. 选择后会显示"配置同步"按钮</li>
                        <li>3. 点击按钮进入下一步配置</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- 步骤1：已选择文章 -->
                <div v-if="currentStep === 1 && selectedArticles.size > 0" class="selected-state">
                  <!-- 单站点用户无法同步提示 -->
                  <div v-if="!canSync" class="single-site-selected">
                    <el-icon class="warning-icon"><CircleClose /></el-icon>
                    <h4>无法进行多站点同步</h4>
                    <p>您已选择 {{ selectedArticles.size }} 篇文章，但您的账户只有 {{ userSites.length }} 个站点权限。</p>

                    <el-alert
                      title="权限不足"
                      type="error"
                      :closable="false"
                    >
                      <p>多站点同步需要至少2个站点权限。请联系管理员为您分配更多站点权限。</p>
                    </el-alert>

                    <el-button @click="clearSelection" class="proceed-btn-alt" size="large">
                      <el-icon><RefreshLeft /></el-icon>
                      <span>重新选择</span>
                    </el-button>
                  </div>

                  <!-- 多站点用户正常流程 -->
                  <div v-else>
                    <el-icon class="success-icon"><CircleCheck /></el-icon>
                    <h4>✓ 已选择 {{ selectedArticles.size }} 篇文章</h4>
                    <p>配置即将同步的文章选项</p>

                    <div class="selected-articles-preview">
                      <h5>选中的文章：</h5>
                      <div class="article-preview-list">
                        <div
                          v-for="article in getSelectedArticles().slice(0, 3)"
                          :key="article.id"
                          class="article-preview-item"
                        >
                          <span class="article-title">{{ article.title }}</span>
                          <el-tag size="small" type="primary">{{ article.site_name }}</el-tag>
                        </div>
                        <div v-if="selectedArticles.size > 3" class="more-articles">
                          还有 {{ selectedArticles.size - 3 }} 篇文章...
                        </div>
                      </div>
                    </div>

                    <el-button type="primary" @click="proceedToConfig" class="proceed-btn-alt" size="large">
                      <el-icon><ArrowRight /></el-icon>
                      <span>开始配置同步</span>
                    </el-button>
                  </div>
                </div>

                <!-- 步骤2：配置同步 -->
                <div v-if="currentStep === 2" class="config-form step2-layout">

                  <!-- 配置状态指示器 -->
                  <div class="config-status">
                      <div class="status-item">
                        <el-icon class="status-icon" :class="{ 'completed': selectedArticles.size > 0 }">
                          <CircleCheck v-if="selectedArticles.size > 0" />
                          <Clock v-else />
                        </el-icon>
                        <span>已选择 {{ selectedArticles.size }} 篇文章</span>
                      </div>
                      <div class="status-item">
                        <el-icon class="status-icon" :class="{ 'completed': syncConfig.targetSites.length > 0 }">
                          <CircleCheck v-if="syncConfig.targetSites.length > 0" />
                          <Clock v-else />
                        </el-icon>
                        <span>已选择 {{ syncConfig.targetSites.length }} 个目标站点</span>
                      </div>
                      <div class="status-item">
                        <el-icon class="status-icon completed">
                          <CircleCheck />
                        </el-icon>
                        <span>预计 {{ selectedArticles.size * syncConfig.targetSites.length }} 个同步操作</span>
                      </div>
                    </div>

                    <div class="config-section">
                      <h4>目标站点选择 <span class="required">*</span></h4>
                    <div class="site-options">
                      <div
                        v-for="site in availableTargetSites"
                        :key="site.id"
                        class="site-option"
                      >
                        <el-checkbox
                          v-model="syncConfig.targetSites"
                          :label="site.id"
                          :value="site.id"
                        >
                          <div class="site-info">
                            <div class="site-header">
                              <span class="site-name">{{ site.name }}</span>
                              <span class="site-id">站点ID: {{ site.id }}</span>
                            </div>
                            <p class="site-domain">域名: {{ site.domain }}</p>
                          </div>
                        </el-checkbox>
                      </div>
                    </div>
                  </div>

                  <div class="config-section">
                    <h4>同步选项</h4>
                    <div class="sync-options">
                      <!-- <el-checkbox v-model="syncConfig.keepStatus">保持原文章状态</el-checkbox> -->
                      <!-- <el-checkbox v-model="syncConfig.autoTranslate">自动翻译内容</el-checkbox> -->
<!--                      <el-checkbox v-model="syncConfig.syncSeo">同步SEO信息</el-checkbox>-->
                      <el-checkbox v-model="syncConfig.overwriteExisting">覆盖已存在文章</el-checkbox>
                      <el-checkbox v-model="syncConfig.autoCreateCategories">自动处理分类映射</el-checkbox>
                      <el-checkbox v-model="syncConfig.skipMissingCategories" :disabled="!syncConfig.autoCreateCategories">跳过禁用的分类</el-checkbox>
                    </div>

                    <!-- 分类同步说明 -->
                    <div class="category-sync-info" v-if="syncConfig.autoCreateCategories">
                      <h5>分类同步说明</h5>
                      <div class="info-content">
                        <p>分类为全局共享，无需创建。如果源分类已启用，将直接使用。</p>
                        <p v-if="syncConfig.skipMissingCategories">当源分类被禁用时，文章将不关联任何分类。</p>
                        <p v-else>如果源分类被禁用且不允许跳过，整篇文章的同步将被中止。</p>
                      </div>
                    </div>
                  </div>

                  <!-- 第2步操作按钮 -->
                  <div class="step-actions">
                    <el-button @click="backToSelection">
                      <el-icon><ArrowLeft /></el-icon>
                      返回选择
                    </el-button>
                    <el-button type="primary" @click="proceedToPreview" :disabled="syncConfig.targetSites.length === 0">
                      <el-icon><View /></el-icon>
                      预览同步计划
                    </el-button>
                  </div>

                  <!-- 占位内容，确保同步选项完全可见 -->
                  <div style="height: 250px; background: transparent; margin-top: 30px;">
                    <div style="text-align: center; padding: 60px 0; color: var(--el-text-color-placeholder);">
                      <p style="margin: 0; font-size: 0.875rem;">配置完成 - 点击预览同步计划继续</p>
                    </div>
                  </div>


                </div>

                <!-- 步骤3：预览确认 -->
                <div v-if="currentStep === 3" class="preview-section">
                  <div class="step-tips">
                    <el-alert
                      title="同步预览"
                      type="warning"
                      :closable="false"
                    >
                      <p>请仔细检查同步计划，确认无误后点击"执行同步"按钮开始同步操作。</p>
                    </el-alert>
                  </div>

                  <div class="preview-header">
                    <h4>同步计划详情</h4>
                    <p>确认以下同步计划，点击执行开始同步</p>
                  </div>

                  <div class="sync-summary">
                    <el-alert
                      title="同步概要"
                      type="info"
                      :closable="false"
                    >
                      <div class="summary-content">
                        <p>• 选中文章: {{ selectedArticles.size }}篇</p>
                        <p>• 目标站点: {{ syncConfig.targetSites.length }}个</p>
                        <p>• 预计操作: {{ selectedArticles.size * syncConfig.targetSites.length }}次同步</p>
                      </div>
                    </el-alert>
                  </div>

                  <div class="preview-actions">
                    <el-button @click="backToConfig">
                      <el-icon><ArrowLeft /></el-icon>
                      返回配置
                    </el-button>
                    <el-button type="primary" @click="startSync">
                      <el-icon><VideoPlay /></el-icon>
                      执行同步
                    </el-button>
                  </div>
                </div>

                <!-- 步骤4：执行同步 -->
                <div v-if="currentStep === 4" class="sync-progress">
                  <div class="progress-header">
                    <h4>同步进行中</h4>
                    <p>正在执行文章同步，请稍候...</p>
                  </div>

                  <div class="progress-info">
                    <div class="progress-text">
                      <span>总体进度</span>
                      <span>{{ syncProgress.current }}/{{ syncProgress.total }}</span>
                    </div>
                    <el-progress
                      :percentage="syncProgress.percentage"
                      :status="syncProgress.status"
                    />
                  </div>

                  <div class="sync-log">
                    <h5>同步日志</h5>
                    <div class="log-content">
                      <div
                        v-for="(log, index) in syncLogs"
                        :key="index"
                        class="log-item"
                        :class="log.type"
                      >
                        <span class="log-time">[{{ formatTime(log.time) }}]</span>
                        <span class="log-message">{{ log.message }}</span>
                      </div>
                    </div>
                  </div>

                  <div v-if="!syncCompleted" class="sync-actions">
                    <el-button @click="cancelSync">
                      <el-icon><CircleClose /></el-icon>
                      取消同步
                    </el-button>
                  </div>

                  <!-- 同步完成状态 -->
                  <div v-if="syncCompleted" class="sync-complete">
                    <el-alert
                      title="同步完成"
                      type="success"
                      :closable="false"
                    >
                      <div class="result-content">
                        <p>成功同步: {{ syncResult.success }} 个文章</p>
                        <p>失败: {{ syncResult.failed }} 个</p>
                        <p>耗时: {{ syncResult.duration }}</p>
                      </div>
                    </el-alert>

                    <div class="complete-actions">
                      <el-button @click="viewSyncReport">
                        <el-icon><Document /></el-icon>
                        查看报告
                      </el-button>
                      <el-button type="primary" @click="startNewSync">
                        <el-icon><Plus /></el-icon>
                        新建同步
                      </el-button>
                    </div>
                  </div>

                  <!-- 占位内容，确保第4步内容完全可见 -->
                  <div style="height: 300px; background: transparent; margin-top: 40px;">
                    <div style="text-align: center; padding: 60px 0; color: var(--el-text-color-placeholder);">
                      <p style="margin: 0; font-size: 0.875rem;">第4步占位区域 - 确保内容完全可见</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  QuestionFilled,
  Filter,
  Search,
  Monitor,
  Folder,
  User,
  Clock,
  ArrowRight,
  Setting,
  RefreshLeft,
  View,
  VideoPlay,
  CircleClose,
  Document,
  Plus,
  CircleCheck,
  ArrowDown
} from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'
import http from '/admin/support/http'
import { useUserStore } from '/admin/stores/modules/user'
import { useI18n } from 'vue-i18n'

// 路由和store
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const { t: $t } = useI18n()

// 基础状态
const loading = ref(false)
const currentStep = ref(1)
const selectedArticles = ref(new Set<number>())
const tableRef = ref()

// 步骤配置
const steps = ref([
  { title: $t('Cms.multiSiteSync.steps.selectArticles') },
  { title: $t('Cms.multiSiteSync.steps.configSync') },
  { title: $t('Cms.multiSiteSync.steps.previewConfirm') },
  { title: $t('Cms.multiSiteSync.steps.executeSync') }
])

// 筛选器
const filters = reactive({
  siteId: null as number | null,
  modelId: null as number | null,
  language: '',
  keyword: ''
})

// 类型定义
interface Site {
  id: number
  name: string
  domain: string
  language: string
}

interface Model {
  id: number
  title: string
  name: string
}

interface Language {
  code: string
  name: string
}

// 数据列表
const siteList = ref<Site[]>([])
const modelList = ref<Model[]>([])
const languageList = ref<Language[]>([])
const userSites = ref<number[]>([]) // 用户所属的站点列表

// 文章数据类型定义
interface Article {
  id: number
  title: string
  description: string
  site_id: number
  site_name: string
  model_id: number
  model_name: string
  language: string
  language_name: string
  category_name: string
  author: string
  created_at: string
}

// 文章列表数据
const articleList = ref<Article[]>([])

// 同步配置
const syncConfig = reactive({
  targetSites: [] as number[],
  keepStatus: true,  // 保持默认值，虽然界面上隐藏了
  autoTranslate: false,  // 保持默认值，虽然界面上隐藏了
  syncSeo: true,  // 默认选中
  overwriteExisting: true,  // 默认选中
  autoCreateCategories: true,  // 默认启用自动创建分类
  skipMissingCategories: true  // 默认允许跳过
})

// 同步进度
const syncProgress = reactive({
  current: 0,
  total: 0,
  percentage: 0,
  status: ''
})

// 定义日志类型
interface SyncLog {
  type: string
  message: string
  time: Date
}

const syncLogs = ref<SyncLog[]>([])
const syncCompleted = ref(false)
const syncResult = reactive({
  success: 0,
  failed: 0,
  duration: ''
})

// 计算属性
const availableTargetSites = computed(() => {
  // 先过滤用户有权限的站点
  const userAccessibleSites = siteList.value.filter(site =>
    userSites.value.includes(site.id)
  )

  // 再过滤掉选中文章的来源站点
  return userAccessibleSites.filter(site => {
    const selectedSites = new Set()
    articleList.value.forEach(article => {
      if (selectedArticles.value.has(article.id)) {
        selectedSites.add(article.site_id)
      }
    })
    return !selectedSites.has(site.id)
  })
})

// 检查用户是否可以进行多站点同步
const canSync = computed(() => {
  return userSites.value.length > 1
})

// 方法
const goBack = () => {
  router.back()
}

const getStepClass = (step: number) => {
  if (step < currentStep.value) return 'completed'
  if (step === currentStep.value) return 'active'
  return ''
}

const goToStep = (step: number) => {
  if (step <= currentStep.value || (step === 2 && selectedArticles.value.size > 0)) {
    currentStep.value = step
  }
}

const loadArticles = async () => {
  console.log('loadArticles 调用时的 filters.modelId:', filters.modelId)
  console.log('当前路由参数:', route.query)
  try {
    loading.value = true

    // 构建请求参数，符合重构后的API格式
    const requestParams = {
      model_id: filters.modelId || 1,
      site_id: filters.siteId || undefined,
      data_lang: filters.language || 'zh_CN',  // 修正语言默认值
      category_id: undefined,
      // status: 1,
      keyword: filters.keyword || undefined,
      page: 1,
      per_page: 50
    }

    // 调用重构后的多站点同步API，使用params对象
    const response = await http.get('/cms/multi-site-sync/articles',requestParams)

    if (response.data && response.data.code === 200) {
      const { articles, pagination } = response.data.data
      articleList.value = articles || []

      console.log('API获取文章成功:', {
        total: pagination?.total || 0,
        count: articles?.length || 0,
        params: requestParams
      })
    } else {
      throw new Error(response.data?.message || '获取文章列表失败')
    }

  } catch (error) {
    console.error('API调用失败:', error)
    ElMessage.warning('API调用失败，请检查网络连接')

    // API调用失败时，设置空数组
    articleList.value = []
  } finally {
    loading.value = false
  }
}

const debounceSearch = debounce(() => {
  loadArticles()
}, 300)

const handleSelectionChange = (selection: any[]) => {
  selectedArticles.value = new Set(selection.map(item => item.id))
  // 如果有选中的文章，自动进入配置步骤
  if (selectedArticles.value.size > 0 && currentStep.value === 1) {
    // 不自动跳转，让用户手动点击
  }
}

const selectAll = () => {
  // 使用表格API选择所有行
  if (tableRef.value) {
    articleList.value.forEach(row => {
      tableRef.value.toggleRowSelection(row, true)
    })
  }
}

const clearSelection = () => {
  selectedArticles.value.clear()
  // 清空表格选择
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
  currentStep.value = 1
}

const proceedToConfig = () => {
  if (selectedArticles.value.size === 0) {
    ElMessage.warning('请先选择要同步的文章')
    return
  }

  // 检查用户站点权限
  if (!canSync.value) {
    ElMessage.error('您的账户只有一个站点权限，无法进行多站点同步')
    return
  }

  // 检查可用目标站点
  if (availableTargetSites.value.length === 0) {
    ElMessage.warning('没有可用的目标站点，请确认您有多个站点权限且选择的文章来源不同')
    return
  }

  // 自动预选第一个可用的目标站点
  if (syncConfig.targetSites.length === 0 && availableTargetSites.value.length > 0) {
    syncConfig.targetSites = [availableTargetSites.value[0].id]
  }

  currentStep.value = 2
  ElMessage.success('进入同步配置页面')
}

const resetConfig = () => {
  syncConfig.targetSites = []
  syncConfig.keepStatus = true  // 保持默认值
  syncConfig.autoTranslate = false  // 保持默认值
  syncConfig.syncSeo = true  // 默认选中
  syncConfig.overwriteExisting = true  // 默认选中
  syncConfig.autoCreateCategories = true  // 默认启用自动创建分类
  syncConfig.skipMissingCategories = true  // 默认允许跳过
}

const proceedToPreview = () => {
  // 验证必填项
  if (syncConfig.targetSites.length === 0) {
    ElMessage.warning('⚠️ 请至少选择一个目标站点才能继续')
    return
  }

  // 计算同步数量
  const totalOperations = selectedArticles.value.size * syncConfig.targetSites.length
  const targetSiteNames = syncConfig.targetSites.map(siteId => {
    const site = siteList.value.find(s => s.id === siteId)
    return site ? site.name : `站点${siteId}`
  }).join(', ')

  ElMessage.success({
    message: `✅ 配置验证通过！准备同步 ${selectedArticles.value.size} 篇文章到 ${syncConfig.targetSites.length} 个站点`,
    duration: 3000
  })

  // 等待一小段时间让用户看到成功消息
  setTimeout(() => {
    currentStep.value = 3
    ElMessage.info({
      message: `🔍 正在生成预览：${totalOperations} 个同步操作`,
      duration: 2000
    })
  }, 500)
}

const backToConfig = () => {
  currentStep.value = 2
}

const backToSelection = () => {
  currentStep.value = 1
}

const startSync = async () => {
  currentStep.value = 4
  syncCompleted.value = false
  syncProgress.current = 0
  syncProgress.total = selectedArticles.value.size * syncConfig.targetSites.length
  syncProgress.percentage = 0
  syncLogs.value = []

  addSyncLog('info', '开始同步任务...')

  try {
    // 准备请求参数
    const requestData = {
      article_ids: Array.from(selectedArticles.value),
      target_site_ids: syncConfig.targetSites,
      sync_options: {
        keepStatus: syncConfig.keepStatus,
        autoTranslate: syncConfig.autoTranslate,
        syncSeo: syncConfig.syncSeo,
        overwriteExisting: syncConfig.overwriteExisting
      }
    }

    addSyncLog('info', `准备同步 ${requestData.article_ids.length} 篇文章到 ${requestData.target_site_ids.length} 个站点...`)

    // 调用后端同步接口
    const response = await http.post('/cms/multi-site-sync/execute', requestData)

    if (response.data && response.data.code === 200) {
      const result = response.data.data
      addSyncLog('success', result.message || '同步任务已启动')
      addSyncLog('info', `已加入队列的任务数: ${result.queued_count || 0}`)

      // 开始轮询同步进度
      startProgressPolling(requestData.article_ids, requestData.target_site_ids)
    } else {
      throw new Error(response.data?.message || '同步启动失败')
    }

  } catch (error: any) {
    addSyncLog('error', `同步启动失败: ${error.message || error}`)
    console.error('同步启动失败:', error)
    ElMessage.error('同步启动失败，请重试')
  }
}

// 轮询同步进度
const startProgressPolling = (articleIds: number[], targetSiteIds: number[]) => {
  let retryCount = 0
  const maxRetries = 5 // 最大重试次数
  let pollingInterval: any = null

  const poll = async () => {
    try {
      const response = await http.get('/cms/multi-site-sync/progress', {
        article_ids: articleIds,
        target_site_ids: targetSiteIds
      })

      if (response.data && response.data.code === 200) {
        const progressData = response.data.data
        retryCount = 0 // 重置重试次数

        if (progressData.progress) {
          const { success, failed, pending, syncing, total } = progressData.progress
          const completed = success + failed

          syncProgress.current = completed
          syncProgress.total = total
          syncProgress.percentage = total > 0 ? Math.round((completed / total) * 100) : 0

          // 添加进度日志
          if (completed > 0) {
            addSyncLog('info', `同步进度: ${completed}/${total} (成功: ${success}, 失败: ${failed})`)
          }

          // 检查是否完成
          if (progressData.is_completed) {
            if (pollingInterval) {
              clearInterval(pollingInterval)
              pollingInterval = null
            }
            syncCompleted.value = true
            syncResult.success = success
            syncResult.failed = failed
            syncResult.duration = '已完成'

            addSyncLog('success', `所有同步操作已完成！成功: ${success}, 失败: ${failed}`)
          }
        }
      } else {
        throw new Error(response.data?.message || '获取进度失败')
      }
    } catch (error: any) {
      retryCount++
      console.error(`获取同步进度失败 (第${retryCount}次):`, error)

      if (retryCount >= maxRetries) {
        // 达到最大重试次数，停止轮询
        if (pollingInterval) {
          clearInterval(pollingInterval)
          pollingInterval = null
        }
        addSyncLog('error', `获取同步进度失败，已停止监控。错误: ${error.message || error}`)
        addSyncLog('warning', '请手动刷新页面或重新启动同步任务')
      } else {
        addSyncLog('warning', `获取同步进度失败 (${retryCount}/${maxRetries})，正在重试...`)
      }
    }
  }

  // 立即执行一次
  poll()

  // 设置定时轮询
  pollingInterval = setInterval(poll, 3000) // 每3秒轮询一次
}

const addSyncLog = (type: string, message: string) => {
  syncLogs.value.push({
    type,
    message,
    time: new Date()
  })
}

const cancelSync = () => {
  ElMessageBox.confirm(
    '确定要取消同步吗？已完成的操作无法撤销。',
    '取消同步',
    {
      confirmButtonText: '确定',
      cancelButtonText: '继续同步',
      type: 'warning'
    }
  ).then(() => {
    addSyncLog('warning', '同步已被用户取消')
    setTimeout(() => {
      currentStep.value = 2
    }, 1500)
  })
}

const viewSyncReport = () => {
  ElMessage.info('查看同步报告功能开发中...')
}

const startNewSync = () => {
  clearSelection()
  resetConfig()
  currentStep.value = 1
  syncCompleted.value = false
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString()
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString()
}

const getSelectedArticles = () => {
  return articleList.value.filter(article => selectedArticles.value.has(article.id))
}

// 辅助函数：获取站点名称
const getSiteName = (siteId: number): string => {
  const site = siteList.value.find(s => s.id === siteId)
  return site ? site.name : `站点${siteId}`
}

// 辅助函数：获取模型名称
const getModelName = (modelId: number): string => {
  const model = modelList.value.find(m => m.id === modelId)
  return model ? model.title : `模型${modelId}`
}

// 辅助函数：获取语言名称
const getLanguageName = (langCode: string): string => {
  const language = languageList.value.find(l => l.code === langCode)
  return language ? language.name : langCode
}

// 从URL参数获取model_id
const getModelIdFromUrl = () => {
  const modelIdParam = route.query.model_id
  if (modelIdParam) {
    if (typeof modelIdParam === 'string') {
      return parseInt(modelIdParam)
    } else if (Array.isArray(modelIdParam) && modelIdParam[0]) {
      return parseInt(modelIdParam[0])
    }
  }
  return 1
}

// 获取用户站点信息 - 复制自profile.vue
const getUserSites = async () => {
  try {
    // 获取当前用户的详细信息，包括站点权限
    const userId = userStore.getId
    if (!userId) {
      console.error('用户未登录')
      return { currentSite: null, allSites: [] }
    }

    const userResponse = await http.get(`/iam/users/${userId}`)
    if (userResponse.data.code !== 200) {
      console.error('获取用户详情失败')
      return { currentSite: null, allSites: [] }
    }

    const userData = userResponse.data.data
    let userSiteIds: number[] = []

    // 解析用户的站点信息
    if (userData.sites) {
      // 如果sites是字符串（逗号分隔的ID），解析它
      if (typeof userData.sites === 'string') {
        userSiteIds = userData.sites.split(',').map((id: string) => parseInt(id.trim())).filter((id: number) => !isNaN(id))
      } else if (Array.isArray(userData.sites)) {
        userSiteIds = userData.sites.map((site: any) => typeof site === 'number' ? site : site.id).filter((id: number) => !isNaN(id))
      }
    } else if (userData.site_id) {
      // 兼容单站点模式
      userSiteIds = [userData.site_id]
    }

    // 如果用户没有分配任何站点，返回空数组
    if (userSiteIds.length === 0) {
      console.warn('当前用户未分配任何站点')
      return { currentSite: null, allSites: [] }
    }

    // 设置当前站点 - 优先用户的主站点，如果没有则用第一个可用站点
    let currentSite: number | null = null
    if (userData.site_id && userSiteIds.includes(userData.site_id)) {
      currentSite = userData.site_id
    } else if (userSiteIds.length > 0) {
      currentSite = userSiteIds[0]
    }

    return { currentSite, allSites: userSiteIds }
  } catch (error) {
    console.error('获取用户站点失败:', error)
    return { currentSite: null, allSites: [] }
  }
}

// 初始化
onMounted(async () => {
  // 从URL参数设置model_id
  console.log('初始化时的路由参数:', route.query)
  const modelIdFromUrl = getModelIdFromUrl()
  console.log('从URL获取的model_id:', modelIdFromUrl)
  if (modelIdFromUrl) {
    filters.modelId = modelIdFromUrl  // 直接赋值number类型
    console.log('设置后的 filters.modelId:', filters.modelId)
  }

  // 获取用户站点信息并设置为默认值
  const { currentSite, allSites } = await getUserSites()
  if (currentSite) {
    filters.siteId = currentSite
  }
  userSites.value = allSites

  // 并行加载基础数据和文章数据
  await Promise.all([
    loadModels(),
    loadLanguages(),
    loadSites(),
    loadArticles()
  ])
})

// 加载模型列表
const loadModels = async () => {
  try {
    const response = await http.get('/cms/multi-site-sync/models')
    if (response.data && response.data.code === 200) {
      modelList.value = response.data.data.models || []
    }
  } catch (error) {
    console.error('加载模型列表失败:', error)
  }
}

// 加载语言列表
const loadLanguages = async () => {
  try {
    const response = await http.get('/cms/multi-site-sync/languages')
    if (response.data && response.data.code === 200) {
      languageList.value = response.data.data.languages || []
    }
  } catch (error) {
    console.error('加载语言列表失败:', error)
  }
}

// 加载站点列表
const loadSites = async () => {
  try {
    const response = await http.get('/cms/multi-site-sync/sites')
    if (response.data && response.data.code === 200) {
      siteList.value = response.data.data.sites || []
    }
  } catch (error) {
    console.error('加载站点列表失败:', error)
  }
}
</script>

<style scoped lang="scss">
.bwms-module {
  .module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-title {
      flex: 1;
      text-align: center;

      h1 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0 0 4px 0;
        color: var(--el-text-color-primary);
      }

      p {
        font-size: 0.875rem;
        color: var(--el-text-color-regular);
        margin: 0;
      }
    }
  }

  .module-con {
    .box {
      padding-top: 0;
    }
  }
}

.step-indicator-wrapper {
  margin-bottom: 20px;
}

.step-indicator {
  background: white;
  border-radius: var(--el-card-border-radius);
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid var(--el-border-color-light);
  background: white;

  &.active {
    background: var(--el-color-primary-light-9);
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
  }

  &.completed {
    background: var(--el-color-success-light-9);
    border-color: var(--el-color-success);
    color: var(--el-color-success);
  }
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--el-border-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 8px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.step-item.active .step-number {
  background: var(--el-color-primary);
  color: white;
}

.step-item.completed .step-number {
  background: var(--el-color-success);
  color: white;
}

.step-title {
  font-size: 0.875rem;
  font-weight: 500;
}

.sync-content-layout {
  display: grid;
  grid-template-columns: 1fr 420px; // 增加右侧面板宽度
  gap: 20px;
  align-items: start;
  min-height: calc(100vh - 150px);
}

.article-list-section {
  min-width: 0;
  overflow: hidden;

  .filter-box {
    margin-bottom: 20px;
  }

  .table-box {
    margin-bottom: 20px;
    overflow: hidden;

    .el-table {
      width: 100% !important;

      // 选中行样式
      :deep(.el-table__row.current-row) {
        background-color: var(--el-color-primary-light-9);
      }

      :deep(.el-table__row:hover) {
        background-color: var(--el-bg-color-page);
      }
    }
  }

  .filter-controls {
    .filter-row {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-bottom: 16px;
    }

    .filter-item {
      label {
        display: block;
        margin-bottom: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }

    .search-row {
      display: flex;
      gap: 16px;
      align-items: flex-end;
    }
  }

  .article-title {
    .title-text {
      font-weight: 500;
      margin-bottom: 8px;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .article-tags {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;
    }
  }
}

.batch-actions {
  background: var(--el-bg-color-page);
  border-radius: var(--el-card-border-radius);
  padding: 16px;
  margin-top: 16px;
}

.batch-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-left {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.config-panel {
  .panel-content {
    padding: 20px;
  }
}

.empty-state {
  text-align: center;
  padding: 40px 0;

  .empty-icon {
    font-size: 3rem;
    color: var(--el-text-color-placeholder);
    margin-bottom: 16px;
  }

  p {
    font-size: 0.875rem;
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.config-section {
  margin-bottom: 24px;

  h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 16px 0;
  }
}

.site-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.site-option {
  padding: 12px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    background: var(--el-bg-color-page);
  }

  .el-checkbox {
    width: 100%;

    :deep(.el-checkbox__label) {
      width: 100%;
    }
  }
}

.site-info {
  width: 100%;

  .site-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  .site-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .site-id {
    font-size: 0.75rem;
    color: var(--el-text-color-regular);
  }

  .site-domain {
    font-size: 0.75rem;
    color: var(--el-text-color-secondary);
    margin: 0;
  }
}

.sync-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-actions {
  display: flex;
  gap: 12px;

  .el-button {
    flex: 1;
  }
}

.preview-actions {
  display: flex;
  gap: 12px;

  .el-button {
    flex: 1;
  }
}

.sync-log {
  margin-bottom: 16px;

  h5 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 12px 0;
  }
}

.log-content {
  background: var(--el-bg-color-page);
  border-radius: 6px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  font-size: 0.75rem;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  &.info .log-message {
    color: var(--el-text-color-regular);
  }

  &.success .log-message {
    color: var(--el-color-success);
  }

  &.error .log-message {
    color: var(--el-color-error);
  }

  &.warning .log-message {
    color: var(--el-color-warning);
  }
}

.log-time {
  color: var(--el-color-primary);
  margin-right: 8px;
}

.complete-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;

  .el-button {
    flex: 1;
  }
}

.selection-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: var(--el-text-color-regular);
}

.batch-actions {
  background: var(--el-color-primary-light-9);
  border-radius: var(--el-card-border-radius);
  padding: 20px;
  border: 2px solid var(--el-color-primary-light-7);
  margin-top: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: sticky;
  bottom: 20px;
  z-index: 10;
}

.batch-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  flex-direction: column;

  @media (min-width: 768px) {
    flex-direction: row;
    justify-content: space-between;
  }
}

.batch-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;

  .selection-text {
    font-size: 1rem;
    font-weight: 600;
    color: var(--el-color-success);
    margin-right: 16px;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }
}

.main-action {
  flex-shrink: 0;
}

.proceed-btn {
  font-size: 1rem !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
  height: auto !important;
  min-width: 180px !important;

  .el-icon {
    margin-right: 8px;
  }
}

.config-panel {
  .panel-content {
    padding: 20px;
  }
}

.empty-state {
  text-align: center;
  padding: 40px 0;

  .empty-icon {
    font-size: 3rem;
    color: var(--el-text-color-placeholder);
    margin-bottom: 16px;
  }

  p {
    font-size: 0.875rem;
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.config-section {
  margin-bottom: 24px;

  h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 16px 0;
  }
}

.site-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.site-option {
  padding: 12px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    background: var(--el-bg-color-page);
  }

  .el-checkbox {
    width: 100%;

    :deep(.el-checkbox__label) {
      width: 100%;
    }
  }
}

.site-info {
  width: 100%;

  .site-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  .site-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .site-id {
    font-size: 0.75rem;
    color: var(--el-text-color-regular);
  }

  .site-domain {
    font-size: 0.75rem;
    color: var(--el-text-color-secondary);
    margin: 0;
  }
}

.sync-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-actions {
  display: flex;
  gap: 12px;

  .el-button {
    flex: 1;
  }
}

.preview-actions {
  display: flex;
  gap: 12px;

  .el-button {
    flex: 1;
  }
}

.sync-log {
  margin-bottom: 16px;

  h5 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 12px 0;
  }
}

.log-content {
  background: var(--el-bg-color-page);
  border-radius: 6px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color-light);
}

.log-item {
  font-size: 0.75rem;
  margin-bottom: 8px;
  line-height: 1.4;

  &:last-child {
    margin-bottom: 0;
  }

  &.info .log-message {
    color: var(--el-text-color-regular);
  }

  &.success .log-message {
    color: var(--el-color-success);
  }

  &.error .log-message {
    color: var(--el-color-error);
  }

  &.warning .log-message {
    color: var(--el-color-warning);
  }
}

.log-time {
  color: var(--el-color-primary);
  margin-right: 8px;
  font-weight: 500;
}

.complete-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;

  .el-button {
    flex: 1;
  }
}

.preview-header {
  margin-bottom: 16px;

  h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 8px 0;
  }

  p {
    font-size: 0.75rem;
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.sync-summary {
  margin-bottom: 16px;

  .summary-content {
    font-size: 0.75rem;

    p {
      margin: 0 0 4px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.progress-header {
  margin-bottom: 16px;

  h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 8px 0;
  }

  p {
    font-size: 0.75rem;
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.progress-info {
  margin-bottom: 16px;

  .progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
}

.sync-actions {
  .el-button {
    width: 100%;
  }
}

.sync-complete {
  .result-content {
    font-size: 0.75rem;

    p {
      margin: 0 0 4px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .sync-content-layout {
    grid-template-columns: 1fr 320px;
  }
}

@media (max-width: 1024px) {
  .sync-content-layout {
    grid-template-columns: 1fr;
  }

  .filter-row {
    grid-template-columns: 1fr !important;
  }

  .steps-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .steps-container {
    grid-template-columns: 1fr;
  }

  .step-title {
    font-size: 0.75rem;
  }
}

// 新增样式
.help-tips {
  text-align: left;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;

  h4 {
    font-size: 0.875rem;
    color: var(--el-text-color-primary);
    margin: 0 0 12px 0;
  }

  ul {
    margin: 0;
    padding-left: 16px;

    li {
      font-size: 0.75rem;
      color: var(--el-text-color-regular);
      margin-bottom: 8px;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.selected-state {
  text-align: center;
  padding: 30px 20px;

  .success-icon {
    font-size: 3rem;
    color: var(--el-color-success);
    margin-bottom: 16px;
  }

  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--el-color-success);
    margin: 0 0 8px 0;
  }

  p {
    font-size: 0.875rem;
    color: var(--el-text-color-regular);
    margin: 0 0 20px 0;
  }

  .selected-articles-preview {
    background: var(--el-bg-color-page);
    border-radius: 8px;
    padding: 16px;
    margin: 20px 0;
    text-align: left;

    h5 {
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0 0 12px 0;
    }

    .article-preview-list {
      .article-preview-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid var(--el-border-color-lighter);

        &:last-child {
          border-bottom: none;
        }

        .article-title {
          font-size: 0.75rem;
          color: var(--el-text-color-primary);
          flex: 1;
          margin-right: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .more-articles {
        font-size: 0.75rem;
        color: var(--el-text-color-placeholder);
        text-align: center;
        padding: 8px 0;
        font-style: italic;
      }
    }
  }

  .proceed-btn-alt {
    margin-top: 8px;
    width: 100%;
  }
}

.step-action-area {
  background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  border: 2px solid var(--el-color-primary-light-7);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.1);

  .action-header {
    margin-bottom: 20px;
    text-align: center;

    h4 {
      font-size: 1.1rem;
      font-weight: 700;
      color: var(--el-color-primary);
      margin: 0 0 12px 0;
    }

    p {
      font-size: 0.875rem;
      color: var(--el-text-color-regular);
      margin: 0;
      line-height: 1.5;
    }
  }

  .primary-action {
    text-align: center;

    .main-preview-btn {
      width: 100%;
      min-height: 54px;
      font-weight: 700 !important;
      font-size: 1.1rem !important;
      background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3)) !important;
      border: none !important;
      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4) !important;
      position: relative;
      overflow: hidden;
      animation: pulse-glow 2s infinite;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(64, 158, 255, 0.5) !important;
      }

      &:disabled {
        background: var(--el-color-info-light-7) !important;
        color: var(--el-text-color-placeholder) !important;
        box-shadow: none !important;
        animation: none;
      }
    }

    .action-hint {
      font-size: 0.75rem;
      color: var(--el-text-color-placeholder);
      margin: 8px 0 0 0;
      font-style: italic;
    }
  }
}

.required {
  color: var(--el-color-danger);
  font-weight: normal;
}

.config-status {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid var(--el-border-color-light);

  .status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding: 8px 0;

    &:last-child {
      margin-bottom: 0;
    }

    .status-icon {
      font-size: 1.2rem;
      transition: all 0.3s ease;

      &.completed {
        color: var(--el-color-success);
      }

      &:not(.completed) {
        color: var(--el-text-color-placeholder);
      }
    }

    span {
      font-weight: 500;
      font-size: 0.875rem;

      .status-icon.completed + & {
        color: var(--el-color-success-dark-2);
      }
    }
  }
}

.scroll-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px;
  margin: 20px 0;
  background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
  border: 2px solid var(--el-color-success);
  border-radius: 12px;
  font-weight: 700;
  color: var(--el-color-success-dark-2);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.2);
  animation: success-glow 2s infinite;

  span {
    text-align: center;
    font-size: 1rem;
  }
}

@keyframes success-glow {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(103, 194, 58, 0.2);
    border-color: var(--el-color-success);
  }
  50% {
    box-shadow: 0 6px 20px rgba(103, 194, 58, 0.4);
    border-color: var(--el-color-success-light-3);
  }
}

// 修复配置面板滚动问题 - 平衡方案
.config-panel {
  max-height: calc(100vh - 80px); // 进一步增加高度，配合占位内容
  overflow-y: auto;

  .piece-box {
    height: auto;
    min-height: auto;
  }

  .panel-content {
    max-height: none;
    padding-bottom: 30px;
  }
}

// 统一的步骤操作按钮样式
.step-actions, .preview-actions, .config-actions {
  display: flex;
  gap: 12px;
  margin-top: 30px;

  .el-button {
    flex: 1;
    min-height: 42px;
  }
}

.proceed-btn {
  background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3)) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(64, 158, 255, 0.4) !important;
  }
}

// 按钮动画效果 - 简化版本
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }
  50% {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }
}

// 第2步专用布局样式 - 简化版本
.step2-layout {
  .step-action-area {
    background: linear-gradient(135deg, #e8f4fd, #f0f9ff);
    border: 2px solid var(--el-color-primary);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;

    .action-header h4 {
      color: var(--el-color-primary);
      font-size: 1.2rem;
      margin-bottom: 8px;
      font-weight: 600;
    }

    .primary-action {
      margin-top: 12px;

      .main-preview-btn {
        width: 100%;
        min-height: 50px;
        font-size: 1.1rem;
        font-weight: 700;
        background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
        border: none;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
        }

        &:disabled {
          background: var(--el-color-info-light-7);
          color: var(--el-text-color-placeholder);
          box-shadow: none;
        }
      }

      .action-hint {
        margin-top: 8px;
        font-size: 0.875rem;
        color: var(--el-text-color-secondary);
      }
    }
  }

  // 优化配置选项布局
  .sync-options {
    .el-checkbox {
      display: block;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 单站点用户样式
.single-site-warning {
  margin: 20px 0;
}

.single-site-selected {
  text-align: center;
  padding: 30px 20px;

  .warning-icon {
    font-size: 3rem;
    color: var(--el-color-danger);
    margin-bottom: 16px;
  }

  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--el-color-danger);
    margin: 0 0 8px 0;
  }

  p {
    font-size: 0.875rem;
    color: var(--el-text-color-regular);
    margin: 0 0 20px 0;
  }

  .el-alert {
    margin: 20px 0;
  }

  .proceed-btn-alt {
    margin-top: 8px;
    width: 100%;
  }
}

// 占位内容，确保同步选项完全可见
.category-sync-info {
  margin-top: 16px;

  .info-content {
    font-size: 0.75rem;
    line-height: 1.5;

    p {
      margin: 0 0 8px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
