<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cfm_meta', function (Blueprint $table) {
            $table->comment('通用Meta數據表');
            $table->bigIncrements('id')->comment('Meta ID 自增');
            $table->unsignedBigInteger('content_id')->comment('內容ID');
            $table->string('meta_key')->nullable()->comment('Meta Key');
            $table->longText('meta_value')->nullable()->comment('Meta Value');
            $table->string('lang', 50)->nullable()->comment('语言标识');
            $table->unsignedInteger('creator_id')->default(0)->comment('creator id');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->index(['content_id', 'meta_key'], 'content_id_meta_key_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cfm_meta');
    }
};
