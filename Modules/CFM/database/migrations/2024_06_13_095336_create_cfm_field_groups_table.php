<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cfm_field_groups', function (Blueprint $table) {
            $table->comment('自定義字段組表');
            $table->increments('id')->comment('ID 自增');
            $table->string('name', 100)->comment('组名称');
            $table->string('key', 100)->comment('组唯一值');
            $table->string('title', 100)->comment('字段组标题');
            $table->text('description')->nullable()->comment('字段组描述');
            $table->json('location_rules')->nullable()->comment('显示条件');
            $table->json('presentation_settings')->nullable()->comment('展示设置');
            $table->boolean('active')->default(true)->comment('是否激活');
            $table->unsignedInteger('creator_id')->default(0)->comment('creator id');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cfm_field_groups');
    }
};
