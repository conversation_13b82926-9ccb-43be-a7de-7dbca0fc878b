<?php

namespace Modules\CFM\Services;

use Bingo\Exceptions\BizException;
use Illuminate\Database\QueryException;
use Modules\CFM\Enums\CFMErrorCode;
use Modules\CFM\Repositories\Eloquent\EloquentFieldGroupRepository;
use Modules\CFM\Repositories\Eloquent\EloquentFieldRepository;

class FieldGroupService
{
    protected EloquentFieldGroupRepository $fieldGroupRepository;
    protected EloquentFieldRepository $fieldRepository;

    public function __construct(
        EloquentFieldGroupRepository $fieldGroupRepository,
        EloquentFieldRepository      $fieldRepository
    ) {
        $this->fieldGroupRepository = $fieldGroupRepository;
        $this->fieldRepository = $fieldRepository;
    }

    public function getAll(): array
    {
        return $this->fieldGroupRepository->all();
    }

    public function save(array $data): array
    {
        try {
            if (empty($data['post_ID']) || $data['post_ID'] == 0) {
                // 创建新字段组的数据
                $createFieldGroupData = [
                    'name' => $data['post_name'] ?? '',
                    'key' => $data['field_group']['key'] ?? '',
                    'title' => $data['post_title'] ?? '',
                    'description' => $data['field_group']['description'] ?? '',
                    'location_rules' => json_encode($data['field_group']['location'] ?? []),
                    'presentation_settings' => json_encode([
                        'style' => $data['field_group']['style'] ?? 'default',
                        'position' => $data['field_group']['position'] ?? 'normal',
                        'label_placement' => $data['field_group']['label_placement'] ?? 'top',
                        'instruction_placement' => $data['field_group']['instruction_placement'] ?? 'label',
                        'menu_order' => $data['field_group']['menu_order'] ?? 0,
                        'hide_on_screen' => $data['field_group']['hide_on_screen'] ?? [],
                    ]),
                    'active' => $data['field_group']['active'] ?? 1,
                ];

                // 检查 key 是否存在
                if (empty($data['field_group']['key'])) {
                    BizException::throws(CFMErrorCode::FIELD_GROUP_KEY_REQUIRED);
                }
                // 检查是否有重复的字段组
                $existingFieldGroup = $this->fieldGroupRepository->findByName($data['field_group']['key']);
                if ($existingFieldGroup) {
                    BizException::throws(CFMErrorCode::FIELD_GROUP_DUPLICATE_NAME);
                }

                // 创建字段组
                $fieldGroup = $this->fieldGroupRepository->create($createFieldGroupData);
            } else {
                // 更新现有字段组
                $fieldGroup = $this->fieldGroupRepository->findById($data['post_ID']);
                if (! $fieldGroup) {
                    BizException::throws(CFMErrorCode::FIELD_GROUP_NOT_FOUND);
                }

                // 检查是否尝试修改 key
                if (isset($data['field_group']['key']) && $fieldGroup['key'] !== $data['field_group']['key']) {
                    BizException::throws(CFMErrorCode::FIELD_GROUP_KEY_CANNOT_CHANGE);
                }

                // 更新字段组的数据
                $updateFieldGroupData = [];
                if (isset($data['post_name'])) {
                    $updateFieldGroupData['name'] = $data['post_name'];
                }
                if (isset($data['post_title'])) {
                    $updateFieldGroupData['title'] = $data['post_title'];
                }
                if (isset($data['field_group']['description'])) {
                    $updateFieldGroupData['description'] = $data['field_group']['description'];
                }
                if (isset($data['field_group']['location'])) {
                    $updateFieldGroupData['location_rules'] = json_encode($data['field_group']['location']);
                }

                if (isset($data['field_group']['style']) || isset($data['field_group']['position']) || isset($data['field_group']['label_placement']) || isset($data['field_group']['instruction_placement']) || isset($data['field_group']['menu_order']) || isset($data['field_group']['hide_on_screen'])) {
                    $updateFieldGroupData['presentation_settings'] = json_encode([
                        'style' => $data['field_group']['style'] ?? json_decode($fieldGroup['presentation_settings'], true)['style'],
                        'position' => $data['field_group']['position'] ?? json_decode($fieldGroup['presentation_settings'], true)['position'],
                        'label_placement' => $data['field_group']['label_placement'] ?? json_decode($fieldGroup['presentation_settings'], true)['label_placement'],
                        'instruction_placement' => $data['field_group']['instruction_placement'] ?? json_decode($fieldGroup['presentation_settings'], true)['instruction_placement'],
                        'menu_order' => $data['field_group']['menu_order'] ?? json_decode($fieldGroup['presentation_settings'], true)['menu_order'],
                        'hide_on_screen' => $data['field_group']['hide_on_screen'] ?? json_decode($fieldGroup['presentation_settings'], true)['hide_on_screen'],
                    ]);
                }
                if (isset($data['field_group']['active'])) {
                    $updateFieldGroupData['active'] = $data['field_group']['active'];
                }

                // 更新字段组
                $fieldGroup = $this->fieldGroupRepository->update($data['post_ID'], $updateFieldGroupData);
            }

            // 处理字段数据
            if (isset($data['fields']) && is_array($data['fields'])) {
                $this->saveFields($fieldGroup['id'], $data['fields']);
            }
        } catch (QueryException $e) {
            BizException::throws(CFMErrorCode::FIELD_GROUP_DUPLICATE_NAME, $e->getMessage());
        }

        return $fieldGroup;
    }


    public function getById(int $id): array
    {
        return $this->fieldGroupRepository->findById($id);
    }
    public function getByTitle(string $title): array
    {
        return $this->fieldGroupRepository->search($title);
    }

    public function delete(int $id): void
    {
        $this->fieldRepository->deleteByGroupId($id);
        $this->fieldGroupRepository->delete($id);
    }

    protected function saveFields(int $groupId, array $fields): void
    {

        foreach ($fields as $field) {
            // 确保字段的 key 值是正确的
            if (empty($field['key']) || ! is_string($field['key'])) {
                continue;
            }

            $fieldData = [
                'group_id' => $groupId,
                'name' => $field['name'],
                'key' => $field['key'],
                'label' => $field['label'],
                'type' => $field['type'],
                'default_value' => $field['default_value'] ?? '',
                'settings' => json_encode($field),
                'parent_key' => $field['parent_key'] ?? '',
            ];

            // 根据 key 和 group_id 查找是否已存在字段
            $existingField = $this->fieldRepository->findByNameAndGroupId($field['key'], $groupId);

            if ($existingField) {
                // 如果存在，则更新字段
                $this->fieldRepository->update($existingField->id, $fieldData);
            } else {
                // 如果不存在，则创建新字段
                $this->fieldRepository->create($fieldData);
            }
        }
    }


}
