<?php

namespace Modules\CFM\Services;

use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\DB;
use Modules\CFM\Domain\Fields\Field;
use Modules\CFM\Enums\CFMErrorCode;
use Modules\CFM\Enums\LocationType;
use Modules\CFM\Models\CfmFieldGroups;
use Modules\CFM\Models\CfmMeta;
use Modules\CFM\Repositories\Eloquent\EloquentFieldRepository;

class FieldService
{
    protected EloquentFieldRepository $fieldRepository;

    public function __construct(EloquentFieldRepository $fieldRepository)
    {
        $this->fieldRepository = $fieldRepository;
    }

    /**
     * 获取自定义字段数据
     *
     * @param string $location
     * @param int $contentId
     * @return array
     * @throws BizException
     */
    public function getCustomFieldData(string $location, int $contentId): array
    {

        if (preg_match('/^(cms_|blog_)/', $location)) {
            list($prefix, $name) = explode('_', $location, 2);
            if ($prefix === 'cms' && ! DB::table('cms_model')->where('name', $name)->exists()) {
                BizException::throws(CFMErrorCode::LOCATION_IS_NOT_VALID);
            }
        } else {
            BizException::throws(CFMErrorCode::LOCATION_IS_NOT_VALID);
        }

        $allFieldGroups = CfmFieldGroups::all();
        $matchingFieldGroups = [];
        $checkalias = true;
        if ($location === 'cms_page') {
            // 返回相应的结果，比如视图或 JSON 响应
            $cmscontent = (new \Modules\Cms\Models\CmsContent())->find($contentId)->toArray();
            if (empty($cmscontent['alias'])) {
                $checkalias = false;
            }
        } else {
            $cmscontent['alias'] = "";
        }
        if ($checkalias) {
            foreach ($allFieldGroups as $fieldGroup) {
                $locationRules = json_decode($fieldGroup->location_rules, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    BizException::throws(CFMErrorCode::LOCATION_IS_NOT_VALID, 'Invalid JSON in location_rules');
                }

                if (! is_array($locationRules)) {
                    BizException::throws(CFMErrorCode::LOCATION_IS_NOT_VALID, 'Location rules should be an array');
                }
                if ($this->matchesLocationRules($locationRules, $prefix, $name, $cmscontent['alias'])) {
                    $fields = [];
                    foreach ($fieldGroup->fields as $field) {
                        $settings = is_string($field->settings) ? json_decode($field->settings, true) : $field->settings;

                        if (! isset($settings['key'])) {
                            BizException::throws(CFMErrorCode::LOCATION_IS_NOT_VALID, 'Field settings missing key');
                        }

                        $settings['content_id'] = $contentId;
                        $settings['lang'] = 'zh_CN';

                        $fieldClass = 'Modules\\CFM\\Domain\\Fields\\' . $field->type . 'Field';

                        if (! class_exists($fieldClass)) {
                            $fieldClass = 'Modules\\CFM\\Domain\\Fields\\TextField';
                        }
                        $metaValue = CfmMeta::getMetaValueByFieldKey($contentId, $settings['key']);
                        $fieldObject = new $fieldClass([
                            'name' => $field->name,
                            'key' => $field->key,
                            'label' => $field->label,
                            'type' => $field->type,
                            'default_value' => $field->default_value,
                            'settings' => $settings
                        ]);
                        $fieldData = [
                            'name' => $fieldObject->name,
                            'key' => $fieldObject->key,
                            'label' => $fieldObject->label,
                            'type' => $fieldObject->type,
                            'default_value' => $fieldObject->default_value,
                            'settings' => $fieldObject->settings,
                            'component' => class_basename($fieldObject),
                            'value' => $field->type == "Gallery" ? json_decode($metaValue) : $metaValue,
                            'parent_key' => $field->parent_key
                        ];
                        if (! empty($field->parent_key)) {
                            // 找到父级字段并插入子字段
                            $this->insertSubField($fields, $field->parent_key, $fieldData);
                        } else {
                            $fields[] = $fieldData;
                        }
                    }

                    $presentationSettings = json_decode($fieldGroup->presentation_settings, true);

                    $matchingFieldGroups[] = array_merge(
                        [
                            'post_ID' => $fieldGroup->id,
                            'key' => $fieldGroup->key,
                            'post_title' => $fieldGroup->title,
                            'description' => $fieldGroup->description,
                            'active' => $fieldGroup->active,
                            'fields' => $fields,
                        ],
                        $presentationSettings
                    );
                }
            }
        }


        return $matchingFieldGroups;
    }
    protected function insertSubField(array &$fields, string $parentKey, array $subField): void
    {

        foreach ($fields as &$field) {
            if ($field['key'] === $parentKey) {
                if (! isset($field['sub_fields'])) {
                    $field['sub_fields'] = [];
                }
                $field['sub_fields'][] = $subField;
                return;
            } elseif (isset($field['sub_fields']) && is_array($field['sub_fields'])) {
                $this->insertSubField($field['sub_fields'], $parentKey, $subField);
            }
        }
    }
    private function matchesLocationRules(array $locationRules, string $prefix, string $name, string $alias): bool
    {
        foreach ($locationRules as $group) {
            foreach ($group as $rule) {
                if ($this->ruleMatches($rule, $prefix, $name, $alias)) {
                    return true;
                }
            }
        }
        return false;
    }
    private function ruleMatches(array $rule, string $prefix, string $name, string $alias): bool
    {
        if (! isset($rule['param'], $rule['value'], $rule['operator'])) {
            return false;
        }
        $paramMatches = true;
        if ($name === 'page') {
            $valueMatches = ($rule['operator'] === "==") ? ($rule['value'] == $alias) : ($rule['value'] != $alias);
        } else {
            $paramMatches = $rule['param'] === $prefix;
            $valueMatches = ($rule['operator'] === "==") ? ($rule['value'] === $name) : ($rule['value'] !== $name);
        }

        return $paramMatches && $valueMatches;
    }
    /**
     * 获取支持的location列表
     *
     * @param string $lang
     * @return array
     */

    public function getSupportedLocations(string $lang): array
    {
        // 查询 bingo_cms_model 表，获取所有 id 及其名称，且 model_id 不等于 5
        $cmsLocations = DB::table('cms_model')
            ->select('name as id', 'title')
            ->where('lang', '=', $lang)
            ->where('id', '<>', 5)
            ->get()
            ->toArray();

        // 手动添加 blog_ 开头的支持列表及其语言标识
        $blogLocations = [
            ['id' => 'blog_page', 'title' => T('CFM::blog_page')],
        ];

        // 查询 bingo_cms_content 表，获取 model_id 为 5 的所有记录
        $pageLocations = DB::table('cms_content')
            ->select('alias as id', 'title')->where('model_id', '=', 5)
            ->get()
            ->toArray();

        // 准备 locations 数据
        $locations = [
            LocationType::CMS->name() => $cmsLocations,
            LocationType::BLOG->name() => $blogLocations,
            LocationType::PAGE->name() => $pageLocations,
        ];

        // 生成返回数组
        $result = [];
        foreach (LocationType::cases() as $type) {
            $result[] = [
                'id' => $type->value,
                'title' => $type->name(),
                'locations' => $locations[$type->name()],
            ];
        }

        return $result;
    }

    public function deleteCfmMetaByContentId($content_id): void
    {

        DB::table('cfm_meta')->where('content_id', $content_id)->delete();
    }
    /**
     * 保存自定义字段数据
     *
     * @param array $data
     * @return void
     * @throws BizException
     */
    public function saveCustomFieldData(array $data): void
    {

        // 确保 fields 是一个数组，如果是字符串则进行解码
        if (is_string($data['fields'])) {
            $data['fields'] = json_decode($data['fields'], true);
        }

        // 确保 content_id 存在
        if (! isset($data['content_id'])) {
            BizException::throws(CFMErrorCode::SAVE_CUSTOM_FIELD_DATA_FAILED, 'Content ID is required');
        }

        // 确保 lang 存在
        if (! isset($data['lang'])) {
            BizException::throws(CFMErrorCode::SAVE_CUSTOM_FIELD_DATA_FAILED, 'Language is required');
        }

        // 检查 location 是否以 'cms_' 或 'blog_' 开头
        $location = $data['location'];
        if (preg_match('/^(cms_|blog_)/', $location)) {
            list($prefix, $name) = explode('_', $location, 2);
            // 验证名称是否存在
            if ($prefix === 'cms' && ! DB::table('cms_model')->where('name', $name)->exists()) {
                BizException::throws(CFMErrorCode::LOCATION_IS_NOT_VALID);
            }
        } else {
            BizException::throws(CFMErrorCode::LOCATION_IS_NOT_VALID);
        }

        // 获取 location 的字段配置
        $locationSettings = $this->getCustomFieldData($data['location'], $data['content_id']);
        $this->deleteCfmMetaByContentId($data['content_id']);
        foreach ($data['fields'] as $field) {
            // 遍历 locationSettings 获取所有字段配置
            $fieldSettings = [];
            foreach ($locationSettings as $group) {
                foreach ($group['fields'] as $setting) {
                    if (isset($field['key']) && $setting['settings']['key'] == $field['key']) { //兼容key值
                        $fieldSettings[] = $setting;
                        continue;
                    } else if (isset($setting['sub_fields'])) { //兼容key值
                        foreach ($setting['sub_fields'] as $sub_field) {
                            if ($sub_field['key'] === $field['key']) { //兼容key值
                                $fieldSettings[] = $sub_field;
                                continue;
                            }
                        }
                    }
                }
            }

            if (empty($fieldSettings)) {
                BizException::throws(CFMErrorCode::SAVE_CUSTOM_FIELD_DATA_FAILED, 'Invalid field key: ' . $field['key']);
            }

            $fieldSettings = array_shift($fieldSettings); // 获取字段配置

            $fieldWithSettings = array_merge($fieldSettings, [
                'value' => $field['value'],
                'settings' => array_merge($fieldSettings['settings'], [
                    'content_id' => $data['content_id'],
                    'lang' => $data['lang']
                ])
            ]);

            if (! $this->validateField($fieldWithSettings)) {
                // 验证字段数据
                BizException::throws(CFMErrorCode::SAVE_CUSTOM_FIELD_DATA_FAILED, 'Invalid field key for: ' . $fieldWithSettings['name']);
            }
            $this->saveField($fieldWithSettings, $data['content_id'], $data['lang']);
        }
    }


    /**
     * 验证字段数据
     *
     * @param array $fieldData
     * @return bool
     */
    protected function validateField(array $fieldData): bool
    {
        $fieldClass = 'Modules\\CFM\\Domain\\Fields\\' . $fieldData['type'] . 'Field';


        if (! class_exists($fieldClass)) {
            $fieldClass = 'Modules\\CFM\\Domain\\Fields\\TextField';
        }
        /** @var Field $field */
        $field = new $fieldClass($fieldData);
        return $field->validate($fieldData['value']);
    }


    /**
     * 保存字段数据
     *
     * @param array $fieldData
     * @param int $contentId
     * @param string $lang
     * @return void
     */
    protected function saveField(array $fieldData, int $contentId, string $lang): void
    {
        $fieldClass = 'Modules\\CFM\\Domain\\Fields\\' . $fieldData['type'] . 'Field';
        if (! class_exists($fieldClass)) {
            $fieldClass = 'Modules\\CFM\\Domain\\Fields\\TextField';
        }

        // 确保传递所有需要的数据
        $fieldData['settings']['content_id'] = $contentId;
        $fieldData['settings']['lang'] = $lang;

        /** @var Field $field */
        $field = new $fieldClass($fieldData);
        $field->save($fieldData['value']);
    }


    /**
     * 删除字段
     *
     * @param int $id
     * @return void
     */
    public function delete(int $id): void
    {
        $this->fieldRepository->delete($id);
    }
}
