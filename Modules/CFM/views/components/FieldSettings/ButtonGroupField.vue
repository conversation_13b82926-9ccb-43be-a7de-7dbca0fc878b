<template>
  <div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.fieldSet.choices') }}</div>
      <el-input type="textarea" v-model="choices" style="width: 50%" :placeholder="$t('CFM.fieldSet.choice_placeholder')" @blur="returnChoices" />
      <div class="botText">{{ $t('CFM.fieldSet.choiceInfo') }}</div>
    </div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.fieldSet.default_value') }}</div>
      <el-input type="textarea" v-model="default_value" style="width: 50%" :placeholder="$t('CFM.fieldSet.default_value_placeholder')" @blur="returnChoices" />
    </div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.fieldSet.return_value') }}</div>
      <el-radio-group v-model="return_format" @change="returnChoices">
        <el-radio :value="'value'">{{ $t('CFM.fieldSet.value') }}</el-radio>
        <el-radio :value="'label'">{{ $t('CFM.fieldSet.label') }}</el-radio>
        <el-radio :value="'array'">{{ $t('CFM.fieldSet.array') }}</el-radio>
      </el-radio-group>
      <div class="botText">{{ $t('CFM.fieldSet.return_formatInfo') }}</div>
    </div>
  </div>
</template>
  
  <script lang="ts" setup>
  import { ref,onMounted } from 'vue'
  const emits = defineEmits(["blur"])
  const choices = ref<String>('')
  const default_value = ref<String>('')
  const return_format = ref<String>('value')
  const { defaultValues } = defineProps({
    defaultValues: Object,
  })
  
  if (defaultValues) {
    if (defaultValues.choices) {
      let str = defaultValues.choices
      let item = str.split(',')
      choices.value = item.join('\n')
    }
    if (defaultValues.default_value) {
      default_value.value = defaultValues.default_value
    }
    if (defaultValues.return_format) {
      return_format.value = defaultValues.return_format
    }
  }
  
  const returnChoices = () => {
    const choicesObj = {
      choices: choices.value.split('\n'),
      default_value: default_value.value.split('\n'),
      return_format: return_format.value
    }
    emits('blur', choicesObj)
  }
  onMounted(() => {
    returnChoices()
  })
  </script>
  
  <style lang="scss" scoped>
  .text {
    width: 100%;
    padding: 20px 0;
    .textLabel {
      font-size: 15px;
      font-weight: 500;
      color: black;
      line-height: 30px;
      text-align: left;
    }
    .el-input {
      margin-right: 10px;
    }
    .botText {
      font-size: 14px;
      color: gray;
      font-weight: 300;
      height: 20px;
    }
  }
  </style>
  