<template>
  <div>
    <div class="field">
      <div class="field-label">{{ $t('CFM.detail.field') }}</div>
      <el-select v-model="clones" :placeholder="$t('CFM.detail.please_select')" multiple filterable @change="handleFieldChange">
        <el-option-group v-for="group in Options" :key="group.label" :label="group.label">
          <el-option v-for="option in group.options" :key="option.value" :label="option.label" :value="option.value"></el-option>
        </el-option-group>
      </el-select>
      <div class="field-label">{{ $t('CFM.detail.display') }}</div>
      <el-select v-model="displayOption" @change="handleFieldChange">
        <el-option :label="$t('CFM.detail.seamless')" value="seamless"></el-option>
        <el-option :label="$t('CFM.detail.group')" value="group"></el-option>
      </el-select>
    </div>
    <div class="flex items-center mb-2 text-sm" v-if="displayOption == 'group'">
      <el-radio-group v-model="layout" class="ml-4">
        <el-radio value="block" size="large">{{ $t('CFM.detail.block') }}</el-radio>
        <el-radio value="table" size="large">{{ $t('CFM.detail.table') }}</el-radio>
        <el-radio value="row" size="large">{{ $t('CFM.detail.row') }}</el-radio>
      </el-radio-group>
    </div>
    <div class="field" style="font-size: 15px; color: black;font-weight: 500;">
      <el-switch v-model="prefixLabel" @change="handleSwitchChange('prefixLabel')" /> {{ $t('CFM.detail.prefix_label') }}
      <div style="font-size: 13px" v-if="prefixLabel">{{ $t('CFM.detail.label_display_as') }}{{defaultValues.label }}%field_label%</div>
    </div>
    <div class="field" style="font-size: 15px; color: black;font-weight: 500;">
      <el-switch v-model="prefixName" @change="handleSwitchChange('prefixName')" /> {{ $t('CFM.detail.prefix_name') }}
      <div style="font-size: 13px" v-if="prefixName">{{ $t('CFM.detail.value_stored_as') }}{{defaultValues.name }}%field_name%</div>
    </div>
  </div>
</template>

  
<script lang="ts" setup>
import { ref, defineEmits, onMounted } from 'vue'
import http from '/admin/support/http'
const clones = ref<Array>([])
const displayOption = ref<string>('seamless')
const prefixLabel = ref<boolean>(false)
const prefixName = ref<boolean>(false)
const layout = ref<string>('block')
const emits = defineEmits(['update'])
const { defaultValues } = defineProps({
  defaultValues: Object,
})
if (defaultValues) {
  if (defaultValues.clone) {
    clones.value = defaultValues.clone
  }
  if (defaultValues.display) {
    displayOption.value = defaultValues.display
  }
  if (defaultValues.layout) {
    layout.value = defaultValues.layout
  }
  if (defaultValues.prefix_label) {
    prefixLabel.value = defaultValues.prefix_label === '1'
  }
  if (defaultValues.prefix_name) {
    prefixName.value = defaultValues.prefix_name === '1'
  }
}
const handleFieldChange = () => {
  let clone = {}
  clones.value.forEach((value, index) => {
    clone[index] = value
  })
  console.log('clones.value', clone)
  if (displayOption.value === 'group') {
    emits('update', {
      clone: clone,
      display: displayOption.value,
      layout: layout.value,
      prefix_label: prefixLabel.value ? '1' : '0',
      prefix_name: prefixName.value ? '1' : '0',
    })
  } else {
    emits('update', {
      clone: clone,
      display: displayOption.value,
      prefix_label: prefixLabel.value ? '1' : '0',
      prefix_name: prefixName.value ? '1' : '0',
    })
  }
}

const handleSwitchChange = (type: string) => {
  let clone = {}
  clones.value.forEach((value, index) => {
    clone[index] = value
  })
  if (type === 'prefixLabel') {
    if (displayOption.value === 'group') {
      emits('update', {
        clone: clone,
        display: displayOption.value,
        layout: layout.value,
        prefix_label: prefixLabel.value ? '1' : '0',
        prefix_name: prefixName.value ? '1' : '0',
      })
    } else {
      emits('update', {
        clone: clone,
        display: displayOption.value,
        prefix_label: prefixLabel.value ? '1' : '0',
        prefix_name: prefixName.value ? '1' : '0',
      })
    }
  } else if (type === 'prefixName') {
    if (displayOption.value === 'group') {
      emits('update', { clone: clone, display: displayOption.value, layout: layout.value, prefix_label: prefixLabel.value ? '1' : '0', prefix_name: prefixName.value ? '1' : '0' })
    } else {
      emits('update', { clone: clone, display: displayOption.value, prefix_label: prefixLabel.value ? '1' : '0', prefix_name: prefixName.value ? '1' : '0' })
    }
  }
}
const Options = ref([])
const getFieldGroupsList = async () => {
  try {
    const res = await http.get('/cfm/field-group')
    Options.value = res.data.data.map(item => {
      const groupOption = {
        label: item.title,
        options: item.fields.map(field => ({
          label: field.label,
          value: field.key,
        })),
      }
      // 添加一个选择所有选项
      groupOption.options.unshift({
        label: `所有${item.title}选项`,
        value: item.key,
      })
      return groupOption
    })
    handleFieldChange()
  } catch (error) {
    console.log(error)
  }
}
onMounted(() => {
  getFieldGroupsList()
})
</script>
  
  <style scoped>
.field {
  margin-bottom: 20px;
}

.field-label {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 10px;
  color: black;
  line-height: 30px;
}

.el-input,
.el-select {
  width: 50%;
}
</style>
  