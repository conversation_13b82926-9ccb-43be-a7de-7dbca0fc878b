<template>
  <div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.filter_by_post_type') }}</div>
      <el-select v-model="postType" :placeholder="$t('CFM.detail.all_post_types')" multiple @change="handleChange">
        <el-option v-for="type in postTypeOptions" :key="type.value" :label="type.label" :value="type.value" />
      </el-select>
    </div>

    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.filter_by_post_status') }}</div>
      <el-select v-model="postStatus" :placeholder="$t('CFM.detail.any_post_status')" multiple @change="handleChange">
        <el-option v-for="status in postStatusOptions" :key="status.value" :label="status.label" :value="status.value" />
      </el-select>
    </div>

    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.filter_by_taxonomy') }}</div>
      <el-select v-model="taxonomy" :placeholder="$t('CFM.detail.all_taxonomies')" multiple @change="handleChange">
        <el-option v-for="tax in taxonomyOptions" :key="tax.value" :label="tax.label" :value="tax.value" />
      </el-select>
    </div>

    <div class="filters">
      <el-checkbox v-model="filters.search" @change="handleChange">{{ $t('CFM.detail.search') }}</el-checkbox>
      <el-checkbox v-model="filters.postType" @change="handleChange">{{ $t('CFM.detail.post_type') }}</el-checkbox>
      <el-checkbox v-model="filters.taxonomy" @change="handleChange">{{ $t('CFM.detail.taxonomy') }}</el-checkbox>
    </div>

    <div class="textLabel">{{ $t('CFM.detail.return_format') }}</div>
    <el-radio-group v-model="returnFormat" @change="handleChange">
      <el-radio label="object">{{ $t('CFM.detail.post_object') }}</el-radio>
      <el-radio label="id">{{ $t('CFM.detail.post_id') }}</el-radio>
    </el-radio-group>
  </div>
</template>


<script lang="ts" setup>
import { ref, onMounted } from 'vue'

const postType = ref<string[]>([])
const postStatus = ref<string[]>([])
const taxonomy = ref<string[]>([])
const returnFormat = ref<string>('object')

const filters = ref({
  search: false,
  postType: false,
  taxonomy: false,
})

const postTypeOptions = ref([
  { label: 'Post', value: 'post' },
  { label: 'Page', value: 'page' },
  // Add more post type options here
])

const postStatusOptions = ref([
  { label: 'Published', value: 'publish' },
  { label: 'Draft', value: 'draft' },
  // Add more post status options here
])

const taxonomyOptions = ref([
  { label: 'Category', value: 'category' },
  { label: 'Tag', value: 'post_tag' },
  // Add more taxonomy options here
])

const { defaultValues } = defineProps({
  defaultValues: Object,
})
if (defaultValues) {
  if (defaultValues.postType) {
    postType.value = defaultValues.postType.split(',')
  }
  if (defaultValues.postStatus) {
    postStatus.value = defaultValues.postStatus.split(',')
  }
  if (defaultValues.taxonomy) {
    taxonomy.value = defaultValues.taxonomy.split(',')
  }
  if (defaultValues.filters) {
    const parsedFilters = defaultValues.filters
    filters.value.search = parsedFilters.includes('search')
    filters.value.postType = parsedFilters.includes('postType')
    filters.value.taxonomy = parsedFilters.includes('taxonomy')
  }
  if (defaultValues.returnFormat) {
    returnFormat.value = defaultValues.returnFormat
  }
}

const emits = defineEmits(['change'])

const handleChange = () => {
  emits('change', {
    postType: postType.value,
    postStatus: postStatus.value,
    taxonomy: taxonomy.value,
    filters: {
      search: filters.value.search ? 'search' : '',
      postType: filters.value.postType? 'postType' : '',
      taxonomy: filters.value.taxonomy? 'taxonomy' : '',
    },
    return_format: returnFormat.value,
  })
}
onMounted(() => {
  handleChange()
})
</script>

<style scoped lang="scss">
.text {
  width: 100%;
  padding: 20px 0;
}
.textLabel {
  font-size: 15px;
  font-weight: 500;
  color: black;
  line-height: 30px;
}
.el-select,
.el-checkbox,
.el-radio-group {
  margin-bottom: 20px;
}
.filters {
  border: 1px solid #dcdfe6;
  padding: 10px;
  border-radius: 4px;
}
.textre {
  width: 100%;
  font-size: 15px;
  font-weight: 500;
  color: black;
  text-align: left;
  line-height: 50px;
}
</style>
