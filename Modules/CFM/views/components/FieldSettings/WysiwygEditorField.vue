<template>
  <div class="text">
    <div class="textLabel">{{ $t('CFM.detail.default_value') }}</div>
    <el-input v-model="default_value" style="width: 300px" @blur="getTextDefault" />
    <div class="botText">{{ $t('CFM.detail.display_on_new_post') }}</div>
  </div>
</template>


<script lang='ts' setup>
import { ref, onMounted } from 'vue'
const emits = defineEmits(['blur'])
const { defaultValues } = defineProps({
  defaultValues: Object,
})
const default_value = ref<String>('')
if (defaultValues) {
  if (defaultValues.default_value !== '') {
    default_value.value = defaultValues.default_value
  }
}
const getTextDefault = () => {
  emits('blur', { default_value: default_value.value })
}
onMounted(() => {
  getTextDefault()
})
</script>

<style lang="scss" scoped>
.text {
  width: 100%;
  padding: 20px 0;
  border-bottom: 1px solid #ccc;
  .textLabel {
    font-size: 15px;
    font-weight: 500;
    color: black;
    text-align: left;
    line-height: 30px;
  }
  .el-input {
    height: 40px;
  }
  .botText {
    font-size: 14px;
    color: gray;
    font-weight: 300;
    height: 20px;
  }
}
</style>
