<template>
  <div class="text">
    <div class="textLabel">{{ $t('CFM.detail.default_value') }}</div>
    <el-input v-model="textDefaultValue" style="width: 300px" @blur="returnURl" />
    <div class="botText">{{ $t('CFM.detail.display_on_new_post') }}</div>
  </div>
</template>

  
  <script lang='ts' setup>
import { ref, onMounted } from 'vue'
const textDefaultValue = ref<String>('')
const emit = defineEmits(['blur'])
const returnURl = () => {
  emit('blur', { default_value: textDefaultValue.value })
}
const { defaultValues } = defineProps({
  defaultValues: Object,
})
if (defaultValues) {
  if (defaultValues.default_value) {
    textDefaultValue.value = defaultValues.default_value
  }
}
onMounted(() => {
  returnURl()
})
</script>
  
  <style lang="scss" scoped>
.text {
  width: 100%;
  padding: 20px 0;
  border-bottom: 1px solid #ccc;
  .textLabel {
    font-size: 15px;
    font-weight: 500;
    color: black;
    line-height: 10px;
    text-align: left;
    line-height: 30px;
  }
  .el-input {
    height: 40px;
  }
  .botText {
    font-size: 14px;
    color: gray;
    font-weight: 300;
    height: 20px;
  }
}
</style>