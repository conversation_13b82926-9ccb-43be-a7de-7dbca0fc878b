<template>
  <div class="taxonomy-component">
    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.taxonomy') }}</div>
      <el-select v-model="taxonomy" :placeholder="$t('CFM.detail.select_taxonomy')" style="width: 50%" @change="updateTaxonomy">
        <el-option v-for="option in taxonomyOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
      </el-select>
      <div class="botText">{{ $t('CFM.detail.select_taxonomy_displayed') }}</div>
    </div>

    <div class="text">
      <el-switch v-model="createTerms" :active-text="$t('CFM.detail.create_terms')" @change="updateTaxonomy"></el-switch>
      <div class="botText">{{ $t('CFM.detail.allow_create_terms') }}</div>
    </div>

    <div class="text">
      <el-switch v-model="saveTerms" :active-text="$t('CFM.detail.save_terms')" @change="updateTaxonomy"></el-switch>
      <div class="botText">{{ $t('CFM.detail.connect_selected_terms') }}</div>
    </div>

    <div class="text">
      <el-switch v-model="loadTerms" :active-text="$t('CFM.detail.load_terms')" @change="updateTaxonomy"></el-switch>
      <div class="botText">{{ $t('CFM.detail.load_value_from_terms') }}</div>
    </div>

    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.return_value') }}</div>
      <el-radio-group v-model="returnValue" @change="updateTaxonomy">
        <el-radio value="object">{{ $t('CFM.detail.term_object') }}</el-radio>
        <el-radio value="id">{{ $t('CFM.detail.term_id') }}</el-radio>
      </el-radio-group>
    </div>

    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.appearance') }}</div>
      <el-select v-model="appearance" :placeholder="$t('CFM.detail.select_appearance')" style="width: 50%" @change="updateTaxonomy">
        <el-option :label="$t('CFM.detail.checkbox')" value="checkbox"></el-option>
        <el-option :label="$t('CFM.detail.multi_select')" value="multi_select"></el-option>
        <el-option :label="$t('CFM.detail.radio_buttons')" value="radio"></el-option>
        <el-option :label="$t('CFM.detail.select')" value="select"></el-option>
      </el-select>
      <div class="botText">{{ $t('CFM.detail.select_field_appearance') }}</div>
    </div>
  </div>
</template>

  
  <script lang="ts" setup>
import { ref, onMounted } from 'vue'

const taxonomy = ref<string>('')
const createTerms = ref<boolean>(false)
const saveTerms = ref<boolean>(false)
const loadTerms = ref<boolean>(false)
const returnValue = ref<string>('id')
const appearance = ref<string>('checkbox')

const taxonomyOptions = ref<Array<{ label: string; value: string }>>([
  { label: '分类', value: 'category' },
  { label: '标签', value: 'tag' },
  { label: '文章格式', value: 'post_format' },
  // 根据需要添加更多选项
])

const emit = defineEmits(['blur'])

const updateTaxonomy = () => {
  emit('blur', {
    taxonomy: taxonomy.value,
    createTerms: createTerms.value,
    saveTerms: saveTerms.value,
    loadTerms: loadTerms.value,
    returnValue: returnValue.value,
    appearance: appearance.value,
  })
}

onMounted(() => {
  updateTaxonomy()
})
</script>
  
  <style scoped lang="scss">
.taxonomy-component {
  width: 100%;
  padding: 20px 0;
  .text {
    margin-bottom: 20px;
  }
  .textLabel {
    font-size: 15px;
    font-weight: 500;
    color: black;
    text-align: left;
    line-height: 30px;
  }
  .botText {
    font-size: 14px;
    color: gray;
    font-weight: 300;
    height: 20px;
  }
}
</style>
  