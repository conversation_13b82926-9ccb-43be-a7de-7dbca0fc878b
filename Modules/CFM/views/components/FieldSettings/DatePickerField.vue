<template>
  <div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.display_format') }}</div>
      <el-radio-group v-model="displayFormat" @change="returnSettings">
        <el-radio :value="'d/m/Y'">
          <span>{{ currentDate.format('DD/MM/YYYY') }}</span><code>d/m/Y</code>
        </el-radio>
        <el-radio :value="'m/d/Y'">
          <span>{{ currentDate.format('MM/DD/YYYY') }}</span><code>m/d/Y</code>
        </el-radio>
        <el-radio :value="'F j, Y'">
          <span>{{ currentDate.format('MMMM D, YYYY') }}</span><code>F j, Y</code>
        </el-radio>
        <el-radio :value="'Ymd'">
          <span>{{ currentDate.format('YYYYMMDD') }}</span><code>Ymd</code>
        </el-radio>
        <el-radio :value="'custom'">
          <span>{{ $t('CFM.detail.custom') }}</span>
          <el-input v-if="displayFormat === 'custom'" v-model="customDisplayFormat" :placeholder="$t('CFM.detail.custom_placeholder')" />
        </el-radio>
      </el-radio-group>
    </div>

    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.return_format') }}</div>
      <el-radio-group v-model="returnFormat" @change="returnSettings">
        <el-radio :value="'d/m/Y'">
          <span>{{ currentDate.format('DD/MM/YYYY') }}</span><code>d/m/Y</code>
        </el-radio>
        <el-radio :value="'m/d/Y'">
          <span>{{ currentDate.format('MM/DD/YYYY') }}</span><code>m/d/Y</code>
        </el-radio>
        <el-radio :value="'F j, Y'">
          <span>{{ currentDate.format('MMMM D, YYYY') }}</span><code>F j, Y</code>
        </el-radio>
        <el-radio :value="'Ymd'">
          <span>{{ currentDate.format('YYYYMMDD') }}</span><code>Ymd</code>
        </el-radio>
        <el-radio :value="'custom'">
          <span>{{ $t('CFM.detail.custom') }}</span>
          <el-input v-if="returnFormat === 'custom'" v-model="customReturnFormat" :placeholder="$t('CFM.detail.custom_placeholder')" />
        </el-radio>
      </el-radio-group>
    </div>

    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.week_starts_on') }}</div>
      <el-select v-model="weekStartsOn" :placeholder="$t('CFM.detail.select_day')" @change="returnSettings">
        <el-option v-for="day in weekDays" :key="day.value" :label="day.label" :value="day.value" />
      </el-select>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import dayjs from 'dayjs'

const emits = defineEmits(['change'])
const { defaultValues } = defineProps({
  defaultValues: Object,
})

const displayFormat = ref<string>('d/m/Y')
const customDisplayFormat = ref<string>('')
const returnFormat = ref<string>('d/m/Y')
const customReturnFormat = ref<string>('')
const weekStartsOn = ref<string>('')

const currentDate = ref(dayjs())

const weekDays = [
  { value: '1', label: '星期一' },
  { value: '2', label: '星期二' },
  { value: '3', label: '星期三' },
  { value: '4', label: '星期四' },
  { value: '5', label: '星期五' },
  { value: '6', label: '星期六' },
  { value: '7', label: '星期日' }
]

if (defaultValues) {
  if (defaultValues.display_format) {
    displayFormat.value = defaultValues.display_format
  }
  if (defaultValues.return_format) {
    returnFormat.value = defaultValues.return_format
  }
  if (defaultValues.first_day) {
    weekStartsOn.value = defaultValues.first_day
  }
}

const returnSettings = () => {
  emits('change', {
    display_format: displayFormat.value === 'custom' ? customDisplayFormat.value : displayFormat.value,
    return_format: returnFormat.value === 'custom' ? customReturnFormat.value : returnFormat.value,
    first_day: weekStartsOn.value
  })
}

onMounted(() => {
  returnSettings()
})
</script>

<style lang="scss" scoped>
.text {
  width: 100%;
  padding: 20px 0;
  .textLabel {
    font-size: 15px;
    font-weight: 500;
    color: black;
    text-align: left;
    line-height: 30px;
  }
  .el-radio-group {
    margin-top: 10px;
  }
  .el-input {
    margin-top: 10px;
    width: 200px;
  }
}
</style>
