<template>
  <div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.default_value') }}</div>
      <el-input v-model="defaultValue" :placeholder="$t('CFM.detail.default_value_placeholder')" style="width: 300px" @blur="returnSettings" />
    </div>
    <div class="text">
      <el-switch v-model="enableTransparency" @change="returnSettings" /> {{ $t('CFM.detail.enable_transparency') }}
    </div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.return_format') }}</div>
      <el-radio-group v-model="returnFormat" @change="returnSettings">
        <el-radio value="string">{{ $t('CFM.detail.hex_string') }}</el-radio>
        <el-radio value="array">{{ $t('CFM.detail.rgba_array') }}</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

  
  <script lang="ts" setup>
  import { ref, onMounted } from 'vue'
  const emits = defineEmits(['blur', 'change'])
  const { defaultValues } = defineProps({
    defaultValues: Object,
  })
  const defaultValue = ref<string>('')
  const enableTransparency = ref<boolean>(false)
  const returnFormat = ref<string>('string')
  
  if (defaultValues) {
    if (defaultValues.default_value) {
      defaultValue.value = defaultValues.default_value
    }
    if (defaultValues.enable_opacity !== undefined) {
      enableTransparency.value = defaultValues.enable_opacity ==='1'
    }
    if (defaultValues.return_format) {
      returnFormat.value = defaultValues.return_format
    }
  }
  
  const returnSettings = () => {
    emits('change', {
      enable_opacity: enableTransparency.value? '1' : '0',
      return_format: returnFormat.value,
      default_value: defaultValue.value 
    })
  }
  
  onMounted(() => {
    returnSettings()
  })
  </script>
  
  <style lang="scss" scoped>
  .text {
    width: 100%;
    padding: 20px 0;
    border-bottom: 1px solid #ccc;
    font-size: 15px;
      font-weight: 500;
      color: black;
      text-align: left;
      line-height: 30px;
    .textLabel {
      font-size: 15px;
      font-weight: 500;
      color: black;
      text-align: left;
      line-height: 30px;
    }
    .el-input {
      height: 40px;
    }
    .el-radio-group {
      margin-top: 10px;
    }
  }
  </style>
  