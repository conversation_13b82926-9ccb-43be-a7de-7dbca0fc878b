<template>
  <div class="tab-component">
    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.placement') }}</div>
      <el-select v-model="placement" :placeholder="$t('CFM.detail.select_placement')" style="width: 50%" @change="updateNewTabGroup">
        <el-option :label="$t('CFM.detail.top_aligned')" value="top"></el-option>
        <el-option :label="$t('CFM.detail.left_aligned')" value="left"></el-option>
      </el-select>
    </div>

    <div class="text">
      <el-switch v-model="newTabGroup" :active-text="$t('CFM.detail.new_tab_group')" @change="updateNewTabGroup"></el-switch>
      <div class="botText">{{ $t('CFM.detail.start_new_tab_group') }}</div>
    </div>
  </div>
</template>

  
  <script lang="ts" setup>
  import { ref ,onMounted} from 'vue'
  
  const placement = ref<string>('top')
  const newTabGroup = ref<boolean>(false)
  
  const emit = defineEmits(['update:settings'])
  const { defaultValues } = defineProps({
    defaultValues: Object,
  })
  if (defaultValues) {
    if (defaultValues.newTabGroup) {
      newTabGroup.value = defaultValues.newTabGroup === '1'
    }
    if (defaultValues.placement) {
      placement.value = defaultValues.placement
    }
  }
  
  const updateNewTabGroup = () => {
    emit('update:settings', { newTabGroup: newTabGroup.value,placement: placement.value })
  }
  onMounted(() => {
    updateNewTabGroup()
  })
  </script>
  
  <style scoped lang="scss">
  .tab-component {
    width: 100%;
    padding: 20px 0;
    .text {
      margin-bottom: 20px;
    }
    .textLabel {
      font-size: 15px;
      font-weight: 500;
      color: black;
      text-align: left;
      line-height: 30px;
    }
    .botText {
      font-size: 14px;
      color: gray;
      font-weight: 300;
      height: 20px;
    }
  }
  </style>
  