<template>
  <div class="text">
    <div class="textLabel">{{ $t('CFM.detail.default_value') }}</div>
    <el-input-number v-model="range" :min="1" :max="10000000" controls-position="right" size="large" @change="returnRange" />
    <div class="botText">{{ $t('CFM.detail.display_on_new_post') }}</div>
  </div>
</template>

  
  <script lang='ts' setup>
import { ref, onMounted } from 'vue'
const range = ref<number>(0)
const emit = defineEmits(['change'])
const returnRange = () => {
  emit('change', { default_value: range.value })
}
const { defaultValues } = defineProps({
  defaultValues: Object,
})
if (defaultValues) {
  if (defaultValues.default_value) {
    range.value = Number(defaultValues.default_value)
  }
}
onMounted(() => {
  returnRange()
})
</script>
  
  <style lang="scss" scoped>
.text {
  width: 100%;
  padding: 20px 0;
  border-bottom: 1px solid #ccc;
  .textLabel {
    font-size: 15px;
    font-weight: 500;
    color: black;
    line-height: 10px;
    text-align: left;
    line-height: 30px;
  }
  .el-input {
    height: 40px;
  }
  .botText {
    font-size: 14px;
    color: gray;
    font-weight: 300;
    height: 20px;
  }
}
</style>