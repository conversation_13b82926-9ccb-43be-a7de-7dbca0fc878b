<template>
  <div class="user-component">
    <div class="filter-section">
      <div class="text">
        <div class="textLabel">{{ $t('CFM.detail.filter_by_role') }}</div>
        <el-select v-model="role" filterable :placeholder="$t('CFM.detail.all_user_roles')" @change="updateReturnFormat">
          <el-option v-for="roleOption in roleOptions" :key="roleOption.value" :label="roleOption.label" :value="roleOption.value" />
        </el-select>
      </div>
    </div>

    <div class="return-section">
      <div class="text">
        <div class="textLabel">{{ $t('CFM.detail.return_format') }}</div>
        <el-radio-group v-model="returnFormat" @change="updateReturnFormat">
          <el-radio :value="'array'">{{ $t('CFM.detail.user_array') }}</el-radio>
          <el-radio :value="'object'">{{ $t('CFM.detail.user_object') }}</el-radio>
          <el-radio :value="'id'">{{ $t('CFM.detail.user_id') }}</el-radio>
        </el-radio-group>
      </div>
    </div>

    <div class="multiple-section">
      <div class="text">
        <el-switch v-model="selectMultiple" :active-text="$t('CFM.detail.select_multiple')" :inactive-text="$t('CFM.detail.select_multiple')" @change="updateReturnFormat"></el-switch>
        <div class="botText">{{ $t('CFM.detail.allow_multiple_selection') }}</div>
      </div>
    </div>
  </div>
</template>

  
  <script lang="ts" setup>
import { ref, onMounted } from 'vue'

const role = ref<string | null>(null)
const returnFormat = ref<string>('array')
const selectMultiple = ref<boolean>(false)

const roleOptions = ref([
  { value: 'administrator', label: 'Administrator' },
  { value: 'editor', label: 'Editor' },
  { value: 'author', label: 'Author' },
  { value: 'contributor', label: 'Contributor' },
  { value: 'subscriber', label: 'Subscriber' },
])

const emit = defineEmits(['blur'])
const { defaultValues } = defineProps({
  defaultValues: Object,
})
if (defaultValues) {
  if (defaultValues.role) {
    role.value = defaultValues.role
  }
  if (defaultValues.returnFormat) {
    returnFormat.value = defaultValues.returnFormat
  }
  if (defaultValues.selectMultiple) {
    selectMultiple.value = defaultValues.selectMultiple === '1'
  }
}
const updateReturnFormat = () => {
  emit('blur', { role: role.value, returnFormat: returnFormat.value, selectMultiple: selectMultiple.value ? '1' : '0' })
}
onMounted(() => {
  updateReturnFormat()
})
</script>
  
  <style scoped>
.user-component {
  width: 100%;
  padding: 20px 0;
}

.filter-section,
.return-section,
.multiple-section {
  margin-bottom: 20px;
}

.text {
  margin-bottom: 20px;
}

.textLabel {
  font-size: 15px;
  font-weight: 500;
  color: black;
  text-align: left;
  line-height: 30px;
}

.botText {
  font-size: 14px;
  color: gray;
  font-weight: 300;
  height: 20px;
}
</style>
  