<template>
  <div class="true-false">
    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.message') }}</div>
      <el-input v-model="message" style="width: 100%" :placeholder="$t('CFM.detail.checkbox_text_placeholder')" @blur="returnTextMessage" />
      <div class="botText">{{ $t('CFM.detail.checkbox_text_placeholder') }}</div>
    </div>
    <div class="default-value">
      <el-checkbox v-model="defaultValue" @change="returnTextMessage">{{ $t('CFM.detail.default_value') }}</el-checkbox>
    </div>
  </div>
</template>

  
  <script lang='ts' setup>
import { ref, onMounted } from 'vue'
const emits = defineEmits(['blur'])
const { defaultValues } = defineProps({
  defaultValues: Object,
})
const message = ref<string>('')
const defaultValue = ref<boolean>(false)
if (defaultValues) {
  if (defaultValues.message) {
    message.value = defaultValues.message
  }
  if (defaultValues.default_value) {
    defaultValue.value = defaultValues.default_value === '1'
  }
}

const returnTextMessage = () => {
  emits('blur', {
    message: message.value,
    default_value: defaultValue.value ? '1' : '0',
  })
}
onMounted(() => {
  returnTextMessage()
})
</script>
  
  <style lang="scss" scoped>
.true-false {
  width: 100%;
  padding: 20px 0;
  .textLabel {
    font-size: 15px;
    font-weight: 500;
    color: black;
    text-align: left;
    line-height: 30px;
  }
  .el-input {
    height: 40px;
  }
  .botText {
    font-size: 14px;
    color: gray;
    font-weight: 300;
  }
  .default-value {
    margin-top: 10px;
  }
}
</style>
  