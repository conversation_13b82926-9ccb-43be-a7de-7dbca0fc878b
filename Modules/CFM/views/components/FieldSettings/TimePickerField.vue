<template>
  <div class="time-picker-component">
    <div class="format-section">
      <div class="text">
        <div class="textLabel">{{ $t('CFM.detail.display_format') }}</div>
        <el-radio-group v-model="displayFormat" @change="updateDisplayFormat">
          <el-radio :value="'g:i a'">
            <span>{{ displayFormatExamples['g:i a'] }}</span>
            <code>g:i a</code>
          </el-radio>
          <el-radio :value="'H:i:s'">
            <span>{{ displayFormatExamples['H:i:s'] }}</span>
            <code>H:i:s</code>
          </el-radio>
          <el-radio :value="'custom'">
            {{ $t('CFM.detail.custom') }}:
            <el-input v-model="customDisplayFormat" :placeholder="$t('CFM.detail.custom_format')" @blur="updateDisplayFormat"></el-input>
          </el-radio>
        </el-radio-group>
        <div class="botText">{{ $t('CFM.detail.display_format_description') }}</div>
      </div>
    </div>

    <div class="format-section">
      <div class="text">
        <div class="textLabel">{{ $t('CFM.detail.return_format') }}</div>
        <el-radio-group v-model="returnFormat" @change="updateDisplayFormat">
          <el-radio :value="'g:i a'">
            <span>{{ returnFormatExamples['g:i a'] }}</span>
            <code>g:i a</code>
          </el-radio>
          <el-radio :value="'H:i:s'">
            <span>{{ returnFormatExamples['H:i:s'] }}</span>
            <code>H:i:s</code>
          </el-radio>
          <el-radio :value="'custom'">
            {{ $t('CFM.detail.custom') }}:
            <el-input v-model="customReturnFormat" :placeholder="$t('CFM.detail.custom_format')" @blur="updateDisplayFormat"></el-input>
          </el-radio>
        </el-radio-group>
        <div class="botText">{{ $t('CFM.detail.return_format_description') }}</div>
      </div>
    </div>
  </div>
</template>

  
  <script lang="ts" setup>
import { ref, onMounted } from 'vue'

const displayFormat = ref<string>('g:i a')
const returnFormat = ref<string>('g:i a')
const customDisplayFormat = ref<string>('')
const customReturnFormat = ref<string>('')

const displayFormatExamples = ref<{ [key: string]: string }>({
  'g:i a': new Date().toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric' }),
  'H:i:s': new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false }),
})

const returnFormatExamples = ref<{ [key: string]: string }>({
  'g:i a': new Date().toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric' }),
  'H:i:s': new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false }),
})

const emit = defineEmits(['blur'])
const { defaultValues } = defineProps({
  defaultValues: Object,
})
if (defaultValues) {
  if (defaultValues.displayFormat) {
    displayFormat.value = defaultValues.displayFormat
  }
  if (defaultValues.returnFormat) {
    returnFormat.value = defaultValues.returnFormat
  }
}
const updateDisplayFormat = () => {
  emit('blur', {
    display_format: displayFormat.value === 'custom' ? customDisplayFormat.value : displayFormat.value,
    return_format: returnFormat.value === 'custom' ? customReturnFormat.value : returnFormat.value,
  })
}
onMounted(() => {
  updateDisplayFormat()
})
</script>
  
  <style scoped>
.time-picker-component {
  width: 100%;
  padding: 20px 0;
}

.format-section {
  display: inline-block;
  width: 48%;
  vertical-align: top;
}

.text {
  margin-bottom: 20px;
}

.textLabel {
  font-size: 15px;
  font-weight: 500;
  color: black;
  text-align: left;
  line-height: 30px;
}

.botText {
  font-size: 14px;
  color: gray;
  font-weight: 300;
  height: 20px;
}
</style>
  