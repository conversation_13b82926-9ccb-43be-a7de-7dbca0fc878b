<template>
  <div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.field.introuduction') }}</div>
      <el-input type="textarea" v-model="value1" style="width: 50%" :placeholder="$t('CFM.detail.introduction')" @blur="returnUrl" />
      <div class="botText">{{ $t('CFM.detail.instructions_for_editors') }}</div>
    </div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.field.placeholder_text') }}</div>
      <el-input v-model="value2" style="width: 50%" @blur="returnUrl" />
      <div class="botText">{{ $t('CFM.detail.appear_in_input') }}</div>
    </div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.field.wrapper_attributes') }}</div>
      <span class="widthchose">{{ $t('CFM.detail.width') }}</span>
      <el-input-number v-model="value5" :min="1" :max="100" controls-position="right" size="large" @change="returnUrl" />
      <span class="widthchose1">%</span>
      <el-input v-model="value6" style="width: 15%" @blur="returnUrl">
        <template #prepend>class</template>
      </el-input>
      <el-input v-model="value7" style="width: 15%" @blur="returnUrl">
        <template #prepend>id</template>
      </el-input>
    </div>
  </div>
</template>

  
  <script lang="ts" setup>
import { ref, onMounted } from 'vue'

const value1 = ref<String>('')
const value2 = ref<String>('')
const value5 = ref<Number>(100)
const value6 = ref<String>('testclass')
const value7 = ref<String>('testid')
const emits = defineEmits(['blur'])
const returnUrl = () => {
  const url = {
    instructions: value1.value,
    placeholder: value2.value,
    width: value5.value,
    class: value6.value,
    id: value7.value,
  }
  emits('blur', url)
}
const { defaultValues } = defineProps({
  defaultValues: Object,
})
if (defaultValues) {
  if (defaultValues.instructions) {
    value1.value = defaultValues.instructions
  }
  if (defaultValues.placeholder) {
    value2.value = defaultValues.placeholder
  }
  if (defaultValues.width) {
    value5.value = Number(defaultValues.width)
  }
  if (defaultValues.class) {
    value6.value = defaultValues.class
  }
  if (defaultValues.id) {
    value7.value = defaultValues.id
  }
}
onMounted(() => {
  returnUrl()
})
</script>
  
  <style lang="scss" scoped>
.text {
  width: 100%;
  padding: 20px 0;
  .textLabel {
    font-size: 15px;
    font-weight: 500;
    color: black;
    line-height: 10px;
    text-align: left;
    line-height: 30px;
  }
  .el-input {
    height: 40px;
    margin-right: 10px;
  }
  .el-input-number {
    height: 40px;
  }
  .botText {
    font-size: 14px;
    color: gray;
    font-weight: 300;
    height: 20px;
  }
  .widthchose {
    display: inline-block;
    width: 50px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: rgb(249, 250, 251);
    border: 1px solid #ccc;
    font-size: 15px;
    font-weight: 200;
    color: rgb(123, 140, 170);
    border-radius: 4px;
    margin-right: 5px;
  }
  .widthchose1 {
    display: inline-block;
    width: 30px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: rgb(249, 250, 251);
    border: 1px solid #ccc;
    font-size: 15px;
    font-weight: 200;
    color: rgb(123, 140, 170);
    margin-right: 10px;
    border-radius: 4px;
    margin-left: 5px;
  }
}
</style>