<template>
  <div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.field.introuduction') }}</div>
      <el-input type="textarea" v-model="instructions" style="width: 50%" :placeholder="$t('CFM.detail.instructions_placeholder')" @blur="returnInstructions" />
      <div class="botText">{{ $t('CFM.detail.instructions_for_editors') }}</div>
    </div>
    <div class="text">
      <el-switch v-model="ui" :active-text="$t('CFM.detail.stylized_ui')" @change="returnInstructions"></el-switch>
    </div>
    <div v-if="ui" class="ui-text">
      <div class="text">
        <div class="textLabel">{{ $t('CFM.detail.ui_on_text') }}</div>
        <el-input v-model="uiOnText" style="width: 50%" :placeholder="$t('CFM.detail.ui_on_text_placeholder')" @blur="returnInstructions" />
        <div class="botText">{{ $t('CFM.detail.ui_on_text') }}</div>
      </div>
      <div class="text">
        <div class="textLabel">{{ $t('CFM.detail.ui_off_text') }}</div>
        <el-input v-model="uiOffText" style="width: 50%" :placeholder="$t('CFM.detail.ui_off_text_placeholder')" @blur="returnInstructions" />
        <div class="botText">{{ $t('CFM.detail.ui_off_text') }}</div>
      </div>
    </div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.field.wrapper_attributes') }}</div>
      <span class="widthchose">{{ $t('CFM.detail.width') }}</span>
      <el-input-number v-model="wrapperWidth" :min="1" :max="100" controls-position="right" size="large" @change="returnInstructions" />
      <span class="widthchose1">%</span>
      <el-input v-model="wrapperClass" style="width: 15%" @blur="returnInstructions">
        <template #prepend>class</template>
      </el-input>
      <el-input v-model="wrapperId" style="width: 15%" @blur="returnInstructions">
        <template #prepend>id</template>
      </el-input>
    </div>
  </div>
</template>

  
  <script lang='ts' setup>
import { ref, onMounted } from 'vue'
const emits = defineEmits(['blur', 'change'])
const { defaultValues } = defineProps({
  defaultValues: Object,
})
const instructions = ref<string>(defaultValues?.instructions || '')
const ui = ref<boolean>(defaultValues?.ui === '1' || false)
const uiOnText = ref<string>(defaultValues?.ui_on_text || '')
const uiOffText = ref<string>(defaultValues?.ui_off_text || '')
const wrapperWidth = ref<Number>(Number(defaultValues?.width) || 100)
const wrapperClass = ref<string>(defaultValues?.class || '')
const wrapperId = ref<string>(defaultValues?.id || '')

const returnInstructions = () => {
  emits('blur', {
    instructions: instructions.value,
    ui: ui.value ? '1' : '0',
    ui_on_text: uiOnText.value,
    ui_off_text: uiOffText.value,
    width: wrapperWidth.value,
    class: wrapperClass.value,
    id: wrapperId.value,
  })
}
onMounted(() => {
  returnInstructions()
})
</script>
  
  <style lang="scss" scoped>
.text {
  width: 100%;
  padding: 20px 0;
  .textLabel {
    font-size: 15px;
    font-weight: 500;
    color: black;
    line-height: 10px;
    text-align: left;
    line-height: 30px;
  }
  .el-input {
    height: 40px;
    margin-right: 10px;
  }
  .el-input-number {
    height: 40px;
  }
  .botText {
    font-size: 14px;
    color: gray;
    font-weight: 300;
    height: 20px;
  }
  .widthchose {
    display: inline-block;
    width: 50px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: rgb(249, 250, 251);
    border: 1px solid #ccc;
    font-size: 15px;
    font-weight: 200;
    color: rgb(123, 140, 170);
    border-radius: 4px;
    margin-right: 5px;
  }
  .widthchose1 {
    display: inline-block;
    width: 30px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: rgb(249, 250, 251);
    border: 1px solid #ccc;
    font-size: 15px;
    font-weight: 200;
    color: rgb(123, 140, 170);
    margin-right: 10px;
    border-radius: 4px;
    margin-left: 5px;
  }
}
</style>
  