<template>
  <div class="wysiwyg-field-presentation">
    <div class="text">
      <div class="textLabel">{{ $t('CFM.field.introuduction') }}</div>
      <el-input type="textarea" v-model="instructions" style="width: 50%" :placeholder="$t('CFM.detail.introduction')" @blur="emitChange" />
      <div class="botText">{{ $t('CFM.detail.instructions_for_editors') }}</div>
    </div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.tabs') }}</div>
      <el-select v-model="tabs" :placeholder="$t('CFM.detail.select_tabs')" @change="emitChange">
        <el-option :label="$t('CFM.detail.visual_text')" value="all"></el-option>
        <el-option :label="$t('CFM.detail.visual_only')" value="visual"></el-option>
        <el-option :label="$t('CFM.detail.text_only')" value="text"></el-option>
      </el-select>
    </div>
    <div class="text">
      <div class="textLabel">{{ $t('CFM.detail.toolbar') }}</div>
      <el-select v-model="toolbar" :placeholder="$t('CFM.detail.select_toolbar')" @change="emitChange">
        <el-option :label="$t('CFM.detail.full_toolbar')" value="full"></el-option>
        <el-option :label="$t('CFM.detail.basic_toolbar')" value="basic"></el-option>
      </el-select>
    </div>
    <div class="textLabel"><el-switch v-model="media_upload" @change="emitChange" />{{ $t('CFM.detail.show_media_upload') }}</div>
    <div class="textLabel">
      <el-switch v-model="delay" @change="emitChange" />{{ $t('CFM.detail.delay_initialization') }}
      <div class="botText">{{ $t('CFM.detail.delay_description') }}</div>
    </div>

    <div class="text">
      <div class="textLabel">{{ $t('CFM.field.wrapper_attributes') }}</div>
      <span class="widthchose">{{ $t('CFM.detail.width') }}</span>
      <el-input-number v-model="width" :min="1" :max="100" controls-position="right" size="large" />
      <span class="widthchose1">%</span>
      <el-input v-model="classs" style="width: 15%">
        <template #prepend>class</template>
      </el-input>
      <el-input v-model="id" style="width: 15%">
        <template #prepend>id</template>
      </el-input>
    </div>
  </div>
</template>

  
  <script lang="ts" setup>
import { ref, onMounted } from 'vue'

interface FieldData {
  instructions?: string
  tabs?: string
  toolbar?: string
  media_upload?: string
  delay?: string

  width?: string
  class?: string
  id?: string
}

const emits = defineEmits(['update'])
const instructions = ref<string>('')
const tabs = ref<string>('all')
const toolbar = ref<string>('full')
const media_upload = ref<boolean>(false)
const delay = ref<boolean>(false)
const width = ref<Number>(100)
const classs = ref<string>('')
const id = ref<string>('')

const { defaultValues } = defineProps<{
  defaultValues: FieldData
}>()

if (defaultValues) {
  if (defaultValues.instructions) {
    instructions.value = defaultValues.instructions
  }
  if (defaultValues.tabs) {
    tabs.value = defaultValues.tabs
  }
  if (defaultValues.toolbar) {
    toolbar.value = defaultValues.toolbar
  }
  if (defaultValues.media_upload) {
    media_upload.value = defaultValues.media_upload === '1'
  }
  if (defaultValues.delay) {
    delay.value = defaultValues.delay === '1'
  }
  if (defaultValues.width) {
    width.value = Number(defaultValues.width)
  }
  if (defaultValues.class) {
    classs.value = defaultValues.class
  }
}

const emitChange = () => {
  const data = {
    instructions: instructions.value,
    tabs: tabs.value,
    toolbar: toolbar.value,
    media_upload: media_upload.value ? '1' : '0',
    delay: delay.value ? '1' : '0',
    width: width.value,
    class: classs.value,
    id: id.value,
  }
  emits('update', data)
}
onMounted(() => {
  emitChange()
})
</script>
  
  <style scoped lang='scss'>
.text {
  width: 100%;
  padding: 20px 0;

  .el-input {
    height: 40px;
    margin-right: 10px;
  }
  .el-input-number {
    height: 40px;
  }
  .textLabel {
    font-size: 15px;
    font-weight: 500;
    color: black;
    line-height: 10px;
    text-align: left;
    line-height: 30px;
  }
  .botText {
    font-size: 14px;
    color: gray;
    font-weight: 300;
    height: 20px;
  }

  .widthchose {
    display: inline-block;
    width: 50px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: rgb(249, 250, 251);
    border: 1px solid #ccc;
    font-size: 15px;
    font-weight: 200;
    color: rgb(123, 140, 170);
    border-radius: 4px;
    margin-right: 5px;
  }
  .widthchose1 {
    display: inline-block;
    width: 30px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: rgb(249, 250, 251);
    border: 1px solid #ccc;
    font-size: 15px;
    font-weight: 200;
    color: rgb(123, 140, 170);
    margin-right: 10px;
    border-radius: 4px;
    margin-left: 5px;
  }
}
.textLabel {
  font-size: 15px;
  font-weight: 500;
  color: black;
  line-height: 10px;
  text-align: left;
  line-height: 30px;
}
.botText {
  font-size: 14px;
  color: gray;
  font-weight: 300;
  height: 20px;
}
</style>