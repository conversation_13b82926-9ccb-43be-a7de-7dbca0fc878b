<template>
  <div class="textVa">
    <div class="textre">
      <el-switch v-model="value1" />{{ $t('CFM.fieldSet.Conditional_logic') }} 
    </div>
    <div class="text" v-show="value1">
      <div class="textLabel">{{ $t('CFM.fieldSet.Show_if') }} </div>
      <div v-for="(group, groupIndex) in groups" :key="group.id" class="group-container">
        <div v-for="(item) in group.conditions" :key="item.id" class="condition-row">
          <el-select v-model="item.item1" placeholder="Select" style="width: 240px; margin-right: 10px">
            <el-option v-for="s1 in item.select1" :key="s1.value" :label="s1.label" :value="s1.value" />
          </el-select>
          <el-select v-model="item.item2" placeholder="Select" style="width: 240px; margin-right: 10px">
            <el-option v-for="s2 in item.select2" :key="s2.value" :label="s2.label" :value="s2.value" />
          </el-select>
          <el-input v-model="item.input" style="width: 30%; height: 35px; margin-right: 10px" />
          <el-button type="primary" @click="addInputData(groupIndex)">and</el-button>
          <el-icon class="delAdd" @click="delCondition(groupIndex, item.id)" v-show="item.canDel"><Remove /></el-icon>
        </div>
        <div class="group-actions">
          <div>
            <el-button type="danger" @click="delGroup(group.id)" v-show="group.canDel">{{ $t('CFM.fieldSet.delete_group') }} </el-button>
          </div>
          <div class="textLabel" v-if="groupIndex < groups.length - 1">or</div>
        </div>
      </div>
    </div>
    <div class="text" v-show="value1">
      <div class="textLabel">or</div>
      <el-button type="primary" plain @click="addGroup">{{ $t('CFM.fieldSet.add_group') }} </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

const value1 = ref<Boolean>(false)
const id = ref(0)
const groupId = ref(0)
const emit = defineEmits(['returnConlogic', 'updateGroups'])
const groups = ref([
  {
    id: groupId.value++,
    conditions: [
      {
        id: id.value++,
        select1: [
          { value: '(no label)', label: '(no label)' },
          { value: 'test2', label: 'test2' },
        ],
        select2: [
          { value: 'Has any value', label: 'Has any value' },
          { value: 'Has no value', label: 'Has no value' },
          { value: 'Value matches pattern', label: 'Value matches pattern' },
          { value: 'value contains', label: 'value contains' },
        ],
        item1: '',
        item2: '',
        input: '',
        canDel: false, // 不可删除的初始条件
      },
    ],
    canDel: false, // 不可删除的初始组
  },
])

const addInputData = (groupIndex: number) => {
  groups.value[groupIndex].conditions.push({
    id: id.value++,
    select1: [
      { value: '(no label)', label: '(no label)' },
      { value: 'test2', label: 'test2' },
    ],
    select2: [
      { value: 'Has any value', label: 'Has any value' },
      { value: 'Has no value', label: 'Has no value' },
      { value: 'Value matches pattern', label: 'Value matches pattern' },
      { value: 'value contains', label: 'value contains' },
    ],
    item1: '',
    item2: '',
    input: '',
    canDel: true,
  })
}

const addGroup = () => {
  groups.value.push({
    id: groupId.value++,
    conditions: [
      {
        id: id.value++,
        select1: [
          { value: '(no label)', label: '(no label)' },
          { value: 'test2', label: 'test2' },
        ],
        select2: [
          { value: 'Has any value', label: 'Has any value' },
          { value: 'Has no value', label: 'Has no value' },
          { value: 'Value matches pattern', label: 'Value matches pattern' },
          { value: 'value contains', label: 'value contains' },
        ],
        item1: '',
        item2: '',
        input: '',
        canDel: true, // 新增组的第一个条件可删除
      },
    ],
    canDel: true, // 新增组可删除
  })
}

const delCondition = (groupIndex: number, conditionId: number) => {
  groups.value[groupIndex].conditions = groups.value[groupIndex].conditions.filter(condition => condition.id !== conditionId)
}

const delGroup = (groupId: number) => {
  groups.value = groups.value.filter(group => group.id !== groupId)
}


watch(groups, (newGroups) => {
  const newConditionsArray = newGroups.flatMap(group =>
    group.conditions.map(condition => ({
      item1: condition.item1,
      item2: condition.item2,
      input: condition.input
    }))
  )
  emit('updateGroups', newConditionsArray)
}, { deep: true })
</script>

<style lang="scss" scoped>
.textVa {
  width: 100%;
  .textre {
    font-size: 15px;
    font-weight: 500;
    color: black;
    line-height: 10px;
    text-align: left;
    line-height: 30px;
  }
  .text {
    width: 100%;
    padding: 20px 0;
    border-bottom: 1px solid #ccc;
    .textLabel {
      font-size: 15px;
      font-weight: 500;
      color: black;
      line-height: 10px;
      text-align: left;
      line-height: 30px;
    }
    .el-input {
      height: 40px;
    }
    .botText {
      font-size: 14px;
      color: gray;
      font-weight: 300;
      height: 20px;
    }
  }
}
.group-actions {
  margin-top: 10px;
  .textLabel {
    margin-left: 10px;
  }
}
.delAdd {
  display: none;
}
.condition-row:hover .delAdd {
  display: inline-block;
  font-size: 15px;
  font-weight: 100;
  text-align: center;
  line-height: -22px;
  background-color: white;
  color: red;
  margin: 5px;
  cursor: pointer;
  position: relative;
  bottom: -12px;
}
</style>
