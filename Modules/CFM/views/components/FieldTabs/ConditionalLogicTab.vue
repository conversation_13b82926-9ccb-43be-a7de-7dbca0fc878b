<template>
  <div>
    <div>
      <TextCon @updateGroups="getConditionalLogic" />
    </div>
  </div>
</template>
  
  <script setup lang="ts">
import { ref } from 'vue'

import TextCon from '../ConditinoalLogc/TextCon.vue'
const emit = defineEmits(['returnConlogic'])
const getRow: any = defineProps({
  fieldData: Object,
})
const fieldData = ref<Object>({})
fieldData.value = getRow.fieldData
const getConditionalLogic = (data: any) => {
  emit('returnConlogic', data)
}
</script>
  