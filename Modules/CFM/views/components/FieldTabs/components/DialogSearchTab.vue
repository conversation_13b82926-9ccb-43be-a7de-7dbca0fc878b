<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" style="height: 520px">
    <el-tab-pane v-for="tab in filteredTabs" :label="tab.label" :name="tab.name" :key="tab.name">
      <div class="flex-container">
        <div
          v-for="(item, index) in filteredData(tab.data)"
          :key="item.name"
          :class="['flex-item', { 'flex-item-selected': item.selected }]"
          @click="selectItem(tab.name, index, item)"
        >
          <div class="icon-wrapper">
            <img :src="$asset(item.icon)" class="icon" />
          </div>
          <div class="item-name">{{ $t(item.label) }}</div>
          <div class="field-type-requires-pro" v-show="item.isPro">PRO</div>
        </div>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import { ref, defineEmits, onMounted, computed } from "vue";
import config from "../../../../config";
import { useI18n } from "vue-i18n";

// 定义类型
interface Item {
  icon: string;
  name: string;
  label: string;
  description: string;
  selected: boolean;
  img: string;
  href: string;
  video?: string;
  enabled: boolean;
  isPro?: boolean;
}

interface Tab {
  name: string;
  label: string;
  data: Item[];
}

const { t } = useI18n();

const emit = defineEmits(["select-item"]);
const props = defineProps<{
  dialogSearch: string;
}>();
const activeName = ref("one");

const tabs = ref<Tab[]>([
  {
    name: "one",
    label: "流行",
    data: [
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-text.svg",
        name: "Text",
        label: "CFM.dialog.text",
        description: "CFM.description.text",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-text.png",
        href: "https://www.advancedcustomfields.com/resources/text/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.text,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-textarea.svg",
        name: "Textarea",
        label: "CFM.dialog.textarea",
        description: "CFM.description.textarea",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-email.png",
        href: "https://www.advancedcustomfields.com/resources/email/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.textarea,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-email.svg",
        name: "Email",
        label: "CFM.dialog.email",
        description: "CFM.description.email",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-textarea.png",
        href: "https://www.advancedcustomfields.com/resources/textarea/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.email,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-url.svg",
        name: "Url",
        label: "CFM.dialog.url",
        description: "CFM.description.url",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-url.png",
        href: "https://www.advancedcustomfields.com/resources/url/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.url,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-file.svg",
        name: "File",
        label: "CFM.dialog.file",
        description: "CFM.description.file",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-file.png",
        href: "https://www.advancedcustomfields.com/resources/file/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.file,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-select.svg",
        name: "Select",
        label: "CFM.dialog.select",
        description: "CFM.description.select",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-select.png",
        href: "https://www.advancedcustomfields.com/resources/select/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.select,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-true-false.svg",
        name: "TrueFalse",
        label: "CFM.dialog.true_false",
        description: "CFM.description.true_false",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-true-false.png",
        href: "https://www.advancedcustomfields.com/resources/true-false/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.trueFalse,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-link.svg",
        name: "Link",
        label: "CFM.dialog.link",
        description: "CFM.description.link",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-link.png",
        href: "https://www.advancedcustomfields.com/resources/link/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.link,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-repeater.svg",
        name: "RepeaterTable",
        label: "CFM.dialog.repeater",
        description: "CFM.description.repeater",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-repeater.png",
        href: "https://www.advancedcustomfields.com/resources/repeater/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        video:
          "https://www.advancedcustomfields.com/resources/how-to-use-the-repeater-field/",
        enabled: config.fields.RepeaterTable,
        isPro: true,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-flexible-content.svg",
        name: "FlexibleContent",
        label: "CFM.dialog.flexible_content",
        description: "CFM.description.flexible_content",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-flexible-content.png",
        href: "https://www.advancedcustomfields.com/resources/flexible-content/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        video:
          "https://www.advancedcustomfields.com/resources/building-layouts-with-the-flexible-content-field-in-a-theme/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.flexibleContent,
        isPro: true,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-gallery.svg",
        name: "Gallery",
        label: "CFM.dialog.gallery",
        description: "CFM.description.gallery",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-gallery.png",
        href: "https://www.advancedcustomfields.com/resources/gallery/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        video:
          "https://www.advancedcustomfields.com/resources/how-to-use-the-gallery-field/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.gallery,
        isPro: true,
      },
    ],
  },
  {
    name: "two",
    label: "基本",
    data: [
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-text.svg",
        name: "Text",
        label: "CFM.dialog.text",
        description: "CFM.description.text",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-text.png",
        href: "https://www.advancedcustomfields.com/resources/text/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.text,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-textarea.svg",
        name: "Textarea",
        label: "CFM.dialog.textarea",
        description: "CFM.description.textarea",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-email.png",
        href: "https://www.advancedcustomfields.com/resources/email/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.textarea,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-email.svg",
        name: "Email",
        label: "CFM.dialog.email",
        description: "CFM.description.email",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-textarea.png",
        href: "https://www.advancedcustomfields.com/resources/textarea/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.email,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-url.svg",
        name: "Url",
        label: "CFM.dialog.url",
        description: "CFM.description.url",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-url.png",
        href: "https://www.advancedcustomfields.com/resources/url/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.url,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-number.svg",
        name: "Number",
        label: "CFM.dialog.number",
        description: "CFM.description.number",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-number.png",
        href: "https://www.advancedcustomfields.com/resources/number/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.number,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-range.svg",
        name: "Range",
        label: "CFM.dialog.range",
        description: "CFM.description.range",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-range.png",
        href: "https://www.advancedcustomfields.com/resources/range/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.range,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-password.svg",
        name: "Password",
        label: "CFM.dialog.password",
        description: "CFM.description.password",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-password.png",
        href: "https://www.advancedcustomfields.com/resources/password/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.password,
      },
    ],
  },
  {
    name: "three",
    label: "内容",
    data: [
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-image.svg",
        name: "Image",
        label: "CFM.dialog.image",
        description: "CFM.description.image",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-image.png",
        href: "https://www.advancedcustomfields.com/resources/image/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.image,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-file.svg",
        name: "File",
        label: "CFM.dialog.file",
        description: "CFM.description.file",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-file.png",
        href: "https://www.advancedcustomfields.com/resources/file/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.file,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-wysiwyg.svg",
        name: "Wysiwyg",
        label: "CFM.dialog.wysiwyg",
        description: "CFM.description.wysiwyg",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-wysiwyg.png",
        href: "https://www.advancedcustomfields.com/resources/wysiwyg-editor/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.wysiwyg,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-oembed.svg",
        name: "OEmbed",
        label: "CFM.dialog.oembed",
        description: "CFM.description.oembed",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-oembed.png",
        href: "https://www.advancedcustomfields.com/resources/oembed/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.oembed,
      },
    ],
  },
  {
    name: "four",
    label: "选择",
    data: [
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-select.svg",
        name: "Select",
        label: "CFM.dialog.select",
        description: "CFM.description.select",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-select.png",
        href: "https://www.advancedcustomfields.com/resources/select/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.select,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-checkbox.svg",
        name: "Checkbox",
        label: "CFM.dialog.checkbox",
        description: "CFM.description.checkbox",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-checkbox.png",
        href: "https://www.advancedcustomfields.com/resources/checkbox/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.checkbox,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-radio.svg",
        name: "Radio",
        label: "CFM.dialog.radio",
        description: "CFM.description.radio",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-radio-button.png",
        href: "https://www.advancedcustomfields.com/resources/radio-button/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.radio,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-button-group.svg",
        name: "ButtonGroup",
        label: "CFM.dialog.button_group",
        description: "CFM.description.button_group",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-button-group.png",
        href: "https://www.advancedcustomfields.com/resources/button-group/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.buttonGroup,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-true-false.svg",
        name: "TrueFalse",
        label: "CFM.dialog.true_false",
        description: "CFM.description.true_false",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-true-false.png",
        href: "https://www.advancedcustomfields.com/resources/true-false/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.trueFalse,
      },
    ],
  },
  {
    name: "five",
    label: "关系",
    data: [
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-relationship.svg",
        name: "Relationship",
        label: "CFM.dialog.relationship",
        description: "CFM.description.relationship",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-relationship.png",
        href: "https://www.advancedcustomfields.com/resources/relationship/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.relationship,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-message.svg",
        name: "Message",
        label: "CFM.dialog.message",
        description: "CFM.description.message",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-message.png",
        href: "https://www.advancedcustomfields.com/resources/relationship/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.message,
      },
    ],
  },
  {
    name: "six",
    label: "高级",
    data: [
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-google-map.svg",
        name: "GoogleMap",
        label: "CFM.dialog.google_map",
        description: "CFM.description.google_map",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-google-map.png",
        href: "https://www.advancedcustomfields.com/resources/google-map/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.googleMap,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-date-picker.svg",
        name: "DatePicker",
        label: "CFM.dialog.date_picker",
        description: "CFM.description.date_picker",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-date-picker.png",
        href: "https://www.advancedcustomfields.com/resources/date-picker/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.datePicker,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-date-time-picker.svg",
        name: "DataTimePicker",
        label: "CFM.dialog.date_time_picker",
        description: "CFM.description.date_time_picker",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-date-time.png",
        href: "https://www.advancedcustomfields.com/resources/date-time-picker/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.dateTimePicker,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-time-picker.svg",
        name: "TimePicker",
        label: "CFM.dialog.time_picker",
        description: "CFM.description.time_picker",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-time.png",
        href: "https://www.advancedcustomfields.com/resources/time-picker/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.timePicker,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-color-picker.svg",
        name: "ColorPicker",
        label: "CFM.dialog.color_picker",
        description: "CFM.description.color_picker",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-color-picker.png",
        href: "https://www.advancedcustomfields.com/resources/color-picker/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.colorPicker,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-icon-picker.svg",
        name: "IconPicker",
        label: "CFM.dialog.icon_picker",
        description: "CFM.description.icon_picker",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-icon-picker.png",
        href: "https://www.advancedcustomfields.com/resources/icon-picker/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.iconPicker,
      },
    ],
  },
  {
    name: "seven",
    label: "版面配置",
    data: [
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-accordion.svg",
        name: "Accordion",
        label: "CFM.dialog.accordion",
        description: "CFM.description.accordion",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-accordion.png",
        href: "https://www.advancedcustomfields.com/resources/accordion/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.accordion,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-group.svg",
        name: "Group",
        label: "CFM.dialog.group",
        description: "CFM.description.group",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-group.png",
        href: "https://www.advancedcustomfields.com/resources/group/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.group,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-repeater.svg",
        name: "RepeaterTable",
        label: "CFM.dialog.repeater",
        description: "CFM.description.repeater",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-repeater.png",
        href: "https://www.advancedcustomfields.com/resources/repeater/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        video:
          "https://www.advancedcustomfields.com/resources/how-to-use-the-repeater-field/",
        enabled: config.fields.repeaterTable,
        isPro: true,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-flexible-content.svg",
        name: "FlexibleContent",
        label: "CFM.dialog.flexible_content",
        description: "CFM.description.flexible_content",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-flexible-content.png",
        href: "https://www.advancedcustomfields.com/resources/flexible-content/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        video:
          "https://www.advancedcustomfields.com/resources/building-layouts-with-the-flexible-content-field-in-a-theme/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.flexibleContent,
        isPro: true,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-clone.svg",
        name: "Clone",
        label: "CFM.dialog.clone",
        description: "CFM.description.clone",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-clone.png",
        href: "https://www.advancedcustomfields.com/resources/clone/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        video:
          "https://www.advancedcustomfields.com/resources/how-to-use-the-clone-field/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.clone,
        isPro: true,
      },
    ],
  },
  {
    name: "eight",
    label: "PRO",
    data: [
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-repeater.svg",
        name: "RepeaterTable",
        label: "CFM.dialog.repeater",
        description: "CFM.description.repeater",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-repeater.png",
        href: "https://www.advancedcustomfields.com/resources/repeater/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        video:
          "https://www.advancedcustomfields.com/resources/how-to-use-the-repeater-field/",
        enabled: config.fields.repeaterTable,
        isPro: true,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-flexible-content.svg",
        name: "FlexibleContent",
        label: "CFM.dialog.flexible_content",
        description: "CFM.description.flexible_content",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-flexible-content.png",
        href: "https://www.advancedcustomfields.com/resources/flexible-content/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        video:
          "https://www.advancedcustomfields.com/resources/building-layouts-with-the-flexible-content-field-in-a-theme/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.flexibleContent,
        isPro: true,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-clone.svg",
        name: "Clone",
        label: "CFM.dialog.clone",
        description: "CFM.description.clone",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-clone.png",
        href: "https://www.advancedcustomfields.com/resources/clone/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        video:
          "https://www.advancedcustomfields.com/resources/how-to-use-the-clone-field/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.clone,
        isPro: true,
      },
      {
        icon: "CFM/Asset/images/field-type-icons/icon-field-gallery.svg",
        name: "Gallery",
        label: "CFM.dialog.gallery",
        description: "CFM.description.gallery",
        selected: false,
        img: "CFM/Asset/images/field-type-previews/field-preview-gallery.png",
        href: "https://www.advancedcustomfields.com/resources/gallery/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        video:
          "https://www.advancedcustomfields.com/resources/how-to-use-the-gallery-field/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
        enabled: config.fields.gallery,
        isPro: true,
      },
    ],
  },
]);


const handleClick = () => {
  tabs.value.forEach((tab) => {
    tab.data.forEach((item) => {
      item.selected = false;
    });
  });
};
const selectedItem = ref<any>(null);
const filteredData = (data: Item[]) => {
  return data.filter((item) => item.enabled);
};

const filteredTabs = computed(() => {
  if (!props.dialogSearch) {
    return tabs.value.filter((tab) => filteredData(tab.data).length > 0);
  }
  const search = props.dialogSearch.toLowerCase();
  return tabs.value
    .map((tab) => ({
      ...tab,
      data: filteredData(tab.data).filter(
        (item) =>
          item.name.toLowerCase().includes(search) ||
          t(item.label).toLowerCase().includes(search)
      ),
    }))
    .filter((tab) => tab.data.length > 0);
});

const selectItem = (tabName: string, index: number, item: any) => {
  selectedItem.value = item;
  const tab = tabs.value.find((tab) => tab.name === tabName);
  if (tab) {
    tab.data.forEach((tabItem) => {
      tabItem.selected = tabItem.name === item.name;
    });
    emit("select-item", item);
  }
};

onMounted(() => {
  emit("select-item", {
    icon: "CFM/Asset/images/field-type-icons/icon-field-text.svg",
    name: "Text",
    label: "CFM.dialog.text",
    description: "CFM.description.text",
    selected: false,
    img: "CFM/Asset/images/field-type-previews/field-preview-text.png",
    href: "https://www.advancedcustomfields.com/resources/text/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection",
  });
});
</script>

<style scoped>
.flex-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  width: 100%;
  height: 100%;
}

.flex-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f5f5f5;
  width: 120px;
  height: 120px;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  flex: 0 1 120px;
  position: relative;
}

.flex-item-selected {
  background: #007bff;
  color: white;
}

.flex-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  color: rgb(35, 146, 197);
  border-radius: 50%;
  margin-bottom: 10px;
}

.flex-item-selected .icon-wrapper {
  background: white;
}

.icon {
  font-size: 24px;
  color: #333;
}

.flex-item-selected .icon {
  color: #007bff;
}

.item-name {
  font-size: 10.5px;
  color: #666;
  width: 120px;
  text-align: center;
}

.flex-item-selected .item-name {
  width: 120px;
  text-align: center;
  color: white;
  font-size: 10.5px;
}

.field-type-requires-pro {
  position: absolute;
  top: -10px;
  right: -10px;
  height: 20px;
  color: white;
  background: linear-gradient(90.52deg, #3e8bff 0.44%, #a45cff 113.3%);
  background-size: 140% 20%;
  background-position: 100% 0;
  border-radius: 100px;
  font-size: 11px;
  padding-right: 6px;
  padding-left: 6px;
  line-height: 20px;
}
</style>
 