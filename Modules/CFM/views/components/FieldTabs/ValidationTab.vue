<template>
  <div>
    <div>
      <TextVa v-if="fieldData.type == 'Text'" @change="getPwd" :defaultValues="defaultValues" />
      <TextVa v-if="fieldData.type == 'Textarea'" @change="getPwd" :defaultValues="defaultValues" />
      <SelectVa v-if="fieldData.type == 'Select'" :defaultValues="defaultValues" @blur="getPwd" />
      <NumberVa v-if="fieldData.type == 'Number'" @change="getPwd" :defaultValues="defaultValues" />
      <RangeVa v-if="fieldData.type == 'Range'" @change="getPwd" :defaultValues="defaultValues" />
      <EmailVa v-if="fieldData.type == 'Email'" @change="getPwd" :defaultValues="defaultValues" />
      <URLVa v-if="fieldData.type == 'Url'" @change="getPwd" :defaultValues="defaultValues" />
      <ImageVa v-if="fieldData.type == 'Image'" :defaultValues="defaultValues" @blur="getPwd" />
      <FileVa v-if="fieldData.type == 'File'" :defaultValues="defaultValues" @blur='getPwd'  />
      <PassWordValidation v-if="fieldData.type == 'Password'" :defaultValues="defaultValues" @change="getPwd" />
      <RepeatValidation v-if="fieldData.type == 'RepeaterTable'" :defaultValues="defaultValues" @change="getPwd" />
      <ButtonGroupValidation v-if="fieldData.type == 'ButtonGroup'" :defaultValues="defaultValues" @blur='getPwd' />
      <CheckBoxValidation v-if="fieldData.type == 'Checkbox'" :defaultValues="defaultValues" @blur='getPwd' />
      <RadioButtonValidation v-if="fieldData.type == 'Radio'" :defaultValues="defaultValues" @blur='getPwd' />
      <TrueFalseValidation v-if="fieldData.type == 'TrueFalse'" :defaultValues="defaultValues"  @change='getPwd'/>
      <LinkValidation v-if="fieldData.type == 'Link'" :defaultValues="defaultValues" @change='getPwd' />
      <PostObjectValidation v-if="fieldData.type == 'PostObject'" :defaultValues="defaultValues" @change='getPwd'  />
      <PageLinkValidation v-if="fieldData.type == 'PageLink'" :defaultValues="defaultValues" @change='getPwd'  />
      <RelationshipValidation v-if="fieldData.type == 'Relationship'" :defaultValues="defaultValues"  @change='getPwd' />
      <TaxonomyValidation v-if="fieldData.type == 'Taxonomy'" :defaultValues="defaultValues" @change='getPwd' />
      <UserValidation v-if="fieldData.type == 'User'" :defaultValues="defaultValues"   @change='getPwd'  />
      <GoogleValidation v-if="fieldData.type == 'GoogleMap'" :defaultValues="defaultValues" @change='getPwd'  />
      <ColorPickerValidation v-if="fieldData.type == 'ColorPicker'" :defaultValues="defaultValues" @change='getPwd'  />
      <IconValidation v-if="fieldData.type == 'IconPicker'" :defaultValues="defaultValues"  @change='getPwd' />
      <DateValidation v-if="fieldData.type == 'DatePicker'" :defaultValues="defaultValues" @change='getPwd'  />
      <TimePickerValidation v-if="fieldData.type == 'TimePicker'" :defaultValues="defaultValues" @change='getPwd' />
      <OembedValidation v-if="fieldData.type == 'OEmbed'" :defaultValues="defaultValues" @change="getPwd" />
      <GalleryValidation v-if="fieldData.type == 'Gallery'" :defaultValues="defaultValues" @blur='getPwd' />
      <WysiwygValidation v-if="fieldData.type == 'Wysiwyg'" :defaultValues="defaultValues" @update='getPwd' />
      <CloneValidation v-if="fieldData.type == 'Clone'" :defaultValues="defaultValues" @update='getPwd'  />
      <FlexValidation v-if="fieldData.type == 'FlexibleContent'" :defaultValues="defaultValues" @change='getPwd' />
      <GroupValidation  v-if="fieldData.type == 'Group'" :defaultValues="defaultValues" @change='getPwd'  />
      <DateTimeValidation v-if="fieldData.type == 'DateTimePicker'" :defaultValues="defaultValues" @change='getPwd' />
      <AccorditonValidation v-if="fieldData.type == 'Accordion'" :defaultValues="defaultValues" @change='getPwd' />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

import TextVa from '../Validation/TextValidation.vue'
import SelectVa from '../Validation/SelectValidation.vue'
import NumberVa from '../Validation/NumberValidation.vue'
import RangeVa from '../Validation/RangeValidation.vue'
import EmailVa from '../Validation/EmailValidation.vue'
import URLVa from '../Validation/URLValidation.vue'
import ImageVa from '../Validation/ImageValidation.vue'
import FileVa from '../Validation/FileValidation.vue'
import PassWordValidation from '../Validation/PassWordValidation.vue'
import RepeatValidation from '../Validation/RepeatValidation.vue'
import ButtonGroupValidation from '../Validation/ButtonGroupValidation.vue'
import CheckBoxValidation from '../Validation/CheckBoxValidation.vue'
import RadioButtonValidation from '../Validation/RadioButtonValidation.vue'
import TrueFalseValidation from '../Validation/TrueFalseValidation.vue'
import LinkValidation from '../Validation/LinkValidation.vue'
import PostObjectValidation from '../Validation/PostObjectValidation.vue'
import PageLinkValidation from '../Validation/PageLinkValidation.vue'
import RelationshipValidation from '../Validation/RelationshipValidation.vue'
import TaxonomyValidation from '../Validation/TaxonomyValidation.vue'
import UserValidation from '../Validation/UserValidation.vue'
import GoogleValidation from '../Validation/GoogleValidation.vue'
import GroupValidation  from '../Validation/GroupValidation.vue'
import ColorPickerValidation from '../Validation/ColorPickerValidation.vue'
import IconValidation from '../Validation/IconValidation.vue'
import DateValidation from '../Validation/DateValidation.vue'
import TimePickerValidation from '../Validation/TimePickerValidation.vue'
import OembedValidation from '../Validation/OembedValidation.vue'
import FlexValidation from '../Validation/FlexValidation.vue'
import GalleryValidation from '../Validation/GalleryValidation.vue'
import WysiwygValidation from '../Validation/WysiwygEditorValidation.vue'
import CloneValidation from '../Validation/CloneValidation.vue'
import DateTimeValidation from '../Validation/DateTimeValidation.vue'
import AccorditonValidation from '../Validation/AccorditonValidation.vue'

const emits = defineEmits(["change"])
const valiDAtionData = ref<Object>({})
const getRow:any = defineProps({
  fieldData: Object,
  defaultValues: Object
})
const fieldData = ref<Object>({})
fieldData.value = getRow.fieldData
const defaultValues = ref<Object>({})
defaultValues.value = getRow.defaultValues

const getPwd = (val:any) => {
  valiDAtionData.value = { ...val }
  emits('change', valiDAtionData.value)
}

onMounted(() => {
  emits('change', valiDAtionData.value)
})
</script>
