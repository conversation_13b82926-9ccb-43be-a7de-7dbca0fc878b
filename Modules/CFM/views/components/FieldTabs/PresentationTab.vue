<template>
  <div>
    <div>
      <TextPre v-if="fieldData.type == 'Text'" @blur="pwdPre" :defaultValues="defaultValues" />
      <TextAreaPre v-if="fieldData.type == 'Textarea'" @blur="pwdPre" :defaultValues="defaultValues" />
      <SelectPre v-if="fieldData.type == 'Select'" :defaultValues="defaultValues" @update="pwdPre" />
      <NumberPre v-if="fieldData.type == 'Number'" @blur="pwdPre" :defaultValues="defaultValues" />
      <RangePre v-if="fieldData.type == 'Range'" @blur="pwdPre" :defaultValues="defaultValues" />
      <EmailPre v-if="fieldData.type == 'Email'" @blur="pwdPre" :defaultValues="defaultValues" />
      <URLPre v-if="fieldData.type == 'Url'" :defaultValues="defaultValues" @blur="pwdPre" />
      <ImagePre v-if="fieldData.type == 'Image'" :defaultValues="defaultValues" @blur="pwdPre" />
      <FilePre v-if="fieldData.type == 'File'" :defaultValues="defaultValues" @blur="pwdPre" />
      <PassWordPre v-if="fieldData.type == 'Password'" :defaultValues="defaultValues" @blur="pwdPre" />
      <RepeatPresenttation v-if="fieldData.type == 'RepeaterTable'" :defaultValues="defaultValues" @blur="pwdPre" />
      <ButtonGroupPresentation v-if="fieldData.type == 'ButtonGroup'" :defaultValues="defaultValues" @blur="pwdPre" />
      <CheckBoxPresentation v-if="fieldData.type == 'Checkbox'" :defaultValues="defaultValues" @blur="pwdPre" />
      <RadioButtonPresentation v-if="fieldData.type == 'RadioButton'" :defaultValues="defaultValues" @blur="pwdPre" />
      <TrueFalsePresentation v-if="fieldData.type == 'TrueFalse'" :defaultValues="defaultValues" @blur="pwdPre" />
      <LinkPresentation v-if="fieldData.type == 'Link'" :defaultValues="defaultValues" @blur="pwdPre" />
      <PostObjectPresentation v-if="fieldData.type == 'PostObject'" :defaultValues="defaultValues" />
      <PageLinkPresentation v-if="fieldData.type == 'PageLink'" :defaultValues="defaultValues" @blur="pwdPre" />
      <RadioButtonPresenttation v-if="fieldData.type == 'Radio'" :defaultValues="defaultValues" @blur="pwdPre" />
      <RelationshipPresentation v-if="fieldData.type == 'Relationship'" :defaultValues="defaultValues" />
      <TaxonomyPresentation v-if="fieldData.type == 'Taxonomy'" :defaultValues="defaultValues" @blur="pwdPre" />
      <UserPresentation v-if="fieldData.type == 'User'" :defaultValues="defaultValues" @blur="pwdPre" />
      <GooglePresentation v-if="fieldData.type == 'GoogleMap'" :defaultValues="defaultValues" @blur="pwdPre" />
      <ColorPickerPresentation v-if="fieldData.type == 'ColorPicker'" :defaultValues="defaultValues" />
      <IconPresentation v-if="fieldData.type == 'IconPicker'" :defaultValues="defaultValues" @blur="pwdPre" />
      <DatePresentation v-if="fieldData.type == 'DatePicker'" :defaultValues="defaultValues" @blur="pwdPre" />
      <TimePickerPresentation v-if="fieldData.type == 'TimePicker'" :defaultValues="defaultValues" @blur="pwdPre" />
      <OEmbedPresentation v-if="fieldData.type == 'OEmbed'" :defaultValues="defaultValues" @blur="pwdPre" />
      <AccordionPresentation v-if="fieldData.type == 'Accordion'" :defaultValues="defaultValues" />
      <GalleryPickerPresentation v-if="fieldData.type == 'Gallery'" :defaultValues="defaultValues" @change="pwdPre" />
      <WysiwygEditorPresenttation v-if="fieldData.type == 'Wysiwyg'" :defaultValues="defaultValues" @update="pwdPre" />
      <ClonePresenttation v-if="fieldData.type == 'Clone'" :defaultValues="defaultValues" @update="pwdPre" />
      <FlexPresenttation v-if="fieldData.type == 'FlexibleContent'" :defaultValues="defaultValues" @blur="pwdPre" />
      <GroupPresenttation v-if="fieldData.type == 'Group'" :defaultValues="defaultValues" @blur="pwdPre" />
      <MessagePresenttation v-if="fieldData.type == 'Message'" :defaultValues="defaultValues" @blur="pwdPre" />
      <DateTimePresenttation v-if="fieldData.type == 'DateTimePicker'" :defaultValues="defaultValues" @blur="pwdPre" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

import TextPre from '../Presenttation/TextPresenttation.vue'
import TextAreaPre from '../Presenttation/TextAreaPresenttation.vue'
import SelectPre from '../Presenttation/SelectPresenttation.vue'
import NumberPre from '../Presenttation/NumberPresenttation.vue'
import RangePre from '../Presenttation/RangePresenttation.vue'
import EmailPre from '../Presenttation/EmailPresenttation.vue'
import URLPre from '../Presenttation/URLPresenttation.vue'
import ImagePre from '../Presenttation/ImagePresenttation.vue'
import FilePre from '../Presenttation/FilePresenttaition.vue'
import PassWordPre from '../Presenttation/PassWordPresenttation.vue'
import RepeatPresenttation from '../Presenttation/RepeatPresenttation.vue'

import ButtonGroupPresentation from '../Presenttation/ButtonGroupPresenttation.vue'
import CheckBoxPresentation from '../Presenttation/CheckBoxPresenttation.vue'
import RadioButtonPresentation from '../Presenttation/RadioButtonPresenttation.vue'
import TrueFalsePresentation from '../Presenttation/TrueFalsePresenttation.vue'
import LinkPresentation from '../Presenttation/LinkPresenttation.vue'
import PostObjectPresentation from '../Presenttation/PostObjectPresenttation.vue'
import PageLinkPresentation from '../Presenttation/PageLinkPresenttation.vue'
import RelationshipPresentation from '../Presenttation/RelationshipPresenttation.vue'
import RadioButtonPresenttation from '../Presenttation/RadioButtonPresenttation.vue'
import TaxonomyPresentation from '../Presenttation/TaxonomyPresenttation.vue'
import UserPresentation from '../Presenttation/UserPresenttation.vue'
import GooglePresentation from '../Presenttation/GooglePresenttation.vue'
import GroupPresenttation from '../Presenttation/GroupPresenttation.vue'
import ColorPickerPresentation from '../Presenttation/ColorPickerPresenttation.vue'
import IconPresentation from '../Presenttation/IconPresenttation.vue'
import DatePresentation from '../Presenttation/DatePresenttation.vue'
import TimePickerPresentation from '../Presenttation/TimePickerPresenttation.vue'
import OEmbedPresentation from '../Presenttation/OEmbedPresenttation.vue'
import AccordionPresentation from '../Presenttation/AccordionPresentttation.vue'
import GalleryPickerPresentation from '../Presenttation/GalleryPickerPresenttation.vue'
import WysiwygEditorPresenttation from '../Presenttation/WysiwygEditorPresenttation.vue'
import ClonePresenttation from '../Presenttation/ClonePresenttation.vue'
import FlexPresenttation from '../Presenttation/FlexPresenttation.vue'
import MessagePresenttation from '../Presenttation/MessagePresenttation.vue'
import DateTimePresenttation from '../Presenttation/DateTimePresenttation.vue'

const getPreObject = ref<Object>({})

const emits = defineEmits(['blur', 'getPre'])
const getRow: any = defineProps({
  fieldData: Object,
  defaultValues: Object,
})
const fieldData = ref<Object>({})
const defaultValues = ref<Object>({})
fieldData.value = getRow.fieldData
defaultValues.value = getRow.defaultValues

const pwdPre = (val: any) => {
  getPreObject.value = { ...val }
  emits('getPre', getPreObject.value)
}

onMounted(() => {
  emits('getPre', getPreObject.value)
})
</script>
  1qws