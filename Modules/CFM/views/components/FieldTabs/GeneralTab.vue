<template>
  <div>
    <div>
      <div class="GeneralSelect">
        <div class="title">{{ $t('CFM.detail.field_type') }}</div>
        <el-select v-model="fieldData.type" :placeholder="$t('CFM.detail.select')" style="width: 300px" size="large" @change="returnValue">
          <el-option-group v-for="group in options" :key="group.label" :label="group.label">
            <el-option v-for="item in group.options" :key="item.value" :label="item.label" :value="item.value" />
          </el-option-group>
        </el-select>
        <el-button @click="openMoreChoice" icon="Grid">{{ $t('CFM.detail.browse_fields') }}</el-button>
      </div>
    </div>
    <div class="GeneralTitle">
      <div class="titItem">
        <div class="title">{{ $t('CFM.detail.field_label') }}</div>
        <el-input v-model="fieldData.label" style="width: 300px" @blur="returnValue" />
        <div class="botText">{{ $t('CFM.detail.will_show') }}</div>
      </div>
      <div class="titItem">
        <div class="title">{{ $t('CFM.detail.field_name') }}</div>
        <el-input v-model="fieldData.name" style="width: 300px" @blur="returnValue" />
        <div class="botText">{{ $t('CFM.detail.singele') }}</div>
      </div>
    </div>
    <div>
      <TextField v-if="fieldData.type == 'Text'" @blur="getTextDefault" :defaultValues="defaultValues" />
      <TextareaField v-if="fieldData.type == 'Textarea'" @blur="getTextDefault" :defaultValues="defaultValues" />
      <SelectField v-if="fieldData.type == 'Select'" @blur="getTextDefault" :defaultValues="defaultValues" />
      <NumberField v-if="fieldData.type == 'Number'" @blur="getTextDefault" :defaultValues="defaultValues" />
      <RangeField v-if="fieldData.type == 'Range'" @change="getTextDefault" :defaultValues="defaultValues" />
      <EmailField v-if="fieldData.type == 'Email'" @blur="getTextDefault" :defaultValues="defaultValues" />
      <URLField v-if="fieldData.type == 'Url'" @blur="getTextDefault" :defaultValues="defaultValues" />
      <ImageField v-if="fieldData.type == 'Image'" :defaultValues="defaultValues" @change="getTextDefault" />
      <FileField v-if="fieldData.type == 'File'" :defaultValues="defaultValues" @blur="getTextDefault" />
      <PasswordField v-if="fieldData.type == 'Password'" :defaultValues="defaultValues" />
      <Repeat v-if="fieldData.type == 'RepeaterTable'" :defaultValues="defaultValues" :parent="fieldData" @returnRepeat="getTextDefault" />
      <Group  v-if="fieldData.type == 'Group'" :defaultValues="defaultValues" :parent="fieldData" @returnRepeat="getTextDefault"  />
      <ButtonGroupField v-if="fieldData.type == 'ButtonGroup'" :defaultValues="defaultValues" @blur="getTextDefault" />
      <CheckboxField v-if="fieldData.type == 'Checkbox'" :defaultValues="defaultValues" @blur="getTextDefault" />
      <RadioButtonField v-if="fieldData.type == 'Radio'" :defaultValues="defaultValues" @blur="getTextDefault" />
      <TrueFalseField v-if="fieldData.type == 'TrueFalse'" :defaultValues="defaultValues" @blur="getTextDefault" />
      <LinkField v-if="fieldData.type == 'Link'" :defaultValues="defaultValues" @change="getTextDefault" />
      <PostObjectField v-if="fieldData.type == 'PostObject'" :defaultValues="defaultValues" />
      <PageLinkField v-if="fieldData.type == 'PageLink'" :defaultValues="defaultValues" @change="getTextDefault" />
      <RelationshipField v-if="fieldData.type == 'Relationship'" :defaultValues="defaultValues" @change="getTextDefault" />
      <TaxonomyField v-if="fieldData.type == 'Taxonomy'" :defaultValues="defaultValues" @blur="getTextDefault" />
      <UserField v-if="fieldData.type == 'User'" :defaultValues="defaultValues" @blur="getTextDefault" />
      <GoogleMapField v-if="fieldData.type == 'GoogleMap'" :defaultValues="defaultValues" @change="getTextDefault" />
      <ColorPickerField v-if="fieldData.type == 'ColorPicker'" :defaultValues="defaultValues" @change="getTextDefault" />
      <IconPickerField v-if="fieldData.type == 'IconPicker'" :defaultValues="defaultValues" @change="getTextDefault" />
      <DatePickerField v-if="fieldData.type == 'DatePicker'" :defaultValues="defaultValues" @change="getTextDefault" />
      <TimePickerField v-if="fieldData.type == 'TimePicker'" :defaultValues="defaultValues" @blur="getTextDefault" />
      <OEmbedField v-if="fieldData.type == 'OEmbed'" :defaultValues="defaultValues" @blur="getTextDefault" />
      <AccordionField v-if="fieldData.type == 'Accordion'" :defaultValues="defaultValues" @change="getTextDefault" />
      <GalleryField v-if="fieldData.type == 'Gallery'" :defaultValues="defaultValues" @change="getTextDefault" />
      <WysiwgField v-if="fieldData.type == 'Wysiwyg'" :defaultValues="defaultValues" @blur="getTextDefault" />
      <CloneField v-if="fieldData.type == 'Clone'" :defaultValues="defaultValues" @update="getTextDefault" />
      <FlexibleContentField v-if="fieldData.type == 'FlexibleContent'" :defaultValues="defaultValues" :parent="fieldData" @blur="getTextDefault" />
      <TabField v-if="fieldData.type == 'Tab'" :defaultValues="defaultValues" @blur="getTextDefault" />
      <MessageField v-if="fieldData.type == 'Message'" :defaultValues="defaultValues" @blur="getTextDefault" />
      <DateTimePickerField v-if="fieldData.type == 'DateTimePicker'" :defaultValues="defaultValues" @blur="getTextDefault" />
    </div>
    <el-dialog v-model="dialogSelectVisible" width="1200" style="z-index: 5;" :modal="false" :append-to-body="true">
      <div class="dalogSearch">
        <div class="dLeft">
          <div class="dialogTop">
            <div class="topTit">{{ $t('CFM.detail.select_field_type') }}</div>
            <div class="topInput">
              <el-input v-model="dialogSearch" style="width: 240px" :placeholder="$t('CFM.detail.search_field')" :prefix-icon="Search" />
            </div>
          </div>
          <DialogSearchTab @select-item="handleSelectItem" :dialogSearch="dialogSearch" />
          <div class="dialogbtn">
            <div class="btnInput">
              <el-input v-model="fieldData.name" style="width: 240px" :prefix-icon="Search" />
            </div>
            <div class="btnButon">
              <el-button type="primary" plain @click="dialogSelectVisible = false">{{ $t('CFM.detail.close') }}</el-button>
              <el-button type="primary" @click="getChoseType">{{ $t('CFM.detail.select_field') }}</el-button>
            </div>
          </div>
        </div>
        <div class="dRight" v-if="selectedItem.label !== ''">
          <div class="tit">{{ $t(selectedItem.label) }}</div>
          <div class="content">{{ $t(selectedItem.description) }}</div>
          <div class="img">
            <img :src="$asset(selectedItem.img)" />
          </div>
          <!-- <div class="bottom">
            <el-link :icon="VideoPlay" :href="selectedItem.video" type="primary" target="_blank" v-show="selectedItem.isPro" style="margin-right: 10px">Video</el-link>
            <el-link :icon="Document" :href="selectedItem.href" type="primary" target="_blank">Documentation</el-link>
          </div> -->
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, defineEmits, defineProps } from 'vue';
import { Search, Document, VideoPlay } from '@element-plus/icons-vue';

import config from '../../../config';
import TextField from '../FieldSettings/TextField.vue';
import DialogSearchTab from './components/DialogSearchTab.vue';
import TextareaField from '../FieldSettings/TextareaField.vue';
import SelectField from '../FieldSettings/SelectField.vue';
import NumberField from '../FieldSettings/NumberField.vue';
import RangeField from '../FieldSettings/RangeField.vue';
import EmailField from '../FieldSettings/EmailField.vue';
import URLField from '../FieldSettings/URLField.vue';
import ImageField from '../FieldSettings/ImageField.vue';
import FileField from '../FieldSettings/FileField.vue';
import PasswordField from '../FieldSettings/PasswordField.vue';
import Repeat from '../../ui/Repeat.vue';
import ButtonGroupField from '../FieldSettings/ButtonGroupField.vue';
import CheckboxField from '../FieldSettings/CheckboxField.vue';
import RadioButtonField from '../FieldSettings/RadioButtonField.vue';
import TrueFalseField from '../FieldSettings/TrueFalseField.vue';
import LinkField from '../FieldSettings/LinkField.vue';
import PostObjectField from '../FieldSettings/PostObjectField.vue';
import PageLinkField from '../FieldSettings/PageLinkField.vue';
import RelationshipField from '../FieldSettings/RelationshipField.vue';
import TaxonomyField from '../FieldSettings/TaxonomyField.vue';
import UserField from '../FieldSettings/UserField.vue';
import GoogleMapField from '../FieldSettings/GoogleMapField.vue';
import ColorPickerField from '../FieldSettings/ColorPickerField.vue';
import IconPickerField from '../FieldSettings/IconPickerField.vue';
import DatePickerField from '../FieldSettings/DatePickerField.vue';
import TimePickerField from '../FieldSettings/TimePickerField.vue';
import OEmbedField from '../FieldSettings/OEmbedField.vue';
import AccordionField from '../FieldSettings/AccordionField.vue';
import GalleryField from '../FieldSettings/GalleryField.vue';
import WysiwgField from '../FieldSettings/WysiwygEditorField.vue';
import CloneField from '../FieldSettings/CloneField.vue';
import FlexibleContentField from '../../ui/FlexibleContentField.vue';
import TabField from '../FieldSettings/TabField.vue';
import MessageField from '../FieldSettings/MessageField.vue';
import DateTimePickerField from '../FieldSettings/DateTimePickerField.vue';
import Group from '../../ui/Group.vue';

// 定义接口
interface FieldData {
  type: string;
  label: string;
  name: string;
  key:string;
}

interface DefaultValues {
  label?: string;
  name?: string;
}

interface SelectedItem {
  icon: string;
  name: string;
  label: string;
  description: string;
  selected: boolean;
  img: string;
  href: string;
  isPro?: boolean;
}
interface ConfigFields {
  [key: string]: boolean;
}

const emits = defineEmits(['blur']);
const getRow = defineProps<{
  fieldData: FieldData;
  defaultValues: DefaultValues;
}>();
const fieldData = ref<FieldData>({
  type: getRow.fieldData.type,
  label: getRow.fieldData.label,
  name: getRow.fieldData.name,
  key: getRow.fieldData.key,
});

const defaultValues = ref<DefaultValues>({
  label: getRow.defaultValues.label,
  name: getRow.defaultValues.name,
  ...getRow.defaultValues,
});

const dialogSelectVisible = ref(false);
const dialogSearch = ref('');

// 使用 config 控制 options
const configFields: ConfigFields = config.fields;
const options = [
  {
    label: 'Basic',
    options: [
      { value: 'Text', label: 'Text' },
      { value: 'Textarea', label: 'Text Area' },
      { value: 'Number', label: 'Number' },
      { value: 'Range', label: 'Range' },
      { value: 'Email', label: 'Email' },
      { value: 'Url', label: 'URL' },
      { value: 'Password', label: 'Password' },
    ].filter(option => configFields[option.value.toLowerCase()]),
  },
  {
    label: 'Content',
    options: [
      { value: 'Image', label: 'Image' },
      { value: 'File', label: 'File' },
      { value: 'Wysiwyg', label: 'WYSIWYG Editor' },
      { value: 'OEmbed', label: 'oEmbed' },
      { value: 'Gallery', label: 'Gallery' },
    ].filter(option => configFields[option.value.toLowerCase()]),
  },
  {
    label: 'Choice',
    options: [
      { value: 'Select', label: 'Select' },
      { value: 'Checkbox', label: 'Checkbox' },
      { value: 'Radio', label: 'Radio Button' },
      { value: 'ButtonGroup', label: 'Button Group' },
      { value: 'TrueFalse', label: 'True / False' },
    ].filter(option => configFields[option.value.toLowerCase()]),
  },
  {
    label: 'Relational',
    options: [
      { value: 'Link', label: 'Link' },
      { value: 'PostObject', label: 'Post Object' },
      { value: 'PageLink', label: 'Page Link' },
      { value: 'Relationship', label: 'Relationship' },
      { value: 'Taxonomy', label: 'Taxonomy' },
      { value: 'User', label: 'User' },
    ].filter(option => configFields[option.value.toLowerCase()]),
  },
  {
    label: 'Advanced',
    options: [
      { value: 'GoogleMap', label: 'Google Map' },
      { value: 'ColorPicker', label: 'Color Picker' },
      { value: 'IconPicker', label: 'Icon Picker' },
      { value: 'DatePicker', label: 'Date Picker' },
      { value: 'DateTimePicker', label: 'Date Time Picker' },
      { value: 'TimePicker', label: 'Time Picker' },
    ].filter(option => configFields[option.value.toLowerCase()]),
  },
  {
    label: 'Layout',
    options: [
      { value: 'Message', label: 'Message' },
      { value: 'Accordion', label: 'Accordion' },
      { value: 'Tab', label: 'Tab' },
      { value: 'Group', label: 'Group' },
      { value: 'RepeaterTable', label: 'Repeater' },
      { value: 'FlexibleContent', label: 'Flexible Content' },
      { value: 'Clone', label: 'Clone' },
    ].filter(option => configFields[option.value.toLowerCase()]),
  },
];
const openMoreChoice = () => {
  dialogSelectVisible.value = true;
};

const selectedItem = ref<SelectedItem>({
  icon: 'CFM/Asset/images/field-type-icons/icon-field-text.svg',
  name: 'Text',
  label: 'CFM.dialog.text',
  description: 'CFM.description.text',
  selected: false,
  img: 'CFM/Asset/images/field-type-previews/field-preview-text.png',
  href: 'https://www.advancedcustomfields.com/resources/text/?utm_source=ACF+PRO&utm_medium=insideplugin&utm_campaign=docs&utm_content=field-type-selection',
});

const handleSelectItem = (item: SelectedItem) => {
  selectedItem.value = item;
};

const getChoseType = () => {
  fieldData.value.type = selectedItem.value.name;
  dialogSelectVisible.value = false;
  returnValue();
};

const getVal = ref<Record<string, any>>({});

const getTextDefault = (val?: any) => {
  getVal.value = { ...val };
  returnValue();
};

const returnValue = () => {
  const generalValue = {
    label: fieldData.value.label,
    name: fieldData.value.name,
    type: fieldData.value.type,
    ...getVal.value,
  };
  emits('blur', generalValue);
};

onMounted(() => {
  returnValue();
});
</script>

<style lang="scss" scoped>
.GeneralSelect {
  width: 100%;
  line-height: 100px;
  border-bottom: 1px solid #ccc;
  .el-button {
    background-color: rgb(7, 131, 190);
    color: white;
    margin-left: 20px;
    height: 40px;
  }
}
.title {
  font-size: 15px;
  font-weight: 500;
  color: black;
  text-align: left;
  line-height: 20px;
}
.GeneralTitle {
  padding: 20px 0;
  width: 100%;
  border-bottom: 1px solid #ccc;
  .titItem {
    width: 100%;
    margin: 10px 0;
    .el-input {
      height: 40px;
    }
    .botText {
      font-size: 14px;
      color: gray;
      font-weight: 300;
      height: 20px;
    }
  }
}
.el-dialog {
  z-index: 5;
}
.dalogSearch {
  display: flex;
}
.dLeft {
  width: 70%;
  padding-right: 10px;
}
.dRight {
  display: flex;
  flex-direction: column;
  width: 30%;
  background-color: #f9fafb;
  background-image: url('../../../Asset/images/field-preview-grid.png');
  background-size: 740px;
  background-repeat: no-repeat;
  background-position: center bottom;
  border-left: 1px solid #eaecf0;
  box-sizing: border-box;
  padding: 32px;
  .tit {
    font-size: 21px;
    margin: 0 0 16px;
  }
  .content {
    margin: 0;
    padding: 0;
    color: #667085;
    font-size: 13px;
    line-height: 1.5;
  }
  .img {
    display: inline-flex;
    justify-content: center;
    width: 100%;
    margin-top: 24px;
    padding: 32px 10px;
    background-color: rgba(255, 255, 255, 0.64);
    border-radius: 8px;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.04), 0 8px 24px rgba(0, 0, 0, 0.04);
  }
  .bottom {
    position: absolute;
    bottom: 20px;
  }
}
.dialogTop {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.dialogbtn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}
</style>
