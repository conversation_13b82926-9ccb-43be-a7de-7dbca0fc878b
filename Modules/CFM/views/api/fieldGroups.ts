import axios from 'axios';

const API_BASE_URL = '/api/field-groups';

export default {
    async fetchFieldGroups() {
        try {
            const response = await axios.get(API_BASE_URL);
            return response.data;
        } catch (error) {
            console.error('Error fetching field groups:', error);
            throw error;
        }
    },

    async fetchFieldGroup(id: number) {
        try {
            const response = await axios.get(`${API_BASE_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching field group with id ${id}:`, error);
            throw error;
        }
    },

    async createFieldGroup(fieldGroup: any) {
        try {
            const response = await axios.post(API_BASE_URL, fieldGroup);
            return response.data;
        } catch (error) {
            console.error('Error creating field group:', error);
            throw error;
        }
    },

    async updateFieldGroup(id: number, fieldGroup: any) {
        try {
            const response = await axios.put(`${API_BASE_URL}/${id}`, fieldGroup);
            return response.data;
        } catch (error) {
            console.error(`<PERSON>rror updating field group with id ${id}:`, error);
            throw error;
        }
    },

    async deleteFieldGroup(id: number) {
        try {
            const response = await axios.delete(`${API_BASE_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Error deleting field group with id ${id}:`, error);
            throw error;
        }
    }
};
