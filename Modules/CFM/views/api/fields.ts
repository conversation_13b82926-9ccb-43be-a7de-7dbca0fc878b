import axios from 'axios';

const API_BASE_URL = '/api/fields';

export default {
    async fetchFields() {
        try {
            const response = await axios.get(API_BASE_URL);
            return response.data;
        } catch (error) {
            console.error('Error fetching fields:', error);
            throw error;
        }
    },

    async fetchField(id: number) {
        try {
            const response = await axios.get(`${API_BASE_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching field with id ${id}:`, error);
            throw error;
        }
    },

    async createField(field: any) {
        try {
            const response = await axios.post(API_BASE_URL, field);
            return response.data;
        } catch (error) {
            console.error('Error creating field:', error);
            throw error;
        }
    },

    async updateField(id: number, field: any) {
        try {
            const response = await axios.put(`${API_BASE_URL}/${id}`, field);
            return response.data;
        } catch (error) {
            console.error(`<PERSON>rror updating field with id ${id}:`, error);
            throw error;
        }
    },

    async deleteField(id: number) {
        try {
            const response = await axios.delete(`${API_BASE_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Error deleting field with id ${id}:`, error);
            throw error;
        }
    }
};
