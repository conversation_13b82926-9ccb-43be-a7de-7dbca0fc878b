<?php

namespace Modules\CFM\Repositories\Eloquent;

use Mo<PERSON>les\CFM\Domain\Entities\FieldGroup as FieldGroupEntity;
use Modules\CFM\Models\CfmFieldGroups as FieldGroup;
use Modules\CFM\Repositories\Interfaces\FieldGroupRepositoryInterface;

class EloquentFieldGroupRepository implements FieldGroupRepositoryInterface
{
    public function findByName(string $name): ?FieldGroup
    {
        return FieldGroup::where('name', '=', $name)->first();
    }

    public function all(): array
    {
        return FieldGroup::with('fields')->get()->map(function ($fieldGroup) {
            $fieldGroupArray = $this->convertJsonFields($fieldGroup->toArray());

            return new FieldGroupEntity($fieldGroupArray);
        })->toArray();
    }
    public function search(string $title = null): array
    {
        $query = FieldGroup::with('fields');

        if ($title) {
            $query->where(function ($subQuery) use ($title) {
                $subQuery->where('title', 'like', '%'.$title.'%')
                    ->orWhere('description', 'like', '%'.$title.'%');
            });
        }

        return $query->get()->map(function ($fieldGroup) {
            $fieldGroupArray = $this->convertJsonFields($fieldGroup->toArray());
            return new FieldGroupEntity($fieldGroupArray);
        })->toArray();
    }

    public function create(array $data): array
    {
        $fieldGroup = FieldGroup::create($data);
        return $this->convertJsonFields($fieldGroup->toArray());
    }

    public function findById(int $id): array
    {
        $fieldGroup = FieldGroup::with('fields')->find($id);

        if ($fieldGroup) {
            return $this->convertJsonFields($fieldGroup->toArray());
        }
        return [];
    }

    public function update(int $id, array $data): array
    {
        $fieldGroup = FieldGroup::findOrFail($id);
        $fieldGroup->update($data);
        return $this->convertJsonFields($fieldGroup->toArray());
    }

    public function delete(int $id): void
    {
        FieldGroup::destroy($id);
    }

    private function convertJsonFields(array $fieldGroupArray): array
    {
        $fieldGroupArray['location_rules'] = json_decode($fieldGroupArray['location_rules'], true);
        $fieldGroupArray['presentation_settings'] = json_decode($fieldGroupArray['presentation_settings'], true);

        // 检查 fields 是否存在且为数组
        if (isset($fieldGroupArray['fields']) && is_array($fieldGroupArray['fields'])) {
            // 组织一个映射，按 key 存储字段
            $fieldsMap = [];
            foreach ($fieldGroupArray['fields'] as &$field) {
                $field['settings'] = json_decode($field['settings'], true);
                $field['sub_fields'] = []; // 初始化 sub_fields 为一个空数组
                if (empty($field['parent_key'])) {
                    $fieldsMap[$field['key']] = $field;
                } else {
                    // 插入子字段到父字段的 sub_fields 中
                    $fieldsMap[$field['parent_key']]['sub_fields'][] = $field;
                }
            }

            // 更新 fields 数组为顶级字段及其子字段
            $fieldGroupArray['fields'] = array_values($fieldsMap);
        }

        return $fieldGroupArray;
    }
}
