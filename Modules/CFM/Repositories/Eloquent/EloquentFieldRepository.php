<?php

namespace Modules\CFM\Repositories\Eloquent;

use Modules\CFM\Domain\Entities\Field;
use Modules\CFM\Models\CfmFields as FieldModel;

class EloquentFieldRepository
{
    public function all(): array
    {
        return FieldModel::all()->toArray();
    }

    public function create(array $data): Field
    {
        $field = FieldModel::create($data);
        return new Field($field->toArray());
    }

    public function findById(int $id): ?Field
    {
        $field = FieldModel::find($id);
        return $field ? new Field($field->toArray()) : null;
    }

    public function update(int $id, array $data): Field
    {
        $field = FieldModel::findOrFail($id);
        $field->update($data);
        return new Field($field->toArray());
    }

    public function delete(int $id): void
    {
        FieldModel::destroy($id);
    }

    public function deleteByGroupId(int $groupId): void
    {
        FieldModel::where('group_id', $groupId)->delete();
    }

    public function findByNameAndGroupId(string $key, int $groupId): ?Field
    {
        $field = FieldModel::where('group_id', $groupId)->where('key', $key)->first();
        return $field ? new Field($field->toArray()) : null;
    }
}
