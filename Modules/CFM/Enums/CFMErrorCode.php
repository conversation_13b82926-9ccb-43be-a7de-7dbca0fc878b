<?php

namespace Modules\CFM\Enums;

use Bingo\Enums\Traits\EnumEnhance;

enum CFMErrorCode: int
{
    use EnumEnhance;

    case FIELD_GROUP_NOT_FOUND = 100000;
    case SAVE_CUSTOM_FIELD_DATA_FAILED = 100001;
    case LOCATION_IS_NOT_VALID = 100002;
    case FIELD_GROUP_DUPLICATE_NAME = 100003;
    case CONTENT_ID_IS_REQUIRED = 100004;
    case FIELD_GROUP_KEY_REQUIRED = 100005;
    case UNSUPPORTED_FIELD_TYPE = 100006;
    case FIELD_GROUP_KEY_CANNOT_CHANGE = 100007;
}
