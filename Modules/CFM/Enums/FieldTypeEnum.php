<?php

namespace Modules\CFM\Enums;

use Bingo\Enums\Traits\EnumEnhance;

enum FieldTypeEnum: string
{
    use EnumEnhance;

    case ACCORDION = 'Accordion';
    case BUTTON_GROUP = 'ButtonGroup';
    case CHECKBOX = 'Checkbox';
    case CLONE = 'Clone';
    case COLOR_PICKER = 'ColorPicker';
    case DATE_PICKER = 'DatePicker';
    case DATE_TIME_PICKER = 'DateTimePicker';
    case EMAIL = 'Email';
    case FILE = 'File';
    case FLEXIBLE_CONTENT = 'FlexibleContent';
    case GALLERY = 'Gallery';
    case GOOGLE_MAP = 'GoogleMap';
    case GROUP = 'Group';
    case ICON_PICKER = 'IconPicker';
    case IMAGE = 'Image';
    case LINK = 'Link';
    case NUMBER = 'Number';

    case MESSAGE = 'Message';
    case OEMBED = 'OEmbed';
    case PASSWORD = 'Password';
    case RADIO = 'Radio';
    case RANGE = 'Range';
    case RELATIONSHIP = 'Relationship';
    case REPEATER = 'Repeater';
    case REPEATER_TABLE = 'RepeaterTable';
    case SELECT = 'Select';
    case TEXT = 'Text';
    case TEXTAREA = 'Textarea';
    case TIME_PICKER = 'TimePicker';
    case TRUE_FALSE = 'TrueFalse';
    case URL = 'Url';
    case WYSIWYG = 'Wysiwyg';
}
