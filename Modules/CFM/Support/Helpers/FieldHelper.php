<?php

namespace Modules\CFM\Support\Helpers;

use Modules\CFM\Models\CfmFieldGroups;

// 可以根据需要添加更多字段类型
// use Modules\CFM\Domain\Fields\SelectField;

class FieldHelper
{
    /**
     * 解码 HTML 实体并处理换行符
     *
     * @param string $value
     * @return string
     */
    public static function decodeHtmlEntities(string $value): string
    {
        // 解码 HTML 实体
        $value = htmlspecialchars_decode($value, ENT_QUOTES);

        // 处理换行符
        return str_replace("\r\n", "\n", $value);
    }

    /**
     * 获取特定位置的自定义字段数据
     *
     * @param string $location
     * @return array
     */
    public static function getCustomFieldData($location)
    {
        $fieldGroups = CfmFieldGroups::whereJsonContains('location_rules', [['param' => 'post_type', 'value' => $location]])->get();
        $fields = [];

        foreach ($fieldGroups as $fieldGroup) {
            foreach ($fieldGroup->fields as $field) {
                // 动态设置组件名称
                $component = match ($field->type) {
                    'text' => 'TextField',
                    // 'select' => 'SelectField', // 示例，具体可根据需要添加更多
                    default => 'TextField', // 默认组件
                };

                $fields[] = [
                    'name' => $field->name,
                    'label' => $field->label,
                    'type' => $field->type,
                    'default_value' => $field->default_value,
                    'component' => $component,
                ];
            }
        }

        return $fields;
    }
}
