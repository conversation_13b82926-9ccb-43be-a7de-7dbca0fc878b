<?php

namespace Modules\CFM\Support\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Modules\CFM\Models\CfmMeta;
use Modules\CFM\Domain\Fields\FieldFactory;

class CustomFieldHelper
{
    /**
     * 获取自定义字段的值
     *
     * @param int|string $contentId 内容ID
     * @param string $fieldName 字段名称
     * @return string 返回字段值
     */
    public function getField(int|string $contentId, string $fieldName): string
    {
        $cacheKey = "cfm_field_{$contentId}_{$fieldName}";

        // 尝试从缓存中获取值
        return Cache::remember($cacheKey, 60 * 60, function () use ($contentId, $fieldName) {
            $fieldMeta = CfmMeta::getFieldMeta($fieldName, $contentId);

            if (! $fieldMeta) {
                return "";
            }

            $fieldDetails = CfmMeta::getFieldDetails($fieldMeta['meta_value'], $contentId, $fieldName);

            if (! $fieldDetails) {
                return "";
            }

            $fieldValue = CfmMeta::getFieldValue($fieldName, $contentId);

            if (! $fieldValue) {
                return "";
            }

            return $this->formatFieldValue($fieldValue, $fieldDetails);
        });
    }

    /**
     * 格式化字段值
     *
     * @param mixed $value 字段值
     * @param array $field 字段信息
     * @return mixed 返回格式化后的字段值
     */
    protected function formatFieldValue(mixed $value, array $field): mixed
    {
        $fieldInstance = FieldFactory::create($field);

        return $fieldInstance->formatValue($value, null, $field);
    }

    /**
     * 输出字段值
     *
     * @param string $selector 字段选择器
     * @param int|string|null $contentId 内容ID
     * @param bool $formatValue 是否格式化字段值
     * @return void
     */
    public function theField(string $selector, int|string|null $contentId = null, bool $formatValue = true): void
    {
        $field = $this->getFieldObject($selector, $contentId, $formatValue, true, $formatValue);
        $value = $field ? $field['value'] : $this->getFieldBySelector($selector, $contentId, $formatValue, $formatValue);

        if (is_array($value)) {
            $value = implode(', ', $value);
        }

        if (! is_scalar($value)) {
            return;
        }

        if (! $formatValue) {
            $value = htmlspecialchars($value);
        }

        $unescapedValue = $this->getFieldBySelector($selector, $contentId, $formatValue, false);
        if (is_array($unescapedValue)) {
            $unescapedValue = implode(', ', $unescapedValue);
        }

        $fieldType = is_array($field) && isset($field['type']) ? $field['type'] : 'text';
        if ($this->applyFilters('acf/the_field/allow_unsafe_html', false, $selector, $contentId, $fieldType, $field)) {
            $value = $unescapedValue;
        } elseif ((string) $value !== (string) $unescapedValue) {
            $this->doAction('acf/removed_unsafe_html', __FUNCTION__, $selector, $field, $contentId);
        }

        echo $value;
    }

    /**
     * 获取字段对象
     *
     * @param string $selector 字段选择器
     * @param int|string|null $contentId 内容ID
     * @param bool $formatValue 是否格式化字段值
     * @param bool $loadValue 是否加载字段值
     * @param bool $escapeHtml 是否转义HTML
     * @return array|false 返回字段对象或false
     */
    public function getFieldObject(int|string|null $contentId = null, $modelname): array|false
    {
        if (is_null($contentId) || empty($modelname)) {
            return [];
        }
        $allMeta = CfmMeta::getAllMetaByContentId($contentId);

        if (empty($allMeta)) {
            return [];
        }

        $result = [];
        $fieldKeys = [];

        // 先处理所有的meta数据，收集带_前缀的field keys
        foreach ($allMeta as $meta) {
            $metaKey = $meta['meta_key'];
            if (str_starts_with($metaKey, '_')) {
                // 带_的meta_key，去掉_并收集
                $fieldKeys[] = ltrim($meta['meta_value'], '_');
            } else {
                // 不带_的meta_key，直接输出值
                $result[$metaKey] = $meta['meta_value'];
            }
        }


        // 获取符合条件的字段组数据及其关联的字段数据
        $fieldGroups = DB::table('cfm_field_groups')
            ->join('cfm_fields', 'cfm_field_groups.id', '=', 'cfm_fields.group_id')
            ->where('cfm_field_groups.location_rules', 'LIKE', '%'.$modelname.'%')
            ->where('cfm_field_groups.deleted_at', 0)
            ->select('cfm_field_groups.*', 'cfm_fields.*')
            ->get()
            ->toArray();

        $resultinfo = [];
        // 一次性查询所有cfm_fields表中的字段详细信息
        foreach ($fieldGroups as $group) {
            if (isset($result[$group->name])) {
                $resultinfo[$group->name] = $result[$group->name];
            } else {
                $resultinfo[$group->name] = "";
            }
        }

        return $resultinfo;
    }

    /**
     * 获取可能存在的字段对象
     *
     * @param mixed $selector 字段选择器
     * @param int|string|null $contentId 内容ID
     * @param bool $strict 是否严格匹配
     * @return array|false 返回字段对象或false
     */
    protected function MaybeGetField(mixed $selector, int|string|null $contentId = null, bool $strict = true): array|false
    {
        if ($this->isFieldKey($selector)) {
            return $this->getField($selector);
        }

        $contentId = $this->getValidContentId($contentId);
        $field = $this->getMetaField($selector, $contentId);
        if ($field) {
            return $field;
        }

        if (! $strict) {
            return $this->getField($selector);
        }

        return false;
    }

    /**
     * 获取可能存在的子字段对象
     *
     * @param array $selectors 字段选择器数组
     * @param int|string|null $contentId 内容ID
     * @param bool $strict 是否严格匹配
     * @return array|false 返回字段对象或false
     */
    protected function MaybeGetSubField(array $selectors, int|string|null $contentId = null, bool $strict = true): array|false
    {
        if (count($selectors) < 3) {
            return false;
        }

        $offset = $this->getSetting('row_index_offset');
        $selector = array_shift($selectors);
        $field = $this->MaybeGetField($selector, $contentId, $strict);

        if (! $field) {
            return false;
        }

        foreach (array_chunk($selectors, 2) as [$subI, $subS]) {
            $field = $this->getSubField($subS, $field);
            if (! $field) {
                return false;
            }
            $field['name'] = $field['name'].'_'.($subI - $offset).'_'.$field['name'];
        }

        return $field;
    }

    /**
     * 获取所有字段
     *
     * @param int|string|null $contentId 内容ID
     * @param bool $formatValue 是否格式化字段值
     * @param bool $escapeHtml 是否转义HTML
     * @return array|false 返回字段数组或false
     */
    public function getFields(int|string|null $contentId = null, bool $formatValue = true, bool $escapeHtml = false): array|false
    {
        if (! $formatValue && $escapeHtml) {
            return false;
        }

        $fields = $this->getFieldObjects($contentId, $formatValue, true, $escapeHtml);
        if (! $fields) {
            return false;
        }

        $meta = [];
        foreach ($fields as $k => $field) {
            $meta[$k] = $field['value'];
        }

        return $meta;
    }

    /**
     * 获取所有字段对象
     *
     * @param int|string|null $contentId 内容ID
     * @param bool $formatValue 是否格式化字段值
     * @param bool $loadValue 是否加载字段值
     * @param bool $escapeHtml 是否转义HTML
     * @return array|false 返回字段对象数组或false
     */
    public function getFieldObjects(int|string|null $contentId = null, bool $formatValue = true, bool $loadValue = true, bool $escapeHtml = false): array|false
    {
        $contentId = $this->getValidContentId($contentId);
        $meta = $this->getMeta($contentId);
        if (empty($meta)) {
            return false;
        }

        if (! $formatValue && $escapeHtml) {
            return false;
        }

        $fields = [];
        foreach ($meta as $key => $value) {
            if (! isset($meta["_$key"]) || ! is_scalar($meta["_$key"])) {
                continue;
            }

            $field = $this->getField($meta["_$key"]);
            if (! $field || $field['name'] !== $key) {
                continue;
            }

            if ($loadValue) {
                $field['value'] = $this->getValue($contentId, $field);
            }

            if (! $formatValue && $escapeHtml) {
                $field['value'] = false;
            }

            if ($loadValue && $formatValue) {
                if ($escapeHtml) {
                    if ($this->fieldTypeSupports($field['type'], 'escaping_html')) {
                        $field['value'] = $this->formatValue($field['value'], $contentId, $field, true);
                    } else {
                        $newValue = $this->formatValue($field['value'], $contentId, $field);
                        if (is_array($newValue)) {
                            $field['value'] = $this->mapDeep($newValue, 'htmlspecialchars');
                        } else {
                            $field['value'] = htmlspecialchars($newValue);
                        }
                    }
                } else {
                    $field['value'] = $this->formatValue($field['value'], $contentId, $field);
                }
            }

            $fields[$key] = $field;
        }

        if (empty($fields)) {
            return false;
        }

        return $fields;
    }

    /**
     * 检查是否有行数据
     *
     * @param string $selector 字段选择器
     * @param int|string|null $contentId 内容ID
     * @return bool 返回是否有行数据
     */
    public function haveRows(string $selector, int|string|null $contentId = null): bool
    {
        $contentId = $this->getValidContentId($contentId);
        $key = "selector={$selector}/contentId={$contentId}";
        $activeLoop = $this->getLoop('active');
        $prevLoop = $this->getLoop('previous');
        $newLoop = false;
        $subField = false;

        if (! $activeLoop) {
            $newLoop = 'parent';
        } elseif ($key !== $activeLoop['key']) {
            $subField = $this->getSubField($selector, $activeLoop['field']);
            if ($subField) {
                $subFieldExists = isset($activeLoop['value'][$activeLoop['i']][$subField['key']]);
            }

            if ($contentId != $activeLoop['contentId']) {
                if (empty($contentId) && $subFieldExists) {
                    $newLoop = 'child';
                } elseif ($prevLoop && $prevLoop['contentId'] == $contentId) {
                    $this->removeLoop('active');
                    $activeLoop = $prevLoop;
                } else {
                    $newLoop = 'parent';
                }
            } elseif ($selector != $activeLoop['selector']) {
                if ($subFieldExists) {
                    $newLoop = 'child';
                } elseif ($prevLoop && $prevLoop['selector'] == $selector && $prevLoop['contentId'] == $contentId) {
                    $this->removeLoop('active');
                    $activeLoop = $prevLoop;
                } else {
                    $newLoop = 'parent';
                }
            }
        }

        if ($newLoop) {
            $args = [
                'key' => $key,
                'selector' => $selector,
                'contentId' => $contentId,
                'name' => null,
                'value' => null,
                'field' => null,
                'i' => -1,
            ];

            if ($newLoop === 'parent') {
                $field = $this->getFieldObject($selector, $contentId, false);
                if ($field) {
                    $args['field'] = $field;
                    $args['value'] = $field['value'];
                    $args['name'] = $field['name'];
                    unset($args['field']['value']);
                }
            } else {
                $args['field'] = $subField;
                $args['value'] = $activeLoop['value'][$activeLoop['i']][$subField['key']];
                $args['name'] = "{$activeLoop['name']}_{$activeLoop['i']}_{$subField['name']}";
                $args['contentId'] = $activeLoop['contentId'];
            }

            if (! $args['value'] || ! is_array($args['value'])) {
                return false;
            }

            if ($this->getFieldTypeProp($args['field']['type'], 'have_rows') === 'single') {
                $args['value'] = [$args['value']];
            }

            $activeLoop = $this->addLoop($args);
        }

        if ($activeLoop && isset($activeLoop['value'][$activeLoop['i'] + 1])) {
            return true;
        }

        $this->removeLoop('active');
        return false;
    }

    /**
     * 获取当前行的数据
     *
     * @param bool $format 是否格式化数据
     * @return array|false 返回行数据或false
     */
    public function theRow(bool $format = false): array|false
    {
        $i = $this->getLoop('active', 'i');
        ++$i;
        $this->updateLoop('active', 'i', $i);
        return $this->getRow($format);
    }

    /**
     * 获取行数据
     *
     * @param bool $format 是否格式化数据
     * @return array|false 返回行数据或false
     */
    public function getRow(bool $format = false): array|false
    {
        $loop = $this->getLoop('active');
        if (! $loop) {
            return false;
        }

        $value = $this->maybeGet($loop['value'], $loop['i']);
        if (! $value) {
            return false;
        }

        if ($format) {
            $field = $loop['field'];
            if ($this->getFieldTypeProp($field['type'], 'have_rows') === 'single') {
                $value = $this->formatValue($value, $loop['contentId'], $field);
            } else {
                $value = $this->formatValue($loop['value'], $loop['contentId'], $field);
                $value = $this->maybeGet($value, $loop['i']);
            }
        }

        return $value;
    }

    /**
     * 获取行的子字段对象
     *
     * @param string $selector 字段选择器
     * @return array|false 返回子字段对象或false
     */
    public function getRowSubField(string $selector): array|false
    {
        $row = $this->getLoop('active');
        if (! $row) {
            return false;
        }

        $subField = $this->getSubField($selector, $row['field']);
        if (! $subField) {
            return false;
        }

        $subField['name'] = "{$row['name']}_{$row['i']}_{$subField['name']}";
        return $subField;
    }

    /**
     * 获取行的子字段值
     *
     * @param string $selector 字段选择器
     * @return mixed 返回子字段值或null
     */
    public function getRowSubValue(string $selector): mixed
    {
        $row = $this->getLoop('active');
        if (! $row) {
            return null;
        }

        return $row['value'][$row['i']][$selector] ?? null;
    }

    /**
     * 检查是否有子字段
     *
     * @param string $fieldName 字段名称
     * @param int|string|null $contentId 内容ID
     * @return bool 返回是否有子字段
     */
    public function hasSubField(string $fieldName, int|string|null $contentId = null): bool
    {
        $r = $this->haveRows($fieldName, $contentId);
        if ($r) {
            $this->theRow();
        }

        return $r;
    }

    /**
     * 检查是否有子字段（别名）
     *
     * @param string $fieldName 字段名称
     * @param int|string|null $contentId 内容ID
     * @return bool 返回是否有子字段
     */
    public function hasSubFields(string $fieldName, int|string|null $contentId = null): bool
    {
        return $this->hasSubField($fieldName, $contentId);
    }

    /**
     * 获取子字段值
     *
     * @param string $selector 字段选择器
     * @param bool $formatValue 是否格式化字段值
     * @param bool $escapeHtml 是否转义HTML
     * @return mixed 返回子字段值或false
     */
    public function getSubField(string $selector = '', bool $formatValue = true, bool $escapeHtml = false): mixed
    {
        $subField = $this->getSubFieldObject($selector, $formatValue, true, $escapeHtml);
        if (! $subField) {
            return false;
        }

        return $subField['value'];
    }

    /**
     * 输出子字段值
     *
     * @param string $fieldName 字段名称
     * @param bool $formatValue 是否格式化字段值
     * @return void
     */
    public function theSubField(string $fieldName, bool $formatValue = true): void
    {
        $field = $this->getSubFieldObject($fieldName, $formatValue, true, $formatValue);
        $value = $field['value'] ?? false;

        if (is_array($value)) {
            $value = implode(', ', $value);
        }

        if (! is_scalar($value)) {
            return;
        }

        if (! $formatValue) {
            $value = htmlspecialchars($value);
        }

        $unescapedField = $this->getSubFieldObject($fieldName, $formatValue, true, false);
        $unescapedValue = $unescapedField['value'] ?? false;
        if (is_array($unescapedValue)) {
            $unescapedValue = implode(', ', $unescapedValue);
        }

        $fieldType = $field['type'] ?? 'text';
        if ($this->applyFilters('acf/the_field/allow_unsafe_html', false, $fieldName, 'sub_field', $fieldType, $field)) {
            $value = $unescapedValue;
        } elseif ((string) $value !== (string) $unescapedValue) {
            $this->doAction('acf/removed_unsafe_html', __FUNCTION__, $fieldName, $field, false);
        }

        echo $value;
    }

    /**
     * 获取子字段对象
     *
     * @param string $selector 字段选择器
     * @param bool $formatValue 是否格式化字段值
     * @param bool $loadValue 是否加载字段值
     * @param bool $escapeHtml 是否转义HTML
     * @return array|false 返回子字段对象或false
     */
    public function getSubFieldObject(string $selector, bool $formatValue = true, bool $loadValue = true, bool $escapeHtml = false): array|false
    {
        $row = $this->getLoop('active');
        if (! $row) {
            return false;
        }

        $subField = $this->getRowSubField($selector);
        if (! $subField) {
            return false;
        }

        if ($loadValue) {
            $subField['value'] = $this->getRowSubValue($subField['key']);
        }

        if (! $formatValue && $escapeHtml) {
            $subField['value'] = false;
        }

        if ($loadValue && $formatValue) {
            if ($escapeHtml) {
                if ($this->fieldTypeSupports($subField['type'], 'escaping_html')) {
                    $subField['value'] = $this->formatValue($subField['value'], $row['contentId'], $subField, true);
                } else {
                    $newValue = $this->formatValue($subField['value'], $row['contentId'], $subField);
                    if (is_array($newValue)) {
                        $subField['value'] = $this->mapDeep($newValue, 'htmlspecialchars');
                    } else {
                        $subField['value'] = htmlspecialchars($newValue);
                    }
                }
            } else {
                $subField['value'] = $this->formatValue($subField['value'], $row['contentId'], $subField);
            }
        }

        return $subField;
    }
    /**
     * 根据选择器获取字段
     *
     * @param string $selector 字段选择器
     * @param int|string|null $contentId 内容ID
     * @param bool $formatValue 是否格式化字段值
     * @param bool $escapeHtml 是否转义HTML
     * @return mixed 返回字段值或false
     */
    private function getFieldBySelector(string $selector, int|string|null $contentId, bool $formatValue, bool $escapeHtml): mixed
    {
        $field = $this->getFieldObject($selector, $contentId, $formatValue, true, $escapeHtml);
        return $field['value'] ?? false;
    }


    /**
     * 获取有效的内容ID
     *
     * @param int|string|null $contentId 内容ID
     * @return int|string 返回有效的内容ID
     */
    private function getValidContentId(int|string|null $contentId): int|string
    {
        // 实现 getValidContentId 的逻辑
        return $contentId ?? 0;
    }

    /**
     * 检查字段类型是否支持指定功能
     *
     * @param mixed $type 字段类型
     * @param string $support 支持的功能
     * @return bool 返回是否支持
     */
    private function fieldTypeSupports(mixed $type, string $support): bool
    {
        // 实现 fieldTypeSupports 的逻辑
        return false;
    }

    /**
     * 获取循环中的数据
     *
     * @param string $type 类型
     * @return mixed 返回循环中的数据
     */
    private function getLoop(string $type): mixed
    {
        // 实现 getLoop 的逻辑
        return [];
    }

    /**
     * 应用过滤器
     *
     * @param string $tag 过滤器标签
     * @param mixed $value 初始值
     * @param mixed ...$args 其他参数
     * @return mixed 返回过滤后的值
     */
    private function applyFilters(string $tag, mixed $value, mixed ...$args): mixed
    {
        // 实现 applyFilters 的逻辑
        return $value;
    }

    /**
     * 执行动作
     *
     * @param string $tag 动作标签
     * @param mixed ...$args 其他参数
     * @return void
     */
    private function doAction(string $tag, mixed ...$args): void
    {
        // 实现 doAction 的逻辑
    }

    /**
     * 获取字段类型属性
     *
     * @param mixed $type 字段类型
     * @param string $prop 属性名称
     * @return mixed 返回属性值
     */
    private function getFieldTypeProp(mixed $type, string $prop): mixed
    {
        // 实现 getFieldTypeProp 的逻辑
        return null;
    }

    /**
     * 获取数组中的值
     *
     * @param array|null $array 数组
     * @param mixed $key 键
     * @return mixed 返回值或null
     */
    private function maybeGet(?array $array, mixed $key): mixed
    {
        return $array[$key] ?? null;
    }
    private function formatValue(mixed $value, int|string|null $contentId, array $field, bool $escapeHtml = false): mixed
    {
        $fieldInstance = FieldFactory::create($field);

        return $fieldInstance->formatValue($value, $contentId, $field, $escapeHtml);
    }

    /**
     * 获取字段值
     *
     * @param int|string $contentId 内容ID
     * @param array $field 字段对象
     * @return mixed 返回字段值
     */
    private function getValue(int|string $contentId, array $field): mixed
    {
        return CfmMeta::getFieldValue($field['name'], $contentId);
    }

    /**
     * 递归处理数组
     *
     * @param array $array 数组
     * @param callable $callback 回调函数
     * @return array 返回处理后的数组
     */
    private function mapDeep(array $array, callable $callback): array
    {
        foreach ($array as &$value) {
            if (is_array($value)) {
                $value = $this->mapDeep($value, $callback);
            } else {
                $value = $callback($value);
            }
        }
        return $array;
    }

    /**
     * 检查选择器是否为字段键
     *
     * @param mixed $selector 选择器
     * @return bool 返回是否为字段键
     */
    private function isFieldKey(mixed $selector): bool
    {
        // 实现 isFieldKey 的逻辑
        return false;
    }

}
