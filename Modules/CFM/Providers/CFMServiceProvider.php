<?php

namespace Modules\CFM\Providers;

use Bingo\Providers\BingoModuleServiceProvider;
use Illuminate\Contracts\Support\DeferrableProvider;
use Modules\CFM\Support\Helpers\CustomFieldHelper;
use Modules\CFM\Support\Helpers\FieldHelper;
use Modules\Iam\Enums\MenuType;

class CFMServiceProvider extends BingoModuleServiceProvider implements DeferrableProvider
{
    public function boot(): void
    {
        $path = dirname(__DIR__, 2).DIRECTORY_SEPARATOR.'CFM'.DIRECTORY_SEPARATOR.'Lang';
        $this->loadTranslationsFrom($path, 'CFM');
        $this->registerNavigation(); // 注册导航项

        $this->app->singleton('fieldhelper', function () {
            return new FieldHelper();
        });

        $this->app->singleton('cfm.helper', function () {
            return new CustomFieldHelper();
        });
        $this->registerModulePermissions();
    }


    /**
     * route path
     *
     * @return string
     */
    public function moduleName(): string
    {
        return 'CFM';
    }

    protected function navigation(): array
    {
        return [
            [
                "key" => "cfm_management",
                "parent" => "application",
                "nav_name" => T("CFM::nav.cfm_management"),
                "path" => "/cfm/cfmList",
                "icon" => "Nav/Asset/menu_icon/cfm_management.png",
                "order" => 1
            ]
        ];
    }

    /**
     * 注册配置
     * @return array
     */
    public function registerSettings(): array
    {
        return [];
    }

    public function provides(): array
    {
        return ['fieldhelper', 'cfm.helper'];
    }

    public function registerPermissions(): array
    {
        $admin = [
            'cfm' => [
                'permission_name' => T("CFM::permission.cfm_management"),
                'route' => '/cfm',
                'parent_id' => 0,
                'permission_mark' => 'cfm',
                'component' => '/admin/layout/index.vue',
                'type' => MenuType::Top->value(),
                'sort' => 1,
                'children' => [
                    [
                        'permission_name' => T("CFM::permission.custom_field_list"),
                        'route' => 'cfmList',
                        'parent_id' => 'cfm',
                        'permission_mark' => 'field',
                        'component' => '/admin/layout/index.vue',
                        'type' => MenuType::Menu->value(),
                        'sort' => 1,
                        'actions' => [
                            [
                                'permission_name' => T("Delete"),
                                'route' => '',
                                'permission_mark' => 'field@destroy',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 1,
                                'parent_id' => 'field'
                            ],
                        ],
                    ],
                    [
                        'permission_name' => T("CFM::permission.add_field"),
                        'route' => 'addFiled',
                        'parent_id' => 'cfm',
                        'permission_mark' => 'field.add',
                        'component' => '/admin/layout/index.vue',
                        'type' => MenuType::Menu->value(),
                        'sort' => 2,
                        'actions' => [],
                    ],
                ],
            ],
        ];
        $frontend = [];
        return array_merge(["admin" => $admin], ["frontend" => $frontend]);
    }


}
