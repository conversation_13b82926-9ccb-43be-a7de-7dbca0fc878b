<?php

namespace Modules\CFM\Models;

use Bingo\Base\BingoModel as Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CfmFields extends Model
{
    protected $table = 'cfm_fields';

    protected $fillable = [
        'id', 'group_id', 'name','parent_key', 'key', 'label', 'type', 'default_value', 'settings', 'creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

    protected $casts = [
        'settings' => 'array',
    ];

    public function fieldGroup(): BelongsTo
    {
        return $this->belongsTo(CfmFieldGroups::class, 'group_id');
    }

}
