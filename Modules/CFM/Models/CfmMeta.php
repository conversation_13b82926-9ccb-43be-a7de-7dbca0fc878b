<?php

namespace Modules\CFM\Models;

use Bingo\Base\BingoModel as Model;

class CfmMeta extends Model
{
    protected $table = 'cfm_meta';

    protected $fillable = [
        'id', 'content_id', 'meta_key', 'meta_value', 'lang', 'creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

    /**
     * 根据fieldKey查询metaValue
     *
     * @param int|string $contentId 内容ID
     * @param string $fieldKey 字段键
     * @return string|null 返回字段值或null
     */
    public static function getMetaValueByFieldKey(int|string $contentId, string $fieldKey): ?string
    {
        $metaKey = self::where('content_id', $contentId)
            ->where('meta_value', $fieldKey)
            ->value('meta_key');

        if ($metaKey) {
            $name = ltrim($metaKey, '_');

            return self::where('content_id', $contentId)
                ->where('meta_key', $name)
                ->value('meta_value');
        }

        return null;
    }

    /**
     * 保存或更新字段值
     *
     * @param int|string $contentId 内容ID
     * @param string $name 字段名称
     * @param string $value 字段值
     * @param string $metaKey 字段键
     * @param string $lang 语言
     * @return bool 返回保存结果
     */
    public static function saveOrUpdate(int|string $contentId, string $name, string $value, string $metaKey, string $lang): bool
    {
        self::updateOrCreate(
            ['content_id' => $contentId, 'meta_key' => $name],
            [
                'meta_value' => $value,
                'lang' => $lang
            ]
        );
        self::updateOrCreate(
            ['content_id' => $contentId, 'meta_key' => '_'.$name],
            [
                'meta_value' => $metaKey,
                'lang' => $lang
            ]
        );

        return true;
    }

    /**
     * 获取字段的meta信息
     *
     * @param string $fieldName 字段名称
     * @param int|string $contentId 内容ID
     * @return array|null 返回字段的meta信息或null
     */
    public static function getFieldMeta(string $fieldName, int|string $contentId): ?array
    {
        $metaKey = self::where('content_id', $contentId)
            ->where('meta_key', "_".$fieldName)
            ->first();

        if (! $metaKey) {
            return null;
        }

        return [
            'meta_key' => $fieldName,
            'meta_value' => $metaKey->meta_value,
        ];
    }

    /**
     * 获取字段详细信息
     *
     * @param string $fieldKey 字段键
     * @param int|string $contentId 内容ID
     * @param string $name 字段名称
     * @return array 返回字段详细信息
     */
    public static function getFieldDetails(string $fieldKey, int|string $contentId, string $name): array
    {

        $field = CfmFields::where('key', $fieldKey)->first();

        if ($field) {
            $field = $field->toArray();
            $fieldSettings = json_decode($field['settings'], true);
            $fieldSettings['content_id'] = $contentId;

            $lang = self::where('content_id', $contentId)
                ->where('meta_key', $name)
                ->value('lang');

            $fieldSettings['lang'] = $lang;
            $field['settings'] = json_encode($fieldSettings);
            return $field;
        }
        return [];
    }

    /**
     * 获取字段的值
     *
     * @param string $fieldKey 字段键
     * @param int|string $contentId 内容ID
     * @return mixed 返回字段值
     */
    public static function getFieldValue(string $fieldKey, int|string $contentId): mixed
    {
        return self::where('content_id', $contentId)
            ->where('meta_key', $fieldKey)
            ->value('meta_value');
    }
    /**
     * 根据内容ID获取所有meta信息
     *
     * @param int|string $contentId 内容ID
     * @return array 返回所有meta信息的数组
     */
    public static function getAllMetaByContentId(int|string $contentId): array
    {
        return self::where('content_id', $contentId)
            ->get()
            ->toArray();
    }
}
