<?php

namespace Modules\CFM\Models;

use Bingo\Base\BingoModel as Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CfmFieldGroups extends Model
{
    protected $table = 'cfm_field_groups';

    protected $fillable = [
        'id', 'name', 'key', 'title', 'description', 'location_rules', 'presentation_settings', 'active', 'creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

    protected $casts = [
        'location_rules' => 'array',  // 将其转换为 JSON 格式
        'presentation_settings' => 'array',  // 将其转换为 JSON 格式
    ];

    public function fields(): HasMany
    {
        return $this->hasMany(CfmFields::class, 'group_id');
    }
}
