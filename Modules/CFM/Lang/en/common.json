{"detail": {"field_group": "Field Group", "edit_field": "Edit Field Group", "add_field": "Add Field", "addline": "Add line", "save": "Save Changes", "total": "Total", "active": "Active", "recycling_bins": "Recycle Bin", "search": "Filter", "refresh": "Reset", "filter": "Filter", "field_title": "Field Group Title", "content_tit": "Content Title", "content_tit_placeholder": "Please enter the content title", "description": "Description", "key": "Key", "position": "Position", "category": "Category", "operate": "Operate", "apply_mechanically": "Apply", "edit": "Edit", "duplicate": "Duplicate", "deactivate": "Deactivate", "delete": "Move to Recycle Bin", "del": "Delete", "label": "Label", "name": "Name", "type": "Type", "move": "Move", "general": "General", "validation": "Validation", "presentation": "Presentation", "conditional_logic": "Conditional Logic", "close_field": "Close", "add": "Add Archived", "location_rules": "Location Rules", "group_settings": "Group Settings", "field_type": "Field Type", "field_label": "Field Label", "field_name": "Field Name", "default_value": "Default Value", "settings": "Settings", "show_this_field_group_if": "Show this field if", "add_rule_group": "Add Rule Group", "delette_field_group": "Delete Field Group", "select_image": "Select Image", "upload_file": "Upload File", "media_library": "Media Library", "upload": "Upload", "searchs": "Search", "custom_value": "Custom Value", "clear_message": "Clear Message", "not_found": "No matching items found.", "drag_upload": "Drag files here to upload", "upload_limit": "File size limit is {size}MB", "click_upload": "Click to upload", "choice": "Choice", "select": "Select", "browse_fields": "Browse Fields", "will_show": "This will be the name that appears on the edit page", "singele": "Single word, no spaces. Underscores and dashes are allowed", "select_field_type": "Select Field Type", "search_field": "Search Field", "close": "Close", "select_field": "Select Field", "field": "Field", "please_select": "Please Select", "display": "Display", "seamless": "Seamless", "group": "Group", "block": "Block", "table": "Table", "row": "Row", "prefix_label": "Prefix Label", "label_display_as": "The label will be displayed as", "prefix_name": "Prefix Name", "value_stored_as": "The name will be displayed as", "default_value_placeholder": "#FFFFFF", "enable_transparency": "Enable Transparency", "return_format": "Return Format", "hex_string": "Hex String", "rgba_array": "RGBA Array", "display_format": "Display Format", "custom": "Custom", "custom_placeholder": "d/m/Y", "week_starts_on": "Week Starts On", "select_day": "Select Day", "custom_placeholder_full": "d/m/Y g:i a", "display_on_new_post": "Display on New Post", "valid_email_error": "Please enter a valid email address", "return_value": "Return Value", "file_array": "File Array", "file_url": "File URL", "file_id": "File ID", "all": "All", "uploaded_to_post": "Uploaded to Post", "limit_media_library": "Limit Media Library Selection", "image_array": "Image Array", "image_url": "Image URL", "image_id": "Image ID", "center": "Center", "lat": "Latitude", "lng": "Longitude", "center_initial_map": "Center Initial Map", "zoom": "Zoom", "set_initial_zoom": "Set Initial Zoom Level", "height": "Height", "height_placeholder": "400", "customize_map_height": "Customize Map Height", "tabs": "Tabs", "dashicons": "Dashicons", "url": "URL", "select_icon_source": "Select the location from which the content editor can choose icons.", "string": "String", "specify_return_format": "Specify the return format for the icon.", "link_array": "<PERSON>", "link_url": "Link URL", "specify_returned_value": "Specify the value returned to the front end", "message": "Message", "introduction": "Introduction", "new_line": "New Line", "control_new_line": "Control the presentation of new lines", "escape_html": "Escape HTML", "allow_multiple_values": "Allow content editors to select multiple values", "embed_size": "<PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON>", "filter_by_post_type": "Filter by Post Type", "all_post_types": "All Post Types", "filter_by_post_status": "Filter by Post Status", "any_post_status": "Any Post Status", "filter_by_taxonomy": "Filter by Taxonomy", "all_taxonomies": "All Taxonomies", "allow_archives_urls": "Allow Archive URLs", "select_multiple": "Select Multiple", "post_object": "Post Object", "post_id": "Post ID", "post_type": "Post Type", "taxonomy": "Taxonomy", "enter_each_option_new_line": "Enter each option on a new line.", "specify_value_label": "For more control, you can specify both a value and label like this:", "enter_each_default_new_line": "Enter each default value on a new line", "select_all_array": "Select All (Array)", "specify_return_value": "Specify the return value", "allow_multiple_selection": "Allow content editors to select multiple values", "placement": "Placement", "select_placement": "Select Placement", "top_aligned": "Top Aligned", "left_aligned": "Left Aligned", "new_tab_group": "New Tab Group", "start_new_tab_group": "Start a new tab group at this tab", "select_taxonomy": "Select Taxonomy", "select_taxonomy_displayed": "Select the taxonomy to display", "create_terms": "Create Terms", "allow_create_terms": "Allow creation of new terms while editing", "save_terms": "Save Terms", "connect_selected_terms": "Connect selected terms to the post", "load_terms": "Load Terms", "load_value_from_terms": "Load value from post terms", "term_object": "Term Object", "term_id": "Term ID", "appearance": "Appearance", "select_appearance": "Select Appearance", "checkbox": "Checkbox", "multi_select": "Multi-select", "radio_buttons": "Radio Buttons", "select_field_appearance": "Select the appearance of this field", "custom_format": "Custom Format", "display_format_description": "Format displayed when editing the post", "return_format_description": "Format returned by the template function", "checkbox_text_placeholder": "Text displayed next to the checkbox", "filter_by_role": "Filter by Role", "all_user_roles": "All User Roles", "user_array": "User <PERSON><PERSON>", "user_object": "User Object", "user_id": "User ID", "instructions": "Instructions", "instructions_for_editors": "Instructions for content editors. Displayed when submitting data.", "instructions_placeholder": "Instructions for content editors", "layout": "Layout", "vertical": "Vertical", "horizontal": "Horizontal", "wrapper_attributes": "Wrapper Attributes", "placeholder_text": "Placeholder Text", "appear_in_input": "Appears in input", "prepend": "Prepend", "appear_before_input": "Appears before input", "append": "Append", "appear_after_input": "Appears after input", "min_layouts": "Minimum Layouts", "min_layouts_placeholder": "Minimum number of layouts", "max_layouts": "Maximum Layouts", "max_layouts_placeholder": "Maximum number of layouts", "button_label": "Button Label", "button_label_placeholder": "Button Label", "insert": "Insert", "select_insert_position": "Select Insert Position", "preview_size": "Preview Size", "select_preview_size": "Select Preview Size", "small": "Small (150 x 150)", "medium": "Medium (300 x 300)", "large": "Large (600 x 600)", "instructions_for_editors_placeholder": "Instructions for content editors", "step_size": "Step Size", "elements": "Elements", "featured_image": "Featured Image", "selected_elements": "Selected elements will be displayed in each result", "collapsed": "Collapsed", "collapsed_description": "Select a sub-field to display when row is collapsed", "stylized_ui": "Stylized UI", "use_select2": "Use select2 for stylized checkboxes", "use_ajax": "Use AJAX to delay loading choices?", "rows": "Number of Rows", "set_textarea_height": "Set textarea height", "new_line_control": "Control the presentation of new lines", "ui_on_text": "On Text", "ui_on_text_placeholder": "Text displayed when active", "ui_off_text": "Off Text", "ui_off_text_placeholder": "Text displayed when inactive", "select_tabs": "Select Tabs", "visual_text": "Visual & Text", "visual_only": "Visual Only", "text_only": "Text Only", "toolbar": "<PERSON><PERSON><PERSON>", "select_toolbar": "Select Toolbar", "full_toolbar": "Full", "basic_toolbar": "Basic", "show_media_upload": "Show Media Upload Button", "delay_initialization": "Delay Initialization", "delay_description": "TinyMCE will be initialized when the field is clicked.", "show_in_field_group_list": "Show in Field Group List", "delete_field_group": "Delete Field Group", "none": "None", "required": "Required", "allow_null": "Allow <PERSON>", "allow_custom_value": "Allow Custom Value", "min_size": "Minimum", "max_size": "Maximum", "file_size": "File Size", "file_upload_limit": "Limit files that can be uploaded", "allowed_file_types": "Allowed File Types", "file_types_description": "List separated by commas, leave blank to allow all types", "min_value": "Minimum Value", "max_value": "Maximum Value", "min": "Minimum", "max": "Maximum", "image_upload_limit": "Limit images that can be uploaded", "allow_other_choice": "Allow Other Choice", "allow_other_choice_description": "Add an 'other' option for custom values", "min_posts": "Minimum Posts", "max_posts": "Maximum Posts", "min_rows": "Minimum Rows", "max_rows": "Maximum Rows", "field_group_title": "Please enter the Field Group Title", "sub_field": "Sub Field", "add_layout": "Add Layout", "delete_layout": "Delete Layout", "select_config": "Select Configuration", "field_group_position": "Field Group Position", "useful_for_large_fields": "Useful for large field groups", "posts_per_page": "Posts per Page", "open": "Open", "opens_in_new_tab": "Opens in a new window/tab", "select_link": "Select Link", "enter_url": "Enter URL", "title": "Title", "enter_title": "Enter title", "open_new_tab": "Open in a new window/tab", "cancel": "Cancel"}, "field": {"field_label": "Field Label", "field_name": "Field Name", "default_value": "Default Value", "will_show": "This will be the name that appears on the edit page", "singele": "Single word, no spaces. Underscores and dashes are allowed", "show_new": "Show when creating new post", "required": "Required", "Maximum_valueNum": "Maximum Value", "Minimum_value": "Minimum Value", "chartLimit": "Character Limit", "Leave_blank_for_no_limit": "Leave blank for no limit", "introuduction": "Instructions", "placeholder_text": "Placeholder Text", "prepenned": "Prepended", "append": "Appended", "wrapper_attributes": "Wrapper Attributes"}, "dialog": {"text": "Text", "textarea": "Textarea", "number": "Number", "email": "Email", "url": "URL", "link": "Link", "password": "Password", "wysiwyg_editor": "WYSIWYG Editor", "file": "File", "image": "Image", "gallery": "Gallery", "select": "Select", "checkbox": "Checkbox", "radio": "Radio", "true_false": "True/False", "date_picker": "Date Picker", "time_picker": "Time Picker", "date_time_picker": "Date Time Picker", "google_map": "Google Map", "color_picker": "Color Picker", "tab": "Tab", "message": "Message", "repeater": "<PERSON><PERSON><PERSON>", "flexible_content": "Flexible Content", "clone": "<PERSON><PERSON>", "group": "Group", "oembed": "OEmbed", "relationship": "Relationship", "taxonomy": "Taxonomy", "user": "User", "post_object": "Post Object", "page_link": "Page Link", "post_link": "Post Link", "accordion": "Accordion", "range": "Range", "wysiwyg": "WYSIWYG Editor", "button_group": "Button Group", "icon_picker": "<PERSON><PERSON> Picker"}, "description": {"text": "Basic text input, suitable for storing a single string value.", "textarea": "Basic textarea input for storing paragraphs of text.", "number": "Input limited to numeric values.", "email": "Text input designed specifically for storing email addresses.", "url": "Text input designed specifically for storing URLs.", "link": "Allows you to specify links and their attributes such as title and target using the native WordPress link picker.", "password": "Input for passwords with masking.", "wysiwyg": "Displays the WordPress WYSIWYG editor as seen on posts and pages, allowing for rich text editing and multimedia content. We do not recommend using this field in ACF blocks.", "file": "Upload or select files using the native WordPress media picker.", "image": "Upload or select images using the native WordPress media picker.", "gallery": "Interactive interface for managing a collection of attachments such as images.", "select": "Dropdown list of choices specified by you.", "checkbox": "Group of checkbox inputs allowing the user to select one or more values specified by you.", "radio": "Group of radio button inputs allowing the user to select a single value from choices specified by you.", "true_false": "Toggle allowing you to select a value of 1 or 0 (on or off, true or false, etc.). Can be displayed as a stylized switch or checkbox.", "date_picker": "Select a date using the native WordPress date picker.", "time_picker": "Select a time using the native WordPress time picker.", "date_time_picker": "Select a date and time using the native WordPress date and time picker.", "google_map": "Interactive interface for selecting a location using Google Maps. Requires a Google Maps API key and other configuration to display correctly.", "color_picker": "Select a color using the native WordPress color picker.", "tab": "Allows you to group fields into tabbed sections on the edit screen. Useful for keeping fields organized and structured.", "message": "Used to display messages to the editor along with other fields. Can be used to provide additional context or instructions about fields.", "repeater": "Allows you to select and display existing fields. It does not duplicate any fields in the database, instead it loads and displays selected fields at runtime. 'Clone' fields can replace themselves with selected fields or display selected fields as a group of subfields.", "flexible_content": "Allows you to define, create, and manage content by creating layouts containing subfields that content editors can choose from, giving complete control. We do not recommend using this field in ACF blocks.", "clone": "Allows you to select and display existing fields. It does not duplicate any fields in the database, instead it loads and displays selected fields at runtime. 'Clone' fields can replace themselves with selected fields or display selected fields as a group of subfields.", "group": "Provides a way to structure fields into groups, making data and edit screens more organized.", "oembed": "Interactive component for embedding videos, images, tweets, audio, and other content using the native WordPress oEmbed feature.", "relationship": "A dual-column interface for selecting one or more posts, pages, or custom post type items to create relationships with the current item being edited. Includes search and filter options.", "taxonomy": "Interactive interface for selecting taxonomy terms, tags, or custom taxonomy items.", "user": "Interactive interface for selecting users, including search and filter options.", "post_object": "Interactive and customizable UI for selecting one or more posts, pages, or post type items, providing search options.", "page_link": "Allows you to specify links and their attributes such as title and target using the native WordPress link picker.", "post_link": "Allows you to specify links and their attributes such as title and target using the native WordPress link picker.", "button_group": "Button Group", "range": "Range", "icon_picker": "<PERSON><PERSON> Picker", "accordion": "Accordion"}, "fieldSet": {"Conditional_logic": "Conditional Logic", "Show_if": "Show this field if", "delete_group": "Delete Group", "add_group": "Add Group", "opean": "Open", "multi_expand": "Multi Expand", "ebdpoint": "Endpoint", "openInfo": "Display this accordion as open on page load.", "multi_expandInfo": "Allow this accordion to open without closing others.", "endpointInfo": "Define an endpoint for the previous accordion to stop. This accordion will not be visible.", "choices": "Choices", "return_format": "Return Format", "choiceInfo": "Enter each choice on a new line. For more control, you may specify both a value and label like this: red : Red", "return_formatInfo": "Specify the returned value on front end", "choice_placeholder": "Enter choices, one per line", "default_value": "Default Value", "default_value_placeholder": "Displayed when creating new posts", "return_value": "Return Value", "value": "Value", "label": "Label", "array": "Two (<PERSON><PERSON><PERSON>)"}, "protect": {"yourWebsite": "Your website is under", "protect": "protection", "nextTakeCare": "Next system maintenance", "today": "Today's protection count", "total": "Total protection count", "scanning": "Scanning files", "danger": "Danger detected", "activityLogBox": "Activity log box", "physicalCore": "Physical core", "logicalCore": "Logical core", "system": "System", "total1": "Total", "total2": "Total", "CPU": "CPU", "used": "Used", "available": "Available", "usageRate": "Usage rate", "recent1MinuteLoad": "Recent 1 minute average load", "recent5MinuteLoad": "Recent 5 minutes average load", "recent15MinuteLoad": "Recent 15 minutes average load", "basicInfo": "Basic information", "runningSmoothly": "Running smoothly", "runningNormally": "Running normally", "runningSlowly": "Running slowly", "runningCongested": "Running congested", "memory": "Memory", "load": "Load", "swapMemory": "Swap memory", "device": "<PERSON><PERSON>", "type": "Type", "mountPoint": "Mount point", "inodesTotal": "Total inodes", "inodesUsed": "Used inodes", "inodesFree": "Free inodes", "inodesUsedPercent": "Inodes usage rate", "diskUsage": "Disk usage", "diskUsedPercent": "Disk usage percentage", "percent": "Percentage", "more": "More", "hide": "<PERSON>de", "units": {"core": "Unit core", "logicCore": "Logical core"}, "core": "Core", "logicCore": "Logical core", "free": "Free", "swapMem": "Swap memory", "baseInfo": "Basic information", "mount": "Mount point", "fileSystem": "File system", "disk": "Disk", "loadAverage": "Load average"}, "common": {"page_size_text": "Show per page", "total_items": "Total {total} items"}, "router": {"cfmManagement": "CFM Management", "fieldGroupList": "Field Group List", "addField": "Add Field", "renderingPreview": "Rendering Preview"}}