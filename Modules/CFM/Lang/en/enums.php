<?php

use Modules\CFM\Enums\CFMErrorCode;

return [
    CFMErrorCode::class => [
        CFMErrorCode::FIELD_GROUP_NOT_FOUND->name => 'Field group not found.',
        CFMErrorCode::SAVE_CUSTOM_FIELD_DATA_FAILED->name => 'Save custom field data failed.',
        CFMErrorCode::LOCATION_IS_NOT_VALID->name => 'Location is not valid.',
        CFMErrorCode::FIELD_GROUP_DUPLICATE_NAME->name => 'Field group with the same name already exists.',
        CFMErrorCode::CONTENT_ID_IS_REQUIRED->name => 'Content ID is required.',
        CFMErrorCode::FIELD_GROUP_KEY_REQUIRED->name => 'Field group key is required.',
        CFMErrorCode::UNSUPPORTED_FIELD_TYPE->name => 'Unsupported field type.',
        CFMErrorCode::FIELD_GROUP_KEY_CANNOT_CHANGE->name => 'Field group key cannot change.',
    ],
];
