<?php

use Modules\CFM\Enums\CFMErrorCode;

return [
    CFMErrorCode::class => [
        CFMErrorCode::FIELD_GROUP_NOT_FOUND->name => '未找到字段组。',
        CFMErrorCode::SAVE_CUSTOM_FIELD_DATA_FAILED->name => '保存自定义字段数据失败。',
        CFMErrorCode::LOCATION_IS_NOT_VALID->name => '位置无效。',
        CFMErrorCode::FIELD_GROUP_DUPLICATE_NAME->name => '具有相同名称的字段组已存在。',
        CFMErrorCode::CONTENT_ID_IS_REQUIRED->name => '内容 ID 是必需的。',
        CFMErrorCode::FIELD_GROUP_KEY_REQUIRED->name => '字段组键是必需的。',
        CFMErrorCode::UNSUPPORTED_FIELD_TYPE->name => '不支持的字段类型。',
        CFMErrorCode::FIELD_GROUP_KEY_CANNOT_CHANGE->name => '字段组键不能更改。'
    ],
];
