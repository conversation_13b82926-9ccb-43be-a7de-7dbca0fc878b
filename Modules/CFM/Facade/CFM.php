<?php

namespace Modules\CFM\Facade;

use Illuminate\Support\Facades\Facade;

/**
 * CFM Facade
 *
 * @method static string getField(int|string $contentId, string $fieldName)
 * @method static string getFieldObject(int|string $contentId,string $modelname)
 * @see \Modules\CFM\Support\Helpers\CustomFieldHelper
 */
class CFM extends Facade
{
    /**
     * 获取Facade访问器的名称
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return 'cfm.helper';
    }
}
