<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
      <div class="header-left">
        <el-input
          v-model="searchText"
          :placeholder="$t('Aticle.info.search')"
          class="search-input"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <div class="header-right">
        <el-button @click="showFilter">
          <el-icon size="16"><img :src="$asset('Cms/Asset/FilterIcon.png')" alt="" /></el-icon>
          <span>{{ $t('Aticle.info.filter') }}</span>
        </el-button>
        
        <el-button @click="exportReport">
          <el-icon size="16"><img :src="$asset('Cms/Asset/DownloadIcon.png')" alt="" /></el-icon>
          <span>{{ $t('Aticle.info.exportReport') }}</span>
        </el-button>
        
        <el-button class="button-no-border el-button-plus" @click="syncArticles" type="primary" :disabled="selectedArticles.length === 0">
          <el-icon size="16"><Refresh /></el-icon>
          <span>{{ $t('Aticle.info.syncPages') }}</span>
        </el-button>
      </div>
    </div>

    <!-- 主体内容区域 -->
     <div class="scroll-bar-custom-transparent">
      <div class="module-con">
        <div class="box">
          <el-table 
            :data="articleList" 
            style="width: 100%; height: 100%" 
            v-loading="loading"
            @selection-change="handleSelectionChange"
          >
            <template #empty>
              <el-empty :description="$t('Aticle.info.noData')" image-size="100px" />
            </template>
            <el-table-column type="selection" width="55" />
            <el-table-column prop="title" :label="$t('Aticle.info.articleTitle')" min-width="200">
              <template #default="scope">
                <div class="article-title">{{ scope.row.title }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="publish_date" :label="$t('Aticle.info.createDate')" width="200">
              <template #default="scope">
                {{ scope.row.publish_date }}
              </template>
            </el-table-column>
            <el-table-column prop="author" :label="$t('Aticle.info.author')" width="150">
              <template #default="scope">
                {{ scope.row.author }}
              </template>
            </el-table-column>
            <el-table-column prop="status" :label="$t('Aticle.info.status')" width="150">
              <template #default="scope">
                <el-tag :type="scope.row.status === '已發佈' ? 'success' : 'warning'">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column fixed="right" :label="$t('Aticle.info.operations')" width="160">
              <template #default="{ row }">
                <div class="bwms-operate-btn-box">
                <div class="bwms-operate-btn" @click="editArticle(row)">
                  <el-icon size="15"><img :src="$asset('Cms/Asset/EditIcon.png')" alt="" /></el-icon>
                </div>
                <div class="bwms-operate-btn" @click="deleteArticle(row)">
                  <el-icon size="16"><img :src="$asset('Cms/Asset/DeleteIcon.png')" alt="" /></el-icon>
                </div>
              </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页器 -->
        <div class="box-footer" style="margin-top: 18px;">
          <div class="table-pagination-style">
            <div class="pagination-left">
              <span class="page-size-text">{{ $t('Aticle.info.pageSize') }}</span>
              <el-select
                v-model="limit"
                class="page-size-select"
                @change="handleSizeChange"
              >
                <el-option
                  v-for="size in [10, 20, 50, 100]"
                  :key="size"
                  :label="size"
                  :value="size"
                  class="page-size-option"
                />
              </el-select>
              <span class="total-text">{{ $t('Aticle.info.totalItems', { total }) }}</span>
            </div>
            <div class="pagination-right">
              <el-pagination
                v-model:current-page="page"
                background
                layout="prev, pager, next"
                :page-size="limit"
                :total="total"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 最近同步记录 -->
      <div class="sync-records">
        <div class="sync-header">
          <h3>{{ $t('Aticle.info.recentSyncRecords') }}</h3>
          <el-button type="text" class="generate-report">
            {{ $t('Aticle.info.generateReport') }} <el-icon style="margin-left: 4px;"><Right /></el-icon>
          </el-button>
        </div>
        
        <div class="sync-list">
          <div class="sync-item success">
            <div class="sync-icon">
              <el-icon><Select /></el-icon>
            </div>
            <div class="sync-content">
              <div class="sync-title">
                <span>成功同步3個頁面至BINGO_EN、BINGO_CN</span>
                <span class="sync-time">3小時前</span>
              </div>
              <div class="sync-meta">
                <span>操作人：系統管理員</span>
              </div>
            </div>
          </div>
          
          <div class="sync-item success">
            <div class="sync-icon">
              <el-icon><Select /></el-icon>
            </div>
            <div class="sync-content">
              <div class="sync-title">
                <span>成功同步1個頁面至BINGO_CN</span>
                <span class="sync-time">5小時前</span>
              </div>
              <div class="sync-meta">
                <span>操作人：連主任</span>
              </div>
            </div>
          </div>

          <div class="sync-item warning">
            <div class="sync-icon">
              <el-icon><WarnTriangleFilled /></el-icon>
            </div>
            <div class="sync-content">
              <div class="sync-title">
                <span>部份同步完成：1個頁面失敗</span>
                <span class="sync-time">昨天 13:25</span>
              </div>
              <div class="sync-meta">
                <span>操作人：陳主任</span>
              </div>
              <div class="sync-error">失敗原因：目標分類不存在</div>
            </div>
          </div>

        </div>
      </div>
    </div>
   
    <!-- 同步弹窗 -->
    <el-dialog
      class="el-dialog-common-cls publish-dialog"
      v-model="dialogVisible" 
      title="頁面一鍵發佈到多個子網站"
      width="1000px"
      destroy-on-close
    >
      <div class="publish-dialog-content">
        <!-- 已选择的文章列表 -->
        <div class="selected-articles">
          <div class="section-title">已選擇的頁面</div>
          <div class="article-list">
            <div v-for="article in selectedArticles" :key="article.id" class="article-item">
              <el-icon><Document /></el-icon>
              <span>{{ article.title }}</span>
            </div>
            <div v-if="selectedArticles.length === 0" class="no-articles">
              請先選擇要發佈的頁面
            </div>
          </div>
        </div>
        
        <!-- 选择发布站点 -->
        <div class="publish-sites">
          <div class="sites-selection-header">
            <div class="section-title">選擇發佈站點</div>
            <el-checkbox v-model="selectAllSites" @change="handleSelectAllSites" class="select-all">全選</el-checkbox>
          </div>
          <div class="sites-selection">
            <div class="sites-container">
              <div class="sites-row">
                <div class="site-item" v-for="site in websiteList.slice(0, 2)" :key="site.id">
                  <div class="site-checkbox">
                    <el-checkbox v-model="site.selected"></el-checkbox>
                  </div>
                  <div class="site-info">
                    <div class="site-name">{{ site.name }}</div>
                    <div class="site-url">{{ site.url }}</div>
                  </div>
                </div>
              </div>
              <div class="sites-row">
                <div class="site-item" v-for="site in websiteList.slice(2, 4)" :key="site.id">
                  <div class="site-checkbox">
                    <el-checkbox v-model="site.selected"></el-checkbox>
                  </div>
                  <div class="site-info">
                    <div class="site-name">{{ site.name }}</div>
                    <div class="site-url">{{ site.url }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 发布设置 -->
        <div class="publish-settings">
          <div class="section-title" style="border-bottom: 1px solid #D8D8D8;">發佈設置</div>
          <div class="settings-container">
            <div class="setting-row">
              <div class="setting-item">
                <span class="setting-label">發佈時間</span>
                <div class="setting-control">
                  <el-select v-model="publishTimeType" class="time-select">
                    <el-option label="立即發佈" value="now" />
                    <el-option label="定時發佈" value="scheduled" />
                  </el-select>
                </div>
              </div>
              <div class="setting-item">
                <span class="setting-label">發佈日期</span>
                <div class="setting-control">
                  <el-date-picker
                    v-model="scheduledTime"
                    type="datetime"
                    placeholder="選擇發佈時間"
                    format="YYYY-MM-DD HH:mm"
                    class="date-picker"
                    :disabled="publishTimeType === 'now'"
                  />
                </div>
              </div>
            </div>
            
            <!-- 发布状态设置 -->
            <div class="setting-row">
              <div class="setting-item setting-status">
                <span class="setting-label">發佈狀態</span>
                <div class="setting-control">
                  <el-radio-group v-model="publishStatus">
                    <el-radio :label="'published'">發佈</el-radio>
                    <el-radio :label="'draft'">草稿</el-radio>
                    <el-radio :label="'pending'">待審核</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>
            
            <!-- 添加高级选项折叠面板 -->
            <div class="advanced-options">
              <div class="advanced-header" @click="toggleAdvancedOptions">
                <div class="header-title">
                  <span>高級選項</span>
                </div>
                <div class="header-icon">
                  <i class="el-icon" :class="{ 'is-rotate': showAdvancedOptions }">
                    <el-icon><ArrowRight /></el-icon>
                  </i>
                </div>
              </div>
              
              <div v-if="showAdvancedOptions" class="advanced-content">
                <!-- 自定义URL -->
                <div class="advanced-form-item">
                  <div class="form-label">自定義URL</div>
                  <el-input v-model="advancedOptions.customUrl" placeholder="www.example.com/" />
                </div>
                
                <!-- SEO标题 -->
                <div class="advanced-form-item">
                  <div class="form-label">SEO標題</div>
                  <el-input v-model="advancedOptions.seoTitle" placeholder="請輸入SEO標題" />
                </div>
                
                <!-- Meta描述 -->
                <div class="advanced-form-item">
                  <div class="form-label">Meta描述</div>
                  <el-input 
                    v-model="advancedOptions.metaDescription" 
                    type="textarea" 
                    :rows="4" 
                    placeholder="請輸入Meta描述"
                  />
                </div>
                
                <!-- 关键字 -->
                <div class="advanced-form-item">
                  <div class="form-label">關鍵字</div>
                  <el-input v-model="advancedOptions.keywords" placeholder="用逗號分隔多個關鍵字" />
                </div>
                
                <!-- 分类 -->
                <div class="advanced-form-item">
                  <div class="form-label">分類</div>
                  <el-select v-model="advancedOptions.category" placeholder="請選擇分類" class="full-width">
                    <el-option label="新聞公告" value="news" />
                    <el-option label="學術研究" value="research" />
                    <el-option label="校園活動" value="events" />
                    <el-option label="招生信息" value="admissions" />
                  </el-select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmPublish" :disabled="!canPublish">確認發佈</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Filter, Plus, Edit, Delete, ArrowRight, Check, Warning,WarnTriangleFilled,Select, Search, Download, Refresh, Document, Right } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 文章接口定义
interface Article {
  id: number
  title: string
  publish_date: string
  author: string
  status: string
}

// 表格数据
const loading = ref(false)
const selectedArticles = ref<Article[]>([])
const searchText = ref('')

// 静态文章数据
const articleList = ref<Article[]>([
  {
    id: 1,
    title: '2025校園文化建設成果頁',
    publish_date: '2025-06-01',
    author: '陳編輯',
    status: '待發佈'
  },
  {
    id: 2,
    title: '2025級新生入學指引專題頁',
    publish_date: '2025-06-07',
    author: '林主任',
    status: '待發佈'
  },
  {
    id: 3,
    title: '夏季校園開放日活動頁',
    publish_date: '2025-06-11',
    author: '陳編輯',
    status: '已發佈'
  },
  {
    id: 4,
    title: '新學期實驗室安全預警公示頁',
    publish_date: '2025-06-13',
    author: '連主任',
    status: '已發佈'
  }
])

// 分页参数
const page = ref(1)
const limit = ref(10)
const total = ref(10)

// 表格选择
const handleSelectionChange = (selection: Article[]) => {
  selectedArticles.value = selection
}

// 分页处理
const handleSizeChange = (val: number) => {
  limit.value = val
  console.log('每页显示:', val)
}

const handleCurrentChange = (val: number) => {
  page.value = val
  console.log('当前页:', val)
}

// 操作方法
const editArticle = (article: Article) => {
  console.log('编辑文章:', article)
}

const deleteArticle = (article: Article) => {
  console.log('删除文章:', article)
}

// 搜索和筛选
const showFilter = () => {
  console.log('显示筛选面板')
  // TODO: 实现筛选功能
}

// 导出报告
const exportReport = () => {
  console.log('导出报告')
  // TODO: 实现导出功能
}

// 网站列表数据
const websiteList = ref<Website[]>([
  { id: 1, name: 'BINGO_HK', url: 'https://www.hk-bingo.com/', selected: false },
  { id: 2, name: 'BINGO_CN', url: 'https://www.cn-bingo.com/', selected: false },
  { id: 3, name: 'BINGO_EN', url: 'https://www.en-bingo.com/', selected: false },
  { id: 4, name: 'BINGO_SP', url: 'https://www.sp-bingo.com/', selected: false },
])

// 弹窗状态
const dialogVisible = ref(false)

// 发布设置
const selectAllSites = ref(false)
const publishTimeType = ref('now')
const scheduledTime = ref('')
const publishStatus = ref('published')
const customUrl = ref('')

// 高级选项
const showAdvancedOptions = ref(false)
const advancedOptions = reactive({
  customUrl: '',
  seoTitle: '',
  metaDescription: '',
  keywords: '',
  category: ''
})

// 是否可以发布
const canPublish = computed(() => {
  return selectedArticles.value.length > 0 && 
         websiteList.value.some(site => site.selected) &&
         (publishTimeType.value === 'now' || (publishTimeType.value === 'scheduled' && scheduledTime.value))
})

// 处理全选/取消全选站点
const handleSelectAllSites = (val: boolean) => {
  websiteList.value.forEach(site => {
    site.selected = val
  })
}

// 同步文章
const syncArticles = () => {
  // 先将当前选中的文章添加到selectedArticles
  if (selectedArticles.value.length === 0) {
    // 获取表格数据中的第一行数据作为默认选中项
    const currentArticle = articleList.value.find(item => item.status === '已發佈')
    if (currentArticle) {
      selectedArticles.value = [currentArticle]
      ElMessage.info('已自動選擇一個已發佈頁面')
    } else if (articleList.value.length > 0) {
      // 如果没有已发布的，就选第一个
      selectedArticles.value = [articleList.value[0]]
      ElMessage.info('已自動選擇第一個頁面')
    } else {
      ElMessage.warning('請先選擇要發佈的頁面')
      return
    }
  }
  
  // 重置站点选择状态
  websiteList.value.forEach(site => {
    site.selected = false
  })
  selectAllSites.value = false
  
  // 显示弹窗
  dialogVisible.value = true
}

// 切换高级选项显示
const toggleAdvancedOptions = () => {
  showAdvancedOptions.value = !showAdvancedOptions.value
}

// 确认发布
const confirmPublish = () => {
  const selectedSites = websiteList.value.filter(site => site.selected).map(site => site.name)
  
  console.log('发布文章:', {
    articles: selectedArticles.value,
    sites: selectedSites,
    publishTime: publishTimeType.value === 'now' ? new Date() : scheduledTime.value,
    status: publishStatus.value,
    customUrl: customUrl.value,
    advancedOptions: advancedOptions
  })
  
  // TODO: 实际发布逻辑
  ElMessage.success('發佈任務已提交')
  dialogVisible.value = false
}

// 定义接口（如果不存在）
interface Website {
  id: number
  name: string
  url: string
  selected: boolean
}
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-header {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .header-left {
      flex: 1;
      max-width: 286px;
      
      .search-input {
        width: 100%;
      }
    }
    
    .header-right {
      display: flex;
      align-items: center;
    }
  }

  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}

.article-title {
  font-weight: 500;
  color: #000;
}

.sync-records {
  margin-top: 28px;
  background: #fff;
  border-radius: 10px;
  padding: 25px 20px;
  
  .sync-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 24px;
    border-bottom: 1px solid #E0EAF2;
    
    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: #000;
    }
    
    .generate-report {
      color: #2866EC;
      font-size: 16px;
      
      &:hover {
        color: #337ECC;
      }
    }
  }
  
  .sync-list {
    .sync-item {
      display: flex;
      align-items: flex-start;
      padding: 24px 0;
      border-bottom: 1px solid #E0EAF2;
      
      &:last-child {
        border-bottom: none;
      }
      
      .sync-icon {
        width: 61px;
        height: 61px;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        flex-shrink: 0;
        
        .el-icon {
          font-size: 20px;
        }
      }
      
      .sync-content {
        flex: 1;
        
        .sync-title {
          font-size: 16px;
          font-weight: 500;
          color: #000;
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        .sync-time {
            color: #858585;
            font-weight: 400;
          }
        .sync-meta {
          font-size: 14px;
          color: #464646;
        }
        
        .sync-error {
          margin-top: 8px;
          font-size: 14px;
          color: #FC5050;
        }
      }
      
      &.success .sync-icon {
        background: #E2FBE8;
        color: #51A365;
      }
      
      &.warning .sync-icon {
        background: #FEFCE8;
        color: #C18D30;
      }
    }
  }
}

.publish-dialog {
  :deep(.el-dialog__header) {
    display: block !important;
    padding: 20px !important;
    margin-right: 0;
    text-align: center;
    border-bottom: 1px solid #E0EAF2;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 500;
    }
    
    .el-dialog__headerbtn {
      top: 20px;
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 20px !important;
  }
  
  .publish-dialog-content {
    .section-title {
      font: normal normal normal 16px/21px Microsoft JhengHei;
      letter-spacing: 0px;
      color: #303030;
      opacity: 1;
      line-height: 46px;
      height: 46px;
      width: 98.5%;
    }
    
    .selected-articles {
      margin-bottom: 24px;
      
      .article-list {
        width: 949px;
        height: 113px;
        background: #F9FAFB;
        box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
        border: 2px solid #DEDEDE;
        border-radius: 10px;
        padding: 15px;
        overflow-y: auto;
        
        .article-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          
          .el-icon {
            margin-right: 8px;
            color: #606266;
          }
          
          span {
            font: normal normal normal 16px/21px Microsoft JhengHei;
            letter-spacing: 0px;
            color: #303030;
          }
        }
        
        .no-articles {
          color: #909399;
          font-style: italic;
          text-align: center;
          padding: 10px 0;
        }
      }
    }
    
    .publish-sites {
      margin-bottom: 24px;
      padding-right: 10px;
      .sites-selection-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }
      .sites-selection {
        .select-all {
          margin-bottom: 15px;
          display: block;
        }
        
        .sites-container {
          .sites-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .site-item {
              width: 467px;
              height: 88px;
              background: #FFFFFF;
              box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
              border: 2px solid #DEDEDE;
              border-radius: 10px;
              display: flex;
              align-items: center;
              padding: 0 15px;
              
              .site-checkbox {
                margin-right: 15px;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                
                :deep(.el-checkbox) {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  height: 100%;
                  
                  .el-checkbox__input {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    
                    .el-checkbox__inner {
                      width: 24px;
                      height: 24px;
                      border-radius: 4px;
                      border: 2px solid #DCDFE6;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      
                      &::after {
                        height: 10px;
                        width: 4px;
                        left: 8px;
                      }
                    }
                  }
                  
                  &.is-checked {
                    .el-checkbox__inner {
                      background-color: #3273F7;
                      border-color: #3273F7;
                    }
                  }
                }
              }
              
              .site-info {
                .site-name {
                  font: normal normal normal 16px/21px Microsoft JhengHei;
                  letter-spacing: 0px;
                  color: #000000;
                  margin-bottom: 8px;
                }
                
                .site-url {
                  font: normal normal normal 14px/19px Microsoft JhengHei;
                  letter-spacing: 0px;
                  color: #9A9A9A;
                }
              }
            }
          }
        }
      }
    }
    
    .publish-settings {
      .settings-container {
        padding: 12px 0;
        
        .setting-row {
          display: flex;
          margin-bottom: 16px;
  
          &:last-child {
            margin-bottom: 0;
          }
          
          .setting-item {
            display: flex;
            margin-right: 20px;
            width: 50%;
            &:last-child {
              margin-right: 0;
            }
            
            .setting-label {
              width: 100px;
              flex-shrink: 0;
              line-height: 38px;
              font: normal normal normal 14px/19px Microsoft JhengHei;
              letter-spacing: 0px;
              color: #1C1C1C;
              opacity: 1;
              display: flex;
              align-items: center;
              margin-right: 189px;
            }
            
            .setting-control {
              flex: 1;
              display: flex;
              align-items: center;
              
              :deep(.el-select) {
                width: 164px !important;
                
                .el-input__wrapper {
                  width: 164px !important;
                  height: 38px !important;
                  background: #FFFFFF 0% 0% no-repeat padding-box;
                  border: 1px solid #D8D8D8;
                  border-radius: 5px;
                  opacity: 1;
                  box-shadow: none;
                  padding: 0 10px;
                }
              }
              
              :deep(.el-date-editor) {
                width: 164px !important;
                height: 38px !important;
                background: #FFFFFF 0% 0% no-repeat padding-box;
                border: 1px solid #D8D8D8;
                border-radius: 5px;
                opacity: 1;
                margin-top: 0;
                padding: 0 !important;
                
                .el-input__wrapper {
                  box-shadow: none;
                  padding: 0 10px;
                  height: 38px !important;
                  width: 100% !important;
                }
                
                .el-input__inner {
                  height: 38px !important;
                }
              }
              
              :deep(.el-input) {
                width: 300px;
                
                .el-input__wrapper {
                  height: 38px;
                  background: #FFFFFF 0% 0% no-repeat padding-box;
                  border: 1px solid #D8D8D8;
                  border-radius: 5px;
                  opacity: 1;
                  box-shadow: none;
                }
              }
              
              :deep(.el-radio) {
                margin-right: 20px;
                
                .el-radio__label {
                  font: normal normal normal 16px/21px Microsoft JhengHei;
                  letter-spacing: 0px;
                  color: #000000;
                  opacity: 1;
                }
                
                &:last-child {
                  margin-right: 0;
                }
              }
            }
          }

          .setting-status {
            width: 60%;
            .setting-label{
              width: 100px;
              flex-shrink: 0;
              line-height: 38px;
              font: normal normal normal 14px/19px Microsoft JhengHei;
              letter-spacing: 0px;
              color: #1C1C1C;
              opacity: 1;
              display: flex;
              align-items: center;
              margin-right: 132px;
            }
          }
        }
      }
    }
  }
  
  .dialog-footer {
    padding-top: 20px;
    text-align: center;
  }
}

// 专门针对日期选择器的设置
.setting-control {
  :deep(.el-date-editor) {
    &.el-input {
      border: none !important;
      background: transparent !important;
      
      .el-input__wrapper {
        border: 1px solid #D8D8D8 !important;
        background: #FFFFFF !important;
        border-radius: 5px !important;
        height: 38px !important;
        width: 164px !important;
        box-shadow: none !important;
      }
    }
  }
}

// 日期选择器图标位置调整
:deep(.el-input__prefix) {
  align-items: center;
  justify-content: center;
}

// 高级选项折叠面板样式
.advanced-options {
  margin-top: 16px;
  width: 949px;
  border: 1px solid #E0EAF2;
  border-radius: 5px;
  overflow: hidden;

  .advanced-header {
    width: 100%;
    height: 48px;
    background: #E9F3FF 0% 0% no-repeat padding-box;
    border-bottom: 1px solid #E0EAF2;
    border-radius: 5px 5px 0px 0px;
    opacity: 1;
    padding: 0 15px !important;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-title {
      display: flex;
      align-items: center;
      font: normal normal bold 16px/21px Microsoft JhengHei;
      letter-spacing: 0px;
      color: #3273F7;
      opacity: 1;
    }

    .header-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      
      .el-icon {
        transition: transform 0.3s ease;
        
        &.is-rotate {
          transform: rotate(90deg);
        }
      }
    }
  }

  .advanced-content {
    padding: 20px 18px;
    background-color: #FFFFFF;

    .advanced-form-item {
      margin-bottom: 20px;

      .form-label {
        font: normal normal normal 14px/19px Microsoft JhengHei;
        letter-spacing: 0px;
        color: #1C1C1C;
        opacity: 1;
        margin-bottom: 8px;
      }

      :deep(.el-input) {
        width: 913px;
        
        .el-input__wrapper {
          width: 913px;
          height: 40px;
          background: #FFFFFF 0% 0% no-repeat padding-box;
          border: 1px solid #DFDFDF;
          border-radius: 5px;
          opacity: 1;
          box-shadow: none;
        }
      }

      :deep(.el-textarea) {
        width: 913px;
        
        .el-textarea__inner {
          width: 913px;
          background: #FFFFFF 0% 0% no-repeat padding-box;
          border: 1px solid #DFDFDF;
          border-radius: 5px;
          opacity: 1;
          box-shadow: none;
          padding: 10px;
        }
      }

      :deep(.el-select) {
        width: 913px;
        
        .el-input__wrapper {
          width: 913px;
          height: 40px;
          background: #FFFFFF 0% 0% no-repeat padding-box;
          border: 1px solid #DFDFDF;
          border-radius: 5px;
          opacity: 1;
          box-shadow: none;
        }
      }
    }
  }
}
</style> 