import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
    {
        path: '/content',
        component: () => import('/admin/layout/index.vue'),
        meta: { title: 'Aticle.router.articleManagement', icon: 'datareport' },
        children: [
            {
                path: 'sync',
                name: 'ContentSync',
                meta: { title: 'Aticle.router.syncManagement' },
                component: () => import('./ui/article/list.vue'),
            },
            {
                path: 'info',
                name: 'ContentInfo',
                meta: { title: 'Aticle.router.infoManagement' },
                component: () => import('./ui/article/info.vue'),
            }
        ]
    }
]

export default router